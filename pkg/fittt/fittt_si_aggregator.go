package fittt

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	mfPhPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/pkg/rms"
)

const (
	expiryDueDays = 91
)

type StandingInstructionFitttAggregator struct {
	rmClient    rmsPb.RuleManagerClient
	mfPayClient mfPhPb.PaymentHandlerClient
}

func NewStandingInstructionFitttAggregator(rmClient rmsPb.RuleManagerClient, mfPayClient mfPhPb.PaymentHandlerClient) *StandingInstructionFitttAggregator {
	return &StandingInstructionFitttAggregator{
		rmClient:    rmClient,
		mfPayClient: mfPayClient,
	}
}

func (s *StandingInstructionFitttAggregator) GetAllStandingInstructionsWithActiveSipsDueForExpiry(ctx context.Context, actorId string) (map[string]*rpPb.RecurringPayment,
	map[string]int, []string, error) {
	// Fetch all active SIPs
	ruleResp, beErr := s.rmClient.GetSubscriptionsByActorForRules(ctx,
		&rmsPb.GetSubscriptionsByActorForRulesRequest{
			ActorId:                actorId,
			RuleIds:                constants.FitAutoInvestRuleIds,
			ShouldNotUsePagination: true,
			States: []rmsPb.RuleSubscriptionState{
				rmsPb.RuleSubscriptionState_ACTIVE,
			},
		})
	if err := epifigrpc.RPCError(ruleResp, beErr); err != nil {
		logger.Error(ctx, "error while getting all active fit rules subscribed by actor", zap.Error(err))
		return nil, nil, nil, fmt.Errorf("error while getting all active fit subscribtions : %v", err)
	}
	// Identify unique funds for which user has an active sip
	uniqueMfIdsWithSip := make(map[string]bool)
	for _, subscriptions := range ruleResp.GetRuleSubscriptions() {
		for _, subscription := range subscriptions.GetRuleSubscriptions() {
			if _, ok := subscription.GetRuleParamValues().GetRuleParamValues()[rms.RmsMutualFundVal]; ok {
				mfVal := subscription.GetRuleParamValues().GetRuleParamValues()[rms.RmsMutualFundVal].GetMutualFundVal()
				if _, ok2 := uniqueMfIdsWithSip[mfVal.GetMfId()]; !ok2 {
					uniqueMfIdsWithSip[mfVal.GetMfId()] = true
				}
			}
		}
	}
	logger.Debug(ctx, "unique mf ids", zap.Any("sips", uniqueMfIdsWithSip))
	// For all the unique funds, find the corresponding standing instruction which can be grouped by the amc
	// SI is created for an Amc, so all funds with same AMC would be using the same SI
	amcActorIdToSIInfo := make(map[string]*rpPb.RecurringPayment)
	amcActorIdToSIPCount := make(map[string]int)
	var amcActorIdList []string

	for mfId, _ := range uniqueMfIdsWithSip {
		paymentInfo, pErr := s.mfPayClient.GetPaymentInfoForActorAndMutualFund(ctx, &mfPhPb.GetPaymentInfoForActorAndMutualFundRequest{
			ActorId:      actorId,
			MutualFundId: mfId,
		})
		if rErr := epifigrpc.RPCError(paymentInfo, pErr); rErr != nil {
			logger.Error(ctx, "error while getting amc information", zap.String(logger.MF_ID, mfId), zap.Error(rErr))
			return nil, nil, nil, fmt.Errorf("error while getting amc information : %v", rErr)
		}
		logger.Debug(ctx, "pay info", zap.Any("recurringPayment", paymentInfo))
		if paymentInfo.GetRecurringPaymentInfo().GetInterval().GetEndTime().AsTime().Before(time.Now().AddDate(0, 0, expiryDueDays)) {
			amcActorIdToSIInfo[paymentInfo.GetRecurringPaymentInfo().GetToActorId()] = paymentInfo.GetRecurringPaymentInfo()
			if _, ok2 := amcActorIdToSIPCount[paymentInfo.GetRecurringPaymentInfo().GetToActorId()]; !ok2 {
				amcActorIdToSIPCount[paymentInfo.GetRecurringPaymentInfo().GetToActorId()] = 1
				amcActorIdToSIInfo[paymentInfo.GetRecurringPaymentInfo().GetToActorId()] = paymentInfo.GetRecurringPaymentInfo()
				amcActorIdList = append(amcActorIdList, paymentInfo.GetRecurringPaymentInfo().GetToActorId())
			} else {
				amcActorIdToSIPCount[paymentInfo.GetRecurringPaymentInfo().GetToActorId()]++
			}
		}
	}
	return amcActorIdToSIInfo, amcActorIdToSIPCount, amcActorIdList, nil
}
