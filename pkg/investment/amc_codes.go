package investment

import (
	"fmt"
	"strings"

	"github.com/epifi/gamma/api/investment/mutualfund"
)

// GetAmcFromCode returns internal AMC enum for a RTA code for AMC
// For Karvy Codes: https://docs.google.com/spreadsheets/d/1c6hu9yJs3FA48CKlPGGiYdUYu7mgWipO/edit#gid=906492001, https://docs.google.com/spreadsheets/d/1LHstd6yuyKApSsATwbnSbB4vuHOHiC8D/edit#gid=854119138
// For Cams Codes: https://docs.google.com/spreadsheets/d/12RG3U8GEJXYrS-3TJeQ4iA8eDdLRcolD/edit#gid=713907179
// nolint: funlen, dupl
func GetAmcFromCode(amcCode string) (mutualfund.Amc, error) {
	amcCode = strings.TrimSpace(amcCode)
	switch amcCode {
	case "H":
		return mutualfund.Amc_HDFC, nil
	case "L":
		return mutualfund.Amc_SBI, nil
	case "P":
		return mutualfund.Amc_ICICI_PRUDENTIAL, nil
	case "B":
		return mutualfund.Amc_ADITYA_BIRLA, nil
	case "G":
		return mutualfund.Amc_IDFC, nil
	case "T":
		return mutualfund.Amc_TATA, nil
	case "K":
		return mutualfund.Amc_KOTAK_MAHINDRA, nil
	case "IF":
		return mutualfund.Amc_IIFL, nil
	case "SH":
		return mutualfund.Amc_SHRIRAM, nil
	case "PP":
		return mutualfund.Amc_PPFAS, nil
	case "D":
		return mutualfund.Amc_DSP, nil
	case "UK":
		return mutualfund.Amc_UNION, nil
	case "O":
		return mutualfund.Amc_HSBC, nil
	case "F":
		// F000 used to represent LNT AMC, but LNT Amc go acquired by HSBC and all the funds were transferred to HSBC.
		// So, we are returning Amc_HSBC to process/reconcile all the older orders.
		return mutualfund.Amc_HSBC, nil
	case "AXF", "128":
		return mutualfund.Amc_AXIS, nil
	case "BPF", "107":
		return mutualfund.Amc_BARODA, nil
	case "178":
		return mutualfund.Amc_BNP_PARIBAS, nil
	case "BAF", "116":
		return mutualfund.Amc_BOI_AXA, nil
	case "CRF", "101":
		return mutualfund.Amc_CANARA_ROBECO, nil
	case "EMF", "118":
		return mutualfund.Amc_EDELWEISS, nil
	case "PLF", "130":
		return mutualfund.Amc_NAVI, nil
	case "135", "IDF":
		return mutualfund.Amc_IDBI, nil
	case "IBM", "125":
		return mutualfund.Amc_INDIABULLS, nil
	case "RGF", "120":
		return mutualfund.Amc_INVESCO, nil
	case "ITI", "152":
		return mutualfund.Amc_ITI, nil
	case "JMF", "105":
		return mutualfund.Amc_JM_FINANCIAL, nil
	case "LIC", "102":
		return mutualfund.Amc_LIC, nil
	case "MAF", "117":
		return mutualfund.Amc_MIRAE, nil
	case "MOF", "127":
		return mutualfund.Amc_MOTILAL_OSWAL, nil
	case "RMF":
		return mutualfund.Amc_NIPPON_LIFE, nil
	case "PRF", "129", "109":
		return mutualfund.Amc_PGIM, nil
	case "PMF", "103":
		return mutualfund.Amc_PRINCIPAL, nil
	case "166":
		return mutualfund.Amc_QUANT_MONEY_MANAGERS, nil
	case "QMF", "123":
		return mutualfund.Amc_QUANTUM, nil
	case "176", "S":
		return mutualfund.Amc_SUNDARAM, nil
	case "TMF", "104":
		return mutualfund.Amc_TAURUS, nil
	case "UTI", "108":
		return mutualfund.Amc_UTI, nil
	case "185":
		return mutualfund.Amc_TRUST, nil
	case "187":
		return mutualfund.Amc_NJ, nil
	case "188":
		return mutualfund.Amc_SAMCO, nil
	case "MM":
		return mutualfund.Amc_MAHINDRA, nil
	case "FTI", "114":
		return mutualfund.Amc_FRANKLIN_TEMPLETON, nil
	case "Y":
		return mutualfund.Amc_WHITEOAK_CAPITAL, nil
	case "189":
		return mutualfund.Amc_BAJAJ_FINSERV, nil
	case "HLS":
		return mutualfund.Amc_HELIOS, nil
	case "Z":
		return mutualfund.Amc_ZERODHA, nil
	case "139":
		return mutualfund.Amc_OLD_BRIDGE, nil
	case "UFI":
		return mutualfund.Amc_UNIFI, nil
	case "AO":
		return mutualfund.Amc_ANGEL_ONE, nil
	case "JIO":
		return mutualfund.Amc_JIO_BLACKROCK, nil
	default:
		return mutualfund.Amc_AMC_UNSPECIFIED, fmt.Errorf("unhandled AMC code: %s", amcCode)
	}
}

// AmcToAmcCodes mapper
var AmcToAmcCodes = map[mutualfund.Amc][]string{
	mutualfund.Amc_ADITYA_BIRLA:         {"B"},
	mutualfund.Amc_AXIS:                 {"AXF", "128"},
	mutualfund.Amc_BARODA:               {"BPF", "107"},
	mutualfund.Amc_BNP_PARIBAS:          {"178"},
	mutualfund.Amc_BOI_AXA:              {"BAF", "116"},
	mutualfund.Amc_CANARA_ROBECO:        {"CRF", "101"},
	mutualfund.Amc_DSP:                  {"D"},
	mutualfund.Amc_EDELWEISS:            {"EMF", "118"},
	mutualfund.Amc_FRANKLIN_TEMPLETON:   {"FTI", "114"},
	mutualfund.Amc_HDFC:                 {"H"},
	mutualfund.Amc_HSBC:                 {"O"},
	mutualfund.Amc_ICICI_PRUDENTIAL:     {"P"},
	mutualfund.Amc_IDBI:                 {"135", "IDF"},
	mutualfund.Amc_IDFC:                 {"G"},
	mutualfund.Amc_IIFL:                 {"IF"},
	mutualfund.Amc_INDIABULLS:           {"IBM", "125"},
	mutualfund.Amc_INVESCO:              {"RGF", "120"},
	mutualfund.Amc_ITI:                  {"ITI", "152"},
	mutualfund.Amc_JM_FINANCIAL:         {"JMF", "105"},
	mutualfund.Amc_KOTAK_MAHINDRA:       {"K"},
	mutualfund.Amc_LNT:                  {"F"},
	mutualfund.Amc_LIC:                  {"LIC", "102"},
	mutualfund.Amc_MAHINDRA:             {"MM"},
	mutualfund.Amc_MIRAE:                {"MAF", "117"},
	mutualfund.Amc_MOTILAL_OSWAL:        {"MOF", "127"},
	mutualfund.Amc_NAVI:                 {"130", "PLF"},
	mutualfund.Amc_NIPPON_LIFE:          {"RMF"},
	mutualfund.Amc_NJ:                   {"187"},
	mutualfund.Amc_PGIM:                 {"PRF", "129", "109"},
	mutualfund.Amc_PPFAS:                {"PP"},
	mutualfund.Amc_PRINCIPAL:            {"PMF", "103"},
	mutualfund.Amc_QUANT_MONEY_MANAGERS: {"166"},
	mutualfund.Amc_QUANTUM:              {"QMF", "123"},
	mutualfund.Amc_SAMCO:                {"188"},
	mutualfund.Amc_SBI:                  {"L"},
	mutualfund.Amc_SHRIRAM:              {"SH"},
	mutualfund.Amc_SUNDARAM:             {"176", "S"},
	mutualfund.Amc_TATA:                 {"T"},
	mutualfund.Amc_TAURUS:               {"TMF", "104"},
	mutualfund.Amc_TRUST:                {"185"},
	mutualfund.Amc_UNION:                {"UK"},
	mutualfund.Amc_UTI:                  {"UTI", "108"},
	mutualfund.Amc_WHITEOAK_CAPITAL:     {"Y"},
	mutualfund.Amc_BAJAJ_FINSERV:        {"189"},
	mutualfund.Amc_HELIOS:               {"HLS"},
	mutualfund.Amc_ZERODHA:              {"Z"},
	mutualfund.Amc_OLD_BRIDGE:           {"139"},
	mutualfund.Amc_UNIFI:                {"UFI"},
	mutualfund.Amc_ANGEL_ONE:            {"AO"},
	mutualfund.Amc_JIO_BLACKROCK:        {"JIO"},
}

// GetAMCCodeForNomineeUpdates : AMC codes required in nominee update apis provided by rta is different for some AMCs. We
// are handing it case by case in this function.
func GetAMCCodeForNomineeUpdates(amc mutualfund.Amc) (string, error) {
	switch amc {
	case mutualfund.Amc_NAVI:
		if len(AmcToAmcCodes[amc]) < 2 {
			return "", fmt.Errorf("invalid amc code")
		}
		return AmcToAmcCodes[amc][1], nil
	default:
		if len(AmcToAmcCodes[amc]) < 1 {
			return "", fmt.Errorf("invalid amc code")
		}
		return AmcToAmcCodes[amc][0], nil
	}
}

// AmcCodeMapForCatalog is a map from AMC Name to AMC code used only for mutual fund catalog.
// AMC code is defined by RTAs.
// RTA shares the AMC code as a static mapping.
// nolint: dupl
var AmcCodeMapForCatalog = map[mutualfund.Amc]string{
	mutualfund.Amc_ADITYA_BIRLA:         "B",
	mutualfund.Amc_AXIS:                 "128",
	mutualfund.Amc_BARODA:               "107",
	mutualfund.Amc_BNP_PARIBAS:          "178",
	mutualfund.Amc_BOI_AXA:              "116",
	mutualfund.Amc_CANARA_ROBECO:        "101",
	mutualfund.Amc_DSP:                  "D",
	mutualfund.Amc_EDELWEISS:            "118",
	mutualfund.Amc_FRANKLIN_TEMPLETON:   "FTI",
	mutualfund.Amc_HDFC:                 "H",
	mutualfund.Amc_HSBC:                 "O",
	mutualfund.Amc_ICICI_PRUDENTIAL:     "P",
	mutualfund.Amc_IDBI:                 "135",
	mutualfund.Amc_IDFC:                 "G",
	mutualfund.Amc_IIFL:                 "IF",
	mutualfund.Amc_INDIABULLS:           "125",
	mutualfund.Amc_INVESCO:              "120",
	mutualfund.Amc_ITI:                  "152",
	mutualfund.Amc_JM_FINANCIAL:         "105",
	mutualfund.Amc_KOTAK_MAHINDRA:       "K",
	mutualfund.Amc_LNT:                  "F",
	mutualfund.Amc_LIC:                  "102",
	mutualfund.Amc_MAHINDRA:             "MM",
	mutualfund.Amc_MIRAE:                "117",
	mutualfund.Amc_MOTILAL_OSWAL:        "127",
	mutualfund.Amc_NAVI:                 "130",
	mutualfund.Amc_NIPPON_LIFE:          "RMF",
	mutualfund.Amc_NJ:                   "187",
	mutualfund.Amc_PGIM:                 "129",
	mutualfund.Amc_PPFAS:                "PP",
	mutualfund.Amc_PRINCIPAL:            "103",
	mutualfund.Amc_QUANT_MONEY_MANAGERS: "166",
	mutualfund.Amc_QUANTUM:              "123",
	mutualfund.Amc_SAMCO:                "188",
	mutualfund.Amc_SBI:                  "L",
	mutualfund.Amc_SHRIRAM:              "SH",
	mutualfund.Amc_SUNDARAM:             "176",
	mutualfund.Amc_TATA:                 "T",
	mutualfund.Amc_TAURUS:               "104",
	mutualfund.Amc_TRUST:                "185",
	mutualfund.Amc_UNION:                "UK",
	mutualfund.Amc_UTI:                  "108",
	mutualfund.Amc_WHITEOAK_CAPITAL:     "Y",
	mutualfund.Amc_BAJAJ_FINSERV:        "189",
	mutualfund.Amc_HELIOS:               "HLS",
	mutualfund.Amc_ZERODHA:              "Z",
	mutualfund.Amc_OLD_BRIDGE:           "139",
	mutualfund.Amc_UNIFI:                "UFI",
	mutualfund.Amc_ANGEL_ONE:            "AO",
	mutualfund.Amc_JIO_BLACKROCK:        "JIO",
}
