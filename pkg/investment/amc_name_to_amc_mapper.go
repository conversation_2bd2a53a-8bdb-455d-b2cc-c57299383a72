package investment

import (
	"github.com/epifi/gamma/api/investment/mutualfund"
)

// AmcEnumToName contains amc to amc name mapping
// Note: This is an alternative to fetching AMC name by enum from AMC table thus avoiding a DB call
// nolint: dupl
var AmcEnumToName = map[mutualfund.Amc]string{
	mutualfund.Amc_ADITYA_BIRLA:         "Aditya Birla Sun Life AMC Ltd",
	mutualfund.Amc_AXIS:                 "Axis Asset Management Company Limited",
	mutualfund.Amc_BARODA:               "Baroda Asset Management India Limited",
	mutualfund.Amc_BNP_PARIBAS:          "BNP Paribas Asset Mgmt India Pvt. Ltd",
	mutualfund.Amc_BOI_AXA:              "Bank of India Investment Managers Private Limited",
	mutualfund.Amc_CANARA_ROBECO:        "Canara Robeco Asset Management Co. Ltd.",
	mutualfund.Amc_DSP:                  "DSP Investment Managers Private Limited",
	mutualfund.Amc_EDELWEISS:            "Edelweiss Asset Management Limited",
	mutualfund.Amc_FRANKLIN_TEMPLETON:   "Franklin Templeton Asst Mgmt(IND)Pvt Ltd",
	mutualfund.Amc_HDFC:                 "HDFC Asset Management Company Limited",
	mutualfund.Amc_HSBC:                 "HSBC Asset Management(India)Private Ltd",
	mutualfund.Amc_ICICI_PRUDENTIAL:     "ICICI Prudential Asset Management Company Limited",
	mutualfund.Amc_IDBI:                 "IDBI Asset Management Limited",
	mutualfund.Amc_IDFC:                 "IDFC Asset Management Company Limited",
	mutualfund.Amc_IIFL:                 "IIFL Asset Management Limited",
	mutualfund.Amc_INDIABULLS:           "Indiabulls Asset Management Company Ltd.",
	mutualfund.Amc_INVESCO:              "Invesco Asset Management (India) Private Ltd",
	mutualfund.Amc_ITI:                  "ITI Asset Management Limited",
	mutualfund.Amc_JM_FINANCIAL:         "JM Financial Asset Management Limited",
	mutualfund.Amc_KOTAK_MAHINDRA:       "Kotak Mahindra Asset Management Co Ltd",
	mutualfund.Amc_LNT:                  "L&T Investment Management Ltd",
	mutualfund.Amc_LIC:                  "LIC Mutual Fund Asset Management Limited",
	mutualfund.Amc_MAHINDRA:             "Mahindra Manulife Investment Management Pvt. Ltd.",
	mutualfund.Amc_MIRAE:                "Mirae Asset Investment Managers (India) Private Limited",
	mutualfund.Amc_MOTILAL_OSWAL:        "Motilal Oswal Asset Management Co. Ltd",
	mutualfund.Amc_NAVI:                 "Navi AMC Limited",
	mutualfund.Amc_NIPPON_LIFE:          "Nippon Life India Asset Management Ltd",
	mutualfund.Amc_NJ:                   "NJ Asset Management Private Limited",
	mutualfund.Amc_PGIM:                 "PGIM India Asset Management Private Limited",
	mutualfund.Amc_PPFAS:                "PPFAS Asset Management Pvt. Ltd",
	mutualfund.Amc_PRINCIPAL:            "Principal Asset Management Private Limited",
	mutualfund.Amc_QUANT_MONEY_MANAGERS: "Quant Money Managers Limited",
	mutualfund.Amc_QUANTUM:              "Quantum Asset Management Co Pvt. Ltd.",
	mutualfund.Amc_SAMCO:                "Samco Asset Management Pvt Ltd",
	mutualfund.Amc_SBI:                  "SBI Funds Management Ltd",
	mutualfund.Amc_SHRIRAM:              "Shriram Asset Management Co Ltd",
	mutualfund.Amc_SUNDARAM:             "Sundaram Asset Management Company Ltd",
	mutualfund.Amc_TATA:                 "Tata Asset Management Private Limited",
	mutualfund.Amc_TAURUS:               "Taurus Asset Management Company Limited",
	mutualfund.Amc_TRUST:                "Trust Asset Management Private Limited",
	mutualfund.Amc_UNION:                "Union Asset Management Co. Pvt. Ltd.",
	mutualfund.Amc_UTI:                  "UTI Asset Management Co Ltd",
	mutualfund.Amc_WHITEOAK_CAPITAL:     "WhiteOak Capital Asset Management Limited",
	mutualfund.Amc_BAJAJ_FINSERV:        "Bajaj Finserv Asset Management Limited",
	mutualfund.Amc_HELIOS:               "Helios Capital Asset Management Private Limited",
	mutualfund.Amc_ZERODHA:              "Zerodha Asset Management Private Limited",
	mutualfund.Amc_OLD_BRIDGE:           "Old Bridge Asset Management Private Limited",
	mutualfund.Amc_UNIFI:                "Unifi Asset Management Private Limited",
	mutualfund.Amc_ANGEL_ONE:            "Angel One Asset Management Company Limited",
	mutualfund.Amc_JIO_BLACKROCK:        "Jio BlackRock Asset Management Company Private Limited",
}
