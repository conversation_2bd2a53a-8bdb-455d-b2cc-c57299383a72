package investment

import (
	"time"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
)

// nolint: dupl
var MorningStarAmcToAmcProtoEnum = map[string]mfPb.Amc{
	"Aditya Birla Sun Life AMC Ltd":                                       mfPb.Amc_ADITYA_BIRLA,
	"Axis Asset Management Company Limited":                               mfPb.Amc_AXIS,
	"Baroda Asset Management India Limited":                               mfPb.Amc_BARODA,
	"BNP Paribas Asset Mgmt India Pvt. Ltd":                               mfPb.Amc_BNP_PARIBAS,
	"Baroda BNP Paribas Asset Management India Pvt. Ltd.":                 mfPb.Amc_BNP_PARIBAS,
	"BOI AXA Investment Mngrs Private Ltd":                                mfPb.Amc_BOI_AXA,
	"Bank of India Investment Managers Private Limited":                   mfPb.Amc_BOI_AXA,
	"Canara Robeco Asset Management Co. Ltd.":                             mfPb.Amc_CANARA_ROBECO,
	"DSP Investment Managers Private Limited":                             mfPb.Amc_DSP,
	"DSP Asset Managers Private Limited":                                  mfPb.Amc_DSP,
	"Edelweiss Asset Management Limited":                                  mfPb.Amc_EDELWEISS,
	"Franklin Templeton Asst Mgmt(IND)Pvt Ltd":                            mfPb.Amc_FRANKLIN_TEMPLETON,
	"HDFC Asset Management Company Limited":                               mfPb.Amc_HDFC,
	"HDFC Asset Management Co Ltd":                                        mfPb.Amc_HDFC,
	"Helios Capital Asset Management (India) Private Limited":             mfPb.Amc_HELIOS,
	"HSBC Asset Management(India)Private Ltd":                             mfPb.Amc_HSBC,
	"HSBC Asset Management (India) Private Ltd":                           mfPb.Amc_HSBC,
	"ICICI Prudential Asset Management Company Limited":                   mfPb.Amc_ICICI_PRUDENTIAL,
	"IDBI Asset Management Limited":                                       mfPb.Amc_IDBI,
	"IDFC Asset Management Company Limited":                               mfPb.Amc_IDFC,
	"IIFL Asset Management Limited":                                       mfPb.Amc_IIFL,
	"Indiabulls Asset Management Company Ltd.":                            mfPb.Amc_INDIABULLS,
	"Groww Asset Management Ltd.":                                         mfPb.Amc_INDIABULLS,
	"360 ONE Asset Management Limited":                                    mfPb.Amc_IIFL,
	"Invesco Asset Management (India) Private Ltd":                        mfPb.Amc_INVESCO,
	"ITI Asset Management Limited":                                        mfPb.Amc_ITI,
	"JM Financial Asset Management Limited":                               mfPb.Amc_JM_FINANCIAL,
	"Kotak Mahindra Asset Management Co Ltd":                              mfPb.Amc_KOTAK_MAHINDRA,
	"L&T Investment Management Ltd":                                       mfPb.Amc_LNT,
	"LIC Mutual Fund Asset Management Limited":                            mfPb.Amc_LIC,
	"Mahindra Manulife Investment Management Pvt. Ltd.":                   mfPb.Amc_MAHINDRA,
	"Mirae Asset Investment Managers (India) Private Limited":             mfPb.Amc_MIRAE,
	"Motilal Oswal Asset Management Co. Ltd":                              mfPb.Amc_MOTILAL_OSWAL,
	"Motilal Oswal Asset Management Company Limited - Portfolio Managers": mfPb.Amc_MOTILAL_OSWAL,
	"Navi AMC Limited":                                                    mfPb.Amc_NAVI,
	"Nippon Life India Asset Management Ltd":                              mfPb.Amc_NIPPON_LIFE,
	"NJ Asset Management Private Limited":                                 mfPb.Amc_NJ,
	"PGIM India Asset Management Private Limited":                         mfPb.Amc_PGIM,
	"PPFAS Asset Management Pvt. Ltd":                                     mfPb.Amc_PPFAS,
	"Principal Asset Management Private Limited":                          mfPb.Amc_PRINCIPAL,
	"Quant Money Managers Limited":                                        mfPb.Amc_QUANT_MONEY_MANAGERS,
	"Quantum Asset Management Co Pvt. Ltd.":                               mfPb.Amc_QUANTUM,
	"Samco Asset Management Pvt Ltd":                                      mfPb.Amc_SAMCO,
	"SBI Funds Management Ltd":                                            mfPb.Amc_SBI,
	"Shriram Asset Management Co Ltd":                                     mfPb.Amc_SHRIRAM,
	"Sundaram Asset Management Company Ltd":                               mfPb.Amc_SUNDARAM,
	"Tata Asset Management Private Limited":                               mfPb.Amc_TATA,
	"Tata Asset Management Limited":                                       mfPb.Amc_TATA,
	"Taurus Asset Management Company Limited":                             mfPb.Amc_TAURUS,
	"Trust Asset Management Private Limited":                              mfPb.Amc_TRUST,
	"Union Asset Management Co. Pvt. Ltd.":                                mfPb.Amc_UNION,
	"UTI Asset Management Co Ltd":                                         mfPb.Amc_UTI,
	"WhiteOak Capital Asset Management Limited":                           mfPb.Amc_WHITEOAK_CAPITAL,
	"Bandhan Asset Management Company Limited":                            mfPb.Amc_IDFC,
	"Bajaj Finserv Asset Management Limited":                              mfPb.Amc_BAJAJ_FINSERV,
	"Helios Capital Asset Management Private Limited":                     mfPb.Amc_HELIOS,
	"Zerodha Asset Management Private Limited":                            mfPb.Amc_ZERODHA,
	"Old Bridge Asset Management Private Limited":                         mfPb.Amc_OLD_BRIDGE,
	"Unifi Asset Management Private Limited":                              mfPb.Amc_UNIFI,
	"Angel One Asset Management Company Limited":                          mfPb.Amc_ANGEL_ONE,
	"Jio BlackRock Asset Management Company Private Limited":              mfPb.Amc_JIO_BLACKROCK,
}

// nolint: dupl
var MorningStarCategoryToProtoEnum = map[string]mfPb.MutualFundCategoryName{
	"Aggressive Allocation":                 mfPb.MutualFundCategoryName_AGGRESSIVE_ALLOCATION,
	"Balanced Allocation":                   mfPb.MutualFundCategoryName_BALANCED_ALLOCATION,
	"Children":                              mfPb.MutualFundCategoryName_CHILDREN,
	"Conservative Allocation":               mfPb.MutualFundCategoryName_CONSERVATIVE_ALLOCATION,
	"Dynamic Asset Allocation":              mfPb.MutualFundCategoryName_DYNAMIC_ASSET_ALLOCATION,
	"Equity Savings":                        mfPb.MutualFundCategoryName_EQUITY_SAVINGS,
	"Fund of Funds":                         mfPb.MutualFundCategoryName_FUND_OF_FUNDS,
	"Multi Asset Allocation":                mfPb.MutualFundCategoryName_MULTI_ASSET_ALLOCATION,
	"Retirement":                            mfPb.MutualFundCategoryName_RETIREMENT,
	"Arbitrage Fund":                        mfPb.MutualFundCategoryName_ARBITRAGE_FUND,
	"Sector - Precious Metals":              mfPb.MutualFundCategoryName_SECTOR_PRECIOUS_METALS,
	"Contra":                                mfPb.MutualFundCategoryName_CONTRA,
	"Dividend Yield":                        mfPb.MutualFundCategoryName_DIVIDEND_YIELD,
	"ELSS (Tax Savings)":                    mfPb.MutualFundCategoryName_ELSS_TAX_SAVING,
	"Equity - Consumption":                  mfPb.MutualFundCategoryName_EQUITY_CONSUMPTION,
	"Equity - ESG":                          mfPb.MutualFundCategoryName_EQUITY_ESG,
	"Equity - Infrastructure":               mfPb.MutualFundCategoryName_EQUITY_INFRASTRUCTURE,
	"Equity - Other":                        mfPb.MutualFundCategoryName_EQUITY_OTHER,
	"Flexi Cap":                             mfPb.MutualFundCategoryName_FLEXI_CAP,
	"Focused Fund":                          mfPb.MutualFundCategoryName_FOCUSED_FUND,
	"Global - Other":                        mfPb.MutualFundCategoryName_GLOBAL_OTHER,
	"Index Funds":                           mfPb.MutualFundCategoryName_INDEX_FUNDS,
	"Large & Mid-Cap":                       mfPb.MutualFundCategoryName_LARGE_AND_MID_CAP,
	"Large-Cap":                             mfPb.MutualFundCategoryName_LARGE_CAP,
	"Mid-Cap":                               mfPb.MutualFundCategoryName_MID_CAP,
	"Multi-Cap":                             mfPb.MutualFundCategoryName_MULTI_CAP,
	"Sector - Financial Services":           mfPb.MutualFundCategoryName_SECTOR_FINANCIAL_SERVICES,
	"Sector - FMCG":                         mfPb.MutualFundCategoryName_SECTOR_FMCG,
	"Sector - Healthcare":                   mfPb.MutualFundCategoryName_SECTOR_HEALTHCARE,
	"Sector - Technology":                   mfPb.MutualFundCategoryName_SECTOR_TECHNOLOGY,
	"Sector - Energy":                       mfPb.MutualFundCategoryName_SECTOR_ENERGY,
	"Small-Cap":                             mfPb.MutualFundCategoryName_SMALL_CAP,
	"10 yr Government Bond":                 mfPb.MutualFundCategoryName_TEN_YR_GOVERNMENT_BOND,
	"Banking & PSU":                         mfPb.MutualFundCategoryName_BANKING_AND_PSU,
	"Corporate Bond":                        mfPb.MutualFundCategoryName_CORPORATE_BOND,
	"Credit Risk":                           mfPb.MutualFundCategoryName_CREDIT_RISK,
	"Dynamic Bond":                          mfPb.MutualFundCategoryName_DYNAMIC_BOND,
	"Floating Rate":                         mfPb.MutualFundCategoryName_FLOATING_RATE,
	"Government Bond":                       mfPb.MutualFundCategoryName_GOVERNMENT_BOND,
	"Long Duration":                         mfPb.MutualFundCategoryName_LONG_DURATION,
	"Low Duration":                          mfPb.MutualFundCategoryName_LOW_DURATION,
	"Medium Duration":                       mfPb.MutualFundCategoryName_MEDIUM_DURATION,
	"Medium to Long Duration":               mfPb.MutualFundCategoryName_MEDIUM_TO_LONG_DURATION,
	"Money Market":                          mfPb.MutualFundCategoryName_MONEY_MARKET,
	"Other Bond":                            mfPb.MutualFundCategoryName_OTHER_BOND,
	"Short Duration":                        mfPb.MutualFundCategoryName_SHORT_DURATION,
	"Ultra Short Duration":                  mfPb.MutualFundCategoryName_ULTRA_SHORT_DURATION,
	"Liquid":                                mfPb.MutualFundCategoryName_LIQUID,
	"Overnight":                             mfPb.MutualFundCategoryName_OVERNIGHT,
	"Value":                                 mfPb.MutualFundCategoryName_VALUE,
	"Index Funds - Fixed Income":            mfPb.MutualFundCategoryName_INDEX_FUNDS_FIXED_INCOME,
	"Fixed Maturity Short-Term Bond":        mfPb.MutualFundCategoryName_FIXED_MATURITY_SHORT_TERM_BOND,
	"Fixed Maturity Intermediate-Term Bond": mfPb.MutualFundCategoryName_FIXED_MATURITY_INTERMEDIATE_TERM_BOND,
	"Fixed Maturity Ultrashort Bond":        mfPb.MutualFundCategoryName_FIXED_MATURITY_ULTRASHORT_BOND,
}

var MorningStarAssetClassToProtoEnum = map[string]mfPb.AssetClass{
	"Hybrid": mfPb.AssetClass_HYBRID,
	"Equity": mfPb.AssetClass_EQUITY,
	"Debt":   mfPb.AssetClass_DEBT,
	"Cash":   mfPb.AssetClass_CASH,
}

var MorningStarCategoryToAssetClass = map[string]string{
	"Aggressive Allocation":                 "Hybrid",
	"Balanced Allocation":                   "Hybrid",
	"Children":                              "Hybrid",
	"Conservative Allocation":               "Hybrid",
	"Dynamic Asset Allocation":              "Hybrid",
	"Equity Savings":                        "Hybrid",
	"Fund of Funds":                         "Hybrid",
	"Multi Asset Allocation":                "Hybrid",
	"Retirement":                            "Hybrid",
	"Arbitrage Fund":                        "Hybrid",
	"Sector - Precious Metals":              "Equity",
	"Contra":                                "Equity",
	"Dividend Yield":                        "Equity",
	"ELSS (Tax Savings)":                    "Equity",
	"Equity - Consumption":                  "Equity",
	"Equity - ESG":                          "Equity",
	"Equity - Infrastructure":               "Equity",
	"Equity - Other":                        "Equity",
	"Flexi Cap":                             "Equity",
	"Focused Fund":                          "Equity",
	"Global - Other":                        "Equity",
	"Index Funds":                           "Equity",
	"Large & Mid-Cap":                       "Equity",
	"Large-Cap":                             "Equity",
	"Mid-Cap":                               "Equity",
	"Multi-Cap":                             "Equity",
	"Sector - Financial Services":           "Equity",
	"Sector - FMCG":                         "Equity",
	"Sector - Healthcare":                   "Equity",
	"Sector - Technology":                   "Equity",
	"Small-Cap":                             "Equity",
	"Value":                                 "Equity",
	"Sector - Energy":                       "Equity",
	"10 yr Government Bond":                 "Debt",
	"Banking & PSU":                         "Debt",
	"Corporate Bond":                        "Debt",
	"Credit Risk":                           "Debt",
	"Dynamic Bond":                          "Debt",
	"Floating Rate":                         "Debt",
	"Government Bond":                       "Debt",
	"Long Duration":                         "Debt",
	"Low Duration":                          "Debt",
	"Medium Duration":                       "Debt",
	"Medium to Long Duration":               "Debt",
	"Money Market":                          "Debt",
	"Other Bond":                            "Debt",
	"Short Duration":                        "Debt",
	"Ultra Short Duration":                  "Debt",
	"Liquid":                                "Cash",
	"Overnight":                             "Cash",
	"Index Funds - Fixed Income":            "Debt",
	"Fixed Maturity Short-Term Bond":        "Debt",
	"Fixed Maturity Intermediate-Term Bond": "Debt",
	"Fixed Maturity Ultrashort Bond":        "Debt",
}

const DurationUntilNextEtaCheck = 12 * time.Hour
