package helper

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	typesPb "github.com/epifi/gamma/api/typesv2"
	payScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/money"

	widget1 "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	gmoney "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/accounts"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	usStocksDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/tiering/constants"
	"github.com/epifi/gamma/frontend/tiering/display_names"
	"github.com/epifi/gamma/frontend/tiering/helper/aasalary"
	"github.com/epifi/gamma/pkg/usstocks/deeplinks"

	cfggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	feSalaryPb "github.com/epifi/gamma/api/frontend/salaryprogram"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	salaryProgramScreenOptionsV2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	tieringPb "github.com/epifi/gamma/api/typesv2/tiering"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/tiering/tiermappings"
)

var (
	AllowedTierForDropOffBottomSheetMap = []tieringExtPb.Tier{
		tieringExtPb.Tier_TIER_FI_PLUS,
		tieringExtPb.Tier_TIER_FI_INFINITE,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
	}

	SalaryBenefitsDeeplink = func() *deeplinkPb.Deeplink {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
		}
	}

	UIEntryPointToExternalTierMap = map[feTransactionPb.UIEntryPoint]tieringExtPb.Tier{
		feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PLUS:     tieringExtPb.Tier_TIER_FI_PLUS,
		feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_INFINITE: tieringExtPb.Tier_TIER_FI_INFINITE,
		feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PRIME:    tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
	}

	TransactionUIEntryPointToExternalTierMap = map[timeline.TransactionUIEntryPoint]tieringExtPb.Tier{
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS:     tieringExtPb.Tier_TIER_FI_PLUS,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE: tieringExtPb.Tier_TIER_FI_INFINITE,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PRIME:    tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
	}
)

// IsAllPlansEntryPoint checks if the UI entry point is from any of the "All Plans" pages.
func IsAllPlansEntryPoint(uiEntryPoint timeline.TransactionUIEntryPoint) bool {
	_, exists := TransactionUIEntryPointToExternalTierMap[uiEntryPoint]
	return exists
}

var SalaryProgramV2Deeplink = func() (*deeplinkPb.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION, &salaryProgramScreenOptionsV2.SalaryProgramLandingScreenRedirectionScreenOptions{
		EntryPoint: feSalaryPb.SalaryProgramLandingRedirectionScreenEntryPoint_TIER_SALARY_PLAN_SCREEN,
	})
}

var SalaryProgramIntroScreenDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_SALARY_PROGRAM_INTRO_SCREEN,
	}
}

var AaSalaryLandingScreenDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_AA_SALARY_LANDING_SCREEN,
	}
}

var AaSalaryLandingScreenDeeplinkForSMSParser = func() *deeplinkPb.Deeplink {
	opts, _ := deeplinkv3.GetScreenOptionV2(&salaryProgramScreenOptionsV2.AaSalaryLandingScreenOptions{
		LoaderText: "Calculating your rewards",
	})
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_AA_SALARY_LANDING_SCREEN,
		ScreenOptionsV2: opts,
	}
}

func ConvertBoolToBooleanEnum(val bool) commontypes.BooleanEnum {
	if val {
		return commontypes.BooleanEnum_TRUE
	}
	return commontypes.BooleanEnum_FALSE
}

func IsUserInCoolOff(currentTier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails) (bool, error) {
	if currentTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return false, errors.New("user is in unspecified tier")
	}

	currTierInternal, _ := tiermappings.GetInternalTierFromExternalTier(currentTier)
	for _, mmtDetail := range mmtDetails {
		tierInternal, _ := tiermappings.GetInternalTierFromExternalTier(mmtDetail.GetTierName())
		if tierInternal.Number() > currTierInternal.Number() && !mmtDetail.GetIsMovementAllowed() {
			return true, nil
		}
	}

	return false, nil
}

// IsUserInGracePeriod returns if the user is in grace period and if yes, the returns movement timestamp also
func IsUserInGracePeriod(currentTier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails) (bool, *timestampPb.Timestamp, error) {
	for _, mmtDetail := range mmtDetails {
		if mmtDetail.GetTierName() == currentTier {
			return !mmtDetail.GetIsMovementAllowed(), mmtDetail.GetMovementTimestamp(), nil
		}
	}
	return false, nil, errors.New("current tier not found in movement details")
}

func GetTierOptions(tier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails) ([]*tieringCriteriaPb.Option, error) {
	if tier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return nil, errors.New("tier cannot be unspecified")
	}

	for _, mmtDetail := range mmtDetails {
		if tier == mmtDetail.GetTierName() {
			return mmtDetail.GetOptions(), nil
		}
	}
	return nil, fmt.Errorf("options not found for tier : %s", tier.String())
}

// GetMinBalanceFromTierOptions
// if minBalance criteria is not present, it will return tieringErrors.ErrTierHasNoMinBalanceCriteria
func GetMinBalanceFromTierOptions(tier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails) (*gmoney.Money, error) {
	var tierOptions []*tieringCriteriaPb.Option
	for _, mmtDetail := range mmtDetails {
		if tier == mmtDetail.GetTierName() {
			tierOptions = mmtDetail.GetOptions()
			break
		}
	}
	// If multiple options present for now taking the minBalance for latest option
	// TODO(sainath): change this in future
	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			actionCriteria := action.GetActionDetails().GetCriteria()
			switch actionCriteria := actionCriteria.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_Balance:
				return actionCriteria.Balance.GetMinBalance(), nil
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				return actionCriteria.BalanceV2.GetMinBalanceForUpgrade(), nil
			}
		}
	}
	return nil, feTieringErrors.ErrTierHasNoMinBalanceCriteria
}

func GetMinBalanceFromOptions(tierOptions []*tieringCriteriaPb.Option) (*gmoney.Money, error) {
	// If multiple options present for now taking the minBalance for latest option
	// TODO(sainath): change this in future
	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			actionCriteria := action.GetActionDetails().GetCriteria()
			switch actionCriteria := actionCriteria.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_Balance:
				return actionCriteria.Balance.GetMinBalance(), nil
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				return actionCriteria.BalanceV2.GetMinBalanceForUpgrade(), nil
			}
		}
	}
	return nil, feTieringErrors.ErrTierHasNoMinBalanceCriteria
}

func GetAllCriteriaMinValuesFromOptions(tierOptions []*tieringCriteriaPb.Option) ([]*CriteriaMinValue, error) {
	var criteriaMinValues []*CriteriaMinValue

	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			actionCriteria := action.GetActionDetails().GetCriteria()
			switch actionCriteria := actionCriteria.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_UsStocksSip:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
						MinValue: actionCriteria.UsStocksSip.GetMinWalletAddFunds(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_Deposits:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
						MinValue: actionCriteria.Deposits.GetMinDepositsAmount(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
						MinValue: actionCriteria.BalanceV2.GetMinBalanceForUpgrade(),
					})
			}
		}
	}

	if len(criteriaMinValues) > 0 {
		return criteriaMinValues, nil
	}

	return nil, feTieringErrors.ErrTierHasNoMinBalanceCriteria
}

func GetLatestMovementDetails(tieringPitchResp *beTieringPb.GetTieringPitchV2Response) *tieringExtPb.LatestMovementDetails {
	var latestMovementDetails *tieringExtPb.LatestMovementDetails
	if tieringPitchResp.GetLastDowngradeDetails().GetMovementTimestamp().AsTime().After(tieringPitchResp.GetLastUpgradeDetails().GetMovementTimestamp().AsTime()) {
		latestMovementDetails = tieringPitchResp.GetLastDowngradeDetails()
	} else {
		latestMovementDetails = tieringPitchResp.GetLastUpgradeDetails()
	}

	return latestMovementDetails
}

func GetPreviousTier(tieringPitchResp *beTieringPb.GetTieringPitchV2Response) tieringExtPb.Tier {
	return GetLatestMovementDetails(tieringPitchResp).GetFromTier()
}

func GetDeeplinkForAddFunds(feTier tieringExtPb.Tier) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TRANSFER_IN,
		ScreenOptions: &deeplinkPb.Deeplink_TransferInScreenOptions{
			TransferInScreenOptions: &deeplinkPb.TransferInScreenOptions{
				UiEntryPoint: getUiEntryPointString(feTier),
			},
		},
	}
}

func GetDeeplinkForPaymentOptions(feTier tieringExtPb.Tier, amount *gmoney.Money, actorId string) (*deeplinkPb.Deeplink, error) {
	transactionUIEntryPoint := getTransactionUIEntryPoint(feTier)

	screenOptions := &payScreenOptions.PaymentOptionsScreenOptions{
		Amount:                  typesPb.GetFromBeMoney(amount),
		TransactionUiEntryPoint: transactionUIEntryPoint,
		OrderedPaymentOptionTypes: []feTransactionPb.PaymentOptionType{
			feTransactionPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
		},
		ActorTo: actorId,
	}

	paymentOptionsDeeplink, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PAYMENT_OPTIONS_FULL_SCREEN, screenOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to construct payment options deeplink: %w", err)
	}

	return paymentOptionsDeeplink, nil
}

// CalculateAmountNeededForTier calculates the amount needed for a user to reach the target tier
// based on the minimum balance requirement and current account balance
func CalculateAmountNeededForTier(targetTier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails, currentBalance *gmoney.Money) (*gmoney.Money, error) {

	// 1. Try to validate the current balance and min balance required for the target tier
	minBalance, err := GetMinBalanceFromTierOptions(targetTier, mmtDetails)
	if err != nil {
		return nil, fmt.Errorf("failed to get min balance for tier %s: %w", targetTier.String(), err)
	}

	if minBalance == nil {
		return nil, fmt.Errorf("no min balance found for tier %s", targetTier.String())
	}

	if currentBalance == nil {
		return nil, fmt.Errorf("current balance not found")
	}

	// 2. If current balance greater than or equal to min balance required return zero
	isGreater, err := money.IsGreaterThanOrEqual(currentBalance, minBalance)
	if err != nil {
		return nil, fmt.Errorf("failed to compare balances: %w", err)
	}

	if isGreater {
		return money.ZeroINR().Pb, nil
	}

	// 3. Return amount diff for target tier
	amountNeeded, err := money.Subtract(minBalance, currentBalance)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate amount needed: %w", err)
	}

	return amountNeeded, nil
}

func GetDeeplinkForManualSalaryTxnSelect() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN}
}

func getUiEntryPointString(feTier tieringExtPb.Tier) string {
	switch feTier {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PLUS.String()
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_INFINITE.String()
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PRIME.String()
	default:
		return ""
	}
}

func getTransactionUIEntryPoint(feTier tieringExtPb.Tier) timeline.TransactionUIEntryPoint {
	switch feTier {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PRIME
	default:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED
	}
}

// GetInfoFromPitchResp fetches tiering related info from tiering pitch v2 rpc
func GetInfoFromPitchResp(tieringPitchResp *beTieringPb.GetTieringPitchV2Response) (currTier tieringExtPb.Tier, minBalanceMap map[tieringExtPb.Tier]*gmoney.Money, eligibilityMap map[tieringExtPb.Tier]bool, baseTier tieringExtPb.Tier) {
	minBalanceMap = make(map[tieringExtPb.Tier]*gmoney.Money)
	eligibilityMap = make(map[tieringExtPb.Tier]bool)

	for _, detail := range tieringPitchResp.GetMovementDetailsList() {
		feTier := detail.GetTierName()
		eligibilityMap[feTier] = detail.GetIsMovementAllowed()
		var curFeTierMinBalance *gmoney.Money
		// If multiple options present for now taking the minBalance for latest option
		// TODO(sainath): change this in future
		curFeTierMinBalance, _ = GetMinBalanceFromOptions(detail.GetOptions())
		minBalanceMap[feTier] = curFeTierMinBalance
	}
	return tieringPitchResp.GetCurrentTier(), minBalanceMap, eligibilityMap, tieringPitchResp.GetActorBaseTier()
}

func ShouldPitchAaSalaryTier(ctx context.Context, gconf *genconf.Config, curTier tieringExtPb.Tier) bool {
	return cfggenconf.IsFeatureEnabledOnPlatform(ctx, gconf.Tiering().NewTiersConstraints().AaSalary()) && curTier != tieringExtPb.Tier_TIER_FI_SALARY &&
		curTier != tieringExtPb.Tier_TIER_FI_SALARY_LITE && curTier != tieringExtPb.Tier_TIER_FI_INFINITE
}

func GetDeepLinksForInfiniteAndPlusBoost(rewardOfferId string) *deeplinkPb.Deeplink {
	opts, _ := deeplinkv3.GetScreenOptionV2(
		&rewards.RewardOfferDetailsScreenOptions{
			RewardOfferId: rewardOfferId,
		},
	)
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_REWARD_OFFER_DETAILS_SCREEN,
		ScreenOptionsV2: opts,
	}
}

func IsUserInSegment(segmentMap map[string]*segmentPb.SegmentMembership, segmentIds []string) bool {
	for _, segmentId := range segmentIds {
		if segmentMap[segmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
			segmentMap[segmentId].GetIsActorMember() {
			return true
		}
	}
	return false
}

//nolint:dupl
func getIconAndTitleForDropOffBottomSheet(wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod) (*commontypes.VisualElement, *commontypes.Text) {
	var imageUrl, text string
	switch wayToPitchTier {
	case beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT:
		imageUrl, text = constants.FixedDepositIconUrl, constants.FixedDeposits
	case beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS:
		imageUrl, text = constants.USStocksIconUrl, constants.USStocks
	}
	return commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrl, 40, 40), commontypes.GetTextFromStringFontColourFontStyle(text, constants.Indigo, commontypes.FontStyle_HEADLINE_XL)
}

//nolint:dupl
func getSubTitleForDropOffBottomSheet(tier tieringExtPb.Tier, wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod, tierCriteriaMinValuesMap TierCriteriaMinValuesMap) *commontypes.Text {
	var text, amountString string

	for _, criteriaMinValue := range tierCriteriaMinValuesMap[tier] {
		amount := criteriaMinValue.MinValue
		amountString = money.ToDisplayStringWithSuffixAndPrecisionV2(amount, true, true, 2, false, money.IndianNumberSystem)

		if criteriaMinValue.Criteria == tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC && wayToPitchTier == beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS {
			text = fmt.Sprintf(constants.USStocksSubtitleString, amountString)
		}
		if criteriaMinValue.Criteria == tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC && wayToPitchTier == beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT {
			text = fmt.Sprintf(constants.FDSubtitleString, amountString)
		}
	}
	return commontypes.GetTextFromStringFontColourFontStyle(text, constants.Indigo, commontypes.FontStyle_SUBTITLE_S)
}

//nolint:dupl
func getBenefitsForDropOffBottomSheet(wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod) []*uiPb.IconTextComponent {
	var benefits []*uiPb.IconTextComponent
	switch wayToPitchTier {
	case beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT:
		benefits = append(benefits, getBenefitFromString(constants.FDBenefitString))
	case beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS:
		benefits = []*uiPb.IconTextComponent{getBenefitFromString(constants.USStocksBenefit1String), getBenefitFromString(constants.USStocksBenefit2String)}
	}
	return benefits
}

func getBenefitFromString(text string) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, constants.DarkBlue, commontypes.FontStyle_OVERLINE_2XS_CAPS)).
		WithContainerBackgroundColor(constants.BrightGray).
		WithContainerCornerRadius(12).
		WithContainerPadding(6, 8, 8, 8)
}

//nolint:dupl
func getDeeplinkForDropOffBottomSheet(wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod, isUSStocksAccountActive bool) *deeplinkPb.Deeplink {
	var deeplink *deeplinkPb.Deeplink
	switch wayToPitchTier {
	case beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT:
		deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_TEMPLATES_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositTemplatesScreenOptions{
				DepositTemplatesScreenOptions: &deeplinkPb.DepositTemplatesScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		}
	case beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS:
		if isUSStocksAccountActive {
			deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_USSTOCKS_WALLET_ADD_FUNDS_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
					&usStocksDlOptions.USStocksAddFundsScreenOptions{}),
			}
		} else {
			deeplink = deeplinks.GetOnBoardingSetupScreen()
		}
	}

	return deeplink
}

//nolint:dupl
func getTitleForDropOffBottomSheet(tier tieringExtPb.Tier, wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod) *commontypes.Text {
	var text string
	tierToCashbackPercent, _ := aasalary.TierToCashbackPercent[tier]
	tierToString, _ := display_names.GetTitleCaseDisplayString(tier)
	switch wayToPitchTier {
	case beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT:
		text = fmt.Sprintf(constants.GetCashbackOnTierWithFDString, tierToCashbackPercent, tierToString)
	case beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS:
		text = fmt.Sprintf(constants.GetCashbackOnTierWithUSStocksString, tierToCashbackPercent, tierToString)
	case beTieringPb.GetDropOffBottomSheetRequest_ADD_FUNDS:
		text = fmt.Sprintf(constants.MoreWaysToGetCashback, tierToCashbackPercent, tierToString)
	}

	return commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L)
}

func getExploreCtaForDropOffBottomSheet(tier tieringExtPb.Tier) *deeplinkPb.Cta {
	tierToString, _ := display_names.GetTitleCaseDisplayString(tier)
	metaData := &tieringPb.TieringScreenMetaData{
		Metadata: &tieringPb.TieringScreenMetaData_PlansV2Metadata{
			PlansV2Metadata: &tieringPb.PlansV2Metadata{
				TierToFocus: tier.String(),
			},
		},
	}
	metaDataMarshalled, metaDataMarshalErr := protojson.Marshal(metaData)
	if metaDataMarshalErr != nil {
		return nil
	}
	return &deeplinkPb.Cta{
		Type: deeplinkPb.Cta_CUSTOM,
		Text: fmt.Sprintf(constants.ExploreCtaString, tierToString),
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_TIER_ALL_PLANS_SCREEN_V2,
			ScreenOptionsV2: getAnyWithoutError(
				&tiering.AllPlansV2ScreenOptions{
					MetaData: string(metaDataMarshalled),
				}),
		},
		DisplayTheme: deeplinkPb.Cta_TEXT,
	}
}

//nolint:dupl
func getComponentsForDropOffBottomSheet(tier tieringExtPb.Tier, wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod, tierCriteriaMinValuesMap TierCriteriaMinValuesMap, isUSStocksAccountActive bool) []*tiering.DropOffOptionComponent {
	var components []*tiering.DropOffOptionComponent
	switch wayToPitchTier {
	case beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS, beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT:
		components = []*tiering.DropOffOptionComponent{
			getComponentForDropOffBottomSheet(tier, wayToPitchTier, tierCriteriaMinValuesMap, isUSStocksAccountActive),
		}
	case beTieringPb.GetDropOffBottomSheetRequest_ADD_FUNDS:
		components = []*tiering.DropOffOptionComponent{
			getComponentForDropOffBottomSheet(tier, beTieringPb.GetDropOffBottomSheetRequest_US_STOCKS, tierCriteriaMinValuesMap, isUSStocksAccountActive),
			getComponentForDropOffBottomSheet(tier, beTieringPb.GetDropOffBottomSheetRequest_FIXED_DEPOSIT, tierCriteriaMinValuesMap, isUSStocksAccountActive),
		}
	}
	return components
}

func getComponentForDropOffBottomSheet(tier tieringExtPb.Tier, wayToPitchTier beTieringPb.GetDropOffBottomSheetRequest_TieringPitchMethod, tierCriteriaMinValuesMap TierCriteriaMinValuesMap, isUSStocksAccountActive bool) *tiering.DropOffOptionComponent {
	icon, title := getIconAndTitleForDropOffBottomSheet(wayToPitchTier)
	return &tiering.DropOffOptionComponent{
		BackgroundColour: &widget1.BackgroundColour{
			Colour: &widget1.BackgroundColour_BlockColour{
				BlockColour: colors.ColorSnow,
			},
		},
		Icon:      icon,
		Title:     title,
		Subtitle:  getSubTitleForDropOffBottomSheet(tier, wayToPitchTier, tierCriteriaMinValuesMap),
		Benefits:  getBenefitsForDropOffBottomSheet(wayToPitchTier),
		Deeplink:  getDeeplinkForDropOffBottomSheet(wayToPitchTier, isUSStocksAccountActive),
		ArrowIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(constants.ArrowIconUrl, 24, 24),
	}
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}

func GetDropOffBottomSheet(req *beTieringPb.GetDropOffBottomSheetRequest, tierCriteriaMinValuesMap TierCriteriaMinValuesMap, isUSStocksAccountActive bool) (*beTieringPb.GetDropOffBottomSheetResponse, error) {
	tieringPitchMethod := req.GetTieringPitchMethod()
	tierToPitch := req.GetTierToPitch()
	if tieringPitchMethod == beTieringPb.GetDropOffBottomSheetRequest_TIERING_PITCH_METHOD_UNSPECIFIED || !lo.Contains(AllowedTierForDropOffBottomSheetMap, tierToPitch) {
		return &beTieringPb.GetDropOffBottomSheetResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(
		&tiering.DropOffBottomSheetScreenOptions{
			PageBackgroundColor: &widget1.BackgroundColour{
				Colour: &widget1.BackgroundColour_BlockColour{
					BlockColour: constants.LightBase,
				},
			},
			Components: getComponentsForDropOffBottomSheet(tierToPitch, tieringPitchMethod, tierCriteriaMinValuesMap, isUSStocksAccountActive),
			Offer: uiPb.NewITC().WithTexts(
				commontypes.GetTextFromStringFontColourFontStyle(constants.LimitedTimeOfferString, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_S),
			).WithContainerBackgroundColor(constants.Chalk).WithContainerCornerRadius(12).WithContainerPadding(7, 12, 7, 12),
			Title:      getTitleForDropOffBottomSheet(tierToPitch, tieringPitchMethod),
			ExploreCta: getExploreCtaForDropOffBottomSheet(tierToPitch),
			GoBackCta: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_DONE,
				Text:         constants.GoBackCtaString,
				DisplayTheme: deeplinkPb.Cta_SECONDARY,
			},
			MetaData: tierToPitch.String(),
		})

	if err != nil {
		return &beTieringPb.GetDropOffBottomSheetResponse{
			Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
		}, nil
	}

	return &beTieringPb.GetDropOffBottomSheetResponse{
		Status: rpcPb.StatusOk(),
		Deeplink: &deeplinkPb.Deeplink{
			Screen:          deeplinkPb.Screen_TIER_DROP_OFF_BOTTOM_SHEET,
			ScreenOptionsV2: screenOptionsV2,
		},
	}, nil
}

func IsFeTieLowerThanOrEqualToCurrentEvaluatedTier(feTier tieringExtPb.Tier, evaluatedTier tieringExtPb.Tier) bool {
	switch evaluatedTier {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return feTier == tieringExtPb.Tier_TIER_FI_PLUS
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return feTier == tieringExtPb.Tier_TIER_FI_PLUS || feTier == tieringExtPb.Tier_TIER_FI_INFINITE
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return feTier == tieringExtPb.Tier_TIER_FI_PLUS || feTier == tieringExtPb.Tier_TIER_FI_INFINITE || feTier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3
	case tieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return feTier == tieringExtPb.Tier_TIER_FI_SALARY_BASIC
	case tieringExtPb.Tier_TIER_FI_SALARY:
		return feTier == tieringExtPb.Tier_TIER_FI_SALARY_BASIC || feTier == tieringExtPb.Tier_TIER_FI_SALARY
	default:
		return false
	}
}
