package tiering

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	fireflyAccPb "github.com/epifi/gamma/api/firefly/accounting"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	"github.com/epifi/gamma/pkg/rewards"

	colorPkg "github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/gamma/frontend/tiering/constants"

	pkgerrors "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/protobuf/field_mask"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/api/vendorgateway"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/proto/json"

	"github.com/epifi/gamma/api/accounts"
	beActorPb "github.com/epifi/gamma/api/actor"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	feSalaryPb "github.com/epifi/gamma/api/frontend/salaryprogram"
	feTieringPb "github.com/epifi/gamma/api/frontend/tiering"
	feTieringEnumPb "github.com/epifi/gamma/api/frontend/tiering/enum"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	beTieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	salaryProgramScreenOptionsV2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	tiering2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	ussScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks"
	ussMetadata "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/metadata"
	"github.com/epifi/gamma/api/typesv2/tiering"
	tieringTypesPb "github.com/epifi/gamma/api/typesv2/tiering"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	beUserPb "github.com/epifi/gamma/api/user"
	beUserGrpPb "github.com/epifi/gamma/api/user/group"
	ussAccountPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/api/usstocks/frontend"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	ambData "github.com/epifi/gamma/frontend/tiering/amb/data"
	ambUI "github.com/epifi/gamma/frontend/tiering/amb/ui"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/deeplink"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/cta"
	earnedBenefits "github.com/epifi/gamma/frontend/tiering/earned_benefits"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/frontend/tiering/feedback_flow_integration"
	"github.com/epifi/gamma/frontend/tiering/helper"
	"github.com/epifi/gamma/frontend/tiering/release"
	deeplinkCfg "github.com/epifi/gamma/pkg/deeplink/cfg"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	pkgRelease "github.com/epifi/gamma/pkg/feature/release"
	tieringPkg "github.com/epifi/gamma/pkg/tiering"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

const (
	earnedBenefitHistoryPageSize              = 13
	tierAllPlansV2HelpCircleIcon              = "https://epifi-icons.pointz.in/tiering/tier_all_plans_v2_question_mark_icon_small.png"
	tierAllPlansV2CompareIcon                 = "https://epifi-icons.pointz.in/tiering/tier_all_plans_v2_compare_icon_small.png"
	benefitCardBanner                         = "BENEFIT_CARD"
	defaultBgColor                            = "#FFFFFF"
	smallEntryBanner                          = "SMALL_ENTRY_BANNER"
	entryBanner                               = "ENTRY_BANNER"
	entryPoint_USStocks                       = "US_STOCKS"
	entryPoint_Deposits                       = "DEPOSITS"
	infoBanner                                = "INFO_BANNER"
	learnMoreBanner                           = "LEARN_MORE_BANNER"
	penaltyBanner                             = "PENALTY_BANNER"
	ManualUpgradeRetryCtaText                 = "Try again"
	ManualUpgradeMaxRetryCtaText              = "Upgrade unavailable"
	ManualUpgradeErrLeftTitleText             = "Could not upgrade you right now"
	ManualUpgradeErrLeftTitleTextFontColor    = "#282828" // Gray/Ink
	ManualUpgradeErrLeftSubTitleText          = "We encountered an error and couldn't upgrade you. You can try again."
	ManaulUpgradeMaxRetryLeftSubTitleText     = "We encountered an error and couldn't upgrade you. Please try later."
	ManualUpgradeErrLeftSubTitleTextFontColor = "#555555" // Gray/Iron
	ManualUpgradeErrRightIconUrl              = "https://epifi-icons.pointz.in/tiering/warning_icon.png"
	ManualUpgradeErrRightIconHeight           = 48
	ManualUpgradeErrRightIconWidth            = 48
	ManualUpgradeErrBgColor                   = "#FAD0D0" // Colour/Pastel Peach
	detailedBenefitsBottomSheet               = "detailed_benefits_bottom_sheet"
	cashbackDetailsBottomSheet                = "cashback_details_bottom_sheet"
	waysToUseFiPointsBottomSheet              = "ways_to_use_fi_points_bottom_sheet"
	addMoney                                  = "Add Money"
	GenericABVariantOne                       = "ONE"
)

var (
	balanceApiFailureStatus = func() *rpcPb.Status {
		return &rpcPb.Status{
			Code:         101,
			ShortMessage: "error in balance api call",
		}
	}
)

type loaderSceenMeta struct {
	CurrTier         string
	RedirectedScreen string
}

type benefitsMeta struct {
	CurrTier    string
	BenefitType string
}

type entryBannerMeta struct {
	CurrTier   string
	EntryPoint string
	BannerType string
}

type TierAllPlansV2Data struct {
	actorId                       string
	tieringEssential              *helper.TieringFeEssentials
	currBalance                   *money.Money
	evaluatedTierForActor         *beTieringPb.EvaluateTierForActorResponse
	tierIndexToFocus              int32
	kycLevel                      kycPb.KYCLevel
	actorScreenInteractionDetails beTieringEnumPb.ActorScreenInteractionStatus
	isUSStocksAccountActive       bool
	b2bVerificationStatus         bool
	isTierPlansV2Enabled          bool
}

type PlanMetaData struct {
	CurrentTier                      string
	CurrentTierCriteriaOptionTypes   string
	EvaluatedTier                    string
	EvaluatedTierCriteriaOptionTypes string
	TierName                         string
	CtaText                          string
	TierCriteriaMet                  string
	BalanceBucket                    string
	ComponentsDisplayed              string
}

type Service struct {
	feTieringPb.UnimplementedTieringServer
	releaseManager                    release.FeManager
	gconf                             *genconf.Config
	beTieringClient                   beTieringPb.TieringClient
	beSavingsClient                   beSavingsPb.SavingsClient
	beActorClient                     beActorPb.ActorClient
	deeplinkManager                   deeplink.Manager
	segmentClient                     segment.SegmentationServiceClient
	tierMovementFeedbackFlowSvcClient feedback_flow_integration.ITierMovementBasedFeedbackFlowIntegrationSvc
	earnedBenefitsDataCollector       earnedBenefits.EarnedBenefitsDataCollector
	earnedBenefitsComponentBuilder    earnedBenefits.IComponentBuilder
	ussAccountManagerClient           ussAccountPb.AccountManagerClient
	beBankCustClient                  bankCustomerPb.BankCustomerServiceClient
	dataCollector                     tieringData.DataCollector
	usersClient                       beUserPb.UsersClient
	abEvaluatorGeneric                *pkgRelease.ABEvaluator[string]
	releaseEvaluator                  pkgRelease.IEvaluator
	ambDataProvider                   ambData.AMBDataProvider
	ambScreenBuilder                  ambUI.AMBScreenBuilder
	fireFlyAccClientV2                fireflyV2Pb.FireflyV2Client
	fireFlyAccClient                  fireflyAccPb.AccountingClient
}

func NewService(
	releaseManager release.FeManager,
	gconf *genconf.Config,
	beTieringClient beTieringPb.TieringClient,
	beSavingsClient beSavingsPb.SavingsClient,
	beActorClient beActorPb.ActorClient,
	deeplinkManager deeplink.Manager,
	segmentClient segment.SegmentationServiceClient,
	tierMovementFeedbackFlowSvcClient feedback_flow_integration.ITierMovementBasedFeedbackFlowIntegrationSvc,
	earnedBenefitsDataCollector earnedBenefits.EarnedBenefitsDataCollector,
	earnedBenefitsComponentBuilder earnedBenefits.IComponentBuilder,
	ussAccountManagerClient ussAccountPb.AccountManagerClient,
	beBankCustClient bankCustomerPb.BankCustomerServiceClient,
	dataCollector tieringData.DataCollector,
	userClient beUserPb.UsersClient,
	userGrpClient beUserGrpPb.GroupClient,
	releaseEvaluator pkgRelease.IEvaluator,
	ambDataProvider ambData.AMBDataProvider,
	ambScreenBuilder ambUI.AMBScreenBuilder,
	fireFlyAccClientV2 fireflyV2Pb.FireflyV2Client,
	fireFlyAccClient fireflyAccPb.AccountingClient,
) *Service {
	return &Service{
		releaseManager:                    releaseManager,
		gconf:                             gconf,
		beTieringClient:                   beTieringClient,
		beSavingsClient:                   beSavingsClient,
		beActorClient:                     beActorClient,
		deeplinkManager:                   deeplinkManager,
		segmentClient:                     segmentClient,
		tierMovementFeedbackFlowSvcClient: tierMovementFeedbackFlowSvcClient,
		earnedBenefitsDataCollector:       earnedBenefitsDataCollector,
		earnedBenefitsComponentBuilder:    earnedBenefitsComponentBuilder,
		ussAccountManagerClient:           ussAccountManagerClient,
		beBankCustClient:                  beBankCustClient,
		dataCollector:                     dataCollector,
		usersClient:                       userClient,
		abEvaluatorGeneric:                helper.GetABEvaluatorOfFeature[string](beActorClient, userClient, userGrpClient, gconf.ABFeatureReleaseConfig(), func(str string) string { return str }),
		releaseEvaluator:                  releaseEvaluator,
		ambDataProvider:                   ambDataProvider,
		ambScreenBuilder:                  ambScreenBuilder,
		fireFlyAccClientV2:                fireFlyAccClientV2,
		fireFlyAccClient:                  fireFlyAccClient,
	}
}

// Upgrade
// RPC to upgrade manually to a tier in case user was eligible
// In case RPC throws any error other than status code "OK" client should fall back to
// error details sent in screen options for TIER_MANUAL_UPGRADE deeplink
func (s *Service) Upgrade(ctx context.Context, upgradeRequest *feTieringPb.UpgradeRequest) (*feTieringPb.UpgradeResponse, error) {
	actorId := upgradeRequest.GetReq().GetAuth().GetActorId()
	appPlatform := upgradeRequest.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := upgradeRequest.GetReq().GetAppVersionCode()
	// upgrade call goes to backend
	upgradeResp, err := s.beTieringClient.Upgrade(ctx, &beTieringPb.UpgradeRequest{
		ActorId:    actorId,
		Provenance: beTieringEnumPb.Provenance_PROVENANCE_FREE_UPGRADE,
	})
	if upgradeErr := epifigrpc.RPCError(upgradeResp, err); upgradeErr != nil {
		logger.Error(ctx, "error upgrading user", zap.Error(upgradeErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.UpgradeResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusFromError(upgradeErr),
			},
		}, nil
	}
	toTier := upgradeResp.GetToTier()

	// returning deeplink from here itself to reduce GetDeeplink call
	upgradeSuccessDeeplink, deeplinkErr := s.deeplinkManager.GetUpgradeSuccessDeeplink(ctx, toTier, actorId, appPlatform, appVersion)
	if deeplinkErr != nil {
		logger.Error(ctx, "error fetching upgrade success deeplink", zap.Error(deeplinkErr),
			zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.UpgradeResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching upgrade success deeplink"),
			},
		}, nil
	}
	return &feTieringPb.UpgradeResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Deeplink: upgradeSuccessDeeplink,
	}, nil
}

// GetTierAllPlans
// RPC to fetch all tier plans based on UI context. Backend will do default handling for nil UI context.
func (s *Service) GetTierAllPlans(ctx context.Context, req *feTieringPb.GetTierAllPlansRequest) (*feTieringPb.GetTierAllPlansResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := req.GetReq().GetAppVersionCode()
	allPlansDeeplink, allPlansDeeplinkErr := s.deeplinkManager.GetAllPlansDeeplink(ctx, actorId, appPlatform, appVersion, req.GetUiContext())
	if allPlansDeeplinkErr != nil {
		if errors.Is(allPlansDeeplinkErr, feTieringErrors.ErrTieringDisabled) {
			logger.Info(ctx, "tiering is disabled for the actor", zap.Error(allPlansDeeplinkErr),
				zap.Any(logger.ACTOR_ID_V2, actorId))
			return &feTieringPb.GetTierAllPlansResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpcPb.StatusFailedPreconditionWithDebugMsg("tiering is disabled for the actor"),
				},
			}, nil
		}
		logger.Error(ctx, "error fetching all plans deeplink", zap.Error(allPlansDeeplinkErr),
			zap.Any(logger.ACTOR_ID_V2, actorId))
		if errors.Is(allPlansDeeplinkErr, feTieringErrors.ErrAccountBalanceFetch) {
			return &feTieringPb.GetTierAllPlansResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: balanceApiFailureStatus(),
				},
			}, nil
		}
		return &feTieringPb.GetTierAllPlansResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching all plans deeplink"),
			},
		}, nil
	}

	respHeader := &headerPb.ResponseHeader{
		Status: rpcPb.StatusOk(),
	}

	feedbackEngineInfo, feedbackFlowErr := s.getTierMovementFeedbackEngineInfo(ctx, actorId, typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF)
	if feedbackFlowErr != nil {
		logger.Error(ctx, "error in setting feedback flow id in resp header", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(feedbackFlowErr))
	}

	respHeader.FeedbackEngineInfo = feedbackEngineInfo

	return &feTieringPb.GetTierAllPlansResponse{
		RespHeader:  respHeader,
		TierAllPlan: allPlansDeeplink,
	}, nil
}

func (s *Service) GetTierAllPlansV2(ctx context.Context, req *feTieringPb.GetTierAllPlansV2Request) (*feTieringPb.GetTierAllPlansV2Response, error) {

	actorId := req.GetReq().GetAuth().GetActorId()
	var tierToFocus string

	metaData := &tieringTypesPb.TieringScreenMetaData{}
	if err := protojson.Unmarshal([]byte(req.GetMetaData()), metaData); err != nil {
		logger.Error(ctx, "error unmarshalling metadata", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTierAllPlansV2Response{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error unmarshalling metadata"),
			},
		}, nil
	}
	switch metaData.GetMetadata().(type) {
	case *tieringTypesPb.TieringScreenMetaData_PlansV2Metadata:
		tierToFocus = metaData.GetPlansV2Metadata().GetTierToFocus()
	default:
		logger.Error(ctx, "metadata is nil", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTierAllPlansV2Response{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("Invalid Metadata type"),
			},
		}, nil
	}

	tierPlans, tierIndexToFocus, tierPlansErr := s.getTierAllPlansV2(ctx, actorId, tierToFocus)
	if tierPlansErr != nil {
		logger.Error(ctx, "error fetching all plans deeplink", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(tierPlansErr))
		return &feTieringPb.GetTierAllPlansV2Response{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetching all plans deeplink"),
			},
		}, nil
	}

	respHeader := &headerPb.ResponseHeader{
		Status: rpcPb.StatusOk(),
		// integrating drop off feedback engine on TIER_ALL_PLANS_SCREEN_V2 for analyzing user feedback
		// survey would be triggered when dropping off ( going back ) from TIER_ALL_PLANS_SCREEN_V2 and survey details can be configured on jarvis
		FeedbackEngineInfo: &headerPb.FeedbackEngineInfo{
			FlowIdDetails: &headerPb.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_SCREEN_V2.String(),
			},
		},
	}
	return &feTieringPb.GetTierAllPlansV2Response{
		RespHeader:       respHeader,
		Title:            commontypes.GetTextFromStringFontColourFontStyle("Plans", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
		TierIndexToFocus: int32(tierIndexToFocus),
		TierPlans:        tierPlans,
	}, nil
}

func (s *Service) getTierAllPlansV2(ctx context.Context, actorId string, metaData string) ([]*feTieringPb.TierPlan, int, error) {

	var resp []*feTieringPb.TierPlan

	tierAllPlansV2Data, gatherDataErr := s.gatherDataForTierAllPlansV2(ctx, actorId)
	if gatherDataErr != nil {
		return nil, 0, gatherDataErr
	}
	actorBaseTier := tierAllPlansV2Data.tieringEssential.GetTieringPitchResp().GetActorBaseTier()

	plansToShow := s.gconf.Tiering().TierAllPlansV2ConfigParams().TierPlansOrdering()

	if actorBaseTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		plansToShow = append([]string{beTieringExtPb.Tier_TIER_FI_REGULAR.String()}, plansToShow...)
	} else {
		plansToShow = append([]string{beTieringExtPb.Tier_TIER_FI_BASIC.String()}, plansToShow...)
	}

	if tierAllPlansV2Data.b2bVerificationStatus {
		currTier := tierAllPlansV2Data.tieringEssential.GetCurrentTier()
		if currTier.IsBaseTier() || currTier.IsSalaryTier() {
			plansToShow = []string{actorBaseTier.String(), beTieringExtPb.Tier_TIER_FI_SALARY_BASIC.String(), beTieringExtPb.Tier_TIER_FI_SALARY.String()}
		} else {
			plansToShow = []string{actorBaseTier.String(), currTier.String(), beTieringExtPb.Tier_TIER_FI_SALARY_BASIC.String(), beTieringExtPb.Tier_TIER_FI_SALARY.String()}
		}
	}

	tierIndexToFocus := 0
	for key, _ := range plansToShow {
		if plansToShow[key] == metaData {
			tierIndexToFocus = key
		}
	}
	if tierIndexToFocus >= len(plansToShow) {
		tierIndexToFocus = 0
	}

	balanceBucket := tieringPkg.DetermineBalanceBucket(tierAllPlansV2Data.currBalance.GetUnits())

	for _, planName := range plansToShow {
		planMeta := &PlanMetaData{
			TierName:      planName,
			BalanceBucket: balanceBucket,
		}
		tierV2Color := helper.GetTierV2Color(planName, s.gconf.Tiering().TierV2ThemeColorMap(), s.gconf.Tiering().TierV2ColorMap())
		tierPlan := &feTieringPb.TierPlan{
			HeaderBackgroundColor:        tierV2Color.HeaderBgColor(),
			HeaderOverlayBackgroundColor: tierV2Color.HeaderOverlayBackgroundColor(),
			ContentBackgroundColor:       tierV2Color.ContentBackgroundColor(),
			Icon:                         s.getAllPlansV2PlanIcon(ctx, planName, tierAllPlansV2Data),
			SwipeIcon:                    s.getAllPlansV2SwipeIcon(ctx, planName, tierAllPlansV2Data),
			PlanCard:                     s.getAllPlansV2PlanCard(ctx, planName, tierAllPlansV2Data),
			PlanBenefits:                 s.getAllPlansV2PlanBenefits(ctx, planName, tierAllPlansV2Data, planMeta),
			Cta:                          s.getAllPlansV2Cta(ctx, planName, actorId, tierAllPlansV2Data, planMeta),
			ToolbarRightImage: func() *commontypes.VisualElement {
				if tierAllPlansV2Data.isTierPlansV2Enabled {
					return commontypes.GetVisualElementFromUrlHeightAndWidth(tierAllPlansV2CompareIcon, 32, 115)
				}
				return commontypes.GetVisualElementFromUrlHeightAndWidth(tierAllPlansV2HelpCircleIcon, 40, 40)
			}(),
			ToolbarRightImageDeeplink: func() *deeplinkPb.Deeplink {
				if tierAllPlansV2Data.isTierPlansV2Enabled {
					return s.getComparePlansDeeplink(tierAllPlansV2Data.tieringEssential.GetTieringPitchResp().GetActorBaseTier())
				}
				return &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HELP_MAIN,
				}
			}(),
			PlanMeta:    planName,
			PlanMetaMap: s.getPlanMetaMap(ctx, planName, tierAllPlansV2Data, planMeta, actorId),
		}
		resp = append(resp, tierPlan)
	}
	return resp, tierIndexToFocus, nil
}

// getPlanMetaData gets the required data to populate PlanMeta
func (s *Service) getPlanMetaMap(ctx context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data, planMeta *PlanMetaData, actorId string) map[string]string {

	tierCriteriaMet := "No"
	planTier := beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName])

	// GetEvaluatedTier fetches the most eligible tier for the actor
	// tierCriteria is met if 'evaluated tier' is greater than 'benefit tier'
	evaluatedTier := tierAllPlansV2Data.evaluatedTierForActor.GetEvaluatedTier()
	if helper.IsFeTieLowerThanOrEqualToCurrentEvaluatedTier(planTier, evaluatedTier) {
		tierCriteriaMet = "Yes"
	}

	currentTierCriteriaOptionTypes := tierAllPlansV2Data.tieringEssential.TieringPitchResp.GetCurrentCriteriaOptionTypes()
	currentTierCriteriaOptionTypesStr := ""
	for idx, currentTierCriteriaOptionType := range currentTierCriteriaOptionTypes {
		currentTierCriteriaOptionTypesStr += currentTierCriteriaOptionType.String()
		if idx < len(currentTierCriteriaOptionTypes)-1 {
			currentTierCriteriaOptionTypesStr += ","
		}
	}

	evaluatedTierCriteriaOptionTypesStr := ""
	evaluatedTierCriteriaOptionTypes := tierAllPlansV2Data.evaluatedTierForActor.GetEvaluatedTierSatisfiedCriteriaOptionTypes()
	for idx, evaluatedTierCriteriaOptionType := range evaluatedTierCriteriaOptionTypes {
		evaluatedTierCriteriaOptionTypesStr += evaluatedTierCriteriaOptionType.String()
		if idx < len(evaluatedTierCriteriaOptionTypes)-1 {
			evaluatedTierCriteriaOptionTypesStr += ","
		}
	}

	// Update the fields of the existing planMeta struct
	planMeta.CurrentTier = tierAllPlansV2Data.tieringEssential.GetCurrentTier().String()
	planMeta.CurrentTierCriteriaOptionTypes = currentTierCriteriaOptionTypesStr
	planMeta.EvaluatedTier = evaluatedTier.String()
	planMeta.EvaluatedTierCriteriaOptionTypes = evaluatedTierCriteriaOptionTypesStr
	planMeta.TierCriteriaMet = tierCriteriaMet

	// check if tiering pitch in payment options is enabled and pass the same in MetaMap
	// to give visibility through events about the type of payment option screen shown to the user
	isPaymentOptionsDeeplinkEnabled, _, err := s.abEvaluatorGeneric.Evaluate(ctx, pkgRelease.NewCommonConstraintData(typesPb.Feature_FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating release", zap.Error(err))
	}

	return map[string]string{
		"CurrentTier":                       planMeta.CurrentTier,
		"CurrentTierCriteriaOptionTypes":    planMeta.CurrentTierCriteriaOptionTypes,
		"EvaluatedTier":                     planMeta.EvaluatedTier,
		"EvaluatedTierCriteriaOptionTypes":  planMeta.EvaluatedTierCriteriaOptionTypes,
		"TierName":                          planMeta.TierName,
		"CtaText":                           planMeta.CtaText,
		"TierCriteriaMet":                   planMeta.TierCriteriaMet,
		"BalanceBucket":                     planMeta.BalanceBucket,
		"ComponentsDisplayed":               planMeta.ComponentsDisplayed,
		"TieringPitchPaymentOptionsEnabled": strconv.FormatBool(isPaymentOptionsDeeplinkEnabled),
		"TierPlansV2Enabled":                strconv.FormatBool(tierAllPlansV2Data.isTierPlansV2Enabled),
	}
}

func (s *Service) gatherDataForTierAllPlansV2(ctx context.Context, actorId string) (*TierAllPlansV2Data, error) {

	collectedData := &TierAllPlansV2Data{}
	gatherDataErrGrp, gCtx := errgroup.WithContext(ctx)

	gatherDataErrGrp.Go(func() error {

		tieringEssentialResp, tieringEssentialErr := s.dataCollector.GetTieringEssentials(gCtx, actorId)
		if tieringEssentialErr != nil {
			return fmt.Errorf("failed to get tiering essentials data, %w", tieringEssentialErr)
		}

		collectedData.tieringEssential = tieringEssentialResp
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		evaluatedTierForActor, evaluatedTierErr := s.beTieringClient.EvaluateTierForActor(gCtx, &beTieringPb.EvaluateTierForActorRequest{
			ActorId: actorId,
			Options: &beTieringPb.EvaluateTierForActorRequest_Options{
				ToEvalForMultipleWays: true,
			},
		})
		if rpcErr := epifigrpc.RPCError(evaluatedTierForActor, evaluatedTierErr); rpcErr != nil {
			logger.Error(context.Background(), "error fetching evaluated tier for actor", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId))
			return rpcErr
		}
		collectedData.evaluatedTierForActor = evaluatedTierForActor
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		bankCustResp, err := s.beBankCustClient.GetBankCustomer(gCtx, &bankCustomerPb.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
				ActorId: actorId,
			},
		})
		if kycStatusErr := epifigrpc.RPCError(bankCustResp, err); kycStatusErr != nil {
			return kycStatusErr
		}
		collectedData.kycLevel = bankCustResp.GetBankCustomer().GetKycInfo().GetKycLevel()
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		actorScreenInteractionResp, actorScreenInteractionErr := s.beTieringClient.GetActorScreenInteractionDetails(gCtx, &beTieringPb.GetActorScreenInteractionDetailsRequest{
			ActorId:     actorId,
			Screen:      beTieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
			RequestType: beTieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_GET,
		})
		if rpcErr := epifigrpc.RPCError(actorScreenInteractionResp, actorScreenInteractionErr); rpcErr != nil {
			if actorScreenInteractionResp.GetStatus().IsRecordNotFound() {
				actorScreenInteractionUpdateResp, actorScreenInteractionUpdateErr := s.beTieringClient.GetActorScreenInteractionDetails(gCtx, &beTieringPb.GetActorScreenInteractionDetailsRequest{
					ActorId:     actorId,
					Screen:      beTieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
					RequestType: beTieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_ADD,
				})
				if rpcErr = epifigrpc.RPCError(actorScreenInteractionUpdateResp, actorScreenInteractionUpdateErr); rpcErr != nil {
					return rpcErr
				}
				collectedData.actorScreenInteractionDetails = beTieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_UNSPECIFIED
				return nil
			} else {
				logger.Error(context.Background(), "error fetching actor screen interaction details", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId))
				return rpcErr
			}
		}
		collectedData.actorScreenInteractionDetails = actorScreenInteractionResp.GetResponse()
		if actorScreenInteractionResp.GetResponse() == beTieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_UNSPECIFIED {
			actorScreenInteractionUpdateResp, actorScreenInteractionUpdateErr := s.beTieringClient.GetActorScreenInteractionDetails(gCtx, &beTieringPb.GetActorScreenInteractionDetailsRequest{
				ActorId:     actorId,
				Screen:      beTieringEnumPb.TieringScreen_TIERING_SCREEN_TIER_ALL_PLANS_PAGE,
				RequestType: beTieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_ADD,
			})
			if rpcErr := epifigrpc.RPCError(actorScreenInteractionUpdateResp, actorScreenInteractionUpdateErr); rpcErr != nil {
				return rpcErr
			}
		}
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		getAccResp, getAccErr := s.ussAccountManagerClient.GetAccount(gCtx, &ussAccountPb.GetAccountRequest{
			Vendor:    vendorgateway.Vendor_ALPACA,
			ActorId:   actorId,
			FieldMask: &field_mask.FieldMask{Paths: []string{(&ussAccountPb.Account{}).GetAccountAccountStatusPath()}},
		})
		if rpcErr := epifigrpc.RPCError(getAccResp, getAccErr); rpcErr != nil {
			if getAccResp.GetStatus().IsRecordNotFound() {
				collectedData.isUSStocksAccountActive = false
			} else {
				logger.Error(context.Background(), "error fetching USStocks account for Tier All plans V2", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId))
				return rpcErr
			}
		}
		if getAccResp.GetAccount().GetAccountStatus() == ussAccountPb.AccountStatus_ACTIVE {
			collectedData.isUSStocksAccountActive = true
		} else {
			collectedData.isUSStocksAccountActive = false
		}
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		currBalance, balanceErr := s.dataCollector.GetBalance(gCtx, actorId)
		if balanceErr != nil {
			return balanceErr
		}
		collectedData.currBalance = currBalance
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		isUserB2BVerified, isUserB2BVerifiedErr := s.usersClient.GetB2BSalaryProgramVerificationStatus(ctx, &beUserPb.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &beUserPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
			logger.Error(ctx, "failed to get b2b verification status", zap.Error(rpcErr))
			return rpcErr
		}
		collectedData.b2bVerificationStatus = isUserB2BVerified.GetIsVerified()
		return nil
	})

	gatherDataErrGrp.Go(func() error {
		var err error
		collectedData.isTierPlansV2Enabled, err = s.releaseEvaluator.Evaluate(gCtx, pkgRelease.NewCommonConstraintData(typesPb.Feature_FEATURE_TIER_ALL_PLANS_V2_2).WithActorId(actorId))
		if err != nil {
			return pkgerrors.Wrap(err, "failed to evaluate TierPlansV2 feature")
		}
		return nil
	})

	gatherDataErr := gatherDataErrGrp.Wait()
	if gatherDataErr != nil {
		return nil, gatherDataErr
	}
	return collectedData, nil
}

func (s *Service) getAllPlansV2PlanIcon(_ context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data) *commontypes.VisualElement {
	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	return commontypes.GetVisualElementFromUrlHeightAndWidth(planData[planName].IconURL, 128, 168)
}

func (s *Service) getAllPlansV2SwipeIcon(_ context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data) *commontypes.VisualElement {
	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	return commontypes.GetVisualElementFromUrlHeightAndWidth(planData[planName].SwipeIconURL, 14, 53)
}

func (s *Service) getAllPlansV2PlanCard(ctx context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data) *feTieringPb.PlanCard {
	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	if planData == nil {
		return nil
	}

	if planData[planName] == nil || planData[planName].PlanCard == nil {
		return nil
	}

	planCard := planData[planName].PlanCard
	if planCard.BgColor == nil || planCard.BorderColor == nil || planCard.PlanInfo == nil {
		return nil
	}

	// If there are no info tiles to display, don't show the PlanCard container
	if len(planCard.PlanInfo.InfoTilesOrdering) == 0 {
		return nil
	}

	planCardResponse := &feTieringPb.PlanCard{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(planCard.BgColor.BgColorBlockColor),
		BorderProperty: &uiPb.BorderProperty{
			BorderThickness: 1,
			BorderColor:     planCard.BorderColor.BgColorBlockColor,
			BorderType:      uiPb.BorderStyle_SOLID_LINE,
		},
		Shadow: &widgetPb.Shadow{
			Height:  12,
			Blur:    32,
			Opacity: 10,
			Colour:  widgetPb.GetBlockBackgroundColour("#19000000"),
		},
	}

	// Only add Title if it exists in configuration
	if planCard.Title != nil {
		planCardResponse.Title = &commontypes.Text{
			FontColor: planCard.Title.FontColor,
			DisplayValue: &commontypes.Text_AnnotatedText_{
				AnnotatedText: &commontypes.Text_AnnotatedText{
					Spans: []*commontypes.Text_AnnotatedText_Span{
						{SpanContent: &commontypes.Text_AnnotatedText_Span_Text{Text: planCard.Title.Content}},
						{SpanContent: &commontypes.Text_AnnotatedText_Span_Icon{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(planCard.RightVisualElementImage, 20, 20),
						}},
					},
				},
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[planCard.Title.FontStyle]),
			},
		}
		// This is specifically for iOS. Since the support for annotated text is not there.
		planCardResponse.TitleItc = uiPb.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
				planCard.Title.Content,
				planCard.Title.FontColor,
				commontypes.FontStyle(commontypes.FontStyle_value[planCard.Title.FontStyle]),
			)).
			WithRightVisualElementUrlHeightAndWidth(planCard.RightVisualElementImage, 20, 20).
			WithRightImagePadding(4)
	}

	planCardResponse.PlanInfo = &feTieringPb.PlanCard_PlanInfo{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(planCard.PlanInfo.BgColor.BgColorBlockColor),
		BorderProperty: &uiPb.BorderProperty{
			BorderThickness: 1,
			BorderColor:     planCard.PlanInfo.BorderColor.BgColorBlockColor,
			BorderType:      uiPb.BorderStyle_SOLID_LINE,
		},
		InfoTiles: s.getAllPlansV2InfoTiles(ctx, planName, tierAllPlansV2Data),
		Divider:   widgetPb.GetBlockBackgroundColour(planCard.PlanInfo.DividerColor.BgColorBlockColor),
		Shadow: &widgetPb.Shadow{
			Height:  4,
			Blur:    0,
			Opacity: 100,
			Colour:  widgetPb.GetBlockBackgroundColour(planCard.PlanInfo.ShadowColor.BgColorBlockColor),
		},
	}
	planCardResponse.Deeplink = s.getAllPlansV2PlanCardDeeplink(ctx, planName, tierAllPlansV2Data)
	planCardResponse.BenefitMeta = getBenefitsMetaAsString(ctx, planName, "plan_card")

	return planCardResponse
}

func (s *Service) getAllPlansV2PlanCardDeeplink(_ context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data) *deeplinkPb.Deeplink {
	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	if planData == nil || planData[planName] == nil || planData[planName].BenefitsBottomSheet == nil ||
		planData[planName].BenefitsBottomSheet.DetailedBenefitsBottomSheet == nil ||
		planData[planName].BenefitsBottomSheet.DetailedBenefitsBottomSheet.BgColor == nil {
		return nil
	}

	screenOptions := &tiering2.BenefitsBottomSheetScreenOptions{
		PageBackgroundColour: widgetPb.GetBlockBackgroundColour(planData[planName].BenefitsBottomSheet.DetailedBenefitsBottomSheet.BgColor.BgColorBlockColor),
		HeaderBar:            &uiPb.HeaderBar{RightItc: uiPb.NewITC().WithRightImageUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/tier_all_plans_v2_cross.png", 24, 24)},
	}
	if planData[planName].PlanCard != nil && planData[planName].PlanCard.BottomSheetType != "" {
		screenOptionsMetaData := &tiering.TieringScreenMetaData{}
		switch planData[planName].PlanCard.BottomSheetType {
		case cashbackDetailsBottomSheet:
			screenOptionsMetaData.Metadata = s.getMetdataForCashbackCalculatorBottomSheet(planName)
			screenOptionsMetaDataMarshalResp, screenOptionsMetaDataMarshallErr := protojson.Marshal(screenOptionsMetaData)
			if screenOptionsMetaDataMarshallErr != nil {
				logger.Error(context.Background(), "error marshalling screen options metadata", zap.Error(screenOptionsMetaDataMarshallErr))
			}
			screenOptions.MetaData = string(screenOptionsMetaDataMarshalResp)
		default:
		}
	}
	benefitDeeplink, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_TIER_DETAILED_BENEFITS_BOTTOM_SHEET, screenOptions)
	return benefitDeeplink
}

func (s *Service) getAllPlansV2InfoTiles(_ context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data) []*feTieringPb.PlanCard_InfoTile {
	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	if planData == nil || planData[planName] == nil || planData[planName].PlanCard == nil ||
		planData[planName].PlanCard.PlanInfo == nil {
		return nil
	}

	var infoTiles []*feTieringPb.PlanCard_InfoTile

	for _, tileName := range planData[planName].PlanCard.PlanInfo.InfoTilesOrdering {
		tile := planData[planName].PlanCard.PlanInfo.InfoTiles[tileName]
		if tile == nil {
			continue
		}

		if tile.Title == nil || len(tile.Title.Texts) == 0 || tile.Rewards == nil {
			continue
		}

		infoTile := &feTieringPb.PlanCard_InfoTile{
			Title: &uiPb.IconTextComponent{
				Texts:    []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(tile.Title.Texts[0].Content, tile.Title.Texts[0].FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tile.Title.Texts[0].FontStyle]))},
				Deeplink: nil,
			},
			Rewards: commontypes.GetTextFromStringFontColourFontStyle(tile.Rewards.Content, tile.Rewards.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tile.Rewards.FontStyle])),
		}
		if tile.RewardsOn != nil {
			infoTile.RewardsOn = commontypes.GetTextFromStringFontColourFontStyle(tile.RewardsOn.Content, tile.RewardsOn.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tile.RewardsOn.FontStyle]))
		}
		if tile.RupeeIcon != nil {
			infoTile.RupeeIcon = commontypes.GetTextFromStringFontColourFontStyle(tile.RupeeIcon.Content, tile.RupeeIcon.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tile.RupeeIcon.FontStyle]))
		}
		infoTiles = append(infoTiles, infoTile)
	}
	return infoTiles
}

// nolint:ineffassign
func (s *Service) getAllPlansV2PlanBenefits(ctx context.Context, planName string, tierAllPlansV2Data *TierAllPlansV2Data, planMeta *PlanMetaData) []*feTieringPb.PlanBenefit {
	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	if planData == nil {
		return nil
	}

	var planBenefits []*feTieringPb.PlanBenefit
	feTier := beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName])

	// Check if plan data exists
	if planData[planName] == nil {
		logger.Error(ctx, "planData is nil for planName", zap.String("planName", planName))
		return planBenefits
	}

	// bannerMetas contains either "usstocks" or "fd", fetched from the entry banner.
	var bannerMetas []string

	for _, benefitName := range planData[planName].PlanBenefitsOrdering {
		benefit := planData[planName].PlanBenefits[benefitName]
		if benefit == nil {
			logger.Error(ctx, "benefit is nil for benefitName", zap.String("benefitName", benefitName), zap.String("planName", planName))
			continue
		}
		benefitData, bannerMeta := s.getAllPlansV2PlanBenefit(ctx, benefit, tierAllPlansV2Data, feTier, tierAllPlansV2Data.isTierPlansV2Enabled)

		// Collect banner metadata for analysis
		if bannerMeta != "" {
			bannerMetas = append(bannerMetas, bannerMeta)
		}

		// Build benefit card with proper styling
		benefitCard := s.buildBenefitCard(benefit, benefitData, planName, feTier, ctx, tierAllPlansV2Data)

		if benefitCard.Benefit == nil {
			continue
		}

		planBenefits = append(planBenefits, benefitCard)
	}

	// Set banner metadata for analysis
	planMeta.ComponentsDisplayed = strings.Join(bannerMetas, ",")
	return planBenefits
}

// buildBenefitCard creates a PlanBenefit card with proper styling and interactions
func (s *Service) buildBenefitCard(benefit *config.TierAllPlansV2PlanBenefit, benefitData *feTieringPb.PlanBenefit, planName string, feTier beTieringExtPb.Tier, ctx context.Context, tierAllPlansV2Data *TierAllPlansV2Data) *feTieringPb.PlanBenefit {
	if benefit == nil || benefitData == nil {
		return nil
	}

	backgroundColor := s.getBenefitBackgroundColor(benefit)

	benefitCard := &feTieringPb.PlanBenefit{
		BackgroundColor: backgroundColor,
		Benefit:         benefitData.GetBenefit(),
	}

	// Build title with visual elements and deeplinks
	if benefit.Title != nil {
		benefitCard.Title = s.buildBenefitTitle(benefit, planName, ctx, tierAllPlansV2Data)
	}

	// Set benefit metadata if bottom sheet type is specified
	if benefit.BottomSheetType != "" {
		benefitCard.BenefitMeta = getBenefitsMetaAsString(ctx, feTier.String(), benefit.BenefitType)
	}

	return benefitCard
}

// getBenefitBackgroundColor returns the appropriate background color for a benefit
func (s *Service) getBenefitBackgroundColor(benefit *config.TierAllPlansV2PlanBenefit) *widgetPb.BackgroundColour {
	if benefit == nil || benefit.BgColor == nil {
		return widgetPb.GetBlockBackgroundColour("#FFFFFF") // Default color
	}

	// Check if linear gradient is configured
	if benefit.BgColor.BgColorLinearGradientStart != "" && benefit.BgColor.BgColorLinearGradientEnd != "" {
		return widgetPb.GetLinearGradientBackgroundColour(90, []*widgetPb.ColorStop{
			{Color: benefit.BgColor.BgColorLinearGradientStart},
			{Color: benefit.BgColor.BgColorLinearGradientEnd, StopPercentage: 80},
		})
	}

	return widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor)
}

// buildBenefitTitle creates the title component with visual elements and deeplinks
func (s *Service) buildBenefitTitle(benefit *config.TierAllPlansV2PlanBenefit, planName string, ctx context.Context, tierAllPlansV2Data *TierAllPlansV2Data) *uiPb.IconTextComponent {
	if benefit == nil || benefit.Title == nil {
		return nil
	}

	benefitTitle := &uiPb.IconTextComponent{
		Deeplink: nil,
	}

	// Add text components
	for _, text := range benefit.Title.Texts {
		if text == nil {
			continue
		}
		textComp := commontypes.GetTextFromStringFontColourFontStyle(
			text.Content,
			text.FontColor,
			commontypes.FontStyle(commontypes.FontStyle_value[text.FontStyle]),
		)
		benefitTitle.Texts = append(benefitTitle.Texts, textComp)
	}

	if benefit.BottomSheetType != "" {
		s.addBottomSheetDeeplink(benefitTitle, benefit, planName, ctx, tierAllPlansV2Data.isTierPlansV2Enabled)
	}

	// Handle left visual element
	if benefit.Title.LeftVisualElementImage != nil {
		benefitTitle.LeftVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(
			benefit.Title.LeftVisualElementImage.Url, 20, 20,
		)
	}

	// Handle right visual element
	if benefit.Title.RightVisualElementImage != nil {
		benefitTitle.RightVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(
			benefit.Title.RightVisualElementImage.Url, 20, 20,
		)
	}

	return benefitTitle
}

// addBottomSheetDeeplink adds deeplink support for bottom sheet interactions
func (s *Service) addBottomSheetDeeplink(benefitTitle *uiPb.IconTextComponent, benefit *config.TierAllPlansV2PlanBenefit, planName string, ctx context.Context, isTierPlansV2Enabled bool) {
	planData := s.getPlanData(isTierPlansV2Enabled)

	// Add nil checks for planData
	if planData == nil || planData[planName] == nil || planData[planName].BenefitsBottomSheet == nil ||
		planData[planName].BenefitsBottomSheet.DetailedBenefitsBottomSheet == nil ||
		planData[planName].BenefitsBottomSheet.DetailedBenefitsBottomSheet.BgColor == nil {
		return
	}

	screenOptions := &tiering2.BenefitsBottomSheetScreenOptions{
		PageBackgroundColour: widgetPb.GetBlockBackgroundColour(planData[planName].BenefitsBottomSheet.DetailedBenefitsBottomSheet.BgColor.BgColorBlockColor),
		HeaderBar: &uiPb.HeaderBar{
			RightItc: uiPb.NewITC().WithRightImageUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/tier_all_plans_v2_cross.png", 24, 24),
		},
	}

	if benefit.BottomSheetType != "" {
		screenOptionsMetaData := &tiering.TieringScreenMetaData{}

		switch benefit.BottomSheetType {
		case detailedBenefitsBottomSheet:
			screenOptionsMetaData.Metadata = s.getMetadataForDetailedBenefitsBottomSheet(planName)
		case waysToUseFiPointsBottomSheet:
			screenOptionsMetaData.Metadata = s.getMetadataForWaysToUseFiPointsBottomSheet(planName)
		}

		// Marshal metadata
		if screenOptionsMetaData.Metadata != nil {
			screenOptionsMetaDataMarshalResp, err := protojson.Marshal(screenOptionsMetaData)
			if err != nil {
				logger.Error(ctx, "error marshalling screen options metadata", zap.Error(err))
			} else {
				screenOptions.MetaData = string(screenOptionsMetaDataMarshalResp)
			}
		}
	}

	benefitDeeplink, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_TIER_DETAILED_BENEFITS_BOTTOM_SHEET, screenOptions)
	benefitTitle.Deeplink = benefitDeeplink
}

func (s *Service) getMetdataForCashbackCalculatorBottomSheet(tier string) *tiering.TieringScreenMetaData_CashbackDetailsBottomSheet {
	return &tiering.TieringScreenMetaData_CashbackDetailsBottomSheet{
		CashbackDetailsBottomSheet: &tiering.CashbackDetailsBottomSheet{
			Tier: tier,
		},
	}
}

func (s *Service) getMetadataForDetailedBenefitsBottomSheet(tier string) *tiering.TieringScreenMetaData_DetailedBenefitsScreenMetadata {
	return &tiering.TieringScreenMetaData_DetailedBenefitsScreenMetadata{
		DetailedBenefitsScreenMetadata: &tiering.DetailedBenefitsScreenMetadata{
			Tier: tier,
		},
	}
}

func (s *Service) getMetadataForWaysToUseFiPointsBottomSheet(tier string) *tiering.TieringScreenMetaData_WaysToUseFiPointsBottomSheet {
	return &tiering.TieringScreenMetaData_WaysToUseFiPointsBottomSheet{
		WaysToUseFiPointsBottomSheet: &tiering.WaysToUseFiPointsBottomSheet{
			Tier: tier,
		},
	}
}

func (s *Service) getAllPlansV2PlanBenefit(ctx context.Context, benefit *config.TierAllPlansV2PlanBenefit, tierAllPlansV2Data *TierAllPlansV2Data, tier beTieringExtPb.Tier, _ bool) (*feTieringPb.PlanBenefit, string) {
	if benefit == nil {
		logger.Error(ctx, "benefit is nil in getAllPlansV2PlanBenefit")
		return nil, ""
	}

	switch benefit.BenefitType {
	case benefitCardBanner:
		return s.getPlanBenefitWithBenefitCardBanner(benefit, tier.String(), ctx, tierAllPlansV2Data), ""
	case entryBanner:
		return s.getPlanBenefitWithEntryBanner(ctx, benefit, tierAllPlansV2Data, tier)
	case infoBanner:
		return s.getPlanBenefitWithInfoBanner(benefit), ""
	case learnMoreBanner:
		return s.getPlanBenefitWithLearnMoreBanner(benefit), ""
	case penaltyBanner:
		return s.getPlanBenefitWithPenaltyBanner(benefit, tier), ""
	default:
		return nil, ""
	}
}

func (s *Service) getPlanBenefitWithPenaltyBanner(benefit *config.TierAllPlansV2PlanBenefit, tier beTieringExtPb.Tier) *feTieringPb.PlanBenefit {
	return s.getPlanBenefitWithPenaltyBannerAsSmallEntryBanner(benefit, tier)
}

func (s *Service) getPlanBenefitWithPenaltyBannerAsSmallEntryBanner(benefit *config.TierAllPlansV2PlanBenefit, _ beTieringExtPb.Tier) *feTieringPb.PlanBenefit {
	if benefit == nil {
		return nil
	}

	// Check if penalty banner exists in config
	if benefit.Benefit == nil || benefit.Benefit.EntryBanner == nil || benefit.Benefit.EntryBanner.SmallEntryBanner == nil {
		logger.Error(context.Background(), "penalty banner config is missing", zap.String("benefitType", benefit.BenefitType))
		return nil
	}

	penaltyBannerData, exists := benefit.Benefit.EntryBanner.SmallEntryBanner[penaltyBanner]
	if !exists {
		logger.Error(context.Background(), "penalty banner data not found in config", zap.String("penaltyBanner", penaltyBanner))
		return nil
	}

	// Check if required fields exist
	if penaltyBannerData.BgColor == nil || penaltyBannerData.LeftIcon == nil || penaltyBannerData.Content == nil {
		logger.Error(context.Background(), "penalty banner data has missing required fields")
		return nil
	}

	// Build background color
	var backgroundColour *widgetPb.BackgroundColour
	if penaltyBannerData.BgColor.BgColorLinearGradientStart != "" && penaltyBannerData.BgColor.BgColorLinearGradientEnd != "" {
		backgroundColour = widgetPb.GetLinearGradientBackgroundColour(90, []*widgetPb.ColorStop{
			{Color: penaltyBannerData.BgColor.BgColorLinearGradientStart},
			{Color: penaltyBannerData.BgColor.BgColorLinearGradientEnd, StopPercentage: 80},
		})
	} else {
		backgroundColour = widgetPb.GetBlockBackgroundColour(penaltyBannerData.BgColor.BgColorBlockColor)
	}

	// Build small entry banner
	smallEntryBanner := &feTieringPb.EntryBannerSmall{
		BackgroundColour: backgroundColour,
		LeftIcon:         commontypes.GetVisualElementFromUrlHeightAndWidth(penaltyBannerData.LeftIcon.Url, 32, 32),
		Content:          commontypes.GetTextFromStringFontColourFontStyle(penaltyBannerData.Content.Content, penaltyBannerData.Content.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[penaltyBannerData.Content.FontStyle])),
	}

	// Add RightCta with deeplink if it exists
	if penaltyBannerData.RightCta != nil {
		rightCta := uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(penaltyBannerData.RightCta.Text, penaltyBannerData.RightCta.FontColor, commontypes.FontStyle(commontypes.FontStyle_value["BUTTON_S"]))).
			WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
				BgColor:       penaltyBannerData.RightCta.BgColor,
				CornerRadius:  20,
				LeftPadding:   16,
				RightPadding:  16,
				TopPadding:    8,
				BottomPadding: 8,
			})

		// Use deeplink from YAML configuration if available
		if penaltyBannerData.RightCta.Deeplink != nil {
			deeplink := s.convertConfigDeeplinkToProtobuf(penaltyBannerData.RightCta.Deeplink)
			if deeplink != nil {
				rightCta = rightCta.WithDeeplink(deeplink)
			}
		} else {
			// Fallback to infinite plan deeplink for penalty banners without YAML deeplink
			infinitePlanDeeplink := s.getInfinitePlanDeeplink()
			if infinitePlanDeeplink != nil {
				rightCta = rightCta.WithDeeplink(infinitePlanDeeplink)
			}
		}
		smallEntryBanner.RightCta = rightCta
	}

	return &feTieringPb.PlanBenefit{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor),
		Benefit: &feTieringPb.PlanBenefit_SmallEntryBanner{
			SmallEntryBanner: smallEntryBanner,
		},
	}
}

// getInfinitePlanDeeplink returns the deeplink to Infinite plan (fallback)
func (s *Service) getInfinitePlanDeeplink() *deeplinkPb.Deeplink {
	metaData := &tieringTypesPb.TieringScreenMetaData{
		Metadata: &tieringTypesPb.TieringScreenMetaData_PlansV2Metadata{
			PlansV2Metadata: &tieringTypesPb.PlansV2Metadata{
				TierToFocus: beTieringExtPb.Tier_TIER_FI_INFINITE.String(),
			},
		},
	}

	metaDataMarshalled, metaDataErr := protojson.Marshal(metaData)
	if metaDataErr != nil {
		logger.Error(context.Background(), "error marshalling tiering screen metadata", zap.Error(metaDataErr))
		return nil
	}

	screenOptions := &tiering2.AllPlansV2ScreenOptions{
		Title:      commontypes.GetTextFromStringFontColourFontStyle("Plans", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
		IdxToFocus: 2, // 0=Basic, 1=Plus, 2=Infinite
		MetaData:   string(metaDataMarshalled),
	}

	deeplink := &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_TIER_ALL_PLANS_SCREEN_V2,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(screenOptions),
	}

	if deeplink.ScreenOptionsV2 != nil {
		return deeplink
	}
	return nil
}

func (s *Service) getPlanBenefitWithBenefitCardBanner(benefit *config.TierAllPlansV2PlanBenefit, planName string, ctx context.Context, tierAllPlansV2Data *TierAllPlansV2Data) *feTieringPb.PlanBenefit {
	var backgroundColor *widgetPb.BackgroundColour
	// Add nil check for benefit.BgColor
	if benefit.BgColor != nil {
		backgroundColor = widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor)
	} else {
		backgroundColor = widgetPb.GetBlockBackgroundColour("#FFFFFF") // Default color
	}
	planBenefit := &feTieringPb.PlanBenefit{
		BackgroundColor: backgroundColor,
		Title: &uiPb.IconTextComponent{
			Deeplink: nil,
		},
		Benefit: &feTieringPb.PlanBenefit_BenefitCard{
			BenefitCard: &feTieringPb.BenefitCard{
				BenefitTileRows: s.getBenefitTileRows(benefit.Benefit.BenefitCard, benefit, planName, ctx, tierAllPlansV2Data),
			},
		},
		// TODO: Add deeplink
		Deeplink: nil,
	}
	if benefit.Title != nil {
		for _, text := range benefit.Title.Texts {
			textComp := commontypes.GetTextFromStringFontColourFontStyle(text.Content, text.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[text.FontStyle]))
			planBenefit.Title.Texts = append(planBenefit.Title.Texts, textComp)
		}

		if benefit.Title.LeftVisualElementImage != nil {
			planBenefit.Title.LeftVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(benefit.Title.LeftVisualElementImage.Url, 20, 20)
		}
		if benefit.Title.RightVisualElementImage != nil {
			planBenefit.Title.RightVisualElement = commontypes.GetVisualElementImageFromUrl(benefit.Title.RightVisualElementImage.Url)
		}
	}
	return planBenefit
}

func (s *Service) getBenefitTileRows(benefitCard *config.TierAllPlansV2BenefitCard, parentBenefit *config.TierAllPlansV2PlanBenefit, planName string, ctx context.Context, tierAllPlansV2Data *TierAllPlansV2Data) []*feTieringPb.BenefitCard_BenefitTileRow {
	if benefitCard == nil {
		return nil
	}

	var benefitTileRows []*feTieringPb.BenefitCard_BenefitTileRow

	for _, benefitTilesRow := range benefitCard.BenefitTilesRowsOrdering {
		row := benefitCard.BenefitTileRows[benefitTilesRow]
		if row == nil {
			continue
		}
		benefitTileRow := &feTieringPb.BenefitCard_BenefitTileRow{
			BenefitTiles: s.getBenefitTiles(row, parentBenefit, planName, ctx, tierAllPlansV2Data),
		}
		benefitTileRows = append(benefitTileRows, benefitTileRow)
	}
	return benefitTileRows
}

func (s *Service) getBenefitTiles(row *config.TierAllPlansV2BenefitTileRow, parentBenefit *config.TierAllPlansV2PlanBenefit, planName string, ctx context.Context, tierAllPlansV2Data *TierAllPlansV2Data) []*feTieringPb.BenefitTile {
	var tiles []*feTieringPb.BenefitTile

	for _, benefitTiles := range row.BenefitTileOrdering {
		tile := row.BenefitTile[benefitTiles]
		if tile == nil {
			continue
		}
		if tile.BgColor == nil || tile.Title == nil {
			continue
		}

		benefitTile := &feTieringPb.BenefitTile{
			BackgroundColor: widgetPb.GetBlockBackgroundColour(tile.BgColor.BgColorBlockColor),
			BorderProperty: &uiPb.BorderProperty{
				BorderThickness: 1,
				BorderType:      uiPb.BorderStyle_SOLID_LINE,
			},
			Title:    commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(tile.Title.Content, nil), tile.Title.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tile.Title.FontStyle])),
			Style:    feTieringPb.BenefitTile_CardStyle(feTieringPb.BenefitTile_CardStyle_value[tile.Style]),
			Deeplink: nil,
		}
		imageUrl := accrualPkg.ReturnApplicableValue(tile.Icon.ImageUrl, constants.FiPointsAllPlansPageIcon, timestampPb.Now(), true).(string)
		if benefitTile.GetStyle() == feTieringPb.BenefitTile_SINGLE {
			benefitTile.Icon = commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrl, 60, 60)
		} else if benefitTile.GetStyle() == feTieringPb.BenefitTile_TRIPLE_CARD_LEFT_ICON {
			benefitTile.Icon = commontypes.GetVisualElementFromUrlHeightAndWidth(tile.Icon.ImageUrl, 40, 40)
		} else {
			benefitTile.Icon = commontypes.GetVisualElementFromUrlHeightAndWidth(tile.Icon.ImageUrl, 28, 28)
		}
		if tile.BorderColor != nil {
			benefitTile.BorderProperty.BorderColor = tile.BorderColor.BgColorBlockColor
		} else {
			benefitTile.BorderProperty.BorderColor = tile.BgColor.BgColorBlockColor
		}
		if tile.Subtitle != nil {
			benefitTile.Subtitle = commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(tile.Subtitle.Content, nil), tile.Subtitle.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tile.Subtitle.FontStyle])).WithMaxLines(3)
		}

		if parentBenefit != nil && parentBenefit.BottomSheetType != "" {
			tempTitle := &uiPb.IconTextComponent{}
			s.addBottomSheetDeeplink(tempTitle, parentBenefit, planName, ctx, tierAllPlansV2Data.isTierPlansV2Enabled)
			benefitTile.Deeplink = tempTitle.Deeplink
		}

		tiles = append(tiles, benefitTile)
	}
	return tiles
}

func (s *Service) getScreenOptionsForUSStocksAddFunds(tier beTieringExtPb.Tier) *ussScreenOptions.USStocksAddFundsScreenOptions {
	return &ussScreenOptions.USStocksAddFundsScreenOptions{
		EntryPoint: deeplinkPb.Screen_TIER_ALL_PLANS_SCREEN_V2,
		UsStocksScreenMetadata: &ussMetadata.USStocksScreenMetadata{
			DropOffBottomSheetId: s.getDropOffBottomSheetIdForTierAllPlansV2(tier),
		},
	}
}

func (s *Service) getDropOffBottomSheetIdForTierAllPlansV2(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_PITCH_PLUS.String()
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_PITCH_INFINITE.String()
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_PITCH_PRIME.String()
	default:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_UNSPECIFIED.String()
	}
}

func (s *Service) getScreenOptionsForDepositsAddFunds() *deeplinkPb.DepositTemplatesScreenOptions {
	return &deeplinkPb.DepositTemplatesScreenOptions{
		DepositType: accounts.Type_FIXED_DEPOSIT,
	}
}

// nolint:ineffassign
func (s *Service) getPlanBenefitWithLargeEntryBanner(ctx context.Context, benefit *config.TierAllPlansV2PlanBenefit, tierAllPlansV2Data *TierAllPlansV2Data, tier beTieringExtPb.Tier) (*feTieringPb.PlanBenefit, string) {

	var entryPoint string
	var largeEntryBannerDeeplink *deeplinkPb.Deeplink
	if tierAllPlansV2Data.isUSStocksAccountActive {
		entryPoint = entryPoint_USStocks
		largeEntryBannerDeeplink, _ = deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_USSTOCKS_WALLET_ADD_FUNDS_SCREEN, s.getScreenOptionsForUSStocksAddFunds(tier))
	} else {
		entryPoint = entryPoint_Deposits
		largeEntryBannerDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_TEMPLATES_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositTemplatesScreenOptions{
				DepositTemplatesScreenOptions: &deeplinkPb.DepositTemplatesScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		}
	}

	entryBannerData := benefit.Benefit.EntryBanner.LargeEntryBanner[entryPoint]

	return &feTieringPb.PlanBenefit{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor),
		Benefit: &feTieringPb.PlanBenefit_EntryBanner{
			EntryBanner: &feTieringPb.EntryBanner{
				BackgroundColour: widgetPb.GetLinearGradientBackgroundColour(90, []*widgetPb.ColorStop{
					{
						Color: entryBannerData.BgColor.BgColorLinearGradientStart,
					},
					{
						Color:          entryBannerData.BgColor.BgColorLinearGradientEnd,
						StopPercentage: 80,
					},
				}),
				BannerImage: commontypes.GetVisualElementFromUrlHeightAndWidth(entryBannerData.BannerImage.Url, 140, 162),
				Content:     commontypes.GetTextFromStringFontColourFontStyle(entryBannerData.Content.Content, entryBannerData.Content.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[entryBannerData.Content.FontStyle])),
				RightCta: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(entryBannerData.RightCta.Text, entryBannerData.RightCta.FontColor, commontypes.FontStyle(commontypes.FontStyle_value["BUTTON_S"]))).
					WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
						BgColor:       entryBannerData.RightCta.BgColor,
						CornerRadius:  12,
						LeftPadding:   8,
						RightPadding:  8,
						TopPadding:    8,
						BottomPadding: 8,
					}).WithDeeplink(largeEntryBannerDeeplink).WithRightVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/tiering/tier_all_plans_v2_right_icon.png", 16, 16),
				TopContent: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(entryBannerData.TopContent.Text, entryBannerData.TopContent.FontColor, commontypes.FontStyle(commontypes.FontStyle_value["BUTTON_S"]))).
					WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
						BgColor:       entryBannerData.TopContent.BgColor,
						CornerRadius:  12,
						LeftPadding:   8,
						RightPadding:  8,
						TopPadding:    2,
						BottomPadding: 2,
					}),
				BannerMeta: getEntryBannerMetaAsString(ctx, tier.String(), benefit.BenefitType, entryPoint),
			},
		},
	}, entryPoint
}

// nolint:ineffassign
func (s *Service) getPlanBenefitWithSmallEntryBanner(ctx context.Context, benefit *config.TierAllPlansV2PlanBenefit, tierAllPlansV2Data *TierAllPlansV2Data, tier beTieringExtPb.Tier) (*feTieringPb.PlanBenefit, string) {

	var entryPoint string
	var smallEntryBannerDeeplink *deeplinkPb.Deeplink

	if tierAllPlansV2Data.isUSStocksAccountActive {
		entryPoint = entryPoint_USStocks
		smallEntryBannerDeeplink, _ = deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_USSTOCKS_WALLET_ADD_FUNDS_SCREEN, s.getScreenOptionsForUSStocksAddFunds(tier))
	} else {
		entryPoint = entryPoint_Deposits
		smallEntryBannerDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_TEMPLATES_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositTemplatesScreenOptions{
				DepositTemplatesScreenOptions: &deeplinkPb.DepositTemplatesScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		}
	}

	entryBannerData := benefit.Benefit.EntryBanner.SmallEntryBanner[entryPoint]

	return &feTieringPb.PlanBenefit{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor),
		Benefit: &feTieringPb.PlanBenefit_SmallEntryBanner{
			SmallEntryBanner: &feTieringPb.EntryBannerSmall{
				BackgroundColour: widgetPb.GetLinearGradientBackgroundColour(90, []*widgetPb.ColorStop{
					{
						Color: entryBannerData.BgColor.BgColorLinearGradientStart,
					},
					{
						Color:          entryBannerData.BgColor.BgColorLinearGradientEnd,
						StopPercentage: 80,
					},
				}),
				LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(entryBannerData.LeftIcon.Url, 32, 32),
				Content:  commontypes.GetTextFromStringFontColourFontStyle(entryBannerData.Content.Content, entryBannerData.Content.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[entryBannerData.Content.FontStyle])),
				RightCta: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(entryBannerData.RightCta.Text, entryBannerData.RightCta.FontColor, commontypes.FontStyle(commontypes.FontStyle_value["BUTTON_S"]))).
					WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
						BgColor:       entryBannerData.RightCta.BgColor,
						CornerRadius:  20,
						LeftPadding:   16,
						RightPadding:  16,
						TopPadding:    8,
						BottomPadding: 8,
					}).WithDeeplink(smallEntryBannerDeeplink),
				BannerMeta: getEntryBannerMetaAsString(ctx, tier.String(), benefit.BenefitType, entryPoint),
			},
		},
	}, entryPoint
}

func (s *Service) getPlanBenefitWithEntryBanner(ctx context.Context, benefit *config.TierAllPlansV2PlanBenefit, tierAllPlansV2Data *TierAllPlansV2Data, tier beTieringExtPb.Tier) (*feTieringPb.PlanBenefit, string) {

	if !s.gconf.Tiering().TierAllPlansV2ConfigParams().ShouldShowEntryBanner() {
		return nil, ""
	}
	switch tierAllPlansV2Data.actorScreenInteractionDetails {
	case beTieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_UNSPECIFIED:
		return s.getPlanBenefitWithSmallEntryBanner(ctx, benefit, tierAllPlansV2Data, tier)
	case beTieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_VISITED:
		return s.getPlanBenefitWithLargeEntryBanner(ctx, benefit, tierAllPlansV2Data, tier)
	default:
		return nil, ""
	}
}

func (s *Service) getPlanBenefitWithInfoBanner(benefit *config.TierAllPlansV2PlanBenefit) *feTieringPb.PlanBenefit {
	if benefit == nil || benefit.Benefit == nil || benefit.Benefit.InfoBanner == nil || benefit.BgColor == nil {
		return nil
	}

	return &feTieringPb.PlanBenefit{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor),
		Benefit: &feTieringPb.PlanBenefit_InfoBanner{
			InfoBanner: &feTieringPb.InfoBanner{
				BackgroundColour: widgetPb.GetBlockBackgroundColour(benefit.Benefit.InfoBanner.BgColor.BgColorBlockColor),
				Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(benefit.Benefit.InfoBanner.Title.Texts[0].Content, benefit.Benefit.InfoBanner.Title.Texts[0].FontColor, commontypes.FontStyle(commontypes.FontStyle_value[benefit.Benefit.InfoBanner.Title.Texts[0].FontStyle]))).
					WithLeftImageUrlHeightAndWidth(benefit.Benefit.InfoBanner.Title.LeftVisualElementImage.Url, 32, 32),
				Content: commontypes.GetTextFromStringFontColourFontStyle(benefit.Benefit.InfoBanner.Content.Content, benefit.Benefit.InfoBanner.Content.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[benefit.Benefit.InfoBanner.Content.FontStyle])),
			},
		},
		// TODO: Add deeplink
		Deeplink: nil,
	}
}

func (s *Service) getPlanBenefitWithLearnMoreBanner(benefit *config.TierAllPlansV2PlanBenefit) *feTieringPb.PlanBenefit {
	if benefit == nil || benefit.BgColor == nil {
		return nil
	}

	// Check if title exists
	var title *uiPb.IconTextComponent
	if benefit.Title != nil && len(benefit.Title.Texts) > 0 {
		title = &uiPb.IconTextComponent{
			Texts:    []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(benefit.Title.Texts[0].Content, benefit.Title.Texts[0].FontColor, commontypes.FontStyle(commontypes.FontStyle_value[benefit.Title.Texts[0].FontStyle]))},
			Deeplink: nil,
		}
	}

	return &feTieringPb.PlanBenefit{
		BackgroundColor: widgetPb.GetBlockBackgroundColour(benefit.BgColor.BgColorBlockColor),
		Title:           title,
		Benefit: &feTieringPb.PlanBenefit_LearnMoreBanner{
			LearnMoreBanner: &feTieringPb.LearnMoreBanner{
				Banners: s.getBanners(benefit),
			},
		},
		// TODO: Add deeplink
		Deeplink: nil,
	}
}

func (s *Service) getBanners(benefit *config.TierAllPlansV2PlanBenefit) []*uiPb.VerticalIconTextComponent {
	if benefit == nil || benefit.Benefit == nil || benefit.Benefit.LearnMoreBanner == nil {
		return nil
	}

	var banners []*uiPb.VerticalIconTextComponent

	for _, banner := range benefit.Benefit.LearnMoreBanner.Banners {
		if banner == nil || banner.TopVisualElement == nil || banner.Text == nil {
			continue
		}

		bannerComponent := &uiPb.VerticalIconTextComponent{
			TopVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(banner.TopVisualElement.Url, 88, 88),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(banner.Text.Content, banner.Text.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[banner.Text.FontStyle])),
			},
			TopImgTxtPadding: 8,
		}

		// Add deeplink if configured in YAML
		if banner.Deeplink != nil {
			// Convert config deeplink to protobuf deeplink
			bannerComponent.Deeplink = s.convertConfigDeeplinkToProtobuf(banner.Deeplink)
		}

		banners = append(banners, bannerComponent)
	}
	return banners
}

// convertConfigDeeplinkToProtobuf converts a config deeplink to protobuf deeplink
func (s *Service) convertConfigDeeplinkToProtobuf(configDeeplink *deeplinkCfg.Deeplink) *deeplinkPb.Deeplink {
	if configDeeplink == nil {
		return nil
	}

	// Convert screen string to protobuf enum
	screen, exists := deeplinkPb.Screen_value[configDeeplink.Screen]
	if !exists {
		return nil
	}

	protobufDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen(screen),
	}

	// Handle different screen options based on the screen type
	switch configDeeplink.Screen {
	case deeplinkPb.Screen_WEB_PAGE.String():
		// For web pages, we need to create WebPageScreenOptions
		if configDeeplink.ExternalRedirectionScreenOptions != nil && configDeeplink.ExternalRedirectionScreenOptions.ExternalUrl != "" {
			protobufDeeplink.ScreenOptions = &deeplinkPb.Deeplink_WebPageScreenOptions{
				WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
					WebpageTitle: "Learn More",
					WebpageUrl:   configDeeplink.ExternalRedirectionScreenOptions.ExternalUrl,
				},
			}
		} else {
			return nil
		}
	case deeplinkPb.Screen_TIER_ALL_PLANS_SCREEN_V2.String():
		// For tier all plans screen, we need to create AllPlansV2ScreenOptions
		if configDeeplink.ScreenOptions != nil && configDeeplink.ScreenOptions.TierAllPlansScreenOptions != nil {
			// Use the existing infinite plan deeplink logic but with custom index
			infiniteDeeplink := s.getInfinitePlanDeeplink()
			if infiniteDeeplink != nil && infiniteDeeplink.ScreenOptionsV2 != nil {
				// Extract the screen options and update the index
				if screenOptionsAny := infiniteDeeplink.ScreenOptionsV2; screenOptionsAny != nil {
					var screenOptions tiering2.AllPlansV2ScreenOptions
					if err := screenOptionsAny.UnmarshalTo(&screenOptions); err == nil {
						screenOptions.IdxToFocus = configDeeplink.ScreenOptions.TierAllPlansScreenOptions.TierIndexToFocus
						protobufDeeplink.ScreenOptionsV2 = deeplinkv3.GetScreenOptionV2WithoutError(&screenOptions)
					}
				}
			}
		} else {
			return nil
		}
	default:
		return nil
	}

	return protobufDeeplink
}

// nolint:govet
func (s *Service) getAllPlansV2Cta(ctx context.Context, planName string, actorId string, tierAllPlansV2Data *TierAllPlansV2Data, planMeta *PlanMetaData) *deeplinkPb.Cta {

	tieringPitchV2Resp := tierAllPlansV2Data.tieringEssential.TieringPitchResp
	evaluateTierResp := tierAllPlansV2Data.evaluatedTierForActor
	feTier := beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName])
	currTier := tieringPitchV2Resp.GetCurrentTier()
	// cat is not visible for current plan, Standard and Regular Tier
	if ((currTier == feTier) && !currTier.IsSalaryTier()) || feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		return nil
	}

	// TODO : create a method for getting the CTA for lower tiers
	switch tieringPitchV2Resp.GetCurrentTier() {
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		if !feTier.IsSalaryTier() {
			return nil
		}
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		if feTier == beTieringExtPb.Tier_TIER_FI_PLUS || feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
			return nil
		}
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		if feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
			return nil
		}
	}

	var ctaTextToSet string
	defer func() {
		planMeta.CtaText = ctaTextToSet
	}()

	if currTier.IsSalaryTier() {
		if currTier == feTier {
			dl, _ := earnedBenefits.GetEarnedBenefitsDeeplink(currTier)
			ctaTextToSet = "View benefits"
			return &deeplinkPb.Cta{
				Text:         ctaTextToSet,
				Deeplink:     dl,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       0,
			}
		} else {
			return nil
		}
	}

	if tierAllPlansV2Data.kycLevel != kycPb.KYCLevel_FULL_KYC {
		vkycDeeplinkCreation, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
			EntryPoint: vkyc.EntryPoint_ENTRY_POINT_TIER_ALL_PLANS,
		})
		ctaTextToSet = "Upgrade to full KYC"
		return &deeplinkPb.Cta{
			Text:         ctaTextToSet,
			Deeplink:     vkycDeeplinkCreation,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	}

	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)
	if helper.IsFeTieLowerThanOrEqualToCurrentEvaluatedTier(feTier, evaluateTierResp.GetEvaluatedTier()) {
		ctaTextToSet = "Upgrade for free"
		return &deeplinkPb.Cta{
			Text:         ctaTextToSet,
			Deeplink:     s.getDeeplinkForManualUpgradeWithScreenOptionsV2(),
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	}

	ctaTextToSet = planData[planName].Cta.Text
	var diffAmountToAdd *money.Money
	if !feTier.IsSalaryTier() {
		calculatedAmount, err := helper.CalculateAmountNeededForTier(feTier, tieringPitchV2Resp.GetMovementDetailsList(), tierAllPlansV2Data.currBalance)
		if err != nil {
			logger.Error(ctx, "error in calculating amount needed for tier", zap.Error(err))
			return nil
		}
		diffAmountToAdd = calculatedAmount
	}

	isPaymentOptionsDeeplinkEnabled, _, err := s.abEvaluatorGeneric.Evaluate(ctx, pkgRelease.NewCommonConstraintData(typesPb.Feature_FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating release", zap.Error(err))
	}

	return &deeplinkPb.Cta{
		Text:         ctaTextToSet,
		Deeplink:     s.getDeeplinkForLoaderScreenWithScreenOptionsV2(ctx, planName, diffAmountToAdd, isPaymentOptionsDeeplinkEnabled, actorId),
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Status:       0,
	}
}

func (s *Service) getAllPlansV2CtaText(plan beTieringExtPb.Tier, tierAllPlansV2Data *TierAllPlansV2Data) string {

	var currTierMinBalanceCriteria *money.Money

	tierCriteriaMinValues := tierAllPlansV2Data.tieringEssential.GetTierCriteriaMinValuesMap()[plan]
	for _, tierCriteriaMinValue := range tierCriteriaMinValues {
		if tierCriteriaMinValue.Criteria == beTieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC {
			currTierMinBalanceCriteria = tierCriteriaMinValue.MinValue
			break
		}
	}

	balanceDiff := currTierMinBalanceCriteria.GetUnits() - tierAllPlansV2Data.currBalance.GetUnits()
	return s.getDiffBalanceCTAText(plan, balanceDiff)
}

// nolint:gocritic
func (s *Service) getDiffBalanceCTAText(plan beTieringExtPb.Tier, balanceDiff int64) string {
	switch plan {
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		if balanceDiff > s.gconf.Tiering().DeltaBalanceParams().BalanceDiffFirstForInfinite() {
			return s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffFirstForInfinite()
		} else if balanceDiff > s.gconf.Tiering().DeltaBalanceParams().BalanceDiffSecondForInfinite() {
			return fmt.Sprintf(s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffSecondForInfinite(), balanceDiff)
		} else {
			return fmt.Sprintf(s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffThirdForInfinite(), balanceDiff)
		}
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		if balanceDiff > s.gconf.Tiering().DeltaBalanceParams().BalanceDiffFirstForPrime() {
			return s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffFirstForPrime()
		} else if balanceDiff > s.gconf.Tiering().DeltaBalanceParams().BalanceDiffSecondForPrime() {
			return fmt.Sprintf(s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffSecondForPrime(), balanceDiff)
		} else if balanceDiff > s.gconf.Tiering().DeltaBalanceParams().BalanceDiffThirdForPrime() {
			return fmt.Sprintf(s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffThirdForPrime(), balanceDiff)
		} else {
			return fmt.Sprintf(s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffFourthForPrime(), balanceDiff)
		}
	default:
		return s.gconf.Tiering().DeltaBalanceParams().TextForBalanceDiffFirstForPrime()
	}
}

func (s *Service) getDeeplinkForLoaderScreenWithScreenOptionsV2(ctx context.Context, planName string, amount *money.Money, isPaymentOptionsDeeplinkEnabled bool, actorId string) *deeplinkPb.Deeplink {

	var pageBgColor *widgetPb.BackgroundColour
	var planInfo *commontypes.Text
	animationTheme := tiering2.LoaderScreenOptions_LIGHT

	// Evaluate the TierPlansV2 feature flag
	isTierPlansV2Enabled, err := s.releaseEvaluator.Evaluate(ctx, pkgRelease.NewCommonConstraintData(typesPb.Feature_FEATURE_TIER_ALL_PLANS_V2_2).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating TierPlansV2 feature flag", zap.Error(err))
		// Default to false if evaluation fails
		isTierPlansV2Enabled = false
	}

	planData := s.getPlanData(isTierPlansV2Enabled)
	planImage := commontypes.GetVisualElementFromUrlHeightAndWidth(planData[planName].LoaderIconUrl, 128, 168)
	switch beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName]) {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		pageBgColor = widgetPb.GetLinearGradientBackgroundColour(0, []*widgetPb.ColorStop{{Color: "#FFFFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFFFF"}})
		planInfo = commontypes.GetTextFromStringFontColourFontStyle("Enjoy seamless payments & smarter money management", "#313234", commontypes.FontStyle_SUBTITLE_S)
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		pageBgColor = widgetPb.GetLinearGradientBackgroundColour(0, []*widgetPb.ColorStop{{Color: "#FFFFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFFFF"}})
		planInfo = commontypes.GetTextFromStringFontColourFontStyle("Enjoy seamless payments & smarter money management", "#313234", commontypes.FontStyle_SUBTITLE_S)
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		pageBgColor = widgetPb.GetLinearGradientBackgroundColour(0, []*widgetPb.ColorStop{{Color: "#FFFFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFFFF"}})
		planInfo = commontypes.GetTextFromStringFontColourFontStyle("Enjoy seamless payments & smarter money management", "#313234", commontypes.FontStyle_SUBTITLE_S)
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		pageBgColor = widgetPb.GetLinearGradientBackgroundColour(0, []*widgetPb.ColorStop{{Color: "#FFFFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFF"}, {Color: "#FFFFFFFF"}})
		planInfo = commontypes.GetTextFromStringFontColourFontStyle("Unlock the best of spending & travel rewards at zero charges ", "#313234", commontypes.FontStyle_SUBTITLE_S)
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION, &salaryProgramScreenOptionsV2.SalaryProgramLandingScreenRedirectionScreenOptions{
			EntryPoint: feSalaryPb.SalaryProgramLandingRedirectionScreenEntryPoint_TIER_SALARY_PLAN_SCREEN,
		})
	default:
		pageBgColor = widgetPb.GetLinearGradientBackgroundColour(0, []*widgetPb.ColorStop{{Color: "#FF18191B"}, {Color: "#18191B"}, {Color: "#18191B"}, {Color: "#FF18191B"}})
		planInfo = commontypes.GetTextFromStringFontColourFontStyle("Step into a realm of tailored rewards and seamless rewards", "#F6F9FD", commontypes.FontStyle_SUBTITLE_S)
		animationTheme = tiering2.LoaderScreenOptions_DARK
	}

	loaderScreenMetaData := &loaderSceenMeta{
		CurrTier:         planName,
		RedirectedScreen: "add_funds",
	}
	loaderScreenMetaDataMarshalled, marshalErr := json.Marshal(loaderScreenMetaData)
	if marshalErr != nil {
		logger.Error(ctx, "Error while marshalling the plan meta", zap.Error(marshalErr), zap.String("plan name", planName))
	}

	screenOptionsDl := s.getPaymentOptionsV2Deeplink(ctx, planName, amount, actorId, isPaymentOptionsDeeplinkEnabled)

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIER_LOADER_SCREEN,
		ScreenOptionsV2: getAnyWithoutError(&tiering2.LoaderScreenOptions{
			MetaData:            string(loaderScreenMetaDataMarshalled),
			PageBackgroundColor: pageBgColor,
			PlanImage:           planImage,
			PlanInfo:            planInfo,
			ScreenDelaySec:      2,
			Deeplink:            screenOptionsDl,
			AnimationTheme:      animationTheme,
		}),
	}
}

func (s *Service) getPaymentOptionsV2Deeplink(ctx context.Context, planName string, amount *money.Money, actorId string, isPaymentOptionsDeeplinkEnabled bool) *deeplinkPb.Deeplink {
	switch {
	case isPaymentOptionsDeeplinkEnabled && amount != nil && amount.GetUnits() != 0:
		dl, err := helper.GetDeeplinkForPaymentOptions(beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName]), amount, actorId)
		if err != nil {
			logger.Error(ctx, "Error while creating payment options deeplink", zap.Error(err), zap.String("plan name", planName))
			dl = helper.GetDeeplinkForAddFunds(beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName]))
		}
		return dl
	default:
		return helper.GetDeeplinkForAddFunds(beTieringExtPb.Tier(beTieringExtPb.Tier_value[planName]))
	}
}

// getComparePlansDeeplink returns the appropriate compare plans URL based on the user's base tier
func (s *Service) getComparePlansDeeplink(baseTier beTieringExtPb.Tier) *deeplinkPb.Deeplink {
	// Standard plan uses a different URL
	if baseTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEB_PAGE,
			ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
				WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
					WebpageTitle: "Compare Plans",
					WebpageUrl:   "https://storifyme.com/stories/g-prateek-gauba-22176/239260/preview",
				},
			},
		}
	}

	// Plus, Infinite, and Prime plans use the same URL
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEB_PAGE,
		ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
				WebpageTitle: "Compare Plans",
				WebpageUrl:   "https://storifyme.com/stories/g-prateek-gauba-22176/239259/preview",
			},
		},
	}
}

func (s *Service) getDeeplinkForManualUpgradeWithScreenOptionsV2() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIER_MANUAL_UPGRADE,
		ScreenOptionsV2: getAnyWithoutError(&deeplinkPb.TierManualUpgradeOptions{
			ErrorDetail: s.getErrorDetailForManualUpgrade(cta.ManualUpgradeErrLeftSubTitleText),
			Cta: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_RETRY,
				Text:         ManualUpgradeRetryCtaText,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
			NoOfRetries:         s.gconf.Tiering().MaxNumberOfManualUpgradeRetries(),
			MaxRetryErrorDetail: s.getErrorDetailForManualUpgrade(cta.ManaulUpgradeMaxRetryLeftSubTitleText),
			MaxRetryCta: &deeplinkPb.Cta{
				Text:         ManualUpgradeMaxRetryCtaText,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
			},
		}),
	}
}

func (s *Service) getErrorDetailForManualUpgrade(leftSubtitleText string) *deeplinkPb.TierPlanBottomInfo {
	return &deeplinkPb.TierPlanBottomInfo{
		LeftTitle: commontypes.GetTextFromStringFontColourFontStyle(
			ManualUpgradeErrLeftTitleText,
			ManualUpgradeErrLeftTitleTextFontColor,
			commontypes.FontStyle_SUBTITLE_S,
		),
		LeftSubTitle: commontypes.GetTextFromStringFontColourFontStyle(
			leftSubtitleText,
			ManualUpgradeErrLeftSubTitleTextFontColor,
			commontypes.FontStyle_BODY_XS,
		),
		BackgroundColour: ManualUpgradeErrBgColor,
		RightIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  ManualUpgradeErrRightIconUrl,
			Width:     int32(ManualUpgradeErrRightIconWidth),
			Height:    int32(ManualUpgradeErrRightIconHeight),
		},
	}
}

func (s *Service) getTierMovementFeedbackEngineInfo(ctx context.Context, actorId string, identifierType typesPb.FeedbackFlowIdentifierType) (*headerPb.FeedbackEngineInfo, error) {
	var feedbackEngineInfoRes *headerPb.FeedbackEngineInfo
	if s.gconf.Tiering().IsTierAllPlansDropOffFeedbackFlowEnabled() {
		feedbackEngineInfo, feedbackFlowErr := s.tierMovementFeedbackFlowSvcClient.GetFeedbackEngineInfo(ctx, actorId, identifierType)
		if feedbackFlowErr != nil {
			return nil, pkgerrors.Wrap(feedbackFlowErr, "error fetching feedbackEngineInfo")
		}
		feedbackEngineInfoRes = feedbackEngineInfo
	} else {
		logger.Debug(ctx, "IsTierAllPlansDropOffFeedbackFlowEnabled is disabled", zap.Any(logger.ACTOR_ID_V2, actorId))
	}
	return feedbackEngineInfoRes, nil
}

// GetDeeplink will be used to get deeplink by screen
// it will be used as a fallback for deeplink when screen options for the corresponding screen is not present
//
//nolint:gocritic
func (s *Service) GetDeeplink(ctx context.Context, getDeeplinkRequest *feTieringPb.GetDeeplinkRequest) (*feTieringPb.GetDeeplinkResponse, error) {
	actorId := getDeeplinkRequest.GetReq().GetAuth().GetActorId()
	appPlatform := getDeeplinkRequest.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := getDeeplinkRequest.GetReq().GetAppVersionCode()

	var finalDeeplink *deeplinkPb.Deeplink
	switch getDeeplinkRequest.GetScreen() {
	case deeplinkPb.Screen_TIER_UPGRADE_SUCCESS_SCREEN:
		// TODO (sainath) : replace with actual tier of user
		var deeplinkErr error
		finalDeeplink, deeplinkErr = s.deeplinkManager.GetUpgradeSuccessDeeplink(ctx, beTieringExtPb.Tier_TIER_FI_PLUS, actorId, appPlatform, appVersion)
		if deeplinkErr != nil {
			logger.Error(ctx, "error fetching upgrade success deeplink", zap.Error(deeplinkErr),
				zap.Any(logger.ACTOR_ID_V2, actorId))
			return &feTieringPb.GetDeeplinkResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpcPb.StatusInternalWithDebugMsg("error fetching upgrade success deeplink"),
				},
			}, nil
		}
	}
	return &feTieringPb.GetDeeplinkResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Deeplink: finalDeeplink,
	}, nil
}

// GetTieringLaunchInfo returns get tiering launch parameters when the app starts
// Returns deeplink to go to next available screen
// In case tiering is not enabled for a user yet flag will be set to false and rpc status code will be OK
// INTERNAL in case of any server error
// nolint:funlen
// current logic to show animation:
//  1. If user is in PLUS tier
//  2. If animation not shown already
//  3. If conditions to show prime tier are met
func (s *Service) GetTieringLaunchInfo(ctx context.Context, req *feTieringPb.GetTieringLaunchInfoRequest) (*feTieringPb.GetTieringLaunchInfoResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	appVersion := req.GetReq().GetAppVersionCode()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	tieringResp, showComponentResp, gatherDataErr := s.gatherDataForTierLaunchInfo(ctx, actorId)
	if gatherDataErr != nil {
		logger.Error(ctx, "error gathering data for tiering launch info", zap.Error(gatherDataErr),
			zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTieringLaunchInfoResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error gathering data for tiering launch info"),
			},
		}, nil
	}
	tierIntroductionDeeplink, tierIntroductionDeeplinkErr := s.deeplinkManager.GetTierLaunchInfoDeeplink(ctx, beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1, actorId, appPlatform, appVersion)
	if tierIntroductionDeeplinkErr != nil {
		logger.Error(ctx, "error getting tier introduction deeplink", zap.Error(tierIntroductionDeeplinkErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTieringLaunchInfoResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting tier introduction deeplink"),
			},
		}, nil

	}
	toPitchAaSalary := helper.ShouldPitchAaSalaryTier(ctx, s.gconf, tieringResp.GetCurrentTier())

	return &feTieringPb.GetTieringLaunchInfoResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		IsTieringEnabled: commontypes.BooleanEnum_TRUE,
		IsLaunchAnimationEnabled: helper.ConvertBoolToBooleanEnum(s.isLaunchAnimationEnabled(appVersion, appPlatform) &&
			showComponentResp.GetShowComponent() &&
			tieringResp.GetCurrentTier() == beTieringExtPb.Tier_TIER_FI_PLUS &&
			toPitchAaSalary),
		LaunchAnimationInactivitySeconds: s.gconf.Tiering().TierIntroduction().LaunchAnimationInactivitySeconds(),
		TierIntroductionDeeplink:         tierIntroductionDeeplink,
		Identifier:                       beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1.String(),
	}, nil
}

func (s *Service) RecordComponentShownToActor(ctx context.Context, req *feTieringPb.RecordComponentShownToActorRequest) (*feTieringPb.RecordComponentShownToActorResponse, error) {
	actorId, componentName := req.GetReq().GetAuth().GetActorId(), req.GetComponentName()
	if actorId == "" || req.GetComponentName() == feTieringEnumPb.DisplayComponent_DISPLAY_COMPONENT_UNSPECIFIED {
		logger.Error(ctx, "received empty actor id or component name in request", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any("componentName", componentName))
		return &feTieringPb.RecordComponentShownToActorResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInvalidArgument(),
			},
		}, nil
	}

	beComponent, getErr := getBeComponentForFeComponent(componentName)
	if getErr != nil {
		logger.Error(ctx, "error getting be component for fe component", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any("componentName", componentName))
		return &feTieringPb.RecordComponentShownToActorResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}
	upgradeResp, err := s.beTieringClient.RecordComponentShownToActor(ctx, &beTieringPb.RecordComponentShownToActorRequest{
		ActorId:   actorId,
		Component: beComponent,
	})
	if upgradeErr := epifigrpc.RPCError(upgradeResp, err); upgradeErr != nil {
		logger.Error(ctx, "error recording component for user", zap.Error(upgradeErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any("componentName", componentName))
		return &feTieringPb.RecordComponentShownToActorResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}

	return &feTieringPb.RecordComponentShownToActorResponse{
		RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
	}, nil
}

func (s *Service) GetTierEarnedBenefits(ctx context.Context, req *feTieringPb.GetTierEarnedBenefitsRequest) (*feTieringPb.GetTierEarnedBenefitsResponse, error) {
	forceUpgradeConfig := s.gconf.RewardsFrontendMeta().OffersCatalogPageConfig().ForceUpgradeFeatureConfig()
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, forceUpgradeConfig) {
		return &feTieringPb.GetTierEarnedBenefitsResponse{RespHeader: &headerPb.ResponseHeader{
			Status:    rpc.StatusFailedPreconditionWithDebugMsg("user needs to upgrade app"),
			ErrorView: GetForceUpgradeErrorView(ctx),
		}}, nil
	}

	actorId := req.GetReq().GetAuth().GetActorId()

	componentsToLoad := earnedBenefits.GetComponentsToLoadForEarnedBenefitsScreen(req.GetBenefitType())
	earnedBenefitsData, gatherDataErr := s.earnedBenefitsDataCollector.GatherData(ctx, actorId, componentsToLoad, req.GetReq())
	if gatherDataErr != nil {
		status := rpcPb.StatusInternal()
		if errors.Is(gatherDataErr, feTieringErrors.ErrEarnedBenefitsScreenNotApplicable) {
			status = rpcPb.NewStatusWithoutDebug(
				uint32(feTieringPb.GetTierEarnedBenefitsResponse_SCREEN_UNAVAILABLE_FOR_TIER.Number()),
				feTieringPb.GetTierEarnedBenefitsResponse_SCREEN_UNAVAILABLE_FOR_TIER.String(),
			)
		}
		logger.Error(ctx, "failed to gather data", zap.Error(gatherDataErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTierEarnedBenefitsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status:    status,
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	resp, gatherCompErr := s.gatherEarnedBenefitsComponents(ctx, earnedBenefitsData, componentsToLoad)
	if gatherCompErr != nil {
		logger.Error(ctx, "failed to gather earned benefits components", zap.Error(gatherCompErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTierEarnedBenefitsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	resp.RespHeader = &headerPb.ResponseHeader{Status: rpcPb.StatusOk()}
	if len(earnedBenefitsData.GetFetchFailedMap()) > 0 {
		resp.GetRespHeader().Status = &rpcPb.Status{
			Code:         uint32(feTieringPb.GetTierEarnedBenefitsResponse_FETCH_FAILED.Number()),
			ShortMessage: feTieringPb.GetTierEarnedBenefitsResponse_FETCH_FAILED.String(),
		}
	}

	// integrating drop off feedback engine on TIERING_EARNED_BENEFIT_SCREEN for analyzing user feedback
	// survey would be triggered when dropping off ( going back ) from TIERING_EARNED_BENEFIT_SCREEN and survey details can be configured on jarvis
	resp.RespHeader.FeedbackEngineInfo = &headerPb.FeedbackEngineInfo{
		FlowIdDetails: &headerPb.FlowIdentifierDetails{
			FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
			FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIERING_EARNED_BENEFIT_SCREEN.String(),
		},
	}
	return resp, nil
}

// nolint: funlen
func (s *Service) gatherEarnedBenefitsComponents(ctx context.Context, data *earnedBenefits.EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) (*feTieringPb.GetTierEarnedBenefitsResponse, error) {
	resp := &feTieringPb.GetTierEarnedBenefitsResponse{}

	var screenOptionsErr error
	resp.ScreenOptions, screenOptionsErr = earnedBenefits.GetEarnedBenefitsScreenOption(data.GetTierToLoad())
	if screenOptionsErr != nil {
		return nil, fmt.Errorf("failed to get earned benefits screen option, %w", screenOptionsErr)
	}

	resp.TotalBenefitEarned = uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(earnedBenefits.TotalBenefitsEarnedTitle, white, commontypes.FontStyle_OVERLINE_S_CAPS))

	var totalBenefitsValueErr error
	resp.TotalBenefitEarnedValue, totalBenefitsValueErr = s.earnedBenefitsComponentBuilder.LifetimeEarnedBenefitValue(data)
	if totalBenefitsValueErr != nil {
		return nil, fmt.Errorf("failed to get lifetime earned benefit value component, %w", totalBenefitsValueErr)
	}

	warningView, warningViewErr := s.earnedBenefitsComponentBuilder.WarningView(data)
	if warningViewErr != nil {
		return nil, fmt.Errorf("failed to get warning view, %w", warningViewErr)
	}
	if warningView != nil {
		resp.BenefitOptions = append(resp.GetBenefitOptions(), &feTieringPb.BenefitsOptions{
			Priority: 100,
			Option:   &feTieringPb.BenefitsOptions_WarningView{WarningView: warningView},
		})
	}

	var monthlyBenefitsErr error
	resp.MonthlyBenefitView, monthlyBenefitsErr = s.earnedBenefitsComponentBuilder.MonthlyBenefits(data)
	if monthlyBenefitsErr != nil {
		return nil, fmt.Errorf("failed to get monthly benefits view, %w", monthlyBenefitsErr)
	}

	monthlyBenefitView, monthlyBenefitsErr := s.earnedBenefitsComponentBuilder.MonthlyBenefitsV2(data, componentsToLoad)
	if monthlyBenefitsErr != nil {
		return nil, fmt.Errorf("failed to get monthly benefits view, %w", monthlyBenefitsErr)
	}
	resp.BenefitOptions = append(resp.GetBenefitOptions(), monthlyBenefitView)

	transferSalaryView, transferSalaryErr := s.earnedBenefitsComponentBuilder.TransferSalaryView(data)
	if transferSalaryErr != nil {
		return nil, fmt.Errorf("failed to get transfer salary view, %w", transferSalaryErr)
	}
	resp.BenefitOptions = append(resp.GetBenefitOptions(), transferSalaryView)

	higherRewards, higherRewardsErr := s.earnedBenefitsComponentBuilder.HigherRewards(ctx, data, componentsToLoad)
	if higherRewardsErr != nil {
		return nil, fmt.Errorf("failed to get higher rewards view, %w", higherRewardsErr)
	}
	if higherRewards != nil {
		resp.BenefitOptions = append(resp.BenefitOptions, higherRewards)
	}

	debitCardBeneifts, debitCardBeneiftsErr := s.earnedBenefitsComponentBuilder.DebitCardBenefits(ctx, data, componentsToLoad)
	if debitCardBeneiftsErr != nil {
		return nil, fmt.Errorf("failed to get debit card benefits component, %w", debitCardBeneiftsErr)
	}
	if debitCardBeneifts != nil {
		resp.BenefitOptions = append(resp.GetBenefitOptions(), debitCardBeneifts)
	}

	healthInsuranceView, healthInsuranceErr := s.earnedBenefitsComponentBuilder.HealthInsuranceView(data)
	if healthInsuranceErr != nil {
		return nil, fmt.Errorf("failed to get health insurance view, %w", healthInsuranceErr)
	}
	resp.BenefitOptions = append(resp.GetBenefitOptions(), healthInsuranceView)

	currentEmployerView, currentEmployerErr := s.earnedBenefitsComponentBuilder.CurrentEmployerView(data)
	if currentEmployerErr != nil {
		return nil, fmt.Errorf("failed to get current employer view, %w", currentEmployerErr)
	}
	resp.BenefitOptions = append(resp.GetBenefitOptions(), currentEmployerView)

	rewardWarningView, rewardWarningErr := s.earnedBenefitsComponentBuilder.RewardWarningView(data)
	if rewardWarningErr != nil {
		return nil, fmt.Errorf("failed to get reward warning view, %w", rewardWarningErr)
	}
	resp.BenefitOptions = append(resp.GetBenefitOptions(), rewardWarningView)

	moneyPlantView, moneyPlantErr := s.earnedBenefitsComponentBuilder.MonthlyRewardWithMoneyPlant(data)
	if moneyPlantErr != nil {
		return nil, fmt.Errorf("failed to get money plant view, %w", moneyPlantErr)
	}
	resp.BenefitOptions = append(resp.GetBenefitOptions(), moneyPlantView...)

	otherBenefits, otherBenefitsErr := s.earnedBenefitsComponentBuilder.OtherBenefits(data, componentsToLoad)
	if otherBenefitsErr != nil {
		return nil, fmt.Errorf("failed to get other benefits component, %w", otherBenefitsErr)
	}
	if otherBenefits != nil {
		resp.BenefitOptions = append(resp.GetBenefitOptions(), otherBenefits)
	}

	morePlans, morePlansErr := s.earnedBenefitsComponentBuilder.MorePlansInfo(data)
	if morePlansErr != nil {
		return nil, fmt.Errorf("failed to get more plans info view, %w", morePlansErr)
	}
	if morePlans != nil {
		resp.BenefitOptions = append(resp.BenefitOptions, morePlans)
	}

	// sort based on priority
	sort.SliceStable(resp.GetBenefitOptions(), func(i, j int) bool {
		return resp.GetBenefitOptions()[i].GetPriority() > resp.GetBenefitOptions()[j].GetPriority()
	})

	var popupErr error
	resp.CelebrationPopup, resp.CelebrationPopupCooloffDuration, resp.PopupCacheKey, popupErr = s.earnedBenefitsComponentBuilder.RewardRockstarPopup(data)
	if popupErr != nil {
		return nil, fmt.Errorf("failed to get reward rockstar popup, %w", popupErr)
	}

	return resp, nil
}

func (s *Service) GetEarnedBenefitsHistory(ctx context.Context, req *feTieringPb.GetEarnedBenefitsHistoryRequest) (*feTieringPb.GetEarnedBenefitsHistoryResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	token, tokenErr := s.getEarnedBenefitsHistoryToken(req.GetToken())
	if tokenErr != nil {
		logger.Error(ctx, "failed to get token from request", zap.Error(tokenErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetEarnedBenefitsHistoryResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	var monthsToFetch []*tieringData.MonthYear
	for _, month := range token.GetMonthsForToken() {
		if month.GetStartOfMonth().After(time.Now().Add(-s.gconf.Tiering().EarnedBenefitsHistoryDepth())) {
			monthsToFetch = append(monthsToFetch, month)
		}
	}

	earnedBenefitsData, gatherDataErr := s.earnedBenefitsDataCollector.GatherDataForHistories(ctx, actorId, monthsToFetch)
	if gatherDataErr != nil {
		logger.Error(ctx, "failed to gather data", zap.Error(gatherDataErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetEarnedBenefitsHistoryResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	resp, gatherCompErr := s.gatherEarnedBenefitsComponentsHistory(ctx, earnedBenefitsData)
	if gatherCompErr != nil {
		logger.Error(ctx, "failed to gather earned benefits components", zap.Error(gatherCompErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetEarnedBenefitsHistoryResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	var tokenErr2 error
	resp.BeforeToken, resp.AfterToken, tokenErr2 = s.getEarnedBenefitsHistoryBeforeAfterToken(token)
	if tokenErr2 != nil {
		logger.Error(ctx, "failed to get before and after token", zap.Error(tokenErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetEarnedBenefitsHistoryResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	if resp.AfterToken != "" {
		resp.ViewMoreCta = earnedBenefits.GetItcCtaForEarnedBenefits("View More", nil)
	}

	resp.RespHeader = &headerPb.ResponseHeader{Status: rpcPb.StatusOk()}
	return resp, nil
}

// buildHeaderBanner constructs a header banner component for the Earned Benefits screen.
// It takes the banner text, icon, color, paddings, and deeplink as arguments and returns a fully configured IconTextComponent.
// This helps avoid code duplication when building similar banners for pre- and post-migration states.
func buildHeaderBanner(
	text string,
	icon string,
	colorNight string,
	leftPadding, rightPadding, topPadding, bottomPadding int32,
	deeplink *deeplinkPb.Deeplink,
) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, colorNight, commontypes.FontStyle_SUBTITLE_S)).
		WithLeftVisualElementUrlHeightAndWidth(icon, 24, 24).
		WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
			BackgroundColour: widgetPb.GetLinearGradientBackgroundColour(90, []*widgetPb.ColorStop{
				{Color: "#FCF1E4", StopPercentage: 0},
				{Color: "#FDDCA8", StopPercentage: 100},
			}),
			LeftPadding:   leftPadding,
			RightPadding:  rightPadding,
			TopPadding:    topPadding,
			BottomPadding: bottomPadding,
		}).
		WithDeeplink(deeplink)
}

func (s *Service) gatherEarnedBenefitsComponentsHistory(ctx context.Context, data *earnedBenefits.EarnedBenefitHistoriesData) (*feTieringPb.GetEarnedBenefitsHistoryResponse, error) {
	resp := &feTieringPb.GetEarnedBenefitsHistoryResponse{}

	var getNavBarErr error
	resp.PageTitle, getNavBarErr = earnedBenefits.GetNavBarTitleForHistory(data.GetEarnedBenefitsData().GetTierToLoad())
	if getNavBarErr != nil {
		return nil, fmt.Errorf("failed to get nav bar title of earned benefit history, %w", getNavBarErr)
	}

	var headerViewError error
	resp.HeaderView, headerViewError = s.earnedBenefitsComponentBuilder.GetHeaderForEarnedBenefitsHistory(data)
	if headerViewError != nil {
		return nil, fmt.Errorf("failed to get lifetime earned benefit header section, %w", headerViewError)
	}

	var monthlyBenefitErr error
	resp.MonthlyRewardEarnedViews, monthlyBenefitErr = s.earnedBenefitsComponentBuilder.GetMonthlyBenefitsList(data)
	if monthlyBenefitErr != nil {
		return nil, fmt.Errorf("failed to get lifetime earned benefit History Monthly reward section, %w", monthlyBenefitErr)
	}

	// Set the header banner for post-migration state, if enabled.
	if data.GetEarnedBenefitsData().GetIsFiCoinsPostMigrationEnabled() {
		resp.HeaderBanner = buildHeaderBanner(
			constants.FiPointsUpgradedText, // Banner text for post-migration
			constants.FiPointsUpgradeIcon,  // Banner icon
			colorPkg.ColorNight,            // Text color
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().LeftPadding(),
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().RightPadding(),
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().TopPadding(),
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().BottomPadding(),
			rewards.GetFiCoinsFiPointsKnowMoreButtonDeeplink(ctx, data.GetEarnedBenefitsData().GetActorId(), s.fireFlyAccClient, s.fireFlyAccClientV2, s.beTieringClient, false), // Deeplink
		)
	}

	// Set the header banner for pre-migration state, if enabled.
	if data.GetEarnedBenefitsData().GetIsFiCoinsPreMigrationEnabled() {
		resp.HeaderBanner = buildHeaderBanner(
			constants.FiPointsUpgradeText, // Banner text for pre-migration
			constants.FiPointsUpgradeIcon, // Banner icon
			colorPkg.ColorNight,           // Text color
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().LeftPadding(),
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().RightPadding(),
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().TopPadding(),
			s.gconf.TieringBenefitsEarnedScreenHeaderBanner().BottomPadding(),
			rewards.GetFiCoinsFiPointsKnowMoreButtonDeeplink(ctx, data.GetEarnedBenefitsData().GetActorId(), s.fireFlyAccClient, s.fireFlyAccClientV2, s.beTieringClient, true), // Deeplink
		)
	}
	return resp, nil

}

func (s *Service) GetTierFlowScreen(ctx context.Context, req *feTieringPb.GetTierFlowScreenRequest) (*feTieringPb.GetTierFlowScreenResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	metaDataReq := req.GetMetaData()

	metadata := &tieringTypesPb.TieringScreenMetaData{}
	unmarshalErr := protojson.Unmarshal([]byte(metaDataReq), metadata)
	if unmarshalErr != nil {
		logger.Error(ctx, "failed to unmarshal metadata", zap.Error(unmarshalErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTierFlowScreenResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}

	switch metadataScoped := metadata.GetMetadata().(type) {
	case *tieringTypesPb.TieringScreenMetaData_NotificationLandingScreenMetadata:
		dl, err := s.constructNotificationLandingScreen(ctx, metadataScoped.NotificationLandingScreenMetadata, metaDataReq)
		if err != nil {
			logger.Error(ctx, "failed to construct notification landing screen", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
			return &feTieringPb.GetTierFlowScreenResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			}, nil
		}

		return &feTieringPb.GetTierFlowScreenResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
			Deeplink:   dl,
		}, nil
	case *tieringTypesPb.TieringScreenMetaData_SuccessV2Metadata:
		tier := metadataScoped.SuccessV2Metadata.GetTier()
		dl, err := s.deeplinkManager.GetUpgradeSuccessDeeplinkV2(beTieringExtPb.Tier(beTieringExtPb.Tier_value[tier]))
		if err != nil {
			logger.Error(ctx, "failed to construct success v2 screen", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
			return &feTieringPb.GetTierFlowScreenResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			}, nil
		}

		return &feTieringPb.GetTierFlowScreenResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
			Deeplink:   dl,
		}, nil
	default:
		logger.Error(ctx, "unknown metadata type", zap.Any("metadata", metadata.GetMetadata()), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetTierFlowScreenResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
			Deeplink:   tiering2.AllPlansDeeplink(beTieringExtPb.Tier_TIER_UNSPECIFIED, true),
		}, nil
	}
}

func (s *Service) GetDetailedBenefitsBottomSheet(ctx context.Context, req *feTieringPb.GetDetailedBenefitsBottomSheetRequest) (*feTieringPb.GetDetailedBenefitsBottomSheetResponse, error) {

	actorId := req.GetReq().GetAuth().GetActorId()
	if req.GetMetaData() == "{}" {
		logger.Error(ctx, "metadata is empty", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetDetailedBenefitsBottomSheetResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInvalidArgument(),
			},
		}, nil
	}

	tierAllPlansV2Data, gatherDataErr := s.gatherDataForTierAllPlansV2(ctx, actorId)
	if gatherDataErr != nil {
		logger.Error(ctx, "failed to gather data for tier all plans v2", zap.Error(gatherDataErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetDetailedBenefitsBottomSheetResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}

	var request tiering.TieringScreenMetaData
	if err := protojson.Unmarshal([]byte(req.GetMetaData()), &request); err != nil {
		logger.Error(ctx, "failed to unmarshal metadata", zap.Error(err))
		return &feTieringPb.GetDetailedBenefitsBottomSheetResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to unmarshal metadata"),
			},
		}, nil
	}

	// // To populate tier plan meta map for analytics events
	balanceBucket := tieringPkg.DetermineBalanceBucket(tierAllPlansV2Data.currBalance.GetUnits())

	planData := s.getPlanData(tierAllPlansV2Data.isTierPlansV2Enabled)

	switch request.GetMetadata().(type) {
	case *tiering.TieringScreenMetaData_DetailedBenefitsScreenMetadata:
		tier := request.GetDetailedBenefitsScreenMetadata().GetTier()
		return s.getBenefitsBottomSheet(ctx, planData[tier].BenefitsBottomSheet, detailedBenefitsBottomSheet, beTieringExtPb.Tier(beTieringExtPb.Tier_value[tier]), tierAllPlansV2Data.tieringEssential.TieringPitchResp, tierAllPlansV2Data.evaluatedTierForActor, tierAllPlansV2Data.kycLevel, balanceBucket, actorId, tierAllPlansV2Data.currBalance)
	case *tiering.TieringScreenMetaData_CashbackDetailsBottomSheet:
		tier := request.GetCashbackDetailsBottomSheet().GetTier()
		return s.getBenefitsBottomSheet(ctx, planData[tier].BenefitsBottomSheet, cashbackDetailsBottomSheet, beTieringExtPb.Tier(beTieringExtPb.Tier_value[tier]), tierAllPlansV2Data.tieringEssential.TieringPitchResp, tierAllPlansV2Data.evaluatedTierForActor, tierAllPlansV2Data.kycLevel, balanceBucket, actorId, tierAllPlansV2Data.currBalance)
	case *tiering.TieringScreenMetaData_WaysToUseFiPointsBottomSheet:
		tier := request.GetWaysToUseFiPointsBottomSheet().GetTier()
		return s.getBenefitsBottomSheet(ctx, planData[tier].BenefitsBottomSheet, waysToUseFiPointsBottomSheet, beTieringExtPb.Tier(beTieringExtPb.Tier_value[tier]), tierAllPlansV2Data.tieringEssential.TieringPitchResp, tierAllPlansV2Data.evaluatedTierForActor, tierAllPlansV2Data.kycLevel, balanceBucket, actorId, tierAllPlansV2Data.currBalance)
	default:
		logger.Error(ctx, "unknown metadata type", zap.Any("metadata", request.GetMetadata()), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feTieringPb.GetDetailedBenefitsBottomSheetResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("unknown metadata type"),
			},
		}, nil
	}
}

func (s *Service) getBenefitsBottomSheet(ctx context.Context, bottomSheetData *config.BenefitsBottomSheet, bottomSheetType string, tier beTieringExtPb.Tier, tieringPitchV2Resp *beTieringPb.GetTieringPitchV2Response, evaluateTierResp *beTieringPb.EvaluateTierForActorResponse, kycLevel kycPb.KYCLevel, balanceBucket string, actorId string, currBalance *money.Money) (*feTieringPb.GetDetailedBenefitsBottomSheetResponse, error) {
	// Get the appropriate CTA based on bottom sheet type
	bottomSheetCta := s.getBottomSheetCTAForType(ctx, bottomSheetData, bottomSheetType, tier, tieringPitchV2Resp, evaluateTierResp, kycLevel, actorId, currBalance)

	return &feTieringPb.GetDetailedBenefitsBottomSheetResponse{
		RespHeader:      &headerPb.ResponseHeader{Status: rpc.StatusOk()},
		Section:         s.getBenefitsBottomSheetSection(tier, bottomSheetData, bottomSheetType),
		Cta:             bottomSheetCta,
		TierPlanMetaMap: s.buildTierPlanMetaMap(tieringPitchV2Resp, tier, bottomSheetCta, balanceBucket),
	}, nil
}

// getBottomSheetCTAForType returns the appropriate CTA based on bottom sheet type
func (s *Service) getBottomSheetCTAForType(ctx context.Context, bottomSheetData *config.BenefitsBottomSheet, bottomSheetType string, tier beTieringExtPb.Tier, tieringPitchV2Resp *beTieringPb.GetTieringPitchV2Response, evaluateTierResp *beTieringPb.EvaluateTierForActorResponse, kycLevel kycPb.KYCLevel, actorId string, currBalance *money.Money) *deeplinkPb.Cta {

	var cta *deeplinkCfg.CTA

	switch bottomSheetType {
	case detailedBenefitsBottomSheet:
		cta = bottomSheetData.DetailedBenefitsBottomSheet.Cta
	case waysToUseFiPointsBottomSheet:
		cta = bottomSheetData.WaysToUseFiPointsBottomSheet.Cta
	case cashbackDetailsBottomSheet:
		cta = bottomSheetData.CashbackDetailsBottomSheet.Cta
	default:
		// Default to detailed benefits bottom sheet CTA
		cta = bottomSheetData.DetailedBenefitsBottomSheet.Cta
	}

	return s.getBottomSheetCTA(ctx, cta, tier, tieringPitchV2Resp, evaluateTierResp, kycLevel, actorId, currBalance)
}

// getPlanData returns the appropriate plan data based on feature flag
func (s *Service) getPlanData(isTierPlansV2Enabled bool) map[string]*config.TierAllPlansV2TierPlans {
	// Return TierPlansV2 if enabled, otherwise TierPlans
	if isTierPlansV2Enabled {
		return s.gconf.Tiering().TierAllPlansV2ConfigParams().TierPlansV2()
	}

	return s.gconf.Tiering().TierAllPlansV2ConfigParams().TierPlans()
}

// buildTierPlanMetaMap creates the tier plan metadata map
func (s *Service) buildTierPlanMetaMap(tieringPitchV2Resp *beTieringPb.GetTieringPitchV2Response, tier beTieringExtPb.Tier, bottomSheetCta *deeplinkPb.Cta, balanceBucket string) map[string]string {
	ctaText := ""
	if bottomSheetCta != nil {
		ctaText = bottomSheetCta.GetText()
	}

	return map[string]string{
		"CurrentTier":   tieringPitchV2Resp.GetCurrentTier().String(),
		"TierName":      tier.String(),
		"CtaText":       ctaText,
		"BalanceBucket": balanceBucket,
	}
}

func (s *Service) getBenefitsBottomSheetSection(tier beTieringExtPb.Tier, bottomSheetData *config.BenefitsBottomSheet, bottomSheetType string) *sections.Section {
	section := &sections.Section{
		Content: &sections.Section_VerticalListSection{VerticalListSection: s.getBenefitsVerticalListSection(tier, bottomSheetData, bottomSheetType)},
	}
	return section
}

func (s *Service) getBenefitsVerticalListSection(tier beTieringExtPb.Tier, bottomSheetData *config.BenefitsBottomSheet, bottomSheetType string) *sections.VerticalListSection {

	// Add nil check for bottomSheetData
	if bottomSheetData == nil {
		return &sections.VerticalListSection{
			IsScrollable: false,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_BgColor{
						BgColor: widgetPb.GetBlockBackgroundColour("#FFFFFF"),
					},
				},
			},
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
			VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
		}
	}

	var bgColor string
	switch bottomSheetType {
	case detailedBenefitsBottomSheet:
		if bottomSheetData.DetailedBenefitsBottomSheet != nil && bottomSheetData.DetailedBenefitsBottomSheet.BgColor != nil {
			bgColor = bottomSheetData.DetailedBenefitsBottomSheet.BgColor.BgColorBlockColor
		} else {
			bgColor = defaultBgColor
		}
	case waysToUseFiPointsBottomSheet:
		if bottomSheetData.WaysToUseFiPointsBottomSheet != nil && bottomSheetData.WaysToUseFiPointsBottomSheet.BgColor != nil {
			bgColor = bottomSheetData.WaysToUseFiPointsBottomSheet.BgColor.BgColorBlockColor
		} else {
			bgColor = defaultBgColor
		}
	case cashbackDetailsBottomSheet:
		if bottomSheetData.CashbackDetailsBottomSheet != nil && bottomSheetData.CashbackDetailsBottomSheet.BgColor != nil {
			bgColor = bottomSheetData.CashbackDetailsBottomSheet.BgColor.BgColorBlockColor
		} else {
			bgColor = defaultBgColor
		}
	default:
		if bottomSheetData.DetailedBenefitsBottomSheet != nil && bottomSheetData.DetailedBenefitsBottomSheet.BgColor != nil {
			bgColor = bottomSheetData.DetailedBenefitsBottomSheet.BgColor.BgColorBlockColor
		} else {
			bgColor = defaultBgColor
		}
	}

	section := &sections.VerticalListSection{
		IsScrollable: false,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_BgColor{
					BgColor: widgetPb.GetBlockBackgroundColour(bgColor),
				},
			},
			{
				Properties: &properties.VisualProperty_Padding{
					Padding: &properties.PaddingProperty{
						Bottom: 30,
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
		VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
	}

	switch bottomSheetType {
	case detailedBenefitsBottomSheet:
		section.Components = s.constructDetailedBenefitsBottomSheet(tier, bottomSheetData.DetailedBenefitsBottomSheet)
	case waysToUseFiPointsBottomSheet:
		section.Components = s.constructWaysToUseFiPointsBottomSheet(bottomSheetData.WaysToUseFiPointsBottomSheet)
	case cashbackDetailsBottomSheet:
		section.Components = s.constructCashbackDetailsBottomSheet(bottomSheetData.CashbackDetailsBottomSheet)
	}

	return section
}

func (s *Service) getBottomSheetCTA(ctx context.Context, cta *deeplinkCfg.CTA, feTier beTieringExtPb.Tier, tieringPitchV2Resp *beTieringPb.GetTieringPitchV2Response, evaluateTierResp *beTieringPb.EvaluateTierForActorResponse, kycLevel kycPb.KYCLevel, actorId string, currBalance *money.Money) *deeplinkPb.Cta {

	currTier := tieringPitchV2Resp.GetCurrentTier()
	// cat is not visible for current plan, Standard and Regular Tier
	if currTier.IsSalaryOrSalaryLiteTier() || currTier == feTier ||
		feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		return nil
	}

	// TODO : create a method for getting the CTA for lower tiers
	switch tieringPitchV2Resp.GetCurrentTier() {
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return nil
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		if feTier == beTieringExtPb.Tier_TIER_FI_PLUS || feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
			return nil
		}
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		if feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
			return nil
		}
	}

	if kycLevel != kycPb.KYCLevel_FULL_KYC {
		vkycDeeplinkCreation, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
			EntryPoint: vkyc.EntryPoint_ENTRY_POINT_TIER_ALL_PLANS,
		})
		return &deeplinkPb.Cta{
			Text:         fmt.Sprintf("Upgrade to full KYC"),
			Deeplink:     vkycDeeplinkCreation,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	}

	if helper.IsFeTieLowerThanOrEqualToCurrentEvaluatedTier(feTier, evaluateTierResp.GetEvaluatedTier()) {
		ctaText := "Upgrade for free"
		if cta != nil && cta.Text != "" {
			ctaText = cta.Text
		}
		return &deeplinkPb.Cta{
			Text:         ctaText,
			Deeplink:     s.getDeeplinkForManualUpgradeWithScreenOptionsV2(),
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	}

	isPaymentOptionsDeeplinkEnabled, _, err := s.abEvaluatorGeneric.Evaluate(ctx, pkgRelease.NewCommonConstraintData(typesPb.Feature_FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating release", zap.Error(err))
	}

	amount, err := helper.CalculateAmountNeededForTier(feTier, tieringPitchV2Resp.GetMovementDetailsList(), currBalance)
	if err != nil {
		logger.Error(ctx, "error in calculating amount needed for tier", zap.Error(err))
		return nil
	}

	ctaText := "Get these benefits"
	if cta != nil && cta.Text != "" {
		ctaText = cta.Text
	}
	return &deeplinkPb.Cta{
		Text:         ctaText,
		Deeplink:     s.getDeeplinkForLoaderScreenWithScreenOptionsV2(ctx, feTier.String(), amount, isPaymentOptionsDeeplinkEnabled, actorId),
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Status:       0,
	}
}

func (s *Service) constructDetailedBenefitsBottomSheet(tier beTieringExtPb.Tier, bottomSheetData *config.DetailedBenefitsBottomSheet) []*components.Component {
	if bottomSheetData == nil || bottomSheetData.Title == nil {
		return nil
	}

	var res []*components.Component
	res = append(res, &components.Component{
		Content: getAnyWithoutError(
			&sections.VerticalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringWithCustomFontStyle(bottomSheetData.Title.Content, bottomSheetData.Title.FontColor, &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "BOLD",
							FontSize:   "24",
						})),
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Bottom: 8,
								},
							},
						},
					},
				},
			}),
	})
	for _, tile := range bottomSheetData.DetailedBenefitsBottomSheetTilesOrdering {
		tileData := bottomSheetData.DetailedBenefitsBottomSheetTiles[tile]
		component := &components.Component{
			Content: s.constructDetailedBenefitsBottomSheetTile(tier, tileData, tile),
		}
		res = append(res, component)
	}
	return res
}

func (s *Service) constructDetailedBenefitsBottomSheetTile(tier beTieringExtPb.Tier, tileData *config.DetailedBenefitsBottomSheetTile, tileKey string) *anyPb.Any {

	var componentList []*components.Component

	if tileData.TopIconUrl != "" {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(tileData.TopIconUrl, 64, 64)),
		})
	}
	if tileData.Title != nil {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(tileData.Title.Content, nil), tileData.Title.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tileData.Title.FontStyle]))),
		})
	}
	if tileData.MiddleIcon != nil {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(tileData.MiddleIcon.Url, tileData.MiddleIcon.Properties.Height, tileData.MiddleIcon.Properties.Width)),
		})
	}
	if tileData.Subtitle != nil {
		subtitle := tileData.Subtitle.Content

		// Only format subtitle for tiles that need cap values (cashback tiles)
		if strings.Contains(tileKey, "percent_cashback") {
			// Use the user's actual tier for cap calculations
			tierForCaps := tier

			// Get caps and worth values
			monthlyCap := rewards.BeTierToFiCoinsMonthlyCapMap[tierForCaps]
			dailyCap := rewards.BeTierToFiCoinsDailyCapMap[tierForCaps]
			txnCap := rewards.BeTierToFiCoinsTxnCapMap[tierForCaps]
			monthlyWorth := rewards.BeTierToTieringMonthlyRewardMonthlyCashEquivalentCapMap[tierForCaps]
			dailyWorth := rewards.BeTierToTieringMonthlyRewardDailyCashEquivalentCapMap[tierForCaps]
			txnWorth := rewards.BeTierToTieringMonthlyRewardTxnCashEquivalentCapMap[tierForCaps]

			// Format subtitle if caps are valid
			if monthlyCap > 0 {
				// Values in maps are small (max 50,000), so conversion to int32 is safe
				monthly := accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(int32(monthlyCap), false, nil) //nolint:gosec
				daily := accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(int32(dailyCap), false, nil)     //nolint:gosec
				txn := accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(int32(txnCap), false, nil)         //nolint:gosec
				subtitle = fmt.Sprintf(tileData.Subtitle.Content, monthly, monthlyWorth, daily, dailyWorth, txn, txnWorth)
			}
		}
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(subtitle, nil), tileData.Subtitle.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tileData.Subtitle.FontStyle]))),
		})
	}

	verticalListSection := &sections.VerticalListSection{
		IsScrollable: false,
		Components:   componentList,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: tileData.TileWidth,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: tileData.TileHeight,
							},
						},
						BgColor: widgetPb.GetBlockBackgroundColour(tileData.BackgroundColor.BgColorBlockColor),
						Padding: &properties.PaddingProperty{
							Top:    20,
							Left:   20,
							Bottom: 20,
							Right:  20,
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  20,
							TopRightCornerRadius: 20,
							BottomLeftCorner:     20,
							BottomRightCorner:    20,
						},
						Margin: &properties.PaddingProperty{
							Left:   36,
							Top:    10,
							Right:  36,
							Bottom: 10,
						},
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
		VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
	}

	return getAnyWithoutError(verticalListSection)
}

func (s *Service) constructCashbackDetailsBottomSheet(bottomSheetData *config.CashbackDetailsBottomSheet) []*components.Component {
	if bottomSheetData == nil {
		return nil
	}

	var res []*components.Component
	res = append(res, &components.Component{
		Content: getAnyWithoutError(
			&sections.VerticalListSection{
				IsScrollable:        false,
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Bottom: 8,
								},
							},
						},
					},
				},
			}),
	})
	for _, tile := range bottomSheetData.CashbackDetailsBottomSheetTilesOrdering {
		tileData := bottomSheetData.CashbackDetailsBottomSheetTiles[tile]
		component := &components.Component{
			Content: s.constructCashbackDetailsBottomSheetTile(tileData),
		}
		res = append(res, component)
	}
	return res
}

func (s *Service) constructCashbackDetailsBottomSheetTile(tileData *config.CashbackDetailsBottomSheetTile) *anyPb.Any {

	var componentList []*components.Component

	if tileData.TopIconUrl != "" {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(tileData.TopIconUrl, 96, 136)),
		})
	}
	if tileData.Title != nil {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(tileData.Title.Content, nil), tileData.Title.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tileData.Title.FontStyle]))),
		})
	}
	if tileData.Subtitle != nil {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(tileData.Subtitle.Content, nil), tileData.Subtitle.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tileData.Subtitle.FontStyle]))),
		})
	}

	if tileData.HorizontalBorder != nil {
		componentList = append(componentList, &components.Component{
			Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(tileData.HorizontalBorder.Content, tileData.HorizontalBorder.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[tileData.Subtitle.FontStyle]))),
		})
	}

	verticalListSection := &sections.VerticalListSection{
		IsScrollable: false,
		Components:   componentList,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: tileData.TileWidth,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: tileData.TileHeight,
							},
						},
						Margin: &properties.PaddingProperty{
							Left:   36,
							Top:    8,
							Right:  36,
							Bottom: 0,
						},
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
		VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
	}

	return getAnyWithoutError(verticalListSection)
}

func (s *Service) constructWaysToUseFiPointsBottomSheet(bottomSheetData *config.WaysToUseFiPointsBottomSheet) []*components.Component {
	if bottomSheetData == nil || bottomSheetData.Title == nil {
		return nil
	}

	var res []*components.Component

	// Add title section
	res = append(res, &components.Component{
		Content: getAnyWithoutError(
			&sections.VerticalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(bottomSheetData.Title.Content, bottomSheetData.Title.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[bottomSheetData.Title.FontStyle]))),
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Bottom: 8,
								},
							},
						},
					},
				},
			}),
	})

	// Add tiles
	for _, tile := range bottomSheetData.DetailedBenefitsBottomSheetTilesOrdering {
		tileData := bottomSheetData.DetailedBenefitsBottomSheetTiles[tile]
		component := &components.Component{
			Content: s.constructDetailedBenefitsBottomSheetTile(beTieringExtPb.Tier_TIER_FI_PLUS, tileData, tile),
		}
		res = append(res, component)
	}
	return res
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}

func getBenefitsMetaAsString(ctx context.Context, currTier, benefitType string) string {
	benefitMetaData := &benefitsMeta{
		CurrTier:    currTier,
		BenefitType: benefitType,
	}
	benefitsMetaMarshalled, marshalErr := json.Marshal(benefitMetaData)
	if marshalErr != nil {
		logger.Error(ctx, "Error while marshalling the plan meta", zap.Error(marshalErr))
	}
	return string(benefitsMetaMarshalled)
}

func getEntryBannerMetaAsString(ctx context.Context, currTier, benefitType, entrypoint string) string {
	entryBannerMetaData := &entryBannerMeta{
		CurrTier:   currTier,
		BannerType: benefitType,
		EntryPoint: entrypoint,
	}
	entryBannerMetaMarshalled, marshalErr := json.Marshal(entryBannerMetaData)
	if marshalErr != nil {
		logger.Error(ctx, "Error while marshalling the plan meta", zap.Error(marshalErr))
	}
	return string(entryBannerMetaMarshalled)
}

func (s *Service) GetAMBScreenDetails(ctx context.Context, req *feTieringPb.GetAMBScreenDetailsRequest) (*feTieringPb.GetAMBScreenDetailsResponse, error) {
	// Use injected provider for getting data
	ambData, err := s.ambDataProvider.BuildAMBScreenData(ctx, req.GetReq())
	if err != nil {
		logger.Error(ctx, "failed to build AMB screen data", zap.Error(err))
		return &feTieringPb.GetAMBScreenDetailsResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	// Use injected builder for building the UI
	section := s.ambScreenBuilder.BuildScreen(ambData)

	return &feTieringPb.GetAMBScreenDetailsResponse{
		RespHeader: &headerPb.ResponseHeader{Status: rpc.StatusOk()},
		Section:    section,
		Title:      commontypes.GetTextFromStringFontColourFontStyle("Average Monthly Balance", "#313234", commontypes.FontStyle_HEADLINE_M),
	}, nil
}

func GetForceUpgradeErrorView(ctx context.Context) *errorsPb.ErrorView {
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	switch appPlatform {
	// iOS requires full-screen error view instead of bottom sheet due to compatibility issues.
	// Bottom sheet error views don't load properly on iOS while they work correctly on Android.
	// Additionally, redirections to UPDATE_APP_SCREEN are not working properly on iOS.
	// Hence, we are using platform-specific handling with full-screen error view for iOS.
	case commontypes.Platform_IOS:
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_FULL_SCREEN_V2,
			Options: &errorsPb.ErrorView_FullScreenErrorViewV2{
				FullScreenErrorViewV2: &errorsPb.FullScreenErrorViewV2{
					Image: commontypes.GetVisualElementFromUrlHeightAndWidth(earnedBenefits.ThunderCloudBig, 200, 200),
					Title: commontypes.GetTextFromStringFontColourFontStyle(
						"Update available!",
						colorPkg.ColorDarkCharcoal,
						commontypes.FontStyle_SUBTITLE_L,
					),
					Subtitle: commontypes.GetTextFromStringFontColourFontStyle(
						"You’re missing out on brand-new features. Tap now for a faster, improved app experience.",
						colorPkg.ColorDarkCharcoal,
						commontypes.FontStyle_SUBTITLE_M,
					),
					Ctas: []*deeplinkPb.Cta{{
						Type: deeplinkPb.Cta_CUSTOM,
						Text: "Update now",
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions_{
								UpdateAppScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions{
									UpdateType: deeplinkPb.Deeplink_UpdateAppScreenOptions_UPDATE_TYPE_IMMEDIATE,
								},
							},
						},
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					}},
					ArrangeCtasVertically: true,
					BackgroundColor:       colorPkg.ColorSnow,
				},
			},
		}
	// Bottom sheet error view as default handling
	default:
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					TitleText: commontypes.GetTextFromStringFontColourFontStyle(
						"Update available!",
						colorPkg.ColorSnow,
						commontypes.FontStyle_SUBTITLE_L,
					),
					SubtitleText: commontypes.GetTextFromStringFontColourFontStyle(
						"You’re missing out on brand-new features. Tap now for a faster, improved app experience.",
						colorPkg.ColorSnow,
						commontypes.FontStyle_SUBTITLE_M,
					),
					Ctas: []*errorsPb.CTA{{
						Type: errorsPb.CTA_CUSTOM,
						Text: "Update now",
						Action: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions_{
								UpdateAppScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions{
									UpdateType: deeplinkPb.Deeplink_UpdateAppScreenOptions_UPDATE_TYPE_IMMEDIATE,
								},
							},
						},
						DisplayTheme: errorsPb.CTA_PRIMARY,
					}},
				},
			},
		}
	}
}
