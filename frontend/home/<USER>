package home

import (
	"context"
	"fmt"
	"testing"
	"time"

	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/bankcust"
	bankcustMock "github.com/epifi/gamma/api/bankcust/mocks"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/kyc"
	vkycMock "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	"github.com/epifi/gamma/api/rewards"
	mocks3 "github.com/epifi/gamma/api/rewards/mocks"
	segmentPb "github.com/epifi/gamma/api/segment"
	segmentMock "github.com/epifi/gamma/api/segment/mocks"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	mocks2 "github.com/epifi/gamma/api/tiering/mocks"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/data_collector/mocks"
	feTieringHelper "github.com/epifi/gamma/frontend/tiering/helper"
	releaseMock "github.com/epifi/gamma/pkg/feature/release/mocks"
	"github.com/epifi/gamma/pkg/user"
	userMock "github.com/epifi/gamma/pkg/user/mocks"

	"github.com/golang/mock/gomock"

	"google.golang.org/protobuf/proto"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
)

type wantRes struct {
	badge       *commontypes.Image
	promptIcon  *feHomePb.Icon
	promptTitle *commontypes.Text
	wantErr     bool
}

func TestService_getNotchComponents(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	actorId := "actor-1"

	mockBcClient := bankcustMock.NewMockBankCustomerServiceClient(ctrl)
	mockVkycFeClient := vkycMock.NewMockVKYCFeClient(ctrl)
	mockUserAttributeFetcher := userMock.NewMockUserAttributesFetcher(ctrl)
	mockTieringDataCollector := mocks.NewMockDataCollector(ctrl)
	mockEvaluator := releaseMock.NewMockIEvaluator(ctrl)

	type args struct {
		ctx     context.Context
		actorId string
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardsClient *mocks3.MockRewardsGeneratorClient, mockTieringClient *mocks2.MockTieringClient, mockSegmentClient *segmentMock.MockSegmentationServiceClient)
		res        wantRes
	}{
		{
			name: "should return template : 'AMB below {{.TierMinBalance}}' for DOWNGRADE_AMB_LOW segment when AMB below current tier target AMB.",
			args: args{ctx, actorId},
			setupMocks: func(mockRewardsClient *mocks3.MockRewardsGeneratorClient, mockTieringClient *mocks2.MockTieringClient, mockSegmentClient *segmentMock.MockSegmentationServiceClient) {
				mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						DedupeInfo: &bankcust.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				mockUserAttributeFetcher.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&user.IsNonResidentUserResponse{
					IsNonResidentUser:   false,
					ResidentCountryCode: typesv2.CountryCode_COUNTRY_CODE_IND,
				}, nil)
				mockTieringDataCollector.EXPECT().GetTieringEssentials(gomock.Any(), actorId).Return(&feTieringHelper.TieringFeEssentials{
					CurrentTier:              external.Tier_TIER_FI_PLUS,
					PreviousTier:             external.Tier_TIER_FI_REGULAR,
					NextHigherTiers:          []external.Tier{external.Tier_TIER_FI_INFINITE},
					IsUserInDowngradedWindow: false,
					IsUserInGrace:            false,
					CurrTierMinBal:           &gmoney.Money{Units: 25000, Nanos: 0, CurrencyCode: "INR"},
					DowngradedTierMinBal:     &gmoney.Money{Units: 5000, Nanos: 0, CurrencyCode: "INR"},
					DowngradeWindowDuration:  0,
					GraceWindowDuration:      0,
					GracePeriodExpiry:        time.Time{},
					LastMovementTimestamp:    time.Time{},
					LastUpgradeDetails: &external.LatestMovementDetails{
						ToTier:                   external.Tier_TIER_FI_PLUS,
						MovementTimestamp:        timestamppb.New(time.Now().Add(-24 * time.Hour)),
						EntryCriteriaOptionType:  0,
						LatestCriteriaOptionType: 0,
					},
					LastDowngradeDetails:                        nil,
					TieringPitchResp:                            nil,
					GetConfigParamsResp:                         nil,
					IsMultipleWaysToEnterTieringEnabledForActor: false,
					CriteriaMinValues:                           nil,
					TierCriteriaMinValuesMap:                    nil,
					IsUSStocksAccountActive:                     false,
				}, nil)
				mockTieringDataCollector.EXPECT().GetCustomRewardProjectionsAggregate(gomock.Any(), actorId, rewards.RewardType_FI_COINS, external.Tier_TIER_FI_PLUS, data_collector.GetMonthYear(time.Now())).Return(float32(2999), nil)
				mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
				mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).AnyTimes().Return(&segmentPb.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						"upgrade_segment": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
					},
				}, nil)
				mockTieringClient.EXPECT().GetAMBInfo(gomock.Any(), gomock.Any()).Return(&tiering.GetAMBInfoResponse{
					Status:     rpc.StatusOk(),
					CurrentAmb: &gmoney.Money{Units: 24000, Nanos: 0, CurrencyCode: "INR"},
					TargetAmb:  &gmoney.Money{Units: 25000, Nanos: 0, CurrencyCode: "INR"},
				}, nil).AnyTimes()
			},
			res: wantRes{
				badge: &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
				promptIcon: &feHomePb.Icon{
					IconImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home/<USER>"},
					Action: &feHomePb.Icon_Deeplink{
						Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN},
					},
					ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
					BgColour: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center:      &ui.CenterCoordinates{CenterX: 107, CenterY: -26},
								OuterRadius: 100,
								Colours:     []string{"#DBB295", "#84432E"},
							},
						},
					},
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/home/<USER>",
								},
							},
						},
					},
				},
				promptTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "AMB below ₹25,000"},
					FontColor:    "#FFFFFF",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				},
				wantErr: false,
			},
		},
		{
			name: "should return notch title : 'Claim {{.Month}} rewards 🎁' for segment MONEY_PLANT_NOT_REDEEMED ",
			args: args{ctx, actorId},
			setupMocks: func(mockRewardsClient *mocks3.MockRewardsGeneratorClient, mockTieringClient *mocks2.MockTieringClient, mockSegmentClient *segmentMock.MockSegmentationServiceClient) {
				mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						DedupeInfo: &bankcust.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				mockUserAttributeFetcher.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&user.IsNonResidentUserResponse{
					IsNonResidentUser:   false,
					ResidentCountryCode: typesv2.CountryCode_COUNTRY_CODE_IND,
				}, nil)
				mockTieringDataCollector.EXPECT().GetTieringEssentials(gomock.Any(), actorId).Return(&feTieringHelper.TieringFeEssentials{
					CurrentTier:              external.Tier_TIER_FI_PLUS,
					PreviousTier:             external.Tier_TIER_FI_REGULAR,
					NextHigherTiers:          []external.Tier{external.Tier_TIER_FI_INFINITE},
					IsUserInDowngradedWindow: false,
					IsUserInGrace:            false,
					CurrTierMinBal:           &gmoney.Money{Units: 25000, Nanos: 0, CurrencyCode: "INR"},
					DowngradedTierMinBal:     &gmoney.Money{Units: 5000, Nanos: 0, CurrencyCode: "INR"},
					DowngradeWindowDuration:  0,
					GraceWindowDuration:      0,
					GracePeriodExpiry:        time.Time{},
					LastMovementTimestamp:    time.Time{},
					LastUpgradeDetails: &external.LatestMovementDetails{
						ToTier:                   external.Tier_TIER_FI_PLUS,
						MovementTimestamp:        timestamppb.New(time.Now().AddDate(0, -1, 0)),
						EntryCriteriaOptionType:  0,
						LatestCriteriaOptionType: 0,
					},
					LastDowngradeDetails:                        nil,
					TieringPitchResp:                            nil,
					GetConfigParamsResp:                         nil,
					IsMultipleWaysToEnterTieringEnabledForActor: false,
					CriteriaMinValues:                           nil,
					TierCriteriaMinValuesMap:                    nil,
					IsUSStocksAccountActive:                     false,
				}, nil)
				mockTieringDataCollector.EXPECT().GetCustomRewardProjectionsAggregate(gomock.Any(), actorId, rewards.RewardType_FI_COINS, external.Tier_TIER_FI_PLUS, data_collector.GetMonthYear(time.Now())).Return(float32(2999), nil)
				mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
				mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).AnyTimes().Return(&segmentPb.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						"73422cc9-fc2e-4604-b011-3f7cf9956043": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						"65ff0b4e-c0d7-4608-b4c1-2cebffdb30f6": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						"9ca74582-a648-47aa-a24a-51f71bcea6c4": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
					},
				}, nil)
				mockTieringClient.EXPECT().GetAMBInfo(gomock.Any(), gomock.Any()).Return(&tiering.GetAMBInfoResponse{
					Status:     rpc.StatusOk(),
					CurrentAmb: &gmoney.Money{Units: 25001, Nanos: 0, CurrencyCode: "INR"},
					TargetAmb:  &gmoney.Money{Units: 25000, Nanos: 0, CurrencyCode: "INR"},
				}, nil).AnyTimes()
				mockTieringDataCollector.EXPECT().GetRewardsByActorId(gomock.Any(), actorId, external.Tier_TIER_FI_PLUS).Return(&rewards.RewardsResponse{
					Status: rpc.StatusOk(),
					Rewards: []*rewards.Reward{
						{Id: "reward-1"},
					},
				}, nil)
			},
			res: wantRes{
				badge: &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
				promptIcon: &feHomePb.Icon{
					IconImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home/<USER>"},
					Action: &feHomePb.Icon_Deeplink{
						Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN},
					},
					ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
					BgColour: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center:      &ui.CenterCoordinates{CenterX: 107, CenterY: -26},
								OuterRadius: 100,
								Colours:     []string{"#DBB295", "#84432E"},
							},
						},
					},
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/home/<USER>",
								},
							},
						},
					},
				},
				promptTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Claim %s rewards 🎁", time.Now().AddDate(0, -1, 0).Month().String()[:3])},
					FontColor:    "#FFFFFF",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				},
				wantErr: false,
			},
		},
		{
			name: "should return notch template : 'Unlock 3% back on spends!' for segment UPGRADE_OPPORTUNITY_PLUS_TO_INFINITE from segment client",
			args: args{ctx, actorId},
			setupMocks: func(mockRewardsClient *mocks3.MockRewardsGeneratorClient, mockTieringClient *mocks2.MockTieringClient, mockSegmentClient *segmentMock.MockSegmentationServiceClient) {
				mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						DedupeInfo: &bankcust.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				mockUserAttributeFetcher.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&user.IsNonResidentUserResponse{
					IsNonResidentUser:   false,
					ResidentCountryCode: typesv2.CountryCode_COUNTRY_CODE_IND,
				}, nil)
				mockTieringDataCollector.EXPECT().GetTieringEssentials(gomock.Any(), actorId).Return(&feTieringHelper.TieringFeEssentials{
					CurrentTier:              external.Tier_TIER_FI_PLUS,
					PreviousTier:             external.Tier_TIER_FI_REGULAR,
					NextHigherTiers:          []external.Tier{external.Tier_TIER_FI_INFINITE},
					IsUserInDowngradedWindow: false,
					IsUserInGrace:            false,
					CurrTierMinBal:           &gmoney.Money{Units: 25000, Nanos: 0, CurrencyCode: "INR"},
					DowngradedTierMinBal:     &gmoney.Money{Units: 5000, Nanos: 0, CurrencyCode: "INR"},
					DowngradeWindowDuration:  0,
					GraceWindowDuration:      0,
					GracePeriodExpiry:        time.Time{},
					LastMovementTimestamp:    time.Time{},
					LastUpgradeDetails: &external.LatestMovementDetails{
						ToTier:                   external.Tier_TIER_FI_PLUS,
						MovementTimestamp:        timestamppb.New(time.Now().AddDate(0, -1, 0)),
						EntryCriteriaOptionType:  0,
						LatestCriteriaOptionType: 0,
					},
					LastDowngradeDetails:                        nil,
					TieringPitchResp:                            nil,
					GetConfigParamsResp:                         nil,
					IsMultipleWaysToEnterTieringEnabledForActor: false,
					CriteriaMinValues:                           nil,
					TierCriteriaMinValuesMap:                    nil,
					IsUSStocksAccountActive:                     false,
				}, nil)
				mockTieringDataCollector.EXPECT().GetCustomRewardProjectionsAggregate(gomock.Any(), actorId, rewards.RewardType_FI_COINS, external.Tier_TIER_FI_PLUS, data_collector.GetMonthYear(time.Now())).Return(float32(2999), nil)
				mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
				mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).AnyTimes().Return(&segmentPb.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						"73422cc9-fc2e-4604-b011-3f7cf9956043": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						"65ff0b4e-c0d7-4608-b4c1-2cebffdb30f6": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						"9ca74582-a648-47aa-a24a-51f71bcea6c4": {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
					},
				}, nil)
				mockTieringClient.EXPECT().GetAMBInfo(gomock.Any(), gomock.Any()).Return(&tiering.GetAMBInfoResponse{
					Status:     rpc.StatusOk(),
					CurrentAmb: &gmoney.Money{Units: 25001, Nanos: 0, CurrencyCode: "INR"},
					TargetAmb:  &gmoney.Money{Units: 25000, Nanos: 0, CurrencyCode: "INR"},
				}, nil).AnyTimes()
				mockTieringDataCollector.EXPECT().GetRewardsByActorId(gomock.Any(), actorId, external.Tier_TIER_FI_PLUS).Return(&rewards.RewardsResponse{
					Status:  rpc.StatusOk(),
					Rewards: []*rewards.Reward{},
				}, nil)
			},
			res: wantRes{
				badge: &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
				promptIcon: &feHomePb.Icon{
					IconImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home/<USER>"},
					Action: &feHomePb.Icon_Deeplink{
						Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN},
					},
					ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
					BgColour: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center:      &ui.CenterCoordinates{CenterX: 107, CenterY: -26},
								OuterRadius: 100,
								Colours:     []string{"#DBB295", "#84432E"},
							},
						},
					},
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/home/<USER>",
								},
							},
						},
					},
				},
				promptTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Unlock 3% back on spends!"},
					FontColor:    "#FFFFFF",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				},
				wantErr: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRewardsClient := mocks3.NewMockRewardsGeneratorClient(ctrl)
			mockTieringClient := mocks2.NewMockTieringClient(ctrl)
			mockSegmentClient := segmentMock.NewMockSegmentationServiceClient(ctrl)
			tt.setupMocks(mockRewardsClient, mockTieringClient, mockSegmentClient)
			svc := &Service{
				bcClient:             mockBcClient,
				vkycFeClient:         mockVkycFeClient,
				userAttributeFetcher: mockUserAttributeFetcher,
				tieringDataCollector: mockTieringDataCollector,
				evaluator:            mockEvaluator,
				segmentClient:        mockSegmentClient,
				genconf:              gconf,
				beTieringClient:      mockTieringClient,
			}
			badge, promptIcon, promptTitle, err := svc.getNotchComponents(tt.args.ctx, tt.args.actorId)
			got := wantRes{
				badge:       badge,
				promptIcon:  promptIcon,
				promptTitle: promptTitle,
				wantErr:     err != nil,
			}

			if !proto.Equal(tt.res.badge, got.badge) {
				t.Errorf("badge mismatch\nwant: %+v\ngot: %+v", tt.res.badge, got.badge)
			}
			if !proto.Equal(tt.res.promptIcon, got.promptIcon) {
				t.Errorf("promptIcon mismatch\nwant: %+v\ngot: %+v", tt.res.promptIcon, got.promptIcon)
			}
			if !proto.Equal(tt.res.promptTitle, got.promptTitle) {
				t.Errorf("promptTitle mismatch\nwant: %+v\ngot: %+v", tt.res.promptTitle, got.promptTitle)
			}
			if tt.res.wantErr != got.wantErr {
				t.Errorf("error presence mismatch: want %v, got %v", tt.res.wantErr, got.wantErr)
			}
		})
	}
}
