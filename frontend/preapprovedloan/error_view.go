package preapprovedloan

import (
	"fmt"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/errors"
)

var (
	defaultErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN,
		Options: &errors.ErrorView_FullScreenErrorView{
			FullScreenErrorView: &errors.FullScreenErrorView{
				Title:    "We faced a server error",
				Subtitle: "Uh-oh! We faced a server error while loading this page. Please try again.",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_RETRY,
						Text: "Retry",
					},
				},
			},
		},
	}

	bottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "We faced a server error",
				Subtitle: "Uh-oh! We faced a server error while loading this page. Please try again.",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_RETRY,
						Text: "Retry",
					},
				},
			},
		},
	}

	bottomSheetBackErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "We faced a server error",
				Subtitle: "Uh-oh! We faced a server error while loading this page. Please try again.",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Back",
					},
				},
			},
		},
	}

	prePayNotAllowedBottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "We can't process your request",
				Subtitle: "Uh-oh! Pre-pay is not allowed at this moment!",
			},
		},
	}

	prePayNotAllowedExistingPaymentInProgressErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "An existing payment is under process",
				Subtitle: "Your recent payment is still being processed by our partner. Please try again in 2 hrs",
			},
		},
	}

	mandateNotActiveBottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "Uh-oh! It seems some steps are missing",
				Subtitle: "Your mandate setup is not completed. Please cancel and retry your application or contact customer support",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Okay",
					},
				},
			},
		},
	}

	addressNotServiceableBottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "This address is not serviceable",
				Subtitle: "Unfortunately, we cannot process loan applications at the pin code you entered.",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Got it",
					},
				},
			},
		},
	}

	prepayAmountIsZeroBottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "Amount cannot be zero",
				Subtitle: "Please select amount greater than zero",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Got it",
					},
				},
			},
		},
	}

	invalidAlternateNumberBottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "Alternate number can not be same as the registered number",
				Subtitle: "Please enter a different phone number",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Got it",
					},
				},
			},
		},
	}

	noOtherOfferErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title: "Sorry! You don't have any other offers",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Okay",
					},
				},
			},
		},
	}

	locationRequiredErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title: "Please enable GPS location permission and try again",
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Okay",
					},
				},
			},
		},
	}
)

func getBankNotSupportedBottomSheetErrView() *errors.ErrorView {
	subTitleText := fmt.Sprintf("We will not be able to set your selected bank account as your disbursal account at this point in time.\nPlease try using a different bank account.")
	titleText := "Bank not supported"
	return &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    titleText,
				Subtitle: subTitleText,
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_DONE,
						Text: "Got it",
					},
				},
			},
		},
	}
}

func getApplicationNonCancellableFullSheetErrorView() *errors.ErrorView {
	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN,
		Options: &errors.ErrorView_FullScreenErrorView{
			FullScreenErrorView: &errors.FullScreenErrorView{
				ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/callout-three-dots.png",
				Title:    "Your loan application is under progress",
				Subtitle: "Since you only have a few steps left, you cannot explore other offers at the moment",
				Ctas: []*errors.CTA{
					{
						Type:         errors.CTA_DONE,
						Text:         "Ok, got it",
						DisplayTheme: errors.CTA_PRIMARY,
					},
				},
			},
		},
	}
}

func getApplicationNonCancellableBottomSheetErrorView() *errors.ErrorView {
	return &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Icon:         commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/callout-three-dots.png", 140, 140),
				TitleText:    commonTypes.GetTextFromStringFontColourFontStyle("Your loan application is under progress", "#313234", commonTypes.FontStyle_HEADLINE_XL),
				SubtitleText: commonTypes.GetTextFromStringFontColourFontStyle("Since you only have a few steps left, you cannot explore other offers at the moment", "#929599", commonTypes.FontStyle_BODY_S),
				Ctas: []*errors.CTA{
					{
						Type:         errors.CTA_DONE,
						Text:         "Ok, got it",
						DisplayTheme: errors.CTA_PRIMARY,
					},
				},
				BgColour: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
			},
		},
	}
}
