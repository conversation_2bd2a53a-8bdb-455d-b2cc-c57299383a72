//go:generate mockgen -source=wb_landing_dashboard.go -destination=./mocks/mock_wb_landing_dashboard.go package=mocks
package dashboard

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"

	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/frontend/config/genconf"

	segmentPb "github.com/epifi/gamma/api/segment"

	typesPb "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/gamma/pkg/feature/release"

	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	feNetworthUi "github.com/epifi/gamma/api/frontend/insights/networth/ui"
	beNetworthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/networth/enums"
	feWealthAnalyserPb "github.com/epifi/gamma/api/insights/secrets/frontend"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	networthConfig "github.com/epifi/gamma/frontend/insights/networth/config"
	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"
	"github.com/epifi/gamma/frontend/insights/networth/generator/section"
	secretColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
)

type wbSectionDisplayState struct {
	isZeroState                       bool
	isDailyPortfolioTrackerAvailable  bool
	isWeeklyPortfolioTrackerAvailable bool
}

type ConnectMoreParams struct {
	bgColor             string
	borderColor         string
	borderWidth         int32
	connectMoreDeeplink *deeplinkPb.Deeplink
}

var (
	WealthBuilderLandingWireSet = wire.NewSet(NewWealthBuilderLanding, wire.Bind(new(IWealthBuilderLanding), new(*WealthBuilderLanding)))
)

const (
	dashboardType = enums.NetWorthDashBoardType_NET_WORTH
)

type WealthBuilderLanding struct {
	networthConfig          *networthConfig.Config
	networthClient          beNetworthPb.NetWorthClient
	dataFetcher             data_fetcher.NetWorthDataFetcher
	sectionGenerator        section.IGenerator
	deeplinkBuilder         deeplink_builder.IDeeplinkBuilder
	releaseEvaluator        release.IEvaluator
	conf                    *genconf.Config
	segmentSrvClient        segmentPb.SegmentationServiceClient
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
	dailyTracker            *strategyImpl.DailyTracker
	weeklyTracker           *strategyImpl.WeeklyTracker
}

type IWealthBuilderLanding interface {
	GetWealthBuilderLandingDashboard(ctx context.Context, actorId string) (*feNetworthUi.WealthBuilderLandingComponent, error)
}

func NewWealthBuilderLanding(
	networthConfig *networthConfig.Config,
	networthClient beNetworthPb.NetWorthClient,
	dataFetcher data_fetcher.NetWorthDataFetcher,
	sectionGenerator section.IGenerator,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
	releaseEvaluator release.IEvaluator,
	conf *genconf.Config,
	segmentSrvClient segmentPb.SegmentationServiceClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	dailyTracker *strategyImpl.DailyTracker,
	weeklyTracker *strategyImpl.WeeklyTracker,
) *WealthBuilderLanding {
	return &WealthBuilderLanding{
		networthConfig:          networthConfig,
		networthClient:          networthClient,
		dataFetcher:             dataFetcher,
		sectionGenerator:        sectionGenerator,
		deeplinkBuilder:         deeplinkBuilder,
		releaseEvaluator:        releaseEvaluator,
		conf:                    conf,
		segmentSrvClient:        segmentSrvClient,
		variableGeneratorClient: variableGeneratorClient,
		dailyTracker:            dailyTracker,
		weeklyTracker:           weeklyTracker,
	}
}

// GetWealthBuilderLandingDashboard Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-45899&t=R5KI7488DNfw2YTx-0
func (w *WealthBuilderLanding) GetWealthBuilderLandingDashboard(ctx context.Context, actorId string) (*feNetworthUi.WealthBuilderLandingComponent, error) {
	networthDashboardConfig, err := w.networthConfig.GetNetworthDashboardConfig()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get networth dashboard config")
	}
	categoriesDataMap, err := w.dataFetcher.GetCategoriesValue(ctx, actorId, dashboardType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get categories value")
	}
	categoriesStatusMap, err := w.dataFetcher.GetCategoriesStatus(ctx, actorId, categoriesDataMap, dashboardType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get categories status")
	}
	refreshDetailsRes, err := w.networthClient.GetNetWorthInstrumentsRefreshDetails(ctx, &beNetworthPb.GetNetWorthInstrumentsRefreshDetailsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(refreshDetailsRes, err); grpcErr != nil {
		logger.Error(ctx, "failed to get net worth instrument refresh details", zap.Error(err))
	}
	refreshDetailsMap := w.dataFetcher.ConvertRefreshDetailsToMap(ctx, refreshDetailsRes.GetInstrumentRefreshSummary())

	// Evaluate feature flags for dashboard variants and CTAs
	wbDashboardLiabilitiesEnabled := w.isFeatureEnabled(ctx, typesPb.Feature_FEATURE_WB_DASHBOARD_LIABILITIES, actorId)
	exportToAiEnabled := w.isFeatureEnabled(ctx, typesPb.Feature_FEATURE_FI_MCP_TOTP_CODE, actorId)
	magicImportEnabled := w.isFeatureEnabled(ctx, typesPb.Feature_FEATURE_WB_MAGIC_IMPORT, actorId)
	isWbDashboardV2Enabled := w.isWbDashboardEnabled(ctx)

	// Build all dashboard sections (assets, liabilities)
	var wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection
	isAllWidgetsRequired := false
	for _, sectionConfig := range networthDashboardConfig.GetSections() {
		if sectionConfig.GetSectionType() == networthFePb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS ||
			(sectionConfig.GetSectionType() == networthFePb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES && wbDashboardLiabilitiesEnabled && !magicImportEnabled) {
			generatedSection, sectionErr := w.sectionGenerator.GenerateWealthBuilderLandingSection(ctx, actorId, sectionConfig, categoriesDataMap, categoriesStatusMap, refreshDetailsMap, isAllWidgetsRequired)
			if sectionErr != nil {
				return nil, fmt.Errorf("failed to generate net worth section %s: %w", sectionConfig.GetSectionType(), sectionErr)
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, generatedSection)
		}
	}

	if len(wealthBuilderLandingSections[0].Widgets) < common.AssetWidgetVisibleLimit {
		// disabling magic import if less than 5 assets are connected to avoid panic while slicing
		logger.Error(ctx, "Magic Import feature is disabled as less than 5 assets are connected", zap.String(logger.ACTOR_ID_V2, actorId))
		magicImportEnabled = false
	}

	// If the Magic Import feature is enabled, add the Magic Import CTA to the dashboard's action CTAs.
	// This CTA allows users to import their wealth data using the Magic Import feature.
	if magicImportEnabled {
		magicImportEntryPointWidget := buildMagicImportEntryPoint()
		widgets := wealthBuilderLandingSections[0].Widgets

		// Split into two parts
		firstPart := widgets[:4]
		secondPart := widgets[4:]

		// Sandwich the magic import widget at 5th position
		var updatedWidgets []*feNetworthUi.WidgetV2
		updatedWidgets = append(updatedWidgets, firstPart...)
		updatedWidgets = append(updatedWidgets, magicImportEntryPointWidget)
		updatedWidgets = append(updatedWidgets, secondPart...)

		// Assign the updated list back
		wealthBuilderLandingSections[0].Widgets = updatedWidgets
	}

	// Build dashboard header and section display states
	wealthBuilderDashboardHeader, wbSectionDisplayStates, err := w.getWealthBuilderSectionHeader(ctx, actorId, networthDashboardConfig, categoriesDataMap, refreshDetailsRes.GetInstrumentRefreshSummary())
	if err != nil {
		return nil, fmt.Errorf("failed to get wealth builder section header: %w", err)
	}

	// Build the deeplink for the "Connect More" CTA
	// connect more cta will be shown more than 5 assets are connected
	// if less than 5 assets are connected, then connect more cta will not be shown
	// instead, add more deeplink will be present on 5th widget of first section (To be implemented P1)
	// as of now, connect more cta will be shown for all cases (changes this logic later)
	connectMoreDeeplink := w.getConnectMoreDeeplink(networthDashboardConfig, categoriesDataMap)

	// Build the main "Connect More" CTA (style depends on dashboard version)
	var connectMore *ui.IconTextComponent
	switch {
	case isWbDashboardV2Enabled:
		connectParams := &ConnectMoreParams{
			bgColor:             colors.ColorDarkBase,
			borderColor:         "",
			borderWidth:         0,
			connectMoreDeeplink: connectMoreDeeplink,
		}
		connectMore = w.buildConnectMore(connectParams, magicImportEnabled)
	default:
		connectParams := &ConnectMoreParams{
			bgColor:             colors.ColorBlackPearl,
			borderColor:         colors.ColorOnDarkDisabled700,
			borderWidth:         1,
			connectMoreDeeplink: connectMoreDeeplink,
		}
		connectMore = w.buildConnectMore(connectParams, magicImportEnabled)
	}

	// Prepare variables for other CTAs and banners
	var (
		exportToAiCta, connectMoreV2, footerCtaV2 *feNetworthUi.WealthBuilderLandingCta
		footerBanner                              *sections.Section
		bannerErr                                 error
	)

	// Build collapsible details for dashboard (if applicable)
	collapsibleDetails := w.getDashboardCollapsibleDetails(wealthBuilderLandingSections, isWbDashboardV2Enabled, magicImportEnabled)

	// Build Export to AI CTA and footer banner if MCP TOTP feature and WBLandingV2Dashboard is enabled
	if isWbDashboardV2Enabled && exportToAiEnabled {
		aiDeeplink, dlErr := w.deeplinkBuilder.ExportToAiBottomSheetScreen(ctx)
		if dlErr != nil {
			logger.Error(ctx, "AI deeplink not found")
		}
		exportToAiCta = w.buildExportToAiCta(aiDeeplink, collapsibleDetails == nil && isWbDashboardV2Enabled)
		footerBanner, bannerErr = getExportToAiBanner(aiDeeplink, epificontext.AppPlatformFromContext(ctx))
	}
	if bannerErr != nil {
		logger.Error(ctx, "failed to get Export to Ai Banner", zap.Error(bannerErr))
	}

	// Build V2 version of Connect More CTA and the V2 footer CTA
	// connectMoreV2 is a different style of the "Connect More" CTA, specifically designed for the V2 dashboard footer.
	// It uses a different component and visual style than the main connectMore CTA, which is used as an Footer CTA
	// Instead of an Action CTA
	connectMoreV2 = w.buildConnectMoreV2(connectMoreDeeplink)
	footerCtaV2 = w.getPartitionFooterCtaV2(wbSectionDisplayStates, collapsibleDetails == nil && isWbDashboardV2Enabled)

	// Build the footer CTAs slice in a clear, sequential way
	footerCtas := make([]*feNetworthUi.WealthBuilderLandingCta, 0)
	if isWbDashboardV2Enabled && collapsibleDetails != nil {
		footerCtas = append(footerCtas, connectMoreV2)
	}
	if isWbDashboardV2Enabled && exportToAiEnabled {
		footerCtas = append(footerCtas, exportToAiCta)
	}
	footerCtas = append(footerCtas, footerCtaV2)

	// Assemble the dashboard object
	wbLandingDashboard := &feNetworthUi.WealthBuilderLandingDashboard{
		DashboardHeader:              wealthBuilderDashboardHeader,
		WealthBuilderLandingSections: wealthBuilderLandingSections,
		CollapsibleDetails:           collapsibleDetails,
		FooterCtas:                   footerCtas,
		FooterBanner:                 footerBanner,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: colors.ColorDarkBase},
		},
	}

	// Add Action CTAs (connect more, magic import) as needed
	if isWbDashboardV2Enabled && collapsibleDetails == nil {
		wbLandingDashboard.ActionCta = append(wbLandingDashboard.ActionCta, connectMore)
	}

	// Assemble the final landing component
	wbLandingComponent := &feNetworthUi.WealthBuilderLandingComponent{
		WealthBuilderLandingDashboard: wbLandingDashboard,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"},
		},
	}

	// Handle V1 dashboard special case for ActionCta and footer CTA
	//nolint:gocritic
	if !isWbDashboardV2Enabled {
		wbLandingComponent.WealthBuilderLandingDashboard.ActionCta = append(wbLandingDashboard.ActionCta, connectMore)
		wbLandingComponent.WealthBuilderLandingDashboard.CornerRadius = 0
		footerCta := w.getPartitionFooterCta(categoriesDataMap, wbSectionDisplayStates)
		wbLandingComponent.FooterCta = footerCta
	}

	return wbLandingComponent, nil
}

func (w *WealthBuilderLanding) isFeatureEnabled(ctx context.Context, feature typesPb.Feature, actorId string) bool {
	featureEnabled, err := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in checking if feature is enabled", zap.String(logger.FEATURE, feature.String()), zap.Error(err))
		return false
	}
	return featureEnabled
}

func (w *WealthBuilderLanding) getWealthBuilderSectionHeader(ctx context.Context, actorId string, netWorthDashboardConfig *networthFePb.NetWorthDashboardConfig, categoriesDataMap map[networthFePb.NetworthCategory]*data_fetcher.CategoryData, refreshDetails []*beNetworthPb.InstrumentRefreshDetails) (*feNetworthUi.DashboardHeader, *wbSectionDisplayState, error) {
	if len(netWorthDashboardConfig.GetSections()) == 0 {
		return nil, nil, nil
	}
	var refreshTag *ui.IconTextComponent
	for _, refreshDetail := range refreshDetails {
		if refreshDetail.GetRefreshStatus() == beNetworthPb.RefreshStatus_REFRESH_STATUS_REQUIRED {
			netWorthRefreshBottomSheetScreenDeeplink, deeplinkErr := w.deeplinkBuilder.NetWorthRefreshBottomSheetScreen(ctx, refreshDetails, actorId, dashboardType)
			if deeplinkErr != nil {
				return nil, nil, fmt.Errorf("failed to get net worth refresh bottom sheet screen deeplink: %w", deeplinkErr)
			}
			refreshTag = ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.Refresh, "#E795AE", commontypes.FontStyle_SUBTITLE_XS)).
				WithBorder(common.WealthBuilderComponentsBorderColor, 1).WithContainerBackgroundColor(colors.ColorBlackPearl).WithContainerPaddingSymmetrical(8, 6).
				WithLeftImageUrlHeightAndWidth(common.RefreshIcon, 20, 20).WithLeftImagePadding(2).WithContainerCornerRadius(32).
				WithDeeplink(netWorthRefreshBottomSheetScreenDeeplink)
			break
		}
	}
	var (
		percentageTag *ui.IconTextComponent
		err           error
	)
	isDailyPortfolioTrackerEnabled, evalErr := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN).WithActorId(actorId))
	if evalErr != nil {
		return nil, nil, fmt.Errorf("error in evaluating portfolio tracker landing screen release flag: %w", evalErr)
	}

	isWeeklyPortfolioTrackerEnabled, weekEvalErr := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER).WithActorId(actorId))
	if weekEvalErr != nil {
		return nil, nil, fmt.Errorf("error in evaluating weekly portfolio tracker landing screen release flag: %w", weekEvalErr)
	}

	weeklyTrackerEnabled := IsWeeklyTrackerEnabled(isWeeklyPortfolioTrackerEnabled)

	if isDailyPortfolioTrackerEnabled {
		percentageTag, err = w.getPercentageTag(ctx, actorId, weeklyTrackerEnabled)
		if err != nil {
			// not returning error in this case to show bare minimal dashboard in case of failure in building daily change page
			logger.Error(ctx, "failed to get daily percentage tag", zap.Error(err))
		}
	}

	var (
		tags                []*ui.IconTextComponent
		sectionDisplayState *wbSectionDisplayState
	)

	if percentageTag != nil {
		tags = append(tags, percentageTag)
		switch {
		case weeklyTrackerEnabled:
			sectionDisplayState = &wbSectionDisplayState{
				isWeeklyPortfolioTrackerAvailable: true,
			}
		default:
			sectionDisplayState = &wbSectionDisplayState{
				isDailyPortfolioTrackerAvailable: true,
			}
		}
	}
	if refreshTag != nil {
		tags = append(tags, refreshTag)
	}

	assetSectionConfig := netWorthDashboardConfig.GetSections()[0]
	for _, widgetConfig := range assetSectionConfig.GetWidgets() {
		category := widgetConfig.GetCategory()
		if categoryData, exists := categoriesDataMap[category]; exists {
			if categoryData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS {
				return &feNetworthUi.DashboardHeader{
					DashboardHeader: &feNetworthUi.DashboardHeader_SectionHeaderConnectedState{
						SectionHeaderConnectedState: &feNetworthUi.DashboardHeaderConnectedState{
							Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.YourWealth, colors.ColorSupportingJade100, commontypes.FontStyle_HEADLINE_M).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
							WealthDisplayDetails: &feNetworthUi.WealthDisplayDetails{
								CurrencySymbol:    commontypes.GetTextFromStringFontColourFontStyle("₹", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_CURRENCY_XL),
								TotalDisplayValue: commontypes.GetTextFromStringFontColourFontStyle("0", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_3XL),
							},
							Tags: tags,
							VisibilityDetails: &feNetworthUi.VisibilityDetails{
								Hide: commontypes.GetVisualElementFromUrlHeightAndWidth(common.InfoHide, 28, 20),
								Show: commontypes.GetVisualElementFromUrlHeightAndWidth(common.InfoShow, 28, 20),
							},
						},
					},
				}, sectionDisplayState, nil
			}
		}
	}

	return &feNetworthUi.DashboardHeader{
		DashboardHeader: &feNetworthUi.DashboardHeader_SectionHeaderZeroState{
			SectionHeaderZeroState: &feNetworthUi.DashboardHeaderZeroState{
				Title: commontypes.GetTextFromStringFontColourFontStyle(common.ZeroStateTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_XL).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
			},
		},
	}, &wbSectionDisplayState{isZeroState: true}, nil
}

func (w *WealthBuilderLanding) getDashboardCollapsibleDetails(wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection, isWbDashboardV2Enabled bool, magicImportEnabled bool) *feNetworthUi.CollapsibleDetails {
	if magicImportEnabled {
		return nil
	}
	// show collapsible details only if there are more than 5 asset widgets are connected or one of liabilities widgets are connected
	if (len(wealthBuilderLandingSections) > 0 && len(wealthBuilderLandingSections[0].GetWidgets()) > 5) ||
		(len(wealthBuilderLandingSections) > 1 && len(wealthBuilderLandingSections[1].GetWidgets()) > 0) {
		collapsibleDetails := &feNetworthUi.CollapsibleDetails{
			ShowMoreCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.ExpandText, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
				WithRightImageUrlHeightAndWidth(common.DownChevron, 20, 20).WithRightImagePadding(4).
				WithContainerBackgroundColor(colors.ColorDarkBase).WithContainerCornerRadius(32).WithContainerPadding(10, 12, 10, 12),
			ShowLessCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.CollapseText, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
				WithRightImageUrlHeightAndWidth(common.UpChevron, 20, 20).WithRightImagePadding(4).
				WithContainerBackgroundColor(colors.ColorDarkBase).WithContainerCornerRadius(32).WithContainerPadding(10, 12, 10, 12),
		}
		if !isWbDashboardV2Enabled {
			collapsibleDetails.ShowMoreCta = collapsibleDetails.ShowMoreCta.WithBorder(colors.ColorOnDarkDisabled700, 1)
			collapsibleDetails.ShowLessCta = collapsibleDetails.ShowLessCta.WithBorder(colors.ColorOnDarkDisabled700, 1)
		}
		return collapsibleDetails
	}
	return nil
}

// deprecated function, current changes are in getPartitionFooterCtaV2
func (w *WealthBuilderLanding) getPartitionFooterCta(categoriesDataMap map[networthFePb.NetworthCategory]*data_fetcher.CategoryData, wbSectionDisplayState *wbSectionDisplayState) *ui.IconTextComponent {
	if wbSectionDisplayState == nil {
		return nil
	}
	// Get mutual fund data before switch statement
	mfData, found := categoriesDataMap[networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS]
	hasMutualFunds := found && mfData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS

	// Base ITC builder with common configurations in all the cases
	baseITC := ui.NewITC().WithContainerBackgroundColor(colors.ColorForest).WithContainerCornerRadius(40).
		WithRightImageUrlHeightAndWidth(common.WhiteChevron, 24, 24).WithRightImagePadding(12).
		WithContainerPaddingSymmetrical(16, 12)

	switch {
	case wbSectionDisplayState.isWeeklyPortfolioTrackerAvailable:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.WeeklyReportText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithLeftVisualElementUrlHeightAndWidth(common.NewTextIcon, 20, 40).WithLeftImagePadding(12).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN})
	case wbSectionDisplayState.isDailyPortfolioTrackerAvailable:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.DailyReportText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithLeftVisualElementUrlHeightAndWidth(common.NewTextIcon, 20, 40).WithLeftImagePadding(12).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN})
	case hasMutualFunds:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.MfSummaryText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithDeeplink(deeplink_builder.WealthAnalyserReportDeeplink(feWealthAnalyserPb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_MUTUAL_FUND))
	case wbSectionDisplayState.isZeroState:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.TrackBankBalancesText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithDeeplink(networthDeeplink.ConnectedAccountDashboardDeeplink())
	default:
		return nil
	}
}

func (w *WealthBuilderLanding) getPartitionFooterCtaV2(wbSectionDisplayState *wbSectionDisplayState, isTalkToAiPrimary bool) *feNetworthUi.WealthBuilderLandingCta {
	// basic CTA with common props for all states
	baseCta := feNetworthUi.NewWealthBuilderLandingCta().
		WithCornerRadius(40).
		WithBorderProperty(1, 40).
		WithPrimaryStatus(true).
		WithIdentifier("daily_tracker")

	// "Talk to AI" color changes
	var textColor, iconUrl string
	switch {
	case isTalkToAiPrimary:
		baseCta = baseCta.WithBackgroundColor(colors.ColorCharcoal)
		textColor = colors.ColorForest
		iconUrl = common.TrackerIconForest
	default:
		baseCta = baseCta.WithBackgroundColor(colors.ColorForest)
		textColor = colors.ColorSnow
		iconUrl = common.TrackerIcon
	}

	// default is always get MF Summary
	if wbSectionDisplayState == nil {
		return baseCta.WithTitle(common.MfSummaryText, textColor, commontypes.FontStyle_SUBTITLE_M).
			WithDeeplink(deeplink_builder.WealthAnalyserReportDeeplink(feWealthAnalyserPb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_MUTUAL_FUND))
	}

	switch {
	case wbSectionDisplayState.isWeeklyPortfolioTrackerAvailable:
		return baseCta.WithTitle(common.WeeklyReportText, textColor, commontypes.FontStyle_SUBTITLE_M).
			WithIcon(iconUrl, 24, 24).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN})
	case wbSectionDisplayState.isDailyPortfolioTrackerAvailable:
		return baseCta.WithTitle(common.DailyReportText, textColor, commontypes.FontStyle_SUBTITLE_M).
			WithIcon(iconUrl, 24, 24).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN})
	case wbSectionDisplayState.isZeroState:
		return baseCta.WithTitle(common.TrackBankBalancesText, textColor, commontypes.FontStyle_SUBTITLE_M).
			WithDeeplink(networthDeeplink.ConnectedAccountDashboardDeeplink())
	default:
		return baseCta.WithTitle(common.MfSummaryText, textColor, commontypes.FontStyle_SUBTITLE_M).
			WithDeeplink(deeplink_builder.WealthAnalyserReportDeeplink(feWealthAnalyserPb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_MUTUAL_FUND))
	}
}

func (w *WealthBuilderLanding) getPercentageTag(ctx context.Context, actorId string, weeklyTrackerEnabled bool) (*ui.IconTextComponent, error) {
	variablesResp, getVariablesErr := w.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId: actorId,
		AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION,
		},
	})
	if getVariablesErr != nil {
		return nil, fmt.Errorf("failed to get analysis analyserVariablePb: %v", getVariablesErr)
	}

	var strategy strategyImpl.PortfolioTrackerStrategy
	strategy = w.dailyTracker
	displayText := common.OneDayText
	if weeklyTrackerEnabled {
		strategy = w.weeklyTracker
		displayText = common.OneWeekText
	}

	changeParams, err := strategy.GetChangeParams(ctx, actorId, variablesResp.GetVariableEnumMap())
	if err != nil {
		return nil, fmt.Errorf("failed to get change params for daily tracker: %w", err)
	}
	percentageChange := float64(0)
	if changeParams.TotalPreviousValue != 0 {
		percentageChange = (changeParams.TotalChange / changeParams.TotalPreviousValue) * 100
	}
	if percentageChange == 0 {
		logger.Info(ctx, "percentage change is 0, not showing daily percentage tag")
		return nil, nil
	}

	percentageSign := ""
	changePercentageColor := secretColors.ColorSupportingAmber200
	if percentageChange >= 0 {
		changePercentageColor = colors.SupportingMoss200
		percentageSign = "+"
	}
	percentageChangeText := fmt.Sprintf("%s%.2f%%", percentageSign, percentageChange)

	return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(displayText+" ", colors.ColorOnlightLowEmphasis, commontypes.FontStyle_SUBTITLE_XS),
		commontypes.GetTextFromStringFontColourFontStyle(percentageChangeText, changePercentageColor, commontypes.FontStyle_SUBTITLE_XS)).
		WithBorder(common.WealthBuilderComponentsBorderColor, 1).WithContainerPaddingSymmetrical(8, 6).WithContainerCornerRadius(32).
		WithRightImageUrlHeightAndWidth(common.WhiteChevron, 20, 20).
		WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN}), nil
}

// nolint: gosec
func (w *WealthBuilderLanding) isWbDashboardEnabled(ctx context.Context) bool {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform == commontypes.Platform_ANDROID && uint32(appVersion) >= w.conf.NetworthConfig().AppVersionsForWBDashboardV2().MinVersionAndroid() {
		return true
	}
	if appPlatform == commontypes.Platform_IOS && uint32(appVersion) >= w.conf.NetworthConfig().AppVersionsForWBDashboardV2().MinVersionIos() {
		return true
	}
	return false
}

// buildConnectMore constructs the "Connect More" CTA for the dashboard.
// It allows customization of background color and border, and is used for both V1 and V2 dashboard styles.
func (w *WealthBuilderLanding) buildConnectMore(connectParams *ConnectMoreParams, isMagicImportEnabled bool) *ui.IconTextComponent {
	itc := ui.NewITC().WithContainerCornerRadius(32).WithDeeplink(connectParams.connectMoreDeeplink)
	switch {
	case isMagicImportEnabled:
		itc = itc.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.SeeAll, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
			WithRightImageUrlHeightAndWidth(common.RightChevron, 20, 20).WithRightImagePadding(4).WithContainerPadding(10, 12, 10, 20)
	default:
		itc = itc.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.AddMore, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
			WithLeftImageUrlHeightAndWidth(common.GreenPlusIcon, 20, 20).WithLeftImagePadding(4).WithContainerPadding(10, 20, 10, 12)
	}
	if connectParams.borderColor != "" && connectParams.borderWidth > 0 {
		itc = itc.WithBorder(connectParams.borderColor, connectParams.borderWidth)
	}
	if connectParams.bgColor != "" {
		itc = itc.WithContainerBackgroundColor(connectParams.bgColor)
	}
	return itc
}

// buildConnectMoreV2 constructs the V2 version of the "Connect More" CTA for the dashboard footer.
// This version uses a different style and is only used in the V2 dashboard.
func (w *WealthBuilderLanding) buildConnectMoreV2(connectMoreDeeplink *deeplinkPb.Deeplink) *feNetworthUi.WealthBuilderLandingCta {
	return feNetworthUi.NewWealthBuilderLandingCta().
		WithBackgroundColor(colors.ColorCharcoal).
		WithCornerRadius(40).
		WithBorderProperty(1, 40).
		WithIcon(common.GreenPlusIcon, 24, 24).
		WithPrimaryStatus(false).
		WithDeeplink(connectMoreDeeplink).
		WithIdentifier("add_more")
}

// buildExportToAiCta constructs the "Export to AI" CTA for the dashboard footer.
// If withTitle is true, it adds a title to the CTA (used in certain dashboard states).
func (w *WealthBuilderLanding) buildExportToAiCta(aiDeeplink *deeplinkPb.Deeplink, withTitle bool) *feNetworthUi.WealthBuilderLandingCta {
	cta := feNetworthUi.NewWealthBuilderLandingCta().
		WithCornerRadius(40).
		WithBorderProperty(1, 40).
		WithIcon(common.TalkToAiIcon, 24, 24).
		WithDeeplink(aiDeeplink).
		WithArrowColour("#015446").
		WithIdentifier("talk_to_ai")
	if withTitle {
		// override Primary State and Icon color as well when Title is visible
		cta = cta.WithTitle(common.AskAi, colors.ColorSnow, commontypes.FontStyle_BUTTON_M).
			WithPrimaryStatus(true).
			WithIcon(common.TalkToAiIconWhite, 24, 24).
			WithBackgroundColor(colors.ColorForest)
	} else {
		cta = cta.WithIcon(common.TalkToAiIcon, 24, 24).
			WithPrimaryStatus(false).
			WithBackgroundColor(colors.ColorCharcoal)
	}
	return cta
}

// buildMagicImportEntryPoint constructs the "Magic Import" CTA for the dashboard action area.
// This CTA is only shown if the magic import feature is enabled for the user.
func buildMagicImportEntryPoint() *feNetworthUi.WidgetV2 {
	return &feNetworthUi.WidgetV2{
		Id:   common.MagicImportEntryPointWidgetId,
		Type: feNetworthUi.WidgetTypeV2_WIDGET_TYPE_V2_WEALTH_BUILDER,
		Params: &feNetworthUi.WidgetV2_WbFeatureEntryPointWidgetParams{
			WbFeatureEntryPointWidgetParams: &feNetworthUi.WbFeatureEntryPointWidgetParams{
				Weight: common.SecondaryWidgetWidth,
				CenterImage: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Lottie_{
						Lottie: &commontypes.VisualElement_Lottie{
							Source: &commontypes.VisualElement_Lottie_Url{
								Url: common.MagicImportHomeScreenLottie,
							},
							RepeatCount: 3,
						},
					},
				},
				Deeplink: GetMagicImportScreen(),
				SduiBorderProperty: &properties.BorderProperty{
					BorderThickness: 1,
					BorderColor:     colors.ColorMoss400,
					CornerRadius:    16,
					IsDash:          true,
					DashLength:      4,
					DashGapLength:   4,
				},
			},
		},
		WidgetAnalyticsPayload: "magic_lens",
	}
}

// IsWeeklyTrackerEnabled checks if the weekly tracker is enabled based on the current day of the week.
func IsWeeklyTrackerEnabled(isFeatureEnabled bool) bool {
	now := time.Now().In(datetime.IST)

	if now.Weekday() == time.Sunday || now.Weekday() == time.Monday {
		return isFeatureEnabled
	}
	return false
}
