// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/dynamicelements/config"
	genconfig2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaximumElements        *syncmap.Map[string, int]
	_ReleaseConfig          *ReleaseConfig
	_QuestSdk               *genconfig.Config
	_AppUpdateBannerConfig  *AppUpdateBannerConfig
	_QuestVariables         *QuestVariables
	_FeatureReleaseConfig   *genconfig2.FeatureReleaseConfig
	_Application            *config.Application
	_Server                 *config.Server
	_Aws                    *cfg.AWS
	_ScreenToServiceMapping map[string][]string
	_PriorityOrder          map[string]map[string][]string
	_Tracing                *cfg.Tracing
	_Profiling              *cfg.Profiling
	_QuestRedisOptions      *cfg.RedisOptions
	_SegmentPriorityConfig  []*config.SegmentPriorityConfigDetails
}

func (obj *Config) MaximumElements() *syncmap.Map[string, int] {
	return obj._MaximumElements
}
func (obj *Config) ReleaseConfig() *ReleaseConfig {
	return obj._ReleaseConfig
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) AppUpdateBannerConfig() *AppUpdateBannerConfig {
	return obj._AppUpdateBannerConfig
}
func (obj *Config) QuestVariables() *QuestVariables {
	return obj._QuestVariables
}
func (obj *Config) FeatureReleaseConfig() *genconfig2.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Aws() *cfg.AWS {
	return obj._Aws
}
func (obj *Config) ScreenToServiceMapping() map[string][]string {
	return obj._ScreenToServiceMapping
}
func (obj *Config) PriorityOrder() map[string]map[string][]string {
	return obj._PriorityOrder
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) SegmentPriorityConfig() []*config.SegmentPriorityConfigDetails {
	return obj._SegmentPriorityConfig
}

type ReleaseConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// to limit Dynamic Elements based on platform or user group
	_IsRestrictedReleaseEnabled uint32
	// if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
	// to enable disable Dynamic Elements for app platform
	_IsEnabledForPlatform *syncmap.Map[string, bool]
	// to enable disable Dynamic Elements on app for INTERNAL users
	_IsEnabledForUserGroup *syncmap.Map[string, bool]
}

// to limit Dynamic Elements based on platform or user group
func (obj *ReleaseConfig) IsRestrictedReleaseEnabled() bool {
	if atomic.LoadUint32(&obj._IsRestrictedReleaseEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
// to enable disable Dynamic Elements for app platform
func (obj *ReleaseConfig) IsEnabledForPlatform() *syncmap.Map[string, bool] {
	return obj._IsEnabledForPlatform
}

// to enable disable Dynamic Elements on app for INTERNAL users
func (obj *ReleaseConfig) IsEnabledForUserGroup() *syncmap.Map[string, bool] {
	return obj._IsEnabledForUserGroup
}

type AppUpdateBannerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Android bump Version we want to show App Update banner to
	// if client_version < this_bump_version we will show this App Update Banner
	_AndroidBumpVersion int32
	// Ios bump Version we want to show App Update banner to
	_IosBumpVersion int32
	// Min bump version allowed to set in Android as client changes are there after this version
	_MinSupportVersionAndroid int32
	// Min bump version allowed to set in Ios as client changes are there after this version
	_MinSupportVersionIos int32
	// min app version android of flexible app update
	_MinSupportVersionAndroidForFlexibleAppUpdate int32
	// min app version ios of flexible app update
	_MinSupportVersionIosForFlexibleAppUpdate int32
	// external redirection url of play store link
	_ExternalRedirectionUrl      string
	_ExternalRedirectionUrlMutex *sync.RWMutex
}

// Android bump Version we want to show App Update banner to
// if client_version < this_bump_version we will show this App Update Banner
func (obj *AppUpdateBannerConfig) AndroidBumpVersion() int32 {
	return int32(atomic.LoadInt32(&obj._AndroidBumpVersion))
}

// Ios bump Version we want to show App Update banner to
func (obj *AppUpdateBannerConfig) IosBumpVersion() int32 {
	return int32(atomic.LoadInt32(&obj._IosBumpVersion))
}

// Min bump version allowed to set in Android as client changes are there after this version
func (obj *AppUpdateBannerConfig) MinSupportVersionAndroid() int32 {
	return int32(atomic.LoadInt32(&obj._MinSupportVersionAndroid))
}

// Min bump version allowed to set in Ios as client changes are there after this version
func (obj *AppUpdateBannerConfig) MinSupportVersionIos() int32 {
	return int32(atomic.LoadInt32(&obj._MinSupportVersionIos))
}

// min app version android of flexible app update
func (obj *AppUpdateBannerConfig) MinSupportVersionAndroidForFlexibleAppUpdate() int32 {
	return int32(atomic.LoadInt32(&obj._MinSupportVersionAndroidForFlexibleAppUpdate))
}

// min app version ios of flexible app update
func (obj *AppUpdateBannerConfig) MinSupportVersionIosForFlexibleAppUpdate() int32 {
	return int32(atomic.LoadInt32(&obj._MinSupportVersionIosForFlexibleAppUpdate))
}

// external redirection url of play store link
func (obj *AppUpdateBannerConfig) ExternalRedirectionUrl() string {
	obj._ExternalRedirectionUrlMutex.RLock()
	defer obj._ExternalRedirectionUrlMutex.RUnlock()
	return obj._ExternalRedirectionUrl
}

type QuestVariables struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_GTMPopUp      uint32
	_HomeBanner    uint32
}

func (obj *QuestVariables) gTMPopUp() bool {
	if atomic.LoadUint32(&obj._GTMPopUp) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *QuestVariables) GTMPopUp(ctx context.Context) bool {
	defVal := obj.gTMPopUp()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "GTMPopUp"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *QuestVariables) homeBanner() bool {
	if atomic.LoadUint32(&obj._HomeBanner) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *QuestVariables) HomeBanner(ctx context.Context) bool {
	defVal := obj.homeBanner()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "HomeBanner"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._MaximumElements = &syncmap.Map[string, int]{}
	_setters["maximumelements"] = _obj.SetMaximumElements
	_ReleaseConfig, _fieldSetters := NewReleaseConfig()
	_obj._ReleaseConfig = _ReleaseConfig
	helper.AddFieldSetters("releaseconfig", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_AppUpdateBannerConfig, _fieldSetters := NewAppUpdateBannerConfig()
	_obj._AppUpdateBannerConfig = _AppUpdateBannerConfig
	helper.AddFieldSetters("appupdatebannerconfig", _fieldSetters, _setters)
	_QuestVariables, _fieldSetters := NewQuestVariables()
	_obj._QuestVariables = _QuestVariables
	helper.AddFieldSetters("questvariables", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._MaximumElements = &syncmap.Map[string, int]{}
	_setters["maximumelements"] = _obj.SetMaximumElements
	_ReleaseConfig, _fieldSetters := NewReleaseConfig()
	_obj._ReleaseConfig = _ReleaseConfig
	helper.AddFieldSetters("releaseconfig", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_AppUpdateBannerConfig, _fieldSetters := NewAppUpdateBannerConfig()
	_obj._AppUpdateBannerConfig = _AppUpdateBannerConfig
	helper.AddFieldSetters("appupdatebannerconfig", _fieldSetters, _setters)
	_QuestVariables, _fieldSetters := NewQuestVariablesWithQuest(questFieldPath + "/" + "QuestVariables")
	_obj._QuestVariables = _QuestVariables
	helper.AddFieldSetters("questvariables", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj._QuestVariables.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	childVars, childVarsErr = obj._QuestVariables.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "Home" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maximumelements":
		return obj.SetMaximumElements(v.MaximumElements, true, path)
	case "releaseconfig":
		return obj._ReleaseConfig.Set(v.ReleaseConfig, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "appupdatebannerconfig":
		return obj._AppUpdateBannerConfig.Set(v.AppUpdateBannerConfig, true, path)
	case "questvariables":
		return obj._QuestVariables.Set(v.QuestVariables, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetMaximumElements(v.MaximumElements, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ReleaseConfig.Set(v.ReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AppUpdateBannerConfig.Set(v.AppUpdateBannerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestVariables.Set(v.QuestVariables, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Aws = v.Aws
	obj._ScreenToServiceMapping = v.ScreenToServiceMapping
	obj._PriorityOrder = v.PriorityOrder
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._SegmentPriorityConfig = v.SegmentPriorityConfig
	return nil
}

func (obj *Config) SetMaximumElements(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MaximumElements", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._MaximumElements, v, path)
}

func NewReleaseConfig() (_obj *ReleaseConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ReleaseConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isrestrictedreleaseenabled"] = _obj.SetIsRestrictedReleaseEnabled

	_obj._IsEnabledForPlatform = &syncmap.Map[string, bool]{}
	_setters["isenabledforplatform"] = _obj.SetIsEnabledForPlatform

	_obj._IsEnabledForUserGroup = &syncmap.Map[string, bool]{}
	_setters["isenabledforusergroup"] = _obj.SetIsEnabledForUserGroup
	return _obj, _setters
}

func (obj *ReleaseConfig) Init() {
	newObj, _ := NewReleaseConfig()
	*obj = *newObj
}

func (obj *ReleaseConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReleaseConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReleaseConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReleaseConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReleaseConfig) setDynamicField(v *config.ReleaseConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isrestrictedreleaseenabled":
		return obj.SetIsRestrictedReleaseEnabled(v.IsRestrictedReleaseEnabled, true, nil)
	case "isenabledforplatform":
		return obj.SetIsEnabledForPlatform(v.IsEnabledForPlatform, true, path)
	case "isenabledforusergroup":
		return obj.SetIsEnabledForUserGroup(v.IsEnabledForUserGroup, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReleaseConfig) setDynamicFields(v *config.ReleaseConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsRestrictedReleaseEnabled(v.IsRestrictedReleaseEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabledForPlatform(v.IsEnabledForPlatform, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabledForUserGroup(v.IsEnabledForUserGroup, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReleaseConfig) setStaticFields(v *config.ReleaseConfig) error {

	return nil
}

func (obj *ReleaseConfig) SetIsRestrictedReleaseEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReleaseConfig.IsRestrictedReleaseEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRestrictedReleaseEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsRestrictedReleaseEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRestrictedReleaseEnabled")
	}
	return nil
}
func (obj *ReleaseConfig) SetIsEnabledForPlatform(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReleaseConfig.IsEnabledForPlatform", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._IsEnabledForPlatform, v, path)
}
func (obj *ReleaseConfig) SetIsEnabledForUserGroup(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReleaseConfig.IsEnabledForUserGroup", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._IsEnabledForUserGroup, v, path)
}

func NewAppUpdateBannerConfig() (_obj *AppUpdateBannerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AppUpdateBannerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["androidbumpversion"] = _obj.SetAndroidBumpVersion
	_setters["iosbumpversion"] = _obj.SetIosBumpVersion
	_setters["minsupportversionandroid"] = _obj.SetMinSupportVersionAndroid
	_setters["minsupportversionios"] = _obj.SetMinSupportVersionIos
	_setters["minsupportversionandroidforflexibleappupdate"] = _obj.SetMinSupportVersionAndroidForFlexibleAppUpdate
	_setters["minsupportversioniosforflexibleappupdate"] = _obj.SetMinSupportVersionIosForFlexibleAppUpdate
	_setters["externalredirectionurl"] = _obj.SetExternalRedirectionUrl
	_obj._ExternalRedirectionUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AppUpdateBannerConfig) Init() {
	newObj, _ := NewAppUpdateBannerConfig()
	*obj = *newObj
}

func (obj *AppUpdateBannerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AppUpdateBannerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AppUpdateBannerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AppUpdateBannerConfig) setDynamicField(v *config.AppUpdateBannerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "androidbumpversion":
		return obj.SetAndroidBumpVersion(v.AndroidBumpVersion, true, nil)
	case "iosbumpversion":
		return obj.SetIosBumpVersion(v.IosBumpVersion, true, nil)
	case "minsupportversionandroid":
		return obj.SetMinSupportVersionAndroid(v.MinSupportVersionAndroid, true, nil)
	case "minsupportversionios":
		return obj.SetMinSupportVersionIos(v.MinSupportVersionIos, true, nil)
	case "minsupportversionandroidforflexibleappupdate":
		return obj.SetMinSupportVersionAndroidForFlexibleAppUpdate(v.MinSupportVersionAndroidForFlexibleAppUpdate, true, nil)
	case "minsupportversioniosforflexibleappupdate":
		return obj.SetMinSupportVersionIosForFlexibleAppUpdate(v.MinSupportVersionIosForFlexibleAppUpdate, true, nil)
	case "externalredirectionurl":
		return obj.SetExternalRedirectionUrl(v.ExternalRedirectionUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AppUpdateBannerConfig) setDynamicFields(v *config.AppUpdateBannerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetAndroidBumpVersion(v.AndroidBumpVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIosBumpVersion(v.IosBumpVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinSupportVersionAndroid(v.MinSupportVersionAndroid, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinSupportVersionIos(v.MinSupportVersionIos, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinSupportVersionAndroidForFlexibleAppUpdate(v.MinSupportVersionAndroidForFlexibleAppUpdate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinSupportVersionIosForFlexibleAppUpdate(v.MinSupportVersionIosForFlexibleAppUpdate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExternalRedirectionUrl(v.ExternalRedirectionUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AppUpdateBannerConfig) setStaticFields(v *config.AppUpdateBannerConfig) error {

	return nil
}

func (obj *AppUpdateBannerConfig) SetAndroidBumpVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.AndroidBumpVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AndroidBumpVersion, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AndroidBumpVersion")
	}
	return nil
}
func (obj *AppUpdateBannerConfig) SetIosBumpVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.IosBumpVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._IosBumpVersion, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IosBumpVersion")
	}
	return nil
}
func (obj *AppUpdateBannerConfig) SetMinSupportVersionAndroid(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.MinSupportVersionAndroid", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSupportVersionAndroid, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSupportVersionAndroid")
	}
	return nil
}
func (obj *AppUpdateBannerConfig) SetMinSupportVersionIos(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.MinSupportVersionIos", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSupportVersionIos, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSupportVersionIos")
	}
	return nil
}
func (obj *AppUpdateBannerConfig) SetMinSupportVersionAndroidForFlexibleAppUpdate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.MinSupportVersionAndroidForFlexibleAppUpdate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSupportVersionAndroidForFlexibleAppUpdate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSupportVersionAndroidForFlexibleAppUpdate")
	}
	return nil
}
func (obj *AppUpdateBannerConfig) SetMinSupportVersionIosForFlexibleAppUpdate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.MinSupportVersionIosForFlexibleAppUpdate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSupportVersionIosForFlexibleAppUpdate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSupportVersionIosForFlexibleAppUpdate")
	}
	return nil
}
func (obj *AppUpdateBannerConfig) SetExternalRedirectionUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppUpdateBannerConfig.ExternalRedirectionUrl", reflect.TypeOf(val))
	}
	obj._ExternalRedirectionUrlMutex.Lock()
	defer obj._ExternalRedirectionUrlMutex.Unlock()
	obj._ExternalRedirectionUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExternalRedirectionUrl")
	}
	return nil
}

func NewQuestVariables() (_obj *QuestVariables, _setters map[string]dynconf.SetFunc) {
	_obj = &QuestVariables{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["gtmpopup"] = _obj.SetGTMPopUp
	_setters["homebanner"] = _obj.SetHomeBanner
	return _obj, _setters
}

func NewQuestVariablesWithQuest(questFieldPath string) (_obj *QuestVariables, _setters map[string]dynconf.SetFunc) {
	_obj = &QuestVariables{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["gtmpopup"] = _obj.SetGTMPopUp
	_setters["homebanner"] = _obj.SetHomeBanner
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *QuestVariables) Init(questFieldPath string) {
	newObj, _ := NewQuestVariables()
	*obj = *newObj
}
func (obj *QuestVariables) InitWithQuest(questFieldPath string) {
	newObj, _ := NewQuestVariablesWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *QuestVariables) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *QuestVariables) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *QuestVariables) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "GTMPopUp",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "HomeBanner",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *QuestVariables) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.QuestVariables)
	if !ok {
		return fmt.Errorf("invalid data type %v *QuestVariables", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *QuestVariables) setDynamicField(v *config.QuestVariables, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "gtmpopup":
		return obj.SetGTMPopUp(v.GTMPopUp, true, nil)
	case "homebanner":
		return obj.SetHomeBanner(v.HomeBanner, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *QuestVariables) setDynamicFields(v *config.QuestVariables, dynamic bool, path []string) (err error) {

	err = obj.SetGTMPopUp(v.GTMPopUp, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHomeBanner(v.HomeBanner, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *QuestVariables) setStaticFields(v *config.QuestVariables) error {

	return nil
}

func (obj *QuestVariables) SetGTMPopUp(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *QuestVariables.GTMPopUp", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._GTMPopUp, 1)
	} else {
		atomic.StoreUint32(&obj._GTMPopUp, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "GTMPopUp")
	}
	return nil
}
func (obj *QuestVariables) SetHomeBanner(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *QuestVariables.HomeBanner", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._HomeBanner, 1)
	} else {
		atomic.StoreUint32(&obj._HomeBanner, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "HomeBanner")
	}
	return nil
}
