package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"

	"github.com/knadh/koanf"

	"github.com/epifi/be-common/pkg/cfg"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"

	"github.com/pkg/errors"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
	kConf      *koanf.Koanf
)

func Load() (*Config, error) {
	once.Do(func() {
		config, kConf, err = loadConfig(false)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, _, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}
	return config, nil
}

func GetLocalKConf() *koanf.Koanf {
	return kConf
}

func loadConfig(onlyStaticFiles bool) (*Config, *koanf.Koanf, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.DYNAMIC_ELEMENTS_SERVICE)

	if err != nil {
		return nil, nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}
	if onlyStaticFiles {
		return conf, k, nil
	}
	err = readAndSetEnv(conf)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to read and set env")
	}

	return conf, k, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}
	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return errors.Wrap(err, "failed to convert port to int")
		}
		c.Server.Port = intVal
	}
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/dynamicelements/config Config
type Config struct {
	Application            *Application
	Server                 *Server
	Aws                    *cfg.AWS
	ScreenToServiceMapping map[string][]string
	PriorityOrder          map[string]map[string][]string
	MaximumElements        map[string]int `dynamic:"true"`
	ReleaseConfig          *ReleaseConfig `dynamic:"true"`
	Tracing                *cfg.Tracing
	Profiling              *cfg.Profiling
	QuestSdk               *sdkconfig.Config `dynamic:"true"`
	QuestRedisOptions      *cfg.RedisOptions
	AppUpdateBannerConfig  *AppUpdateBannerConfig `dynamic:"true"`
	// quest variables for running experiments on different variants of dynamic elements. Eg: GTM pop up, home banners
	QuestVariables        *QuestVariables `dynamic:"true" ,quest:"component,area:Home"`
	SegmentPriorityConfig []*SegmentPriorityConfigDetails
	FeatureReleaseConfig  *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
}

type AppUpdateBannerConfig struct {
	// Android bump Version we want to show App Update banner to
	// if client_version < this_bump_version we will show this App Update Banner
	AndroidBumpVersion int32 `dynamic:"true"`
	// Ios bump Version we want to show App Update banner to
	IosBumpVersion int32 `dynamic:"true"`
	// Min bump version allowed to set in Android as client changes are there after this version
	MinSupportVersionAndroid int32 `dynamic:"true"`
	// Min bump version allowed to set in Ios as client changes are there after this version
	MinSupportVersionIos int32 `dynamic:"true"`
	// external redirection url of play store link
	ExternalRedirectionUrl string `dynamic:"true"`
	// min app version android of flexible app update
	MinSupportVersionAndroidForFlexibleAppUpdate int32 `dynamic:"true"`
	// min app version ios of flexible app update
	MinSupportVersionIosForFlexibleAppUpdate int32 `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Port            int
	HealthCheckPort int
	EnablePoller    bool
}

type ReleaseConfig struct {
	// to limit Dynamic Elements based on platform or user group
	IsRestrictedReleaseEnabled bool `dynamic:"true"`
	// if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
	// to enable disable Dynamic Elements for app platform
	IsEnabledForPlatform map[string]bool `dynamic:"true"`
	// to enable disable Dynamic Elements on app for INTERNAL users
	IsEnabledForUserGroup map[string]bool `dynamic:"true"`
}

type QuestVariables struct {
	GTMPopUp   bool `dynamic:"true" ,quest:"variable"`
	HomeBanner bool `dynamic:"true" ,quest:"variable"`
}

type SegmentPriorityConfigDetails struct {
	ServiceName         string `dynamic:"true"`
	SegmentIdExpression string `dynamic:"true"`
}
