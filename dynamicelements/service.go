package dynamicelements

import (
	"context"
	"fmt"
	"sync"
	"time"

	"k8s.io/apimachinery/pkg/util/waitgroup"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	waitgrp "github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/gamma/api/tiering/external"

	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	"github.com/samber/lo"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/dynamicelements/collector"
	"github.com/epifi/gamma/dynamicelements/config"
	"github.com/epifi/gamma/dynamicelements/config/genconf"
	"github.com/epifi/gamma/dynamicelements/constant"
	"github.com/epifi/gamma/dynamicelements/sort"
	"github.com/epifi/gamma/pkg/feature/release"
	questSdk "github.com/epifi/gamma/quest/sdk"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const (
	ExperimentId = "experiment_id"
	VariantId    = "variant_id"
)

type DynamicElementsService struct {
	dePb.UnimplementedDynamicElementsServer
	collectorFactory       collector.IDynamicElementsCollectorFactory
	sortingStrategyFactory *sort.SortingStrategyFactory
	conf                   *config.Config
	dynConf                *genconf.Config
	actorClient            actorPb.ActorClient
	userClient             userPb.UsersClient
	userGroupClient        userGroupPb.GroupClient
	questSdkClient         *questSdk.Client
	onboardingClient       onboardingPb.OnboardingClient
	beTieringClient        beTieringPb.TieringClient
	releaseEvaluator       release.IEvaluator
}

func NewDynamicElementsService(collectorFactory collector.IDynamicElementsCollectorFactory, sortingStrategyFactory *sort.SortingStrategyFactory, conf *config.Config, dynConf *genconf.Config, actorClient actorPb.ActorClient,
	userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient, questSdkClient *questSdk.Client, onboardingClient onboardingPb.OnboardingClient, beTieringClient beTieringPb.TieringClient, releaseEvaluator release.IEvaluator) *DynamicElementsService {
	return &DynamicElementsService{
		collectorFactory:       collectorFactory,
		sortingStrategyFactory: sortingStrategyFactory,
		conf:                   conf,
		dynConf:                dynConf,
		actorClient:            actorClient,
		userClient:             userClient,
		userGroupClient:        userGroupClient,
		questSdkClient:         questSdkClient,
		onboardingClient:       onboardingClient,
		beTieringClient:        beTieringClient,
		releaseEvaluator:       releaseEvaluator,
	}
}

var _ dePb.DynamicElementsServer = &DynamicElementsService{}

var DynamicElementsDisabledForActorErr = errors.New("Dynamic elements is not enabled for the user")
var DynamicElementsDisabledForPlatformErr = errors.New("Dynamic elements is not enabled for platform")

//nolint:funlen
func (s *DynamicElementsService) FetchDynamicElementsFromAllServices(ctx context.Context, request *dePb.FetchDynamicElementsFromAllServicesRequest) (*dePb.FetchDynamicElementsFromAllServicesResponse, error) {
	screenName := request.GetClientContext().GetScreenName()
	if screenName == deeplinkPb.Screen_DEEP_LINK_URI_UNSPECIFIED {
		logger.Info(ctx, "mandatory param screen name is unspecified in request")
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory param screen name is unspecified"),
		}, nil
	}
	actorId := epificontext.ActorIdFromContext(ctx)
	maximumElementCount, err := s.getMaxElementCountFromConfig(request.GetClientContext())
	if err != nil {
		logger.Error(ctx, "could not read max elements count from config", zap.Error(err))
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("could not read max elements count from config"),
		}, nil
	}
	// check if the screen has any space to show dynamic elements.
	// Useful to disable this service GRPC on a screen.
	if maximumElementCount == 0 {
		logger.Error(ctx, "Dynamic elements disabled for this screen", zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.SCREEN, screenName.String()))
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	// Check for controlled release conditions.
	restrictErr := s.CheckReleaseRestrictions(ctx, actorId)
	if restrictErr != nil {
		logger.Error(ctx, "Dynamic elements restricted for this user / app platform", zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.PLATFORM, epificontext.AppPlatformFromContext(ctx).String()), zap.String(logger.SCREEN, screenName.String()), zap.Error(restrictErr))
		if errors.Is(restrictErr, DynamicElementsDisabledForPlatformErr) || errors.Is(restrictErr, DynamicElementsDisabledForActorErr) {
			return &dePb.FetchDynamicElementsFromAllServicesResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while evaluating restrictions on dynamic elements service usage"),
		}, nil
	}
	// Retrieve list of services which need to be invoked for this screen. This info is stored in config.
	serviceList, err := s.GetServicesForScreenFromConfig(ctx, request.GetClientContext())
	if err != nil {
		logger.Error(ctx, "could not read service names for the screen from config", zap.String(logger.SCREEN, screenName.String()), zap.Error(err))
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("could not get services for the screen from config"),
		}, nil
	}

	fetchRequest := buildFetchRequest(ctx, actorId, request)
	// make async calls to each service in serviceList
	var dynamicElementsList []*dePb.DynamicElement
	var wg waitgroup.SafeWaitGroup
	var m sync.Mutex // Add a mutex for synchronization
	for _, service := range serviceList {
		_service := service
		_ = wg.Add(1)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			deList, srvcErr := s.fetchDynamicElementsForService(ctx, _service, fetchRequest)
			if srvcErr != nil {
				logger.Error(ctx, "could not fetch dynamic elements from the BE service",
					zap.String(logger.SERVICE, _service.String()), zap.Error(srvcErr))
				return
			}
			// taking a lock before modifying variables initialized outside goroutine
			m.Lock()
			defer m.Unlock()
			dynamicElementsList = append(dynamicElementsList, deList...)
		})
	}
	wg.Wait()
	if len(dynamicElementsList) == 0 {
		logger.Debug(ctx, "no relevant dynamic elements found", zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.SCREEN, screenName.String()),
			zap.String("home_version", request.GetClientContext().GetHomeInfo().GetVersion().String()),
			zap.String("home_section", request.GetClientContext().GetHomeInfo().GetSection().String()))
		// sending the app update maintenance  banner to the eligible user if no service is returning any banner
		// if the eligibility criteria for the app update banner is not fulfill we send StatusRecordNotFound
		appUpdateBanner := s.checksForAppUpdateBanner(request.GetClientContext())
		switch {
		case appUpdateBanner != nil && request.GetClientContext().GetHomeInfo().GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_TOP_BAR:
			logger.Debug(ctx, "User is eligible for app update banner", zap.String(logger.ACTOR_ID, actorId),
				zap.String(logger.SCREEN, screenName.String()),
				zap.String("home_version", request.GetClientContext().GetHomeInfo().GetVersion().String()),
				zap.String("home_section", request.GetClientContext().GetHomeInfo().GetSection().String()))
			dynamicElementsList = append(dynamicElementsList, appUpdateBanner)
		default:
			return &dePb.FetchDynamicElementsFromAllServicesResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
	}

	// filter out dynamic elements which are not intended for requested client context
	dynamicElementsList = s.filterOutInappropriateDynamicElements(ctx, fetchRequest.GetClientContext(), dynamicElementsList)

	// filter dynamic elements based on quest experiment conditions
	dynamicElementsList, err = s.filterDynamicElements(ctx, actorId, dynamicElementsList)
	if err != nil {
		logger.Error(ctx, "error in filtering dynamic elements based on quest conditions", zap.Error(err))
	}

	sortingStrategyContext, err := s.getSortingStrategyContext(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while getting sorting strategy context", zap.Error(err))
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while getting sorting strategy context"),
		}, nil
	}

	sortingStrategy := s.sortingStrategyFactory.GetSortingStrategy(ctx, screenName, fetchRequest.GetClientContext(), sortingStrategyContext, actorId)
	dynamicElementsList, err = sortingStrategy.Sort(ctx, actorId, screenName, request.GetSessionId(), dynamicElementsList)
	if err != nil {
		logger.Error(ctx, "unable to sort dynamic elements according to strategy", zap.String(logger.SCREEN, screenName.String()), zap.Error(err))
		return &dePb.FetchDynamicElementsFromAllServicesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to sort dynamic elements according to strategy"),
		}, nil
	}

	respListLen := integer.Min(maximumElementCount, len(dynamicElementsList))
	// Return only top x elements based on space availability in the screen.
	return &dePb.FetchDynamicElementsFromAllServicesResponse{
		Status:       rpcPb.StatusOk(),
		ElementsList: dynamicElementsList[0:respListLen],
	}, nil
}

func (s *DynamicElementsService) getSortingStrategyContext(ctx context.Context, actorId string) (*sort.SortingStrategyContext, error) {
	var (
		isNonB2BBaseTierUser, isB2BVerified, isAssumedB2BVerified bool
		currentTier                                               external.Tier
		userOnboardingDetails                                     *onboardingPb.OnboardingDetails
		isFiLiteUser, isSANewUser, isWealthAnalyserUser           bool
	)
	// Gracefully handle errors from all goroutines to ensure the flow continues
	// Any failure in one of the go routines will not affect other goroutines and the respective data will be collected for non failing go routines
	// Eg. If only GetDetails fails, isNonB2BBaseTierUser won't be affected.
	var wg = &sync.WaitGroup{}
	// get details of the user by actor id
	wg.Add(1)
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		defer wg.Done()
		getDetailsResp, getDetailsErr := s.onboardingClient.GetDetails(ctx, &onboardingPb.GetDetailsRequest{
			ActorId:    actorId,
			CachedData: true,
		})
		if rpcErr := epifigrpc.RPCError(getDetailsResp, getDetailsErr); rpcErr != nil {
			logger.Error(ctx, "error getting onboarding details", zap.Error(rpcErr))
			return
		}
		userOnboardingDetails = getDetailsResp.GetDetails()
	})

	// get current tier of the user
	wg.Add(1)
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		defer wg.Done()
		tierResp, getTierErr := s.beTieringClient.GetCurrentTierForActor(ctx, &beTieringPb.GetCurrentTierForActorRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(tierResp, getTierErr); rpcErr != nil {
			logger.Error(ctx, "error fetching current tier for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return
		}
		currentTier = tierResp.GetTier()
	})

	// get b2b verification status for user
	wg.Add(1)
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		defer wg.Done()
		isUserB2BVerified, isUserB2BVerifiedErr := s.userClient.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
			logger.Error(ctx, "failed to get b2b verification status", zap.Error(rpcErr))
			// Due to the error, B2B verified users may incorrectly have their status set to false,
			// which could lead to promo widget to pitch non B2B users being shown to them even though they are actually B2B verified.
			isAssumedB2BVerified = true
			return
		}
		isB2BVerified = isUserB2BVerified.GetIsVerified()
	})
	waitgrp.SafeWaitCtx(ctx, wg)

	// Process userOnboardingDetails after errGrp.Wait() to avoid race condition
	if userOnboardingDetails != nil {
		isFiLiteUser = userOnboardingDetails.GetFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE

		onboardingTime := userOnboardingDetails.GetStageDetails().GetStageMapping()[onboardingPb.OnboardingStage_ACCOUNT_CREATION.String()].GetStartedAt().AsTime()
		daysSinceOnboarding := time.Since(onboardingTime).Hours() / 24
		isSANewUser = daysSinceOnboarding < 28

		isWealthAnalyserUser = userOnboardingDetails.GetFeatureDetails().GetFeatureInfo()[onboardingPb.Feature_FEATURE_WEALTH_ANALYSER.String()].GetFeatureStatus() == onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE
	}

	if currentTier.IsBaseTier() && !isB2BVerified && !isAssumedB2BVerified {
		isNonB2BBaseTierUser = true
	}

	return &sort.SortingStrategyContext{
		IsSANewUser:          isSANewUser,
		IsFiLiteUser:         isFiLiteUser,
		IsWealthAnalyserUser: isWealthAnalyserUser,
		IsNonB2BBaseTierUser: isNonB2BBaseTierUser,
	}, nil
}

func (s *DynamicElementsService) checksForAppUpdateBanner(clientContext *dePb.ClientContext) *dePb.DynamicElement {
	if (clientContext.GetAppPlatform() == commontypes.Platform_ANDROID && clientContext.GetAppVersion() >= s.dynConf.AppUpdateBannerConfig().MinSupportVersionAndroid() && clientContext.GetAppVersion() < s.dynConf.AppUpdateBannerConfig().AndroidBumpVersion()) ||
		(clientContext.GetAppPlatform() == commontypes.Platform_IOS && clientContext.GetAppVersion() >= s.dynConf.AppUpdateBannerConfig().MinSupportVersionIos() && clientContext.GetAppVersion() < s.dynConf.AppUpdateBannerConfig().IosBumpVersion()) {

		deeplinkAction := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions_{
				UpdateAppScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions{
					UpdateType: deeplinkPb.Deeplink_UpdateAppScreenOptions_UPDATE_TYPE_FLEXIBLE,
				},
			},
		}
		if (clientContext.GetAppPlatform() == commontypes.Platform_ANDROID && clientContext.GetAppVersion() < s.dynConf.AppUpdateBannerConfig().MinSupportVersionAndroidForFlexibleAppUpdate()) ||
			(clientContext.GetAppPlatform() == commontypes.Platform_IOS && clientContext.GetAppVersion() < s.dynConf.AppUpdateBannerConfig().MinSupportVersionIosForFlexibleAppUpdate()) {
			deeplinkAction = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
						ExternalUrl: s.dynConf.AppUpdateBannerConfig().ExternalRedirectionUrl(),
					},
				},
			}
		}

		return &dePb.DynamicElement{
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_BannerV2{
					BannerV2: &dePb.BannerElementContentV2{
						Title: commontypes.GetTextFromStringFontColourFontStyle("New app Version is out for you. Update Now!!", "#FFFFFF", commontypes.FontStyle_SUBTITLE_S),
						BackgroundColor: &typesUi.BackgroundColour{
							Colour: &typesUi.BackgroundColour_BlockColour{
								BlockColour: "#00b899",
							},
						},
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/home/<USER>",
						},
						Deeplink: deeplinkAction,
						CtaList: []*dePb.DynamicElementCta{
							{
								Type: dePb.DynamicElementCta_TYPE_DISMISSED,
								// TODO: Temp. Adding redundant deeplink here to mitigate an Android Cta/Banner click
								//  issue. Clients will still honor Cta Type DISMISSED, when Cta button is clicked.
								//  Remove this post a client fix is out:
								Deeplink: deeplinkAction,
							},
						},
					},
				},
			},
		}
	}
	return nil
}

func buildFetchRequest(ctx context.Context, actorId string, request *dePb.FetchDynamicElementsFromAllServicesRequest) *dePb.FetchDynamicElementsRequest {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	clientContext := request.GetClientContext()
	clientContext.AppPlatform = appPlatform
	clientContext.AppVersion = int32(appVersion)
	return &dePb.FetchDynamicElementsRequest{
		ActorId:       actorId,
		ClientContext: clientContext,
	}
}

// fetchDynamicElementsForService fetches dynamic element from the given BE service
func (s *DynamicElementsService) fetchDynamicElementsForService(ctx context.Context, service types.ServiceName, fetchRequest *dePb.FetchDynamicElementsRequest) ([]*dePb.DynamicElement, error) {
	collectorService, err := s.collectorFactory.GetDynamicElementsCollector(service)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting collector for service")
	}
	// adding timeout for BE services to prevent single service increasing the overall latency
	// Timeout value can be changed per service if needed in future
	ctxWithTimeout, ctxCancel := context.WithTimeout(ctx, 2*time.Second)
	defer ctxCancel()
	serviceResp, err := collectorService.FetchDynamicElements(ctxWithTimeout, fetchRequest)
	if te := epifigrpc.RPCError(serviceResp, err); te != nil {
		return nil, errors.Wrap(err, "unable to fetch dynamic elements for service")
	}
	return serviceResp.GetElementsList(), nil
}

func (s *DynamicElementsService) DynamicElementCallbackToService(ctx context.Context, request *dePb.DynamicElementCallbackToServiceRequest) (*dePb.DynamicElementCallbackToServiceResponse, error) {
	// validate request
	service := request.GetService()
	if service == types.ServiceName_SERVICE_UNSPECIFIED {
		logger.Error(ctx, "mandatory param service is unspecified in request")
		return &dePb.DynamicElementCallbackToServiceResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory param service is unspecified"),
		}, nil
	}

	// callback to owner service and return the same response
	ownerService, err := s.collectorFactory.GetDynamicElementsCollector(service)
	if err != nil {
		logger.Error(ctx, "error while getting collector for service", zap.String("service", service.String()), zap.Error(err))
		return &dePb.DynamicElementCallbackToServiceResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while getting collector for service"),
		}, nil
	}
	serviceResp, err := ownerService.DynamicElementCallback(ctx, &dePb.DynamicElementCallbackRequest{
		ActorId:         request.GetActorId(),
		ElementId:       request.GetElementId(),
		CallbackPayload: request.GetCallbackPayload(),
	})
	if te := epifigrpc.RPCError(serviceResp, err); te != nil {
		logger.Error(ctx, "unable to callback owner service", zap.String("service", service.String()), zap.Error(te))
		return &dePb.DynamicElementCallbackToServiceResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to callback owner service"),
		}, nil
	}
	return &dePb.DynamicElementCallbackToServiceResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// GetServicesForScreenFromConfig Returns list of BE services to be invoked for this client context
func (s *DynamicElementsService) GetServicesForScreenFromConfig(ctx context.Context, clientContext *dePb.ClientContext) ([]types.ServiceName, error) {
	screenSectionId := s.getScreenSectionIdentifier(clientContext)
	serviceNameList, ok := s.dynConf.ScreenToServiceMapping()[screenSectionId]
	if !ok {
		// if screen section is not found, we search for screen only
		screenName := clientContext.GetScreenName().String()
		serviceNameList, ok = s.dynConf.ScreenToServiceMapping()[screenName]
		if !ok {
			// if screen name is valid but not found in config, return default list
			serviceNameList, ok = s.dynConf.ScreenToServiceMapping()[constant.Default]
			if !ok {
				logger.Error(ctx, "failed to get screen to service mapping from config")
				return nil, errors.New("cannot get screen to service mapping from config")
			}
		}
	}
	var serviceNameEnumList []types.ServiceName
	for _, serviceName := range serviceNameList {
		serviceNameEnum, ok := types.ServiceName_value[serviceName]
		if !ok {
			logger.Info(ctx, "invalid service name read from config", zap.String("Service", serviceName))
			continue
		}
		serviceNameEnumList = append(serviceNameEnumList, types.ServiceName(serviceNameEnum))
	}
	return serviceNameEnumList, nil
}

func (s *DynamicElementsService) getMaxElementCountFromConfig(clientContext *dePb.ClientContext) (int, error) {
	screenSectionId := s.getScreenSectionIdentifier(clientContext)
	maximumElementCount := s.dynConf.MaximumElements().Get(screenSectionId)
	if maximumElementCount == 0 {
		// if screen section is not found, we search for screen only
		maximumElementCount = s.dynConf.MaximumElements().Get(clientContext.GetScreenName().String())
		if maximumElementCount == 0 {
			// use default count for the given screen name is not found in config
			maximumElementCount = s.dynConf.MaximumElements().Get(constant.Default)
			if maximumElementCount == 0 {
				return 0, errors.New("could not read max elements count from config")
			}
		}
	}
	return maximumElementCount, nil
}

func (s *DynamicElementsService) getScreenSectionIdentifier(clientContext *dePb.ClientContext) string {
	switch clientContext.GetScreenName() {
	case deeplinkPb.Screen_HOME:
		return fmt.Sprintf("%s_%s", clientContext.GetScreenName().String(), clientContext.GetHomeInfo().GetSection().String())
	default:
		return ""
	}
}

func (s *DynamicElementsService) CheckReleaseRestrictions(ctx context.Context, actorId string) error {
	if s.dynConf.ReleaseConfig() == nil {
		return nil
	}
	// check if restricted release is enabled or not
	if !s.dynConf.ReleaseConfig().IsRestrictedReleaseEnabled() {
		return nil
	}
	// check if dynamic elements is enabled for the app platform
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	if appPlatform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		return errors.New("app platform unspecified")
	}
	if !s.dynConf.ReleaseConfig().IsEnabledForPlatform().Get(appPlatform.String()) {
		return DynamicElementsDisabledForPlatformErr
	}
	// get the list of user groups which this user belongs to
	userGroupList, err := s.GetUserGroupsForActor(ctx, actorId)
	if err != nil {
		return errors.Wrap(err, "error while identifying user group for actor")
	}
	// check if the user belongs to one of enabled user group categories in config
	for _, group := range userGroupList {
		isEnabled := !s.dynConf.ReleaseConfig().IsEnabledForUserGroup().Get(group.String())
		if isEnabled {
			return nil
		}
	}
	return DynamicElementsDisabledForActorErr
}

func (s *DynamicElementsService) GetUserGroupsForActor(ctx context.Context, actorId string) ([]commontypes.UserGroup, error) {
	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		return nil, errors.Wrap(err, "error while fetching entity details using actor id")
	}
	userRes, userErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{Id: entityRes.GetEntityId()},
	})
	if err := epifigrpc.RPCError(userRes, userErr); err != nil {
		return nil, errors.Wrap(err, "error while fetching user details")
	}
	userGrpRes, userGrpErr := s.userGroupClient.GetGroupsMappedToEmail(ctx,
		&userGroupPb.GetGroupsMappedToEmailRequest{Email: userRes.GetUser().GetProfile().GetEmail()})
	if err := epifigrpc.RPCError(userGrpRes, userGrpErr); err != nil {
		return nil, errors.Wrap(err, "error while fetching user group details")
	}
	return userGrpRes.GetGroups(), nil
}

// filterDynamicElements filter dynamic elements based on quest experimental conditions
func (s *DynamicElementsService) filterDynamicElements(ctx context.Context, actorId string, dynamicElementsList []*dePb.DynamicElement) ([]*dePb.DynamicElement, error) {
	var filteredDynamicElements []*dePb.DynamicElement
	// filter dynamic elements which are in quest experiment setup with experiment id as key
	questDynamicElements := make(map[string][]*dePb.DynamicElement, 0)

	for _, dynamicElement := range dynamicElementsList {
		expId, ok := dynamicElement.GetBizAnalyticsData()[ExperimentId]
		if ok && expId != "" {
			// if experiment id key exists and id is not empty,
			// then append to quest dynamic elements list to filter based on experiments
			questDynamicElements[expId] = append(questDynamicElements[expId], dynamicElement)
		} else {
			filteredDynamicElements = append(filteredDynamicElements, dynamicElement)
		}
	}

	for expId, dynElements := range questDynamicElements {
		// check if actor is part of the configured experiment.
		variantEvaluated, err := s.questSdkClient.GetVariantEvaluatedForActor(ctx, s.userClient, expId, actorId)
		if err != nil {
			logger.Error(ctx, "error while fetching variant for actor", zap.String("actorId", actorId), zap.Any("dynamicElement", dynElements), zap.Error(err))
			return filteredDynamicElements, err
		}
		if variantEvaluated != nil {
			for _, dynElement := range dynElements {
				variantId, ok := dynElement.GetBizAnalyticsData()[VariantId]
				if !ok || variantId == "" {
					continue
				}
				// check if user is in that evaluated experiment variant or not
				if variantEvaluated.GetId() == variantId {
					// if both variant ids matches append to original dynamic elements list
					filteredDynamicElements = append(filteredDynamicElements, dynElement)
					// no need to check for further variants as already user is in this variant
					// user can't be in 2 or more variants in an experiment
					break
				}
			}
		}
	}
	return filteredDynamicElements, nil
}

// filterOutInappropriateDynamicElements filters out all the inappropriate dynamic elements which are not suitable for requested client context
func (s *DynamicElementsService) filterOutInappropriateDynamicElements(ctx context.Context, clientContext *dePb.ClientContext, dynamicElementsList []*dePb.DynamicElement) []*dePb.DynamicElement {
	return lo.Filter(dynamicElementsList, func(element *dePb.DynamicElement, _ int) bool {
		return s.isDynamicElementSuitableForClientContext(ctx, clientContext, element)
	})
}

// isDynamicElementSuitableForClientContext checks if the given dynamic element is suitable for the requested client context
func (s *DynamicElementsService) isDynamicElementSuitableForClientContext(ctx context.Context, clientContext *dePb.ClientContext, element *dePb.DynamicElement) bool {
	if clientContext.GetScreenName() == deeplinkPb.Screen_HOME {
		switch clientContext.GetHomeInfo().GetSection() {
		case dePb.HomeScreenAdditionalInfo_SECTION_TOP_BAR:
			if !lo.Contains([]dePb.ElementStructureType{dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER, dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2}, element.GetStructureType()) {
				logger.Error(ctx, "element structure type not supported for top bar section", zap.String("structureType", element.GetStructureType().String()), zap.String("ownerService", element.GetOwnerService().String()))
				return false
			}
		case dePb.HomeScreenAdditionalInfo_SECTION_BODY:
			if !lo.Contains([]dePb.ElementStructureType{dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER, dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2}, element.GetStructureType()) {
				logger.Error(ctx, "element structure type not supported for body section", zap.String("structureType", element.GetStructureType().String()), zap.String("ownerService", element.GetOwnerService().String()))
				return false
			}
		case dePb.HomeScreenAdditionalInfo_SECTION_BODY2:
			if element.GetStructureType() != dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE {
				logger.Error(ctx, "element structure type not supported for body2 section", zap.String("structureType", element.GetStructureType().String()), zap.String("ownerService", element.GetOwnerService().String()))
				return false
			}
		case dePb.HomeScreenAdditionalInfo_SECTION_GTM_POPUP:
			if element.GetStructureType() != dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_GTM_POP_UP {
				logger.Error(ctx, "element structure type not supported for popup section", zap.String("structureType", element.GetStructureType().String()), zap.String("ownerService", element.GetOwnerService().String()))
				return false
			}
		case dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY, dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY:
			if !lo.Contains([]dePb.ElementStructureType{dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_TWO_POINTS, dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS, dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
				dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD}, element.GetStructureType()) {
				logger.Error(ctx, "element structure type not supported for primary or secondary section", zap.String("structureType", element.GetStructureType().String()), zap.String("ownerService", element.GetOwnerService().String()))
				return false
			}
		case dePb.HomeScreenAdditionalInfo_SECTION_TABBED_CARD:
			if element.GetStructureType() != dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD {
				logger.Error(ctx, "element structure type not supported for tabbed card section", zap.String("structureType", element.GetStructureType().String()), zap.String("ownerService", element.GetOwnerService().String()))
				return false
			}
		}
	}
	return true
}
