Application:
  Environment: "staging"
  Name: "bre"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

AWS:
  Region: "ap-south-1"

Tracing:
  Enable: true

Apis:
  LoanDecisioning:
    Url: "https://simulator.staging.pointz.in:9091/bre/decision"
    AuthToken: "Bearer tIis1v3sBYJWivJIvfx5OdVV_itmh4UxbU12v9Xwy8OYWZtbEtCUkVRDgLDawVOKbPkKtrmczNIjDznFzeFzCg=="
  LoanPreScreening:
    Url: "https://simulator.staging.pointz.in:9091/bre/prescreening"
    AuthToken: "Bearer qdMoFeOyCS6ZxHdNZgirHboCs-Wf2LKxBYtaQo1ahSnUbZ5AK79DeGKpkqbYFI9KFTAbavL0RLb8FkPAWT8xCA=="
  LoanDecisioningRealtimeSubvention:
    Url: "https://simulator.staging.pointz.in:9091/bre/decision"
    AuthToken: "Bearer qdMoFeOyCS6ZxHdNZgirHboCs-Wf2LKxBYtaQo1ahSnUbZ5AK79DeGKpkqbYFI9KFTAbavL0RLb8FkPAWT8xCA=="
  LoanPreScreeningNonFiCore:
    Url: "https://simulator.staging.pointz.in:9091/bre/prescreening"
  LoanDecisioningNonFiCore:
    Url: "https://simulator.staging.pointz.in:9091/bre/decision"
  LoanPreBre:
    Url: "https://simulator.staging.pointz.in:9091/bre/prebre"
  LoanFinalBre:
    Url: "https://simulator.staging.pointz.in:9091/bre/finalbre"
  LoanPreBreOffer:
    Url: "https://simulator.staging.pointz.in:9091/bre/prebreoffer"
  LoanFinalBreV2:
    Url: "https://simulator.staging.pointz.in:9091/bre/finalbre"

RawInhouseBreBucketName: "epifi-raw-dev"
InhouseBreBucketName: "epifi-staging-cc-inhouse-bre-responses"

InHouseBreConfig:
  InhouseBreRawS3BucketName: "epifi-raw-dev"
  InhouseBreS3BucketName: "epifi-staging-cc-inhouse-bre-responses"
  InhouseBreRawBucketS3FilePath: "qa/data/vendor/bre_scienaptic/cc_bre_output/%s/%s-inhouseBreOutputs.csv"
  InhouseBreS3FilePath: "cc/inhouse/bre/%s-inhouseBreResponses.csv"

Secrets:
  Ids:
    BreCredentials: "staging/lending/bre-credentials"
