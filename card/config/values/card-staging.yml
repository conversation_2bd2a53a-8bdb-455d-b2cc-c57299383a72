Application:
  Environment: "staging"
  Name: "card"

Server:
  Ports:
    GrpcPort: 8093
    GrpcSecurePort: 9504
    HttpPort: 9999
    HttpPProfPort: 9990


DebitCardPgDb:
  Username: "debit_card_pgdb_dev_user"
  Password: ""
  Name: "debit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "card"
  SecretName: "staging/rds/epifimetis/debit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiDb:
  AppName: "card"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  DbType: "CRDB"
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "debit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "card"
          StatementTimeout: 1m
          Name: "debit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "staging/rds/epifimetis/debit_card_pgdb_dev_user"

# TODO(chandres): rename this at resources level as well -> sftp, consumer, s3buckets-policies, upload path
CardsDispatchedCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-dc-cards-sent-for-printing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-process-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CreationCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-creation-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CreationPublisher:
  QueueName: "staging-card-process-creation-queue"

PiCreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-pi-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

PiCreationPublisher:
  QueueName: "staging-card-pi-creation-queue"

PinSetEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-pin-set-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

PinSetEventPublisher:
  QueueName: "staging-card-pin-set-event-queue"

RenewCardSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-renew-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

RenewCardPublisher:
  QueueName: "staging-card-renew-queue"

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false
  SkipControlValidation: true
  EnableCardSuccessNotification: true
  EnableEcommNotificationFlag: true
  EnableSMSFromEpifi: true
  EnableOnbEmail: true
  EnableCardDeliveryInAppNotif: false
  EnableCardDeliveryDelayNotif: true
  EnableV2QRVerificationLogic: true
  EnableRenewCardRateLimiter: true
  EnableVgPciServerClient: true
  EnableRenewCardFlowV2:
    IsEnableOnAndroid: true
    MinAndroidVersion: 322
    IsEnableOnIos: true
    MinIosVersion: 465
  EnableSwitchNotificationDbWrites: true
  ShowUpdatedUiOnPhysicalCardOrderScreen:
    IsEnableOnAndroid: false
    MinAndroidVersion: 20000
    IsEnableOnIos: false
    MinIosVersion: 20000
  ShowUpdatedUiOnPhysicalCardOrderSuccessScreen:
    IsEnableOnAndroid: false
    MinAndroidVersion: 20000
    IsEnableOnIos: false
    MinIosVersion: 20000
  OrderPhysicalCardCriticalNotification: true
  FeatureReleaseConfig:
    FeatureConstraints:
      FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    ClientEncryptionKey: "staging/card/card-qr-code-aes-cbc-encryption-key"
    ClientEncryptionInitialisationVector: "staging/card/card-qr-code-aes-cbc-encryption-iv"
    MctClientEncryptionKey: "staging/card/card-qr-code-aes-cbc-encryption-key"
    MctClientEncryptionInitialisationVector: "staging/card/card-qr-code-aes-cbc-encryption-iv"
    TemporalCodecAesKey: "staging/temporal/codec-encryption-key"
    SlackOauthToken: "staging/card/slack-oauth-token"

CardOnboardingEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardAuthFactorUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

UserDevicePropertiesUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 10
  MaxMessages: 5
  QueueName: "staging-dc-user-device-properties-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ShipmentRegisterEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-shipment-register-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ShipmentRegisterEventPublisher:
  QueueName: "staging-card-shipment-register-queue"

TrackingCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-tracking-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeliveryDelayEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-delivery-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeliveryDelayEventPublisher:
  QueueName: "staging-card-delivery-notification-queue"

DeliveryDelayNotificationConfig:
  NotificationTime:
    - "5m"
    - "15m"
  DelayTimePostNotif: "5m"
  DelayTime: "3m"

ActivationNotificationConfig:
  NotificationTime:
    - "5m"
    - "15m"
  DelayTimePostNotif: "5m"
  DelayTime: "3m"

DeliveredEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-delivered-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeliveredEventPublisher:
  QueueName: "staging-card-delivered-event-queue"

MinAndroidVersionForAuthValidation : 200
MinIOSVersionForAuthValidation: 200

CardReplacementFeeUserGroupCheck:
  EnableUserGroupCheck: false
  AllowedUserGrp:
    - 1 # INTERNAL

FreeCardReplacementUserGroupCheck:
  EnableUserGroupCheck: true
  AllowedUserGrp:
    - 11 # FREE_CARD_REPLACEMENT

RateLimiterConfig:
  ResourceMap:
    dc_renew_card_api_1:
      Rate: 1
      Period: 2m
    dc_renew_card_api_2:
      Rate: 2
      Period: 4m # 7 days
    dc_renew_card_api_3:
      Rate: 5
      Period: 10m # 30 days
  Namespace: "card"

FetchTrackingDetailsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-tracking-details-fetch-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

FetchCardDeliveryDetailsDelay: "2m"

Tracing:
  Enable: true

CardDispatchRequestPublisher:
  QueueName: "staging-card-dispatch-request-queue"

CardDispatchRequestSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-dispatch-request-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardDispatchRequestCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-dispatch-request-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

InitDispatchWithAddressUpdatePublisher:
  QueueName: "staging-physical-card-dispatch-queue"

InitDispatchWithAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-physical-card-dispatch-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CardSwitchFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-switch-financial-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 9
      TimeUnit: "Second"

CardSwitchNonFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-switch-non-financial-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 9
      TimeUnit: "Second"

CardForexTxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-dc-forex-refund-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 11
      TimeUnit: "Minute"

CardTxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-card-txns-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 11
      TimeUnit: "Minute"

Buckets:
  DebitCardDocs: "epifi-staging-debit-card-docs"

CardCreationEventPublisher:
  TopicName: "staging-card-creation-event-topic"

DebitCardUpdateEventPublisher:
  TopicName: "staging-debit-card-update-topic"

CardSwitchNotificationsRawDataStore:
  BucketName: "epifi-raw-dev"
  FinancialNotificationsObjectKey: "staging/data/vendor/federal_reports/card_switch_notifications/%s/data_dump_%s.csv"
  NonFinancialNotificationsObjectKey: "staging/data/vendor/federal_reports/card_switch_notifications/%s/data_dump_1_%s.csv"

CardsRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  ClientName: card
  HystrixCommand:
    CommandName: "card_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

CacheConfigs:
  CardCacheConfig:
    Prefix: "card:"
    IsCachingEnabled: true
    CacheTTl: "30m"

DcAmcChargesEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-dc-amc-charges-event-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"

OrderPhysicalCardCriticalNotificationPublisher:
  QueueName: "staging-dc-onboarding-queue"

OrderPhysicalCardCriticalNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-dc-onboarding-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Second"

PhysicalDCOfferExperiment:
  IncludedSegments:
    - SegmentId: ""
  ExcludedSegments:
  OfferAmount:
    CurrencyCode: "INR"
    Units: 149
    Nanos: 0
  Description: "Price drop alert! Fi-Federal Debit Card now for ₹149"
  Icon: "https://epifi-icons.pointz.in/card-images/cash-asset.png"

QuestSdk:
  Disable: false

CardSwitchNotificationRewardsPublisher:
  TopicName: "staging-dc-switch-notification-topic"
