package datacompleteness

import (
	"context"

	"github.com/google/wire"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkerGConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
)

type IFactory interface {
	GetDataCompletenessChecker(ctx context.Context, req GetDataCompletenessCheckerRequest) (IDataCompletenessChecker, error)
}

type GetDataCompletenessCheckerRequest struct {
	Vendor      palPb.Vendor
	LoanProgram palPb.LoanProgram
}

type Factory struct {
	epifiDataCompletenessChecker  *EpifiDataCompletenessChecker
	lendenDataCompletenessChecker *LendenDataCompletenessChecker
	conf                          *palWorkerGConf.Config
}

var DataCompletenessFactoryWireSet = wire.NewSet(NewFactory, wire.Bind(new(IFactory), new(*Factory)))

func NewFactory(epifiDataCompletenessChecker *EpifiDataCompletenessChecker, lendenDataCompletenessChecker *LendenDataCompletenessChecker, conf *palWorkerGConf.Config) *Factory {
	return &Factory{
		epifiDataCompletenessChecker:  epifiDataCompletenessChecker,
		lendenDataCompletenessChecker: lendenDataCompletenessChecker,
		conf:                          conf,
	}
}

func (f *Factory) GetDataCompletenessChecker(ctx context.Context, req GetDataCompletenessCheckerRequest) (IDataCompletenessChecker, error) {
	switch {
	case req.Vendor == palPb.Vendor_LENDEN:
		if f.conf.Flags().IsLdcApplicationMovementEnabled() {
			return f.epifiDataCompletenessChecker, nil
		}
		return f.lendenDataCompletenessChecker, nil
	default:
		return f.epifiDataCompletenessChecker, nil
	}
}
