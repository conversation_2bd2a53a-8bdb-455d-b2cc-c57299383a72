// nolint: funlen, dupl, goimports
package activity

import (
	"context"
	"fmt"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/money"
	"go.temporal.io/sdk/activity"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	json "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	types "github.com/epifi/gamma/api/typesv2"
	abflVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	vendorAbflPb "github.com/epifi/gamma/api/vendors/abfl/lending"
	LendingVnPb "github.com/epifi/gamma/api/vendors/lending"
	"github.com/epifi/gamma/pkg/loans"
	abflPkg "github.com/epifi/gamma/pkg/loans/abfl"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/vendorgateway/abfl"
)

const (
	loanAgreementSigned            = "loan_esigned"
	loanClosedByUser               = "loan_closed"
	loanApproved                   = "loan_approved"
	applicationExpired             = "user_expired"
	loanDisbursed                  = "loan_disbursed"
	userDisqualified               = "user_disqualified"
	cibilPullFailed                = "cibil_pull_failed"
	kycRejected                    = "kyc_rejected"
	loanRejected                   = "loan_rejected"
	mandateSetupFailed             = "bank_failed"
	cibilWorkflowRejectedTriggered = "credit_workflow_rejected_triggered"
	kycWorkflowRejectedTriggered   = "kyc_workflow_rejected_triggered"
	offerExpired                   = "offer_expired"
	kycDocRejected                 = "kyc_doc_rejected"
	livenessCheckFailed            = "liveness_check_failed"
	backVerificationFailed         = "bank_verification_failed"
)

const (
	eventLogTimeStampFormat = "2006-01-02 15:04:05.999999"
)

//nolint:gocritic
func (p *Processor) ProcessCallbackNotification(ctx context.Context, req *palActivityPb.ProcessCallbackNotificationsRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	switch req.GetLoanHeader().GetVendor() {
	case palPb.Vendor_ABFL:
		switch req.GetLoanHeader().GetLoanProgram() {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			abflPalErr := p.handleAbflPalNotifications(ctx, req)
			if abflPalErr != nil {
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("unable to process the ABFL PAL notification request,err: %v, loanNotificationData : %v", abflPalErr, req.GetLoansNotification()))
			}
			return &palActivityPb.PalActivityResponse{}, nil
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			err := p.handleAbflRtdNotification(ctx, req.GetLoansNotification().GetAbflPwaJourneyStatusCallbackRequest())
			if err != nil {
				lg.Error("unable to process the ABFL Real time dist notification request", zap.Error(err), zap.Any("loan_notification", req.GetLoansNotification()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to process the ABFL Real time dist notification request,err: %v, loanNotificationData : %v", err, req.GetLoansNotification()))
			}
			return &palActivityPb.PalActivityResponse{}, nil
		}
	}

	return nil, errors.Wrap(epifierrors.ErrPermanent, "unknown request received")
}

func (p *Processor) handleAbflRtdNotification(ctx context.Context, notifReq *vendorAbflPb.PwaJourneyStatusCallbackRequest) error {
	lg := activity.GetLogger(ctx)
	customerId := notifReq.GetCustomerId()
	eventType := notifReq.GetEventType()
	eventType = strings.ToLower(eventType)

	lr, lrErr := p.loanRequestDao.GetByVendorReqId(ctx, customerId)
	if lrErr != nil {
		return errors.Wrap(lrErr, "error in fetching loan request")
	}

	err := p.UpdateAbflVendorCallbackStatusInLoanDb(ctx, &UpdateVendorCallbackStatusReq{
		EventName:               eventType,
		LoanRequestId:           lr.GetId(),
		VendorUpdateAtTimestamp: notifReq.GetLoggedAt(),
	})
	if err != nil {
		// Todo(Anupam): Add alert on this log
		// Intentionally not returning error to continue the further processing
		lg.Error("failed to update vendor PWA callback status in loan DB",
			zap.Error(err),
			zap.String(logger.LOAN_REQUEST_ID, lr.GetId()),
			zap.String("event_type", eventType),
			zap.String("vendor_timestamp", notifReq.GetLoggedAt()))
	}

	if notifReq.GetEventDescription().GetStringValue() != "" {
		lg.Info("ABFL PWA user got some event description", zap.String("event_description", notifReq.GetEventDescription().GetStringValue()), zap.String(logger.ACTOR_ID_V2, lr.GetActorId()))
	}

	vendor := lr.GetVendor().String()
	lp := lr.GetLoanProgram().String()
	p.emitPwaStatusEvent(ctx, lr.GetActorId(), lr.GetId(), eventType, vendor, lp, notifReq.GetLoggedAt())

	if eventType == loanAgreementSigned {
		p.emitDisbursalEvent(ctx, lr.GetActorId(), vendor, lp)
	}

	if eventType == loanClosedByUser {
		la, laErr := p.loanAccountDao.GetById(ctx, lr.GetLoanAccountId())
		if laErr != nil {
			return errors.Wrap(laErr, "error in fetching loan account")
		}

		if la.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			var fieldMasks []palPb.LoanAccountFieldMask
			la.Status = palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED
			fieldMasks = append(fieldMasks, palPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS)

			la.LoanEndDate = datetime.TimestampToDateInLoc(timestamppb.New(time.Now()), datetime.IST)
			fieldMasks = append(fieldMasks, palPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE)

			if updateErr := p.loanAccountDao.Update(ctx, la, fieldMasks); updateErr != nil {
				return errors.Wrap(updateErr, "error in closing loan account")
			}
		}
	} else {
		if eventType == loanApproved {
			loanOffer, loErr := p.loanOffersDao.GetById(ctx, lr.GetOfferId())
			if loErr != nil {
				return errors.Wrap(loErr, "error in getting loan offer")
			}

			loec, loecErr := p.loanOfferEligibilityCriteriaDao.GetById(ctx, loanOffer.GetLoanOfferEligibilityCriteriaId())
			if loecErr != nil {
				return errors.Wrap(loecErr, "error in getting loec")
			}

			loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR
			updateErr := p.loanOfferEligibilityCriteriaDao.Update(ctx, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS})
			if updateErr != nil {
				return errors.Wrap(updateErr, "error in updating loec")
			}
		}
		subStatus, err := p.getTerminalLrSubStatus(eventType)
		if err != nil {
			// status not need to be updated so not returning error
		}

		lr.SubStatus = subStatus

		var fieldMasks []palPb.LoanRequestFieldMask
		fieldMasks = append(fieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS)
		if subStatus == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS {

			var loanAmount float64
			if notifReq.GetEventDescription().GetStructValue().GetFields() != nil && notifReq.GetEventDescription().GetStructValue().GetFields()["loanAmount"] != nil {
				loanAmount = notifReq.GetEventDescription().GetStructValue().GetFields()["loanAmount"].GetNumberValue()
			}

			// Now, seeing the event description coming as a string value inside, so unmarshalling it to
			// get the loan amount if loan amount not present in the structure
			if loanAmount == 0 && notifReq.GetEventDescription().GetStringValue() != "" {
				str := []byte(notifReq.GetEventDescription().GetStringValue())
				eventDescription := &vendorAbflPb.PwaEventDescription{}
				err = json.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(str, eventDescription)
				if err != nil {
					logger.Error(ctx, "error in unmarshalling event description", zap.Error(err))
				} else {
					loanAmount = eventDescription.GetLoanAmount()
				}
			}

			if lr.GetDetails().GetLoanInfo() == nil {
				if lr.GetDetails() == nil {
					lr.Details = &palPb.LoanRequestDetails{}
				}
				lr.Details.LoanInfo = &palPb.LoanRequestDetails_LoanInfo{}
			}
			lr.Details.LoanInfo.Amount = money.ParseFloat(loanAmount, "INR")
			// todo(Anupam): vendor is not piping these fields as of now, passing zero to not maintain any stale data here
			lr.Details.LoanInfo.TenureInMonths = 0
			lr.Details.LoanInfo.InterestRate = 0
			lr.Details.LoanInfo.DisbursalAmount = money.ParseFloat(loanAmount, "INR")
			fieldMasks = append(fieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS)
			// todo(Anupam): remove this log once we are done with loan amount not piping to db debugging
			lg.Info("loan disbursal info updated", zap.String("loan_info", lr.Details.LoanInfo.String()))
		}
		updateLrErr := p.loanRequestDao.Update(ctx, lr, fieldMasks)
		if updateLrErr != nil {
			return errors.Wrap(updateLrErr, "error in updating loan request")
		}

		if eventType != applicationExpired && subStatus == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED {
			if deactivateLoanOfferErr := p.loanOffersDao.DeactivateLoanOffer(ctx, lr.GetOfferId()); err != nil {
				return errors.Wrap(deactivateLoanOfferErr, "error in deactivating loan offer")
			}
		}
	}
	return nil
}

func (p *Processor) getTerminalLrSubStatus(applicationStatus string) (palPb.LoanRequestSubStatus, error) {
	applicationStatus = strings.TrimSpace(applicationStatus)

	switch strings.ToLower(applicationStatus) {
	case loanDisbursed:
		return palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS, nil
	case kycRejected, loanRejected, mandateSetupFailed, userDisqualified, applicationExpired, cibilPullFailed, cibilWorkflowRejectedTriggered, kycWorkflowRejectedTriggered, offerExpired, kycDocRejected, livenessCheckFailed, backVerificationFailed:
		return palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED, nil
	default:
		return palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED, errors.New(fmt.Sprintf("unhandled application status: %v", applicationStatus))
	}
}

// EmitPwaStatusEvent emits event to the event broker
func (p *Processor) emitPwaStatusEvent(ctx context.Context, actorId string, lrId string, stageName string, vendor string, loanProgram string, eventLogTime string) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		expirationTime, err := time.ParseInLocation(eventLogTimeStampFormat, eventLogTime, datetime.IST)
		if err != nil {
			expirationTime = time.Now()
			activity.GetLogger(ctx).Error("error in parsing event log time", zap.Error(err), zap.String("eventLogTime", eventLogTime))
		}

		// passing stage status as dummy in the events as we don't have any handling for failure or success case,
		// and the stage event string will be able to better justify the stage status
		p.eventBroker.AddToBatch(ctx, loans.NewVendorPwaStageStatusUpdateEvent(actorId, lrId, vendor, loanProgram, stageName, "dummy", expirationTime))
	})
}

// EmitDisbursalEvent emits disbursal event to the event broker
func (p *Processor) emitDisbursalEvent(ctx context.Context, actorId string, vendor string, loanProgram string) {
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		event := palEvents.NewDisbursal(actorId, vendor, loanProgram)
		err := p.acqEventPublisher.PublishAcqEventSelectively(ctx, actorId, event)
		if err != nil {
			logger.Error(ctx, "error in publishing acq event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.PAYLOAD, event.GetEventProperties()))
		}
	})
}

func (p *Processor) handleAbflPalNotifications(ctx context.Context, req *palActivityPb.ProcessCallbackNotificationsRequest) error {
	switch req.GetLoansNotification().GetDetails().(type) {
	case *LendingVnPb.LoansNotification_AbflBreCallbackResponse:
		breData, err := abflPkg.GetVgBrePollingResponse(req.GetLoansNotification().GetAbflBreCallbackResponse())
		if err != nil {
			return err
		}
		payloadBytes, payloadBytesErr := json.Marshal(breData)
		if payloadBytesErr != nil {
			return errors.Wrap(payloadBytesErr, "unable to marshal the payload")
		}

		return p.sendSignalByAccountId(ctx, payloadBytes, breData.GetBrePollingData().GetAccountId(), string(palNs.LoanApplicationBreCallbackSignal))
	case *LendingVnPb.LoansNotification_AbflCkycCallbackResponse:
		ckycData, err := p.convertCkycVendorRequestToVgRequest(ctx, req)
		if err != nil {
			return err
		}

		payloadBytes, payloadBytesErr := json.Marshal(ckycData)
		if payloadBytesErr != nil {
			return errors.Wrap(payloadBytesErr, "unable to marshal the payload")
		}

		return p.sendSignalByAccountId(ctx, payloadBytes, ckycData.GetAccountId(), string(palNs.LoanApplicationCkycCallbackSignal))
	case *LendingVnPb.LoansNotification_AbflUkycCallbackResponse:
		ukycData, err := abflPkg.GetVgUkycStatusResponse(req.GetLoansNotification().GetAbflUkycCallbackResponse())
		if err != nil {
			return err
		}

		payloadBytes, payloadBytesErr := json.Marshal(ukycData)
		if payloadBytesErr != nil {
			return errors.Wrap(payloadBytesErr, "unable to marshal the payload")
		}

		return p.sendSignalByOrchId(ctx, payloadBytes, ukycData.GetUnifiedKycStatusData().GetTransactionId(), string(palNs.LoanApplicationUkycCallbackSignal))
	case *LendingVnPb.LoansNotification_AbflMandateCallbackResponse:
		mandateData := p.handleMandateHttpStatus(req)

		payloadBytes, payloadBytesErr := json.Marshal(mandateData)
		if payloadBytesErr != nil {
			return errors.Wrap(payloadBytesErr, "unable to marshal the payload")
		}

		return p.sendSignalByOrchId(ctx, payloadBytes, mandateData.GetData().GetRazorpayCustId(), string(palNs.LoanApplicationMandateCallbackSignal))
	case *LendingVnPb.LoansNotification_AbflImpsCallbackResponse:
		impsData := p.convertImpsVendorReqToVgReq(req)

		payloadBytes, payloadBytesErr := json.Marshal(impsData)
		if payloadBytesErr != nil {
			return errors.Wrap(payloadBytesErr, "unable to marshal the payload")
		}

		return p.sendSignalByOrchId(ctx, payloadBytes, impsData.GetData().GetIblRefNo(), string(palNs.LoanApplicationImpsCallbackSignal))
	default:
		return errors.New("loans notifications type is not handled")
	}
}

func (p *Processor) sendSignalByOrchId(ctx context.Context, payloadBytes []byte, refId string, signalName string) error {
	lse, lseErr := p.loanStepExecutionDao.GetByOrchId(ctx, refId)
	if lseErr != nil {
		return errors.Wrap(lseErr, "unable to fetch the lse")
	}

	lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
	if lrErr != nil {
		return errors.Wrap(lseErr, "unable to fetch the lr")
	}

	abflactivityReq := &palActivityPb.AbflPollingActivityRequest{
		CallbackData: payloadBytes,
	}

	payload, payloadErr := json.Marshal(abflactivityReq)
	if payloadErr != nil {
		return errors.Wrap(payloadErr, "unable to marshal the payload")
	}

	signalErr := p.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), signalName, payload)
	if signalErr != nil {
		return errors.Wrap(signalErr, "unable to send the signal")
	}
	return nil
}

func (p *Processor) sendSignalByAccountId(ctx context.Context, payloadBytes []byte, refId string, signalName string) error {
	lr, lrErr := p.loanRequestDao.GetByVendorReqId(ctx, refId)
	if lrErr != nil {
		return errors.Wrap(lrErr, "unable to fetch the loan request")
	}

	abflactivityReq := &palActivityPb.AbflPollingActivityRequest{
		CallbackData: payloadBytes,
	}

	payload, payloadErr := json.Marshal(abflactivityReq)
	if payloadErr != nil {
		return errors.Wrap(payloadErr, "unable to marshal the payload")
	}

	signalErr := p.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), signalName, payload)
	if signalErr != nil {
		return errors.Wrap(signalErr, "unable to send the signal")
	}
	return nil
}

func (p *Processor) convertImpsVendorReqToVgReq(request *palActivityPb.ProcessCallbackNotificationsRequest) *abflVgPb.ImpsCallbackResponse {
	req := request.GetLoansNotification().GetAbflImpsCallbackResponse()

	resp := &abflVgPb.ImpsCallbackResponse{
		Status: rpc.StatusOk(),
		Data: &abflVgPb.ImpsCallbackResponse_Data{
			IblRefNo:      req.GetData().GetIblRefNo(),
			CustomerRefNo: req.GetData().GetCustomerRefNo(),
			StatusDesc:    req.GetData().GetStatusDesc(),
			Amount:        req.GetData().GetAmount(),
			TranType:      req.GetData().GetTranType(),
			StatusCode:    req.GetData().GetStatusCode(),
			Utr:           req.GetData().GetUtr(),
			PaymentDate:   req.GetData().GetPaymentDate(),
			ImpsBeneName:  req.GetData().GetImpsBeneName(),
		},
	}

	return resp
}

func (p *Processor) convertCkycVendorRequestToVgRequest(ctx context.Context, req *palActivityPb.ProcessCallbackNotificationsRequest) (*abflVgPb.PollingStatusCkycResponse, error) {
	ckycDetails := req.GetLoansNotification().GetAbflCkycCallbackResponse()

	rpcStatus, statusErr := p.handleStatus(ckycDetails)
	if statusErr != nil {
		logger.Error(ctx, "error in handling status CKYC abfl", zap.Error(statusErr))
		return nil, statusErr
	}

	var resp *abflVgPb.PollingStatusCkycResponse
	if rpcStatus.GetCode() != rpc.StatusOk().GetCode() {
		logger.Error(ctx, "ckyc is not in a success stage", zap.Error(statusErr), zap.String("ckyc_id_number", ckycDetails.GetData().GetResult().GetSourceOutput().GetCkycPersonalDetails().GetCkycIdDetails().GetCkycId_1().GetCkycIdNo()))
		resp = &abflVgPb.PollingStatusCkycResponse{
			Status: rpcStatus,
		}
	} else {
		res, err := p.handleCkycVgResp(ckycDetails)
		if err != nil {
			logger.Error(ctx, "error in handleCkycVgResp CKYC abfl", zap.Error(statusErr))
			return nil, err
		}
		resp = res
	}
	return resp, nil
}

//nolint:dupl,gocritic
func (p *Processor) handleStatus(msg proto.Message) (*rpc.Status, error) {
	switch msg.(type) {
	case *vendorAbflPb.PollingStatusCkycResponse:
		if msg.(*vendorAbflPb.PollingStatusCkycResponse).GetResponseStatus() == "IN_PROGRESS" {
			return rpc.ExtendedStatusInProgress(), nil
		}
		if m, ok := ResponseStatus[msg.(*vendorAbflPb.PollingStatusCkycResponse).GetResponseStatus()]; ok && !m {
			if len(msg.(*vendorAbflPb.PollingStatusCkycResponse).GetErrors()) != 0 {
				return VendorErrorResponseCodesToRpcResponse(msg.(*vendorAbflPb.PollingStatusCkycResponse).GetErrors()[0].GetCode(), msg.(*vendorAbflPb.PollingStatusCkycResponse).GetErrors()[0].GetDescription()), nil
			}
			return VendorErrorResponseCodesToRpcResponse(msg.(*vendorAbflPb.PollingStatusCkycResponse).GetError().GetCode(), msg.(*vendorAbflPb.PollingStatusCkycResponse).GetError().GetDescription()), nil
		} else {
			return rpc.StatusOk(), nil
		}
	default:
		return nil, errors.New("unable to recognize the request type")
	}
}

func (p *Processor) handleMandateHttpStatus(request *palActivityPb.ProcessCallbackNotificationsRequest) *abflVgPb.EmandateStatusResponse {
	req := request.GetLoansNotification().GetAbflMandateCallbackResponse()

	var eMandateStatus abflVgPb.MandateStatus
	var isRejectedOrFailed bool
	switch req.GetData().GetTokenStatus() {
	case "failed":
		isRejectedOrFailed = true
		eMandateStatus = abflVgPb.MandateStatus_MANDATE_STATUS_FAILED
	case "rejected":
		isRejectedOrFailed = true
		eMandateStatus = abflVgPb.MandateStatus_MANDATE_STATUS_REJECTED
	default:
		eMandateStatus = abflVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS
	}
	vgResponse := &abflVgPb.EmandateStatusResponse{
		Status:         rpc.StatusOk(),
		EMandateStatus: eMandateStatus,
		Data: &abflVgPb.EmandateStatusResponse_Data{
			RazorpayCustId: req.GetData().GetRazorpayCustId(),
			TokenId:        req.GetData().GetTokenId(),
			TokenStatus:    req.GetData().GetTokenStatus(),
			MrnNo:          req.GetData().GetMrnNo(),
			Code:           req.GetData().GetCode(),
			Desc:           req.GetData().GetDesc(),
		},
	}

	if isRejectedOrFailed {
		return vgResponse
	}

	if m, ok := ResponseStatus[req.GetResponseStatus()]; ok && !m {
		if len(req.GetErrors()) != 0 {
			return &abflVgPb.EmandateStatusResponse{
				Status: VendorErrorResponseCodesToRpcResponse(req.GetErrors()[0].GetCode(), req.GetErrors()[0].GetDescription()),
			}
		}
		return &abflVgPb.EmandateStatusResponse{
			Status: VendorErrorResponseCodesToRpcResponse(req.GetError().GetCode(), req.GetError().GetDescription()),
		}
	}

	if req.GetData().GetCode() == "CALLBACK_NOT_RECEIVED" {
		return &abflVgPb.EmandateStatusResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg(req.GetData().GetDesc()),
		}
	}

	vgResponse = &abflVgPb.EmandateStatusResponse{
		Status:         rpc.StatusOk(),
		EMandateStatus: abflVgPb.MandateStatus_MANDATE_STATUS_SUCCESS,
		Data: &abflVgPb.EmandateStatusResponse_Data{
			RazorpayCustId: req.GetData().GetRazorpayCustId(),
			TokenId:        req.GetData().GetTokenId(),
			TokenStatus:    req.GetData().GetTokenStatus(),
			MrnNo:          req.GetData().GetMrnNo(),
			Code:           req.GetData().GetCode(),
			Desc:           req.GetData().GetDesc(),
			FailureReason:  req.GetData().GetFailureReason(),
		},
	}
	return vgResponse
}

//nolint:funlen
func (p *Processor) handleCkycVgResp(res *vendorAbflPb.PollingStatusCkycResponse) (*abflVgPb.PollingStatusCkycResponse, error) {
	if vendorSuccessCodes(res.GetData().GetStatus(), res.GetData().GetMsg()) != nil {
		return &abflVgPb.PollingStatusCkycResponse{
			Status: vendorSuccessCodes(res.GetData().GetStatus(), res.GetData().GetMsg()),
		}, nil
	}

	personalData := res.GetData().GetResult().GetSourceOutput().GetCkycPersonalDetails()
	gender, err := getGenderType(personalData.GetCkycGender())
	if err != nil || personalData.GetCkycGender() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, gender: %v", err, gender))
	}

	// ckycImageMap := make(map[string]*vgAbflPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData, 0)
	// for _, imageData := range personalData.GetCkycImageDetails() {
	//	data := &vgAbflPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
	//		CkycImageData: nil,
	//		CkycImgType:   getCkycImageTypeEnum(imageData.GetCkycImgType()),
	//	}
	//	ckycImageMap[imageData]
	// }

	// TODO - Move this to map implementation
	ckycImage1Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_1().GetCkycImgType())
	ckycImage2Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_2().GetCkycImgType())
	ckycImage3Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_3().GetCkycImgType())
	ckycImage4Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_4().GetCkycImgType())
	ckycImage5Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_5().GetCkycImgType())
	ckycImage6Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_6().GetCkycImgType())
	ckycImage7Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_7().GetCkycImgType())
	ckycImage8Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_8().GetCkycImgType())
	ckycImage9Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_9().GetCkycImgType())
	ckycImage10Type := getCkycImageTypeEnum(personalData.GetCkycImageDetails().GetCkycImage_10().GetCkycImgType())

	ckycAccountType, err := getCkycAccountType(personalData.GetCkycAccType())
	if err != nil || personalData.GetCkycAccType() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, ckycAccountType: %v", err, ckycAccountType))
	}

	ckycDateOfDeclaration, err := datetime.ParseStringToDateInLocation(abfl.CkycDateOfDeclarationLayout, personalData.GetCkycDeclarationDetails().GetCkycDateOfDeclaration(), datetime.IST)
	if err != nil || personalData.GetCkycDeclarationDetails().GetCkycDateOfDeclaration() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, ckycDateOfDeclaration: %v", err, ckycDateOfDeclaration))
	}

	ckycVerDate, err := datetime.ParseStringToDateInLocation(abfl.CkycDateOfDeclarationLayout, personalData.GetCkycDeclarationDetails().GetCkycDateOfDeclaration(), datetime.IST)
	if err != nil || personalData.GetCkycDeclarationDetails().GetCkycDateOfDeclaration() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, ckycVerDate: %v", err, ckycVerDate))
	}

	ckycNoOfIds, err := convertStringToInt64(personalData.GetCkycDeclarationDetails().GetCkycNoOfIds())
	if err != nil || personalData.GetCkycDeclarationDetails().GetCkycNoOfIds() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, ckycNoOfIds: %v", err, ckycNoOfIds))
	}

	ckycNoOfImages, err := convertStringToInt64(personalData.GetCkycDeclarationDetails().GetCkycNoOfImages())
	if err != nil || personalData.GetCkycDeclarationDetails().GetCkycNoOfImages() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, ckycNoOfImages: %v", err, ckycNoOfImages))
	}

	ckycNoOfRelPerson, err := convertStringToInt64(personalData.GetCkycDeclarationDetails().GetCkycNoOfRelPerson())
	if err != nil || personalData.GetCkycDeclarationDetails().GetCkycNoOfRelPerson() == "" {
		return nil, errors.Wrap(err, fmt.Sprintf("err: %v, ckycNoOfRelPerson: %v", err, ckycNoOfRelPerson))
	}

	vgResponse := &abflVgPb.PollingStatusCkycResponse{
		Status:    rpc.StatusOk(),
		RequestId: res.GetData().GetRequestId(),
		AccountId: res.GetData().GetAccountId(),
		PollingStatusCkycData: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData{
			Result: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result{
				SourceOutput: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput{
					CkycRequestStatus: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycRequestStatus{
						CkycArn:              res.GetData().GetResult().GetSourceOutput().GetCkycRequestStatus().GetCkycArn(),
						CkycIsSuccess:        res.GetData().GetResult().GetSourceOutput().GetCkycRequestStatus().GetCkycIsSuccess(),
						CkycRejectionMessage: res.GetData().GetResult().GetSourceOutput().GetCkycRequestStatus().GetCkycRejectionMessage(),
					},
					CkycPersonalDetails: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails{
						CkycMaidenFullName: &commontypes.Name{
							FirstName:  personalData.GetCkycMaidenFirstName(),
							MiddleName: personalData.GetCkycMaidenMiddleName(),
							LastName:   personalData.GetCkycMaidenLastName(),
							Honorific:  personalData.GetCkycMaidenNamePrefix(),
						},
						CkycForm60: personalData.GetCkycForm60(),
						CkycAddresses: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycAddresses{
							CkycCorAddress: &types.PostalAddress{
								RegionCode:         personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAddCountry(),
								PostalCode:         personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAddPin(),
								AdministrativeArea: personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAddState(),
								Sublocality:        personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAddCity(),
								AddressLines:       []string{personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAdd1(), personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAdd2(), personalData.GetCkycAddresses().GetCkycCorAddress().GetCkycCorAdd3()},
							},
							CkycPerAddSameAsCor: getCkycPerAddSameAsCor(personalData.GetCkycAddresses().GetCkycPerAddSameAsCor()),
							CkycPerAddress: &types.PostalAddress{
								RegionCode:         personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAddCountry(),
								PostalCode:         personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAddPin(),
								AdministrativeArea: personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAddState(),
								Sublocality:        personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAddDistrict(),
								AddressLines:       []string{personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAdd1(), personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAdd2(), personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAdd3()},
								Locality:           personalData.GetCkycAddresses().GetCkycPerAddress().GetCkycPerAddCity(),
							},
						},
						CkycGender:    gender,
						CkycPan:       personalData.GetCkycPan(),
						CkycConstType: personalData.GetCkycConstType(),
						CkycFatherFullName: &commontypes.Name{
							FirstName:  personalData.GetCkycFatherFirstName(),
							MiddleName: personalData.GetCkycFatherMiddleName(),
							LastName:   personalData.GetCkycFatherLastName(),
							Honorific:  personalData.GetCkycFatherNamePrefix(),
						},
						CkycNumber: personalData.GetCkycNumber(),
						CkycMotherFullName: &commontypes.Name{
							FirstName:  personalData.GetCkycMotherFirstName(),
							MiddleName: personalData.GetCkycMotherMiddleName(),
							LastName:   personalData.GetCkycMotherLastName(),
							Honorific:  personalData.GetCkycMotherNamePrefix(),
						},
						CkycContactDetails: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycContactDetails{
							CkycEmail:     personalData.GetCkycContactDetails().GetCkycEmail(),
							CkycMobileIsd: personalData.GetCkycContactDetails().GetCkycMobileIsd(),
							CkycMobileNo:  personalData.GetCkycContactDetails().GetCkycMobileNo(),
							CkycOffTelNo:  personalData.GetCkycContactDetails().GetCkycOffTelNo(),
							CkycOffTelStd: personalData.GetCkycContactDetails().GetCkycOffTelStd(),
							CkycResTelNo:  personalData.GetCkycContactDetails().GetCkycResTelNo(),
							CkycResTelStd: personalData.GetCkycContactDetails().GetCkycResTelStd(),
						},
						CkycFullName: &commontypes.Name{
							FirstName:  personalData.GetCkycFirstName(),
							MiddleName: personalData.GetCkycMiddleName(),
							LastName:   personalData.GetCkycLastName(),
							Honorific:  personalData.GetCkycNamePrefix(),
						},
						CkycImageDetails: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageDetails{
							CkycImage_1: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_1().GetCkycImageData(),
								CkycImgType:   ckycImage1Type,
							},
							CkycImage_2: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_2().GetCkycImageData(),
								CkycImgType:   ckycImage2Type,
							},
							CkycImage_3: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_3().GetCkycImageData(),
								CkycImgType:   ckycImage3Type,
							},
							CkycImage_4: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_4().GetCkycImageData(),
								CkycImgType:   ckycImage4Type,
							},
							CkycImage_5: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_5().GetCkycImageData(),
								CkycImgType:   ckycImage5Type,
							},
							CkycImage_6: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_6().GetCkycImageData(),
								CkycImgType:   ckycImage6Type,
							},
							CkycImage_7: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_7().GetCkycImageData(),
								CkycImgType:   ckycImage7Type,
							},
							CkycImage_8: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_8().GetCkycImageData(),
								CkycImgType:   ckycImage8Type,
							},
							CkycImage_9: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_9().GetCkycImageData(),
								CkycImgType:   ckycImage9Type,
							},
							CkycImage_10: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycImageData{
								CkycImageData: personalData.GetCkycImageDetails().GetCkycImage_10().GetCkycImageData(),
								CkycImgType:   ckycImage10Type,
							},
						},
						CkycDob:     datetime.DateFromString(personalData.GetCkycDob()),
						CkycRemarks: personalData.GetCkycRemarks(),
						CkycSpouseFullName: &commontypes.Name{
							FirstName:  personalData.GetCkycSpouseFirstName(),
							MiddleName: personalData.GetCkycSpouseMiddleName(),
							LastName:   personalData.GetCkycSpouseLastName(),
							Honorific:  personalData.GetCkycSpouseNamePrefix(),
						},
						CkycIdDetails: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycIdDetails{
							CkycId_1: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycIdDetail{
								CkycIdNo:      personalData.GetCkycIdDetails().GetCkycId_1().GetCkycIdNo(),
								CkycIdType:    personalData.GetCkycIdDetails().GetCkycId_1().GetCkycIdType(),
								CkycVerStatus: personalData.GetCkycIdDetails().GetCkycId_1().GetCkycVerStatus(),
							},
							CkycId_2: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycIdDetail{
								CkycIdNo:      personalData.GetCkycIdDetails().GetCkycId_2().GetCkycIdNo(),
								CkycIdType:    personalData.GetCkycIdDetails().GetCkycId_2().GetCkycIdType(),
								CkycVerStatus: personalData.GetCkycIdDetails().GetCkycId_2().GetCkycVerStatus(),
							},
							CkycId_3: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycIdDetail{
								CkycIdNo:      personalData.GetCkycIdDetails().GetCkycId_3().GetCkycIdNo(),
								CkycIdType:    personalData.GetCkycIdDetails().GetCkycId_3().GetCkycIdType(),
								CkycVerStatus: personalData.GetCkycIdDetails().GetCkycId_3().GetCkycVerStatus(),
							},
						},
						CkycAccType: ckycAccountType,
						CkycDeclarationDetails: &abflVgPb.PollingStatusCkycResponse_PollingStatusCkycData_Result_SourceOutput_CkycPersonalDetails_CkycDeclarationDetails{
							CkycDateOfDeclaration:  ckycDateOfDeclaration,
							CkycNoOfIds:            ckycNoOfIds,
							CkycNoOfImages:         ckycNoOfImages,
							CkycNoOfRelPerson:      ckycNoOfRelPerson,
							CkycPlaceOfDeclaration: personalData.GetCkycDeclarationDetails().GetCkycPlaceOfDeclaration(),
							CkycTypeOfDocSubmitted: personalData.GetCkycDeclarationDetails().GetCkycTypeOfDocSubmitted(),
							CkycVerBranch:          personalData.GetCkycDeclarationDetails().GetCkycVerBranch(),
							CkycVerDate:            ckycVerDate,
							CkycVerDesignation:     personalData.GetCkycDeclarationDetails().GetCkycVerDesignation(),
							CkycVerEmpCode:         personalData.GetCkycDeclarationDetails().GetCkycVerEmpCode(),
							CkycVerName:            personalData.GetCkycDeclarationDetails().GetCkycVerName(),
						},
					},
				},
			},
		},
	}
	return vgResponse, nil
}
