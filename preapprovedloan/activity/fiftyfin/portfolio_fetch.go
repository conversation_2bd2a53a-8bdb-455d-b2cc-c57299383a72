package fiftyfin

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// PortfolioFetch activity initiates a portfolio fetch request for the user.
// if one fetch request is already in progress, this will return the status of the fetch and will keep polling till the portfolio gets to  a
// terminal state. If no request found, this will initiate a new request and will start polling for it.
// nolint:funlen
func (p *Processor) PortfolioFetch(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	loanStep, err := p.loanStepExecutionDao.GetById(ctx, req.GetLoanStep().GetId())
	if err != nil {
		lg.Error("failed to get loan step execution by id", zap.Error(err))
		return nil, palActivity.GetActivityErrFromDaoError(err)
	}
	res := &palActivityPb.PalActivityResponse{
		LoanStep:      loanStep,
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
	}
	switch loanStep.GetStatus() {
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:
		return res, nil
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION:
		return nil, errors.Wrap(epifierrors.ErrPermanent, "failed to execute work")
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED:
		err = p.cancelPortfolioFetchWorkflow(ctx, loanStep.GetOrchId(), &palPb.LoanHeader{
			LoanProgram: req.GetLoanProgram(),
			Vendor:      req.GetVendor(),
		})
		if err != nil {
			lg.Error("failed to cancel portfolio fetch workflow", zap.Error(err))
			return nil, fmt.Errorf("failed to cancel portfolio fetch workflow: %w", err)
		}
		return res, nil
	}
	// get the portfolio fetch status for this client_id to check if one request is already in progress.
	pfFetchStatus, err := p.securedLoansClient.GetPortfolioFetchStatus(ctx, &secured_loans.GetPortfolioFetchStatusRequest{
		ClientRequestId: loanStep.GetOrchId(),
		LoanHeader:      p.getBeLoanHeader(req.GetLoanProgram()),
	})
	if te := epifigrpc.RPCError(pfFetchStatus, err); te != nil && !pfFetchStatus.GetStatus().IsRecordNotFound() {
		lg.Error("error in fetching portfolio fetch flow status, pfFetchStatus", zap.Error(te))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching portfolio fetch flow status, err: %v, status: %v", err, pfFetchStatus.GetStatus().String()))
	}
	// if record not found, initiate a new request
	if pfFetchStatus.GetStatus().IsRecordNotFound() {
		var additionDetails *secured_loans.InitPfFetchAdditionalDetails
		releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_LAMF_PHASE3_1).WithActorId(loanStep.GetActorId())
		isReleased, releaseErr := p.releaseEvaluator.Evaluate(ctx, releaseConstraint)
		if releaseErr != nil {
			lg.Error("failed to evaluate feature_lamf_phase3_1", zap.Error(releaseErr))
			return nil, fmt.Errorf("failed to evaluate feature_lamf_phase3_1: %v, %w", releaseErr, epifierrors.ErrTransient)
		}
		if isReleased {
			// Enable CAS summary fetch and remove email from user input
			additionDetails = initPfFetchAdditionalDetails(false, true, true, false, true)
		} else {
			additionDetails = initPfFetchAdditionalDetails(false, true, false, false, false)
		}
		initErr := p.initiateNewFetchRequest(ctx, loanStep, req.GetLoanProgram(), additionDetails, loanStep.GetOrchId(), nil)
		if initErr != nil {
			lg.Error("error in initiating new portfolio fetch request", zap.Error(initErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in initiating new portfolio fetch request, err: %v", initErr.Error()))
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("portfolio fetch flow initiated"))
	}
	subStatus, nextAction, handleStatusErr := p.handlePortfolioFetchStatus(ctx, pfFetchStatus, loanStep)
	if handleStatusErr != nil {
		return res, handleStatusErr
	}
	if subStatus != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED {
		res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
	}
	if loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED &&
		subStatus != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND && subStatus != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE {
		lg.Error(fmt.Sprintf("portfolio fetch failed, substatus: %s", subStatus))
	}
	res.NextAction = nextAction
	res.LoanStep.SubStatus = subStatus
	return res, nil
}

func (p *Processor) initiateNewFetchRequest(ctx context.Context, lse *palPb.LoanStepExecution, loanProgram palPb.LoanProgram,
	additionalDetails *secured_loans.InitPfFetchAdditionalDetails, clientReqId string, dlGenerator func(ctx context.Context, dl *deeplink.Deeplink) (*deeplink.Deeplink, error)) error {
	redirectDl, dlErr := p.deeplinkProvider.GetPollScreenDeepLink(p.getFeLoanHeader(loanProgram), lse)
	if dlErr != nil {
		return fmt.Errorf("error in generating poll screen deeplink: %w", dlErr)
	}
	initiateRes, err := p.securedLoansClient.InitiatePortfolioFetch(ctx, &secured_loans.InitiatePortfolioFetchRequest{
		ActorId:           lse.GetActorId(),
		LoanHeader:        p.getBeLoanHeader(loanProgram),
		LoanRequestType:   palPb.LoanRequestType_LOAN_REQUEST_TYPE_NEW_PORTFOLIO_FETCH,
		RedirectDeeplink:  redirectDl,
		ClientRequestId:   clientReqId,
		Vendor:            palPb.Vendor_FIFTYFIN,
		AdditionalDetails: additionalDetails,
	})
	if rpcErr := epifigrpc.RPCError(initiateRes, err); rpcErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in initiating new portfolio fetch request, err: %s", rpcErr))
	}
	dl := initiateRes.GetNextAction()
	if dlGenerator != nil {
		dl, err = dlGenerator(ctx, initiateRes.GetNextAction())
		if err != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating next action deeplink : %s", err.Error()))
		}
	}
	updateErr := p.updateLseStatusAndLrNextAction(ctx, lse, dl, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS)
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse status or lr next action, err: %v", updateErr.Error()))
	}

	return nil
}

// nolint:funlen
func (p *Processor) handlePortfolioFetchStatus(ctx context.Context, res *secured_loans.GetPortfolioFetchStatusResponse, lse *palPb.LoanStepExecution) (palPb.LoanStepExecutionSubStatus, *deeplink.Deeplink, error) {
	var subStatus palPb.LoanStepExecutionSubStatus
	if !helper.IsLrTerminalStatus(res.GetPortfolioFetchStatus()) {
		return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, "portfolio fetch in progress")
	}

	switch res.GetPortfolioFetchStatus() {
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		dl, err := p.deeplinkProvider.GetNoFundsFailureScreen(ctx, p.deeplinkProvider.GetLoanHeader(), lse.GetActorId())
		if err != nil {
			return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating no funds failure screen : %s", err.Error()))
		}
		switch res.GetPortfolioFetchSubStatus() {
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOW_PORTFOLIO:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE
			dl, err = p.deeplinkProvider.GetLowFundsValueFailureScreen(ctx, p.deeplinkProvider.GetLoanHeader(), lse.GetActorId())
			if err != nil {
				return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating low funds failure screen: %s", err.Error()))
			}
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED
			dl, err = p.deeplinkProvider.GetCasSummaryFetchFailedErrorScreenDl(ctx, p.deeplinkProvider.GetLoanHeader())
			if err != nil {
				return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting cas summary fetch failed error screen: %s", err.Error()))
			}
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED
			dl, err = p.deeplinkProvider.GetCasSummaryFetchFailedErrorScreenDl(ctx, p.deeplinkProvider.GetLoanHeader())
			if err != nil {
				return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting cas summary fetch failed error screen: %s", err.Error()))
			}
		case palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_DETAILED_FETCH_FAILED:
			subStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_DETAILED_FETCH_FAILED
			dl, err = p.deeplinkProvider.GetCasSummaryFetchFailedErrorScreenDl(ctx, p.deeplinkProvider.GetLoanHeader())
			if err != nil {
				return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting cas detailed fetch failed error screen: %s", err.Error()))
			}
		}
		return subStatus, dl, nil
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		updateErr := p.updateLseStatusAndLrNextAction(ctx, lse, p.deeplinkProvider.GetLoanLandingInfo(p.deeplinkProvider.GetLoanHeader()), palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED)
		if updateErr != nil {
			return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse status and lr next action, err: %v", updateErr))
		}
		return subStatus, nil, nil
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED:
		// user has dropped off from the flow and portfolio fetch process expired
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		updateErr := p.updateLseStatusAndLrNextAction(ctx, lse, p.deeplinkProvider.GetLoanLandingInfo(p.deeplinkProvider.GetLoanHeader()), palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED)
		if updateErr != nil {
			return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse status and lr next action, err: %v", updateErr))
		}
		return subStatus, nil, nil
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		nextAction, err := p.deeplinkProvider.GetPollScreenDeepLink(p.deeplinkProvider.GetLoanHeader(), lse)
		if err != nil {
			return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to generate poll screen deeplink, err: %v", err))
		}
		return subStatus, nextAction, nil
	default:
		return subStatus, nil, errors.Wrap(epifierrors.ErrTransient, "portfolio fetch in progress")
	}
}

func (p *Processor) updateLseStatusAndLrNextAction(ctx context.Context, lse *palPb.LoanStepExecution, nextAction *deeplink.Deeplink, status palPb.LoanStepExecutionStatus) error {
	lse.Status = status
	updateLseErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	})
	if updateLseErr != nil {
		return errors.Wrap(updateLseErr, "error in updating lse status")
	}

	updateLrErr := palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, lse.GetRefId(), nextAction)
	if updateLrErr != nil {
		return errors.Wrap(updateLrErr, "error in updating lr next action")
	}

	return nil
}

func (p *Processor) cancelPortfolioFetchWorkflow(ctx context.Context, clientReqId string, lh *palPb.LoanHeader) error {
	res, err := p.securedLoansClient.CancelPortfolioFetch(ctx, &secured_loans.CancelPortfolioFetchRequest{
		ClientRequestId: clientReqId,
		LoanHeader:      lh,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while canceling portfolio fetch workflow : %s", rpcErr.Error()))
	}
	return nil
}

func initPfFetchAdditionalDetails(skipAccountDetailUpdate, fetchMfcPf, fetchMfcCasSummaryPf, fetchFiftyfinPf, isUserEmailInferred bool) *secured_loans.InitPfFetchAdditionalDetails {
	return &secured_loans.InitPfFetchAdditionalDetails{
		Details: &secured_loans.InitPfFetchAdditionalDetails_FiftyfinDetails_{
			FiftyfinDetails: &secured_loans.InitPfFetchAdditionalDetails_FiftyfinDetails{
				SkipAccountDetailUpdate:     skipAccountDetailUpdate,
				FetchMfcPortfolio:           fetchMfcPf,
				FetchMfcCasSummaryPortfolio: fetchMfcCasSummaryPf,
				FetchFiftyfinPortfolio:      fetchFiftyfinPf,
				UserEmailInputInferred:      isUserEmailInferred,
			},
		},
	}
}
