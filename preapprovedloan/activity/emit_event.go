package activity

import (
	"context"
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
)

type EventEmitter struct {
	EventName   string
	StageName   string
	Vendor      string
	LoanProgram string
	Status      string
	SubStatus   string
	EventParams map[string]string
	EventType   string
	EventId     string
	ActorId     string
	ProspectId  string
	FlowName    string
}

func NewEventEmitter(lse *preapprovedloan.LoanStepExecution, eventName string, vendor string, loanProgram string, eventParams map[string]string) *EventEmitter {
	return &EventEmitter{
		EventName:   eventName,
		StageName:   lse.GetStepName().String(),
		Vendor:      vendor,
		LoanProgram: loanProgram,
		Status:      lse.GetStatus().String(),
		SubStatus:   lse.GetSubStatus().String(),
		EventParams: eventParams,
		EventType:   events.EventTrack,
		EventId:     uuid.New().String(),
		ActorId:     lse.GetActorId(),
		ProspectId:  "",
		FlowName:    lse.GetFlow().String(),
	}
}

func (c *EventEmitter) GetEventType() string {
	return c.EventType
}

func (c *EventEmitter) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *EventEmitter) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *EventEmitter) GetEventId() string {
	return c.EventId
}

func (c *EventEmitter) GetUserId() string {
	return c.ActorId
}

func (c *EventEmitter) GetProspectId() string {
	return c.ProspectId
}

func (c *EventEmitter) GetEventName() string {
	return c.EventName
}

func (p *Processor) EmitEvent(ctx context.Context, req *palActivityPb.EmitEventRequest) (*palActivityPb.EmitEventResponse, error) {
	res := &palActivityPb.EmitEventResponse{}
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), NewEventEmitter(req.GetLse(), req.GetEventName(), req.GetVendor(), req.GetLoanProgram(), req.GetEventParams()))
		if isDisbursalEquivalentStage(req.GetLse().GetStepName(), req.GetVendor(), req.GetLoanProgram()) && req.GetLse().GetStatus() == preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
			// Disbursal event is required for consumption on AppsFlyer which does not have a way to read the specific properties of the event
			// That is why we cannot use the step name property to determine the disbursal action
			// The main purpose of this event is to attribute the conversion to a particular campaign/ad partner
			event := palEvents.NewDisbursal(req.GetLse().GetActorId(), req.GetVendor(), req.GetLoanProgram())
			err := p.acqEventPublisher.PublishAcqEventSelectively(ctx, req.GetLse().GetActorId(), event)
			if err != nil {
				logger.Error(ctx, "error in publishing acq event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetLse().GetActorId()), zap.Any(logger.PAYLOAD, event.GetEventProperties()))
			}
		}
	})

	return res, nil
}

// TODO: Implement an interface (implemented by stages) to fetch events to be published for tracking acquisition metrics
func isDisbursalEquivalentStage(stage preapprovedloan.LoanStepExecutionStepName, vendor string, loanProgram string) bool {
	switch {
	case vendor == preapprovedloan.Vendor_MONEYVIEW.String():
		return stage == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION
	// Removing ABFL disbursal event as it is already been sent from the job and it is not required to be sent from here
	// case vendor == preapprovedloan.Vendor_ABFL.String() && loanProgram == preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION.String():
	//	 return stage == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION
	default:
		return stage == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL
	}
}
