// nolint:gosec,dupl,funlen
package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	logger2 "github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	activityPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans/activity"
	activity2 "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

const PortfolioMinAmountForCASSummaryInRupees = 50000

// SetOtpData updates the LSE with the required details and set the next action to OTP screen.
func (p *Processor) SetOtpData(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	logger := activity.GetLogger(ctx)

	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}

		la, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
		if err != nil {
			logger.Error("error in getting loan applicant details", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan applicant details : %v", err.Error()))
		}

		email, phoneNumber := la.GetPersonalDetails().GetEmailId(), la.GetPersonalDetails().GetPhoneNumber()
		if phoneNumber == nil {
			logger.Error("phone number does not exist in loan applicant")
			return nil, errors.Wrap(epifierrors.ErrPermanent, "phone number does not exist in loan applicant")
		}

		allOtps := make([]*preapprovedloan.OtpVerificationData_OtpData, 0)
		portfolioFetchDetails := lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails()
		if portfolioFetchDetails.GetFetchMfcPortfolio() {
			allOtps = append(allOtps, p.mfCentralOtpData(phoneNumber, email, lr)...)
		}

		startDigit := 1
		if len(allOtps) > 0 {
			startDigit = 2
		}
		if portfolioFetchDetails.GetFetchFiftyfinPortfolio() {
			if email == "" {
				logger.Error("email id does not exist in loan applicant")
				return nil, errors.Wrap(epifierrors.ErrPermanent, "email id does not exist in loan applicant")
			}
			allOtps = append(allOtps, &preapprovedloan.OtpVerificationData_OtpData{
				Token:       uuid.New().String(),
				OtpType:     preapprovedloan.OtpType_OTP_TYPE_CAMS_PF_FETCH,
				OtpStatus:   preapprovedloan.OtpStatus_OTP_STATUS_UNSPECIFIED,
				Email:       email,
				OtpSerialNo: int32(startDigit),
			}, &preapprovedloan.OtpVerificationData_OtpData{
				Token:       uuid.New().String(),
				OtpType:     preapprovedloan.OtpType_OTP_TYPE_KARVY_PF_FETCH,
				OtpStatus:   preapprovedloan.OtpStatus_OTP_STATUS_UNSPECIFIED,
				PhoneNumber: phoneNumber,
				OtpSerialNo: int32(startDigit) + 1,
			})
		}

		// update the details in loan step execution
		lse.Details = &preapprovedloan.LoanStepExecutionDetails{
			Details: &preapprovedloan.LoanStepExecutionDetails_OtpVerificationData{
				OtpVerificationData: &preapprovedloan.OtpVerificationData{
					OtpData: allOtps,
				},
			},
		}

		updateErr := p.loanStepExecutionDao.Update(ctx, lse, []preapprovedloan.LoanStepExecutionFieldMask{preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
		if updateErr != nil {
			logger.Error("error in updating otp info in LSE details", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating otp info in LSE details: %v", updateErr.Error()))
		}

		// for new portfolio fetch, set the next action to otp screen, client will make the rpc call to get the screen options for this
		updateErr = p.updateNextActionInLoanRequest(ctx, req.GetRequestHeader().GetClientReqId(), &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_CONFIRMATION_VIA_OTP_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
				PreApprovedLoanApplicationConfirmationViaOtpScreenOptions: &deeplinkPb.PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
					LoanRequestId: lr.GetId(),
					LoanHeader: &palFeEnums.LoanHeader{
						LoanProgram: palFeEnums.LoanProgram_LOAN_PROGRAM_LAMF,
						Vendor:      palFeEnums.Vendor_FIFTYFIN,
					},
					LoanStepExecutionId: lse.GetId(),
					DoAutoRead:          true,
				},
			},
		})
		if updateErr != nil {
			logger.Error("error in updating LR next action", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action: %v", updateErr.Error()))
		}
		return actRes, nil
	})
}

func (p *Processor) mfCentralOtpData(phone *commontypes.PhoneNumber, email string, lr *preapprovedloan.LoanRequest) []*preapprovedloan.OtpVerificationData_OtpData {
	firstOtpId := p.uuidGen.GenerateUuid()
	otpData := []*preapprovedloan.OtpVerificationData_OtpData{
		{
			Token:       firstOtpId,
			OtpType:     preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH,
			OtpStatus:   preapprovedloan.OtpStatus_OTP_STATUS_UNSPECIFIED,
			PhoneNumber: phone,
			OtpSerialNo: 1,
		},
	}
	if !lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails().GetUserEmailInputInferred() {
		// In phase 3.1, since we don't take email as input, we will skip fetching cas detailed via email in case it failed via phone.
		// For old cases we still want to fetch cas detailed via email in case of failure to fetch via phone.
		otpData = append(otpData, &preapprovedloan.OtpVerificationData_OtpData{
			Token:     p.uuidGen.GenerateUuid(),
			OtpType:   preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH,
			OtpStatus: preapprovedloan.OtpStatus_OTP_STATUS_UNSPECIFIED,
			Email:     email,
			OtpValidationCondition: notCondition(&preapprovedloan.Condition{
				Condition: &preapprovedloan.Condition_OtpStatusCondition{
					OtpStatusCondition: &preapprovedloan.OtpStatusCondition{
						OtpId: firstOtpId,
						Statuses: []preapprovedloan.OtpStatus{
							preapprovedloan.OtpStatus_OTP_STATUS_SUCCESS,
						},
					},
				},
			}),
			OtpSerialNo: 1,
		})
	}
	return otpData
}

func notCondition(baseCondition *preapprovedloan.Condition) *preapprovedloan.Condition {
	return &preapprovedloan.Condition{
		Condition: &preapprovedloan.Condition_NotCondition{
			NotCondition: &preapprovedloan.NotCondition{
				BaseCondition: baseCondition,
			},
		},
	}
}

// VerifyOtpStatus polls the status of OTP verification by the user. Once user enters the correct Otp, BE service will update the sub-status
// of Loan Request to "OTP_VERIFIED", this activity polls for this status
func (p *Processor) VerifyOtpStatus(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		if lse.GetSubStatus() == preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFIED {
			return actRes, nil
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, "waiting for otp verification by user")
	})
}

func (p *Processor) FetchMfcPortfolio(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	logger := activity.GetLogger(ctx)
	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		var (
			isSuccessfulMfFetch     = false
			mfFetchRequestSubstatus external.FailureReason
		)
		for _, otpDetails := range lse.GetDetails().GetOtpVerificationData().GetOtpData() {
			var externalId string
			if (otpDetails.GetOtpType() == preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH || otpDetails.GetOtpType() == preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH) && otpDetails.GetVendorSessionId() != "" {
				externalId = otpDetails.GetVendorSessionId()
			}
			if externalId == "" {
				continue
			}
			importStatusRes, importStatusErr := p.mfExternalOrdersClient.GetHoldingsImportStatus(ctx, &external.GetHoldingsImportStatusRequest{
				Identifier: &external.GetHoldingsImportStatusRequest_ExternalId{
					ExternalId: externalId,
				},
			})
			if rpcErr := epifigrpc.RPCError(importStatusRes, importStatusErr); rpcErr != nil && !importStatusRes.GetStatus().IsRecordNotFound() {
				logger.Error("failed to get holdings import status", zap.Error(rpcErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get holdings import status: %v", rpcErr.Error()))
			}
			if importStatusRes.GetStatus().IsRecordNotFound() {
				logger.Error("no holdings import status found for given external id", zap.String(logger2.EXTERNAL_ID, externalId))
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no holdings import status found for given external id: %s", externalId))
			}

			switch importStatusRes.GetHoldingsImportState() {
			case external.State_OTP_VERIFICATION_SUCCESSFUL:
				logger.Error("mf portfolio not fetched", zap.String(logger2.EXTERNAL_ID, externalId))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("mf portfolio not fetched: %s", externalId))
			case external.State_EXTERNAL_ORDERS_REFRESH_SUCCESSFUL:
				logger.Info("mutual funds were successfully fetched from MFC", zap.String(logger2.EXTERNAL_ID, externalId))
				isSuccessfulMfFetch = true
				continue
			case external.State_FAILED:
				logger.Info("failed to fetch mutual funds from MFC", zap.String(logger2.EXTERNAL_ID, externalId))
				mfFetchRequestSubstatus = importStatusRes.GetFailureReason()
				continue
			default:
				lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
				if err != nil {
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
				}
				fieldMask := []preapprovedloan.LoanRequestFieldMask{
					preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
					preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
					preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
				}
				lr.NextAction = lr.GetRedirectLink()
				lr.Status = preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
				lr.CompletedAt = timestamppb.New(p.time.Now())
				updateErr := p.loanRequestDao.Update(ctx, lr, fieldMask)
				if updateErr != nil {
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action: %v", updateErr.Error()))
				}
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failure in fetching mfc portfolio: %s", externalId))
			}
		}

		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}
		err = p.handlePostProcessingMfcPortfolioFetch(ctx, req, lse, lr, isSuccessfulMfFetch, mfFetchRequestSubstatus)
		if err != nil {
			logger.Error("failed to handle post processing of mfc portfolio fetch", zap.Error(err))
			return nil, fmt.Errorf("failed to handle post processing of mfc portfolio fetch: %w", err)
		}

		return actRes, nil
	})
}

// This method checks if mfc portfolio fetch was successful or not. If it was successful then we fetch the mf holdings summary and
// checks if the portfolio has sufficient funds for further processing (total portfolio value is greater than 50k or not).
// If any of the checks fails then we mark the lr and lse failed with appropriate substatus.
func (p *Processor) handlePostProcessingMfcPortfolioFetch(ctx context.Context, req *activityPb.ActivityRequest, lse *preapprovedloan.LoanStepExecution,
	lr *preapprovedloan.LoanRequest, isSuccessfulMfFetch bool, mfFetchSubstatus external.FailureReason) error {
	if !isSuccessfulMfFetch {
		var (
			lrSubstatus  preapprovedloan.LoanRequestSubStatus
			lseSubStatus preapprovedloan.LoanStepExecutionSubStatus
		)
		// TODO(Brijesh): Check and add more sub statuses if we see more reasons for failure.
		if mfFetchSubstatus == external.FailureReason_EMPTY_EXTERNAL_MF_DATA_FAILURE ||
			mfFetchSubstatus == external.FailureReason_MF_HOLDINGS_NO_TRANSACTION {
			lrSubstatus = preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND
			lseSubStatus = preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND
		} else {
			lrSubstatus = preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_DETAILED_FETCH_FAILED
			lseSubStatus = preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_DETAILED_FETCH_FAILED
		}
		err := p.markLrAndLseAsFailed(ctx, req, lse, lr, lrSubstatus, lseSubStatus)
		if err != nil {
			return fmt.Errorf("failed to mark lr and lse as failed: %w", err)
		}
		return errors.Wrap(epifierrors.ErrPermanent, "failure to fetch mfc portfolio using phone and email")
	} else {
		// If sufficient portfolio is not there then fail the portfolio fetch request and update appropriate sub status
		mfHoldingsSummaryResp, err := p.mfExternalOrdersClient.GetMFHoldingsSummary(ctx, &external.GetMFHoldingsSummaryRequest{
			ActorId: lse.GetActorId(),
		})
		if rpcErr := epifigrpc.RPCError(mfHoldingsSummaryResp, err); rpcErr != nil {
			if mfHoldingsSummaryResp.GetStatus().IsRecordNotFound() {
				err = p.markLrAndLseAsFailed(ctx, req, lse, lr,
					preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND,
					preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND)
				if err != nil {
					return fmt.Errorf("failed to mark lr and lse as failed: %w", err)
				}
				return errors.Wrap(epifierrors.ErrPermanent, "failure to fetch mfc portfolio using phone and email")
			}
			return fmt.Errorf("failed to fetch mf holdings summary: %s: %w", rpcErr, epifierrors.ErrTransient)
		}

		isSufficientPortfolio, err := p.isSufficientPortfolio(mfHoldingsSummaryResp.GetMutualFundExternalHoldingsSummaries())
		if err != nil {
			return fmt.Errorf("failed to check if user has sufficient portfolio for cas summary: %w", err)
		}
		if !isSufficientPortfolio {
			err = p.markLrAndLseAsFailed(ctx, req, lse, lr,
				preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_LOW_PORTFOLIO,
				preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE)
			if err != nil {
				return fmt.Errorf("failed to mark lr and lse as failed: %w", err)
			}
			return fmt.Errorf("low portfolio found in cas detailed statement: %w", epifierrors.ErrPermanent)
		}

		if lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails().GetUserEmailInputInferred() {
			err = p.getAndUpdateMaxPortfolioEmail(ctx, mfHoldingsSummaryResp, req, lr, lse)
			if err != nil {
				return fmt.Errorf("failed to get and update max portfolio email: %w", err)
			}
		}
	}

	return nil
}

func (p *Processor) getAndUpdateMaxPortfolioEmail(ctx context.Context, mfHoldingsSummaryResp *external.GetMFHoldingsSummaryResponse,
	req *activityPb.ActivityRequest, lr *preapprovedloan.LoanRequest, lse *preapprovedloan.LoanStepExecution) error {
	email := p.getEmailWithMaxFolioValue(mfHoldingsSummaryResp.GetMutualFundExternalHoldingsSummaries())
	if email == "" {
		err := p.markLrAndLseAsFailed(ctx, req, lse, lr,
			preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_EMAIL_SELECTION_FAILURE,
			preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_SELECTION_FAILURE)
		if err != nil {
			return fmt.Errorf("failed to mark lr and lse failed: %w", err)
		}
		return fmt.Errorf("failed to get email linked with max folio amount: %w", epifierrors.ErrPermanent)
	}

	ow := helper.GetPalOwnership(lr.GetVendor())
	txnExecutor, txnExecErr := p.txnExecutorProvider.GetResourceForOwnership(ow)
	if txnExecErr != nil {
		return fmt.Errorf("failed to get txn executor Provider: %s: %w", txnExecErr, epifierrors.ErrTransient)
	}

	la, laErr := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lse.GetActorId(), req.GetVendor(), req.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
	if laErr != nil {
		return fmt.Errorf("failed to get loan applicant for lamf: %s: %w", laErr, epifierrors.ErrTransient)
	}
	// not update is needed if old email is same as new email
	if la.GetPersonalDetails().GetEmailId() == email {
		return nil
	}

	// save to loan applicant and deactivate old offer
	if la.GetPersonalDetails() != nil {
		la.GetPersonalDetails().EmailId = strings.ToLower(email)
	} else if la.GetPersonalDetails() == nil {
		la.PersonalDetails = &preapprovedloan.PersonalDetails{
			EmailId: strings.ToLower(email),
		}
	}
	txnErr := txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err := p.loanApplicantDao.Update(txnCtx, la, []preapprovedloan.LoanApplicantFieldMask{preapprovedloan.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS})
		if err != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating email in loan applicant: %v", err.Error()))
		}
		err = p.deactivateActiveLamfLoanOffers(txnCtx, lr.GetActorId())
		if err != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in deactivating active lamf loan offers: %v", err.Error()))
		}
		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("failed to update loan applicant and deactivate active loan offers: %w", txnErr)
	}

	return nil
}

func (p *Processor) isSufficientPortfolio(mfExternalHoldingsSummaryList []*external.MutualFundExternalHoldingsSummary) (bool, error) {
	var totalAmount = &moneyPb.Money{CurrencyCode: "INR"}
	for _, holdingsSummary := range mfExternalHoldingsSummaryList {
		var err error
		totalAmount, err = money.Sum(totalAmount, holdingsSummary.GetMarketValue())
		if err != nil {
			return false, fmt.Errorf("failed to sum holdings summary market value: %s, %w", err, epifierrors.ErrTransient)
		}
	}
	totalAmountPaise, err := money.ToPaise(totalAmount)
	if err != nil {
		return false, fmt.Errorf("failed to convert money to paise: %s: %w", err, epifierrors.ErrTransient)
	}
	if (totalAmountPaise / 100) > PortfolioMinAmountForCASSummaryInRupees {
		return true, nil
	}
	return false, nil
}

func (p *Processor) markLrAndLseAsFailed(
	ctx context.Context, req *activityPb.ActivityRequest,
	lse *preapprovedloan.LoanStepExecution, lr *preapprovedloan.LoanRequest,
	lrSubstatus preapprovedloan.LoanRequestSubStatus, lseSubStatus preapprovedloan.LoanStepExecutionSubStatus,
) error {
	if lr == nil {
		var err error
		lr, err = p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}
	}
	fieldMask := []preapprovedloan.LoanRequestFieldMask{
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	}
	lr.NextAction = lr.GetRedirectLink()
	lr.Status = preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
	lr.SubStatus = lrSubstatus
	lr.CompletedAt = timestamppb.New(p.time.Now())
	updateErr := p.loanRequestDao.Update(ctx, lr, fieldMask)
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update failed status and next action of LR: %v", updateErr.Error()))
	}

	lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
	lse.SubStatus = lseSubStatus
	lseFieldMasks := []preapprovedloan.LoanStepExecutionFieldMask{
		preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	}
	lseUpdateErr := p.loanStepExecutionDao.Update(ctx, lse, lseFieldMasks)
	if lseUpdateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update failed status of lse: %v", lseUpdateErr.Error()))
	}

	return nil
}

func (p *Processor) getEmailWithMaxFolioValue(mfExternalHoldingsSummaryList []*external.MutualFundExternalHoldingsSummary) string {
	var emailToPortfolioValueMap = make(map[string]float64)
	for _, mfHolding := range mfExternalHoldingsSummaryList {
		if mfHolding.GetEmail() == "" {
			continue
		}
		marketValue, _ := money.ToDecimal(mfHolding.GetMarketValue()).Float64()
		emailToPortfolioValueMap[mfHolding.GetEmail()] += marketValue
	}

	var selectedEmail string
	var maxLoanAmount float64 = -1
	for email, portfolioValue := range emailToPortfolioValueMap {
		if maxLoanAmount < portfolioValue {
			maxLoanAmount = portfolioValue
			selectedEmail = email
		}
	}
	return selectedEmail
}
