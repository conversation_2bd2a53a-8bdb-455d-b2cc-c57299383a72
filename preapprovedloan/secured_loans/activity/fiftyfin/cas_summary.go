package fiftyfin

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	activityPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans/activity"
	activity2 "github.com/epifi/gamma/preapprovedloan/activity"
)

func (p *Processor) FetchMfcCasSummaryPortfolio(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	lg := activity.GetLogger(ctx)
	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		externalId, err := p.getSuccessfulMfcExternalId(ctx, lse)
		if err != nil {
			lg.Error("failed to get successful mfc external id", zap.Error(err))
			if errors.Is(err, epifierrors.ErrPermanent) {
				updateErr := p.markLrAndLseAsFailed(ctx, req, lse, nil,
					preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED,
					preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
				)
				if updateErr != nil {
					lg.Error("failed to mark lr and lse as failed", zap.Error(updateErr))
					return nil, fmt.Errorf("failed to mark lr and lse as failed: %w", updateErr)
				}
			}
			return nil, fmt.Errorf("failed to get successful mfc external id: %w", err)
		}

		lrSubStatus, lseSubStatus, err := p.initiateAndWaitForCasSummaryTerminalResponse(ctx, lse, externalId)
		if err != nil {
			lg.Error("failed to initiate and wait for cas summary terminal response", zap.Error(err))
			if errors.Is(err, epifierrors.ErrPermanent) {
				updateErr := p.markLrAndLseAsFailed(ctx, req, lse, nil, lrSubStatus, lseSubStatus)
				if updateErr != nil {
					lg.Error("failed to mark lr and lse as failed", zap.Error(updateErr))
					return nil, fmt.Errorf("failed to mark lr and lse as failed: %w", updateErr)
				}
			}
			return nil, fmt.Errorf("failed to initiate and wait for cas summary terminal response: %w", err)
		}

		return actRes, nil
	})
}

// This method iterates over all the otps details and returns the external id for the mfc otp fetch which was successful.
func (p *Processor) getSuccessfulMfcExternalId(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (string, error) {
	lg := activity.GetLogger(ctx)
	var successfulExternalId string
	for _, otpDetails := range lse.GetDetails().GetOtpVerificationData().GetOtpData() {
		var externalId string
		if (otpDetails.GetOtpType() == preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH ||
			otpDetails.GetOtpType() == preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH) &&
			otpDetails.GetVendorSessionId() != "" {
			externalId = otpDetails.GetVendorSessionId()
		}
		if externalId == "" {
			continue
		}
		importStatusRes, importStatusErr := p.mfExternalOrdersClient.GetHoldingsImportStatus(ctx, &external.GetHoldingsImportStatusRequest{
			Identifier: &external.GetHoldingsImportStatusRequest_ExternalId{
				ExternalId: externalId,
			},
		})
		if rpcErr := epifigrpc.RPCError(importStatusRes, importStatusErr); rpcErr != nil && !importStatusRes.GetStatus().IsRecordNotFound() {
			return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get holdings import status: %v", rpcErr.Error()))
		}
		if importStatusRes.GetStatus().IsRecordNotFound() {
			lg.Error("no holdings import status found for given external id, skipping", zap.String(logger.EXTERNAL_ID, externalId))
			continue
		}

		var isSuccessful bool
		switch importStatusRes.GetHoldingsImportState() {
		case external.State_EXTERNAL_ORDERS_REFRESH_SUCCESSFUL:
			isSuccessful = true
			break
		}
		if isSuccessful {
			successfulExternalId = externalId
			break
		}
	}
	if successfulExternalId == "" {
		return "", fmt.Errorf("unable to find successful mf portfolio fetch external id: %w", epifierrors.ErrPermanent)
	}
	return successfulExternalId, nil
}

func (p *Processor) initiateAndWaitForCasSummaryTerminalResponse(
	ctx context.Context,
	lse *preapprovedloan.LoanStepExecution,
	externalId string,
) (preapprovedloan.LoanRequestSubStatus, preapprovedloan.LoanStepExecutionSubStatus, error) {
	la, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lse.GetActorId(), preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get loan applicant: %s: %w", err, epifierrors.ErrTransient)
	}

	casErr := p.getCasSummary(ctx, &external.FetchAdditionalCasStatementRequest{
		ActorId:                     lse.GetActorId(),
		PrimaryStatementClientReqId: externalId,
		ContactIdentifier: &external.FetchAdditionalCasStatementRequest_PhoneNumber{
			PhoneNumber: la.GetPersonalDetails().GetPhoneNumber(),
		},
		StatementType: external.CasStatementType_CAS_STATEMENT_TYPE_SUMMARY,
	})
	if casErr != nil {
		return preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED,
			preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED,
			fmt.Errorf("failure in fetching phone cas summary: %w", casErr)
	}

	return 0, 0, nil
}

func (p *Processor) getCasSummary(ctx context.Context, fetchAdditionalCasStatementReq *external.FetchAdditionalCasStatementRequest) error {
	casSummaryRes, err := p.mfExternalOrdersClient.FetchAdditionalCasStatement(ctx, fetchAdditionalCasStatementReq)
	if rpcErr := epifigrpc.RPCError(casSummaryRes, err); rpcErr != nil {
		return fmt.Errorf("failure in FetchAdditionalCasStatement: %w", epifierrors.ErrTransient)
	}

	switch casSummaryRes.GetImportStatus() {
	case external.MfSingleOtpCasImportStatus_MF_SINGLE_OTP_CAS_IMPORT_STATUS_FAILED:
		return fmt.Errorf("failed to fetch cas summary: %w", epifierrors.ErrPermanent)
	case external.MfSingleOtpCasImportStatus_MF_SINGLE_OTP_CAS_IMPORT_STATUS_SUCCESS:
		break
	default:
		return fmt.Errorf("cas summary fetch in progress: %w", epifierrors.ErrTransient)
	}

	return nil
}
