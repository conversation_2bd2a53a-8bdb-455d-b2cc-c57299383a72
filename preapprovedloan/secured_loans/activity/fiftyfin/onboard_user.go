// nolint:gosec,dupl,funlen
package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	logger2 "github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	activityPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans/activity"
	userPb "github.com/epifi/gamma/api/user"
	ffVgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	activity2 "github.com/epifi/gamma/preapprovedloan/activity"
)

// SetUserDetailsScreen activity sets the next action to update details screen
func (p *Processor) SetUserDetailsScreen(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	logger := activity.GetLogger(ctx)
	res, err := activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}

		phone, email, pan, err := p.getUserDetailsForUpdatePfScreen(ctx, lr)
		if err != nil {
			logger.Error("error in getting user phone and email", zap.Error(err))
			return nil, err
		}
		if pan == "" {
			logger.Error("pan does not exist for actor", zap.String(logger2.ACTOR_ID_V2, lr.GetActorId()), zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("pan does not exist for actor: %s", lr.GetActorId()))
		}

		// We will not take email input from user in case the flag is true.
		if lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails().GetUserEmailInputInferred() {
			email = ""
		}
		nextAction := p.fiftyFinDlProvider.GetConfirmPfAccountScreen(p.fiftyFinDlProvider.GetLoanHeader(), lr.GetId(), lse.GetId(), phone, email, pan)

		// set next action to update user details screen
		updateErr := p.updateNextActionInLoanRequest(ctx, req.GetRequestHeader().GetClientReqId(), nextAction)
		if updateErr != nil {
			logger.Error("error in updating LR next action", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action: %v", updateErr.Error()))
		}

		return &activityPb.ActivityResponse{}, nil
	})
	return res, err
}

func (p *Processor) getUserDetailsForUpdatePfScreen(ctx context.Context, lr *preapprovedloan.LoanRequest) (*commontypes.PhoneNumber, string, string, error) {
	user, err1 := p.rpcHelper.GetUserByActorId(ctx, lr.GetActorId())
	if err1 != nil {
		return nil, "", "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching user details from user service : %s", err1))
	}

	// check if user is already registered at vendor's end
	la, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
	switch {
	case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, "", "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting loan applicant: %v", err.Error()))
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, "", "", errors.Wrap(epifierrors.ErrPermanent, "loan applicant record not found")
	}

	var (
		email = la.GetPersonalDetails().GetEmailId()
		phone = la.GetPersonalDetails().GetPhoneNumber()
		pan   = user.GetProfile().GetPAN()
	)
	if email == "" {
		email = user.GetProfile().GetEmail()
	}
	if phone == nil {
		phone = user.GetProfile().GetPhoneNumber()
	}
	return phone, email, pan, nil
}

func (p *Processor) fetchUserDetailsFromVendor(ctx context.Context, userId string) (*ffVgPb.FetchUserResponse_UserData, error) {
	intUserId, err := p.convertStringToInt32(userId)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error converting userId to int"))
	}
	userDetails, err := p.ffVgClient.FetchUser(ctx, &ffVgPb.FetchUserRequest{
		IdType: ffVgPb.IdType_ID_TYPE_USER_ID,
		UserId: intUserId,
	})
	if rpcErr := epifigrpc.RPCError(userDetails, err); rpcErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching user details from vendor : %s", rpcErr))
	}
	return userDetails.GetUserData(), nil
}

func (p *Processor) fetchDefaultUserData(ctx context.Context, actorId string) (*userPb.Profile, error) {
	user, err := p.rpcHelper.GetUserByActorId(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching user details from user service : %s", err))
	}
	return user.GetProfile(), nil
}

// CheckUserDetailsStatus activity polls for the loan_request status to check whether user has entered the details or not.
func (p *Processor) CheckUserDetailsStatus(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	logger := activity.GetLogger(ctx)

	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %s", err.Error()))
		}

		if lr.GetSubStatus() == preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED {
			return &activityPb.ActivityResponse{}, nil
		}

		return nil, errors.Wrap(epifierrors.ErrTransient, "waiting for user to verify phone and email")
	})
}

// UpdateUserDetails activity updates/creates the user details at vendor's end.
// it checks if entry is already present in loan_applicant table, if not it will create user at vendor's end and also create entry in db table
// if entry already found, it will update the user details at vendor's end
func (p *Processor) UpdateUserDetails(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	logger := activity.GetLogger(ctx)

	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}

		// check if user is already registered at vendor's end
		la, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
		switch {
		case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error("error in getting loan applicant", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting loan applicant: %v", err.Error()))
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return nil, errors.Wrap(epifierrors.ErrPermanent, "loan applicant record not found")
		case la.GetSubStatus() == preapprovedloan.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI:
			onboardUserErr := p.onboardUser(ctx, la, lr)
			if onboardUserErr != nil {
				logger.Error("error in user signup process", zap.Error(onboardUserErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in onboarding user at vendor's end: %v", onboardUserErr.Error()))
			}
			// user onboarded successfully, return from here
			return actRes, nil
		}

		// loan applicant found, user was already registered at vendor's end, call the update flow
		updateUserErr := p.updateUserDetails(ctx, la.GetVendorApplicantId(), lr)
		if updateUserErr != nil {
			logger.Error("error in updating user", zap.Error(updateUserErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating user at vendor's end: %v", updateUserErr.Error()))
		}
		return actRes, nil
	})
}

// FiftyfinUpdateUserDetailsInLoanApplicant activity updates user details from LSE to loan applicant.
func (p *Processor) FiftyfinUpdateUserDetailsInLoanApplicant(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	logger := activity.GetLogger(ctx)
	res, err := activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}

		// check if user is already registered at vendor's end
		la, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
		switch {
		case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error("error in getting loan applicant", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting loan applicant: %v", err.Error()))
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error("loan applicant record not found")
			return nil, errors.Wrap(epifierrors.ErrPermanent, "loan applicant record not found")
		}

		oldPhone, oldEmail := la.GetPersonalDetails().GetPhoneNumber(), la.GetPersonalDetails().GetEmailId()
		newPhone, newEmail := lse.GetDetails().GetApplicantData().GetPhoneNumber(), lse.GetDetails().GetApplicantData().GetEmail()
		isPhoneUpdateRequired := newPhone.GetNationalNumber() != oldPhone.GetNationalNumber()
		isEmailUpdateRequired := newEmail != oldEmail
		if newPhone == nil {
			return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("user phone number is not present in lse for loan request id: %v", lr.GetId()))
		}
		if lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails().GetUserEmailInputInferred() {
			isEmailUpdateRequired = false
			newEmail = oldEmail
		}

		if !isEmailUpdateRequired && !isPhoneUpdateRequired {
			return &activityPb.ActivityResponse{}, nil
		}

		la.PersonalDetails = &preapprovedloan.PersonalDetails{
			PhoneNumber: newPhone,
			EmailId:     newEmail,
		}
		err = p.loanApplicantDao.Update(ctx, la, []preapprovedloan.LoanApplicantFieldMask{preapprovedloan.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS})
		if err != nil {
			logger.Error("error in updating phone number and email in loan applicant", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating phone number and email in loan applicant: %v", err.Error()))
		}

		// expire offer because once the phone number and email are updated, these will be used in any further loan application process as well.
		// And if loan offer was generated with different phone and email, then it will cause data mismatch problems in loan application.
		err = p.deactivateActiveLamfLoanOffers(ctx, lse.GetActorId())
		if err != nil {
			logger.Error("failed to deactivate active lamf loan offers", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to deactivate active lamf loan offers: %v", err.Error()))
		}

		return &activityPb.ActivityResponse{}, nil
	})

	return res, err
}

func (p *Processor) onboardUser(ctx context.Context, la *preapprovedloan.LoanApplicant,
	lr *preapprovedloan.LoanRequest) error {
	user, err := p.rpcHelper.GetUserByActorId(ctx, lr.GetActorId())
	if err != nil {
		return err
	}

	// fetch the lse to get the data given by the user
	lse, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lr.GetId(),
		preapprovedloan.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
		preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error in getting lse for applicant creation"))
	}

	// vendor call for user signup
	vgRes, err := p.ffVgClient.UserSignup(ctx, &ffVgPb.UserSignupRequest{
		Name:        user.GetProfile().GetPanName(),
		Email:       lse.GetDetails().GetApplicantData().GetEmail(),
		PhoneNumber: lse.GetDetails().GetApplicantData().GetPhoneNumber(),
		Dob:         user.GetProfile().GetDateOfBirth(),
	})
	var (
		dl           *deeplink.Deeplink
		permanentErr bool
		subStatus    preapprovedloan.LoanRequestSubStatus
	)
	switch {
	case vgRes.GetStatus().GetCode() == uint32(ffVgPb.UserSignupResponse_PHONE_NUMBER_ALREADY_EXISTS) ||
		vgRes.GetStatus().GetCode() == uint32(ffVgPb.UserSignupResponse_PHONE_AND_EMAIL_MISMATCH):
		permanentErr = true
		subStatus = preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED
		dl, err = p.fiftyFinDlProvider.GetPhoneNumberConflictErrorScreen()
		if err != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating phone conflict error deeplink : %s", err.Error()))
		}
	case vgRes.GetStatus().GetCode() == uint32(ffVgPb.UserSignupResponse_EMAIl_ALREADY_ALREADY_EXISTS):
		permanentErr = true
		subStatus = preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED
		dl, err = p.fiftyFinDlProvider.GetEmailConflictErrorScreen()
		if err != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating email conflict error deeplink : %s", err.Error()))
		}
	case err != nil || !vgRes.GetStatus().IsSuccess():
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update user's details"))
	default:
		break
	}
	if permanentErr {
		if dl != nil {
			lr.NextAction = dl
			lr.SubStatus = subStatus
			updateErr := p.loanRequestDao.Update(ctx, lr, []preapprovedloan.LoanRequestFieldMask{
				preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
			})
			if updateErr != nil {
				return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateErr))
			}
		}
		return errors.Wrap(epifierrors.ErrPermanent, "details match with existing user")
	}
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		return errors.Wrap(te, fmt.Sprintf("failed in user signup vg call"))
	}

	// link the PAN of the user, not using CheckPanStatus explicitly as LinkPan API is idempotent at vendor's end
	panRes, err := p.ffVgClient.LinkPan(ctx, &ffVgPb.LinkPanRequest{
		UserId:  vgRes.GetData().GetUserId(),
		Pan:     user.GetProfile().GetPAN(),
		PanName: user.GetProfile().GetPanName().ToString(),
		Dob:     user.GetProfile().GetDateOfBirth(),
	})
	if te := epifigrpc.RPCError(panRes, err); te != nil {
		return errors.Wrap(te, fmt.Sprintf("failed to link user's PAN at vendor end"))
	}

	la.SubStatus = preapprovedloan.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR
	la.VendorApplicantId = fmt.Sprintf("%d", vgRes.GetData().GetUserId())

	// create an entry in loan applicants table
	err = p.loanApplicantDao.Update(ctx, la, []preapprovedloan.LoanApplicantFieldMask{
		preapprovedloan.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_SUB_STATUS,
		preapprovedloan.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID,
	})
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan applicant details : %s", err))
	}

	return nil
}

func (p *Processor) updateUserDetails(ctx context.Context, vendorApplicantId string, lr *preapprovedloan.LoanRequest) error {
	userId, err := strconv.Atoi(vendorApplicantId)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in converting userId string to int: %v", err.Error()))
	}

	// fetch user to verify if user is registered at vendor's end
	vgRes, err := p.ffVgClient.FetchUser(ctx, &ffVgPb.FetchUserRequest{
		IdType: ffVgPb.IdType_ID_TYPE_USER_ID,
		UserId: int32(userId),
	})
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user : %s", te.Error()))
	}

	// fetch the lse to get the data given by the user
	lse, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lr.GetId(),
		preapprovedloan.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
		preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting lse for applicant creation : %s", err.Error()))
	}

	oldPhone, oldEmail := vgRes.GetUserData().GetPhoneNumber(), vgRes.GetUserData().GetEmail()
	newPhone, newEmail := lse.GetDetails().GetApplicantData().GetPhoneNumber(), lse.GetDetails().GetApplicantData().GetEmail()

	isPhoneUpdateRequired := newPhone.GetNationalNumber() != oldPhone.GetNationalNumber()
	isEmailUpdateRequired := newEmail != oldEmail

	if !isEmailUpdateRequired && !isPhoneUpdateRequired {
		return nil
	}

	// mark the current loan offer (if any) as deactivated/expired as we are updating user details and need to fetch the portfolio again
	lo, err := p.loanOfferDao.GetActiveOfferByActorIdAndVendor(ctx, lr.GetActorId(), preapprovedloan.Vendor_FIFTYFIN)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get active loan offer for the actor: %v", err.Error()))
	}
	if lo != nil {
		lo.DeactivatedAt = timestamppb.New(time.Now())
		updateErr := p.loanOfferDao.Update(ctx, lo, []preapprovedloan.LoanOfferFieldMask{preapprovedloan.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT})
		if updateErr != nil {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to deactivate loan offer %v", err.Error()))
		}
	}

	var (
		emailForUpdate string
		phoneForUpdate *commontypes.PhoneNumber
		dl             *deeplink.Deeplink
		subStatus      preapprovedloan.LoanRequestSubStatus
	)

	if isEmailUpdateRequired {
		emailForUpdate = newEmail
	}
	if isPhoneUpdateRequired {
		phoneForUpdate = newPhone
	}

	updateRes, err := p.ffVgClient.UpdateUserBulk(ctx, &ffVgPb.UpdateUserBulkRequest{
		UserId: vendorApplicantId,
		Phone:  phoneForUpdate,
		Email:  emailForUpdate,
	})
	switch {
	case updateRes.GetStatus().GetCode() == uint32(ffVgPb.UpdateUserBulkResponse_EMAIL_ALREADY_EXISTS):
		dl, err = p.fiftyFinDlProvider.GetEmailConflictErrorScreen()
		subStatus = preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED
	case updateRes.GetStatus().GetCode() == uint32(ffVgPb.UpdateUserBulkResponse_PHONE_NUMBER_ALREADY_EXISTS):
		dl, err = p.fiftyFinDlProvider.GetPhoneNumberConflictErrorScreen()
		subStatus = preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED
	case err != nil || !updateRes.GetStatus().IsSuccess():
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update user's details"))
	default:
		return nil
	}
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating partial match error deeplink : %s", err.Error()))
	}
	lr.NextAction = dl
	lr.SubStatus = subStatus
	updateErr := p.loanRequestDao.Update(ctx, lr, []preapprovedloan.LoanRequestFieldMask{
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
	})
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateErr))
	}
	return errors.Wrap(epifierrors.ErrPermanent, "details match with existing user")
}

func (p *Processor) deactivateActiveLamfLoanOffers(ctx context.Context, actorId string) error {
	offers, err := p.loanOfferDao.GetActiveOffersByActorIdAndLoanPrograms(ctx, actorId, []preapprovedloan.LoanProgram{preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("error while fetching active offers for lamf: %s: %w", err, epifierrors.ErrTransient)
	}
	for _, offer := range offers {
		// In the future, we might have different vendor other than FIFTYFIN for LAMF
		// so to avoid deactivating those offers, this check has been added
		if offer.GetVendor() != preapprovedloan.Vendor_FIFTYFIN {
			continue
		}
		err = p.loanOfferDao.DeactivateLoanOffer(ctx, offer.GetId())
		if err != nil {
			return fmt.Errorf("error while deactivating past offer with id: %s, err: %s: %w", offer.GetId(), err, epifierrors.ErrTransient)
		}
	}
	return nil
}
