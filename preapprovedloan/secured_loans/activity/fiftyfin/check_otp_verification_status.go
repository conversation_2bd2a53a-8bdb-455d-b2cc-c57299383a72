package fiftyfin

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	mfExternalOrderPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	activityPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans/activity"
	activity2 "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/dao/filters"
)

const (
	MfCentralCasSummaryPhoneUserIdentifier = "MF_CENTRAL_CAS_SUMMARY_PHONE"
	MfCentralCasSummaryEmailUserIdentifier = "MF_CENTRAL_CAS_SUMMARY_EMAIL"
)

func (p *Processor) VerifyOtpSuccess(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	logger := activity.GetLogger(ctx)

	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}
		lr.Status = preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED
		updateErr := p.loanRequestDao.Update(ctx, lr, []preapprovedloan.LoanRequestFieldMask{
			preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		})
		if updateErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action: %v", updateErr.Error()))
		}
		err = p.handleOtpVerificationFailure(ctx, lr, lse)
		if err != nil {
			return nil, errors.Wrap(err, "error handling otp verification failure")
		}
		return actRes, nil
	})
}

func (p *Processor) ProcessPortfolioFetchCompletion(ctx context.Context, req *activityPb.ActivityRequest) (*activityPb.ActivityResponse, error) {
	actRes := &activityPb.ActivityResponse{}
	logger := activity.GetLogger(ctx)

	return activity2.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *preapprovedloan.LoanStepExecution) (*activityPb.ActivityResponse, error) {
		lr, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %v", err.Error()))
		}
		// fetch the loan applicant to get the user id registered at vendor's end
		la, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), preapprovedloan.Vendor_FIFTYFIN, preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
		if err != nil {
			logger.Error("error in getting loan applicant by actorId", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan applicant: %v", err.Error()))
		}
		pfFetchDetails := lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails()
		txnExec, err := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
		if err != nil {
			logger.Error("error in GetTxnExecutorByOwnership", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in GetTxnExecutorByOwnership : %s", err.Error()))
		}
		err = txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
			var txnErr error
			if pfFetchDetails.GetFetchMfcPortfolio() {
				txnErr = p.createMfcFetchedAssetEntry(txnCtx, lr, lse, la)
				if txnErr != nil {
					return fmt.Errorf("error in createMfcFetchedAssetEntry : %w", txnErr)
				}
			}
			if pfFetchDetails.GetFetchMfcCasSummaryPortfolio() {
				txnErr = p.createMfcCasSummaryFetchedAssetEntries(txnCtx, lr, lse, la)
				if txnErr != nil {
					return fmt.Errorf("error in createMfcCasSummaryFetchedAssetEntries : %w", txnErr)
				}
			}
			if pfFetchDetails.GetFetchFiftyfinPortfolio() {
				txnErr = p.createFiftyfinFetchedAssetEntry(txnCtx, lr, lse, la)
				if txnErr != nil {
					return fmt.Errorf("error in createFiftyfinFetchedAssetEntry : %w", txnErr)
				}
			}
			// data stored in fetched asset, mark the loan step to success
			lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			txnErr = p.loanStepExecutionDao.Update(txnCtx, lse, []preapprovedloan.LoanStepExecutionFieldMask{
				preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			})
			if txnErr != nil {
				logger.Error("error in updating lse to success", zap.Error(txnErr))
			}
			txnErr = p.updateNextActionInLoanRequest(txnCtx, lr.GetOrchId(), lr.GetRedirectLink())
			if txnErr != nil {
				return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action: %v", txnErr.Error()))
			}
			return nil
		})
		if err != nil {
			logger.Error("error while creating fetched asset entries", zap.Error(err))
			return nil, err
		}
		return actRes, nil
	})
}

func (p *Processor) createFiftyfinFetchedAssetEntry(ctx context.Context, lr *preapprovedloan.LoanRequest, lse *preapprovedloan.LoanStepExecution, la *preapprovedloan.LoanApplicant) error {
	camsOtpData, found := lo.Find(lse.GetDetails().GetOtpVerificationData().GetOtpData(), func(item *preapprovedloan.OtpVerificationData_OtpData) bool {
		return preapprovedloan.OtpType_OTP_TYPE_CAMS_PF_FETCH == item.GetOtpType()
	})
	if !found {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("didn't find otp verification data for cams"))
	}
	karvyOtpData, found := lo.Find(lse.GetDetails().GetOtpVerificationData().GetOtpData(), func(item *preapprovedloan.OtpVerificationData_OtpData) bool {
		return preapprovedloan.OtpType_OTP_TYPE_KARVY_PF_FETCH == item.GetOtpType()
	})
	if !found {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("didn't find otp verification data for karvy"))
	}
	_, createErr := p.fetchedAssetDao.Create(ctx, &preapprovedloan.FetchedAsset{
		ActorId:             lr.GetActorId(),
		Vendor:              lr.GetVendor(),
		AssetType:           preapprovedloan.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       la.GetVendorApplicantId(),
		UserAssetIdentifier: preapprovedloan.Vendor_FIFTYFIN.String(),
		Details: &preapprovedloan.FetchedAssetDetails{
			Details: &preapprovedloan.FetchedAssetDetails_FiftyFinLamfDetails{
				FiftyFinLamfDetails: &preapprovedloan.FiftyFinLamfDetails{
					CamsSessionId:  camsOtpData.GetVendorSessionId(),
					KarvySessionId: karvyOtpData.GetVendorSessionId(),
				},
			},
		},
		FetchedAt: timestamppb.New(time.Now()),
	}, filters.WithOnConflictClause())
	if createErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in creating fetched asset: %v", createErr.Error()))
	}
	return nil
}

func (p *Processor) createMfcFetchedAssetEntry(ctx context.Context, lr *preapprovedloan.LoanRequest, _ *preapprovedloan.LoanStepExecution, la *preapprovedloan.LoanApplicant) error {
	_, createErr := p.fetchedAssetDao.Create(ctx, &preapprovedloan.FetchedAsset{
		ActorId:             lr.GetActorId(),
		Vendor:              lr.GetVendor(),
		AssetType:           preapprovedloan.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       la.GetVendorApplicantId(),
		UserAssetIdentifier: preapprovedloan.Vendor_VENDOR_MF_CENTRAL.String(),
		Details: &preapprovedloan.FetchedAssetDetails{
			Details: &preapprovedloan.FetchedAssetDetails_FiftyFinLamfDetails{},
		},
		FetchedAt: timestamppb.New(time.Now()),
	}, filters.WithOnConflictClause())
	if createErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in creating fetched asset: %v", createErr.Error()))
	}
	return nil
}

func (p *Processor) createMfcCasSummaryFetchedAssetEntries(ctx context.Context, lr *preapprovedloan.LoanRequest, _ *preapprovedloan.LoanStepExecution, la *preapprovedloan.LoanApplicant) error {
	_, createErr := p.fetchedAssetDao.Create(ctx, &preapprovedloan.FetchedAsset{
		ActorId:             lr.GetActorId(),
		Vendor:              lr.GetVendor(),
		AssetType:           preapprovedloan.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       la.GetVendorApplicantId(),
		UserAssetIdentifier: fmt.Sprintf("%s_%s", MfCentralCasSummaryPhoneUserIdentifier, la.GetPersonalDetails().GetPhoneNumber().ToString()),
		Details: &preapprovedloan.FetchedAssetDetails{
			Details: &preapprovedloan.FetchedAssetDetails_FiftyFinLamfDetails{},
		},
		FetchedAt: timestamppb.New(p.time.Now()),
	}, filters.WithOnConflictClause())
	if createErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in creating fetched asset for cas summary phone: %v", createErr.Error()))
	}

	_, createErr = p.fetchedAssetDao.Create(ctx, &preapprovedloan.FetchedAsset{
		ActorId:             lr.GetActorId(),
		Vendor:              lr.GetVendor(),
		AssetType:           preapprovedloan.AssetType_ASSET_TYPE_MUTUAL_FUNDS,
		VendorAssetId:       la.GetVendorApplicantId(),
		UserAssetIdentifier: fmt.Sprintf("%s_%s", MfCentralCasSummaryEmailUserIdentifier, la.GetPersonalDetails().GetEmailId()),
		Details: &preapprovedloan.FetchedAssetDetails{
			Details: &preapprovedloan.FetchedAssetDetails_FiftyFinLamfDetails{},
		},
		FetchedAt: timestamppb.New(p.time.Now()),
	}, filters.WithOnConflictClause())
	if createErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in creating fetched asset for cas summary email: %v", createErr.Error()))
	}
	return nil
}

func (p *Processor) handleOtpVerificationFailure(ctx context.Context,
	lr *preapprovedloan.LoanRequest, lse *preapprovedloan.LoanStepExecution) error {
	otpData := lse.GetDetails().GetOtpVerificationData().GetOtpData()
	err := p.validateFiftyfinOtpVerification(ctx, lr, otpData)
	if err != nil {
		ok, otpVerFailErr := isOtpVerificationFailureErr(err)
		if !ok {
			return errors.Wrap(err, "non otp verification failure error")
		}
		err = p.updateStatusForOtpVerificationFailure(ctx, lr, lse, otpVerFailErr)
		return errors.Wrap(err, "error verifying fifty-fin otp")
	}
	err = p.validateMfcOtpVerification(ctx, lr, otpData)
	if err != nil {
		ok, otpVerFailErr := isOtpVerificationFailureErr(err)
		if !ok {
			return errors.Wrap(err, "non otp verification failure error")
		}
		err = p.updateStatusForOtpVerificationFailure(ctx, lr, lse, otpVerFailErr)
		return errors.Wrap(err, "error verifying mf-central otp")
	}
	return nil
}

func (p *Processor) updateStatusForOtpVerificationFailure(ctx context.Context,
	lr *preapprovedloan.LoanRequest, lse *preapprovedloan.LoanStepExecution, err *otpVerificationFailureErr) error {
	fieldMask := []preapprovedloan.LoanRequestFieldMask{
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	}
	lr.NextAction = lr.GetRedirectLink()
	lr.Status = preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
	lr.CompletedAt = timestamppb.Now()
	lr.SubStatus = err.subStatus
	fieldMask = append(fieldMask, preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS)
	updateErr := p.loanRequestDao.Update(ctx, lr, fieldMask)
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action: %v", updateErr.Error()))
	}
	lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
	lse.SubStatus = err.lseSubStatus
	lseFieldMasks := []preapprovedloan.LoanStepExecutionFieldMask{
		preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	}
	lseUpdateErr := p.loanStepExecutionDao.Update(ctx, lse, lseFieldMasks)
	if lseUpdateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error updating LSE: %v", lseUpdateErr.Error()))
	}
	// Returning permanent error so that activity fails and workflow fails without continuing to next activity
	return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("otp verification validation failed : %s", err.Error()))
}

func isOtpVerificationFailureErr(err error) (bool, *otpVerificationFailureErr) {
	var otpVerFailErr *otpVerificationFailureErr
	ok := errors.As(err, &otpVerFailErr)
	return ok, otpVerFailErr
}

type otpVerificationFailureErr struct {
	msg          string
	subStatus    preapprovedloan.LoanRequestSubStatus
	lseSubStatus preapprovedloan.LoanStepExecutionSubStatus
}

func (o *otpVerificationFailureErr) Error() string {
	if o == nil {
		return ""
	}
	return o.msg
}

func newOtpVerificationFailureErr(
	subStatus preapprovedloan.LoanRequestSubStatus,
	lseSubStatus preapprovedloan.LoanStepExecutionSubStatus,
	msg string,
) *otpVerificationFailureErr {
	return &otpVerificationFailureErr{
		msg:          msg,
		subStatus:    subStatus,
		lseSubStatus: lseSubStatus,
	}
}

func (p *Processor) validateFiftyfinOtpVerification(_ context.Context, lr *preapprovedloan.LoanRequest, otpData []*preapprovedloan.OtpVerificationData_OtpData) error {
	pfFetchDetails := lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails()
	if !pfFetchDetails.GetFetchFiftyfinPortfolio() {
		return nil
	}
	// filtering out OTPs that are not part of the loan offer generation process like MF Central PF fetch otp.
	otpsToBeVerified := lo.Filter(otpData, func(item *preapprovedloan.OtpVerificationData_OtpData, _ int) bool {
		return lo.Contains([]preapprovedloan.OtpType{
			preapprovedloan.OtpType_OTP_TYPE_CAMS_PF_FETCH,
			preapprovedloan.OtpType_OTP_TYPE_KARVY_PF_FETCH,
		}, item.GetOtpType())
	})
	for _, v := range otpsToBeVerified {
		if v.GetOtpStatus() != preapprovedloan.OtpStatus_OTP_STATUS_SUCCESS &&
			v.GetOtpStatus() != preapprovedloan.OtpStatus_OTP_STATUS_NOT_FOUND {
			return newOtpVerificationFailureErr(
				preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED,
				preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED,
				"at least one otp verification failed or not completed")
		}
	}
	return nil
}

func (p *Processor) validateMfcOtpVerification(ctx context.Context,
	lr *preapprovedloan.LoanRequest, otpData []*preapprovedloan.OtpVerificationData_OtpData) error {
	lg := activity.GetLogger(ctx)
	pfFetchDetails := lr.GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails()
	if !pfFetchDetails.GetFetchMfcPortfolio() {
		return nil
	}
	otps := lo.Filter(otpData, func(item *preapprovedloan.OtpVerificationData_OtpData, _ int) bool {
		return lo.Contains([]preapprovedloan.OtpType{
			preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH,
			preapprovedloan.OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH,
		}, item.GetOtpType())
	})
	if len(otps) == 0 {
		lg.Info("no MF central OTPs found")
		return nil
	}
	var notFoundOtpCount = 0
	for _, otp := range otps {
		if otp.GetOtpStatus() == preapprovedloan.OtpStatus_OTP_STATUS_NOT_FOUND {
			notFoundOtpCount++
			continue
		}
		if otp.GetVendorSessionId() == "" {
			continue
		}
		statusRes, err := p.mfExternalOrdersClient.GetHoldingsImportStatus(ctx, &mfExternalOrderPb.GetHoldingsImportStatusRequest{
			Identifier: &mfExternalOrderPb.GetHoldingsImportStatusRequest_ExternalId{
				ExternalId: otp.GetVendorSessionId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(statusRes, err); rpcErr != nil && !statusRes.GetStatus().IsRecordNotFound() {
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching mf external order fetch request status : %s", rpcErr.Error()))
		}
		if statusRes.GetStatus().IsRecordNotFound() {
			continue
		}
		status := statusRes.GetHoldingsImportState()
		if status == mfExternalOrderPb.State_EXTERNAL_ORDERS_REFRESH_SUCCESSFUL ||
			status == mfExternalOrderPb.State_OTP_VERIFICATION_SUCCESSFUL {
			return nil
		}
	}
	if notFoundOtpCount == len(otps) {
		return newOtpVerificationFailureErr(
			preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND,
			preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND,
			"no otp found against any portfolio",
		)
	}
	return newOtpVerificationFailureErr(
		preapprovedloan.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED,
		preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED,
		"at least one otp verification should be successful")
}
