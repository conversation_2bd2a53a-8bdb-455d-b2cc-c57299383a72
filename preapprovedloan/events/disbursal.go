//nolint:dupl
package events

import (
	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type Disbursal struct {
	EventName   string
	Vendor      string
	LoanProgram string
	EventType   string
	EventId     string
	ActorId     string
	ProspectId  string
}

func NewDisbursal(actorId, vendor, loanProgram string) *Disbursal {
	return &Disbursal{
		EventName:   DisbursalEventName,
		Vendor:      vendor,
		LoanProgram: loanProgram,
		EventType:   events.EventTrack,
		EventId:     uuid.New().String(),
		ActorId:     actorId,
		ProspectId:  "",
	}
}

func (c *Disbursal) GetEventType() string {
	return c.EventType
}

func (c *Disbursal) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *Disbursal) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *Disbursal) GetEventId() string {
	return c.EventId
}

func (c *Disbursal) GetUserId() string {
	return c.ActorId
}

func (c *Disbursal) GetProspectId() string {
	return c.ProspectId
}

func (c *Disbursal) GetEventName() string {
	return c.EventName
}
