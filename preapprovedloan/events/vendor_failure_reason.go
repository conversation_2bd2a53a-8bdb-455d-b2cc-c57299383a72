package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

// This will be used to publish events for error reasons that occurred in vendorgateway calls.
const eventName = "VendorFailureReasonEvent"

type VendorFailureReasonEvent struct {
	ActorId    string
	ProspectId string
	EventName  string
	SessionId  string
	EventId    string
	Timestamp  time.Time
	EventType  string
	// custom properties
	LoanRequestId string
	LoanVendor    string
	LoanProgram   string
	ApiName       string
	FailureReason string
	Status        string
}

func NewVendorFailureReasonEvent(actorId, loanRequestId, loanVendor, loanProgram, apiName, failureReason, status string) *VendorFailureReasonEvent {
	return &VendorFailureReasonEvent{
		ActorId:   actorId,
		EventType: events.EventTrack,
		EventName: eventName,
		EventId:   uuid.NewString(),
		Timestamp: time.Now(),
		// custom properties
		LoanRequestId: loanRequestId,
		LoanVendor:    loanVendor,
		LoanProgram:   loanProgram,
		ApiName:       apiName,
		FailureReason: failureReason,
		Status:        status,
	}
}

func (c *VendorFailureReasonEvent) GetEventType() string {
	return c.EventType
}

func (c *VendorFailureReasonEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *VendorFailureReasonEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *VendorFailureReasonEvent) GetEventId() string {
	return c.EventId
}

func (c *VendorFailureReasonEvent) GetUserId() string {
	return c.ActorId
}

func (c *VendorFailureReasonEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *VendorFailureReasonEvent) GetEventName() string {
	return c.EventName
}
