//nolint:dupl
package events

import (
	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

// Approved event represents lender approval with final offer details
type Approved struct {
	EventName   string
	Vendor      string
	LoanProgram string
	EventType   string
	EventId     string
	ActorId     string
	ProspectId  string
}

func NewApproved(actorId, vendor, loanProgram string) *Approved {
	return &Approved{
		EventName:   ApprovedEventName,
		Vendor:      vendor,
		LoanProgram: loanProgram,
		EventType:   events.EventTrack,
		EventId:     uuid.New().String(),
		ActorId:     actorId,
		ProspectId:  "",
	}
}

func (c *Approved) GetEventType() string {
	return c.EventType
}

func (c *Approved) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *Approved) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *Approved) GetEventId() string {
	return c.EventId
}

func (c *Approved) GetUserId() string {
	return c.ActorId
}

func (c *Approved) GetProspectId() string {
	return c.ProspectId
}

func (c *Approved) GetEventName() string {
	return c.EventName
}
