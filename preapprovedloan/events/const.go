package events

const (
	VendorFederal = "Federal"
)

const (
	EventApiTriggered                     = "LoanAPITriggered"
	EventApiResponse                      = "LoanAPIResponse"
	EventLoanRequest                      = "LoanRequest"
	EventLoanRequestStage                 = "LoanRequestStage"
	DelinquentUserPartialPaymentEventName = "DelinquentUserPartialPaymentEvent"
	PrePaymentSuccessfulEvent             = "PrePaymentSuccessful"
	LoansVendorPwaStageStatusUpdate       = "LoansVendorPwaStageStatusUpdate"
	EventLoanRecordUserAction             = "LoanRecordUserAction"
	EventMfFoliosUpdateCount              = "MfFoliosUpdateCount"
	EventLoansOtpStatusEvent              = "LoansOtpStatus"
	EventLoanStageEnableTime              = "LoanStageEnableTime"
	EventNameDiscovery                    = "LoanAPIResponse"
	EventNameSecondOfferFetchAttempted    = "SecondOfferFetchAttempted"
	EventPlSecondLookV1                   = "PlSecondLookV1"
	ApiNameLandingInfo                    = "LandingInfo"
	DisbursalEventName                    = "PLDisbursed"
	EligibilityStartedEventName           = "PLEligibilityStarted"
	EligibleEventName                     = "PLEligible"
	ApprovedEventName                     = "PLApproved"
	RejectedEventName                     = "PLRejected"
	EventLoecCreated                      = "PLLoecCreated"
	PwaNudgeEvent                         = "PwaNudgeEvent"
)

const (
	EventGenerateEligibleBaseList                       = "GenerateEligibleBaseList"
	EventUploadEligibleUsersListOnVendorGateway         = "UploadEligibleUsersListOnVendorGateway"
	EventReceivedApprovedOffersListOnFiFromVendorServer = "ReceivedApprovedOffersListOnFiFromVendorServer"
	EventWriteApprovedOffersInCRDB                      = "WriteApprovedOffersInCRDB "
	ApiNameCheckProfileValidation                       = "CheckProfileValidation"
)
const (
	LoanApplicationFlow     = "LoanApplication"
	EventFlowNameSecondLook = "SecondLook"
	PlSecondLookV1Flow      = "PlSecondLookV1Flow"
)

const (
	EventSubstatusSecondLookOfferShown      = "OfferShown"
	EventSubstatusSecondLookOfferNeedUpdate = "NeedUpdate"
	EventSubstatusSecondLookApiFailure      = "ApiFailure"
	EventSubstatusSecondLookSameOffer       = "SameOffer"
	EventSubstatusSecondLookOfferNotFound   = "OfferNotFound"
)

const (
	EventSubstatusSecondLookV1OfferShown         = "SecondLookOfferShown"
	EventSubstatusSecondLookV1PollingScreenError = "SecondLookPollingScreenError"
)

const (
	Success = "success"
	Failure = "failure"
)

const (
	StageInitiated = "PlStageInitiated"
	StageCompleted = "PlStageCompleted"
)

const (
	FiPreBreEventName                = "FiPreBre"
	FiFinalBreEventName              = "FiFinalBre"
	FiAADataReceivedForBREEventName  = "FiAADataReceivedForBRE"
	VendorEligibilityStatusEventName = "VendorEligibilityStatus"
)

const (
	// Event names for terminal and non-terminal rejections
	PlLoanApplicationRejected = "PlLoanApplicationRejected"
)
const (
	PlLoanApplicationRejectedStatusTerminal    = "PlLoanApplicationRejectedStatusTerminal"
	PlLoanApplicationRejectedStatusNonTerminal = "PlLoanApplicationRejectedStatusNonTerminal"
)
