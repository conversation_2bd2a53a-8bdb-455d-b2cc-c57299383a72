package observers

import (
	"context"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
)

// AppsFlyerEventObserver is an observer that handles publishing of events for consumption on appsflyer.
// Specific events for appsflyer are needed because the event consumer cannot take decisions based on the event property values except for the event name.
type AppsFlyerEventObserver struct {
	acqEventPublisher palEvents.AcqEventPublisher
}

func NewAppsFlyerEventObserver(acqEventPublisher palEvents.AcqEventPublisher) *AppsFlyerEventObserver {
	return &AppsFlyerEventObserver{
		acqEventPublisher: acqEventPublisher,
	}
}

// NotifyLoecUpdate publishes event for relevant updates to the loan offer eligibility criteria.
func (a *AppsFlyerEventObserver) NotifyLoecUpdate(ctx context.Context, loec *palPb.LoanOfferEligibilityCriteria, loecFieldMasks []palPb.LoanOfferEligibilityCriteriaFieldMask) {
	defer metric_util.TrackDuration("preapprovedloan/dao/wrapper/observers", "AppsFlyerEventObserver", "NotifyLoecUpdate", time.Now())
	statusUpdate := lo.Contains(loecFieldMasks, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS)
	subStatusUpdate := lo.Contains(loecFieldMasks, palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS)
	// the events that are published below rely only on status and substatus updates
	if !statusUpdate && !subStatusUpdate {
		return
	}
	switch {
	// status might already be approved in case of soft offers generated by Fi BRE, so sub status update is being used to differentiate between approved by vendor and approved by Fi
	case subStatusUpdate && (loec.GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR ||
		loec.GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_WITH_CHANGED_OFFER):
		a.publishAcqEvent(ctx, loec.GetActorId(), palEvents.NewApproved(loec.GetActorId(), loec.GetVendor().String(), loec.GetLoanProgram().String()))
	case statusUpdate && loec.GetStatus() == palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED:
		rejectionReason := ""
		if subStatusUpdate && loec.GetSubStatus() != palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED {
			rejectionReason = loec.GetSubStatus().String()
		}
		a.publishAcqEvent(ctx, loec.GetActorId(), palEvents.NewRejected(loec.GetActorId(), loec.GetVendor().String(), loec.GetLoanProgram().String(), rejectionReason))
	// status might not be changed to approved if Fi BRE is not generating soft offers, so sub status update is being used to identify approvals by Fi BRE
	// sub status is marked as APPROVED_BY_FI_PRE_BRE in case of approvals by Fi BRE
	// sub status is marked as APPROVED_BY_FI_BRE in case of offer generation by Fi BRE
	// to avoid double events, the event is published only in case of APPROVED_BY_FI_PRE_BRE
	case subStatusUpdate && loec.GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE:
		a.publishAcqEvent(ctx, loec.GetActorId(), palEvents.NewEligible(loec.GetActorId(), loec.GetVendor().String(), loec.GetLoanProgram().String()))
	default:
		// do nothing
	}
}

func (a *AppsFlyerEventObserver) publishAcqEvent(ctx context.Context, actorId string, event events.Event) {
	err := a.acqEventPublisher.PublishAcqEventSelectively(ctx, actorId, event)
	if err != nil {
		logger.Error(ctx, "error in publishing acq event", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.PAYLOAD, event.GetEventProperties()))
	}
}
