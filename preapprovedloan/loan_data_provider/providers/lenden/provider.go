package lenden

import (
	"context"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	"github.com/epifi/gamma/preapprovedloan/pkg"
)

type Provider struct {
	ldcVgClient    ldcVgPb.LendenClient
	loanAccountDao dao.LoanAccountsDao
	palClient      palPb.PreApprovedLoanClient
}

func NewProvider(
	ldcVgClient ldcVgPb.LendenClient,
	loanAccountDao dao.LoanAccountsDao,
	palClient palPb.PreApprovedLoanClient,
) *Provider {
	return &Provider{
		ldcVgClient:    ldcVgClient,
		loanAccountDao: loanAccountDao,
		palClient:      palClient,
	}
}

func (s *Provider) FetchLoanScheduleFromVendor(ctx context.Context, req *providers.FetchLoanScheduleRequest) (*providers.FetchLoanScheduleResponse, error) {
	amortizationScheduleRes, err := s.ldcVgClient.GetAmortizationSchedule(ctx, &ldcVgPb.GetAmortizationScheduleRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: req.LoanAccount.GetAccountNumber(),
	})
	if err = epifigrpc.RPCError(amortizationScheduleRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting amortization schedule for loan account: %v", req.LoanAccount.GetId())
	}
	installments, err := convertScheduleToPayouts(req.LoanAccount.GetId(), amortizationScheduleRes.GetAmortizationSchedule())
	if err != nil {
		return nil, errors.Wrap(err, "error converting schedule to payouts")
	}
	loanDetailsRes, err := s.ldcVgClient.GetLoanDetails(ctx, &ldcVgPb.GetLoanDetailsRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: req.LoanAccount.GetAccountNumber(),
	})
	if err = epifigrpc.RPCError(loanDetailsRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting loan details for loan account: %v", req.LoanAccount.GetId())
	}
	status, err := getLoanAccountStatus(loanDetailsRes.GetLoanStatus())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan account status")
	}
	return &providers.FetchLoanScheduleResponse{
		LoanInstallments: installments,
		Status:           status,
	}, nil
}

func convertScheduleToPayouts(loanAccountId string, schedule []*ldcVgPb.AmortizationScheduleItem) ([]*palPb.LoanInstallmentPayout, error) {
	var installments []*palPb.LoanInstallmentPayout
	if len(schedule) == 0 {
		return nil, errors.New("no schedule found")
	}
	// Sort the schedule slice in place by due date ascending, so that callers can assume the schedule is sorted
	// regardless of vendor not being deterministic in their ordering.
	sort.Slice(schedule, func(i, j int) bool {
		return !datetime.IsDateAfter(schedule[i].GetDueDate(), schedule[j].GetDueDate())
	})
	for i := 1; i < len(schedule); i++ {
		if datetime.DateEquals(schedule[i-1].GetDueDate(), schedule[i].GetDueDate()) {
			return nil, errors.Errorf("duplicate due date found in schedule, (i-1)th: %v, ith: %v", schedule[i-1].GetDueDate(), schedule[i].GetDueDate())
		}
	}
	for _, item := range schedule {
		paymentStatus, err := getPaymentStatus(item.GetStatus())
		if err != nil {
			return nil, errors.Wrap(err, "error getting payment status")
		}
		var principal, interest *moneyPb.Money
		for _, component := range item.GetBreakup() {
			switch component.GetPurpose() {
			case ldcVgPb.AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL:
				principal = component.GetAmount()
			case ldcVgPb.AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_INTEREST:
				interest = component.GetAmount()
			default:
				return nil, errors.Errorf("unexpected purpose: %s", component.GetPurpose().String())
			}
		}
		amountPaid, err := moneyPkg.Subtract(item.GetDueAmount(), item.GetOutstandingAmount())
		if err != nil {
			return nil, errors.Wrapf(err, "error calculating amount paid, dueAmount: %v, outstandingAmount: %v", item.GetDueAmount(), item.GetOutstandingAmount())
		}
		var payoutDate *date.Date
		if moneyPkg.IsZero(item.GetOutstandingAmount()) {
			// TODO(Brijesh): Discuss and add correct handling after discussing with Vikas
			payoutDate = item.GetDueDate()
		}
		installment := &palPb.LoanInstallmentPayout{
			Amount:              amountPaid,
			DueDate:             item.GetDueDate(),
			PayoutDate:          payoutDate,
			Status:              paymentStatus,
			LoanAccountId:       loanAccountId,
			VendorInstallmentId: datetime.DateToString(item.GetDueDate(), datetime.DATE_LAYOUT_DDMMYYYY, datetime.IST),
			Principal:           principal,
			Interest:            interest,
			DueAmount:           item.GetDueAmount(),
		}
		installments = append(installments, installment)
	}
	return installments, nil
}

func getLoanAccountStatus(status ldcVgPb.LoanStatus) (palPb.LoanAccountStatus, error) {
	switch status {
	case ldcVgPb.LoanStatus_LOAN_STATUS_PROCESSING,
		ldcVgPb.LoanStatus_LOAN_STATUS_SANCTIONED,
		ldcVgPb.LoanStatus_LOAN_STATUS_DISBURSED:
		return palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE, nil
	case ldcVgPb.LoanStatus_LOAN_STATUS_CLOSED,
		ldcVgPb.LoanStatus_LOAN_STATUS_CANCELLED:
		return palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED, nil
	default:
		return 0, errors.Errorf("unexpected loan status: %s", status.String())
	}
}

func getPaymentStatus(status ldcVgPb.AmortizationScheduleItemStatus) (palPb.LoanInstallmentPayoutStatus, error) {
	// TODO(Brijesh): Discuss if we need to map LDC statuses to other internal statuses like partially failed, partially paid, cancelled, etc.
	switch status {
	case ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE:
		return palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING, nil
	case ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT:
		return palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS, nil
	case ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PARTIALLY_PAID:
		return palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID, nil
	default:
		return 0, errors.Errorf("unexpected payment status: %s", status.String())
	}
}

func (s *Provider) GetOutstandingLoanAmount(ctx context.Context, req *providers.GetOutstandingLoanAmountRequest) (*providers.GetOutstandingLoanAmountResponse, error) {
	if req.LoanAccountId == "" {
		return nil, errors.New("loan account id is empty")
	}

	loanAccount, err := s.loanAccountDao.GetById(ctx, req.LoanAccountId)
	if err != nil {
		return nil, fmt.Errorf("unable to get loan account: %w", err)
	}

	loanDetailsRes, err := s.ldcVgClient.GetLoanDetails(ctx, &ldcVgPb.GetLoanDetailsRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: loanAccount.GetAccountNumber(),
	})
	if err = epifigrpc.RPCError(loanDetailsRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting loan details for loan account: %s", req.LoanAccountId)
	}
	return &providers.GetOutstandingLoanAmountResponse{
		OutstandingLoanAmount: pkg.GetTotalOutstandingAmountFromLoanDetailsRes(loanDetailsRes),
	}, nil
}
func (p *Provider) GetPaymentLink(ctx context.Context, req *providers.GetPaymentLinkReq) (*providers.GetPaymentLinkResp, error) {
	paymentLinkResp, err := p.ldcVgClient.GeneratePaymentLink(ctx, &ldcVgPb.GeneratePaymentLinkRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: req.LoanId,
		Amount: req.TxnAmount,
	})
	if te := epifigrpc.RPCError(paymentLinkResp, err); te != nil {
		return nil, errors.Wrap(te, "error in lenden payment link api")
	}
	return &providers.GetPaymentLinkResp{
		PaymentLink: paymentLinkResp.GetUrl(),
		OrderId:     paymentLinkResp.GetOrderId(),
	}, nil
}

func (s *Provider) FetchLoanCancellationDetailsFromVendor(ctx context.Context, loanAccount *palPb.LoanAccount) (*providers.FetchLoanCancellationDetailsFromVendorResponse, error) {
	res, err := s.ldcVgClient.GetForeclosureDetails(ctx, &ldcVgPb.GetForeclosureDetailsRequest{
		Header:  &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId:  loanAccount.GetAccountNumber(),
		Purpose: ldcVgPb.GetForeclosureDetailsRequest_PURPOSE_COOL_OFF,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsFailedPrecondition() {
			return &providers.FetchLoanCancellationDetailsFromVendorResponse{
				IsCancellationAllowed: false,
			}, nil
		}
		return nil, errors.Wrap(err, "error getting cool-off details")
	}

	return &providers.FetchLoanCancellationDetailsFromVendorResponse{
		IsCancellationAllowed:  true,
		LoanCancellationAmount: res.GetForeclosureDetails().GetForeclosureAmount(),
	}, nil
}

func (s *Provider) FetchLoanPreClosureDetailsFromVendor(ctx context.Context, loanAccount *palPb.LoanAccount) (*providers.FetchLoanPreClosureDetailsFromVendorResponse, error) {
	res, err := s.ldcVgClient.GetForeclosureDetails(ctx, &ldcVgPb.GetForeclosureDetailsRequest{
		Header:  &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId:  loanAccount.GetAccountNumber(),
		Purpose: ldcVgPb.GetForeclosureDetailsRequest_PURPOSE_FORECLOSURE,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsFailedPrecondition() {
			res, err = s.ldcVgClient.GetForeclosureDetails(ctx, &ldcVgPb.GetForeclosureDetailsRequest{
				Header:  &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
				LoanId:  loanAccount.GetAccountNumber(),
				Purpose: ldcVgPb.GetForeclosureDetailsRequest_PURPOSE_COOL_OFF,
			})
			if err = epifigrpc.RPCError(res, err); err != nil {
				return nil, errors.Wrap(err, "error getting cool-off details")
			}
			return &providers.FetchLoanPreClosureDetailsFromVendorResponse{
				LoanPreCloseAmount:             res.GetForeclosureDetails().GetForeclosureAmount(),
				LoanPreCloseCharges:            res.GetForeclosureDetails().GetForeclosureCharges(),
				LoanPrincipalOutstandingAmount: res.GetForeclosureDetails().GetPrincipalOutstanding(),
				LoanOtherCharges:               res.GetForeclosureDetails().GetDelayChargesOutstanding(),
				LoanFeesAmount:                 res.GetForeclosureDetails().GetLateFeeOutstanding(),
				LoanInterestOutstandingAmount:  res.GetForeclosureDetails().GetInterestOutstanding(),
			}, nil
		} else if res.GetStatus().GetCode() == uint32(ldcVgPb.GetForeclosureDetailsResponse_INVALID_LOAN_STATUS) {
			// refresh LMS
			refreshLmsResp, refreshLmsErr := s.palClient.RefreshLMSSchedule(ctx, &palPb.RefreshLmsScheduleRequest{
				ActorId: loanAccount.GetActorId(),
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: loanAccount.GetLoanProgram(),
					Vendor:      loanAccount.GetVendor(),
				},
			})
			if te := epifigrpc.RPCError(refreshLmsResp, refreshLmsErr); te != nil {
				return nil, errors.Wrap(err, "error refreshing LMS details")
			}
		}
		return nil, errors.Wrap(err, "error getting foreclosure details")
	}
	return &providers.FetchLoanPreClosureDetailsFromVendorResponse{
		LoanPreCloseAmount:             res.GetForeclosureDetails().GetForeclosureAmount(),
		LoanPreCloseCharges:            res.GetForeclosureDetails().GetForeclosureCharges(),
		LoanPrincipalOutstandingAmount: res.GetForeclosureDetails().GetPrincipalOutstanding(),
		LoanOtherCharges:               res.GetForeclosureDetails().GetDelayChargesOutstanding(),
		LoanFeesAmount:                 res.GetForeclosureDetails().GetLateFeeOutstanding(),
		LoanInterestOutstandingAmount:  res.GetForeclosureDetails().GetInterestOutstanding(),
	}, nil
}
