package main

import (
	"bytes"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha512"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"

	"golang.org/x/crypto/pkcs12"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/transform"
)

func GetPrivateKey(privateKeyPath string, privateKeyPassword string) (*rsa.PrivateKey, error) {
	pfxFile, err := os.Open(privateKeyPath)
	if err != nil {
		return nil, fmt.Errorf("Error opening .pfx file:", err)
	}
	defer pfxFile.Close()

	stat, err := pfxFile.Stat()
	if err != nil {
		return nil, fmt.Errorf("Error getting file stats:", err)
	}

	pfxBytes := make([]byte, stat.Size())
	_, err = pfxFile.Read(pfxBytes)
	if err != nil {
		return nil, fmt.Errorf("Error reading .pfx file:", err)
	}
	block2, _, err := pkcs12.Decode(pfxBytes, privateKeyPassword)
	if err != nil {
		return nil, fmt.Errorf("Error parsing PKCS12:", err)
	}

	rsaPrivateKey := block2.(*rsa.PrivateKey)
	return rsaPrivateKey, nil
}

func generateSessionKey(length int) (string, error) {
	key := make([]byte, length/8)
	_, err := rand.Read(key)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

func EncryptRequestDataUsingSymmetricKey(Symmetric string, text string) string {
	plaintextBytes := []byte(text)
	plaintextBytes = padPKCS7(plaintextBytes, aes.BlockSize)
	decodedBytesofSymmtricKey, err := base64.StdEncoding.DecodeString(Symmetric)
	if err != nil {
		panic(err.Error())
	}

	block, err := aes.NewCipher(decodedBytesofSymmtricKey)
	if err != nil {
		panic(err.Error())
	}
	// Create a new ECB mode encrypter
	encrypter := NewECBEncrypter(block)

	// Encrypt the plaintext
	ciphertext := make([]byte, len(plaintextBytes))
	encrypter.CryptBlocks(ciphertext, plaintextBytes)

	// Encode the ciphertext as base64 for easy display
	encodedCiphertext := base64.StdEncoding.EncodeToString(ciphertext)
	return encodedCiphertext
}

func padPKCS7(input []byte, blockSize int) []byte {
	padding := blockSize - (len(input) % blockSize)
	pad := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(input, pad...)
}

// ecbEncrypter encrypts a single block using ECB mode
type ecbEncrypter struct {
	b         cipher.Block
	blockSize int
}

func NewECBEncrypter(b cipher.Block) *ecbEncrypter {
	return &ecbEncrypter{b, b.BlockSize()}
}

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }

func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("input not full blocks")
	}
	if len(dst) < len(src) {
		panic("output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

func DecryptRequestDataUsingSymmetricKey(SymmetricKey []byte, ciphertext string) (string, error) {
	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return err.Error(), err
	}

	block, err := aes.NewCipher(SymmetricKey)
	if err != nil {
		return err.Error(), err
	}

	if len(ciphertextBytes)%aes.BlockSize != 0 {
		return err.Error(), errors.New("ciphertext is not a multiple of the block size")
	}

	// Create a new ECB mode decrypter
	decrypter := NewECBDecrypter(block)

	// Decrypt the ciphertext
	plaintext := make([]byte, len(ciphertextBytes))
	decrypter.CryptBlocks(plaintext, ciphertextBytes)

	// Unpad the plaintext
	plaintext, err = unpadPKCS7(plaintext)
	if err != nil {
		return err.Error(), err
	}

	return string(plaintext), err
}

// unpadPKCS7 removes PKCS7 padding from the plaintext
func unpadPKCS7(plaintext []byte) ([]byte, error) {
	length := len(plaintext)
	unpadding := int(plaintext[length-1])
	if unpadding > length {
		return nil, errors.New("invalid padding")
	}
	return plaintext[:length-unpadding], nil
}

func NewECBDecrypter(block cipher.Block) cipher.BlockMode {
	return ecbDecrypter{block}
}

type ecbDecrypter struct {
	b cipher.Block
}

func (x ecbDecrypter) BlockSize() int {
	return x.b.BlockSize()
}

func (x ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.b.BlockSize() != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Decrypt(dst, src[:x.b.BlockSize()])
		src = src[x.b.BlockSize():]
		dst = dst[x.b.BlockSize():]
	}
}

func EncryptUsingPublicKey(sessionKey []byte, publicKeyFile string) (string, error) {
	var encryptedKey string

	publicKeyBytes, err := ioutil.ReadFile(publicKeyFile)

	if err != nil {
		return "Error occured while reading public key file ", err
	}
	block, _ := pem.Decode(publicKeyBytes)
	if block == nil {
		return "", errors.New("failed to decode PEM block from public key file")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return "Error occured while parsion the public key file", err
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return "", errors.New("public key is not an RSA public key")
	}

	ciphertext, err := rsa.EncryptPKCS1v15(rand.Reader, rsaPublicKey, sessionKey)
	if err != nil {
		return "error encrypting cypher text", err
	}

	encryptedKey = base64.StdEncoding.EncodeToString(ciphertext)

	return encryptedKey, nil
}

func encryptUsingSessionKey(sessionKey []byte, plainText string) (string, error) {
	block, err := aes.NewCipher(sessionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// Pad plaintext if needed (AES requires input to be a multiple of the block size)
	plainTextBytes := []byte(plainText)
	padding := aes.BlockSize - len(plainTextBytes)%aes.BlockSize
	paddedPlainText := append(plainTextBytes, bytes.Repeat([]byte{byte(padding)}, padding)...)

	cipherText := make([]byte, len(paddedPlainText))
	// Use ECB mode for simplicity. Use a proper mode like GCM or CBC in real-world scenarios.
	ecb := cipher.NewCBCEncrypter(block, sessionKey[:aes.BlockSize])
	ecb.CryptBlocks(cipherText, paddedPlainText)

	// Encode ciphertext to base64
	encryptedData := base64.StdEncoding.EncodeToString(cipherText)
	return encryptedData, nil
}

func decodePFX(pfxData []byte, password string) (*x509.Certificate, interface{}, error) {
	// Decode the PFX data
	blocks, err := pkcs12.ToPEM(pfxData, password)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode PFX data: %v", err)
	}

	var certDERBlock *pem.Block
	var keyDERBlock *pem.Block
	for _, block := range blocks {
		if block.Type == "CERTIFICATE" {
			certDERBlock = block
		} else if block.Type == "PRIVATE KEY" {
			keyDERBlock = block
		}
	}

	if certDERBlock == nil || keyDERBlock == nil {
		return nil, nil, fmt.Errorf("certificate or private key not found in PFX")
	}

	// Parse the certificate
	certificate, err := x509.ParseCertificate(certDERBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse certificate: %v", err)
	}

	// Parse the private key
	privateKey, err := x509.ParsePKCS8PrivateKey(keyDERBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return certificate, privateKey, nil
}

func GetPublicKey(publicKeyPath string) (*rsa.PublicKey, error) {
	cerFile, err := os.Open(publicKeyPath)
	if err != nil {
		return nil, fmt.Errorf("Error opening public key file: %w", err)
	}
	defer cerFile.Close()

	stat, err := cerFile.Stat()
	if err != nil {
		return nil, fmt.Errorf("Error getting file stats: %w", err)
	}

	cerBytes := make([]byte, stat.Size())
	_, err = cerFile.Read(cerBytes)
	if err != nil {
		return nil, fmt.Errorf("Error reading public key file: %w", err)
	}

	// Parse the certificate
	fmt.Println("cer")
	fmt.Println(string(cerBytes))
	block, _ := pem.Decode(cerBytes)
	if block == nil {
		return nil, fmt.Errorf("Error decoding PEM block")
	}
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("Error parsing certificate: %w", err)
	}

	publicKey := cert.PublicKey.(*rsa.PublicKey)
	return publicKey, nil
}

func EncryptSymmetricKeyAsymetrically(symmetricKey string, publicKeyPath string) (string, error) {
	publicKey, err := GetPublicKey(publicKeyPath)
	if err != nil {
		return "", fmt.Errorf("Error of getting public key: %w", err)
	}
	decodedBytes := []byte(symmetricKey)
	encryptedData, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, decodedBytes)
	if err != nil {
		return "", err
	}
	encodedEncryptedData := base64.StdEncoding.EncodeToString(encryptedData)
	return encodedEncryptedData, nil
}

func signData(signingParams string, privateKeyPath string, privateKeyPassword string) (string, error) {
	privateKey, err := GetPrivateKey(privateKeyPath, privateKeyPassword)
	if err != nil {
		return "", err
	}
	data, err := Utf16leBytes(signingParams)
	if err != nil {
		return "", err
	}
	hashed := sha512.Sum512(data)

	// Sign the hashed data using RSA private key with SHA-512
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA512, hashed[:])
	if err != nil {
		return "", err
	}

	// Encode the signature to base64
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)
	return signatureBase64, nil
}

// Utf16leBytes converts a string to UTF-16LE encoded bytes
func Utf16leBytes(s string) ([]byte, error) {
	utf16leEncoder := unicode.UTF16(unicode.LittleEndian, unicode.IgnoreBOM).NewEncoder()
	encodedBytes, _, err := transform.Bytes(utf16leEncoder, []byte(s))
	if err != nil {
		return nil, err
	}
	return encodedBytes, nil
}

func GetScreenzaaResponse(encryptRequest EncryptedRequestModel, apiUrl string, apiToken string) (string, error) {
	// Convert the struct to JSON
	requestBody, err := json.Marshal(encryptRequest)
	fmt.Println("Request Body:", string(requestBody))
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return err.Error(), err
	}

	u, err := url.Parse(apiUrl)
	if err != nil {
		fmt.Println("Error parsing URL:", err)
		return err.Error(), err
	}
	// Define custom headers
	headers := map[string]string{
		"Content-Type": "application/json",
		"Cluster":      "CL1_User",
		"Domain":       u.Hostname(),
		"ApiToken":     apiToken,
	}

	// Create a new request
	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(requestBody))
	if err != nil {
		fmt.Println("Error creating request:", err)
		return err.Error(), err
	}

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return err.Error(), err
	}
	defer resp.Body.Close()

	// Print response status code
	fmt.Println("Response Status:", resp.Status)
	bdy, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return err.Error(), err
	}
	return string(bdy), err
}

func VerifySign(data string, signature string, publicKeyPath string) (bool, error) {
	publicKey, err := GetPublicKey(publicKeyPath)
	if err != nil {
		return false, err
	}
	Data, err := Utf16leBytes(data)
	if err != nil {
		return false, err
	}
	decodedBytesofSignature, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return false, err
	}
	hashed := sha512.Sum512(Data)
	if err := rsa.VerifyPKCS1v15(publicKey, crypto.SHA512, hashed[:], decodedBytesofSignature); err != nil {
		fmt.Println("Signature Verification failed")
		return false, err
	}
	return true, nil
}

type EncryptedRequestModel struct {
	EncryptedData string `json:"encryptedData"`
	EncryptionKey string `json:"encryptionKey"`
	Signature     string `json:"signature"`
	RequestId     string `json:"requestId"`
}

// Define a struct to represent the JSON object
type EncryptedResponseModel struct {
	RequestID             string `json:"requestId"`
	ValidationCode        string `json:"validationCode"`
	ValidationDescription string `json:"validationDescription"`
	EncryptedData         string `json:"encryptedData"`
	EncryptionKey         string `json:"encryptionKey"`
	Signature             string `json:"signature"`
}

func DecryptSymmetricKeyAssymetricaly(encodedEncryptedData string, privateKeyPath string, privateKeyPassword string) ([]byte, error) {
	decodedEncryptedData, err := base64.StdEncoding.DecodeString(encodedEncryptedData)
	if err != nil {
		return nil, fmt.Errorf("Error decoding Base64: %w", err)
	}

	privateKey, err := GetPrivateKey(privateKeyPath, privateKeyPassword)
	if err != nil {
		return nil, err
	}

	decryptedData, err := rsa.DecryptPKCS1v15(rand.Reader, privateKey, decodedEncryptedData)
	if err != nil {
		return nil, err
	}
	decodedData, err := base64.StdEncoding.DecodeString(string(decryptedData))
	return decodedData, err
}

func main() {

	privateKeyPath := "./local_cert_decoded.pfx"
	privateKeyPassword := "epifi123"
	publickeyPath := "./tss_live_public_key_2023.cer"
	apiUrl := "https://epifitechnologiespvtltd-sb.trackwizz.app/customerinfo/as501"
	apiToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************._7ZGsjljg60InwI9rBoqzGfC52t33KQV7mjoVwbHSVY"

	// 1) generating session key
	println("1) Generating session keys")
	sessionKey, err := generateSessionKey(128)
	if err != nil {
		panic(err.Error())
	}
	// fmt.Println("session key : ", sessionKey)

	// 2) Encrypting the data using the session key
	println("2) Encrypting data using session key")
	plainTextJson, err := ioutil.ReadFile("./req.json")
	if err != nil {
		panic(err.Error())
	}
	encryptedPlainText := EncryptRequestDataUsingSymmetricKey(sessionKey, string(plainTextJson))
	// println("Encrypted data : ", encryptedPlainText)

	// 3) Encryption SessionKey with PublicKey
	println("3) Encrypting session key using public key")
	encryptedSessionKey, err := EncryptSymmetricKeyAsymetrically(sessionKey, publickeyPath)
	if err != nil {
		panic(err.Error())
	}
	// println("Encrypted session key : ", encryptedSessionKey)

	// 4) Signature on plaintext using private Key
	println("4) Signature on plaintext using private Key")
	signingParams := encryptedPlainText + encryptedSessionKey
	signature, err := signData(signingParams, privateKeyPath, privateKeyPassword)
	if err != nil {
		panic(err.Error())
	}
	// fmt.Println("Encrypted data:", signature)

	fmt.Println("5) Sending the request to TW server")
	encryptRequest := EncryptedRequestModel{
		EncryptedData: encryptedPlainText,
		EncryptionKey: encryptedSessionKey,
		Signature:     signature,
		RequestId:     "1235467",
	}
	encryptedRequest, err := GetScreenzaaResponse(encryptRequest, apiUrl, apiToken)
	if err != nil {
		panic(err.Error())
	}
	fmt.Println(encryptedRequest)

	fmt.Println("6) Verifying the Response Signature")
	var responseModel EncryptedResponseModel
	err = json.Unmarshal([]byte(encryptedRequest), &responseModel)
	if err != nil {
		fmt.Println("Error deserializing JSON:", err)
		return
	}
	// fmt.Println("Error deserializing JSON:", responseModel.Signature)
	if responseModel.Signature == "" || responseModel.EncryptionKey == "" || responseModel.EncryptedData == "" {
		fmt.Println(encryptedRequest)
	} else {
		signingParams := responseModel.EncryptedData + responseModel.EncryptionKey
		isVerify, err := VerifySign(signingParams, responseModel.Signature, publickeyPath)
		if isVerify {
			fmt.Println("7) Decrypting the SymmtricKey")
			symmetricKeyBytes, err := DecryptSymmetricKeyAssymetricaly(responseModel.EncryptionKey, privateKeyPath, privateKeyPassword)
			if err != nil {
				fmt.Println("Error Decrypting the EncryptionKey of Response:", err)
			}
			fmt.Println("8) Decrypting the EncryptedData")
			decryptedResponse, err := DecryptRequestDataUsingSymmetricKey(symmetricKeyBytes, responseModel.EncryptedData)
			if err != nil {
				fmt.Println("Error Decrypting the EncryptionData of Response:", err)
			}
			fmt.Println(decryptedResponse)
		} else {
			fmt.Println(err)
		}
	}
	fmt.Println("Completed Processing the Request")

}
