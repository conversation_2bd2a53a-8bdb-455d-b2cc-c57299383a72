package federal

import (
	"fmt"

	federal2 "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"

	"github.com/epifi/be-common/pkg/idgen"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/order/payment"
	paymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	"github.com/epifi/gamma/api/vendors/federal/payment/b2c"
	federalPay "github.com/epifi/gamma/api/vendors/federal/payment/b2c"
	"github.com/epifi/gamma/vendorgateway/federal"
)

type GetStatusReq struct {
	*federal.RequestSourceBasedHeaderAdder
	*federal2.DefaultPGPSecuredExchange

	Method string
	Req    *paymentPb.GetB2CTransactionStatusRequest
	Url    string
}

func (p *GetStatusReq) Marshal() ([]byte, error) {
	params := federal.GetB2CRequestParams(p.RequestSource)

	transactionType, err := getTransactionType(p.Req.GetProtocol())
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("failed to get api id for protocol: %s", p.Req.GetProtocol()), zap.Error(err))
		return nil, err
	}
	requestId := idgen.FederalRandomSequence("ENQ", 5)
	if err != nil {
		return nil, fmt.Errorf("invalid arguments for transaction status %w", err)
	}
	requestPayload := &federalPay.EnquireTransactionStatusRequest{
		UserId:              (*params)[userId],
		Password:            (*params)[password],
		SenderCode:          (*params)[senderCode],
		ReferenceId:         requestId,
		OriginalReferenceId: p.Req.GetOriginalRequestId(),
		TransactionType:     transactionType,
	}
	return protojson.Marshal(requestPayload)
}

func (p *GetStatusReq) HTTPMethod() string {
	return p.Method
}

func (p *GetStatusReq) URL() string {
	return p.Url
}

func (p *GetStatusReq) GetResponse() vendorapi.Response {
	return &GetStatusRes{}
}

type GetStatusRes struct {
}

var (
	inProcessTxnStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(paymentPb.GetB2CTransactionStatusResponse_IN_PROGRESS),
			"transaction in process")
	}

	deemedApprovedTxnStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(paymentPb.GetB2CTransactionStatusResponse_DEEMED_APPROVED),
			"transaction went to deemed approved status")
	}

	permanentPayStatusError rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(paymentPb.GetB2CTransactionStatusResponse_PERMANENT_FAILURE),
			"permanent transaction failure")
	}

	transientPayStatusError rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(paymentPb.GetB2CTransactionStatusResponse_TRANSIENT_FAILURE),
			"transient failure")
	}

	insuffcientFundStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(paymentPb.GetB2CTransactionStatusResponse_INSUFFICIENT_FUND),
			"Insufficient funds in account")
	}

	accountClosedStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(paymentPb.GetB2CTransactionStatusResponse_ACCOUNT_IS_CLOSED),
			"account is closed or frozen")
	}
)

var statusCodes = map[string]rpc.StatusFactory{
	"000":  rpc.StatusOk,
	"999":  permanentPayStatusError,
	"F002": transientPayStatusError,
	"F001": transientPayStatusError,
	"F003": permanentPayStatusError,
	"F004": permanentPayStatusError,
	"F005": inProcessTxnStatus,
	"F006": inProcessTxnStatus,
	"F010": inProcessTxnStatus,
	"F008": inProcessTxnStatus,
	"F007": inProcessTxnStatus,
	"116":  insuffcientFundStatus,
	"119":  accountClosedStatus,
}

var responseActionToStatusFactoryMap = map[string]rpc.StatusFactory{
	"SUCCESS":         rpc.StatusOk,
	"FAILURE":         permanentPayStatusError,
	"SUSPECT":         inProcessTxnStatus,
	"DEEMED APPROVED": deemedApprovedTxnStatus,
}

func (p *GetStatusRes) Unmarshal(b []byte) (proto.Message, error) {
	st := &b2c.EnquireTransactionStatusResponse{}
	err := protojson.Unmarshal(b, st)
	if err != nil {
		logger.ErrorNoCtx("Could not parse json response")
		return nil, err
	}

	// federal enquiry response returns two level of responseAction/Result
	// 1st is the upper level response action which represents status of the enquiry API
	// 2nd is the lower level response action which represents original transaction status
	// The 2nd will appear only if enquiry API succeeds with response action `SUCCESS`
	if statusFactory, ok := statusCodes[st.GetResponse()]; ok {
		status := statusFactory()
		if status.IsSuccess() {
			if originalStatusFactory, ok := responseActionToStatusFactoryMap[st.GetOriginalTxnDetails().GetResponseAction()]; !ok {
				return &paymentPb.GetB2CTransactionStatusResponse{
					Status:       rpc.StatusUnknown(),
					ResponseCode: st.GetOriginalTxnDetails().GetResponseCode(),
					Reason:       st.GetOriginalTxnDetails().GetResponseReason(),
				}, nil
			} else {
				status = originalStatusFactory()
				if finalStatus, ok := statusCodes[st.GetOriginalTxnDetails().GetResponseCode()]; ok {
					status = finalStatus()
				}
			}
		} else {
			status.SetDebugMessage(st.GetReason())
		}

		return &paymentPb.GetB2CTransactionStatusResponse{
			Status:       status,
			Utr:          st.GetOriginalTxnDetails().GetUtr(),
			ResponseCode: st.GetOriginalTxnDetails().GetResponseCode(),
			Reason:       st.GetOriginalTxnDetails().GetResponseReason(),
		}, nil
	}
	logger.InfoNoCtx("Got unknown response",
		zap.String("ResponseCode", st.Response),
		zap.String("Reason", st.Reason),
	)
	res := &paymentPb.GetB2CTransactionStatusResponse{
		Status:       rpc.StatusUnknown(),
		ResponseCode: st.GetResponse(),
		Reason:       st.GetReason(),
	}
	return res, nil
}

// getTransactionType returns the Transaction Type to be used in Federal's Enquiry Status API
func getTransactionType(paymentProtocol payment.PaymentProtocol) (string, error) {
	switch paymentProtocol {
	case payment.PaymentProtocol_INTRA_BANK:
		return "FT", nil
	case payment.PaymentProtocol_NEFT:
		return "NEFT", nil
	case payment.PaymentProtocol_IMPS:
		return "IMPS", nil
	case payment.PaymentProtocol_RTGS:
		return "RTGS", nil
	case payment.PaymentProtocol_UPI:
		return "UPI", nil
	default:
		return "", fmt.Errorf("received invalid payment protocol value: %s", paymentProtocol)
	}
}
