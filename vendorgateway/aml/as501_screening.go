package aml

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/aml"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const (
	AS501DateFormat       = "02-Jan-2006"
	AS501Purpose01        = "01" // Initial Screening
	AS501Purpose04        = "04" // Continuous Screening
	AS501CountryCodeIndia = "IND"
)

type as501ScreenCustomerReq struct {
	method     string
	req        *amlVgPb.ScreenCustomerRequest
	config     *config.Tss
	encryption *AS501Encryption
}

func (s *as501ScreenCustomerReq) Marshal() ([]byte, error) {
	// Get TSS config for owner
	// TODO: Revisit this
	tssConfigForOwner, err := s.getTSSConfigForOwner(s.req.GetOwner())
	if err != nil {
		return nil, errors.Wrap(err, "error getting TSS service config")
	}

	customerData, err := s.convertToAS501CustomerData()
	if err != nil {
		return nil, errors.Wrap(err, "error converting customer data")
	}
	as501Request := &tss.AS501Request{
		RequestId:        s.req.GetVendorRequestId(),
		SourceSystemName: tssConfigForOwner.SystemName,
		// TODO: Change purpose code
		Purpose:      AS501Purpose01, // Default to initial screening
		CustomerList: []*tss.CustomerData{customerData},
		ApiToken:     tssConfigForOwner.ApiToken,
	}
	encryptedData, sessionKey, signature, err := s.encryption.EncryptRequest(as501Request)
	if err != nil {
		return nil, errors.Wrap(err, "error encrypting AS501 request")
	}
	encryptedReq := &tss.EncryptedRequest{
		EncryptedData: encryptedData,
		EncryptionKey: sessionKey,
		Signature:     signature,
	}
	return protojson.Marshal(encryptedReq)
}

func (s *as501ScreenCustomerReq) convertToAS501CustomerData() (*tss.CustomerData, error) {
	customerDetails := s.req.GetCustomerDetails()
	if customerDetails == nil {
		return nil, errors.New("customer details are required")
	}
	gender, err := convertToAS501Gender(customerDetails.GetGender())
	if err != nil {
		return nil, errors.Wrap(err, "error converting gender to code")
	}
	// TODO: Map on-prem products to correct cloud products / screening lists
	// product, err := convertToAS501Product(s.req.GetProduct())
	// if err != nil {
	// 	return nil, errors.Wrap(err, "error converting product to code")
	// }
	nationality, err := convertToAS501Nationality(customerDetails.GetNationality())
	if err != nil {
		return nil, errors.Wrap(err, "error converting nationality to code")
	}
	permanentAddress := customerDetails.GetPermanentAddress()
	correspondenceAddress := customerDetails.GetCorrespondenceAddress()
	dateOfBirth := ""
	if customerDetails.GetDateOfBirth() != nil {
		dateOfBirth = datetime.DateToString(customerDetails.GetDateOfBirth(), AS501DateFormat, datetime.IST)
		// TODO: error if date of birth is in future
	}

	// Convert address lines
	permanentAddressLines := permanentAddress.GetAddressLines()
	correspondenceAddressLines := correspondenceAddress.GetAddressLines()
	permanentAddressLine1 := ""
	permanentAddressLine2 := ""
	permanentAddressLine3 := ""
	if len(permanentAddressLines) > 0 {
		permanentAddressLine1 = permanentAddressLines[0]
		if len(permanentAddressLines) > 1 {
			permanentAddressLine2 = permanentAddressLines[1]
			if len(permanentAddressLines) > 2 {
				permanentAddressLine3 = permanentAddressLines[2]
			}
		}
	}
	correspondenceAddressLine1 := ""
	correspondenceAddressLine2 := ""
	correspondenceAddressLine3 := ""
	if len(correspondenceAddressLines) > 0 {
		correspondenceAddressLine1 = correspondenceAddressLines[0]
		if len(correspondenceAddressLines) > 1 {
			correspondenceAddressLine2 = correspondenceAddressLines[1]
			if len(correspondenceAddressLines) > 2 {
				correspondenceAddressLine3 = correspondenceAddressLines[2]
			}
		}
	}

	return &tss.CustomerData{
		// TODO: Understand diff b/w actor id and request id and send it in appropriate fields to TSS
		SourceSystemCustomerCode:         s.req.GetRecordIdentifier(),
		UniqueIdentifier:                 s.req.GetRecordIdentifier(),
		SourceSystemCustomerCreationDate: time.Now().Format(AS501DateFormat),
		// TODO: Find corresponding fields for
		// 					RequestId:        s.req.GetVendorRequestId(),
		//			RecordIdentifier: s.req.GetRecordIdentifier(),
		//			SystemName:       tssConfigForOwner.SystemName,
		//			ParentCompany:    tssConfigForOwner.ParentCompany,
		//			Products: &Products{
		//				Product: []string{product},
		//			},

		FirstName:            customerDetails.GetName().GetFirstName(),
		MiddleName:           customerDetails.GetName().GetMiddleName(),
		LastName:             customerDetails.GetName().GetLastName(),
		ConstitutionType:     "1", // Individual
		Gender:               gender,
		Pan:                  customerDetails.GetPanNumber(),
		DateOfBirth:          dateOfBirth,
		Nationalities:        strings.Join([]string{nationality}, ","),
		PassportNumber:       customerDetails.GetPassportNumber(),
		DrivingLicenseNumber: customerDetails.GetDrivingLicenseNumber(),

		// Correspondence address
		CorrespondenceAddressLine1:   correspondenceAddressLine1,
		CorrespondenceAddressLine2:   correspondenceAddressLine2,
		CorrespondenceAddressLine3:   correspondenceAddressLine3,
		CorrespondenceAddressCity:    correspondenceAddress.GetLocality(),
		CorrespondenceAddressState:   correspondenceAddress.GetAdministrativeArea(),
		CorrespondenceAddressCountry: AS501CountryCodeIndia, // Default to India
		CorrespondenceAddressZipCode: correspondenceAddress.GetPostalCode(),

		// Permanent address
		PermanentAddressLine1:   permanentAddressLine1,
		PermanentAddressLine2:   permanentAddressLine2,
		PermanentAddressLine3:   permanentAddressLine3,
		PermanentAddressCity:    permanentAddress.GetLocality(),
		PermanentAddressState:   permanentAddress.GetAdministrativeArea(),
		PermanentAddressCountry: AS501CountryCodeIndia, // Default to India
		PermanentAddressZipCode: permanentAddress.GetPostalCode(),

		// TODO: Find AS501 fields for the below fields
		// CreateAlert:                  "Yes",
		// ScreeningCategory:            ScreeningCategory,

		// TODO: Add other fields if FL1-FL43 fields if mandatory
	}, nil
}

func (s *as501ScreenCustomerReq) getTSSConfigForOwner(owner common.Owner) (*config.TSSService, error) {
	switch owner {
	case common.Owner_OWNER_EPIFI_TECH:
		return s.config.Epifi, nil
	case common.Owner_OWNER_STOCK_GUARDIAN_TSP:
		return s.config.StockGuardian, nil
	default:
		return nil, errors.Errorf("unexpected owner: %s", owner.String())
	}
}

func (s *as501ScreenCustomerReq) URL() string {
	tssConfigForOwner, err := s.getTSSConfigForOwner(s.req.GetOwner())
	if err != nil {
		panic(errors.Wrapf(err, "error getting TSS config for owner: %s", s.req.GetOwner().String()))
	}
	return tssConfigForOwner.ScreeningUrl
}

func (s *as501ScreenCustomerReq) HTTPMethod() string {
	return s.method
}

func (s *as501ScreenCustomerReq) Add(req *http.Request) *http.Request {
	// log header
	// add error handling

	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", "https://epifitechnologiespvtltd-sb.trackwizz.app/customerinfo/as501")
	tssConfigForOwner, err := s.getTSSConfigForOwner(s.req.GetOwner())
	if err != nil {
		logger.ErrorNoCtx("error getting TSS config for owner", zap.Error(err))
		// return nil, errors.Wrap(err, "error getting TSS service config")
	}
	req.Header.Add("ApiToken", tssConfigForOwner.ApiToken)
	logger.InfoNoCtx("AS501 request header", zap.Any("header", req.Header))
	return req
}

func (s *as501ScreenCustomerReq) GetResponse() vendorapi.Response {
	return &as501ScreenCustomerRes{
		encryption: s.encryption,
	}
}

func (s *as501ScreenCustomerReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

type as501ScreenCustomerRes struct {
	encryption *AS501Encryption
}

func (s *as501ScreenCustomerRes) Unmarshal(b []byte) (proto.Message, error) {
	logger.InfoNoCtx("AS501 response", zap.String("response", string(b)))
	encryptedRes := &tss.EncryptedResponse{}
	if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, encryptedRes); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling AS501 response")
	}
	decryptedData, err := s.encryption.DecryptResponse(
		encryptedRes.GetEncryptedData(),
		encryptedRes.GetEncryptionKey(),
		encryptedRes.GetSignature(),
	)
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting AS501 response")
	}
	as501Res := &tss.AS501Response{}
	err = (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(decryptedData, as501Res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling decrypted AS501 response")
	}
	return s.convertAS501ResToVGRes(as501Res)
}

func (s *as501ScreenCustomerRes) convertAS501ResToVGRes(as501Response *tss.AS501Response) (*amlVgPb.ScreenCustomerResponse, error) {
	// Check overall status
	if as501Response.GetOverallStatus() != "AcceptedByTW" {
		return &amlVgPb.ScreenCustomerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("AS501 API error: %s", as501Response.GetValidationDescription())),
		}, nil
	}

	// Process customer responses
	if len(as501Response.GetCustomerResponse()) == 0 {
		return &amlVgPb.ScreenCustomerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("No customer response received from AS501 API"),
		}, nil
	}

	customerResponse := as501Response.GetCustomerResponse()[0]
	if customerResponse.GetValidationOutcome() != "Success" {
		return &amlVgPb.ScreenCustomerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("Customer validation failed: %s", customerResponse.GetValidationDescription())),
		}, nil
	}

	// Process purpose responses
	var matchStatus amlVgPb.MatchStatus
	var alertCount uint64
	var matchDetails []*aml.MatchDetails

	for _, purposeResponse := range customerResponse.GetPurposeResponse() {
		if purposeResponse.GetPurposeCode() == AS501Purpose01 {
			data := purposeResponse.GetData()
			if data.GetHitsDetected() == "Yes" {
				matchStatus = amlVgPb.MatchStatus_MATCH_STATUS_MATCHED
				alertCount = uint64(data.GetHitsCount())

				// Parse report data if available
				if data.GetReportData() != "" {
					// TODO: Parse base64 report data to extract match details
					// This would require additional parsing logic based on the report format
				}
			} else {
				matchStatus = amlVgPb.MatchStatus_MATCH_STATUS_NOT_MATCHED
			}
			break
		}
	}

	return &amlVgPb.ScreenCustomerResponse{
		Status:           rpcPb.StatusOk(),
		RejectionMessage: "",
		RejectionCode:    0,
		RecordIdentifier: customerResponse.GetSourceSystemCustomerCode(),
		MatchStatus:      matchStatus,
		CaseId:           "",
		CaseLink:         "",
		AlertCount:       alertCount,
		MatchDetails:     matchDetails,
	}, nil
}

func (s *as501ScreenCustomerRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in AS501 screening API", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}

func convertToAS501Gender(gender common.Gender) (string, error) {
	switch gender {
	case common.Gender_MALE:
		return "01", nil
	case common.Gender_FEMALE:
		return "02", nil
	case common.Gender_TRANSGENDER:
		return "03", nil
	default:
		return "", errors.New(fmt.Sprintf("unsupported gender type: %s", gender.String()))
	}
}

func convertToAS501Product(product aml.AmlProduct) (string, error) {
	// TODO: Add correct list
	switch product {
	case aml.AmlProduct_AML_PRODUCT_MUTUAL_FUNDS:
		return "MF", nil
	case aml.AmlProduct_AML_PRODUCT_US_STOCKS:
		return "USS", nil
	case aml.AmlProduct_AML_PRODUCT_SAVINGS_ACCOUNT:
		return "SA", nil
	case aml.AmlProduct_AML_PRODUCT_LOANS:
		return "NBFC", nil
	default:
		return "", errors.Errorf("unsupported product type: %s", product.String())
	}
}

func convertToAS501Nationality(nationality common.Nationality) (string, error) {
	switch nationality {
	case common.Nationality_NATIONALITY_INDIAN:
		return "IND", nil
	default:
		return "", errors.Errorf("unsupported nationality type: %s", nationality.String())
	}
}
