package aml

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/aml"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const (
	Individual        = "IND"
	DateFormat        = "02-Jan-2006"
	IndiaCode         = "IND"
	ScreeningCategory = "Initial Screening Master"
)

func (s *screenCustomerReq) getTSSConfigForOwner(owner common.Owner) (*config.TSSService, error) {
	switch owner {
	case common.Owner_OWNER_EPIFI_TECH:
		return s.config.Epifi, nil
	case common.Owner_OWNER_STOCK_GUARDIAN_TSP:
		return s.config.StockGuardian, nil
	default:
		return nil, errors.Errorf("unexpected owner: %s", owner.String())
	}
}

type screenCustomerReq struct {
	method string
	req    *amlVgPb.ScreenCustomerRequest
	config *config.Tss
}

// nolint:
func (s *screenCustomerReq) Marshal() ([]byte, error) {
	gender, err := convertToTssGender(s.req.GetCustomerDetails().GetGender())
	if err != nil {
		return nil, err
	}
	product, err := convertToTssProduct(s.req.GetProduct())
	if err != nil {
		return nil, err
	}
	nationality, err := convertToTssNationality(s.req.GetCustomerDetails().GetNationality())
	if err != nil {
		return nil, err
	}
	var corAdd1, corAdd2, corAdd3, perAdd1, perAdd2, perAdd3 string
	corAddLines := s.req.GetCustomerDetails().GetCorrespondenceAddress().GetAddressLines()
	if len(corAddLines) > 0 {
		corAdd1 = corAddLines[0]
		if len(corAddLines) > 1 {
			corAdd2 = corAddLines[1]
			if len(corAddLines) > 2 {
				corAdd2 = corAddLines[2]
			}
		}
	}
	perAddLines := s.req.GetCustomerDetails().GetPermanentAddress().GetAddressLines()
	if len(perAddLines) > 0 {
		perAdd1 = perAddLines[0]
		if len(perAddLines) > 1 {
			perAdd2 = perAddLines[1]
			if len(perAddLines) > 2 {
				perAdd3 = perAddLines[2]
			}
		}
	}
	tssConfigForOwner, err := s.getTSSConfigForOwner(s.req.GetOwner())
	if err != nil {
		return nil, errors.Wrap(err, "error getting TSS service config")
	}
	req := &ScreeningRequestData{
		XMLName:  xml.Name{},
		Xsi:      "http://www.w3.org/2001/XMLSchema-instance",
		Xsd:      "http://www.w3.org/2001/XMLSchema",
		ApiToken: tssConfigForOwner.ApiToken,
		Records: &Records{
			Record: &Record{
				RequestId:        s.req.GetVendorRequestId(),
				RecordIdentifier: s.req.GetRecordIdentifier(),
				SystemName:       tssConfigForOwner.SystemName,
				ParentCompany:    tssConfigForOwner.ParentCompany,
				Products: &Products{
					Product: []string{product},
				},
				FirstName:                    s.req.GetCustomerDetails().GetName().GetFirstName(),
				MiddleName:                   s.req.GetCustomerDetails().GetName().GetMiddleName(),
				LastName:                     s.req.GetCustomerDetails().GetName().GetLastName(),
				CustomerCategory:             Individual,
				Gender:                       gender,
				Pan:                          s.req.GetCustomerDetails().GetPanNumber(),
				DateOfBirth:                  datetime.DateToString(s.req.GetCustomerDetails().GetDateOfBirth(), DateFormat, datetime.IST),
				Nationality:                  nationality,
				Passport:                     s.req.GetCustomerDetails().GetPassportNumber(),
				DrivingLicenceNumber:         s.req.GetCustomerDetails().GetDrivingLicenseNumber(),
				CorrespondenceAddress1:       corAdd1,
				CorrespondenceAddress2:       corAdd2,
				CorrespondenceAddress3:       corAdd3,
				CorrespondenceAddressCity:    s.req.GetCustomerDetails().GetCorrespondenceAddress().GetLocality(),
				CorrespondenceAddressState:   s.req.GetCustomerDetails().GetCorrespondenceAddress().GetAdministrativeArea(),
				CorrespondenceAddressCountry: IndiaCode,
				CorrespondenceAddressPin:     s.req.GetCustomerDetails().GetCorrespondenceAddress().GetPostalCode(),
				PermanentAddress1:            perAdd1,
				PermanentAddress2:            perAdd2,
				PermanentAddress3:            perAdd3,
				PermanentAddressCity:         s.req.GetCustomerDetails().GetPermanentAddress().GetLocality(),
				PermanentAddressState:        s.req.GetCustomerDetails().GetPermanentAddress().GetAdministrativeArea(),
				PermanentAddressCountry:      IndiaCode,
				PermanentAddressPin:          s.req.GetCustomerDetails().GetPermanentAddress().GetPostalCode(),
				CreateAlert:                  "Yes",
				ScreeningCategory:            ScreeningCategory,
			},
		},
	}
	xmlData, xmlErr := xml.Marshal(req)
	if xmlErr != nil {
		return nil, xmlErr
	}
	jsonReq := &tss.ScreenCustomerRequest{
		XmlString: string(xmlData),
	}
	return protojson.Marshal(jsonReq)
}

func (s *screenCustomerReq) URL() string {
	tssConfigForOwner, err := s.getTSSConfigForOwner(s.req.GetOwner())
	if err != nil {
		panic(errors.Wrapf(err, "error getting TSS config for owner: %s", s.req.GetOwner().String()))
	}
	return tssConfigForOwner.ScreeningUrl
}

func (s *screenCustomerReq) HTTPMethod() string {
	return s.method
}

func (s *screenCustomerReq) Add(req *http.Request) *http.Request {
	return req
}

func (s *screenCustomerReq) GetResponse() vendorapi.Response {
	return &screenCustomerRes{}
}

func (s *screenCustomerReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

type screenCustomerRes struct{}

func (s *screenCustomerRes) Unmarshal(b []byte) (proto.Message, error) {
	jsonResp := &tss.ScreenCustomerResponse{}
	jsonErr := protojson.Unmarshal(b, jsonResp)
	if jsonErr != nil {
		return nil, errors.New(fmt.Sprintf("error while unmarshaling screen customer response: %v", jsonErr))
	}
	// xml.Unmarshal works only for UTF-8 format. This vendor's response is in UTF-16 format, so we have to use a custom charset reader
	// this charset reader just passes on the input as is, ref - https://dzhg.dev/posts/2020/08/how-to-parse-xml-with-non-utf8-encoding-in-go/
	charsetReader := func(charset string, input io.Reader) (io.Reader, error) {
		return input, nil
	}
	if jsonResp.GetErrorXml() != "" {
		errorMessage := &ErrorMessage{}
		decoder := xml.NewDecoder(bytes.NewBuffer([]byte(jsonResp.GetErrorXml())))
		decoder.CharsetReader = charsetReader
		if xmlErr := decoder.Decode(&errorMessage); xmlErr != nil {
			return nil, errors.New(fmt.Sprintf("error while unmarshaling error message: %v", xmlErr))
		}
		return &amlVgPb.ScreenCustomerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("error in vendor response: %v", errorMessage.ErrorMessage)),
		}, nil
	}
	if jsonResp.GetResultXml() == "" {
		return nil, errors.New(fmt.Sprintf("empty result in vendor response"))
	}

	result := &ScreeningResults{}
	decoder := xml.NewDecoder(bytes.NewBuffer([]byte(jsonResp.GetResultXml())))
	decoder.CharsetReader = charsetReader
	if xmlErr := decoder.Decode(&result); xmlErr != nil {
		return nil, errors.New(fmt.Sprintf("error while unmarshaling screen customer response: %v", xmlErr))
	}
	screeningResponse, pErr := parseScreenCustomerXMLResponse(result)
	if pErr != nil {
		return nil, errors.New(fmt.Sprintf("error while parsing screen customer response xml: %v", pErr))
	}
	return screeningResponse, nil
}

func (s *screenCustomerRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}

func parseScreenCustomerXMLResponse(xmlRes *ScreeningResults) (*amlVgPb.ScreenCustomerResponse, error) {
	var alertCount uint64
	var aErr error
	if xmlRes.ScreeningResult.RecordIdentifier == "" {
		return nil, errors.New("empty record identifier")
	}
	if xmlRes.ScreeningResult.AlertCount != "" {
		alertCount, aErr = strconv.ParseUint(xmlRes.ScreeningResult.AlertCount, 10, 64)
	}
	if aErr != nil {
		return nil, errors.New(fmt.Sprintf("alertCount is not a number"))
	}
	matchDetails, mErr := parseMatchDataXML(xmlRes)
	if mErr != nil {
		return nil, mErr
	}
	return &amlVgPb.ScreenCustomerResponse{
		Status:           rpcPb.StatusOk(),
		RejectionMessage: xmlRes.ScreeningResult.RejectionMessage,
		RejectionCode:    convertToBERejectionCode(xmlRes.ScreeningResult.RejectionCode),
		RecordIdentifier: xmlRes.ScreeningResult.RecordIdentifier,
		MatchStatus:      ConvertToBEMatchStatus(xmlRes.ScreeningResult.Matched),
		CaseId:           xmlRes.ScreeningResult.CaseId,
		CaseLink:         xmlRes.ScreeningResult.Link,
		AlertCount:       alertCount,
		MatchDetails:     matchDetails,
	}, nil
}

func parseMatchDataXML(xmlRes *ScreeningResults) ([]*aml.MatchDetails, error) {
	if len(xmlRes.ScreeningResult.Alerts.Alert) == 0 {
		return nil, nil
	}
	var matchDatas []*aml.MatchDetails
	for _, alert := range xmlRes.ScreeningResult.Alerts.Alert {
		matchingPercentage := 0.0
		if alert.MatchingPercentage != "" {
			var fErr error
			matchingPercentage, fErr = strconv.ParseFloat(alert.MatchingPercentage, 64)
			if fErr != nil {
				return nil, errors.New("matchingPercentage is not a decimal")
			}
		}
		matchDatas = append(matchDatas, &aml.MatchDetails{
			MatchId:                 alert.AlertId,
			Watchlist:               alert.Source,
			MatchingRule:            alert.Rule,
			WatchlistRecordId:       alert.SourceUniqueId,
			VendorWatchlistRecordId: alert.AmlWatchlistId,
			WatchlistRecordName:     alert.WatchListName,
			MatchingPercentage:      matchingPercentage,
			IsActive:                convertTobool(alert.IsActive),
			MatchingParameter:       ConvertToBEParameter(alert.Activity),
			MatchType:               ConvertToBEMatchType(alert.MatchType),
		})
	}
	return matchDatas, nil
}
