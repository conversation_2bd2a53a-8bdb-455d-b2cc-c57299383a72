package aml

import (
	"bytes"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/sha512"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"

	"github.com/pkg/errors"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/transform"
)

// AS501Encryption handles encryption for the AS501 API
type AS501Encryption struct {
	privateKey *rsa.PrivateKey
	publicKey  *rsa.PublicKey
}

// NewAS501Encryption creates a new encryption instance
func NewAS501Encryption(privateKeyPEM, publicKeyPEM string) (*AS501Encryption, error) {
	// Parse private key
	privateKeyBlock, _ := pem.Decode([]byte(privateKeyPEM))
	if privateKeyBlock == nil {
		return nil, errors.New("failed to decode private key PEM")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse private key")
	}
	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("private key is not RSA")
	}

	// Parse public key
	publicKeyBlock, _ := pem.Decode([]byte(publicKeyPEM))
	if publicKeyBlock == nil {
		return nil, errors.New("failed to decode public key PEM")
	}

	cert, err := x509.ParseCertificate(publicKeyBlock.Bytes)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse public key")
	}

	rsaPublicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("public key is not RSA")
	}

	return &AS501Encryption{
		privateKey: rsaPrivateKey,
		publicKey:  rsaPublicKey,
	}, nil
}

// EncryptRequest encrypts the AS501 request data
func (e *AS501Encryption) EncryptRequest(requestData interface{}) (string, string, string, error) {
	// Convert request data to JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to marshal request data to JSON")
	}

	// Generate 256-bit session key
	// sessionKey := make([]byte, 16) // 256 bits = 32 bytes
	// if _, err := io.ReadFull(rand.Reader, sessionKey); err != nil {
	// 	return "", "", "", errors.Wrap(err, "failed to generate session key")
	// }

	sessionKey := make([]byte, 128/8)
	_, err = rand.Read(sessionKey)
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to generate session key")
	}

	// Encrypt data with AES-256
	encryptedData, err := e.encryptAES(jsonData, sessionKey)
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to encrypt data with AES")
	}

	// Base64 encode encrypted data
	encryptedDataBase64 := base64.StdEncoding.EncodeToString(encryptedData)

	// Encrypt session key with RSA
	sessionKeyBase64 := base64.StdEncoding.EncodeToString(sessionKey)

	encryptedSessionKey, err := rsa.EncryptPKCS1v15(rand.Reader, e.publicKey, []byte(sessionKeyBase64))
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to encrypt session key with RSA")
	}

	// Base64 encode encrypted session key
	encryptedSessionKeyBase64 := base64.StdEncoding.EncodeToString(encryptedSessionKey)

	// Create signature
	signatureData := encryptedDataBase64 + encryptedSessionKeyBase64
	signature, err := e.signData([]byte(signatureData))
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to sign data")
	}

	// Base64 encode signature
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	return encryptedDataBase64, encryptedSessionKeyBase64, signatureBase64, nil
}

// DecryptResponse decrypts the AS501 response data
func (e *AS501Encryption) DecryptResponse(encryptedDataBase64, encryptedSessionKeyBase64, signatureBase64 string) ([]byte, error) {
	// Verify signature
	signatureData := encryptedDataBase64 + encryptedSessionKeyBase64
	signature, err := base64.StdEncoding.DecodeString(signatureBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode signature")
	}

	if err := e.verifySignature([]byte(signatureData), signature); err != nil {
		return nil, errors.Wrap(err, "failed to verify signature")
	}

	// Decrypt session key
	encryptedSessionKey, err := base64.StdEncoding.DecodeString(encryptedSessionKeyBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode encrypted session key")
	}

	sessionKey, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, e.privateKey, encryptedSessionKey, nil)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decrypt session key")
	}

	// Decrypt data
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedDataBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode encrypted data")
	}

	decryptedData, err := e.decryptAES(encryptedData, sessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decrypt data")
	}

	return decryptedData, nil
}

func padPKCS7(input []byte, blockSize int) []byte {
	padding := blockSize - (len(input) % blockSize)
	pad := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(input, pad...)
}

// encryptAES encrypts data using AES-256 in CBC mode
func (e *AS501Encryption) encryptAES(data, key []byte) ([]byte, error) {

	data = padPKCS7(data, aes.BlockSize)

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Generate IV
	// iv := make([]byte, aes.BlockSize)
	// if _, err := io.ReadFull(rand.Reader, iv); err != nil {
	// 	return nil, err
	// }

	// Pad data to block size
	// paddedData := e.pkcs7Pad(data, aes.BlockSize)
	//
	// // Encrypt
	// ciphertext := make([]byte, len(iv)+len(paddedData))
	// copy(ciphertext, iv)

	ciphertext := make([]byte, len(data))

	// mode := cipher.NewCBCEncrypter(block, iv)
	mode := NewECBEncrypter(block)
	// mode.CryptBlocks(ciphertext[len(iv):], paddedData)
	mode.CryptBlocks(ciphertext, data)

	return ciphertext, nil
}

// ecbEncrypter encrypts a single block using ECB mode
type ecbEncrypter struct {
	b         cipher.Block
	blockSize int
}

func NewECBEncrypter(b cipher.Block) *ecbEncrypter {
	return &ecbEncrypter{b, b.BlockSize()}
}

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }

func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("input not full blocks")
	}
	if len(dst) < len(src) {
		panic("output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

// decryptAES decrypts data using AES-256 in CBC mode
func (e *AS501Encryption) decryptAES(ciphertext, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	if len(ciphertext) < aes.BlockSize {
		return nil, errors.New("ciphertext too short")
	}

	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	if len(ciphertext)%aes.BlockSize != 0 {
		return nil, errors.New("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Remove padding
	plaintext, err = e.pkcs7Unpad(plaintext)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// signData signs data using RSA-SHA256
func (e *AS501Encryption) signData(data []byte) ([]byte, error) {
	data, err := Utf16leBytes(string(data))
	if err != nil {
		return nil, errors.Wrap(err, "error converting string to utf16le bytes")
	}
	hash := sha512.Sum512(data)
	signature, err := rsa.SignPKCS1v15(nil, e.privateKey, crypto.SHA512, hash[:])
	if err != nil {
		return nil, err
	}
	return signature, nil
}

// Utf16leBytes converts a string to UTF-16LE encoded bytes
func Utf16leBytes(s string) ([]byte, error) {
	utf16leEncoder := unicode.UTF16(unicode.LittleEndian, unicode.IgnoreBOM).NewEncoder()
	encodedBytes, _, err := transform.Bytes(utf16leEncoder, []byte(s))
	if err != nil {
		return nil, err
	}
	return encodedBytes, nil
}

// verifySignature verifies signature using RSA-SHA256
func (e *AS501Encryption) verifySignature(data, signature []byte) error {
	hash := sha256.Sum256(data)
	err := rsa.VerifyPKCS1v15(e.publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	return nil
}

// pkcs7Pad adds PKCS7 padding to data
func (e *AS501Encryption) pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// pkcs7Unpad removes PKCS7 padding from data
func (e *AS501Encryption) pkcs7Unpad(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("invalid padding")
	}

	padding := int(data[length-1])
	if padding > length {
		return nil, errors.New("invalid padding")
	}

	// Verify padding
	for i := length - padding; i < length; i++ {
		if data[i] != byte(padding) {
			return nil, errors.New("invalid padding")
		}
	}

	return data[:length-padding], nil
}
