package aml

import (
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"

	"github.com/pkg/errors"
)

// AS501Encryption handles encryption for the AS501 API
type AS501Encryption struct {
	privateKey *rsa.PrivateKey
	publicKey  *rsa.PublicKey
}

// NewAS501Encryption creates a new encryption instance
func NewAS501Encryption(privateKeyPEM, publicKeyPEM string) (*AS501Encryption, error) {
	// Parse private key
	privateKeyBlock, _ := pem.Decode([]byte(privateKeyPEM))
	if privateKeyBlock == nil {
		return nil, errors.New("failed to decode private key PEM")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse private key")
	}
	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("private key is not RSA")
	}

	// Parse public key
	publicKeyBlock, _ := pem.Decode([]byte(publicKeyPEM))
	if publicKeyBlock == nil {
		return nil, errors.New("failed to decode public key PEM")
	}

	publicKey, err := x509.ParsePKIXPublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse public key")
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("public key is not RSA")
	}

	return &AS501Encryption{
		privateKey: rsaPrivateKey,
		publicKey:  rsaPublicKey,
	}, nil
}

// EncryptRequest encrypts the AS501 request data
func (e *AS501Encryption) EncryptRequest(requestData interface{}) (string, string, string, error) {
	// Convert request data to JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to marshal request data to JSON")
	}

	// Generate 256-bit session key
	sessionKey := make([]byte, 32) // 256 bits = 32 bytes
	if _, err := io.ReadFull(rand.Reader, sessionKey); err != nil {
		return "", "", "", errors.Wrap(err, "failed to generate session key")
	}

	// Encrypt data with AES-256
	encryptedData, err := e.encryptAES(jsonData, sessionKey)
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to encrypt data with AES")
	}

	// Base64 encode encrypted data
	encryptedDataBase64 := base64.StdEncoding.EncodeToString(encryptedData)

	// Encrypt session key with RSA
	encryptedSessionKey, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, e.publicKey, sessionKey, nil)
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to encrypt session key with RSA")
	}

	// Base64 encode encrypted session key
	encryptedSessionKeyBase64 := base64.StdEncoding.EncodeToString(encryptedSessionKey)

	// Create signature
	signatureData := encryptedDataBase64 + encryptedSessionKeyBase64
	signature, err := e.signData([]byte(signatureData))
	if err != nil {
		return "", "", "", errors.Wrap(err, "failed to sign data")
	}

	// Base64 encode signature
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	return encryptedDataBase64, encryptedSessionKeyBase64, signatureBase64, nil
}

// DecryptResponse decrypts the AS501 response data
func (e *AS501Encryption) DecryptResponse(encryptedDataBase64, encryptedSessionKeyBase64, signatureBase64 string) ([]byte, error) {
	// Verify signature
	signatureData := encryptedDataBase64 + encryptedSessionKeyBase64
	signature, err := base64.StdEncoding.DecodeString(signatureBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode signature")
	}

	if err := e.verifySignature([]byte(signatureData), signature); err != nil {
		return nil, errors.Wrap(err, "failed to verify signature")
	}

	// Decrypt session key
	encryptedSessionKey, err := base64.StdEncoding.DecodeString(encryptedSessionKeyBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode encrypted session key")
	}

	sessionKey, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, e.privateKey, encryptedSessionKey, nil)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decrypt session key")
	}

	// Decrypt data
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedDataBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode encrypted data")
	}

	decryptedData, err := e.decryptAES(encryptedData, sessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decrypt data")
	}

	return decryptedData, nil
}

// encryptAES encrypts data using AES-256 in CBC mode
func (e *AS501Encryption) encryptAES(data, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Generate IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	// Pad data to block size
	paddedData := e.pkcs7Pad(data, aes.BlockSize)

	// Encrypt
	ciphertext := make([]byte, len(iv)+len(paddedData))
	copy(ciphertext, iv)

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[len(iv):], paddedData)

	return ciphertext, nil
}

// decryptAES decrypts data using AES-256 in CBC mode
func (e *AS501Encryption) decryptAES(ciphertext, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	if len(ciphertext) < aes.BlockSize {
		return nil, errors.New("ciphertext too short")
	}

	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	if len(ciphertext)%aes.BlockSize != 0 {
		return nil, errors.New("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Remove padding
	plaintext, err = e.pkcs7Unpad(plaintext)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// signData signs data using RSA-SHA256
func (e *AS501Encryption) signData(data []byte) ([]byte, error) {
	hash := sha256.Sum256(data)
	signature, err := rsa.SignPKCS1v15(nil, e.privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return nil, err
	}
	return signature, nil
}

// verifySignature verifies signature using RSA-SHA256
func (e *AS501Encryption) verifySignature(data, signature []byte) error {
	hash := sha256.Sum256(data)
	err := rsa.VerifyPKCS1v15(e.publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	return nil
}

// pkcs7Pad adds PKCS7 padding to data
func (e *AS501Encryption) pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// pkcs7Unpad removes PKCS7 padding from data
func (e *AS501Encryption) pkcs7Unpad(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("invalid padding")
	}

	padding := int(data[length-1])
	if padding > length {
		return nil, errors.New("invalid padding")
	}

	// Verify padding
	for i := length - padding; i < length; i++ {
		if data[i] != byte(padding) {
			return nil, errors.New("invalid padding")
		}
	}

	return data[:length-padding], nil
}
