package aml

import (
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"

	"net/http"

	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/vendorgateway/config"

	"google.golang.org/protobuf/proto"
)

type AmlFactory struct {
	conf *config.Config
}

func NewAmlFactory(conf *config.Config) *AmlFactory {
	return &AmlFactory{conf: conf}
}

func (f *AmlFactory) getRequestFactoryMap() map[commonvgpb.Vendor]vendorapi.SyncRequestFactory {
	return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
		commonvgpb.Vendor_TSS: f.NewTssRequest,
	}
}

func (f *AmlFactory) NewTssRequest(req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *aml.ScreenCustomerRequest:
		// Check if we should use the new AS501 API
		if f.conf.Application.Aml.Tss.UseCloudHostedAPI {
			// Initialize encryption for AS501 API
			// TODO: Migrate to cryptor interface
			encryption, err := NewAS501Encryption(
				f.conf.Application.Aml.Tss.PrivateKey,
				f.conf.Application.Aml.Tss.PublicKey,
			)
			if err != nil {
				logger.ErrorNoCtx("Failed to initialize AS501 encryption", zap.Error(err))
				return nil
				// // Fallback to old API if encryption setup fails
				// return &screenCustomerReq{
				// 	method: http.MethodPost,
				// 	req:    v,
				// 	config: f.conf.Application.Aml.Tss,
				// }
			}

			return &as501ScreenCustomerReq{
				method:     http.MethodPost,
				req:        v,
				config:     f.conf.Application.Aml.Tss,
				encryption: encryption,
			}
		}

		// Use old API
		return &screenCustomerReq{
			method: http.MethodPost,
			req:    v,
			config: f.conf.Application.Aml.Tss,
		}
	default:
		return nil
	}
}
