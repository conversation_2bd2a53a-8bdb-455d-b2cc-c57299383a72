SecureLogging:
  EnableSecureLog: true
  DefaultFileLoggingParams:
    LogPath: "/var/log/frontend/secure.log"
    MaxSizeInMBs: 50
    MaxBackups: 20

Profiling:
  StackDriverProfiling:
    EnableStackDriver: true

Flags:
  EnableRateLimitingInterceptor: true

RateLimiterConfig:
  Namespace: "frontend-rpc"
  # rate limit keys in map are as per GenerateKey method in pkg/epifigrpc/interceptors/ratelimit/helper.go
  ResourceMap:
    #    frontend_clientlogger_clientLogger_log:
    #      Rate: 30
    #      Period: 1s
    frontend_user_user_synccontactdetails:
      Rate: 50
      Period: 1s
    frontend_user_user_recordhashedcontacts:
      Rate: 20
      Period: 1s

Frontend:
  MaxGRPCTimeout: "2m"
  EnablePartnerLog: true
  IPInterceptorParams:
    EnableIPInterceptor: true
    BlockedIPAddresses: [ "**************", "*************", "*************", "**************", "*************" ]
  EnableDeviceIntegrityCheck: true
  EnableLocationInterceptor: true
  MockFEReqHeaderAndContextInterceptorParams:
    EnableMockInterceptor: false
  DeviceIntegrity:
    EnableWhitelistedTokens: false
    SkipAsyncDeviceIntegrityChecks: false
    WhitelistedTokensList: [ "DUMMY_TOKEN" ]
    DefaultHighRiskDeviceConsentDuration: "24h"
    MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
    AsyncDeviceIntegrityCheck:
      DisableFeature: false
      MinAndroidVersion: 183
      MinIosVersion: 100000
    AsyncDeviceIntegrityRolloutPercentage: 100
  ActorRequestTracingInterceptorParams:
    Enabled: true
    TraceActorIds:
      "AC210622KqPeekvVTOuMmZaVRMZBew==": true
      "AC210128Bx6k5I2YQqKO7nTLHnQ7eA==": true
      "AC230101AT+dOLTEQFS1NalKWpOdUQ==": true

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    RudderClientApiKey: "prod/rudder/android_write_key"
    AppsFlyerClientKey: "prod/appsflyer/client_key"
    OneMoneyClientSecret: "prod/onemoney/client_secret"
    DeviceIdsEnabledForSafetyNetV2: "prod/frontend-auth/v2_safetynet_enabled_device_ids"
    MoengageAppSdkKey: "prod/moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "prod/frontend-auth/device-integrity-token-signing-key"
    MiAmpPushSecretJson: "prod/mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "prod/appsflyer/client_key_2"
    RudderIosClientApiKey: "prod/rudder/ios_write_key"

RedisRateLimiterName: "HomeRedisStore"
RedisClusters:
  HomeRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/growth-infra/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: home
  AnalyserRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-13988.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:13988"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/demystifi/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: analyser
  QuestRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/growth-infra/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: frontend-quest-sdk
    HystrixCommand:
      CommandName: "quest_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 10000
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 50
        SleepWindow: 15s
        FallbackMaxConcurrency: 10000


EnableGetPaymentOptionsV1: false

GrpcRatelimiterParams:
  Disable: false

GrpcAttributeRatelimiterParams:
  ResourceMap:
    frontend_insights_networth_networth_magicimportfiles:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_CTX_ACTOR_ID"
          AttributeValueRegexp: ".*"
          Rate: 50
          Period: 24h
    frontend_account_signup_signup_loginwithoauth:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_account_signup_signup_addoauthaccount:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_account_signup_signup_fetchaccesstoken:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
    frontend_pay_transaction_transaction_createfundtransferorder:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 10
          Period: 1s
      EnableLogging: false
    frontend_pay_transaction_transaction_createfundtransferorderv1:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 10
          Period: 1s
      EnableLogging: false
    frontend_upi_onboarding_onboarding_balanceenquiry:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 50
          Period: 24h
      EnableLogging: false
  Namespace: "frontend-rpc"
  Disable: false

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    AuthDetails:
      SecretPath: "prod/redis/growth-infra/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: "frontend-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: true
