Application:
  Environment: "prod"
  Namespace: "prod-upi"
  TaskQueue: "prod-upi-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: upi-worker

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "upi-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "upi-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

WorkflowParamsList:
  - WorkflowName: "LinkUpiAccount"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntityForLinking"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CreateVpa"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CreateInternalPiForVpa"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "ActivateUpiAccount"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GetNotificationTemplateForVpaAction"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GetNotificationTemplateForPinSet"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "SendNotification"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "DelinkUpiAccountWithVendor"
    ChildWorkflowParamsList:
      - WorkflowName: "DelinkUpiNumber"
        WorkflowExecutionTimeout: "720h"
        WorkflowRunTimeout: "15m"
        RetryParams:
          # Exponential retry strategy that runs for ~6hr with max cap between retries at 6hr
          # Retry interval - 10s 20s 40s 1min20s 2min40s 5min20s 10min40s 21min20s 42min40s 1hr25min20s
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntityForDelinking"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GetDelinkUpiNumberWorkflowPayloads"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CheckDelinkUpiNumbersWorkflowsStatus"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "DelinkAccountWithVendor"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "DeactivatePi"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "DelinkUpiAccount"
    ActivityParamsList:
      - ActivityName: "MarkAccountAsDelinked"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "LinkUpiNumber"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "LinkUpiNumberWithVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CheckActionStatusWithVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpsertUpiNumberPiMapping"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GenerateNotificationForUpiNumberAction"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
  - WorkflowName: "DelinkUpiNumber"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "DelinkUpiNumberWithVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CheckActionStatusWithVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpsertUpiNumberPiMapping"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35

  - WorkflowName: "ActivateInternationalUpiPayments"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CheckIntlUpiPaymentsActionStatusWithVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpdateInternationalPaymentsInUpiControls"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GenerateNotificationForIntPaymentAction"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35

  - WorkflowName: "DeactivateInternationalUpiPayments"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CheckIntlUpiPaymentsActionStatusWithVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpdateInternationalPaymentsInUpiControls"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GenerateNotificationForIntPaymentAction"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "ActivateUpiLite"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpiLitePiCreation"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpiLitePiDeactivation"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CreateUpiLitePaymentOrder"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "EnquireOrderStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "ActivateUpiAccount"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GenerateNotificationForUpiLiteAction"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "DeactivateUpiLite"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "CreateUpiLitePaymentOrder"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "EnquireOrderStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpiLitePiDeactivation"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "MarkAccountAsDelinked"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GenerateNotificationForUpiLiteAction"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "DeactivateZeroBalanceUpiLite"
    ActivityParamsList:
      - ActivityName: "CreateUpiOnboardingEntity"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "SyncUpiLiteInfo"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "UpiLitePiDeactivation"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "MarkAccountAsDelinked"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
      - ActivityName: "GenerateNotificationForUpiLiteAction"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

UpiCacheConfig:
  IsCachingEnabled: false
  CacheTTl: "24h" # 1 day
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: upi

TpapAccountConnectionSnsPublisher:
  TopicName: "prod-tpap-account-connection-topic"

NudgeConfig:
  RupayCCAccountActivationIds:
    - "f41a7f8d-b935-4c38-b1c4-02b94b01b674"
