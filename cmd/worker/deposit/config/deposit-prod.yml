Application:
  Environment: "prod"
  Namespace: "prod-deposit"
  TaskQueue: "prod-deposit-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "deposit-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "deposit-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

InterestRateUpdateSlackChannel: "C04S8KRHHD5"
Secrets:
  Ids:
    DepositInterestSlackBotOauthToken: "prod/deposit/slack-bot-oauth-token"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"
