package networth

import (
	"context"
	"encoding/base64"
	"fmt"
	"sort"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/file"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/analyser/variables/mutualfund"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/enums"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthEnums "github.com/epifi/gamma/api/insights/networth/enums"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	networthTools "github.com/epifi/gamma/api/mcp/networth"
)

const (
	mfMaxTxnsAllowed      = 500
	mfOrderFetchPageSize  = 1000
	rawAATxnFetchPageSize = 100
)

type networthDataComponents struct {
	netWorthResp           *networthPb.GetNetWorthValueResponse
	mfSchemeAnalytics      *mutualfund.MfSchemeAnalytics
	aaAccountDetails       *connectedAccountPb.GetAccountDetailsBulkResponse
	creditReportsResp      *creditReportPb.GetCreditReportsResponse
	uanAccountsResp        *beEpfPb.GetUANAccountsResponse
	mfExternalOrders       []*mfExternalPb.MutualFundExternalOrder
	rawBankTransactions    []*networthTools.BankTransactions
	manualAssetsDetailsRes *networthPb.GetInvestmentDeclarationsResponse
}

func (s *Service) GetNetworthDataFile(ctx context.Context, req *networthPb.GetNetworthDataFileRequest) (*networthPb.GetNetworthDataFileResponse, error) {
	var (
		networthDataCollection *networthTools.NetWorthDataCollection
		err                    error
	)
	switch req.GetNetworthDataFileType() {
	case networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA:
		networthDataCollection, err = s.getNetworthDataFileContent(ctx, req.GetActorId())
	case networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA:
		networthDataCollection, err = s.getBankTransactionsFileContent(ctx, req.GetActorId())
	default:
		logger.Error(ctx, "unhandled networth data file type", zap.Any("type", req.GetNetworthDataFileType()))
		return &networthPb.GetNetworthDataFileResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unhandled networth data file type"),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error in while net worth data file content", zap.Error(err))
		return &networthPb.GetNetworthDataFileResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	serializedData, err := protojson.Marshal(networthDataCollection)
	if err != nil {
		logger.Error(ctx, "error marshaling networth data", zap.Error(err))
		return &networthPb.GetNetworthDataFileResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	encodedFileContent := base64.StdEncoding.EncodeToString(serializedData)
	networthFile := &file.File{
		FileName:   s.config.NetworthMcp().NetWorthDataFileName(),
		Type:       file.FileType_FILE_TYPE_TXT,
		Base64Data: encodedFileContent,
	}

	return &networthPb.GetNetworthDataFileResponse{
		Status:       rpc.StatusOk(),
		NetworthFile: networthFile,
	}, nil
}

func (s *Service) getNetworthDataFileContent(ctx context.Context, actorId string) (*networthTools.NetWorthDataCollection, error) {
	var (
		networthResponse         *networthPb.GetNetWorthValueResponse
		mutualFundAnalytics      *mutualfund.MfSchemeAnalytics
		accountAggregatorDetails *connectedAccountPb.GetAccountDetailsBulkResponse
		creditReports            *creditReportPb.GetCreditReportsResponse
		uanAccountsResp          *beEpfPb.GetUANAccountsResponse
		mutualFundOrders         []*mfExternalPb.MutualFundExternalOrder
		manualAssetsDetailsRes   *networthPb.GetInvestmentDeclarationsResponse
	)
	grp, gCtx := errgroup.WithContext(ctx)

	// total networth, includes vales for asset and liabilities
	grp.Go(func() error {
		var err error
		networthResponse, err = s.GetNetWorthValue(gCtx, &networthPb.GetNetWorthValueRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(networthResponse, err); rpcErr != nil {
			return fmt.Errorf("error from networth rpc: %v", rpcErr)
		}
		return nil
	})

	// mf portfolio holdings
	grp.Go(func() error {
		var err error
		mutualFundAnalytics, err = s.getMfHoldingDetails(gCtx, actorId)
		if err != nil {
			return fmt.Errorf("error while getting mf holdings details: %v", err)
		}
		return nil
	})

	// aa account details (includes all bank account, indian stocks, nps, deposits etc.)
	grp.Go(func() error {
		var err error
		accountAggregatorDetails, err = s.getAaAccountDetails(gCtx, actorId)
		if err != nil {
			return fmt.Errorf("error while getting aa account details: %v", err)
		}
		return nil
	})

	// credit report data
	grp.Go(func() error {
		var err error
		creditReports, err = s.creditReportClient.GetCreditReports(gCtx, &creditReportPb.GetCreditReportsRequest{
			ActorId: actorId,
			Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
			Limit:   2,
		})
		if rpcErr := epifigrpc.RPCError(creditReports, err); rpcErr != nil && !creditReports.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("failed to fetch credit report: %w", rpcErr)
		}
		return nil
	})

	// get all uan account details for actor
	grp.Go(func() error {
		var err error
		uanAccountsResp, err = s.epfClient.GetUANAccounts(gCtx, &beEpfPb.GetUANAccountsRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(uanAccountsResp, err); rpcErr != nil && !uanAccountsResp.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("failed to get uan accounts for actor: %w", rpcErr)
		}
		return nil
	})

	// get all external mf orders for actor in paginated way
	grp.Go(func() error {
		var err error
		mutualFundOrders, err = s.getMfExternalOrders(gCtx, actorId)
		if err != nil {
			return fmt.Errorf("error while getting all external orders: %w", err)
		}
		return nil
	})

	// get all manually connected assets details
	grp.Go(func() error {
		var err error
		manualAssetsDetailsRes, err = s.GetInvestmentDeclarations(gCtx, &networthPb.GetInvestmentDeclarationsRequest{
			ActorId:     actorId,
			PageContext: &rpc.PageContextRequest{PageSize: 25},
		})
		if rpcErr := epifigrpc.RPCError(manualAssetsDetailsRes, err); rpcErr != nil && !manualAssetsDetailsRes.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("error while getting manual asset details: %w", rpcErr)
		}
		return nil
	})

	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in while getting networth and holding details", zap.Error(err))
		return nil, errors.Wrap(err, "error in while getting networth and holding details")
	}

	networthDataCollection := s.getNetworthDataObject(&networthDataComponents{
		netWorthResp:           networthResponse,
		mfSchemeAnalytics:      mutualFundAnalytics,
		aaAccountDetails:       accountAggregatorDetails,
		creditReportsResp:      creditReports,
		uanAccountsResp:        uanAccountsResp,
		mfExternalOrders:       mutualFundOrders,
		manualAssetsDetailsRes: manualAssetsDetailsRes,
	})
	return networthDataCollection, nil
}

func (s *Service) getMfHoldingDetails(ctx context.Context, actorId string) (*mutualfund.MfSchemeAnalytics, error) {
	analysisVariableResponse, err := s.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
	})
	if rpcErr := epifigrpc.RPCError(analysisVariableResponse, err); rpcErr != nil {
		return nil, fmt.Errorf("error fetching analysis variable generator: %w", rpcErr)
	}
	analysisVariable, exists := analysisVariableResponse.GetVariableEnumMap()[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !exists {
		logger.Info(ctx, "mf scheme analytics not found for actor")
		return nil, nil
	}
	return analysisVariable.GetMfSecretsSchemeAnalytics(), nil
}

func (s *Service) getAaAccountDetails(ctx context.Context, actorId string) (*connectedAccountPb.GetAccountDetailsBulkResponse, error) {
	accountsResponse, err := s.beConnectedAccClient.GetAllAccounts(ctx, &connectedAccountPb.GetAllAccountsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: 1000,
		},
		ActorId: actorId,
		AccountFilterList: []connectedAccountExternalPb.AccountFilter{
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL,
		},
	})
	if rpcErr := epifigrpc.RPCError(accountsResponse, err); rpcErr != nil && !accountsResponse.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to get account details for actor : %w", rpcErr)
	}
	if len(accountsResponse.GetAccountDetailsList()) == 0 || accountsResponse.GetStatus().IsRecordNotFound() {
		return nil, nil
	}
	var accountIds []string
	for _, account := range accountsResponse.GetAccountDetailsList() {
		accountIds = append(accountIds, account.GetAccountId())
	}

	accountDetailsResponse, err := s.beConnectedAccClient.GetAccountDetailsBulk(ctx, &connectedAccountPb.GetAccountDetailsBulkRequest{
		AccountIdList: accountIds,
		AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{
			connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
			connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
		},
	})
	if rpcErr := epifigrpc.RPCError(accountDetailsResponse, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to fetch accDetails for aa transaction: %w", rpcErr)
	}
	return accountDetailsResponse, nil
}

func (s *Service) getMfExternalOrders(ctx context.Context, actorId string) ([]*mfExternalPb.MutualFundExternalOrder, error) {
	ordersResponse, err := s.mfExternalClient.FetchAllExternalMFOrders(ctx, &mfExternalPb.FetchAllExternalMFOrdersRequest{
		ActorId: actorId,
		PageContextRequest: &rpc.PageContextRequest{
			PageSize: mfOrderFetchPageSize,
		},
	})
	if rpcErr := epifigrpc.RPCError(ordersResponse, err); rpcErr != nil && !ordersResponse.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("error while fetching external mf orders for networth mcp: %w", rpcErr)
	}

	orders := make([]*mfExternalPb.MutualFundExternalOrder, 0)
	for _, extOrder := range ordersResponse.GetMutualFundExternalOrders() {
		if extOrder.GetExternalOrderType() == mfExternalPb.ExternalOrderType_BUY || extOrder.GetExternalOrderType() == mfExternalPb.ExternalOrderType_SELL {
			orders = append(orders, extOrder.MinimalRespForLLM())
		}
	}
	return orders, nil
}

func (s *Service) getNetworthDataObject(networthDataItems *networthDataComponents) *networthTools.NetWorthDataCollection {
	dataCollection := &networthTools.NetWorthDataCollection{
		NetworthDataDescription: s.config.NetworthMcp().FileDataDescription(),
		DataItems:               []*networthTools.NetWorthDataItem{},
	}
	orders := networthDataItems.mfExternalOrders

	mutualFundTransactionList := &networthTools.MfTransactionsResponse{
		MfTransactions:    networthTools.GroupMfTransactionsFromExtOrders(orders, mfMaxTxnsAllowed),
		SchemaDescription: fmt.Sprintf(s.config.NetworthMcp().MfTransactionResponseSchemaDesc(), mfMaxTxnsAllowed),
	}

	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(networthDataItems.netWorthResp.MinimalRespForLLM(), s.config.NetworthMcp().NetWorthFileDescription()))
	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(networthDataItems.mfSchemeAnalytics.MinimalRespForLLM(), s.config.NetworthMcp().MfHoldingsFileDescription()))
	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(networthDataItems.aaAccountDetails.MinimalRespForLLM(), s.config.NetworthMcp().AaFileDescription()))
	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(mutualFundTransactionList, s.config.NetworthMcp().MfTransactionsDescription()))
	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(networthDataItems.uanAccountsResp.MinimalRespForLLM(), s.config.NetworthMcp().EpfDescription()))
	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(networthDataItems.creditReportsResp.MinimalRespForLLM(), s.config.NetworthMcp().CreditReportDescription()))
	dataCollection.DataItems = append(dataCollection.GetDataItems(), networthTools.NewNetworthToolComponent(networthDataItems.manualAssetsDetailsRes.MinimalRespForLLM(), s.config.NetworthMcp().ManualAssetsFileDescription()))
	return dataCollection
}

func (s *Service) getBankTransactionsFileContent(ctx context.Context, actorId string) (*networthTools.NetWorthDataCollection, error) {
	// get bank transaction across all connected banks in paginated way
	rawBankTransactions, err := s.getRawBankTransactions(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting raw bank transactions")
	}
	networthDataCollection := s.getNetworthDataObjectForBankTransactions(&networthDataComponents{
		rawBankTransactions: rawBankTransactions,
	})
	return networthDataCollection, nil
}

func (s *Service) getRawBankTransactions(ctx context.Context, actorId string) ([]*networthTools.BankTransactions, error) {
	getAccountsResp, getAccountsErr := s.beConnectedAccClient.GetAllAccounts(ctx, &connectedAccountPb.GetAllAccountsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: 1000,
		},
		ActorId:               actorId,
		AccInstrumentTypeList: []enums.AccInstrumentType{enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT},
		AccountFilterList: []connectedAccountExternalPb.AccountFilter{
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_INCL_FI_FEDERAL,
		},
	})
	if rpcErr := epifigrpc.RPCError(getAccountsResp, getAccountsErr); rpcErr != nil && !getAccountsResp.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to get account details for actor : %w", rpcErr)
	}
	if len(getAccountsResp.GetAccountDetailsList()) == 0 || getAccountsResp.GetStatus().IsRecordNotFound() {
		return nil, nil
	}

	bankTransactionChan := make(chan *networthTools.BankTransactions, len(getAccountsResp.GetAccountDetailsList()))
	eg, gctx := errgroup.WithContext(ctx)

	for _, _acc := range getAccountsResp.GetAccountDetailsList() {
		acc := _acc
		if acc.GetAccInstrumentType() != enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT {
			logger.Info(ctx, "account instrument type in not deposit", zap.Any(logger.ACC_INSTRUMENT_TYPE, acc.GetAccInstrumentType()))
			continue
		}
		eg.Go(func() error {
			var err error
			rawTransactions, err := s.fetchBankTransactionForAccount(gctx, acc.GetAccountId())
			if err != nil {
				return fmt.Errorf("error in fetching bank transactions for account : %w", err)
			}
			bankTransactionChan <- &networthTools.BankTransactions{
				BankName:            acc.GetFipMeta().GetName(),
				RawBankTransactions: rawTransactions,
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return nil, fmt.Errorf("failed to get bank transactions after wait operation: %w", err)
	}
	close(bankTransactionChan)

	var bankTxns []*networthTools.BankTransactions
	for bankTransaction := range bankTransactionChan {
		bankTxns = append(bankTxns, bankTransaction)
	}
	return bankTxns, nil
}

func (s *Service) fetchBankTransactionForAccount(ctx context.Context, accountId string) ([]*connectedAccountPb.RawAaTransaction, error) {
	toTime := time.Now()
	fromTime := toTime.AddDate(0, -6, 0)

	rawTxnsRes, rawTxnErr := s.beConnectedAccClient.GetRawTxnsForAccountV2(ctx, &connectedAccountPb.GetRawTxnsForAccountV2Request{
		AccountId: accountId,
		FiType:    enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
		Filters: &connectedAccountPb.RawTransactionFilters{
			TransactionDateAfter:  timestampPb.New(fromTime),
			TransactionDateBefore: timestampPb.New(toTime),
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: rawAATxnFetchPageSize,
		},
	})
	if rpcErr := epifigrpc.RPCError(rawTxnsRes, rawTxnErr); rpcErr != nil && !rawTxnsRes.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("error while fetching raw bank transaction for mcp: %w", rpcErr)
	}

	rawTransactions := rawTxnsRes.GetRawTxnList()
	for rawTxnsRes.GetPageContext().GetHasAfter() {
		rawTxnsRes, rawTxnErr = s.beConnectedAccClient.GetRawTxnsForAccountV2(ctx, &connectedAccountPb.GetRawTxnsForAccountV2Request{
			AccountId: accountId,
			FiType:    enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			Filters: &connectedAccountPb.RawTransactionFilters{
				TransactionDateAfter:  timestampPb.New(fromTime),
				TransactionDateBefore: timestampPb.New(toTime),
			},
			PageContext: &rpc.PageContextRequest{
				Token: &rpc.PageContextRequest_AfterToken{
					AfterToken: rawTxnsRes.GetPageContext().GetAfterToken(),
				},
				PageSize: rawAATxnFetchPageSize,
			},
		})
		if rpcErr := epifigrpc.RPCError(rawTxnsRes, rawTxnErr); rpcErr != nil {
			return nil, fmt.Errorf("error while fetching raw bank transaction for mcp: %w", rpcErr)
		}
		rawTransactions = append(rawTransactions, rawTxnsRes.GetRawTxnList()...)
	}
	var minimalRawTransactions []*connectedAccountPb.RawAaTransaction
	for _, rawTx := range rawTransactions {
		minimalRawTransactions = append(minimalRawTransactions, rawTx.MinimalRespForLLM())
	}
	// sorting all the transactions by transaction date in descending order of transaction date
	sort.Slice(minimalRawTransactions, func(i, j int) bool {
		return minimalRawTransactions[i].GetAaTransaction().GetTransactionDate().AsTime().After(minimalRawTransactions[j].GetAaTransaction().GetTransactionDate().AsTime())
	})
	return minimalRawTransactions, nil
}

func (s *Service) getNetworthDataObjectForBankTransactions(networthDataItems *networthDataComponents) *networthTools.NetWorthDataCollection {
	bankTransactions := &networthTools.BankTransactionResponse{
		SchemaDescription: s.config.NetworthMcp().BankTransactionsResponseSchemaDesc(),
		BankTransactions:  networthTools.GetMinimalBankTransactionForLLM(networthDataItems.rawBankTransactions),
	}
	dataCollection := &networthTools.NetWorthDataCollection{
		NetworthDataDescription: s.config.NetworthMcp().FileDataDescription(),
		DataItems: []*networthTools.NetWorthDataItem{
			networthTools.NewNetworthToolComponent(bankTransactions, s.config.NetworthMcp().BankTransactionFileDescription()),
		},
	}
	return dataCollection
}
