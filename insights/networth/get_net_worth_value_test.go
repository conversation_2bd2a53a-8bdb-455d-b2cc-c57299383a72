package networth

import (
	"context"
	"fmt"
	"sort"
	"testing"

	mockHistory "github.com/epifi/gamma/insights/networth/history/mocks"

	mockPb "github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	mockTime "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/logger"

	mockCa "github.com/epifi/gamma/api/connected_account/mocks"
	mockCreditReport "github.com/epifi/gamma/api/creditreportv2/mocks"
	mockEpf "github.com/epifi/gamma/api/insights/epf/mocks"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/networth/mocks"
	mockMfExternal "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	mockNetworth "github.com/epifi/gamma/insights/networth/mocks"
)

type mockFields struct {
	assetAggregatorFactory     *mockNetworth.MockIValueAggregatorFactory[networthPb.AssetType, *networthPb.AssetValue]
	liabilityAggregatorFactory *mockNetworth.MockIValueAggregatorFactory[networthPb.LiabilityType, *networthPb.LiabilityValue]
	onbClient                  *mockOnb.MockOnboardingClient
	connectedAccountClient     *mockCa.MockConnectedAccountClient
	creditReportClient         *mockCreditReport.MockCreditReportManagerClient
	epfClient                  *mockEpf.MockEpfClient
	mfExternalOrdersClient     *mockMfExternal.MockMFExternalOrdersClient
	netWorthClient             *mocks.MockNetWorthClient
	mockTimeClient             *mockTime.MockTime
	networthHistory            *mockHistory.MockNetworth
}

func initMocks(ctl *gomock.Controller, ctrl *mockPb.Controller) *mockFields {
	return &mockFields{
		assetAggregatorFactory:     mockNetworth.NewMockIValueAggregatorFactory[networthPb.AssetType, *networthPb.AssetValue](ctl),
		liabilityAggregatorFactory: mockNetworth.NewMockIValueAggregatorFactory[networthPb.LiabilityType, *networthPb.LiabilityValue](ctl),
		onbClient:                  mockOnb.NewMockOnboardingClient(ctrl),
		connectedAccountClient:     mockCa.NewMockConnectedAccountClient(ctrl),
		creditReportClient:         mockCreditReport.NewMockCreditReportManagerClient(ctrl),
		epfClient:                  mockEpf.NewMockEpfClient(ctrl),
		mfExternalOrdersClient:     mockMfExternal.NewMockMFExternalOrdersClient(ctrl),
		netWorthClient:             mocks.NewMockNetWorthClient(ctrl),
		mockTimeClient:             mockTime.NewMockTime(ctrl),
		networthHistory:            mockHistory.NewMockNetworth(ctrl),
	}
}

func TestService_GetNetWorthValue(t *testing.T) {
	logger.Init(cfg.TestEnv)
	tests := []struct {
		name        string
		networthReq *networthPb.GetNetWorthValueRequest
		before      func(m *mockFields, ctl *gomock.Controller)
		want        *networthPb.GetNetWorthValueResponse
		wantErr     bool
	}{
		{
			name: "Successfully get net worth values",
			networthReq: &networthPb.GetNetWorthValueRequest{
				ActorId: "actorId",
				AssetTypes: []networthPb.AssetType{
					networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					networthPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
				},
				LiabilityTypes: []networthPb.LiabilityType{
					networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
				},
			},
			before: func(m *mockFields, ctl *gomock.Controller) {
				mockSavingsAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.AssetValue](ctl)
				mockSavingsAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.AssetValue{
					NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
					ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
				}, nil)
				mockMFAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.AssetValue](ctl)
				mockMFAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.AssetValue{
					NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
					ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND,
				}, nil)

				mockPersonalLoanAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.LiabilityValue](ctl)
				mockPersonalLoanAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.LiabilityValue{
					NetWorthAttribute: networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
					ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
					Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 500},
				}, nil)
				m.assetAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS).Return(mockSavingsAggregator, nil)
				m.assetAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.AssetType_ASSET_TYPE_MUTUAL_FUND).Return(mockMFAggregator, nil)
				m.liabilityAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN).Return(mockPersonalLoanAggregator, nil)
			},
			want: &networthPb.GetNetWorthValueResponse{
				Status: rpcPb.StatusOk(),
				AssetValues: []*networthPb.AssetValue{
					{
						NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
						Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
					},
					{
						NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND,
					},
				},
				LiabilityValues: []*networthPb.LiabilityValue{
					{
						NetWorthAttribute: networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
						Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 500},
					},
				},
			},
		},
		{
			name: "Multiple request for same asset or liability should be deduped",
			networthReq: &networthPb.GetNetWorthValueRequest{
				ActorId: "actorId",
				AssetTypes: []networthPb.AssetType{
					networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
				},
				LiabilityTypes: []networthPb.LiabilityType{
					networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
					networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
				},
			},
			before: func(m *mockFields, ctl *gomock.Controller) {
				mockSavingsAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.AssetValue](ctl)
				mockSavingsAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.AssetValue{
					NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
					ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
				}, nil)

				mockPersonalLoanAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.LiabilityValue](ctl)
				mockPersonalLoanAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.LiabilityValue{
					NetWorthAttribute: networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
					ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
					Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 500},
				}, nil)
				m.assetAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS).Return(mockSavingsAggregator, nil)
				m.liabilityAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN).Return(mockPersonalLoanAggregator, nil)
			},
			want: &networthPb.GetNetWorthValueResponse{
				Status: rpcPb.StatusOk(),
				AssetValues: []*networthPb.AssetValue{
					{
						NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
						Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
					},
				},
				LiabilityValues: []*networthPb.LiabilityValue{
					{
						NetWorthAttribute: networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
						Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 500},
					},
				},
			},
		},
		{
			name: "Error in getting asset values should return internal error",
			networthReq: &networthPb.GetNetWorthValueRequest{
				ActorId: "actorId",
				AssetTypes: []networthPb.AssetType{
					networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
				},
			},
			before: func(m *mockFields, ctl *gomock.Controller) {
				m.assetAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS).Return(nil, fmt.Errorf("some err"))
			},
			want: &networthPb.GetNetWorthValueResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "Error in getting liability values should return internal error",
			networthReq: &networthPb.GetNetWorthValueRequest{
				ActorId: "actorId",
				AssetTypes: []networthPb.AssetType{
					networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
				},
				LiabilityTypes: []networthPb.LiabilityType{
					networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
				},
			},
			before: func(m *mockFields, ctl *gomock.Controller) {
				mockSavingsAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.AssetValue](ctl)
				mockSavingsAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.AssetValue{
					NetWorthAttribute: networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
					ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
				}, nil)

				m.assetAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS).Return(mockSavingsAggregator, nil).MaxTimes(1)
				m.liabilityAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), networthPb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN).Return(nil, fmt.Errorf("some err"))
			},
			want: &networthPb.GetNetWorthValueResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "Empty asset and liabilities type lists should return response for all asset and liability types",
			networthReq: &networthPb.GetNetWorthValueRequest{
				ActorId: "actorId",
			},
			before: func(m *mockFields, ctl *gomock.Controller) {
				allAssetTypes := GetAllAssetTypes()
				allLiabilityTypes := GetAllLiabilityTypes()

				for _, assetType := range allAssetTypes {
					mockAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.AssetValue](ctl)
					mockAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.AssetValue{
						NetWorthAttribute: assetType,
						Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
					}, nil)
					m.assetAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), assetType).Return(mockAggregator, nil)
				}
				for _, liabilityType := range allLiabilityTypes {
					mockAggregator := mockNetworth.NewMockIValueAggregator[*networthPb.LiabilityValue](ctl)
					mockAggregator.EXPECT().GetAggregatedValue(gomock.Any(), "actorId").Return(&networthPb.LiabilityValue{
						NetWorthAttribute: liabilityType,
						Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
						ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
					}, nil)
					m.liabilityAggregatorFactory.EXPECT().GetValueAggregator(gomock.Any(), liabilityType).Return(mockAggregator, nil)
				}

			},
			want: &networthPb.GetNetWorthValueResponse{
				Status:          rpcPb.StatusOk(),
				AssetValues:     getValuesForAllAssetTypes(),
				LiabilityValues: getValuesForAllLiabilityTypes(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctl := gomock.NewController(t)
			ctrl := mockPb.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl, ctrl)
			tt.before(m, ctl)
			s := NewService(m.assetAggregatorFactory, m.liabilityAggregatorFactory, nil, nil, nil, m.onbClient, m.connectedAccountClient,
				m.mfExternalOrdersClient, m.netWorthClient, m.creditReportClient, m.epfClient, genConf, m.mockTimeClient, nil, nil, nil, nil,
				nil, nil, nil, nil, nil, nil, nil, nil)
			got, err := s.GetNetWorthValue(context.Background(), tt.networthReq)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNetWorthValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got.Status.DebugMessage = ""

			sort.Slice(tt.want.GetAssetValues(), func(i, j int) bool {
				return tt.want.GetAssetValues()[i].GetNetWorthAttribute() < tt.want.GetAssetValues()[j].GetNetWorthAttribute()
			})
			sort.Slice(got.GetAssetValues(), func(i, j int) bool {
				return got.GetAssetValues()[i].GetNetWorthAttribute() < got.GetAssetValues()[j].GetNetWorthAttribute()
			})
			sort.Slice(tt.want.GetLiabilityValues(), func(i, j int) bool {
				return tt.want.GetLiabilityValues()[i].GetNetWorthAttribute() < tt.want.GetLiabilityValues()[j].GetNetWorthAttribute()
			})
			sort.Slice(got.GetLiabilityValues(), func(i, j int) bool {
				return got.GetLiabilityValues()[i].GetNetWorthAttribute() < got.GetLiabilityValues()[j].GetNetWorthAttribute()
			})
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetNetWorthValue() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func getValuesForAllAssetTypes() []*networthPb.AssetValue {
	assetValues := make([]*networthPb.AssetValue, 0)
	allAssetTypes := GetAllAssetTypes()

	for _, assetType := range allAssetTypes {
		assetValue := &networthPb.AssetValue{
			NetWorthAttribute: assetType,
			Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
		}
		assetValues = append(assetValues, assetValue)
	}
	return assetValues
}

func getValuesForAllLiabilityTypes() []*networthPb.LiabilityValue {
	var liabilityValues []*networthPb.LiabilityValue
	allLiabilityTypes := GetAllLiabilityTypes()
	for _, liabilityType := range allLiabilityTypes {
		liabilityValue := &networthPb.LiabilityValue{
			NetWorthAttribute: liabilityType,
			Value:             &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
			ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
		}
		liabilityValues = append(liabilityValues, liabilityValue)
	}
	return liabilityValues
}
