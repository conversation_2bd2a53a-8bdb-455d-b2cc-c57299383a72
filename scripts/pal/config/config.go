package config

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	pkgLoansConfig "github.com/epifi/gamma/pkg/loans/config"
	"github.com/epifi/gamma/preapprovedloan/config/common"
)

const (
	// Experian
	// Adding #nosec to suppress G101: Potential hardcoded credential
	_ExperianPgpKeySet = "EpifiExperianPgpKeySet"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "pal")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToSecretFederal, err := cfg.LoadAllSecretsV2(conf.FederalDb, conf.FederalPgDb, conf.Secrets.Ids, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}

	dbOwnershipMap := conf.DbConfigMap.GetOwnershipToDbConfigMap()
	if err = updateDefaultDbConfigMap(conf, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to update secret values in the config: %w", err)
	}

	if err = updateDefaultConfig(conf, keyToSecretFederal); err != nil {
		return nil, fmt.Errorf("error in updateDefaultConfig: %w", err)
	}

	keyToSecretEpifi, err := cfg.LoadAllSecrets(conf.EpifiDb, nil, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}
	updateEpifiConfig(conf, keyToSecretEpifi)

	if confErr := updateDefaultPgdbConfig(conf, conf.FederalPgDb); confErr != nil {
		return nil, confErr
	}
	if confErr := updateDefaultPgdbConfig(conf, conf.FeatureEngineeringDb); confErr != nil {
		return nil, confErr
	}

	if confErr := updateDefaultPgdbConfig(conf, conf.EpifiPgdb); confErr != nil {
		return nil, confErr
	}

	err = updateDefaultPgDbConfigMap(conf, conf.PgDbConfigMap.GetOwnershipToDbConfigMap())
	if err != nil {
		return nil, err
	}

	return conf, nil
}

func updateDefaultPgdbConfig(c *Config, dbConf *cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(dbConf, dbServerEndpoint)
	readAndSetEnv(c)

	var keyToId map[string]string
	keyToId = cfg.AddPgdbSecretIds(dbConf, keyToId)
	keyToSecret, err := cfg.LoadSecrets(keyToId, c.Application.Environment, c.Aws.Region)
	if err != nil {
		return err
	}
	return cfg.UpdatePGDBSecretValuesV3(dbConf, keyToSecret)
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.FederalDb, dbServerEndpoint)
	cfg.UpdateSecretValues(c.FederalDb, c.Secrets, keyToSecret)
	if val, ok := c.Secrets.Ids[_ExperianPgpKeySet]; ok {
		if val != "" {
			var keySet PgpKeySet
			err := json.Unmarshal([]byte(val), &keySet)
			if err != nil {
				return errors.Wrap(err, "failed to unmarshal ExperianPgpKeySet secrets")
			}
			c.ExperianPgpKeySet = &keySet
			if c.ExperianPgpKeySet.PrivateKey != "" {
				dk, decodeErr := base64.StdEncoding.DecodeString(c.ExperianPgpKeySet.PrivateKey)
				if decodeErr != nil {
					return errors.Wrap(decodeErr, "failed to decode ExperianPgpKeySet PrivateKey")
				}
				c.ExperianPgpKeySet.PrivateKey = string(dk)
			} else {
				fmt.Println("empty value in the key for ExperianPgpKeySet.PrivateKey")
			}
			if c.ExperianPgpKeySet.PublicKey != "" {
				dk, decodeErr := base64.StdEncoding.DecodeString(c.ExperianPgpKeySet.PublicKey)
				if decodeErr != nil {
					return errors.Wrap(decodeErr, "failed to decode ExperianPgpKeySet PublicKey")
				}
				c.ExperianPgpKeySet.PublicKey = string(dk)
			} else {
				fmt.Println("empty value in the key for ExperianPgpKeySet.PublicKey")
			}
			if c.ExperianPgpKeySet.VendorPublicKey != "" {
				dk, decodeErr := base64.StdEncoding.DecodeString(c.ExperianPgpKeySet.VendorPublicKey)
				if decodeErr != nil {
					return errors.Wrap(decodeErr, "failed to decode ExperianPgpKeySet VendorPublicKey")
				}
				c.ExperianPgpKeySet.VendorPublicKey = string(dk)
			} else {
				fmt.Println("empty value in the key for ExperianPgpKeySet.VendorPublicKey")
			}
		} else {
			fmt.Println("empty value in the key for ExperianPgpKeySet")
		}
	}
	return nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultDbConfigMap(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	var err error
	if _, err = cfg.LoadAndUpdateSecretValues(dbOwnershipMap, nil, c.Application.Environment, c.Aws.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}

	return nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateEpifiConfig(c *Config, keyToSecret map[string]string) {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)
	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultPgDbConfigMap(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	var err error
	if _, err = cfg.LoadAndUpdateSecretValues(dbOwnershipMap, nil, c.Application.Environment, c.Aws.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}

	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.FederalDb.Host = val
		cfg.OverwriteDbHost(c.DbConfigMap.GetOwnershipToDbConfigMap(), val)
	}
}

type Config struct {
	Application                        *Application
	EpifiDb                            *cfg.DB
	FederalDb                          *cfg.DB
	FederalPgDb                        *cfg.DB
	Aws                                *Aws
	RudderStack                        *cfg.RudderStackBroker
	Secrets                            *cfg.Secrets
	Notification                       *common.Notification
	DbConfigMap                        cfg.DbConfigMap
	FeatureEngineeringDb               *cfg.DB
	ExperianPgpKeySet                  *PgpKeySet
	PgDbConfigMap                      cfg.DbConfigMap
	PgdbMigrationFlag                  bool
	DeeplinkConfig                     *common.DeeplinkConfig
	RawBucketScienapticBreDataFilePath string
	RawDataDevBucketName               string
	PalTemporalNamespace               string
	// category format <vendor>:<loan_program>
	CategoryToEmiComms                map[string]*pkgLoansConfig.EmiComms
	EpifiPgdb                         *cfg.DB
	CreditReportFlattenPublisher      *cfg.SqsPublisher
	ITRFileConf                       *ITRFileConf
	RedisStore                        *cfg.RedisOptions
	CredgenicsNACHPaymentsPostingConf *CredgenicsNACHPaymentsPostingConfig
	CreditReportPurgeConfig           *CreditReportPurgeConfig
	EpfoDataRecipients                []string
	DSAIdToEmailMap                   map[string][]string
}

type Application struct {
	Environment string
	Name        string
	Namespace   string
}

type Aws struct {
	Region string
	S3     *S3
}

type S3 struct {
	EligibleUsersBucketName   string
	PreapprovedloanBucketName string
}

type PgpKeySet struct {
	PublicKey       string `json:"public"`
	PrivateKey      string `json:"private"`
	PassPhrase      string `json:"pass_phrase"`
	VendorPublicKey string `json:"vendor_public_key"`
}

type ITRFileConf struct {
	ITRS3Bucket     string
	S3PrefixPath    string
	OutputS3Bucket  string
	OutputS3PfxPath string
}

type CredgenicsNACHPaymentsPostingConfig struct {
	PreApprovedLoanBucketName string
}

type CreditReportPurgeConfig struct {
	// minimum age of a fetched credit report after which it's eligible for deletion.
	SoftDeletionMinAge time.Duration
	// minimum time duration to wait before hard deleting a soft deleted credit report.
	HardDeletionMinDuration time.Duration

	// minimum age of a fetched cibil report after which it's eligible for deletion.
	CibilSoftDeletionMinAge time.Duration
	// minimum time duration to wait before hard deleting a soft deleted cibil report.
	CibilHardDeletionMinDuration time.Duration
}
