package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/gocarina/gocsv"
	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/order/recon"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	vendorMappingPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/scripts/Pay/recon_automated/config"
)

var (
	inputSource      = flag.String("inputSource", string(DailyReportJob), "Indicates the source of the input data")
	s3FilePath       = flag.String("s3FilePath", "", "S3 file path for CSV file")
	startTimeInput   = flag.String("startTime", time.Now().AddDate(0, 0, -45).Format("2006-01-02")+"T00:00:00", "start time for triggering reconciliation")
	maxReconAttempts = flag.Int("maxReconAttempts", 2, "Maximum number of reconciliation attempts for each account")
	csvFilepath      = flag.String("csvFilepath", "", "Local CSV file path for input data")

	failedAccountIds []string
	conf             *config.Config
)

// InputSource represents the source of the input data
type InputSource string

const (
	DailyReportJob InputSource = "DailyReportJob" // Daily scheduled job that reads from a dated folder in S3
	CustomS3Path   InputSource = "CustomS3Path"   // Custom S3 path provided through s3FilePath flag
	LocalCSVFile   InputSource = "LocalCSVFile"   // Local CSV file provided through csvFilepath flag
)

// AccountCSV represents the data entry in the input .csv file.
// Can contain ACCOUNT_ID or ACTOR_ID col(which actually contains firehose ID)
type AccountCSV struct {
	AccountId  string `csv:"ACCOUNT_ID,omitempty"`
	FirehoseId string `csv:"ACTOR_ID,omitempty"` // Column is named ACTOR_ID but contains firehose IDs
}

func getAccountIds(ctx context.Context, s3Client *s3pkg.Client, savingsClient savingsPb.SavingsClient, vmClient vendorMappingPb.VendorMappingServiceClient) ([]string, error) {

	var allAccountIds []string
	seenAccounts := make(map[string]bool)
	firehoseIds := []string{}                // For storing firehose IDs from the ACTOR_ID column
	processedActors := make(map[string]bool) // Track actors that already have an associated account ID

	var failedFirehoseIds []string      // Track firehose IDs that failed conversion
	var failedActorFirehoseIds []string // Track original firehose IDs for failed actor processing

	directAccountsFromCSV := 0      // Track direct account IDs from CSV
	firehoseToActorConversions := 0 // Track successful firehose to actor conversions
	actorToAccountLookups := 0      // Track successful actor to account lookups

	// Map to store actor ID to firehose ID mapping
	actorToFirehoseMap := make(map[string]string)

	firehoseConversionErrorMap := make(map[string][]string) // error message -> list of firehose IDs
	actorAccountLookupErrorMap := make(map[string][]string) // error message -> list of actor IDs

	// Step 1: Read the CSV file and separate account IDs from firehose IDs
	var csvContent []byte

	switch *inputSource {
	case string(DailyReportJob):
		// Get dated folder path
		dateToCheck := time.Now().Format("2006-01-02")
		s3Path := filepath.Join(conf.Aws.S3BasePath, dateToCheck)

		// Check if today's folder exists, if not use yesterday's
		objects, err := s3Client.ListObjects(ctx, &awsS3.ListObjectsInput{
			Bucket: &conf.Aws.S3BucketName,
			Prefix: &s3Path,
		})

		if err != nil || objects == nil || len(objects.Contents) == 0 {
			// Try yesterday's date
			dateToCheck = time.Now().AddDate(0, 0, -1).Format("2006-01-02")
			s3Path = filepath.Join(conf.Aws.S3BasePath, dateToCheck)
		}

		// Append part-00000 prefix to find the CSV file
		s3FilePrefix := filepath.Join(s3Path, "part-00000")
		logger.Info(context.Background(), "Checking for CSV file in S3",
			zap.String("bucket", conf.Aws.S3BucketName),
			zap.String("path", s3FilePrefix))

		// List objects with the part-00000 prefix
		objectList, err := s3Client.ListObjects(ctx, &awsS3.ListObjectsInput{
			Bucket: &conf.Aws.S3BucketName,
			Prefix: &s3FilePrefix,
		})

		if err != nil {
			logger.ErrorNoCtx("Failed to list objects in S3",
				zap.String("prefix", s3FilePrefix),
				zap.Error(err))
			return nil, fmt.Errorf("failed to list objects in S3: %w", err)
		}
		if objectList != nil && len(objectList.Contents) > 0 {
			// Use the first matching file
			s3FileKey := *objectList.Contents[0].Key
			csvContent, err = s3Client.Read(ctx, s3FileKey)
			if err != nil {
				return nil, fmt.Errorf("failed to read CSV from S3: %w", err)
			}

			logger.InfoNoCtx("Processing daily report CSV file",
				zap.String("path", s3FileKey))
		} else {
			return nil, fmt.Errorf("no CSV files found for date %s", dateToCheck)
		}

	case string(CustomS3Path):
		if *s3FilePath == "" {
			return nil, fmt.Errorf("s3FilePath is required for CustomS3Path input source")
		}
		// Read single CSV file
		var readErr error
		csvContent, readErr = s3Client.Read(ctx, *s3FilePath)
		if readErr != nil {
			return nil, fmt.Errorf("failed to read CSV from S3: %w", readErr)
		}

		logger.InfoNoCtx("Processing custom CSV file",
			zap.String("path", *s3FilePath))

	case string(LocalCSVFile):
		if *csvFilepath == "" {
			return nil, fmt.Errorf("csvFile path is required for LocalCSVFile input source")
		}

		// Read the local CSV file
		fileContent, readErr := os.ReadFile(*csvFilepath)
		if readErr != nil {
			return nil, fmt.Errorf("failed to read local CSV file: %w", readErr)
		}

		csvContent = fileContent
		logger.InfoNoCtx("Processing local CSV file",
			zap.String("path", *csvFilepath))

	default:
		return nil, fmt.Errorf("invalid input source: %s", *inputSource)
	}

	// Parse the CSV content
	var accounts []*AccountCSV
	if err := gocsv.UnmarshalBytes(csvContent, &accounts); err != nil {
		return nil, fmt.Errorf("failed to unmarshal CSV: %w", err)
	}

	// Process CSV rows
	for _, acc := range accounts {
		hasAccountId := false

		// Process direct savings account IDs first
		if acc.AccountId != "" {
			trimmedAccountId := strings.TrimSpace(acc.AccountId)
			if trimmedAccountId != "" && !seenAccounts[trimmedAccountId] {
				// For rows containing ACCOUNT_ID, use it directly for reconciliation
				allAccountIds = append(allAccountIds, trimmedAccountId)
				seenAccounts[trimmedAccountId] = true
				hasAccountId = true
				directAccountsFromCSV++
			}
		}

		// Process firehose IDs from the ACTOR_ID column
		if acc.FirehoseId != "" && !hasAccountId {
			sanitisedFirehoseId := strings.TrimSpace(acc.FirehoseId)
			if sanitisedFirehoseId != "" {
				// Add to firehose IDs for processing
				firehoseIds = append(firehoseIds, sanitisedFirehoseId)
			}
		}
	}
	logger.InfoNoCtx("CSV processing summary",
		zap.Int("total_rows", len(accounts)),
		zap.Int("directAccountIds", directAccountsFromCSV),
		zap.Int("firehoseIdsToProcess", len(firehoseIds)))

	// Convert all firehose IDs to actor IDs
	if len(firehoseIds) > 0 {
		var actorIds []string
		for _, firehoseId := range firehoseIds {
			// Make RPC call to get actor ID
			resp, err := vmClient.GetActorIdByVendorId(context.Background(), &vendorMappingPb.GetActorIdByVendorIdRequest{
				VendorId: &vendorMappingPb.GetActorIdByVendorIdRequest_FirehoseId{
					FirehoseId: firehoseId,
				},
			})

			if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
				errorMessage := rpcErr.Error()
				firehoseConversionErrorMap[errorMessage] = append(firehoseConversionErrorMap[errorMessage], firehoseId)
				failedFirehoseIds = append(failedFirehoseIds, firehoseId) // Add to failed list
				continue
			}

			actorId := resp.GetActorId()
			if actorId == "" {
				firehoseConversionErrorMap["empty_actor_id"] = append(firehoseConversionErrorMap["empty_actor_id"], firehoseId)
				failedFirehoseIds = append(failedFirehoseIds, firehoseId)
				continue
			}
			// Store the mapping
			actorToFirehoseMap[actorId] = firehoseId

			if !processedActors[actorId] {
				actorIds = append(actorIds, actorId)
				processedActors[actorId] = true
				firehoseToActorConversions++
			}
		}
		logger.InfoNoCtx("Firehose ID conversion summary",
			zap.Int("total_attempted", len(firehoseIds)),
			zap.Int("successful", firehoseToActorConversions),
			zap.Int("failed", len(failedFirehoseIds)))

		// Get savings account IDs for each actor ID
		if len(actorIds) > 0 {
			for _, actorId := range actorIds {
				originalFirehoseId := actorToFirehoseMap[actorId]
				// Set actor context
				ctx := epificontext.WithActorId(context.Background(), metadata.MD{"ACTOR_ID": []string{actorId}})

				// Use GetSavingsAccountEssentials RPC to directly get savings account details
				savingsResp, err := savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				})

				if rpcErr := epifigrpc.RPCError(savingsResp, err); rpcErr != nil {
					// Check if it's a record not found error
					if storagev2.IsRecordNotFoundError(rpcErr) {
						actorAccountLookupErrorMap["no_savings_account"] = append(actorAccountLookupErrorMap["no_savings_account"], originalFirehoseId)
					} else {
						// Handle other unexpected errors
						errorMessage := rpcErr.Error()
						actorAccountLookupErrorMap[errorMessage] = append(actorAccountLookupErrorMap[errorMessage], originalFirehoseId)
					}
					failedActorFirehoseIds = append(failedActorFirehoseIds, originalFirehoseId)
					continue // Skip to the next actor
				}

				// Check if we got a valid account
				if savingsResp.GetAccount() != nil && savingsResp.GetAccount().GetId() != "" {
					accountId := savingsResp.GetAccount().GetId()
					if !seenAccounts[accountId] {
						allAccountIds = append(allAccountIds, accountId)
						seenAccounts[accountId] = true
						actorToAccountLookups++
					}
				}
			}
			logger.InfoNoCtx("Savings account lookup summary",
				zap.Int("total_actors", len(actorIds)),
				zap.Int("accounts_found", actorToAccountLookups),
				zap.Int("failed_lookups", len(failedActorFirehoseIds)))
		}
	}
	for errorMessage, ids := range firehoseConversionErrorMap {
		logger.ErrorNoCtx("Firehose ID conversion errors",
			zap.String("error", errorMessage),
			zap.Int("count", len(ids)),
			zap.Strings("failed_firehose_ids", ids))
	}
	for errorMessage, ids := range actorAccountLookupErrorMap {
		logger.ErrorNoCtx("Actor to account lookup errors",
			zap.String("error", errorMessage),
			zap.Int("count", len(ids)),
			zap.Strings("failed_firehose_ids", ids))
	}

	totalFailedFirehoseIds := len(failedFirehoseIds) + len(failedActorFirehoseIds)
	if totalFailedFirehoseIds > 0 {
		logger.ErrorNoCtx("Total firehose ID processing failures",
			zap.Int("total_failures", totalFailedFirehoseIds))
	}
	logger.InfoNoCtx("Final list of savings account IDs for reconciliation",
		zap.Int("total_unique_accounts", len(allAccountIds)))
	return allAccountIds, nil
}

// triggerReconForAccountIds processes a batch of account IDs and returns a list of failed account IDs
func triggerReconForAccountIds(savingsClient savingsPb.SavingsClient, actorClient actorPb.ActorClient,
	reconClient recon.LedgerReconciliationClient, startTime *timestamppb.Timestamp, accountIds []string) (failureInProcessingIds []string) {

	successfulRecons := 0
	accountReconErrorMap := make(map[string][]string) // error message -> list of account IDs

	for i, accountId := range accountIds {
		if i%1000 == 0 {
			logger.InfoNoCtx("Processing progress", zap.Int("processed", i), zap.Int("total", len(accountIds)))
		}
		// Sleep before each account to prevent overwhelming the services
		time.Sleep(25 * time.Millisecond)

		// Get savings account details
		savingsAccountRes, err := savingsClient.GetAccount(epificontext.WithActorId(context.Background(),
			metadata.MD{"accountID": []string{accountId}}), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_Id{Id: accountId},
		})
		if err != nil {
			accountReconErrorMap["get_account_error"] = append(accountReconErrorMap["get_account_error"], accountId)
			failureInProcessingIds = append(failureInProcessingIds, accountId)
			continue
		}

		// Get actor details
		actorRes, err := actorClient.GetActorByEntityId(epificontext.WithActorId(context.Background(),
			metadata.MD{"accountID": []string{accountId}}), &actorPb.GetActorByEntityIdRequest{
			Type:     types.Actor_USER,
			EntityId: savingsAccountRes.GetAccount().GetPrimaryAccountHolder(),
		})
		if rpcErr := epifigrpc.RPCError(actorRes, err); rpcErr != nil {
			// Handle transport-level errors
			accountReconErrorMap["get_actor_error"] = append(accountReconErrorMap["get_actor_error"], accountId)
			failureInProcessingIds = append(failureInProcessingIds, savingsAccountRes.GetAccount().GetId())
			continue
		}

		// Set actor context
		ctx := epificontext.WithActorId(context.Background(), metadata.MD{"ACTOR_ID": []string{actorRes.GetActor().GetId()}})

		// Check reconciliation status
		reconData, err := reconClient.GetAccountLedgerReconciliationStatus(ctx, &recon.GetAccountLedgerReconciliationStatusRequest{
			SavingsAccountId: savingsAccountRes.GetAccount().GetId(),
		})

		if grpcErr := epifigrpc.RPCError(reconData, err); grpcErr != nil && !reconData.GetStatus().IsRecordNotFound() {
			accountReconErrorMap["recon_status_error"] = append(accountReconErrorMap["recon_status_error"], accountId)
			failureInProcessingIds = append(failureInProcessingIds, savingsAccountRes.GetAccount().GetId())
			continue
		}

		// If the account was reconciled after our start time, reset it
		if reconData.GetSavingsLedgerRecon() != nil && datetime.IsBefore(startTime, reconData.GetSavingsLedgerRecon().GetLastReconciledAt()) {
			res, setTimeErr := reconClient.SetStartTime(ctx, &recon.SetStartTimeRequest{
				SavingsAccountId: savingsAccountRes.GetAccount().GetId(),
				ReconStartTime:   startTime,
			})

			if grpcErr := epifigrpc.RPCError(res, setTimeErr); grpcErr != nil {
				logger.InfoNoCtx("Error in SetStartTime call", zap.Error(grpcErr))
				failureInProcessingIds = append(failureInProcessingIds, savingsAccountRes.GetAccount().GetId())
				continue
			}
		}

		// Get account opening date
		tm := savingsAccountRes.GetAccount().GetCreatedAt().AsTime().In(datetime.IST)

		// Call reconciliation with all required fields
		reconRes, err := reconClient.ReconcileAccountLedger(ctx, &recon.ReconcileAccountLedgerRequest{
			SavingsAccountId:     savingsAccountRes.GetAccount().GetId(),
			PartnerBank:          savingsAccountRes.GetAccount().GetPartnerBank(),
			AccountNo:            savingsAccountRes.GetAccount().GetAccountNo(),
			PrimaryAccountHolder: savingsAccountRes.GetAccount().GetPrimaryAccountHolder(),
			AccOpeningDate:       datetime.TimeToDateInLoc(tm, datetime.IST),
			ActorId:              actorRes.GetActor().GetId(),
		})

		if grpcErr := epifigrpc.RPCError(reconRes, err); grpcErr != nil {
			accountReconErrorMap["reconcile_error"] = append(accountReconErrorMap["reconcile_error"], accountId)
			failureInProcessingIds = append(failureInProcessingIds, savingsAccountRes.GetAccount().GetId())
			continue
		}
		successfulRecons++
	}
	logger.InfoNoCtx("Reconciliation processing summary",
		zap.Int("total_accounts", len(accountIds)),
		zap.Int("successful", successfulRecons),
		zap.Int("failed", len(failureInProcessingIds)))

	for errorMessage, accountIds := range accountReconErrorMap {
		logger.ErrorNoCtx("Failed reconciliations by error",
			zap.String("error", errorMessage),
			zap.Int("count", len(accountIds)),
			zap.Strings("failed_account_ids", accountIds))
	}

	return failureInProcessingIds
}

func main() {
	ctx := context.Background()
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)

	// Log startup information
	logger.InfoNoCtx("Starting recon_automated script",
		zap.String("environment", env),
		zap.String("inputSource", *inputSource),
		zap.String("s3FilePath", *s3FilePath))

	defer func() {
		_ = logger.Log.Sync()
		if len(failedAccountIds) > 0 {
			logger.InfoNoCtx(fmt.Sprintf("Reconciliation failed for account ids: %v", failedAccountIds))
		}
	}()

	// Log current directory and expected config paths
	execDir, err := os.Getwd()
	if err == nil {
		logger.InfoNoCtx("Current directory", zap.String("dir", execDir))
	}

	// Load configuration
	logger.InfoNoCtx("Attempting to load configuration")
	conf, err = config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}
	logger.InfoNoCtx("Configuration loaded successfully")

	// Initialize AWS session
	awsConfig, awsErr := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		panic(awsErr)
	}

	cxS3Client := s3pkg.NewClient(awsConfig, conf.Aws.S3BucketName)

	// Initialize recon client
	reconConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	reconClient := recon.NewLedgerReconciliationClient(reconConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	actorClient := actorPb.NewActorClient(actorConn)

	// Set up the vendor mapping client
	vmConn := epifigrpc.NewConnByService(cfg.VENDORMAPPING_SERVICE)
	vmClient := vendorMappingPb.NewVendorMappingServiceClient(vmConn)

	defer func() {
		epifigrpc.CloseConn(savingsConn)
		epifigrpc.CloseConn(actorConn)
		epifigrpc.CloseConn(reconConn)
		epifigrpc.CloseConn(vmConn)
	}()

	// Get account IDs from input source
	accountIds, err := getAccountIds(ctx, cxS3Client, savingsClient, vmClient)
	if err != nil {
		logger.Panic("failed to get account IDs", zap.Error(err))
	}

	if len(accountIds) == 0 {
		logger.InfoNoCtx("No account IDs found, exiting")
		return
	}

	logger.InfoNoCtx("Found account IDs", zap.Int("count", len(accountIds)))

	// Parse start time for reconciliation
	startTimeParsed, err := time.Parse("2006-01-02T15:04:05", *startTimeInput)
	if err != nil {
		logger.Panic("failed to parse start time", zap.Error(err))
	}
	startTimeProto := timestamppb.New(startTimeParsed)

	// Process accounts with retry logic matching trigger_recon_for_accountId script
	accountIdsToProcess := accountIds
	for attempt := 0; attempt < *maxReconAttempts; attempt++ {
		if len(accountIdsToProcess) == 0 {
			break
		}

		logger.InfoNoCtx(fmt.Sprintf("Attempt: %d, processing accountIds: %d",
			attempt+1, len(accountIdsToProcess)))

		// Process this batch of accounts
		failedIds := triggerReconForAccountIds(
			savingsClient,
			actorClient,
			reconClient,
			startTimeProto,
			accountIdsToProcess,
		)

		// Update the list of accounts to process in the next attempt
		accountIdsToProcess = failedIds

		// If we still have failures and this isn't the last attempt, wait before retrying
		if len(accountIdsToProcess) > 0 && attempt < *maxReconAttempts-1 {
			logger.InfoNoCtx(fmt.Sprintf("Will retry %d failed account IDs in next attempt",
				len(accountIdsToProcess)))
			time.Sleep(5 * time.Second)
		}
	}

	// Update the global failed accounts list
	failedAccountIds = accountIdsToProcess

	if len(failedAccountIds) > 0 {
		logger.InfoNoCtx("Reconciliation failed for some accounts after all attempts",
			zap.Int("count", len(failedAccountIds)))
	} else {
		logger.InfoNoCtx("Successfully reconciled all accounts")
	}
}
