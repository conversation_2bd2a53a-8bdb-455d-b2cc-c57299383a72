package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/gamma/api/risk/mnrl"
)

// ProcessMnrlReportData processes CSV records for MNRL report data and publishes to SQS
func ProcessMnrlReportData(ctx context.Context, records [][]string, publisher queue.Publisher, filenameLower string, batchSize int) (int, int) {
	var processed, errors int
	var failedMobileNumbers []string

	// Skip header row
	var batch []*mnrl.MnrlReport

	for i := 1; i < len(records); i++ {
		record := records[i]

		// Parse the record
		mnrlReport, err := parseMnrlReportRecord(record, filenameLower)
		if err != nil {
			logger.Error(ctx, "failed to parse MNRL report record", zap.Int("row", i), zap.Error(err))
			if len(record) > 0 {
				failedMobileNumbers = append(failedMobileNumbers, record[0])
			}
			errors++
			continue
		}

		// Add to batch
		batch = append(batch, mnrlReport)

		// Publish batch when it reaches batchSize or on last record
		if len(batch) >= batchSize || i == len(records)-1 {
			// Create the event with batch
			event := &mnrl.MnrlReportIngestEvent{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				MnrlReports: batch,
			}

			// Debug: Log the event details (only in debug mode)
			logger.Debug(ctx, "publishing MNRL report event",
				zap.Int("batch_end_row", i),
				zap.String("event_type", fmt.Sprintf("%T", event)),
				zap.Int("mnrl_reports_count", len(event.GetMnrlReports())),
			)

			// Publish to SQS
			_, err = publisher.Publish(ctx, event)
			if err != nil {
				logger.Error(ctx, "failed to publish MNRL report event", zap.Int("batch_end_row", i), zap.Error(err))
				// Extract mobile numbers for failed logging
				for _, report := range batch {
					if report.MobileNumber != "" {
						failedMobileNumbers = append(failedMobileNumbers, report.MobileNumber)
					}
				}
				errors += len(batch)
				continue
			}

			processed += len(batch)

			// Log progress every 1000 records
			if processed%1000 == 0 {
				logger.Info(ctx, "MNRL report processing progress", zap.Int("processed", processed), zap.Int("errors", errors))
			}

			// Reset batch for next iteration
			batch = nil
		}
	}

	// Print failed mobile numbers
	if len(failedMobileNumbers) > 0 {
		logger.Error(ctx, "number of Failed Mobile numbers", zap.Int("failures:", len(failedMobileNumbers)), zap.Int("total:", len(records)))
		logger.Error(ctx, "failed mobile numbers", zap.Strings("failed_mobiles", failedMobileNumbers))
	}

	// Final summary
	fmt.Printf("MNRL Report Processing Complete: %d processed, %d errors\n", processed, errors)

	return processed, errors
}

func parseMnrlReportRecord(record []string, filenameLower string) (*mnrl.MnrlReport, error) {
	// Handle records with fewer fields by providing defaults
	if len(record) < 1 {
		return nil, fmt.Errorf("record is empty")
	}

	// Safe array access with defaults
	mobileNumber := getField(record, 0, "")                        // Mobile_Number is at index 0
	lsaNameStr := getField(record, 1, "NOT_PRESENT")               // LSA Name is at index 1
	tspName := getField(record, 2, "TSP_not_present")              // TSP Name is at index 2
	dateOfDisconnectionStr := getField(record, 3, "")              // Date Of Disconnection is at index 3
	actionReasons := getField(record, 4, "Default action reasons") // Action Reasons is at index 4

	// Check if mobile number is valid (at least 10 digits)
	if len(mobileNumber) < 10 {
		return nil, fmt.Errorf("mobile number too short: %s (length: %d)", mobileNumber, len(mobileNumber))
	}

	// Extract last 10 digits from mobile number if longer than 10 digits
	if len(mobileNumber) > 10 {
		mobileNumber = mobileNumber[len(mobileNumber)-10:]
	}

	// Derive usecase from filename instead of CSV
	var usecase enums.MnrlUsecase
	switch {
	case strings.Contains(filenameLower, "dot_fake"):
		usecase = enums.MnrlUsecase_MNRL_USECASE_DOT_FAKE
	case strings.Contains(filenameLower, "lea"):
		usecase = enums.MnrlUsecase_MNRL_USECASE_LEA
	case strings.Contains(filenameLower, "non_compliant"):
		usecase = enums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT
	default:
		// Default fallback
		fmt.Println("Warning - UseCase not recognized for:" + filenameLower + "Proceeding with default - USECASE_UNSPECIFIED")
		usecase = enums.MnrlUsecase_MNRL_USECASE_UNSPECIFIED
	}

	// Parse LSA name
	normalizedLsaName := strings.ToLower(strings.ReplaceAll(lsaNameStr, " ", "_"))
	lsaName, exists := LsaNameMap[normalizedLsaName]
	if !exists {
		logger.ErrorNoCtx("LSA NAME NOT FOUND")
		lsaName = enums.LsaName_LSA_NAME_UNSPECIFIED
	}

	// Parse date of disconnection - no default value
	var dateOfDisconnection *date.Date
	if dateOfDisconnectionStr != "" {
		parsedDate, err := time.Parse("2006-01-02", dateOfDisconnectionStr)
		if err != nil {
			// Return error if date parsing fails - no default
			return nil, fmt.Errorf("failed to parse date of disconnection: %s", dateOfDisconnectionStr)
		}
		// Safe conversion with bounds checking to avoid integer overflow
		year := parsedDate.Year()
		month := parsedDate.Month()
		day := parsedDate.Day()

		// Since year, month, and day are always positive and within reasonable ranges,
		// we can safely convert them to int32
		if year > 2050 {
			return nil, fmt.Errorf("year %d is out of range for int32", year)
		}
		if int(month) > 12 {
			return nil, fmt.Errorf("month %d is out of range for int32", month)
		}
		if day > 31 {
			return nil, fmt.Errorf("day %d is out of range for int32", day)
		}

		dateOfDisconnection = &date.Date{
			Year:  int32(year),  //nolint:gosec
			Month: int32(month), //nolint:gosec
			Day:   int32(day),   //nolint:gosec
		}
	} else {
		return nil, fmt.Errorf("disconnection is empty")
	}

	now := timestamppb.Now()

	return &mnrl.MnrlReport{
		MobileNumber:        mobileNumber,
		LsaName:             lsaName,
		TspName:             tspName,
		DateOfDisconnection: dateOfDisconnection,
		ActionReasons:       actionReasons,
		Usecase:             usecase,
		CreatedAt:           now,
		UpdatedAt:           now,
	}, nil
}

// Helper function to safely get field from array with default
func getField(record []string, index int, defaultValue string) string {
	if index < len(record) {
		return record[index]
	}
	return defaultValue
}
