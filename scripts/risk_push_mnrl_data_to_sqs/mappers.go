package main

import (
	"github.com/epifi/gamma/api/risk/enums"
)

// LsaNameMap maps LSA name strings to enum values for MNRL Report files
var LsaNameMap = map[string]enums.LsaName{
	"andhra pradesh":       enums.LsaName_LSA_NAME_ANDHRA_PRADESH,
	"assam":                enums.LsaName_LSA_NAME_ASSAM,
	"bihar":                enums.LsaName_LSA_NAME_BIHAR,
	"delhi":                enums.LsaName_LSA_NAME_DELHI,
	"gujarat":              enums.LsaName_LSA_NAME_GUJARAT,
	"himachal pradesh":     enums.LsaName_LSA_NAME_HIMACHAL_PRADESH,
	"haryana":              enums.LsaName_LSA_NAME_HARYANA,
	"jammu kashmir":        enums.LsaName_LSA_NAME_JAMMU_KASHMIR,
	"karnataka":            enums.LsaName_LSA_NAME_KARNATAKA,
	"kerala":               enums.LsaName_LSA_NAME_KERALA,
	"kolkata":              enums.LsaName_LSA_NAME_KOLKATA,
	"madhya pradesh":       enums.LsaName_LSA_NAME_MADHYA_PRADESH,
	"maharashtra":          enums.LsaName_LSA_NAME_MAHARASHTRA,
	"mumbai":               enums.LsaName_LSA_NAME_MUMBAI,
	"north_east":           enums.LsaName_LSA_NAME_NORTH_EAST,
	"odisha":               enums.LsaName_LSA_NAME_ODISHA,
	"punjab":               enums.LsaName_LSA_NAME_PUNJAB,
	"rajasthan":            enums.LsaName_LSA_NAME_RAJASTHAN,
	"tamil_nadu":           enums.LsaName_LSA_NAME_TAMIL_NADU,
	"uttar_pradesh_east":   enums.LsaName_LSA_NAME_UTTAR_PRADESH_EAST,
	"Uttar Pradesh (East)": enums.LsaName_LSA_NAME_UTTAR_PRADESH_EAST,
	"uttar_pradesh_west":   enums.LsaName_LSA_NAME_UTTAR_PRADESH_WEST,
	"Uttar Pradesh (WEst)": enums.LsaName_LSA_NAME_UTTAR_PRADESH_WEST,
	"west bengal":          enums.LsaName_LSA_NAME_WEST_BENGAL,
}

// LsaCodeMap maps LSA code strings to enum values for Suspect Mobile Number files
// Supports both full names and short codes
var LsaCodeMap = map[string]enums.LsaName{

	// Short codes
	"ap":   enums.LsaName_LSA_NAME_ANDHRA_PRADESH,     // Andhra Pradesh
	"as":   enums.LsaName_LSA_NAME_ASSAM,              // Assam
	"br":   enums.LsaName_LSA_NAME_BIHAR,              // Bihar
	"dl":   enums.LsaName_LSA_NAME_DELHI,              // Delhi
	"gj":   enums.LsaName_LSA_NAME_GUJARAT,            // Gujarat
	"hr":   enums.LsaName_LSA_NAME_HARYANA,            // Haryana
	"hp":   enums.LsaName_LSA_NAME_HIMACHAL_PRADESH,   // Himachal Pradesh
	"jk":   enums.LsaName_LSA_NAME_JAMMU_KASHMIR,      // Jammu & Kashmir
	"ka":   enums.LsaName_LSA_NAME_KARNATAKA,          // Karnataka
	"kl":   enums.LsaName_LSA_NAME_KERALA,             // Kerala
	"wb":   enums.LsaName_LSA_NAME_WEST_BENGAL,        // West Bengal
	"mp":   enums.LsaName_LSA_NAME_MADHYA_PRADESH,     // Madhya Pradesh
	"mh":   enums.LsaName_LSA_NAME_MAHARASHTRA,        // Maharashtra
	"mum":  enums.LsaName_LSA_NAME_MUMBAI,             // Mumbai
	"ne":   enums.LsaName_LSA_NAME_NORTH_EAST,         // North East
	"od":   enums.LsaName_LSA_NAME_ODISHA,             // Odisha
	"pb":   enums.LsaName_LSA_NAME_PUNJAB,             // Punjab
	"rj":   enums.LsaName_LSA_NAME_RAJASTHAN,          // Rajasthan
	"tn":   enums.LsaName_LSA_NAME_TAMIL_NADU,         // Tamil Nadu
	"up_e": enums.LsaName_LSA_NAME_UTTAR_PRADESH_EAST, // Uttar Pradesh East
	"up_w": enums.LsaName_LSA_NAME_UTTAR_PRADESH_WEST, // Uttar Pradesh West

	// Additional 3-character codes from official list <https://www.trai.gov.in/sites/default/files/2024-11/Direction_26042024%20%281%29.pdf>
	"asm": enums.LsaName_LSA_NAME_ASSAM,              // Assam
	"del": enums.LsaName_LSA_NAME_DELHI,              // Delhi
	"guj": enums.LsaName_LSA_NAME_GUJARAT,            // Gujarat
	"har": enums.LsaName_LSA_NAME_HARYANA,            // Haryana
	"ktk": enums.LsaName_LSA_NAME_KARNATAKA,          // Karnataka
	"krl": enums.LsaName_LSA_NAME_KERALA,             // Kerala
	"kol": enums.LsaName_LSA_NAME_KOLKATA,            // Kolkatta
	"or":  enums.LsaName_LSA_NAME_ODISHA,             // Odisha
	"raj": enums.LsaName_LSA_NAME_RAJASTHAN,          // Rajasthan
	"upe": enums.LsaName_LSA_NAME_UTTAR_PRADESH_EAST, // Uttar Pradesh (East)
	"upw": enums.LsaName_LSA_NAME_UTTAR_PRADESH_WEST, // Uttar Pradesh (West)
}

// MnrlUsecaseMap maps usecase strings to enum values for both file types
var MnrlUsecaseMap = map[string]enums.MnrlUsecase{
	"dot_fake":                 enums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
	"lea":                      enums.MnrlUsecase_MNRL_USECASE_LEA,
	"noncompliant":             enums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
	"suspected_flagged_mobile": enums.MnrlUsecase_MNRL_USECASE_SUSPECTED_FLAGGED_MOBILE,
}

// SensitivityIndexMap maps sensitivity strings to enum values for Suspect Mobile Number files
var SensitivityIndexMap = map[string]enums.SensitivityIndex{
	// Simple mappings
	"medium":    enums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM,
	"high":      enums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
	"very_high": enums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH,

	"medium_severity":    enums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM,
	"high_severity":      enums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
	"very_high_severity": enums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH,
}
