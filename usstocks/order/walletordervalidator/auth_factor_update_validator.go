package walletordervalidator

import (
	"context"

	"github.com/pkg/errors"

	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/usstocks/order/uservalidator"
)

type AuthFactorUpdateValidator struct {
	afuChecker uservalidator.IValidator
}

func NewAuthFactorUpdateValidator(
	afuChecker *uservalidator.AfuChecker,
) *AuthFactorUpdateValidator {
	return &AuthFactorUpdateValidator{
		afuChecker: afuChecker,
	}
}

func (s *AuthFactorUpdateValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := s.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for auth factor update validator")
	}
	return s.afuChecker.Validate(ctx, order.GetActorId())
}

// validate necessary fields for validator
func (s *AuthFactorUpdateValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetActorId() == "" {
		return errors.New("actor id is empty")
	}
	if order.GetVendorAccountId() == "" {
		return errors.New("vendor account id is empty")
	}
	return nil
}
