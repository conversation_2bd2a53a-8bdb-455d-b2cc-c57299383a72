package walletordervalidator

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/pkg/usstocks/utils"
	errors2 "github.com/epifi/gamma/usstocks/internal/errors"
)

type PersonalLoanValidator struct {
	preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient
}

func NewPersonalLoanValidator(preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient) *PersonalLoanValidator {
	return &PersonalLoanValidator{preApprovedLoanClient: preApprovedLoanClient}
}

func (v *PersonalLoanValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := v.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for personal loan validator")
	}
	loanAccCheckState, err := utils.CheckActiveLoanAccountsForUser(ctx, v.preApprovedLoanClient, order.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting loan accounts for user", zap.Error(err))
		return err
	}

	// blocking add funds, if user is entering amount more than wallet balance and has a active loan account
	if loanAccCheckState == utils.ActiveLoanAccountPresent {
		return errors2.ErrUserPersonalLoanHolder
	}
	return nil
}

// validate necessary fields for validator
func (v *PersonalLoanValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetActorId() == "" {
		return errors.New("actor id is empty")
	}
	if order.GetVendorAccountId() == "" {
		return errors.New("vendor account id is empty")
	}
	return nil
}
