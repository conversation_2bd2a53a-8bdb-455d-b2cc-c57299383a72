package walletordervalidator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPb "github.com/epifi/be-common/pkg/money"

	internationalFundTransferPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	usstocks2 "github.com/epifi/gamma/pkg/usstocks"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
)

type ForexRateValidator struct {
	iftClient internationalFundTransferPb.InternationalFundTransferClient
}

func NewForexRateValidator(iftClient internationalFundTransferPb.InternationalFundTransferClient) *ForexRateValidator {
	return &ForexRateValidator{
		iftClient: iftClient,
	}
}

func (s *ForexRateValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := s.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for forex rate validator")
	}
	forexRateResp, err := s.iftClient.GetForexRate(ctx, &internationalFundTransferPb.GetForexRateRequest{
		Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
		RequiredAmount: order.GetAmountRequested(),
		CurrencyCode:   moneyPb.USDCurrencyCode,
		RemittanceType: internationalFundTransferPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
	})
	if err = epifigrpc.RPCError(forexRateResp, err); err != nil {
		if forexRateResp.GetStatus().IsRecordNotFound() {
			return usstocks2.ForexRateNotPresentErr
		}
		return errors.Wrap(err, "failed to get forex rate")
	}

	// Note: Currently, we check the deal passed by client against
	// the deal returned by forex service for the same order amount.
	// A smarter choice can be to use an outward forex deal
	// that has the same exchange rate as the one sent by the client and persist
	// that deal in order invoice and other places.
	if forexRateResp.GetForexRateId() != order.GetInvoiceDetails().GetForexRateId() {
		return ussErrors.ErrForexRateChanged
	}
	return nil
}

// validate necessary fields for validator
func (s *ForexRateValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetAmountRequested() == nil {
		return errors.New("amount requested is nil")
	}
	if order.GetInvoiceDetails() == nil {
		return errors.New("invoice details is nil")
	}
	if order.GetInvoiceDetails().GetForexRateId() == "" {
		return errors.New("forex rate id is empty")
	}
	return nil
}
