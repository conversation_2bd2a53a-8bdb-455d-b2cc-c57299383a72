package walletordervalidator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	ussVg "github.com/epifi/gamma/api/vendorgateway/stocks"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
)

type WithdrawableAmountValidator struct {
	usStocksVgClient ussVg.StocksClient
}

func NewWithdrawableAmountValidator(usStocksVgClient ussVg.StocksClient) *WithdrawableAmountValidator {
	return &WithdrawableAmountValidator{
		usStocksVgClient: usStocksVgClient,
	}
}

func (v *WithdrawableAmountValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := v.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for withdrawable amount validator")
	}
	if order.GetOrderType() != usstocks.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS {
		return nil
	}
	// TODO(Mihir): use trading account account manager api to get trading account information
	tradingAccRes, err := v.usStocksVgClient.GetTradingAccount(ctx, &ussVg.GetTradingAccountRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
		AccountId: order.GetVendorAccountId(),
	})
	if err = epifigrpc.RPCError(tradingAccRes, err); err != nil {
		return errors.Wrap(err, "unable to get trading account")
	}
	tradingAcc := tradingAccRes.GetTradingAccount()
	if money.Compare(tradingAcc.GetWithdrawableAmount(), order.GetAmountRequested()) == -1 {
		return ussErrors.ErrOrderAmountGreaterThanWithdrawableAmount
	}
	return nil
}

// validate necessary fields for validator
func (v *WithdrawableAmountValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetAmountRequested() == nil {
		return errors.New("amount requested is nil")
	}
	if order.GetVendorAccountId() == "" {
		return errors.New("vendor account id is empty")
	}
	return nil
}
