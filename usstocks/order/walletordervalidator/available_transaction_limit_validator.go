package walletordervalidator

import (
	"context"

	"github.com/pkg/errors"

	moneyPb "github.com/epifi/be-common/pkg/money"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/usstocks"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	errors2 "github.com/epifi/gamma/usstocks/internal/errors"
	orderDao "github.com/epifi/gamma/usstocks/order/dao"
	orderUtils "github.com/epifi/gamma/usstocks/order/utils"
)

type AvailableTransactionLimitValidator struct {
	remittanceReqDao orderDao.TransactionRemittanceRequestDao
	iftClient        iftPb.InternationalFundTransferClient
	walletOrderDao   orderDao.WalletOrderDao
}

func NewAvailableTransactionLimitValidator(
	remittanceReqDao orderDao.TransactionRemittanceRequestDao,
	iftClient iftPb.InternationalFundTransferClient,
	walletOrderDao orderDao.WalletOrderDao) *AvailableTransactionLimitValidator {
	return &AvailableTransactionLimitValidator{
		remittanceReqDao: remittanceReqDao,
		iftClient:        iftClient,
		walletOrderDao:   walletOrderDao,
	}
}

func (v *AvailableTransactionLimitValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := v.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for avaliable transaction limit validator")
	}
	if order.GetOrderType() != usstocks.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS {
		return nil
	}
	availableTxnLimit, err := orderUtils.GetAvailableTransactionLimitForInwardRemittanceInInr(ctx, order.GetActorId(),
		v.remittanceReqDao, v.iftClient, v.walletOrderDao)
	if err != nil {
		return errors.Wrap(err, "error in getting available transaction limit for inward remittance")
	}

	comp, err := moneyPb.CompareV2(order.GetAmountRequested(), availableTxnLimit)
	if err != nil {
		return errors.Wrap(err, "error in comparing available txn limit and requested amount")
	}

	// returning error if requested amount is greater than available limit for transaction
	if comp == 1 {
		return errors2.ErrOrderAmountGreaterThanAllowedRemittanceLimit
	}
	return nil
}

// validate necessary fields for validator
func (v *AvailableTransactionLimitValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetAmountRequested() == nil {
		return errors.New("amount requested is nil")
	}
	return nil
}
