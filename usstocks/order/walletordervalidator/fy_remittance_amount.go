package walletordervalidator

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"

	moneyPkg "github.com/epifi/be-common/pkg/money"

	accountPb "github.com/epifi/gamma/api/usstocks/account"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	iftErrPkg "github.com/epifi/gamma/pkg/internationalfundtransfer/errors"
	iftValidation "github.com/epifi/gamma/pkg/internationalfundtransfer/validation"
	"github.com/epifi/gamma/usstocks/account/dao"
	ussErr "github.com/epifi/gamma/usstocks/internal/errors"
)

type FYRemittanceAmountValidator struct {
	suitabilityLedgerDao        dao.SuitabilityLedgerDao
	fyRemittanceAmountValidator iftValidation.FYRemittanceAmountValidator
}

func NewFYRemittanceAmountValidator(
	suitabilityLedgerDao dao.SuitabilityLedgerDao,
	fyRemittanceAmountValidator iftValidation.FYRemittanceAmountValidator,
) *FYRemittanceAmountValidator {
	return &FYRemittanceAmountValidator{
		suitabilityLedgerDao:        suitabilityLedgerDao,
		fyRemittanceAmountValidator: fyRemittanceAmountValidator,
	}
}

func (s *FYRemittanceAmountValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := s.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for fy remittance amount validator")
	}
	maxFYRemittanceAmount, err := s.getMaxFYRemittanceAmount(ctx, order.GetActorId())
	if err != nil {
		return errors.Wrap(err, "error getting max amount to remit in FY")
	}
	err = s.fyRemittanceAmountValidator.Validate(ctx, &iftValidation.FYRemittanceAmountValidationRequest{
		ActorId:               order.GetActorId(),
		RemittanceAmount:      order.GetInvoiceDetails().GetAmountInInr(),
		MaxFYRemittanceAmount: maxFYRemittanceAmount,
	})
	if err != nil {
		if errors.Is(err, iftErrPkg.MaxFYRemittanceAmountBreachedErr) {
			return errors.Wrap(ussErr.ErrFYRemittanceLimitBreached, "FY remittance limit breached")
		}
		return errors.Wrap(err, "error in FY remittance amount validation")
	}
	return nil
}

func (s *FYRemittanceAmountValidator) getMaxFYRemittanceAmount(ctx context.Context, actorId string) (*money.Money, error) {
	suitabilityLedger, err := s.suitabilityLedgerDao.GetLatestByActorId(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting suitability ledger")
	}
	if suitabilityLedger.GetStatus() != accountPb.SuitabilityStatus_SUITABILITY_STATUS_SCORE_CALCULATED {
		return nil, fmt.Errorf("invalid state of suitability ledger")
	}
	if moneyPkg.IsZero(suitabilityLedger.GetScoreInfo().GetRemittanceLimit()) {
		return nil, fmt.Errorf("remittance limit is zero based on sutability checks")
	}
	return suitabilityLedger.GetScoreInfo().GetRemittanceLimit(), nil
}

// validate necessary fields for validator
func (s *FYRemittanceAmountValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetActorId() == "" {
		return errors.New("actor id is empty")
	}
	if order.GetInvoiceDetails() == nil {
		return errors.New("invoice details is nil")
	}
	if order.GetInvoiceDetails().GetAmountInInr() == nil {
		return errors.New("amount in INR is nil")
	}
	if order.GetVendorAccountId() == "" {
		return errors.New("vendor account id is empty")
	}
	return nil
}
