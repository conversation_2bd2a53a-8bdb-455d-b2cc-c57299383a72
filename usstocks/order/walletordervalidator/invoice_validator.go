package walletordervalidator

import (
	"context"

	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/usstocks"
	orderPb "github.com/epifi/gamma/api/usstocks/order"

	usStocksPkg "github.com/epifi/gamma/pkg/usstocks"
	"github.com/epifi/gamma/usstocks/config/genconf"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
)

type InvoiceValidator struct {
	conf *genconf.Config
}

func NewInvoiceValidator(conf *genconf.Config) *InvoiceValidator {
	return &InvoiceValidator{
		conf: conf,
	}
}

func (s *InvoiceValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	var err error
	err = s.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for invoice validator")
	}
	// checking amount requested currency code
	if !moneyPb.IsCurrencyCodeUSD(order.GetAmountRequested()) {
		return ussErrors.ErrInvalidCurrencyInInvoiceData
	}
	if order.GetOrderType() == usstocks.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS {
		// checking currency code for all amount values in invoice details
		if err = usStocksPkg.ValidateBEWalletAddFundsOrderInvoiceDetails(order.GetInvoiceDetails()); err != nil {
			return err
		}
	} else {
		// checking currency code for all amount values in invoice details
		if err = usStocksPkg.ValidateBEWalletWithdrawalOrderInvoiceDetails(order.GetInvoiceDetails()); err != nil {
			return err
		}
	}

	amountRequested := order.GetAmountRequested()
	if s.conf.AddFundsPreReqChecksData().Enabled() &&
		(moneyPb.Compare(amountRequested, s.conf.AddFundsPreReqChecksData().MinAllowedAmountForAnOrder()) < 0 ||
			moneyPb.Compare(amountRequested, s.conf.AddFundsPreReqChecksData().MaxAllowedAmountForAnOrder()) > 0) {
		return ussErrors.ErrAddFundsPreReqChecksFailed
	}
	return nil
}

// validate necessary fields for validator
func (s *InvoiceValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetAmountRequested() == nil {
		return errors.New("amount requested is nil")
	}
	if order.GetInvoiceDetails() == nil {
		return errors.New("invoice details is nil")
	}
	return nil
}
