// Code generated by MockGen. DO NOT EDIT.
// Source: validator.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	order "github.com/epifi/gamma/api/usstocks/order"
	gomock "github.com/golang/mock/gomock"
)

// MockIValidator is a mock of IValidator interface.
type MockIValidator struct {
	ctrl     *gomock.Controller
	recorder *MockIValidatorMockRecorder
}

// MockIValidatorMockRecorder is the mock recorder for MockIValidator.
type MockIValidatorMockRecorder struct {
	mock *MockIValidator
}

// NewMockIValidator creates a new mock instance.
func NewMockIValidator(ctrl *gomock.Controller) *MockIValidator {
	mock := &MockIValidator{ctrl: ctrl}
	mock.recorder = &MockIValidatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIValidator) EXPECT() *MockIValidatorMockRecorder {
	return m.recorder
}

// Validate mocks base method.
func (m *MockIValidator) Validate(ctx context.Context, order *order.WalletOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", ctx, order)
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockIValidatorMockRecorder) Validate(ctx, order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockIValidator)(nil).Validate), ctx, order)
}
