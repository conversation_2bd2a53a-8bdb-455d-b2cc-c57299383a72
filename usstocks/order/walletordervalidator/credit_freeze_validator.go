package walletordervalidator

import (
	"context"

	"github.com/pkg/errors"

	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/usstocks/order/uservalidator"
)

type CreditFreezeValidator struct {
	userCreditFreezeValidator uservalidator.IValidator
}

func NewCreditFreezeValidator(
	userCreditFreezeValidator *uservalidator.CreditFreezeValidator,
) *CreditFreezeValidator {
	return &CreditFreezeValidator{
		userCreditFreezeValidator: userCreditFreezeValidator,
	}
}

func (s *CreditFreezeValidator) Validate(ctx context.Context, order *orderPb.WalletOrder) error {
	err := s.validateRequest(order)
	if err != nil {
		return errors.Wrap(err, "invalid request for credit freeze validator")
	}
	return s.userCreditFreezeValidator.Validate(ctx, order.GetActorId())
}

// validate necessary fields for validator
func (s *CreditFreezeValidator) validateRequest(order *orderPb.WalletOrder) error {
	if order == nil {
		return errors.New("order is nil")
	}
	if order.GetActorId() == "" {
		return errors.New("actor id is empty")
	}
	return nil
}
