package uservalidator

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	riskProfilePb "github.com/epifi/gamma/api/risk/profile"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/epifigrpc"
	genConf "github.com/epifi/gamma/usstocks/config/genconf"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
	"github.com/epifi/gamma/usstocks/internal/savings"
)

type CreditFreezeValidator struct {
	conf              *genConf.Config
	savingsAccessor   savings.ISavingsAccessor
	riskProfileClient riskProfilePb.ProfileClient
}

func NewCreditFreezeValidator(
	conf *genConf.Config,
	savingsAccessor savings.ISavingsAccessor,
	riskProfileClient riskProfilePb.ProfileClient,
) *CreditFreezeValidator {
	return &CreditFreezeValidator{
		conf:              conf,
		savingsAccessor:   savingsAccessor,
		riskProfileClient: riskProfileClient,
	}
}

func (s *CreditFreezeValidator) Validate(ctx context.Context, actorId string) error {
	actorId = strings.TrimSpace(actorId)
	if lo.Contains(s.conf.CreditFrozenActorIds().ToStringArray(), actorId) {
		return ussErrors.ErrCreditFrozenForUser
	}
	savingsAcc, err := s.savingsAccessor.GetSavingsAccount(ctx, actorId)
	if err != nil {
		return errors.Wrap(err, "unable to get saving account")
	}
	if lo.Contains(savingsAcc.GetConstraints().GetRestrictions(), savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE) {
		return errors.Wrap(ussErrors.ErrCreditFrozenForUser, "credit to account is frozen as per savings account")
	}
	riskProfileRes, err := s.riskProfileClient.GetUserProfile(ctx, &riskProfilePb.GetUserProfileRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(riskProfileRes, err); err != nil {
		return errors.Wrap(err, "error getting risk user profile")
	}
	for _, acc := range riskProfileRes.GetAccountsInfo() {
		if acc.GetAccountId() != savingsAcc.GetId() {
			continue
		}
		if acc.GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
			acc.GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE ||
			// GST debits are also part of inward remittances, hence even a debit freeze would not allow user to receive money
			acc.GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE {
			return errors.Wrapf(ussErrors.ErrCreditFrozenForUser,
				"credit to account is frozen as per user risk profile, present freeze status: %s", acc.GetPresentFreezeStatus())
		}
		if s.conf.Flags().EnableImpendingSavingsAccountFreezeValidation() {
			if acc.GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
				acc.GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE ||
				acc.GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE {
				// GST debits are also part of inward remittances, hence even a debit freeze would not allow user to receive money
				return errors.Wrapf(ussErrors.ErrCreditFrozenForUser,
					"credit to account is frozen as per user risk profile, impending freeze status: %s", acc.GetImpendingFreezeStatus())
			}
		}
	}
	return nil
}
