package uservalidator

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/risk/enums"
	riskProfilePb "github.com/epifi/gamma/api/risk/profile"
	riskProfileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	ussErrors "github.com/epifi/gamma/usstocks/internal/errors"
	accessorMocks "github.com/epifi/gamma/usstocks/internal/mocks"
)

func TestCreditFreezeValidator_Validate(t *testing.T) {
	ctr := gomock.NewController(t)
	mockISavingsAccessor := accessorMocks.NewMockISavingsAccessor(ctr)
	mockRiskProfileClient := riskProfileMocks.NewMockProfileClient(ctr)
	sampleActorId := "sample-actor-id"
	sampleAccountId := "sample-account-id"
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name      string
		args      args
		setupMock func()
		wantErr   bool
		assertErr error
	}{
		{
			name: "ok if not frozen account",
			args: args{
				ctx:     context.Background(),
				actorId: sampleActorId,
			},
			setupMock: func() {
				mockISavingsAccessor.EXPECT().
					GetSavingsAccount(gomock.Any(), sampleActorId).
					Return(&savingsPb.Account{Id: sampleAccountId}, nil)
				mockRiskProfileClient.EXPECT().
					GetUserProfile(gomock.Any(), &riskProfilePb.GetUserProfileRequest{ActorId: sampleActorId}).
					Return(&riskProfilePb.GetUserProfileResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: false,
		},
		{
			name: "error if actor is one of credit-frozen actors present in env conf",
			args: args{
				ctx:     context.Background(),
				actorId: "sample-credit-frozen-actor-id",
			},
			setupMock: func() {},
			wantErr:   true,
			assertErr: ussErrors.ErrCreditFrozenForUser,
		},
		{
			name: "error if restriction present in savings account",
			args: args{
				ctx:     context.Background(),
				actorId: sampleActorId,
			},
			setupMock: func() {
				mockISavingsAccessor.EXPECT().GetSavingsAccount(gomock.Any(), sampleActorId).Return(&savingsPb.Account{
					Constraints: &savingsPb.AccountConstraints{
						Restrictions: []savingsPb.Restriction{
							savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE,
						},
					},
				}, nil)
			},
			wantErr:   true,
			assertErr: ussErrors.ErrCreditFrozenForUser,
		},
		{
			name: "error if impending freeze",
			args: args{
				ctx:     context.Background(),
				actorId: sampleActorId,
			},
			setupMock: func() {
				mockISavingsAccessor.EXPECT().
					GetSavingsAccount(gomock.Any(), sampleActorId).
					Return(&savingsPb.Account{Id: sampleAccountId}, nil)
				mockRiskProfileClient.EXPECT().
					GetUserProfile(gomock.Any(), &riskProfilePb.GetUserProfileRequest{ActorId: sampleActorId}).
					Return(&riskProfilePb.GetUserProfileResponse{
						Status: rpc.StatusOk(),
						AccountsInfo: []*riskProfilePb.AccountInfo{
							{
								AccountId:           sampleAccountId,
								PresentFreezeStatus: enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE,
							},
						},
					}, nil)
			},
			wantErr:   true,
			assertErr: ussErrors.ErrCreditFrozenForUser,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			s := &CreditFreezeValidator{
				conf:              conf,
				savingsAccessor:   mockISavingsAccessor,
				riskProfileClient: mockRiskProfileClient,
			}
			err := s.Validate(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !errors.Is(err, tt.assertErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.assertErr)
			}
		})
	}
}
