package walletorderprocessor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	usStocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	ussOrderMgPb "github.com/epifi/gamma/api/usstocks/order"
	workflow2 "github.com/epifi/gamma/api/usstocks/workflow"
	"github.com/epifi/gamma/usstocks/order/dao"
	"github.com/epifi/gamma/usstocks/order/walletordervalidator"
)

type WithdrawFundsOrderProcessor struct {
	validators      []walletordervalidator.IValidator
	walletOrderDao  dao.WalletOrderDao
	celestialClient celestialPb.CelestialClient
}

func NewWithdrawFundsOrderProcessor(
	creditFreezeValidator *walletordervalidator.CreditFreezeValidator,
	amountValidator *walletordervalidator.WithdrawableAmountValidator,
	availableTxnLimitValidator *walletordervalidator.AvailableTransactionLimitValidator,
	walletOrderDao dao.WalletOrderDao,
	celestialClient celestialPb.CelestialClient,
	afuValidator *walletordervalidator.AuthFactorUpdateValidator) *WithdrawFundsOrderProcessor {
	return &WithdrawFundsOrderProcessor{
		validators: []walletordervalidator.IValidator{
			afuValidator,
			creditFreezeValidator,
			amountValidator,
			availableTxnLimitValidator,
		},
		celestialClient: celestialClient,
		walletOrderDao:  walletOrderDao,
	}
}

func (v *WithdrawFundsOrderProcessor) ValidateOrder(ctx context.Context, order *ussOrderMgPb.WalletOrder) error {
	for _, validator := range v.validators {
		err := validator.Validate(ctx, order)
		if err != nil {
			return errors.Wrap(err, "error validating order")
		}
	}
	return nil
}

func (v *WithdrawFundsOrderProcessor) InitiateOrderProcessing(ctx context.Context, req *InitiateOrderProcessingRequest) (*InitiateOrderProcessingResponse, error) {
	wfPayload := &workflow2.WithdrawFundsFromWalletRequest{
		ActorId:          req.Order.GetActorId(),
		WithdrawalAmount: req.Order.GetAmountRequested(),
		WalletOrderId:    req.Order.GetId(),
	}
	wfPayloadBytes, err := protojson.Marshal(wfPayload)
	if err != nil {
		return nil, errors.Wrap(err, "error in marshalling add funds to wallet request")
	}

	// initiate workflow for the order
	resp, err := v.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: req.Order.GetActorId(),
			Version: workflow.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(usStocksNs.WithdrawFundsFromWallet),
			Payload: wfPayloadBytes,
			ClientReqId: &workflow.ClientReqId{
				Id:     req.Order.GetId(),
				Client: workflow.Client_US_STOCKS,
			},
			Ownership:        commontypes.Ownership_US_STOCKS_ALPACA,
			QualityOfService: celestialPb.QoS_GUARANTEED,
		},
	})
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		return nil, errors.Wrap(err2, "unable to initiate celestial workflow")
	}
	return &InitiateOrderProcessingResponse{}, nil
}
