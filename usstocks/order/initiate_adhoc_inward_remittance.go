package order

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	fileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/usstocks/order/dao"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_getter/aggregated_transactions_getter"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_updater"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator"
)

func (s *Service) InitiateAdHocInwardRemittance(ctx context.Context,
	req *order.InitiateAdHocInwardRemittanceRequest) (*order.InitiateAdHocInwardRemittanceResponse, error) {
	stuckTransactions, err := s.getFilteredTransactions(ctx,
		dao.WithRemittanceType([]usstocks.RemittanceType{usstocks.RemittanceType_REMITTANCE_TYPE_INWARD}),
		dao.WithAggregatedRemittanceTransactionActorIds(req.GetActorIds()),
		dao.WithAggregatedRemittanceTransactionState([]usstocks.AggregatedRemittanceTransactionState{
			usstocks.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE,
		}))
	if err != nil {
		logger.Error(ctx, "error getting stuck transactions", zap.Error(err))
		return &order.InitiateAdHocInwardRemittanceResponse{Status: rpc.StatusInternal()}, nil
	}
	if len(stuckTransactions) == 0 {
		logger.Info(ctx, "no stuck transactions to remit")
		return &order.InitiateAdHocInwardRemittanceResponse{Status: rpc.StatusRecordNotFound()}, nil
	}
	forexRateIdToTransactionsMap := lo.GroupBy(stuckTransactions, func(transaction *order.AggregatedRemittanceTransaction) string {
		return transaction.GetForexRateId()
	})
	for forexRateId, transactions := range forexRateIdToTransactionsMap {
		err = s.generateInwardRemittanceFilesForForexRate(ctx, forexRateId, transactions)
		if err != nil {
			logger.Error(ctx, "error generating inward remittance files", zap.Error(err), zap.String(logger.FOREX_RATE_ID, forexRateId))
			return &order.InitiateAdHocInwardRemittanceResponse{Status: rpc.StatusInternal()}, nil
		}
	}
	return &order.InitiateAdHocInwardRemittanceResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) generateInwardRemittanceFilesForForexRate(ctx context.Context,
	forexRateId string, transactions []*order.AggregatedRemittanceTransaction) error {
	if len(forexRateId) == 0 {
		return errors.New("empty forex rate id")
	}
	if len(transactions) == 0 {
		return errors.Errorf("no transactions found")
	}
	totalUsdTransactionAmount := money.ZeroUSD()
	for _, transaction := range transactions {
		var err error
		totalUsdTransactionAmount, err = money.Sum(totalUsdTransactionAmount, transaction.GetTotalAmount())
		if err != nil {
			return errors.Wrap(err, "error adding transaction amount")
		}
	}
	if money.IsZero(totalUsdTransactionAmount) {
		return errors.New("total transaction amount is zero")
	}
	if transactions[0].GetBatchId() == "" {
		return errors.Errorf("empty batch id for transaction id: %s", transactions[0].GetId())
	}
	fileRes, err := s.iftFileGenerationClient.GenerateInwardFundTransferFile(ctx, &fileGenPb.GenerateInwardFundTransferFileRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		// Having an idempotent client request ID for adhoc-generated remittance files requires
		// adding incremental numeric suffixes to the original remittance batch ID.
		// For now, we are relying on the file-entity mappings constraint
		// to ensure that one transaction can be part of only one file generation attempt at a time.
		ClientRequestId: transactions[0].GetBatchId() + "_" + uuid.NewString(),
		TotalAmount:     totalUsdTransactionAmount,
		OpsDate:         timestamp.Now(),
		BatchId:         transactions[0].GetBatchId(),
		FileType:        fileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
	})
	if err = epifigrpc.RPCError(fileRes, err); err != nil {
		return errors.Wrap(err, "error generating inward remittance file")
	}
	forexRate, err := s.getForexRateById(ctx, forexRateId)
	if err != nil {
		return errors.Wrap(err, "error getting forex rate")
	}
	// Note: State of forex rate is not checked for, as the rate has already been used to convert amount to INR.
	inwardTransactions := aggregated_transactions_getter.GetInwardTransactions(transactions, money.ToDecimal(forexRate.GetExchangeRate()))
	processTxnResp, err := s.inwardRemittanceTransactionProcessor.GenerateAndUploadFile(ctx, &inward_transactions_file_generator.GenerateAndUploadFileRequest{
		Transactions:      inwardTransactions,
		FileGenAttempt:    fileRes.GetFileGenerationAttempt(),
		RoundTransactions: false,
		ExchangeRate:      forexRate.GetExchangeRate(),
	})
	if err != nil {
		return errors.Wrap(err, "error while generating and uploading inward remittance files")
	}
	err = s.inwardRemittanceTransactionUpdater.BulkUpdateTransactions(ctx, &inward_transactions_bulk_updater.UpdateTransactionsRequest{
		EligibleTransactions:     processTxnResp.EligibleTransactions,
		CreditFreezeTransactions: processTxnResp.CreditFreezeTransactions,
		ForexRateId:              forexRateId,
	})
	if err != nil {
		return errors.Wrap(err, "error while updating transactions")
	}
	return nil
}

func (s *Service) getFilteredTransactions(ctx context.Context, filterOptions ...storage.FilterOption) ([]*order.AggregatedRemittanceTransaction, error) {
	var (
		transactions  = make([]*order.AggregatedRemittanceTransaction, 0)
		nextPageToken *pagination.PageToken
	)
	for {
		pageOfTransactions, nextPageCtx, err := s.aggrRemTxnDao.GetAggregatedRemittanceTransactions(ctx, nextPageToken, uint32(aggRemittanceTxnBatchSize), filterOptions...)
		if err != nil {
			return nil, errors.Wrap(err, "error getting a page of aggregated remittance transactions")
		}
		transactions = append(transactions, pageOfTransactions...)
		if !nextPageCtx.GetHasAfter() {
			return transactions, nil
		}
		nextPageToken, err = pagination.GetPageToken(&rpc.PageContextRequest{
			Token: &rpc.PageContextRequest_AfterToken{AfterToken: nextPageCtx.GetAfterToken()},
		})
		if err != nil {
			return nil, errors.Wrap(err, "error creating next page token")
		}
	}
}
