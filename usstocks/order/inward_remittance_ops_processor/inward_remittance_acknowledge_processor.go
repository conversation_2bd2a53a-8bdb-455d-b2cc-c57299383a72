package inward_remittance_ops_processor

import (
	"context"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/epifi/gamma/api/usstocks"

	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_getter"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_updater"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_file_generator"
)

// The federal agent inserts the exchange rate and generates the TTUM,
// which assists in starting the process of the amount transfer to the user account via the banking system.
type AcknowledgeInwardOpsProcessor struct {
	transactionsGetter    inward_transactions_bulk_getter.InwardTransactionsBulkGetter
	transactionsUpdater   inward_transactions_bulk_updater.InwardTransactionsBulkUpdater
	transactionsProcessor inward_transactions_file_generator.InwardTransactionsProcessor
}

var AcknowledgeInwardOpsProcessorWireSet = wire.NewSet(NewAcknowledgeInwardOpsProcessor, wire.Bind(new(InwardRemittanceOpsProcessor), new(*AcknowledgeInwardOpsProcessor)))

func NewAcknowledgeInwardOpsProcessor(transactionsGetter inward_transactions_bulk_getter.InwardTransactionsBulkGetter, transactionsUpdater inward_transactions_bulk_updater.InwardTransactionsBulkUpdater, transactionsProcessor inward_transactions_file_generator.InwardTransactionsProcessor) *AcknowledgeInwardOpsProcessor {
	return &AcknowledgeInwardOpsProcessor{
		transactionsGetter:    transactionsGetter,
		transactionsUpdater:   transactionsUpdater,
		transactionsProcessor: transactionsProcessor,
	}
}

func (s *AcknowledgeInwardOpsProcessor) PerformInwardRemittanceProcess(ctx context.Context, req *PerformInwardRemittanceProcessRequest) error {
	transactions, err := s.transactionsGetter.BulkGetTransactions(ctx, &inward_transactions_bulk_getter.InwardTransactionBulkGetterRequest{
		BatchId:   req.FileGenAttempt.GetClientRequestId(),
		ForexRate: req.ForexRate.GetExchangeRate(),
	})
	if err != nil {
		return errors.Wrap(err, "error while getting transactions")
	}

	var newTransactions []*inward_transaction_utils.InwardTransaction
	var txnsToExclude []*inward_transaction_utils.InwardTransaction
	for _, transaction := range transactions {
		if transaction.Status != usstocks.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE {
			if excludeActor := lo.Contains(req.ExcludeActorIds, transaction.ActorId); excludeActor {
				txnsToExclude = append(txnsToExclude, transaction)
			} else {
				newTransactions = append(newTransactions, transaction)
			}
		}
	}
	if len(txnsToExclude) > 0 {
		err = s.transactionsUpdater.BulkUpdateTransactions(ctx, &inward_transactions_bulk_updater.UpdateTransactionsRequest{
			EligibleTransactions:     nil,
			CreditFreezeTransactions: txnsToExclude,
		})
		if err != nil {
			return errors.Wrap(err, "error while updating transactions")
		}
	}

	processTxnResp, err := s.transactionsProcessor.GenerateAndUploadFile(ctx, &inward_transactions_file_generator.GenerateAndUploadFileRequest{
		Transactions:      newTransactions,
		FileGenAttempt:    req.FileGenAttempt,
		RoundTransactions: true,
		ExchangeRate:      req.ForexRate.GetExchangeRate(),
	})
	if err != nil {
		return errors.Wrap(err, "error while processing transactions")
	}
	err = s.transactionsUpdater.BulkUpdateTransactions(ctx, &inward_transactions_bulk_updater.UpdateTransactionsRequest{
		EligibleTransactions:     processTxnResp.EligibleTransactions,
		CreditFreezeTransactions: processTxnResp.CreditFreezeTransactions,
		ForexRateId:              req.ForexRate.GetId(),
	})
	if err != nil {
		return errors.Wrap(err, "error while updating transactions")
	}
	return nil
}
