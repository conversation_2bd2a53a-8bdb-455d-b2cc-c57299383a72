package aggregated_transactions_updater

import (
	"context"

	"github.com/google/wire"
	"github.com/pkg/errors"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/be-common/pkg/errgroup"
	orderDao "github.com/epifi/gamma/usstocks/order/dao"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_processor"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_updater"
)

type AggregatedTransactionsUpdater struct {
	aggTransactionDao    orderDao.AggregatedRemittanceTransactionDao
	transactionProcessor inward_transaction_processor.TransactionProcessor
}

var AggregatedTransactionsUpdaterWireSet = wire.NewSet(NewAggregatedTransactionsUpdater, wire.Bind(new(inward_transactions_bulk_updater.InwardTransactionsBulkUpdater), new(*AggregatedTransactionsUpdater)))

func NewAggregatedTransactionsUpdater(aggTransactionDao orderDao.AggregatedRemittanceTransactionDao, transactionProcessor inward_transaction_processor.TransactionProcessor) *AggregatedTransactionsUpdater {
	return &AggregatedTransactionsUpdater{
		aggTransactionDao:    aggTransactionDao,
		transactionProcessor: transactionProcessor,
	}
}

func (o *AggregatedTransactionsUpdater) BulkUpdateTransactions(ctx context.Context, req *inward_transactions_bulk_updater.UpdateTransactionsRequest) error {
	grp, gCtx := errgroup.WithContext(ctx)
	for _, _cft := range req.CreditFreezeTransactions {
		transaction := _cft
		grp.Go(func() error {
			err := o.updateTransaction(
				gCtx,
				transaction,
				usstocksPb.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE,
				req.ForexRateId,
			)
			if err != nil {
				return errors.Wrapf(err, "error marking transaction state credit-freeze: %s", transaction.Id)
			}
			return nil
		})
	}
	err := grp.Wait()
	if err != nil {
		return errors.Wrap(err, "error in one of the go routines")
	}
	grp, gCtx = errgroup.WithContext(ctx)
	for _, _cft := range req.EligibleTransactions {
		transaction := _cft
		grp.Go(func() error {
			err = o.updateTransaction(
				gCtx,
				transaction,
				usstocksPb.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_ACKNOWLEDGE,
				req.ForexRateId,
			)
			if err != nil {
				return errors.Wrapf(err, "error marking transaction state payment-acked: %s", transaction.Id)
			}
			return nil
		})
	}
	err = grp.Wait()
	if err != nil {
		return errors.Wrap(err, "error in one of the go routines")
	}
	return nil
}

func (o *AggregatedTransactionsUpdater) updateTransaction(
	ctx context.Context,
	transaction *inward_transaction_utils.InwardTransaction,
	state usstocksPb.AggregatedRemittanceTransactionState,
	forexRateId string,
) error {
	err := o.aggTransactionDao.Update(ctx,
		&orderPb.AggregatedRemittanceTransaction{
			Id:               transaction.Id,
			GstCharged:       transaction.Gst,
			TransactionState: state,
			ForexRateId:      forexRateId,
		},
		[]orderPb.AggregatedRemittanceTransactionFieldMask{
			orderPb.AggregatedRemittanceTransactionFieldMask_AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_GST_CHARGED,
			orderPb.AggregatedRemittanceTransactionFieldMask_AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_STATE,
			orderPb.AggregatedRemittanceTransactionFieldMask_AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_FOREX_RATE,
		},
	)
	if err != nil {
		return errors.Wrapf(err, "error updating aggregate transaction: %s", transaction.Id)
	}

	// TODO(Brijesh): Remove all unused code related to inward_transaction_processor.UpdateTransactionAcknowledge
	return nil
}
