package inward_transactions_bulk_updater

import (
	"context"

	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
)

//go:generate mockgen -source=inward_transactions_bulk_updater.go -destination=./mocks/mock.go -package=mocks
type InwardTransactionsBulkUpdater interface {
	// BulkUpdateTransactions updates aggregated remittance transactions which where part of a remittance batch
	// It also updates the individual remittance transactions mapped to the aggregated entity if needed
	BulkUpdateTransactions(ctx context.Context, request *UpdateTransactionsRequest) error
}

type UpdateTransactionsRequest struct {
	EligibleTransactions []*inward_transaction_utils.InwardTransaction
	// credit freeze transactions are present in this list
	CreditFreezeTransactions []*inward_transaction_utils.InwardTransaction
	// forex rate ID applicable for current inward swift transfer
	ForexRateId string
}
