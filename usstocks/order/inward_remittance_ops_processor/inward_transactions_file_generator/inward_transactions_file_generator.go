package inward_transactions_file_generator

import (
	"context"

	moneypb "google.golang.org/genproto/googleapis/type/money"

	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
)

//go:generate mockgen -source=inward_transactions_file_generator.go -destination=./mocks/mock.go -package=mocks
type InwardTransactionsProcessor interface {
	// GenerateAndUploadFile takes in a list of aggregated remittance transactions and generates an inward remittance
	// file for them.
	// It also generates a GST reporting file for these transactions.
	// These files are stored in S3 and the corresponding file paths are recorded in the file generation attempt
	// It reports back the list of transactions that were skipped due to credit being frozen to those user's accounts
	// and the list of remaining transactions which were used to generate the files.
	// Note: Skipping transactions whose data cannot be added to file contents is not supported since it is going to be
	// hard to re-trigger remittance process them as of now.
	GenerateAndUploadFile(context.Context, *GenerateAndUploadFileRequest) (*GenerateAndUploadFileResponse, error)
}

// GenerateAndUploadFileRequest represents all the transactions and file generation attempt IDs
// for which an inward remittance process is being performed
type GenerateAndUploadFileRequest struct {
	// Inward transactions to calculate GST and generate and upload inward remittance files
	Transactions []*inward_transaction_utils.InwardTransaction

	// An empty file generation attempt for inward TTUM file
	FileGenAttempt *iftFileGenPb.FileGenerationAttempt

	// Adjust one of the constituent transactions
	// to round the total debit from pool account to the nearest integer
	RoundTransactions bool

	// Add comment
	ExchangeRate *moneypb.Money
}

// GenerateAndUploadFileResponse represents the list of credit-freeze and eligible transactions
type GenerateAndUploadFileResponse struct {
	EligibleTransactions     []*inward_transaction_utils.InwardTransaction
	CreditFreezeTransactions []*inward_transaction_utils.InwardTransaction
}
