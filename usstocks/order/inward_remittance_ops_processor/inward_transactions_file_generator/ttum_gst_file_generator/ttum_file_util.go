package ttum_gst_file_generator

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/utils"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
)

type UserDataForInwardRemittanceFile struct {
	// gst reporting data for an inward txn
	gstReportingDataObj *iftPb.GetGstReportingDataForInwardTxnResponse
	// bank account number of user
	accountNumber string
	// sol ID of user's bank account
	solId string
	// represents if the account is credit frozen or not
	isCreditFrozen bool
}

type TTUMFileGenerationRequest struct {
	eligibleTransaction      []*inward_transaction_utils.InwardTransaction
	userReqDataForFileGenMap map[string]*UserDataForInwardRemittanceFile
	totalPoolAccountDebit    *moneyPb.Money
	fileGenAttempt           *iftFileGenPb.FileGenerationAttempt
	exchangeRate             *moneyPb.Money
	gstFileGenAttempt        *iftFileGenPb.FileGenerationAttempt
}

type InwardFundTransferFileModel struct {
	SrNo string

	// account number to which fund is to be transferred
	AccountNumber string

	// SOL ID of bank account
	// It is a four-digit numerical (the last four digits in IFSC code of the bank branch)
	SolId string

	// transaction type, 'C' signifies credit and 'D' signifies Debit
	CreditOrDebit string

	// the amount to be transferred
	Amount string

	// any additional remarks for the transaction
	// e.g., orderId, etc.
	Remarks string
}

func (o *GenerateFileForTransaction) generateTTUMAndUpload(ctx context.Context, req *TTUMFileGenerationRequest) error {
	content, totalEntries, err := o.generateTTUMContent(req)
	if err != nil {
		return errors.Wrap(err, "error while getting ttum content")
	}
	err = o.uploadTTUMFile(ctx, content, req, totalEntries)
	return err
}

func (o *GenerateFileForTransaction) uploadTTUMFile(ctx context.Context, content string, req *TTUMFileGenerationRequest, totalEntries int32) error {
	var entityIds []string
	for _, txn := range req.eligibleTransaction {
		entityIds = append(entityIds, "AGGREMTXN_"+txn.ExternalId)
	}
	uploadResp, err := o.iftFileGenClient.UploadFile(ctx, &iftFileGenPb.UploadFileRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		FileGenerationAttempt: &iftFileGenPb.FileGenerationAttempt{
			Id:       req.fileGenAttempt.GetId(),
			FileType: iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
		},
		FileContents: []byte(content),
		TotalEntries: totalEntries,
		EntityIds:    entityIds,
	})
	if err2 := epifigrpc.RPCError(uploadResp, err); err2 != nil {
		return errors.Wrap(err2, "error while uploading file")
	}
	return nil
}

func GenerateUserCreditTxnRemark(txnExternalId string) string {
	return fmt.Sprintf("USS/AGG/%s", txnExternalId)
}

// Includes forex rate and amount in USD for CBWT reporting by partner bank
func generateUserCreditTxnRemarkForCBWTReporting(exchangeRate *moneyPb.Money, transaction *inward_transaction_utils.InwardTransaction) (string, error) {
	remarkDescription, err := getUserTxnRemarkDescription(exchangeRate, transaction)
	if err != nil {
		return "", errors.Wrap(err, "error getting user txn remark description")
	}
	return fmt.Sprintf("%s_%s_USS/AGG/%s", remarkDescription.ExchangeRate, remarkDescription.UsdTxnAmount, transaction.ExternalId), nil
}

func generateUserGstDebitTxnRemarkForCBWTReporting(exchangeRate *moneyPb.Money, transaction *inward_transaction_utils.InwardTransaction) (string, error) {
	remarkDescription, err := getUserTxnRemarkDescription(exchangeRate, transaction)
	if err != nil {
		return "", errors.Wrap(err, "error getting user txn remark description")
	}
	return fmt.Sprintf("%s_%s_USS/GST/AGG/%s", remarkDescription.ExchangeRate, remarkDescription.UsdTxnAmount, transaction.ExternalId), nil
}

type UserTransactionRemarkDescription struct {
	// Add comments (on truncation)
	ExchangeRate  string
	UsdTxnAmount  string
	TxnExternalId string
}

func getUserTxnRemarkDescription(exchangeRate *moneyPb.Money, transaction *inward_transaction_utils.InwardTransaction) (*UserTransactionRemarkDescription, error) {
	exchangeRateForRemarks, err := money.ToString(exchangeRate, inward_transaction_utils.MaxINRAmtPrecision)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting exchange rate for remarks: %s", exchangeRate)
	}
	usdTxnAmtForRemarks, err := money.ToString(transaction.AmountInUSD, inward_transaction_utils.MaxUSDAmtPrecision)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting USD transaction amount for remarks: %s", transaction.AmountInUSD.String())
	}
	return &UserTransactionRemarkDescription{
		ExchangeRate:  exchangeRateForRemarks,
		UsdTxnAmount:  usdTxnAmtForRemarks,
		TxnExternalId: transaction.ExternalId,
	}, nil
}

func (o *GenerateFileForTransaction) generateTTUMContent(req *TTUMFileGenerationRequest) (string, int32, error) {
	ttumEntries := make([]*InwardFundTransferFileModel, 0)
	srNo := 1
	for _, transaction := range req.eligibleTransaction {
		// user inward_transaction_processor credit
		amountStr, err := money.ToString(transaction.AmountInINR, inward_transaction_utils.MaxINRAmtPrecision)
		if err != nil {
			return "", 0, errors.Wrap(err, "error while forming string")
		}
		userReqDataForFileGen := req.userReqDataForFileGenMap[transaction.ExternalId]

		userCreditTxnRemark := GenerateUserCreditTxnRemark(transaction.ExternalId)
		userGstDebitTxnRemark := fmt.Sprintf("USS/GST/AGG/%s", transaction.ExternalId)

		if o.genConf.Flags().EnableModifiedTransactionRemarkForInwardRemittance() {
			userCreditTxnRemark, err = generateUserCreditTxnRemarkForCBWTReporting(req.exchangeRate, transaction)
			if err != nil {
				return "", 0, errors.Wrap(err, "error generating user credit transaction remark for CBWTR")
			}
			userGstDebitTxnRemark, err = generateUserGstDebitTxnRemarkForCBWTReporting(req.exchangeRate, transaction)
			if err != nil {
				return "", 0, errors.Wrap(err, "error generating user GST debit transaction remark for CBWTR")
			}
		}

		ttumEntries = append(ttumEntries, &InwardFundTransferFileModel{
			SrNo:          strconv.Itoa(srNo),
			AccountNumber: userReqDataForFileGen.accountNumber,
			SolId:         userReqDataForFileGen.solId,
			CreditOrDebit: "C",
			Amount:        amountStr,
			Remarks:       userCreditTxnRemark,
		})
		srNo++

		// add gst credit entries to respective gst account
		gstTTUMEntries, err := o.getGstTTUMEntries(userReqDataForFileGen.gstReportingDataObj.GetReportingData(), userReqDataForFileGen.accountNumber, srNo)
		if err != nil {
			return "", 0, errors.Wrap(err, "error while gst ttum entires")
		}
		ttumEntries = append(ttumEntries, gstTTUMEntries...)
		srNo += len(gstTTUMEntries)

		// add gst debit entry from user account
		gstAmountStr, err := money.ToString(userReqDataForFileGen.gstReportingDataObj.GetGst(), inward_transaction_utils.MaxINRAmtPrecision)
		if err != nil {
			return "", 0, errors.Wrap(err, "error while forming string")
		}

		ttumEntries = append(ttumEntries, &InwardFundTransferFileModel{
			SrNo:          strconv.Itoa(srNo),
			AccountNumber: userReqDataForFileGen.accountNumber,
			SolId:         userReqDataForFileGen.solId,
			CreditOrDebit: "D",
			Amount:        gstAmountStr,
			Remarks:       userGstDebitTxnRemark,
		})
		srNo++
	}

	poolAccountDebit, err := money.ToString(req.totalPoolAccountDebit, inward_transaction_utils.MaxINRAmtPrecision)
	if err != nil {
		return "", 0, errors.Wrap(err, "error while forming string")
	}
	// now adding pool account debit entries
	ttumEntries = append(ttumEntries, &InwardFundTransferFileModel{
		SrNo:          strconv.Itoa(srNo),
		AccountNumber: o.genConf.InwardPoolAccountNumber(),
		SolId:         o.genConf.InwardsRemittancePoolAccountSolId(),
		CreditOrDebit: "D",
		Amount:        poolAccountDebit,
		// This remark helps in uniquely identifying the debit transaction from the inward remittance pool account.
		// The details of this transaction are later retrieved from the pool account's statement to be used for GST reporting
		Remarks: "USS/POOL/" + req.fileGenAttempt.GetClientRequestId(),
	})

	return o.createFileContentForTTUM(ttumEntries), int32(len(ttumEntries)), nil

}

func (o *GenerateFileForTransaction) getGstTTUMEntries(gstModel []*iftPb.GSTReportingDataModel, userAccount string, srNo int) ([]*InwardFundTransferFileModel, error) {
	fileEntries := make([]*InwardFundTransferFileModel, 0)
	for _, data := range gstModel {
		var gstRemark string
		var amount *moneyPb.Money
		var solId string
		switch {
		case data.GetCgstAmount() != nil && !money.IsZero(data.GetCgstAmount()):
			amount = data.GetCgstAmount()
			gstRemark = fmt.Sprintf("CGST/%v", userAccount)
			solId = data.GetCgstAccountSolId()
		case data.GetIgstAmount() != nil && !money.IsZero(data.GetIgstAmount()):
			amount = data.GetIgstAmount()
			gstRemark = fmt.Sprintf("IGST/%v", userAccount)
			solId = data.GetIgstAccountSolId()
		case data.GetSgstAmount() != nil && !money.IsZero(data.GetSgstAmount()):
			amount = data.GetSgstAmount()
			gstRemark = fmt.Sprintf("SGST/%v", userAccount)
			solId = data.GetSgstAccountSolId()
		default:
			continue
		}
		amountStr, err := money.ToString(amount, inward_transaction_utils.MaxINRAmtPrecision)
		if err != nil {
			return nil, err
		}
		fileEntries = append(fileEntries, &InwardFundTransferFileModel{
			SrNo:          strconv.Itoa(srNo),
			AccountNumber: data.GetReceiverAccountNumber(),
			SolId:         solId,
			CreditOrDebit: "C",
			Amount:        amountStr,
			Remarks:       gstRemark,
		})
		srNo++
	}
	return fileEntries, nil
}

// create ttum content according to struct
func (o *GenerateFileForTransaction) createFileContentForTTUM(inwardFundTransferFileModels []*InwardFundTransferFileModel) string {
	var sb strings.Builder
	for _, fileTxn := range inwardFundTransferFileModels {
		value := reflect.ValueOf(fileTxn).Elem()
		for i := 0; i < value.NumField(); i++ {
			sb.WriteString(value.Field(i).String())
			if i < value.NumField()-1 {
				// tab separate value
				sb.WriteString("\t")
			}
		}
		sb.WriteString(utils.UnixNewLine)
	}
	return sb.String()
}
