package errors

import (
	"errors"
	"fmt"
)

var (
	// Note: Below errors are from IFT service which are received after creating a US stocks order

	ErrDailyRemittanceLimitBreached       = errors.New("breached max allowed purchase limit for day")
	ErrFYRemittanceLimitBreached          = errors.New("breached max allowed purchase limit for current financial year")
	ErrSofBasedFYRemittanceLimitBreached  = errors.New("breached max allowed sof based remittance limit for current finantial year")
	ErrInsufficientSavingsAccountVintage  = errors.New("insufficient savings account vintage")
	ErrInsufficientNumberOfTransactions   = errors.New("insufficient number of transactions")
	ErrLrsLimitBreached                   = errors.New("LRS limit breached")
	ErrForeignRemittanceNotAllowedForUser = errors.New("foreign remittance is not allowed for the user")
	ErrBlacklistedUser                    = errors.New("blacklisted user for international funds transfer")
	// ErrAmountSuspected order amount is suspected only if all the following conditions are met:
	// 1. International Transaction amount is >2 L
	// 2. International Transaction amount/ Account Balance > 80%
	// 3. International Transaction Amount/ Max(Last 5 Credit transaction Amount) > 90%
	ErrAmountSuspected                  = errors.New("suspected order amount")
	ErrKycCheckFailedWithBankingPartner = errors.New("kyc check failed with banking partner")
	ErrPanCheckFailedWithBankingPartner = errors.New("pan check failed with banking partner")

	// Note: Below validations are done before creating a US stocks order

	ErrCreditFrozenForUser                          = errors.New("credit is frozen for the user")
	ErrForexRateChanged                             = errors.New("forex rate has changed from the time order is placed")
	ErrInvalidCurrencyInInvoiceData                 = errors.New("invalid order invoice data")
	ErrOrderAmountGreaterThanWithdrawableAmount     = errors.New("order amount greater than withdrawable amount")
	ErrAddFundsPreReqChecksFailed                   = errors.New("add funds pre req checks failed")
	ErrOrderAmountGreaterThanAllowedRemittanceLimit = errors.New("order amount greater than allowed remittance limit")
	ErrUserPersonalLoanHolder                       = errors.New("user is a personal loan holder, not allowing outward remittance")
	ErrUserAuthFactorRecentlyUpdated                = fmt.Errorf("user has recently updated auth factors")
)
