package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/usstocks"
	activityPb "github.com/epifi/gamma/api/usstocks/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/usstocks/order/dao"
)

// UpdateRemittanceTxnAndProcessStatus runs update query on remittance process and aggregate txn tables depending on the
// request passed to it. If detail field is present for the table, update query is run for them.
// If detail field is present for both the tables, updates are done in a transaction.
func (p *Processor) UpdateRemittanceTxnAndProcessStatus(ctx context.Context, req *activityPb.UpdateRemittanceTxnAndProcessStatusRequest) (*activityPb.UpdateRemittanceTxnAndProcessStatusResponse, error) {
	lg := activity.GetLogger(ctx)
	txnUpdateDetails := req.GetAggregateRemittanceTxnUpdateDetails()
	processUpdateDetails := req.GetRemittanceProcessUpdateDetails()
	err := p.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		if txnUpdateDetails != nil {
			target := txnUpdateDetails.GetTargetValue()
			filterOptions := make([]storageV2.FilterOption, 0)
			if target == nil || len(txnUpdateDetails.GetFieldMasks()) == 0 {
				return errors.Wrap(epifierrors.ErrPermanent, "target value or field masks for aggregate remittance txns is not present")
			}
			switch txnUpdateDetails.GetIdentifier().(type) {
			case *activityPb.AggregateRemittanceTxnUpdateDetails_ProcessId:
				target.BatchId = txnUpdateDetails.GetProcessId()
				filterOptions = append(filterOptions, dao.WithAggregatedRemittanceTransactionStateNotIn([]usstocks.AggregatedRemittanceTransactionState{
					usstocks.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_SUCCESSFUL,
					usstocks.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE,
					usstocks.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_FAILED,
				}))
			default:
				return errors.Wrap(epifierrors.ErrPermanent, "no valid identifier found for aggregate txn entry")
			}
			err := p.aggRemittanceTxnDao.Update(ctx, target, txnUpdateDetails.GetFieldMasks(), filterOptions...)
			if err != nil && !errors.Is(err, epifierrors.ErrRowNotUpdated) {
				return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while updating aggregate txn entries : %s", err.Error()))
			}
		}
		if processUpdateDetails != nil {
			target := processUpdateDetails.GetTargetValue()
			if target == nil || len(processUpdateDetails.GetFieldMasks()) == 0 {
				return errors.Wrap(epifierrors.ErrPermanent, "target value or field masks for remittance process is not present")
			}
			switch processUpdateDetails.GetIdentifier().(type) {
			case *activityPb.RemittanceProcessUpdateDetails_Id:
				target.Id = processUpdateDetails.GetId()
			default:
				return errors.Wrap(epifierrors.ErrPermanent, "no valid identifier found for remittance process entry")
			}
			_, err := p.remittanceProcessesDao.Update(ctx, target, processUpdateDetails.GetFieldMasks())
			if err != nil && !errors.Is(err, epifierrors.ErrRowNotUpdated) {
				return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while updating remittance process entry : %s", err.Error()))
			}
		}
		return nil
	})
	if err != nil {
		lg.Error("encountered error while updating details", zap.Error(err))
		return nil, err
	}
	return &activityPb.UpdateRemittanceTxnAndProcessStatusResponse{}, nil
}
