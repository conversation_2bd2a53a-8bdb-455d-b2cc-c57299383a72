package portfolio

import (
	"context"
	"sort"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/usstocks"
	usstocksEnumPb "github.com/epifi/gamma/api/usstocks"
	accountMgPb "github.com/epifi/gamma/api/usstocks/account"
	catalogMgPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksOrderManagerPb "github.com/epifi/gamma/api/usstocks/order"
	portfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	ussVg "github.com/epifi/gamma/api/vendorgateway/stocks"
	catalogDao "github.com/epifi/gamma/usstocks/catalog/dao"
	catUtil "github.com/epifi/gamma/usstocks/catalog/utils"
	genConf "github.com/epifi/gamma/usstocks/config/genconf"
	orderManagerDao "github.com/epifi/gamma/usstocks/order/dao"
	"github.com/epifi/gamma/usstocks/utils"
)

const (
	Vendor             = commonvgpb.Vendor_ALPACA
	PlaceHolderLogoUrl = "https://epifi-icons.pointz.in/usstocks_images/potfolio-default-icon.png"
)

/*
Portfolio Manager Service takes care of on getting position for user investment
All the Position related APIs should be part of Portfolio Manager
eg: getAllPositions, track the progress of a stock investment's market value
*/

type Service struct {
	portfolioPb.UnimplementedPortfolioManagerServer
	accountManagerClient            accountMgPb.AccountManagerClient
	ussVgClient                     ussVg.StocksClient
	config                          *genConf.Config
	catalogManagerClient            catalogMgPb.CatalogManagerClient
	stockDao                        catalogDao.StockDao
	symbolToExchange                map[string]catalogMgPb.Exchange
	ussOrderManagerClient           usstocksOrderManagerPb.OrderManagerClient
	orderDao                        orderManagerDao.OrdersDao
	internationalFundTransferClient iftPb.InternationalFundTransferClient
	positionsCache                  PositionsCache
}

func NewService(
	config *genConf.Config,
	accountManagerClient accountMgPb.AccountManagerClient,
	ussVgClient ussVg.StocksClient,
	catalogManagerClient catalogMgPb.CatalogManagerClient,
	stockDao catalogDao.StockDao,
	ussOrderManagerClient usstocksOrderManagerPb.OrderManagerClient,
	orderDao orderManagerDao.OrdersDao,
	internationalFundTransferClient iftPb.InternationalFundTransferClient,
	positionsCache PositionsCache,
) *Service {
	symbolToExchange := make(map[string]catalogMgPb.Exchange)
	ctx := context.Background()
	stockUniverse, err := catUtil.GetStockUniverse(ctx, stockDao, config)
	if err != nil {
		logger.Panic("failed to load stock universe", zap.Error(err))
	}
	for _, stockConfig := range stockUniverse {
		symbolToExchange[stockConfig.Symbol] = stockConfig.Exchange
	}
	return &Service{
		accountManagerClient:            accountManagerClient,
		config:                          config,
		ussVgClient:                     ussVgClient,
		catalogManagerClient:            catalogManagerClient,
		stockDao:                        stockDao,
		symbolToExchange:                symbolToExchange,
		ussOrderManagerClient:           ussOrderManagerClient,
		orderDao:                        orderDao,
		internationalFundTransferClient: internationalFundTransferClient,
		positionsCache:                  positionsCache,
	}
}

// Todo(numan) add pagination for this rpc
func (s *Service) GetAllOpenPositions(ctx context.Context, request *portfolioPb.GetAllOpenPositionsRequest) (*portfolioPb.GetAllOpenPositionsResponse, error) {
	actorId := request.GetActorId()
	storedOpenPositions, _, err := s.getAllOpenPositions(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while getting all open position", zap.Error(err))
		return &portfolioPb.GetAllOpenPositionsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// user doesn't have an open position in any stock
	if storedOpenPositions == nil || len(storedOpenPositions.Positions) == 0 {
		return &portfolioPb.GetAllOpenPositionsResponse{Status: rpc.StatusOk()}, nil
	}
	portfolioConfig := s.config.PortfolioConfig()

	positions := make([]*portfolioPb.Position, 0)
	for _, storedPosition := range storedOpenPositions.Positions {
		symbol := storedPosition.Position.GetSymbol()
		// Check if filtering is enabled and the symbol is in the filtered list
		if portfolioConfig.EnableSymbolFiltering() &&
			lo.Contains(portfolioConfig.FilteredSymbols().ToStringArray(), symbol) {
			logger.Info(ctx, "Skipping filtered symbol from positions", zap.String(logger.SYMBOL_ID, symbol))
			continue
		}

		bePosition := getPosition(storedPosition)
		exchange, ok := s.symbolToExchange[symbol]
		if !ok {
			logger.Error(ctx, "unable to find exchange for symbol", zap.String(logger.SYMBOL_ID, symbol))
			return &portfolioPb.GetAllOpenPositionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("unable to find exchange for symbol"),
			}, nil
		}
		stock, err := s.stockDao.GetBySymbolAndExchangeId(ctx, symbol, exchange, []catalogMgPb.StockFieldMask{catalogMgPb.StockFieldMask_STOCK_ID, catalogMgPb.StockFieldMask_STOCK_COMPANY_INFO, catalogMgPb.StockFieldMask_STOCK_STOCK_BASIC_DETAILS})
		if err != nil {
			logger.Error(ctx, "unable to find stockId for given symbol", zap.String(logger.SYMBOL_ID, symbol), zap.Error(err))
			return &portfolioPb.GetAllOpenPositionsResponse{
				Status: rpc.StatusInternalWithDebugMsg("unable to find stockId for given symbol"),
			}, nil
		}
		bePosition.StockId = stock.GetId()
		bePosition.DisplayName = stock.GetStockBasicDetails().GetName().GetShortName()
		bePosition.LogoUrl = stock.GetStockBasicDetails().GetLogoUrl()
		positions = append(positions, bePosition)
	}
	switch request.GetSortBy() {
	case usstocksEnumPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_INVESTED_AMOUNT:
		sort.Slice(positions, func(i, j int) bool {
			// TODO(Brijesh): Migrate to CompareV2
			return moneyPkg.Compare(positions[i].GetInvestedAmount(), positions[j].GetInvestedAmount()) == 1
		})
	case usstocksEnumPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_MARKET_VALUE:
		sort.Slice(positions, func(i, j int) bool {
			return moneyPkg.Compare(positions[i].GetMarketValue(), positions[j].GetMarketValue()) == 1
		})
	case usstocksEnumPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_RETURNS:
		sort.Slice(positions, func(i, j int) bool {
			return moneyPkg.Compare(positions[i].GetPlAmount(), positions[j].GetPlAmount()) == 1
		})
		// default sort by returns
	case usstocksEnumPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED:
		sort.Slice(positions, func(i, j int) bool {
			return positions[i].GetPlPercentage() > positions[j].GetPlPercentage()
		})
	case usstocksEnumPb.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_RETURNS_PERCENTAGE:
		sort.Slice(positions, func(i, j int) bool {
			return positions[i].GetPlPercentage() > positions[j].GetPlPercentage()
		})
	}
	return &portfolioPb.GetAllOpenPositionsResponse{
		Status: rpc.StatusOk(),
		PageContext: &rpc.PageContextResponse{
			HasBefore: false,
			HasAfter:  false,
		},
		Positions:    positions,
		TotalHolding: uint32(len(positions)),
	}, nil
}
func (s *Service) GetPositionBySymbol(ctx context.Context, request *portfolioPb.GetPositionBySymbolRequest) (*portfolioPb.GetPositionBySymbolResponse, error) {
	actorId := request.GetActorId()
	getAccountResp, err := s.accountManagerClient.GetAccount(ctx, &accountMgPb.GetAccountRequest{
		ActorId: actorId,
		Vendor:  Vendor,
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{(&accountMgPb.Account{}).GetAccountVendorAccountIdPath(), (&accountMgPb.Account{}).GetAccountAccountStatusPath()},
		},
	})
	if err2 := epifigrpc.RPCError(getAccountResp, err); err2 != nil {
		logger.Error(ctx, "error in getting accounts", zap.Error(err2))
		if getAccountResp.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode() {
			return &portfolioPb.GetPositionBySymbolResponse{Status: &rpc.Status{Code: uint32(portfolioPb.GetPositionBySymbolResponse_NOT_FOUND)}}, nil
		}
		return &portfolioPb.GetPositionBySymbolResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting accounts"),
		}, nil
	}

	// handle inactive user
	if getAccountResp.GetAccount().GetAccountStatus() != accountMgPb.AccountStatus_ACTIVE {
		return &portfolioPb.GetPositionBySymbolResponse{Status: &rpc.Status{Code: uint32(portfolioPb.GetPositionBySymbolResponse_NOT_FOUND)}}, nil
	}

	getStocksResp, err := s.catalogManagerClient.GetStocks(ctx, &catalogMgPb.GetStocksRequest{
		Ids: []string{request.GetStockId()},
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{"symbol", "company_info", "stock_basic_details"},
		},
	})

	if err2 := epifigrpc.RPCError(getStocksResp, err); err2 != nil {
		logger.Error(ctx, "error in getting stock", zap.String(logger.STOCK_ID, request.GetStockId()), zap.Error(err2))
		return &portfolioPb.GetPositionBySymbolResponse{Status: rpc.StatusInternalWithDebugMsg("error in getting stock")}, nil
	}
	stock, ok := getStocksResp.Stocks[request.GetStockId()]
	if !ok {
		logger.Error(ctx, "error while fetching Stock", zap.String(logger.STOCK_ID, request.GetStockId()))
		return &portfolioPb.GetPositionBySymbolResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching Stock"),
		}, nil
	}
	storedPosition, err := s.positionsCache.GetPositionForSymbol(ctx, getAccountResp.GetAccount().GetVendorAccountId(), stock.GetSymbol())
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting position for symbol from cache", zap.Error(err))
			return &portfolioPb.GetPositionBySymbolResponse{Status: rpc.StatusInternal()}, nil
		}
		return &portfolioPb.GetPositionBySymbolResponse{Status: rpc.StatusOk()}, nil
	}
	position := getPosition(storedPosition)
	sellLockUnit, err := s.GetSellLockUnit(ctx, actorId, position.GetSymbol(), getAccountResp.GetAccount().GetVendorAccountId())
	if err != nil {
		logger.Error(ctx, "error in getting sell Lock Units", zap.Error(err))
		return &portfolioPb.GetPositionBySymbolResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	position.SellLockUnit = sellLockUnit
	position.LogoUrl = stock.GetStockBasicDetails().GetLogoUrl()
	position.DisplayName = stock.GetStockBasicDetails().GetName().GetShortName()
	return &portfolioPb.GetPositionBySymbolResponse{
		Status:   rpc.StatusOk(),
		Position: position,
	}, nil
}

func getPosition(storedPosition *CachedPosition) *portfolioPb.Position {
	return &portfolioPb.Position{
		Symbol:                  storedPosition.Position.GetSymbol(),
		InvestedAmount:          storedPosition.Position.GetCostBasis(),
		PlAmount:                storedPosition.Position.GetUnrealizedPl(),
		PlPercentage:            storedPosition.Position.GetUnrealizedPlPercentage() * 100,
		TotalQty:                storedPosition.Position.GetQty(),
		AvgPrice:                storedPosition.Position.GetAvgEntryPrice(),
		MarketValue:             storedPosition.Position.GetMarketValue(),
		DisplayName:             storedPosition.Position.GetSymbol(),
		LogoUrl:                 PlaceHolderLogoUrl,
		CurrentPrice:            storedPosition.Position.GetCurrentPrice(),
		LastRefreshedAt:         storedPosition.UpdatedAt,
		PlPercentageChangeToday: storedPosition.Position.GetPercentageChangeToday(),
	}
}

func (s *Service) GetSellLockUnit(ctx context.Context, actorId string, symbol string, vendorAccountId string) (float64, error) {

	if !s.config.ShouldUseSellLock() {
		return 0.0, nil
	}
	logger.Info(ctx, "fetching order confirmed with vendor and in-progress due to buy flow")
	// Buy Order is considered completed after FOREIGN_FUND_TRANSFER stage is successful.
	// All the orders with FOREIGN_FUND_TRANSFER stage not success is considered to be incomplete, user is not allowed to create a sell request for such orders
	// Getting all orders with FOREIGN_FUND_TRANSFER in incomplete state to apply sell lock on the quantities associated with the orders
	orderWithQtyConfirmed, err := s.orderDao.GetOrdersForWFHistory(ctx, actorId, symbol, usstocks.OrderSide_BUY, utils.NonTerminalOrderState, workflowPb.Stage_FOREIGN_FUND_TRANSFER, stagePb.Status_SUCCESSFUL)
	sellLockUnit := 0.0
	if err != nil {
		logger.Error(ctx, "error while getting FOREIGN_FUND_TRANSFER stage Order", zap.Error(err))
		return 0, err
	}
	for _, order := range orderWithQtyConfirmed {
		sellLockUnit += order.GetQtyConfirmed()
	}

	// TRACK_ORDER we pool for order status so, getting all order which are in this stage and status is not success
	// get all qty which are filed by vendor recently but our system might unaware of state update
	// it is required since we are fetching portfolio from vendor real time
	// so this unit are not suppose to be sell till swift transfer happens
	orderPlacedWithVendor, err := s.orderDao.GetOrdersForWFHistory(ctx, actorId, symbol, usstocks.OrderSide_BUY, utils.NonTerminalOrderState, workflowPb.Stage_TRACK_ORDER, stagePb.Status_SUCCESSFUL)
	if err != nil {
		logger.Error(ctx, "error while getting TRACK_ORDER stage orders", zap.Error(err))
		return 0, err
	}

	logger.Info(ctx, "fetching order placed with vendor", zap.String(logger.SYMBOL_ID, symbol))
	for _, order := range orderPlacedWithVendor {
		vgOrderResp, err := s.ussVgClient.GetOrderDetails(ctx, &ussVg.GetOrderDetailsRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
			AccountId: vendorAccountId,
			OrderId:   order.GetId(),
		})

		// returning even one call is failed, to handle consistency and making sure user doesnt sell more than he expected
		if err2 := epifigrpc.RPCError(vgOrderResp, err); err2 != nil {
			logger.Error(ctx, "error in getting order status", zap.Error(err2))
			return 0, err2
		}
		if vgOrderResp.GetOrderDetails().GetOrderStatus() == ussVg.OrderStatus_ORDER_STATUS_FILLED {
			sellLockUnit += vgOrderResp.GetOrderDetails().GetFilledQty()
		}
	}
	return sellLockUnit, nil
}

func (s *Service) GetInvestmentSummaryInfo(ctx context.Context, request *portfolioPb.GetInvestmentSummaryInfoRequest) (*portfolioPb.GetInvestmentSummaryInfoResponse, error) {
	onboardingSummary, err := s.getOnboardingSummary(ctx, request)
	if err != nil {
		logger.Error(ctx, "error in getting onboarding status", zap.Error(err))
		return &portfolioPb.GetInvestmentSummaryInfoResponse{Status: rpc.StatusInternal()}, nil
	}
	// If a user is not onboarded, there won't be any investment / wallet info to retrieve.
	if onboardingSummary.GetOnboardingStatus() != portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_SUCCESSFUL {
		return &portfolioPb.GetInvestmentSummaryInfoResponse{
			Status:            rpc.StatusOk(),
			OnboardingSummary: onboardingSummary,
		}, nil
	}

	var investmentSummary *portfolioPb.InvestmentSummaryInfo
	if lo.Contains(request.GetFieldMasks(), portfolioPb.GetInvestmentSummaryInfoRequest_FIELD_MASK_INVESTMENT_SUMMARY) {
		var statsRes *usstocksOrderManagerPb.GetStatsResponse
		statsRes, err = s.ussOrderManagerClient.GetStats(ctx, &usstocksOrderManagerPb.GetStatsRequest{
			ActorId: request.GetActorId(),
			Sides:   []usstocks.OrderSide{usstocks.OrderSide_BUY},
			FieldMask: []usstocksOrderManagerPb.StatsFieldMask{
				usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT,
				usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT,
				usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_SUCCESS_ORDERS_COUNT,
			},
			WalletSubTypes: []usstocks.WalletOrderSubType{usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_NON_INSTANT_WALLET_FUNDING},
		})
		if err = epifigrpc.RPCError(statsRes, err); err != nil {
			logger.Error(ctx, "error getting stats", zap.Error(err))
			return &portfolioPb.GetInvestmentSummaryInfoResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		investmentSummary, err = s.getInvestmentSummary(ctx, request, statsRes)
		if err != nil {
			logger.Error(ctx, "error getting investment summary", zap.Error(err))
			return &portfolioPb.GetInvestmentSummaryInfoResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	var walletSummaryInfo *portfolioPb.WalletSummary
	if lo.Contains(request.GetFieldMasks(), portfolioPb.GetInvestmentSummaryInfoRequest_FIELD_MASK_WALLET_SUMMARY) {
		var statsRes *usstocksOrderManagerPb.GetStatsResponse
		statsRes, err = s.ussOrderManagerClient.GetStats(ctx, &usstocksOrderManagerPb.GetStatsRequest{
			ActorId: request.GetActorId(),
			WalletSubTypes: []usstocks.WalletOrderSubType{
				usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_NON_INSTANT_WALLET_FUNDING,
				usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_NON_INSTANT_WALLET_WITHDRAWAL,
			},
			FieldMask: []usstocksOrderManagerPb.StatsFieldMask{
				usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_COUNT,
				usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_AMOUNT,
				usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_SUCCESS_WALLET_ORDERS_COUNT,
			},
		})
		if err = epifigrpc.RPCError(statsRes, err); err != nil {
			logger.Error(ctx, "error getting stats", zap.Error(err))
			return &portfolioPb.GetInvestmentSummaryInfoResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		walletSummaryInfo, err = s.getWalletSummary(ctx, request.GetActorId(), statsRes)
		if err != nil {
			logger.Error(ctx, "error getting wallet summary", zap.Error(err))
			return &portfolioPb.GetInvestmentSummaryInfoResponse{Status: rpc.StatusInternal()}, nil
		}
	}
	return &portfolioPb.GetInvestmentSummaryInfoResponse{
		Status:            rpc.StatusOk(),
		InvestmentSummary: investmentSummary,
		WalletSummaryInfo: walletSummaryInfo,
		OnboardingSummary: onboardingSummary,
	}, nil
}

func (s *Service) getOnboardingSummary(ctx context.Context, request *portfolioPb.GetInvestmentSummaryInfoRequest) (*portfolioPb.OnboardingSummary, error) {
	accountResp, err := s.accountManagerClient.GetAccount(ctx, &accountMgPb.GetAccountRequest{
		Vendor:    commonvgpb.Vendor_ALPACA,
		ActorId:   request.GetActorId(),
		FieldMask: &fieldmaskpb.FieldMask{Paths: []string{(&accountMgPb.Account{}).GetAccountAccountStatusPath()}},
	})
	if err2 := epifigrpc.RPCError(accountResp, err); err2 != nil {
		if accountResp.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode() {
			return &portfolioPb.OnboardingSummary{
				OnboardingStatus: portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_NOT_STARTED,
			}, nil
		}
		return nil, errors.Wrap(err2, "error in getting us stocks account")
	}

	switch accountResp.GetAccount().GetAccountStatus() {
	case accountMgPb.AccountStatus_ACTIVE:
		return &portfolioPb.OnboardingSummary{
			OnboardingStatus: portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_SUCCESSFUL,
			LastUpdatedAt:    accountResp.GetAccount().GetUpdatedAt(),
		}, nil
	case accountMgPb.AccountStatus_CREATION_ON_HOLD, accountMgPb.AccountStatus_REJECTED, accountMgPb.AccountStatus_INACTIVE:
		return &portfolioPb.OnboardingSummary{
			OnboardingStatus: portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_FAILED,
		}, nil
	default:
		return &portfolioPb.OnboardingSummary{OnboardingStatus: portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_IN_PROGRESS}, nil
	}
}

func getPercentageChange(l *money.Money, r *money.Money) float64 {
	// if denominator is null or zero, returning 0
	if r == nil || moneyPkg.IsZero(r) {
		return 0
	}
	decimalL := moneyPkg.ToDecimal(l)
	decimalR := moneyPkg.ToDecimal(r)
	res, _ := decimalL.Div(decimalR).Float64()
	return res * 100
}

func (s *Service) getAllOpenPositions(ctx context.Context, actorId string) (*CachedPositions, string, error) {
	getAccountResp, err := s.accountManagerClient.GetAccount(ctx, &accountMgPb.GetAccountRequest{
		ActorId: actorId,
		Vendor:  Vendor,
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{(&accountMgPb.Account{}).GetAccountVendorAccountIdPath(), (&accountMgPb.Account{}).GetAccountAccountStatusPath()},
		},
	})
	if err2 := epifigrpc.RPCError(getAccountResp, err); err2 != nil {
		if getAccountResp.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode() {
			return nil, "", nil
		}
		logger.Error(ctx, "error in getting accounts", zap.Error(err2))
		return nil, "", err2
	}

	// handle inactive user and send no position data to handle v0 design
	if getAccountResp.GetAccount().GetAccountStatus() != accountMgPb.AccountStatus_ACTIVE {
		return nil, "", nil
	}

	storedPositions, err := s.positionsCache.GetAllOpenPositions(ctx, getAccountResp.GetAccount().GetVendorAccountId())
	if err != nil {
		logger.Error(ctx, "error getting all open positions", zap.Error(err))
		return nil, "", err
	}
	return storedPositions, getAccountResp.GetAccount().GetVendorAccountId(), nil
}

func (s *Service) GetPortfolioDetailsForCx(ctx context.Context, req *portfolioPb.GetPortfolioDetailsForCxRequest) (*portfolioPb.GetPortfolioDetailsForCxResponse, error) {
	getAccountResp, err := s.accountManagerClient.GetAccount(ctx, &accountMgPb.GetAccountRequest{
		ActorId: req.GetActorId(),
		Vendor:  Vendor,
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{(&accountMgPb.Account{}).GetAccountVendorAccountIdPath()},
		},
	})
	if err2 := epifigrpc.RPCError(getAccountResp, err); err2 != nil {
		if getAccountResp.GetStatus().IsRecordNotFound() {
			return &portfolioPb.GetPortfolioDetailsForCxResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		logger.Error(ctx, "error in getting accounts", zap.Error(err2))
		return &portfolioPb.GetPortfolioDetailsForCxResponse{Status: rpc.StatusInternal()}, nil
	}

	assetDetailsResp, err := s.orderDao.GetPortfolioDetails(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while fetching data from GetPortfolioDetails", zap.Error(err))
		return &portfolioPb.GetPortfolioDetailsForCxResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	stockSymbolToTypeMap := make(map[string]catalogMgPb.StockType)
	stockUniverse, err := catUtil.GetStockUniverse(ctx, s.stockDao, s.config)
	if err != nil {
		logger.Error(ctx, "error loading stock universe", zap.Error(err))
		return &portfolioPb.GetPortfolioDetailsForCxResponse{
			Status: rpc.StatusInternalWithDebugMsg("error loading stock universe"),
		}, nil
	}
	for _, stockConfig := range stockUniverse {
		stockSymbolToTypeMap[stockConfig.Symbol] = stockConfig.StockType
	}

	// assigning sell lock units for each symbol
	errGroup, gCtx := errgroup.WithContext(ctx)
	for i := 0; i < len(assetDetailsResp); i++ {
		func(idx int) {
			errGroup.Go(func() error {
				sellLockUnits, respErr := s.GetSellLockUnit(gCtx, req.GetActorId(), assetDetailsResp[idx].GetSymbol(), getAccountResp.GetAccount().GetVendorAccountId())
				if respErr != nil {
					logger.Error(ctx, "error while getting sell lock units", zap.Error(respErr))
					return respErr
				}
				assetDetailsResp[idx].SellLockUnits = sellLockUnits
				assetDetailsResp[idx].StockType = stockSymbolToTypeMap[assetDetailsResp[idx].GetSymbol()]
				return nil
			})
		}(i)
	}

	if err = errGroup.Wait(); err != nil {
		logger.Error(ctx, "error while getting sell lock units for orders", zap.Error(err))
		return &portfolioPb.GetPortfolioDetailsForCxResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &portfolioPb.GetPortfolioDetailsForCxResponse{
		Status:        rpc.StatusOk(),
		AssetsDetails: assetDetailsResp,
	}, nil
}

// nolint:funlen
func (s *Service) getInvestmentSummary(ctx context.Context, request *portfolioPb.GetInvestmentSummaryInfoRequest, statsRes *usstocksOrderManagerPb.GetStatsResponse) (*portfolioPb.InvestmentSummaryInfo, error) {
	var forexRateRes *iftPb.GetForexRateResponse
	forexRateRes, err := s.internationalFundTransferClient.GetForexRate(ctx, &iftPb.GetForexRateRequest{
		Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
		CurrencyCode:   moneyPkg.USDCurrencyCode,
		RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_INWARD,
	})
	if err = epifigrpc.RPCError(forexRateRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting forex rate")
	}
	storedOpenPositions, _, err := s.getAllOpenPositions(ctx, request.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting all open position")
	}
	if storedOpenPositions == nil || len(storedOpenPositions.Positions) == 0 {
		return &portfolioPb.InvestmentSummaryInfo{
			ProcessingAmount:   statsRes.GetAmountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT.String()],
			PendingOrdersCount: statsRes.GetCountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT.String()],
			ForexRateInInr:     forexRateRes.GetExchangeRate(),
		}, nil
	}
	investedAmount, currValOfInvestedAmount, unrealizedPnl := moneyPkg.ZeroUSD(), moneyPkg.ZeroUSD(), moneyPkg.ZeroUSD()
	for _, storedPosition := range storedOpenPositions.Positions {
		// Due to corporate actions like acquisitions, mergers, de-listings, etc. the market value, P&L, and other
		// metrics for the position held by user in a ticker may be temporarily set to zero by Alpaca.
		// In such cases, returning an error out leads to user not seeing any data in the portfolio page.
		// Hence, we skip these positions from the calculation.
		if !moneyPkg.IsZero(storedPosition.Position.GetCostBasis()) {
			investedAmount, err = moneyPkg.Sum(investedAmount, storedPosition.Position.GetCostBasis())
			if err != nil {
				return nil, errors.Wrapf(err, "error adding position cost basis: %v", storedPosition.Position.GetCostBasis())
			}
		}
		if !moneyPkg.IsZero(storedPosition.Position.GetMarketValue()) {
			currValOfInvestedAmount, err = moneyPkg.Sum(currValOfInvestedAmount, storedPosition.Position.GetMarketValue())
			if err != nil {
				return nil, errors.Wrapf(err, "error adding position market value: %v", storedPosition.Position.GetMarketValue())
			}
		}
		if !moneyPkg.IsZero(storedPosition.Position.GetUnrealizedIntradayPl()) {
			unrealizedPnl, err = moneyPkg.Sum(unrealizedPnl, storedPosition.Position.GetUnrealizedIntradayPl())
			if err != nil {
				return nil, errors.Wrapf(err, "error adding position unrealized intraday P&L: %v", storedPosition.Position.GetUnrealizedIntradayPl())
			}
		}
	}
	realizedCurrValOfInvestedAmount, err := moneyPkg.Subtract(currValOfInvestedAmount, unrealizedPnl)
	if err != nil {
		return nil, errors.Wrap(err, "error subtracting one day absolute change from current amount")
	}
	growthAmount, err := moneyPkg.Subtract(currValOfInvestedAmount, investedAmount)
	if err != nil {
		return nil, errors.Wrap(err, "error subtracting invested amount from current amount")
	}
	growthPercentage, err := moneyPkg.CalculateDiffPercentage(currValOfInvestedAmount, investedAmount)
	if err != nil {
		return nil, errors.Wrap(err, "error calculating growth percentage")
	}
	growthPercent, _ := growthPercentage.Float64()
	return &portfolioPb.InvestmentSummaryInfo{
		InvestedAmount:           investedAmount,
		CurrentAmount:            currValOfInvestedAmount,
		GrowthAmount:             growthAmount,
		GrowthPercent:            growthPercent,
		ProcessingAmount:         statsRes.GetAmountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT.String()],
		HasInvested:              storedOpenPositions != nil && len(storedOpenPositions.Positions) > 0,
		OneDayGrowth:             getPercentageChange(unrealizedPnl, realizedCurrValOfInvestedAmount),
		ForexRateInInr:           forexRateRes.GetExchangeRate(),
		PendingOrdersCount:       statsRes.GetCountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT.String()],
		PositionsLastRefreshedAt: storedOpenPositions.UpdatedAt,
		HasEverInvested:          statsRes.GetCountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_SUCCESS_ORDERS_COUNT.String()] > 0,
	}, nil
}

func (s *Service) getWalletSummary(ctx context.Context, actorId string, statsRes *usstocksOrderManagerPb.GetStatsResponse) (*portfolioPb.WalletSummary, error) {
	tradingAccountDetailsResp, err := s.accountManagerClient.GetTradingAccountDetails(ctx, &accountMgPb.GetTradingAccountDetailsRequest{
		Identifier: &accountMgPb.TradingAccountIdentifier{
			Identifier: &accountMgPb.TradingAccountIdentifier_ActorId{ActorId: actorId},
		},
		Strategy: accountMgPb.GetTradingAccountDetailsRequest_REAL_TIME,
	})
	if err2 := epifigrpc.RPCError(tradingAccountDetailsResp, err); err2 != nil {
		if tradingAccountDetailsResp.GetStatus().IsRecordNotFound() {
			return &portfolioPb.WalletSummary{}, nil
		}
		return nil, errors.Wrap(err2, "error in getting trading account details")
	}

	return &portfolioPb.WalletSummary{
		HasFundsInWallet:   !moneyPkg.IsZero(tradingAccountDetailsResp.GetTradingAccount().GetWalletDetails().GetCash()),
		ProcessingAmount:   statsRes.GetAmountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_AMOUNT.String()],
		PendingOrdersCount: statsRes.GetCountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_COUNT.String()],
		SuccessOrdersCount: statsRes.GetCountStats()[usstocksOrderManagerPb.StatsFieldMask_STATS_FIELD_MASK_SUCCESS_WALLET_ORDERS_COUNT.String()],
		Cash:               tradingAccountDetailsResp.GetTradingAccount().GetWalletDetails().GetCash(),
	}, nil
}
