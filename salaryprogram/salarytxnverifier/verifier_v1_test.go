package salarytxnverifier

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	moneyPb "github.com/epifi/be-common/pkg/money"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	categorizerMocksPb "github.com/epifi/gamma/api/categorizer/mocks"
	empPb "github.com/epifi/gamma/api/employment"
	employmentPb "github.com/epifi/gamma/api/employment"
	employmentMocks "github.com/epifi/gamma/api/employment/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	mocksVgEmp "github.com/epifi/gamma/api/vendorgateway/employment/mocks"
	namecheckVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	employernamecategoriserMocksPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser/mocks"
	employernamematchVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	employernamematchMocksPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch/mocks"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgPaymentMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	"github.com/epifi/gamma/api/vendors/karza"
	daoMocks "github.com/epifi/gamma/salaryprogram/test/mocks/dao"
	mock_employerverifier "github.com/epifi/gamma/salaryprogram/test/mocks/employerverifier"
	minreqsalaryamountMocks "github.com/epifi/gamma/salaryprogram/test/mocks/minreqsalaryamount"
)

func TestSalaryTxnVerifierV1_IsSalaryTxn(t *testing.T) {
	toActorId := "to-actor-1"
	fromActorId := "from-actor-1"
	fromActorName := "from-actor-name"
	userDeclaredEmployerName := "employer-name"
	employerDbEmployerId := "db-employer-id"
	currTimeTimeStamp := timestampPb.Now()
	timestampBeforeOneMonthFromNow := timestampPb.New(currTimeTimeStamp.AsTime().AddDate(0, -1, 0))

	type args struct {
		ctx                     context.Context
		salaryTaxVerifierParams SalaryTxnVerifierParams
	}

	type mocksStruct struct {
		mockTxnCatClient                  *categorizerMocksPb.MockTxnCategorizerClient
		mockActorClient                   *actorMocks.MockActorClient
		mockPiClient                      *piMocks.MockPiClient
		mockEmpClient                     *employmentMocks.MockEmploymentClient
		mockEmployerNameMatchClient       *employernamematchMocksPb.MockEmployerNameMatchClient
		vgPaymentClient                   *vgPaymentMocks.MockPaymentClient
		employerVerifier                  *mock_employerverifier.MockISalaryEmployerVerifier
		vgEmploymentClient                *mocksVgEmp.MockEmploymentClient
		usersClient                       *userMocks.MockUsersClient
		mockEmployerNameCategoriserClient *employernamecategoriserMocksPb.MockEmployerNameCategoriserClient
		mockSalaryTxnVerReqDao            *daoMocks.MockISalaryTxnVerificationRequestDao
	}

	tests := []struct {
		name                     string
		args                     args
		setupMocks               func(mocks *mocksStruct)
		wantVerificationResult   bool
		wantVerificationMetadata *VerificationMetadata
		wantErr                  bool
		wantErrType              error
	}{
		{
			name: "should return false when order workflow is not NO_OP",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_P2P_FUND_TRANSFER},
						Transactions: []*paymentPb.Transaction{
							{
								Id:        "txn-id-1",
								CreatedAt: timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: OrderWorkFlowNotAllowed,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_WORKFLOW,
			},
			wantVerificationResult: false,
		},
		{
			name: "should return false when txn is DEPOSIT txn",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id: "order-1", Status: orderPb.OrderStatus_PAID,
							ToActorId: "to-actor-1",
							Workflow:  orderPb.OrderWorkflow_NO_OP,
							Tags:      []orderPb.OrderTag{orderPb.OrderTag_DEPOSIT},
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:        "txn-id-1",
								CreatedAt: timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: TxnIsDepositTxn,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_NOT_DEPOSIT_TXN,
			},
		},
		{
			name: "should return false when order amount < 25k",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(24000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: TxnAmountIsLessThanMinReqSalaryAmount,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MIN_AMOUNT,
			},
		},
		{
			name: "should return false when order amount is greater than discounted threshold but less than min req threshold for reverification",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(18000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
					DiscountOnMinimumSalaryThreshold:         30,
					MinRequiredSalaryAmountForReverification: 20000,
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: TxnAmountIsLessThanMinReqSalaryAmount,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MIN_AMOUNT,
			},
		},
		{
			name: "should return error when order is not in PAID (terminal success) state",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_IN_PAYMENT, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(25000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: OrderStatusNotPaid,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TERMINAL_STATE,
			},
			wantErr: false,
		},
		{
			name: "should return false when order has more than 1 txn state",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP},
						Transactions: []*paymentPb.Transaction{
							{
								Id: "txn-id-1",
							},
							{
								Id: "txn-id-2",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: NoOfTxnIsNotOneInOrder,
			},
		},
		{
			name: "should return false when order amount < 25k and reverification discount is applied on threshold but payment protocol of txn is not NEFT/RTGS/IMPS/INTRA",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(25000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
					DiscountOnMinimumSalaryThreshold:         30,
					MinRequiredSalaryAmountForReverification: 10000,
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: PaymentProtocolNotAllowed,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PAYMENT_PROTOCOL,
			},
		},
		{
			name: "should return false when order amount < 25k and reverification discount is applied on threshold but payment protocol of txn is not NEFT/RTGS/IMPS/INTRA, when passed in validations to run",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(25000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
					DiscountOnMinimumSalaryThreshold:         30,
					MinRequiredSalaryAmountForReverification: 10000,
					ValidationsToRun:                         []salaryPb.SalaryTxnValidation{salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PAYMENT_PROTOCOL},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: PaymentProtocolNotAllowed,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_PAYMENT_PROTOCOL,
			},
		},
		{
			name: "should return error ErrTxnCategoryNotFound when GetTxnCategoryDetails rpc returns record not found status",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantVerificationResult: false,
			wantErr:                true,
			wantErrType:            ErrTxnCategoryNotFound,
		},
		{
			name: "should return false when order is not categorized as income by txn categorizer",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "non-income-ontology-id-1",
							},
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: TxnIsCategorisedAsNonIncome,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TXN_CATEGORY_NON_INCOME,
			},
		},
		{
			name: "should return false when order is not categorized as income by txn categorizer, when given in validations to run",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
					ValidationsToRun: []salaryPb.SalaryTxnValidation{salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TXN_CATEGORY_NON_INCOME},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "non-income-ontology-id-1",
							},
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: TxnIsCategorisedAsNonIncome,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_TXN_CATEGORY_NON_INCOME,
			},
		},
		{
			name: "should return error when txn beneficiary is not a Fi User",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_EXTERNAL_MERCHANT,
				}, nil)
			},
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:       "emp-id-1",
				VerificationFailureReason: NotFiUser,
				FailedSalaryTxnValidation: salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_FI_USER,
			},
			wantErr: true,
		},
		{
			name: "should return false when no current employer exist for the beneficiary actor",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				VerificationFailureReason: NoEmployerExistForUser,
			},
			wantErr: false,
		},
		{
			name: "should return error when rpc call to fetch the current employer of beneficiaryActor fails",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantVerificationResult: false,
			wantErr:                true,
		},
		{
			name: "should return false if current employer of the beneficiaryActor is not a verified employer",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     false,
							PossibleRemitterNames:          []string{"remitter-name-1"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				VerificationFailureReason: NoEmployerExistForUser,
			},
			wantErr: false,
		},
		{
			name: "should return true when txn remitter pi matches with the pi associated to user's employer",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "emp-id-1",
					},
				}, nil)
			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_ID_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return error ErrUanLookupApiFailed when txn remitter name matches one of the remitter names of the user declared employer and the employer is not eligible for salary program and uan lookup api fails",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_SOFT_BLACKLISTED,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-2",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(2)

				mocks.usersClient.EXPECT().GetUser(context.Background(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_ActorId{
						ActorId: toActorId,
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &usersPb.User{
						Profile: &usersPb.Profile{
							PAN: "pan-no",
						},
					},
				}, nil)
				mocks.vgEmploymentClient.EXPECT().UANLookupByPan(context.Background(), &vgEmploymentPb.UANLookupByPanRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_KARZA,
					},
					Pan: "pan-no",
				}).Return(&vgEmploymentPb.UANLookupByPanResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantVerificationResult: false,
			wantErr:                true,
			wantErrType:            ErrUanLookupApiFailed,
		},
		{
			name: "should return error ErrUanLookupApiFailed when txn remitter name doesn't matches user declared employer but matches other employer in db and the employer is eligible for salary program and uan lookup api fails",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-z",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(4)

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "remitter-name-z",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "remitter-name-z",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						SalaryProgramEligibilityStatus: empPb.EmployerSalaryProgramEligibilityStatus_SOFT_BLACKLISTED,
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					})

				mocks.usersClient.EXPECT().GetUser(context.Background(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_ActorId{
						ActorId: toActorId,
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &usersPb.User{
						Profile: &usersPb.Profile{
							PAN: "pan-no",
						},
					},
				}, nil)
				mocks.vgEmploymentClient.EXPECT().UANLookupByPan(context.Background(), &vgEmploymentPb.UANLookupByPanRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_KARZA,
					},
					Pan: "pan-no",
				}).Return(&vgEmploymentPb.UANLookupByPanResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantVerificationResult: false,
			wantErr:                true,
			wantErrType:            ErrUanLookupApiFailed,
		},
		{
			name: "should return true when txn remitter name matches one of the remitter names of the user declared employer and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-2",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(2)

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return true when txn remitter name matches one of the remitter names of the user declared employer and the employer is eligible for salary program, with unspecified payment protocol",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-2",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(2)

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return true when txn remitter name matches one of the remitter names of the user declared employer and the employer is not eligible for salary program but employer uan check passed",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-x", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_SOFT_BLACKLISTED,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-2",
							},
						},
						VerifiedName: "remitter-name-x",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(3)

				mocks.usersClient.EXPECT().GetUser(context.Background(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_ActorId{
						ActorId: toActorId,
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &usersPb.User{
						Profile: &usersPb.Profile{
							PAN: "pan-no",
						},
					},
				}, nil)
				mocks.vgEmploymentClient.EXPECT().UANLookupByPan(context.Background(), &vgEmploymentPb.UANLookupByPanRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_KARZA,
					},
					Pan: "pan-no",
				}).Return(&vgEmploymentPb.UANLookupByPanResponse{
					Status: rpc.StatusOk(),
					Result: &karza.UANResult{
						Summary: &karza.LookupSummary{
							UanLookup: &karza.LookupSummary_UanLookup{
								CurrentEmployer: userDeclaredEmployerName,
							},
						},
					},
				}, nil)
			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return true when txn remitter name didn't match with user declared employer but matches with any employer in DB and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-y",
							},
						},
						VerifiedName: "remitter-name-x",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(3)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(6)

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "remitter-name-vg",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "remitter-name-vg",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						SalaryProgramEligibilityStatus: empPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					})

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:                  employerDbEmployerId,
				RemitterMatchesWithOtherEmployerInDb: true,
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_NAME_FROM_FEDERAL_REMITTER_API,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return true when txn remitter name didn't match with user declared employer but matches with any employer in DB and the employer is not eligible for salary program but employer uan check passed",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_SOFT_BLACKLISTED,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-y",
							},
						},
						VerifiedName: "remitter-name-x",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(3)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(6)

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "remitter-name-vg",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "remitter-name-vg",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						NameBySource:                   "remitter-name-vg",
						SalaryProgramEligibilityStatus: empPb.EmployerSalaryProgramEligibilityStatus_SOFT_BLACKLISTED,
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					})

				mocks.usersClient.EXPECT().GetUser(context.Background(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_ActorId{
						ActorId: toActorId,
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &usersPb.User{
						Profile: &usersPb.Profile{
							PAN: "pan-no",
						},
					},
				}, nil)
				mocks.vgEmploymentClient.EXPECT().UANLookupByPan(context.Background(), &vgEmploymentPb.UANLookupByPanRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_KARZA,
					},
					Pan: "pan-no",
				}).Return(&vgEmploymentPb.UANLookupByPanResponse{
					Status: rpc.StatusOk(),
					Result: &karza.UANResult{
						Summary: &karza.LookupSummary{
							UanLookup: &karza.LookupSummary_UanLookup{
								CurrentEmployer: "remitter-name-vg",
							},
						},
					},
				}, nil)
				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(context.Background(), &employernamematchVgPb.EmployerNameMatchRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					Name_1: "remitter-name-vg",
					Name_2: "remitter-name-vg",
				}).Return(
					&employernamematchVgPb.EmployerNameMatchResponse{
						Status:   rpc.StatusOk(),
						Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
					}, nil)
			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:                  employerDbEmployerId,
				RemitterMatchesWithOtherEmployerInDb: true,
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_NAME_FROM_FEDERAL_REMITTER_API,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return false when all the remitter name are invalid",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: "",
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "rem",
							},
						},
						VerifiedName: "Others",
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				VerificationFailureReason: NoTxnRemitterNamesLeftAfterFiltering,
			},
			wantErr: false,
		},
		{
			name: "should return false when txn remitter name neither match with user declared employer nor matches with any employer in DB",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-from-pi-account-name",
							},
						},
						VerifiedName: "remitter-name-from-pi-verified-name",
					},
				}, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Decision: 1,
					Status:   rpc.StatusOk(),
				}, nil).Times(3)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(4)

				remitterNames := []string{"remitter-name-vg", "remitter-name-from-pi-verified-name", fromActorName}

				for _, remitterName := range remitterNames {
					mocks.mockEmpClient.EXPECT().SearchCompanyV2(gomock.Any(), &empPb.SearchCompanyRequestV2{
						SearchString: remitterName,
						Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
					}).Return(&empPb.SearchCompanyResponseV2{
						Status: rpc.StatusOk(),
						Companies: []*empPb.EmployerInfo{
							{
								NameBySource: "remitter-name-random",
								EmployerId:   employerDbEmployerId,
							},
						},
					}, nil)
				}

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(5)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				VerificationFailureReason: RemitterNotMatchedCurrentEmployerOrAnyEmployerInDb,
			},
			wantErr: false,
		},
		{
			name: "should return error ErrRemitterInfoNotFound if vg remitter info api return status RecordNotFound",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: "to-actor-1", FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					CbsTranId:       "cbs-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-from-pi-account-name",
							},
						},
						VerifiedName: "remitter-name-from-pi-verified-name",
					},
				}, nil)
			},
			wantVerificationResult: false,
			wantErr:                true,
			wantErrType:            ErrRemitterInfoNotFound,
		},
		{
			name: "should return true when txn remitter name didn't with user declared employer but categorized as merchant and matches with any employer in DB and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-y",
							},
						},
						VerifiedName: "remitter-name-x",
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(6)

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "remitter-name-vg",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "remitter-name-vg",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
					},
				}, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Decision: 1,
					Status:   rpc.StatusOk(),
				}, nil).Times(3)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					})

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:                  employerDbEmployerId,
				RemitterMatchesWithOtherEmployerInDb: true,
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_NAME_FROM_FEDERAL_REMITTER_API,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return true when txn remitter name didn't match with user declared employer and NOT categorized as merchant",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-y",
							},
						},
						VerifiedName: "remitter-name-x",
					},
				}, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Decision: 0,
					Status:   rpc.StatusOk(),
				}, nil).Times(3)
			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				VerificationFailureReason: NoTxnRemitterNamesLeftAfterFiltering,
			},
			wantErr: false,
		},
		{
			name: "should return true when user is new to salaryprogram and txn remitter name (except pi verified name) matches to the user declared employer and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: "remitter-name-1",
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-z",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(4)

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_TXN_REMITTER_ACTOR_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return false with RaiseSalaryTxnVerificationRequestToOps flag true when user is new to salaryprogram and txn remitter name (pi verified name) matches to the user declared employer",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-1",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(2)

			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				RaiseSalaryTxnVerificationRequestToOps: true,
				VerificationFailureReason:              UserNewToSalaryAndRemitterPiVerifiedNameMatchedWithUserEmployerRaisingToOps,
				FailedSalaryTxnValidation:              salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_USER_DECLARED_EMPLOYER,
			},
			wantErr: false,
		},
		{
			name: "should return true when user is not new to salary program and txn remitter name didn't match with user declared employer but matches with any employer in DB and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "pi-verified-remitter-name",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "pi-verified-remitter-name",
							},
						},
						VerifiedName: "pi-verified-remitter-name",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(3)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).AnyTimes()

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "pi-verified-remitter-name",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "pi-verified-remitter-name",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						SalaryProgramEligibilityStatus: empPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).AnyTimes()

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:                  employerDbEmployerId,
				RemitterMatchesWithOtherEmployerInDb: true,
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_NAME_FROM_FEDERAL_REMITTER_API,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return false with RaiseSalaryTxnVerificationRequestToOps flag true when user is new to salary program and txn remitter name didn't match with user declared employer but txn remitter (pi_verified_name) matches with any employer in DB and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-y",
							},
						},
						VerifiedName: "pi-verified-remitter-name",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(3)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(6)

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "pi-verified-remitter-name",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "pi-verified-remitter-name",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						SalaryProgramEligibilityStatus: empPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(1)

			},
			wantVerificationResult: false,
			wantVerificationMetadata: &VerificationMetadata{
				RaiseSalaryTxnVerificationRequestToOps: true,
				RemitterMatchesWithOtherEmployerInDb:   true,
				VerificationFailureReason:              UserNewToSalaryAndRemitterPiVerifiedNameMatchedWithAnyEmployerInDbRaisingToOps,
				FailedSalaryTxnValidation:              salaryPb.SalaryTxnValidation_SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_ANY_EMPLOYER_IN_DB,
			},
			wantErr: false,
		},
		{
			name: "should return error when GetSalaryTxnVerificationRequestsPaginated dao call fails while checking whether user is new to salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								CreatedAt:       timestampPb.Now(),
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: currTimeTimeStamp,
								},
								Utr:    "utr-id",
								PiFrom: "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							NameBySource:                   userDeclaredEmployerName,
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.vgPaymentClient.EXPECT().GetRemitterDetailsV1(gomock.Any(), &vgPaymentPb.GetRemitterDetailsV1Request{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Utr:             "utr-id",
					TxnDatetime:     currTimeTimeStamp,
					PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
				}).Return(&vgPaymentPb.GetRemitterDetailsV1Response{
					Status: rpc.StatusOk(),
					RemitterInfo: &vgPaymentPb.GetRemitterDetailsV1Response_RemitterAccountInfo{
						RemitterAccountInfo: &vgPaymentPb.RemitterAccountInfo{
							RemitterName: "remitter-name-vg",
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-y",
							},
						},
						VerifiedName: "pi-verified-remitter-name",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return(nil, nil, errors.New(""))

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(3)
			},
			wantVerificationResult: false,
			wantErr:                true,
		},
		{
			name: "should return true when remitter api is not called due to txn being older than allowed duration and txn remitter name matches one of the remitter names of the user declared employer and the employer is eligible for salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-1", ToActorId: toActorId, FromActorId: fromActorId, Status: orderPb.OrderStatus_PAID, Workflow: orderPb.OrderWorkflow_NO_OP, Amount: moneyPb.AmountINR(26000).GetPb()},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
								PiFrom:          "pi-from-1",
								DedupeId: &paymentPb.DedupeId{
									CbsId:   "cbs-id",
									TxnTime: timestampPb.New(currTimeTimeStamp.AsTime().Add(-conf.SalaryTxnVerifierConfig.MaxAllowedDurationSinceTxnTimeToFetchRemitterInfoFromVg)),
								},
								CreatedAt: timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(context.Background(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-related-ontology-id",
							},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "to-actor-1",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id-1",
					Type:     types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(context.Background(), &empPb.GetEmployerOfUserRequest{ActorId: "to-actor-1"}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							PossibleRemitterNames:          []string{"remitter-name-1", "remitter-name-2"},
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusOk(),
					EmployerPiMapping: &empPb.EmployerPiMapping{
						PiId:       "pi-from-1",
						EmployerId: "random-id",
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "remitter-name-x",
							},
						},
						VerifiedName: "remitter-name-2",
					},
				}, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(2), salaryPb.SortOrder_ASC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: timestampBeforeOneMonthFromNow,
					},
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockSalaryTxnVerReqDao.EXPECT().GetSalaryTxnVerificationRequestsPaginated(context.Background(), "", []string{toActorId}, nil, salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED, nil, salaryPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED, nil, uint32(1), salaryPb.SortOrder_DESC, nil, nil, nil).Return([]*salaryPb.SalaryTxnVerificationRequest{
					{
						TxnTimestamp: currTimeTimeStamp,
					},
				}, nil, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).Return(&employernamecategoriser.EmployerNameCategoriserResponse{
					Status:   rpc.StatusOk(),
					Decision: 1,
				}, nil).Times(2)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						if req.GetName_1() == req.GetName_2() {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
							}, nil
						} else {
							return &employernamematchVgPb.EmployerNameMatchResponse{
								Status:   rpc.StatusOk(),
								Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_FAIL,
							}, nil
						}
					}).Times(2)

			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return true for B2B employee without checking if user is new to salary program",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							ToActorId:   toActorId,
							FromActorId: fromActorId,
							Status:      orderPb.OrderStatus_PAID,
							Workflow:    orderPb.OrderWorkflow_NO_OP,
							Amount:      moneyPb.AmountINR(25000).GetPb(),
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
								CreatedAt:       timestampPb.Now(),
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				// Mock employer info with B2B channel
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(gomock.Any(), &empPb.GetEmployerOfUserRequest{ActorId: toActorId}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-1",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
							SalaryProgramChannel:           employmentPb.EmployerSalaryProgramChannel_B2B,
							PossibleRemitterNames:          []string{userDeclaredEmployerName},
							NameBySource:                   userDeclaredEmployerName,
						},
					},
				}, nil)

				// Mock txn categorizer for non-income check
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      toActorId,
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{
								OntologyId: "income-ontology-id",
							},
						},
					},
				}, nil)

				// Mock actor client for beneficiary
				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: toActorId,
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpc.StatusOk(),
					Type:   types.ActorType_USER,
				}, nil)

				// Mock actor client for remitter
				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				// Mock PI client
				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{
					Id: "pi-from-1",
				}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						VerifiedName: userDeclaredEmployerName,
					},
				}, nil)

				// Mock employer PI mapping check
				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(gomock.Any(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				// Mock employer name categorizer - use AnyTimes() with more flexible parameters
				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamecategoriser.EmployerNameCategoriserRequest, opts ...interface{}) (*employernamecategoriser.EmployerNameCategoriserResponse, error) {
						return &employernamecategoriser.EmployerNameCategoriserResponse{
							Status:   rpc.StatusOk(),
							Decision: 1,
						}, nil
					}).AnyTimes()

				// Mock employer name match - use AnyTimes() with more flexible parameters
				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						return &employernamematchVgPb.EmployerNameMatchResponse{
							Status:   rpc.StatusOk(),
							Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
						}, nil
					}).AnyTimes()
			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-1",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
		},
		{
			name: "should return true for B2B employer and PI in bypass list",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-bypass",
							ToActorId:   toActorId,
							FromActorId: fromActorId,
							Status:      orderPb.OrderStatus_PAID,
							Workflow:    orderPb.OrderWorkflow_NO_OP,
							Amount:      moneyPb.AmountINR(30000).GetPb(),
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-bypass-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "bypass-pi-1",
								CreatedAt:       currTimeTimeStamp,
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {
				// Mock employer info with B2B channel
				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(gomock.Any(), &empPb.GetEmployerOfUserRequest{ActorId: toActorId}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     "emp-id-bypass",
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
							SalaryProgramChannel:           employmentPb.EmployerSalaryProgramChannel_B2B,
						},
					},
				}, nil)

				// Mock txn categorizer for income check
				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      toActorId,
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-bypass-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{OntologyId: "income-ontology-id"},
						},
					},
				}, nil)

				// Mock actor client for beneficiary
				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: toActorId,
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpc.StatusOk(),
					Type:   types.ActorType_USER,
				}, nil)
			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId: "emp-id-bypass",
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_BYPASS_PAYMENT_INSTRUMENT_ID_MATCH,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_ID_MATCH,
					},
				},
			},
		},
		{
			name: "should prioritize payment instrument verified name over account name",
			args: args{
				ctx: context.Background(),
				salaryTaxVerifierParams: SalaryTxnVerifierParams{
					OrderWithTxns: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-id-1",
							ToActorId:   "to-actor-1",
							FromActorId: "from-actor-1",
							Status:      orderPb.OrderStatus_PAID,
							Workflow:    orderPb.OrderWorkflow_NO_OP,
							Amount:      moneyPb.AmountINR(30000).GetPb(),
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-id-1",
								PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
								PiFrom:          "pi-from-1",
							},
						},
					},
				},
			},
			setupMocks: func(mocks *mocksStruct) {

				mocks.mockTxnCatClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), &categorizerPb.GetTxnCategoryDetailsRequest{
					ActorId:      "to-actor-1",
					Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: "txn-id-1"},
					Provenance:   categorizerPb.Provenance_DS,
					DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
				}).Return(&categorizerPb.GetTxnCategoryDetailsResponse{
					Status: rpc.StatusOk(),
					TxnCategories: &categorizerPb.TransactionCategories{
						Ontologies: []*categorizerPb.OntologyDetails{
							{OntologyId: "income-ontology-id"},
						},
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: toActorId,
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpc.StatusOk(),
					Type:   types.ActorType_USER,
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployerOfUser(gomock.Any(), &empPb.GetEmployerOfUserRequest{
					ActorId: "to-actor-1",
				}).Return(&empPb.GetEmployerOfUserResponse{
					Status: rpc.StatusOk(),
					EmploymentInfo: &empPb.GetEmployerOfUserResponse_EmployerInfo{
						EmployerInfo: &empPb.EmployerInfo{
							EmployerId:                     employerDbEmployerId,
							IsVerified:                     true,
							SalaryProgramEligibilityStatus: employmentPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
							SalaryProgramChannel:           employmentPb.EmployerSalaryProgramChannel_B2B,
						},
					},
				}, nil)

				mocks.mockEmpClient.EXPECT().GetEmployer(gomock.Any(), &empPb.GetEmployerRequest{
					Identifier: &employmentPb.GetEmployerRequest_EmployerId{
						EmployerId: employerDbEmployerId,
					},
				}).Return(&empPb.GetEmployerResponse{
					Status: rpc.StatusOk(),
					EmployerInfo: &empPb.EmployerInfo{
						EmployerId:                     employerDbEmployerId,
						SalaryProgramEligibilityStatus: empPb.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
					},
				}, nil)

				mocks.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: fromActorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Name: fromActorName,
					},
				}, nil)

				mocks.mockEmployerNameCategoriserClient.EXPECT().EmployerNameCategoriser(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamecategoriser.EmployerNameCategoriserRequest, opts ...interface{}) (*employernamecategoriser.EmployerNameCategoriserResponse, error) {
						return &employernamecategoriser.EmployerNameCategoriserResponse{
							Status:   rpc.StatusOk(),
							Decision: 1,
						}, nil
					}).AnyTimes()

				mocks.mockEmpClient.EXPECT().SearchCompanyV2(context.Background(), &empPb.SearchCompanyRequestV2{
					SearchString: "verified-employer-name",
					Provenance:   empPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
				}).Return(&empPb.SearchCompanyResponseV2{
					Status: rpc.StatusOk(),
					Companies: []*empPb.EmployerInfo{
						{
							NameBySource: "verified-employer-name",
							EmployerId:   employerDbEmployerId,
						},
					},
				}, nil)

				mocks.mockEmployerNameMatchClient.EXPECT().EmployerNameMatch(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *employernamematchVgPb.EmployerNameMatchRequest, opts ...interface{}) (*employernamematchVgPb.EmployerNameMatchResponse, error) {
						return &employernamematchVgPb.EmployerNameMatchResponse{
							Status:   rpc.StatusOk(),
							Decision: namecheckVgPb.NameMatchDecision_NAMEMATCH_PASS,
						}, nil
					}).AnyTimes()

				mocks.mockEmpClient.EXPECT().GetEmployerPiMappingByPiId(context.Background(), &empPb.GetEmployerPiMappingByPiIdRequest{
					PiId: "pi-from-1",
				}).Return(&empPb.GetEmployerPiMappingByPiIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mocks.mockPiClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: "pi-from-1"}).Return(&piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								Name: "account-name",
							},
						},
						VerifiedName: "verified-employer-name",
					},
				}, nil)
			},
			wantVerificationResult: true,
			wantVerificationMetadata: &VerificationMetadata{
				SalaryTxnEmployerId:                  employerDbEmployerId,
				RemitterMatchesWithOtherEmployerInDb: true,
				AdditionalMeta: &salaryPb.SalaryAutoVerifierMeta{
					VerificationSuccessMeta: &salaryPb.SalaryAutoVerifierMeta_VerificationSuccessMeta{
						FieldUsedForTxnRemitterName:    salaryPb.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
						EmployerMatchedWithTxnRemitter: salaryPb.SalaryAutoVerifierMeta_EMPLOYER_FROM_EMPLOYER_DB,
						RemitterToEmployerMatchLogic:   salaryPb.SalaryAutoVerifierMeta_DS_NAME_MATCH,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init daoMocks
			mockTxnCatClient := categorizerMocksPb.NewMockTxnCategorizerClient(ctr)
			mockActorClient := actorMocks.NewMockActorClient(ctr)
			mockPiClient := piMocks.NewMockPiClient(ctr)
			mockEmpClient := employmentMocks.NewMockEmploymentClient(ctr)
			mockEmployerNameMatchClient := employernamematchMocksPb.NewMockEmployerNameMatchClient(ctr)
			mockVgPaymentClient := vgPaymentMocks.NewMockPaymentClient(ctr)
			mockMinReqSalaryAmtGetter := minreqsalaryamountMocks.NewMockIMinReqSalaryAmountGetter(ctr)
			mockVgEmploymentClient := mocksVgEmp.NewMockEmploymentClient(ctr)
			mockEmployerVerifier := mock_employerverifier.NewMockISalaryEmployerVerifier(ctr)
			mockUsersClient := userMocks.NewMockUsersClient(ctr)
			mockSalaryTxnVerReqDao := daoMocks.NewMockISalaryTxnVerificationRequestDao(ctr)

			// mocking mockMinReqSalaryAmtGetter to return 25k as min required salary txn amount.
			mockMinReqSalaryAmtGetter.EXPECT().GetMinReqAmount(context.Background(), gomock.Any()).Return(moneyPb.AmountINR(25000).GetPb(), nil).AnyTimes()
			mockEmployerNameCategoriserClient := employernamecategoriserMocksPb.NewMockEmployerNameCategoriserClient(ctr)

			mocks := &mocksStruct{
				mockTxnCatClient:                  mockTxnCatClient,
				mockActorClient:                   mockActorClient,
				mockPiClient:                      mockPiClient,
				mockEmpClient:                     mockEmpClient,
				mockEmployerNameMatchClient:       mockEmployerNameMatchClient,
				vgPaymentClient:                   mockVgPaymentClient,
				employerVerifier:                  mockEmployerVerifier,
				vgEmploymentClient:                mockVgEmploymentClient,
				usersClient:                       mockUsersClient,
				mockEmployerNameCategoriserClient: mockEmployerNameCategoriserClient,
				mockSalaryTxnVerReqDao:            mockSalaryTxnVerReqDao,
			}

			tt.setupMocks(mocks)

			// set of pi ids fro which salary verification is bypassed
			conf.BypassSalaryVerificationChecksForPIs = []string{"bypass-pi-1"}

			s := NewSalaryTxnVerifierV1(mockTxnCatClient, mockActorClient, mockPiClient, mockEmpClient, mockMinReqSalaryAmtGetter, mockEmployerNameMatchClient, mockVgPaymentClient, mockEmployerVerifier, mockVgEmploymentClient, mockUsersClient, conf, mockEmployerNameCategoriserClient, mockSalaryTxnVerReqDao)
			gotVerificationResult, gotVerificationMetadata, err := s.IsSalaryTxn(tt.args.ctx, tt.args.salaryTaxVerifierParams)

			if (err != nil) != tt.wantErr {
				t.Errorf("IsSalaryTxn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("IsSalaryTxn() gotErrType = %v, wantErrType %v", err.Error(), tt.wantErrType.Error())
				return
			}
			if gotVerificationResult != tt.wantVerificationResult {
				t.Errorf("IsSalaryTxn() gotVerificationResult = %v, wantVerificationResult %v", gotVerificationResult, tt.wantVerificationResult)
				return
			}
			if !isSalaryVerificationMetadataEqual(gotVerificationMetadata, tt.wantVerificationMetadata) {
				t.Errorf("IsSalaryTxn() \n got = %+v\nwant = %+v", gotVerificationMetadata, tt.wantVerificationMetadata)
			}
		})
	}
}

func isSalaryVerificationMetadataEqual(got, want *VerificationMetadata) bool {
	if got == nil && want == nil {
		return true
	}
	if got == nil || want == nil {
		return false
	}

	got.MarshalledDsNameMatchRpcReqResList = want.MarshalledDsNameMatchRpcReqResList

	if !proto.Equal(got.AdditionalMeta, want.AdditionalMeta) {
		return false
	} else {
		got.AdditionalMeta = want.AdditionalMeta
	}

	return reflect.DeepEqual(got, want)
}
