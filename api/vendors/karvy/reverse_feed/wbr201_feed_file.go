package reverse_feed

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

const (
	DDMMYYYYLayout         = "02/01/2006"
	altDDMMYYYYLayout      = "02-01-2006"
	mmddyyyyhhmmssPMLayout = "1/2/2006 15:04:05 PM"
	mmddyyyyhhmmssAMLayout = "1/2/2006 11:04:05 AM"
	mmddyyyyhhmmssLayout   = "1/2/2006 15:04:05"
)

// parseDateTimeWithMultipleFormats tries to parse a date string with multiple layouts as fallbacks
func parseDateTimeWithMultipleFormats(dateStr string) (time.Time, error) {
	// List of layouts to try in order of preference
	layouts := []string{
		mmddyyyyhhmmssPMLayout,   // "1/2/2006 15:04:05 PM"
		mmddyyyyhhmmssAMLayout,   // "1/2/2006 15:04:05 AM"
		mmddyyyyhhmmssLayout,     // "1/2/2006 15:04:05" (24-hour format)
		altDDMMYYYYLayout,        // "02-01-2006"
		DDMMYYYYLayout,           // "02/01/2006"
		"1/2/2006 3:04 PM",       // 12-hour format without seconds
		"1/2/2006 3:04 AM",       // AM format without seconds
		"01/02/2006 3:04 PM",     // Zero Padded format without seconds
		"01/02/2006 3:04 AM",     // Zero Padded AM format without seconds
		"1/2/2006 3:04:05 PM",    // Single digit month/day with PM
		"1/2/2006 3:04:05 AM",    // Single digit month/day with AM
		"1/2/2006 09:04:05 AM",   // "1/2/2006 15:04:05 AM"
		"1/2/2006 3:04:05",       // Single digit month/day 24-hour
		"7/2/2006 12:00 AM",      // Specific format: "7/21/2025 12:00 AM"
		"1/21/2006 12:00 PM",     // Specific format: "7/21/2025 12:00 PM"
		"7/21/2006 15:04:05 PM",  // Zero-padded with PM
		"01/02/2006 09:04:05 AM", // Zero-padded with AM
		"01/02/2006 15:04:05",    // Zero-padded 24-hour
		"01/02/2006 3:04:05 PM",  // Zero-padded month/day with PM
		"01/02/2006 3:04:05 AM",  // Zero-padded month/day with AM
		"01/02/2006 3:04:05",     // Zero-padded month/day 24-hour
		"01/02/2006 12:00 AM",    // Zero-padded specific format
		"01/02/2006 12:00 PM",    // Zero-padded specific format
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, dateStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("cannot parse date with any supported format: %s", dateStr)
}

// WBR33File struct represents an individual row present in the reverse feed file of WBR201 format
type WBR201File struct {
	PRODCODE       string
	AmcCode        string
	FolioNo        string
	DIVOPT         string
	SCHEME         string
	TRXNNO         string
	InvName        string
	TRXNMODE       string
	TRXNSTAT       string
	POSTDATE       string
	UNITS          string
	Amount         string
	BROKCODE       string
	SUBBROK        string
	RepDate        string
	APPLICATION    string
	TrxnNature     string
	TRXNTYPE       string
	TRADDATE       string
	SchemeType     string
	TRXNSUBTYPE    string
	TerLocation    string
	EUIN           string
	TrxnCharge     string
	ClientID       string
	DPID           string
	STT            string
	USRTRXNOIhno   string
	USERCODE       string
	USRTRXNOInward string
	PAN            string
	TotalTDS       string
	LOAD           string
	TaxStatus      string
	NAV            string
	EuinValid      string
	EuinOpted      string
	SubBrkArn      string
	SysRegnDate    string
	SIPTRXNNO      string
	CAN            string
	StampDuty      string
	TRFlag         string
	ALTFolio       string
	FTACCNO        string
	REJTRNOOR2     string
	PURPRICE       string
	CHQNO          string
	TargSrcS       string
	ReversalC      string
	ExchangeF      string
	Remarks        string
}

var WBR201FieldMap = map[int]string{
	0:  "PRODCODE",
	1:  "AmcCode",
	2:  "FolioNo",
	3:  "DIVOPT",
	4:  "SCHEME",
	5:  "TRXNNO",
	6:  "InvName",
	7:  "TRXNMODE",
	8:  "TRXNSTAT",
	9:  "POSTDATE",
	10: "UNITS",
	11: "Amount",
	12: "BROKCODE",
	13: "SUBBROK",
	14: "RepDate",
	15: "APPLICATION",
	16: "TrxnNature",
	17: "TRXNTYPE",
	18: "TRADDATE",
	19: "SchemeType",
	20: "TRXNSUBTYPE",
	21: "TerLocation",
	22: "EUIN",
	23: "TrxnCharge",
	24: "ClientID",
	25: "DPID",
	26: "STT",
	27: "USRTRXNOIhno",
	28: "USERCODE",
	29: "USRTRXNOInward",
	30: "PAN",
	31: "TotalTDS",
	32: "LOAD",
	33: "TaxStatus",
	34: "NAV",
	35: "EuinValid",
	36: "EuinOpted",
	37: "SubBrkArn",
	38: "SysRegnDate",
	39: "SIPTRXNNO",
	40: "CAN",
	41: "StampDuty",
	42: "TRFlag",
	43: "ALTFolio",
	44: "FTACCNO",
	45: "REJTRNOOR2",
	46: "PURPRICE",
	47: "CHQNO",
	48: "TargSrcS",
	49: "ReversalC",
	50: "ExchangeF",
	51: "Remarks",
}

func ParseToMoney(amount string) (*moneyPb.Money, error) {
	amt, err := money.ParseString(amount, "")
	if err != nil {
		return nil, err
	}
	return amt, nil
}

func ParseWBR201File(ctx context.Context, fileContent []byte) ([]*WBR201File, error) {
	// first line contains column names, ignoring them.
	fileContent = []byte(strings.Join(strings.Split(string(fileContent), "\n")[1:], "\n"))

	reader := csv.NewReader(bytes.NewReader(fileContent))
	reader.Comma = ','
	feedRes, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "error while reading feed file")
	}
	// iterate over every line and parse it
	var wbr201FeedFiles []*WBR201File
	for _, feed := range feedRes {
		wbr201file := WBR201File{}
		for keyIdx, value := range feed {
			// The actual wbr201 files has some additional legacy columns which are present in the template. These columns can be ignored.
			if keyIdx >= len(WBR201FieldMap) {
				break
			}
			keyName, keyOk := WBR201FieldMap[keyIdx]
			if !keyOk {
				logger.ErrorNoCtx(fmt.Sprintf("error while fetching key name using index, keyIdx: %v", keyIdx))
				continue
			}
			// setting the value as string, parsing to apt data type will be done during sending out request to order manager for updates.
			// TODO(ismail): the following line panics if we try set a key which is not present in the struct, for now the FieldMap and Struct keys need to be in sync.
			reflect.ValueOf(&wbr201file).Elem().FieldByName(keyName).SetString(value)
		}
		// ToDo(Junaid): Remove this log
		logger.Info(ctx, "value of struct", zap.Any("struct", wbr201file), zap.String(logger.ORDER_ID, wbr201file.USRTRXNOInward))
		wbr201FeedFiles = append(wbr201FeedFiles, &wbr201file)
	}
	return wbr201FeedFiles, nil

}

func AggregateWBR201File(ctx context.Context, wbr201Rows []*WBR201File) ([]*WBR201File, map[string]string, error) {
	aggregatedMap := make(map[string]*WBR201File)
	parsingFailedOrderIDs := make(map[string]string)
	var err error
	for _, row := range wbr201Rows {
		// USRTRXNOInward per USERCODE should be grouped as USRTRXNOInward(VendorOrderID) can get duplicated among different UserCodes.

		vendorOrderIDUserCode := row.USERCODE + "_" + row.USRTRXNOInward
		if len(row.USERCODE) == 0 || len(row.USRTRXNOInward) == 0 {
			logger.Error(ctx, fmt.Sprintf("invalid user code: %s or venodrOrderID: %s", row.USERCODE, row.USRTRXNOInward))
			continue
		}

		_, ok := parsingFailedOrderIDs[vendorOrderIDUserCode]
		if ok {
			continue
		}

		_, ok = aggregatedMap[vendorOrderIDUserCode]
		if ok {
			aggregatedMap[vendorOrderIDUserCode], err = aggregateWBR201Entries(ctx, aggregatedMap[vendorOrderIDUserCode], row)
			if err != nil {
				logger.Error(ctx, "error in aggregateWBR201Entries", zap.Error(err), zap.String(logger.VENDOR_ORDER_ID, row.USRTRXNOInward))
				parsingFailedOrderIDs[vendorOrderIDUserCode] = err.Error()
			}
		} else {
			aggregatedMap[vendorOrderIDUserCode] = row
		}
	}

	var aggregatedList []*WBR201File
	for key, value := range aggregatedMap {
		_, ok := parsingFailedOrderIDs[key]
		if ok {
			continue
		}

		aggregatedList = append(aggregatedList, value)
	}

	return aggregatedList, parsingFailedOrderIDs, nil

}

//nolint:funlen
func aggregateWBR201Entries(ctx context.Context, entry1 *WBR201File, entry2 *WBR201File) (*WBR201File, error) {
	units1, err := strconv.ParseFloat(entry1.UNITS, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing feed units", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry1.USRTRXNOInward))
		return nil, err
	}
	units2, err := strconv.ParseFloat(entry2.UNITS, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing feed units", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry2.USRTRXNOInward))
		return nil, err
	}

	amount1, err := strconv.ParseFloat(entry1.Amount, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing amount", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry1.USRTRXNOInward))
		return nil, err
	}
	amount2, err := strconv.ParseFloat(entry2.Amount, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing units", zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, entry2.USRTRXNOInward))
		return nil, err
	}

	transactionCharges1 := ParseFloat(ctx, entry1.TrxnCharge, "TrxnCharge", entry1.USRTRXNOInward)
	transactionCharges2 := ParseFloat(ctx, entry2.TrxnCharge, "TrxnCharge", entry2.USRTRXNOInward)

	stt1 := ParseFloat(ctx, entry1.STT, "STT", entry1.USRTRXNOInward)
	stt2 := ParseFloat(ctx, entry2.STT, "STT", entry2.USRTRXNOInward)

	totalTDS1 := ParseFloat(ctx, entry1.TotalTDS, "totalTDS", entry1.USRTRXNOInward)
	totalTDS2 := ParseFloat(ctx, entry2.TotalTDS, "totalTDS", entry2.USRTRXNOInward)

	entryLoad1 := ParseFloat(ctx, entry1.LOAD, "LOAD", entry1.USRTRXNOInward)
	entryLoad2 := ParseFloat(ctx, entry2.LOAD, "LOAD", entry2.USRTRXNOInward)

	stampDuty1 := ParseFloat(ctx, entry1.StampDuty, "StampDuty", entry1.USRTRXNOInward)
	stampDuty2 := ParseFloat(ctx, entry2.StampDuty, "StampDuty", entry2.USRTRXNOInward)

	var aggregatedEntry WBR201File
	err = copier.Copy(&aggregatedEntry, entry1)
	if err != nil {
		return nil, err
	}

	aggregatedEntry.UNITS = fmt.Sprintf("%v", units1+units2)
	aggregatedEntry.Amount = fmt.Sprintf("%v", amount1+amount2)
	aggregatedEntry.TrxnCharge = fmt.Sprintf("%v", transactionCharges1+transactionCharges2)
	aggregatedEntry.TotalTDS = fmt.Sprintf("%v", totalTDS1+totalTDS2)
	aggregatedEntry.STT = fmt.Sprintf("%v", stt1+stt2)
	aggregatedEntry.LOAD = fmt.Sprintf("%v", entryLoad1+entryLoad2)
	aggregatedEntry.StampDuty = fmt.Sprintf("%v", stampDuty1+stampDuty2)
	return &aggregatedEntry, nil

}

func ParseFloat(ctx context.Context, value string, param string, vendorOrderId string) float64 {
	if len(value) == 0 {
		return 0.0
	}

	parsedValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while parsing %s", param), zap.Error(err), zap.String("file_type", "wbr2"),
			zap.String(logger.VENDOR_ORDER_ID, vendorOrderId))
		return 0.0
	}
	return parsedValue
}

//nolint:funlen
func ConvertWBR201ToMetaData(wbr201Feed *WBR201File) (*pb.OrderConfirmationMetadata, error) {
	var err error
	allotmentDate, err := parseDateTimeWithMultipleFormats(wbr201Feed.TRADDATE)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing trade date")
	}
	orderDate, err := parseDateTimeWithMultipleFormats(wbr201Feed.POSTDATE)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing post date")
	}

	dataFeedDate, err := parseDateTimeWithMultipleFormats(wbr201Feed.RepDate)
	if err != nil {
		return nil, errors.Wrap(err, "error while parsing date of the data feed")
	}

	dataFeedTime := time.Now()

	dataFeedDate.Add(time.Hour * time.Duration(dataFeedTime.Hour()))
	dataFeedDate.Add(time.Minute * time.Duration(dataFeedTime.Minute()))
	dataFeedDate.Add(time.Second * time.Duration(dataFeedTime.Second()))

	var tax *moneyPb.Money = nil
	if len(wbr201Feed.TotalTDS) > 0 {
		tax, err = ParseToMoney(wbr201Feed.TotalTDS)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing tax")
		}
	}

	var stt *moneyPb.Money = nil
	if len(wbr201Feed.STT) > 0 {
		stt, err = ParseToMoney(wbr201Feed.STT)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing security transaction tax")
		}
	}

	var transactionCharges *moneyPb.Money = nil
	if len(wbr201Feed.TrxnCharge) > 0 {
		transactionCharges, err = ParseToMoney(wbr201Feed.TrxnCharge)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing transaction charges")
		}
	}

	var entryLoad *moneyPb.Money = nil
	if len(wbr201Feed.LOAD) > 0 {
		entryLoad, err = ParseToMoney(wbr201Feed.LOAD)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing entry load")
		}
	}

	var stampDuty *moneyPb.Money = nil
	if len(wbr201Feed.StampDuty) > 0 {
		stampDuty, err = ParseToMoney(wbr201Feed.StampDuty)
		if err != nil {
			return nil, errors.Wrap(err, "error while parsing stamp duty")
		}
	}

	orderConfirmationMetaData := &pb.OrderConfirmationMetadata{
		TransactionNumber:      wbr201Feed.TRXNNO,
		SourceCode:             wbr201Feed.USERCODE,
		AllotmentDate:          timestampPb.New(allotmentDate),
		OrderDate:              timestampPb.New(orderDate),
		DataFeedTime:           timestampPb.New(dataFeedDate),
		TransactionType:        wbr201Feed.TrxnNature,
		Tax:                    tax,
		SecurityTransactionTax: stt,
		SchemeType:             wbr201Feed.SchemeType,
		TaxStatus:              wbr201Feed.TaxStatus,
		EntryLoad:              entryLoad,
		ReversalReason:         wbr201Feed.Remarks,
		TransactionCharges:     transactionCharges,
		StampDuty:              stampDuty,
	}
	return orderConfirmationMetaData, nil
}

// FilterExternalAndInternalOrders :This function takes in a list of entries present in wbr2 and splits them into
//
//	internal and external orders. Internal orders are orders placed via fi app and
//	external orders are order placed via an external platform using a folioId that
//	was created by fi. If the sourceCode(same as UserCode) present fot the entry is
//	same as the one used by FI, then it means that, the order
//	was created by fi systems. But, if it is not the same, then it means that the
//	order was placed by a folio number created at fi in other external platforms/apps.
func FilterExternalAndInternalOrders(ctx context.Context, wbr201Entries []*WBR201File, fiUserCode string) ([]*WBR201File, map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File, map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File, map[string]string) {
	var internallyPlacedOrders []*WBR201File
	externallyPlacedSellOrders := make(map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File)
	externallyPlacedBuyOrders := make(map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File)

	failedOrderTypeDetection := make(map[string]string)
	for _, wbr201FeedEntry := range wbr201Entries {
		if strings.Compare(fiUserCode, wbr201FeedEntry.USERCODE) == 0 {
			internallyPlacedOrders = append(internallyPlacedOrders, wbr201FeedEntry)
			continue
		}

		amc, err := invPkg.GetAmcFromCode(wbr201FeedEntry.AmcCode)
		if err != nil {
			logger.Error(ctx, wbr201FeedEntry.AmcCode)
			logger.Error(ctx, "AMC code not supported", zap.String(logger.MF_AMC, wbr201FeedEntry.AmcCode))
			continue
		}

		_, ok := externallyPlacedBuyOrders[amc]
		if !ok {
			externallyPlacedBuyOrders[amc] = make(map[pb.OrderSubType][]*WBR201File)
		}

		_, ok2 := externallyPlacedSellOrders[amc]
		if !ok2 {
			externallyPlacedSellOrders[amc] = make(map[pb.OrderSubType][]*WBR201File)
		}

		if IsBuyOrderSuccessful(wbr201FeedEntry.TRXNTYPE) {
			externallyPlacedBuyOrders[amc][pb.OrderSubType_BUY_EXTERNAL] = append(externallyPlacedBuyOrders[amc][pb.OrderSubType_BUY_EXTERNAL], wbr201FeedEntry)
			continue
		}
		if IsSellOrderSuccessful(wbr201FeedEntry.TRXNTYPE) {
			externallyPlacedSellOrders[amc][pb.OrderSubType_SELL_EXTERNAL] = append(externallyPlacedSellOrders[amc][pb.OrderSubType_SELL_EXTERNAL], wbr201FeedEntry)
			continue
		}
		failedOrderTypeDetection[wbr201FeedEntry.USRTRXNOInward] = fmt.Sprintf("transaction type: %s not recongnised for order, userCode: %s", wbr201FeedEntry.TRXNTYPE, wbr201FeedEntry.USERCODE)
	}
	return internallyPlacedOrders, externallyPlacedBuyOrders, externallyPlacedSellOrders, failedOrderTypeDetection
}

func FilterSwitchOrders(ctx context.Context, wbr201Entries []*WBR201File, fiUserCode string) ([]*WBR201File, map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File, []*WBR201File) {

	var internalSwitchOrders []*WBR201File
	var filteredWbr201Entries []*WBR201File
	externalSwitchOrders := make(map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File)

	for _, val := range wbr201Entries {
		if IsSwitchInOrderSuccessful(val.TRXNTYPE) || IsSwitchOutOrderSuccessful(val.TRXNTYPE) {
			if strings.Compare(fiUserCode, val.USERCODE) == 0 {
				internalSwitchOrders = append(internalSwitchOrders, val)
				continue
			}
			amc, err := invPkg.GetAmcFromCode(val.AmcCode)
			if err != nil {
				logger.Error(ctx, "AMC code not supported", zap.String(logger.MF_AMC, val.AmcCode))
				continue
			}

			_, ok3 := externalSwitchOrders[amc]
			if !ok3 {
				externalSwitchOrders[amc] = make(map[pb.OrderSubType][]*WBR201File)
			}

			if IsSwitchInOrderSuccessful(val.TRXNTYPE) {
				externalSwitchOrders[amc][pb.OrderSubType_SWITCH_IN_EXTERNAL] = append(externalSwitchOrders[amc][pb.OrderSubType_SWITCH_IN_EXTERNAL], val)
				continue
			}
			if IsSwitchOutOrderSuccessful(val.TRXNTYPE) {
				externalSwitchOrders[amc][pb.OrderSubType_SWITCH_OUT_EXTERNAL] = append(externalSwitchOrders[amc][pb.OrderSubType_SWITCH_OUT_EXTERNAL], val)
				continue
			}
		} else {
			filteredWbr201Entries = append(filteredWbr201Entries, val)
		}
	}

	return internalSwitchOrders, externalSwitchOrders, filteredWbr201Entries

}

func IsBuyOrderSuccessful(code string) bool {
	if strings.Compare(strings.ToUpper(code), "ADD") == 0 ||
		strings.Compare(strings.ToUpper(code), "NEW") == 0 ||
		strings.Compare(strings.ToUpper(code), "SIN") == 0 {
		return true
	}
	return false
}

func IsSellOrderSuccessful(code string) bool {
	if strings.Compare(strings.ToUpper(code), "RED") == 0 || // RED represents Redemption
		strings.Compare(strings.ToUpper(code), "FUL") == 0 { // FUL represents Redemption
		return true
	}
	return false
}

func IsSwitchInOrderSuccessful(code string) bool {
	return strings.Compare(strings.ToUpper(code), "SWIN") == 0
}

func IsSwitchOutOrderSuccessful(code string) bool {
	return strings.Compare(strings.ToUpper(code), "SWOF") == 0
}

// SplitOrderByCategories takes in WBR201File and splits orders into the following categories.
// internallyPlacedOrders, externallyPlacedBuyOrders, externallyPlacedSellOrders, unspecifiedOrders, aggregationFailedOrders and internalSwitchOrders.
// Orders are split into these categories as they need to be processed seperately with different logic.
func SplitOrderByCategories(ctx context.Context, fileContent []byte, branchCode string, filePath string) (
	internallyPlacedOrders []*WBR201File,
	externallyPlacedBuyOrders map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File,
	externallyPlacedSellOrders map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File,
	unspecifiedOrders map[string]string,
	aggregationFailedOrders map[string]string,
	internalSwitchOrders []*WBR201File,
	externalSwitchOrders map[mfPb.Amc]map[pb.OrderSubType][]*WBR201File,
	err error) {
	// Parse reverse feed file into a list of WBR201 objects
	wbr201FeedFiles, err := ParseWBR201File(ctx, fileContent)
	if err != nil {
		logger.Error(ctx, "error in ParseWBR201File", zap.String(logger.AWS_FILE_PATH, filePath), zap.Error(err))
		return nil, nil, nil, nil, nil, nil, nil, errors.Wrap(err, "error in ParseWBR201File")
	}

	logger.Info(ctx, fmt.Sprintf("number of entries in wbr201 file: %d", len(wbr201FeedFiles)), zap.String(logger.AWS_FILE_PATH, filePath))

	// Switch orders have certain edge cases because of which they need to filtered out and handled separately.
	internalSwitchOrders, externalSwitchOrders, filteredWbr201FeedFiles := FilterSwitchOrders(ctx, wbr201FeedFiles, branchCode)

	// For some orders, rta sometimes splits the order into multiple rows in the file.
	// For eg: 	If user places an order for ₹100 with vendor order id v1, then there could be 2 entries in the file
	// 			with ₹70 and ₹30 each. We don't know on what basis rta splits these order, but the sum of entries in
	//			this file for an order should be equivalent to order that user has placed for. So, we group the entries
	//			present in the file by (UserCode, VendorOrderID) and aggregate them so that these can be sent to as a
	//			single entry/order to orderManager service.
	wbr201FeedFiles, aggregationFailedOrders, err = AggregateWBR201File(ctx, filteredWbr201FeedFiles)
	if err != nil {
		logger.Error(ctx, "error in AggregateWBR201File", zap.String(logger.AWS_FILE_PATH, filePath), zap.Error(err))
		return nil, nil, nil, nil, nil, nil, nil, errors.Wrap(err, "error in AggregateWBR201File")
	}

	internallyPlacedOrders, externallyPlacedBuyOrders, externallyPlacedSellOrders, unspecifiedOrders = FilterExternalAndInternalOrders(ctx, wbr201FeedFiles, branchCode)

	return internallyPlacedOrders, externallyPlacedBuyOrders, externallyPlacedSellOrders, unspecifiedOrders, aggregationFailedOrders, internalSwitchOrders, externalSwitchOrders, err

}

func IsOrderRejected(code string) bool {
	if strings.Compare(strings.ToUpper(code), "REDD") == 0 || // REDD represents Redemption Rejection
		strings.Compare(strings.ToUpper(code), "ADDD") == 0 || // ADDD represents Additional Purchase Pre-Rejection
		strings.Compare(strings.ToUpper(code), "NEWD") == 0 || // NEWD represents New Purchase Pre-Rejection
		strings.Compare(strings.ToUpper(code), "NEWR") == 0 { // NEWD represents Purchase Rejection
		return true
	}
	return false
}

// GetBatchOrderRequest converts a feed chunk array to an OrderUpdate array
// It does it on a best effort basis while silently logging any errors occurred during the parsing of a feed file
//
//nolint:dupl, funlen
func GetBatchOrderRequest(ctx context.Context, feedFile []*WBR201File) ([]*pb.OrderConfirmationDetail, map[string]*pb.OrderRejectionMetaData, map[string]string, error) {
	var res []*pb.OrderConfirmationDetail
	orderRejectionInfoMap := make(map[string]*pb.OrderRejectionMetaData)

	failedVendorOrderIDToReasonMap := make(map[string]string)
	for _, feed := range feedFile {
		amt, err := ParseToMoney(feed.Amount)
		if err != nil {
			logger.Error(ctx, "error while parsing feed amount", zap.Error(err), zap.String("file_type", "wbr201"),
				zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = "error while parsing feed amount"
			continue
		}
		price, err := ParseToMoney(feed.NAV)
		if err != nil {
			logger.Error(ctx, "error while parsing feed price", zap.Error(err), zap.String("file_type", "wbr201"),
				zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = "error while parsing feed price"
			continue
		}
		units, err := strconv.ParseFloat(feed.UNITS, 64)
		if err != nil {
			logger.Error(ctx, "error while parsing feed units", zap.Error(err), zap.String("file_type", "wbr201"),
				zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = "error while parsing feed units"
			continue
		}

		isRejected := IsOrderRejected(feed.TRXNTYPE)
		if isRejected {
			orderRejectionInfoMap[feed.USRTRXNOInward] = &pb.OrderRejectionMetaData{
				RejectedAt:      feed.REJTRNOOR2,
				RejectionReason: feed.Remarks,
			}
			continue
		}

		if !money.IsPositive(amt) || units <= 0 {
			rejectionReason := fmt.Sprintf("atleast one of aggregated units: %v, amount: %v is not positive", units, amt)
			logger.Error(ctx, rejectionReason, zap.String("remarks", feed.Remarks),
				zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			orderRejectionInfoMap[feed.USRTRXNOInward] = &pb.OrderRejectionMetaData{
				RejectedAt:      feed.REJTRNOOR2,
				RejectionReason: rejectionReason,
			}
			continue
		}

		if !money.IsPositive(price) {
			rejectionReason := fmt.Sprintf("NAV present in feed is not positive. nav: %v ", price)
			logger.Error(ctx, rejectionReason, zap.String("remarks", feed.Remarks),
				zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = rejectionReason
			continue
		}

		amc, err := invPkg.GetAmcFromCode(feed.AmcCode)
		if err != nil {
			logger.Error(ctx, "error in GetAmcFromCode", zap.Error(err), zap.String(logger.MF_AMC, feed.AmcCode), zap.String("file_type", "wbr201"), zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = "error in  GetAmcFromCode" + err.Error()
			continue
		}

		orderType, err := getOrderType(feed)
		if err != nil {
			logger.Error(ctx, "error in getOrderType", zap.Error(err), zap.String(logger.MF_AMC, feed.AmcCode), zap.String("file_type", "wbr201"), zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
			failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = "error in getOrderType" + err.Error()
			continue
		}

		if ((feed.TRFlag == "P" || feed.TRFlag == "SI") && feed.TRXNMODE == "N") || ((feed.TRFlag == "R" || feed.TRFlag == "SO") && feed.TRXNMODE == "N") {
			orderConfirmationDetail, err2 := getOrderConfirmationDetail(feed, amt, price, units, amc, orderType)
			if err2 != nil {
				logger.Error(ctx, "error while parsing orderConfirmationMetaData", zap.Error(err2), zap.String("file_type", "wbr201"),
					zap.String(logger.VENDOR_ORDER_ID, feed.USRTRXNOInward))
				failedVendorOrderIDToReasonMap[feed.USRTRXNOInward] = "error while parsing orderConfirmationMetaData"
				continue
			}
			res = append(res, orderConfirmationDetail)
		} else if (feed.TRFlag == "R" && feed.TRXNMODE == "R") || (feed.TRFlag == "P" && feed.TRXNMODE == "R") {
			orderRejectionInfoMap[feed.USRTRXNOInward] = &pb.OrderRejectionMetaData{
				RejectedAt:      feed.REJTRNOOR2, // TODO satyam
				RejectionReason: feed.Remarks,
			}
		}
	}
	return res, orderRejectionInfoMap, failedVendorOrderIDToReasonMap, nil
}

func getOrderConfirmationDetail(feed *WBR201File, amt *moneyPb.Money, price *moneyPb.Money, units float64, amc mfPb.Amc, orderType pb.OrderType) (*pb.OrderConfirmationDetail, error) {
	orderConfirmationMetaData, err := ConvertWBR201ToMetaData(feed)
	if err != nil {
		return nil, err
	}

	if len(feed.PRODCODE) < len(feed.AmcCode) {
		return nil, fmt.Errorf("product code length is less than amc code")
	}

	// SchemeCode for karvy is the combination of suffix of product code and the dividendOptionType
	schemeCode := feed.PRODCODE[len(feed.AmcCode):] + feed.DIVOPT

	return &pb.OrderConfirmationDetail{
		VendorOrderId:             feed.USRTRXNOInward,
		Amount:                    amt,
		Nav:                       price,
		UnitsAllocated:            units,
		OrderConfirmationMetaData: orderConfirmationMetaData,
		FolioId:                   feed.FolioNo,
		UserCode:                  feed.USERCODE,

		// ToDo(Junaid): Earlier we were feed.TRXNNO here. But we figured out in production that karvy is duplicating the feed.TRXNNO for different orders.
		//				 USRTRXNOIhno is the unique identifier for karvy. So, using that as the transaction number. Back-fill for all older orders and
		//				 replace TRXNNO with USRTRXNOIhno using a jenkins job. (https://monorail.pointz.in/p/fi-app/issues/detail?id=27142)
		RtaTransactionNumber: feed.USRTRXNOIhno,
		OrderAllocatedDate:   datetime.TimestampToDateInLoc(orderConfirmationMetaData.GetAllotmentDate(), datetime.IST),
		SchemeCode:           schemeCode,
		Amc:                  amc,
		OrderType:            orderType,
		PostDate:             datetime.TimestampToDateInLoc(orderConfirmationMetaData.GetOrderDate(), datetime.IST),
	}, nil
}

func getOrderType(wbr201Feed *WBR201File) (pb.OrderType, error) {
	if IsBuyOrderSuccessful(wbr201Feed.TRXNTYPE) {
		return pb.OrderType_BUY, nil
	}
	if IsSellOrderSuccessful(wbr201Feed.TRXNTYPE) {
		return pb.OrderType_SELL, nil
	}
	return pb.OrderType_ORDER_TYPE_UNSPECIFIED, fmt.Errorf("unsupported transaction type code: %s", wbr201Feed.TRXNTYPE)
}
