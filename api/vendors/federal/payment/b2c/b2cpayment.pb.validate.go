// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/federal/payment/b2c/b2cpayment.proto

package b2c

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateB2CTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateB2CTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateB2CTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateB2CTransactionRequestMultiError, or nil if none found.
func (m *UpdateB2CTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateB2CTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for Utr

	// no validation rules for RemitterAccountNumber

	// no validation rules for BeneficiaryAccountNumber

	// no validation rules for CreditedAccountName

	// no validation rules for Amount

	// no validation rules for TransTimestamp

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	if len(errors) > 0 {
		return UpdateB2CTransactionRequestMultiError(errors)
	}

	return nil
}

// UpdateB2CTransactionRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateB2CTransactionRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateB2CTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateB2CTransactionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateB2CTransactionRequestMultiError) AllErrors() []error { return m }

// UpdateB2CTransactionRequestValidationError is the validation error returned
// by UpdateB2CTransactionRequest.Validate if the designated constraints
// aren't met.
type UpdateB2CTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateB2CTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateB2CTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateB2CTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateB2CTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateB2CTransactionRequestValidationError) ErrorName() string {
	return "UpdateB2CTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateB2CTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateB2CTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateB2CTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateB2CTransactionRequestValidationError{}

// Validate checks the field values on EnquireTransactionStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireTransactionStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireTransactionStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// EnquireTransactionStatusRequestMultiError, or nil if none found.
func (m *EnquireTransactionStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireTransactionStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Password

	// no validation rules for SenderCode

	// no validation rules for ReferenceId

	// no validation rules for OriginalReferenceId

	// no validation rules for TransactionType

	// no validation rules for TransactionId

	// no validation rules for TransactionDate

	if len(errors) > 0 {
		return EnquireTransactionStatusRequestMultiError(errors)
	}

	return nil
}

// EnquireTransactionStatusRequestMultiError is an error wrapping multiple
// validation errors returned by EnquireTransactionStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type EnquireTransactionStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireTransactionStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireTransactionStatusRequestMultiError) AllErrors() []error { return m }

// EnquireTransactionStatusRequestValidationError is the validation error
// returned by EnquireTransactionStatusRequest.Validate if the designated
// constraints aren't met.
type EnquireTransactionStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireTransactionStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireTransactionStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireTransactionStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireTransactionStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireTransactionStatusRequestValidationError) ErrorName() string {
	return "EnquireTransactionStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireTransactionStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireTransactionStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireTransactionStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireTransactionStatusRequestValidationError{}

// Validate checks the field values on EnquireTransactionStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EnquireTransactionStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireTransactionStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// EnquireTransactionStatusResponseMultiError, or nil if none found.
func (m *EnquireTransactionStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireTransactionStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReferenceId

	// no validation rules for OriginalReferenceId

	// no validation rules for TransactionDate

	// no validation rules for Response

	// no validation rules for Reason

	if all {
		switch v := interface{}(m.GetOriginalTxnDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireTransactionStatusResponseValidationError{
					field:  "OriginalTxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireTransactionStatusResponseValidationError{
					field:  "OriginalTxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOriginalTxnDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireTransactionStatusResponseValidationError{
				field:  "OriginalTxnDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireTransactionStatusResponseMultiError(errors)
	}

	return nil
}

// EnquireTransactionStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// EnquireTransactionStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type EnquireTransactionStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireTransactionStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireTransactionStatusResponseMultiError) AllErrors() []error { return m }

// EnquireTransactionStatusResponseValidationError is the validation error
// returned by EnquireTransactionStatusResponse.Validate if the designated
// constraints aren't met.
type EnquireTransactionStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireTransactionStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireTransactionStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireTransactionStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireTransactionStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireTransactionStatusResponseValidationError) ErrorName() string {
	return "EnquireTransactionStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireTransactionStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireTransactionStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireTransactionStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireTransactionStatusResponseValidationError{}

// Validate checks the field values on BalanceEnquiryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BalanceEnquiryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BalanceEnquiryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BalanceEnquiryRequestMultiError, or nil if none found.
func (m *BalanceEnquiryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BalanceEnquiryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Password

	// no validation rules for SenderCode

	// no validation rules for ReferenceId

	// no validation rules for AccountNumber

	if len(errors) > 0 {
		return BalanceEnquiryRequestMultiError(errors)
	}

	return nil
}

// BalanceEnquiryRequestMultiError is an error wrapping multiple validation
// errors returned by BalanceEnquiryRequest.ValidateAll() if the designated
// constraints aren't met.
type BalanceEnquiryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BalanceEnquiryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BalanceEnquiryRequestMultiError) AllErrors() []error { return m }

// BalanceEnquiryRequestValidationError is the validation error returned by
// BalanceEnquiryRequest.Validate if the designated constraints aren't met.
type BalanceEnquiryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BalanceEnquiryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BalanceEnquiryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BalanceEnquiryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BalanceEnquiryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BalanceEnquiryRequestValidationError) ErrorName() string {
	return "BalanceEnquiryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BalanceEnquiryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBalanceEnquiryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BalanceEnquiryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BalanceEnquiryRequestValidationError{}

// Validate checks the field values on BalanceEnquiryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BalanceEnquiryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BalanceEnquiryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BalanceEnquiryResponseMultiError, or nil if none found.
func (m *BalanceEnquiryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BalanceEnquiryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for ReferenceId

	// no validation rules for AccountNumber

	// no validation rules for CustomerName

	// no validation rules for AccountType

	// no validation rules for AccountStatus

	// no validation rules for LedgerBalance

	// no validation rules for AvailableBalance

	// no validation rules for FloatBalance

	// no validation rules for FfdBalance

	// no validation rules for UserDefinedBalance

	// no validation rules for BalanceCurrency

	// no validation rules for Response

	// no validation rules for Reason

	// no validation rules for CustId

	if len(errors) > 0 {
		return BalanceEnquiryResponseMultiError(errors)
	}

	return nil
}

// BalanceEnquiryResponseMultiError is an error wrapping multiple validation
// errors returned by BalanceEnquiryResponse.ValidateAll() if the designated
// constraints aren't met.
type BalanceEnquiryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BalanceEnquiryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BalanceEnquiryResponseMultiError) AllErrors() []error { return m }

// BalanceEnquiryResponseValidationError is the validation error returned by
// BalanceEnquiryResponse.Validate if the designated constraints aren't met.
type BalanceEnquiryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BalanceEnquiryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BalanceEnquiryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BalanceEnquiryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BalanceEnquiryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BalanceEnquiryResponseValidationError) ErrorName() string {
	return "BalanceEnquiryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BalanceEnquiryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBalanceEnquiryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BalanceEnquiryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BalanceEnquiryResponseValidationError{}

// Validate checks the field values on
// EnquireTransactionStatusResponse_OriginalTransactionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireTransactionStatusResponse_OriginalTransactionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EnquireTransactionStatusResponse_OriginalTransactionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireTransactionStatusResponse_OriginalTransactionDetailsMultiError, or
// nil if none found.
func (m *EnquireTransactionStatusResponse_OriginalTransactionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireTransactionStatusResponse_OriginalTransactionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Utr

	// no validation rules for RemitterAccountNumber

	// no validation rules for BeneficiaryAccountNumber

	// no validation rules for TransactionAmount

	// no validation rules for CreatedTime

	// no validation rules for ResponseCode

	// no validation rules for ResponseReason

	// no validation rules for ResponseAction

	if len(errors) > 0 {
		return EnquireTransactionStatusResponse_OriginalTransactionDetailsMultiError(errors)
	}

	return nil
}

// EnquireTransactionStatusResponse_OriginalTransactionDetailsMultiError is an
// error wrapping multiple validation errors returned by
// EnquireTransactionStatusResponse_OriginalTransactionDetails.ValidateAll()
// if the designated constraints aren't met.
type EnquireTransactionStatusResponse_OriginalTransactionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireTransactionStatusResponse_OriginalTransactionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireTransactionStatusResponse_OriginalTransactionDetailsMultiError) AllErrors() []error {
	return m
}

// EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError
// is the validation error returned by
// EnquireTransactionStatusResponse_OriginalTransactionDetails.Validate if the
// designated constraints aren't met.
type EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError) ErrorName() string {
	return "EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireTransactionStatusResponse_OriginalTransactionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireTransactionStatusResponse_OriginalTransactionDetailsValidationError{}
