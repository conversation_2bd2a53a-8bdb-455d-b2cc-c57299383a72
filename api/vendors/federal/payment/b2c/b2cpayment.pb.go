// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/federal/payment/b2c/b2cpayment.proto

package b2c

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateB2CTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Reference Id that is passed in the request.
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=REFERENCE_ID,proto3" json:"request_id,omitempty"`
	// unique identifier for a transaction
	// it may be empty and non empty depending on the status of transaction
	Utr string `protobuf:"bytes,2,opt,name=utr,json=UTR,proto3" json:"utr,omitempty"`
	// account from which money is transferred
	RemitterAccountNumber string `protobuf:"bytes,3,opt,name=remitter_account_number,json=REMITTER_ACCOUNTNO,proto3" json:"remitter_account_number,omitempty"`
	// account to which money is transferred
	BeneficiaryAccountNumber string `protobuf:"bytes,4,opt,name=beneficiary_account_number,json=BENEFICIARY_ACCOUNTNO,proto3" json:"beneficiary_account_number,omitempty"`
	// Registered name of Account holder with the beneficiary bank
	// Populated only in the case of IMPS transaction
	CreditedAccountName string `protobuf:"bytes,5,opt,name=credited_account_name,json=CREDITED_ACCOUNT_NAME,proto3" json:"credited_account_name,omitempty"`
	// transaction amount passed in the request
	Amount float64 `protobuf:"fixed64,6,opt,name=amount,json=TRAN_AMOUNT,proto3" json:"amount,omitempty"`
	// Timestamp when transaction got processed
	TransTimestamp string `protobuf:"bytes,7,opt,name=trans_timestamp,json=TRAN_DATE,proto3" json:"trans_timestamp,omitempty"`
	// Response Code for the Transaction
	ResponseCode string `protobuf:"bytes,8,opt,name=response_code,json=RESPONSE_CODE,proto3" json:"response_code,omitempty"`
	// Response Description for the Transaction
	ResponseReason string `protobuf:"bytes,9,opt,name=response_reason,json=RESPONSE_REASON,proto3" json:"response_reason,omitempty"`
	// High level response codes depicting the stage of the transaction. Can take these
	// PROCESSED, SUCCESS, FAILURE, SUSPECT values
	ResponseAction string `protobuf:"bytes,10,opt,name=response_action,json=RESPONSE_ACTION,proto3" json:"response_action,omitempty"`
}

func (x *UpdateB2CTransactionRequest) Reset() {
	*x = UpdateB2CTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateB2CTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateB2CTransactionRequest) ProtoMessage() {}

func (x *UpdateB2CTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateB2CTransactionRequest.ProtoReflect.Descriptor instead.
func (*UpdateB2CTransactionRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateB2CTransactionRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetRemitterAccountNumber() string {
	if x != nil {
		return x.RemitterAccountNumber
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetBeneficiaryAccountNumber() string {
	if x != nil {
		return x.BeneficiaryAccountNumber
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetCreditedAccountName() string {
	if x != nil {
		return x.CreditedAccountName
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UpdateB2CTransactionRequest) GetTransTimestamp() string {
	if x != nil {
		return x.TransTimestamp
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *UpdateB2CTransactionRequest) GetResponseAction() string {
	if x != nil {
		return x.ResponseAction
	}
	return ""
}

type EnquireTransactionStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business account credentials provided by Federal bank
	// Credentials will be different for UAT and PROD
	// Credentials will be provided by Federal Bank's API support team
	// Credentials include user_id, password and sender code
	UserId     string `protobuf:"bytes,1,opt,name=user_id,json=userid,proto3" json:"user_id,omitempty"`
	Password   string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	SenderCode string `protobuf:"bytes,3,opt,name=sender_code,json=sendercd,proto3" json:"sender_code,omitempty"`
	// Client should pass unique value to each call, to identify the request
	ReferenceId string `protobuf:"bytes,4,opt,name=reference_id,json=ReferenceId,proto3" json:"reference_id,omitempty"`
	// Original ReferenceId i.e., ReferenceId of the transaction about which the status is sought.
	// API will return the status of the transaction identified by the this reference Id.
	OriginalReferenceId string `protobuf:"bytes,5,opt,name=original_reference_id,json=Org_ReferenceId,proto3" json:"original_reference_id,omitempty"`
	// Determine the service for which the status is being enquired
	// Can be one of NEFT, IMPS, FT, RTGS, UPI
	TransactionType string `protobuf:"bytes,6,opt,name=transaction_type,json=TxnType,proto3" json:"transaction_type,omitempty"`
	// Unique Id which has been assigned to this transaction.
	TransactionId string `protobuf:"bytes,7,opt,name=transaction_id,json=TxnId,proto3" json:"transaction_id,omitempty"`
	// Date on which this transaction is initiated. Date should be in this format “DD-MM-YYYY”
	TransactionDate string `protobuf:"bytes,8,opt,name=transaction_date,json=Tran_Date,proto3" json:"transaction_date,omitempty"`
}

func (x *EnquireTransactionStatusRequest) Reset() {
	*x = EnquireTransactionStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireTransactionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireTransactionStatusRequest) ProtoMessage() {}

func (x *EnquireTransactionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireTransactionStatusRequest.ProtoReflect.Descriptor instead.
func (*EnquireTransactionStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP(), []int{1}
}

func (x *EnquireTransactionStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetOriginalReferenceId() string {
	if x != nil {
		return x.OriginalReferenceId
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetTransactionType() string {
	if x != nil {
		return x.TransactionType
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *EnquireTransactionStatusRequest) GetTransactionDate() string {
	if x != nil {
		return x.TransactionDate
	}
	return ""
}

type EnquireTransactionStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ReferenceId passed in the request
	ReferenceId string `protobuf:"bytes,2,opt,name=reference_id,json=ReferenceId,proto3" json:"reference_id,omitempty"`
	// Original reference Id passed in the request
	OriginalReferenceId string `protobuf:"bytes,3,opt,name=original_reference_id,json=Org_ReferenceId,proto3" json:"original_reference_id,omitempty"`
	// Transaction date passed in the request
	TransactionDate string `protobuf:"bytes,4,opt,name=transaction_date,json=tranTimeStamp,proto3" json:"transaction_date,omitempty"`
	// Response Code for the Transaction
	Response string `protobuf:"bytes,5,opt,name=response,json=Response,proto3" json:"response,omitempty"`
	// Response Description for the Transaction
	Reason             string                                                       `protobuf:"bytes,6,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
	OriginalTxnDetails *EnquireTransactionStatusResponse_OriginalTransactionDetails `protobuf:"bytes,7,opt,name=original_txn_details,json=Original_Transaction_Details,proto3" json:"original_txn_details,omitempty"`
}

func (x *EnquireTransactionStatusResponse) Reset() {
	*x = EnquireTransactionStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireTransactionStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireTransactionStatusResponse) ProtoMessage() {}

func (x *EnquireTransactionStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireTransactionStatusResponse.ProtoReflect.Descriptor instead.
func (*EnquireTransactionStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP(), []int{2}
}

func (x *EnquireTransactionStatusResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *EnquireTransactionStatusResponse) GetOriginalReferenceId() string {
	if x != nil {
		return x.OriginalReferenceId
	}
	return ""
}

func (x *EnquireTransactionStatusResponse) GetTransactionDate() string {
	if x != nil {
		return x.TransactionDate
	}
	return ""
}

func (x *EnquireTransactionStatusResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *EnquireTransactionStatusResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *EnquireTransactionStatusResponse) GetOriginalTxnDetails() *EnquireTransactionStatusResponse_OriginalTransactionDetails {
	if x != nil {
		return x.OriginalTxnDetails
	}
	return nil
}

type BalanceEnquiryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business account credentials provided by Federal bank
	// Credentials will be different for UAT and PROD
	// Credentials will be provided by Federal Bank's API support team
	// Credentials include user_id, password and sender code
	UserId     string `protobuf:"bytes,1,opt,name=user_id,json=userid,proto3" json:"user_id,omitempty"`
	Password   string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	SenderCode string `protobuf:"bytes,3,opt,name=sender_code,json=sendercd,proto3" json:"sender_code,omitempty"`
	// Client should pass unique value to each call, to identify the request
	ReferenceId string `protobuf:"bytes,4,opt,name=reference_id,json=ReferenceId,proto3" json:"reference_id,omitempty"`
	// b2c account's number to get balance data
	AccountNumber string `protobuf:"bytes,5,opt,name=account_number,json=Account_Number,proto3" json:"account_number,omitempty"`
}

func (x *BalanceEnquiryRequest) Reset() {
	*x = BalanceEnquiryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceEnquiryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceEnquiryRequest) ProtoMessage() {}

func (x *BalanceEnquiryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceEnquiryRequest.ProtoReflect.Descriptor instead.
func (*BalanceEnquiryRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP(), []int{3}
}

func (x *BalanceEnquiryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BalanceEnquiryRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *BalanceEnquiryRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *BalanceEnquiryRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *BalanceEnquiryRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

type BalanceEnquiryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode         string `protobuf:"bytes,1,opt,name=sender_code,json=sendercd,proto3" json:"sender_code,omitempty"`
	ReferenceId        string `protobuf:"bytes,2,opt,name=reference_id,json=ReferenceId,proto3" json:"reference_id,omitempty"`
	AccountNumber      string `protobuf:"bytes,3,opt,name=account_number,json=Account_Number,proto3" json:"account_number,omitempty"`
	CustomerName       string `protobuf:"bytes,4,opt,name=customer_name,json=CustomerName,proto3" json:"customer_name,omitempty"`
	AccountType        string `protobuf:"bytes,5,opt,name=account_type,json=Account_Type,proto3" json:"account_type,omitempty"`
	AccountStatus      string `protobuf:"bytes,6,opt,name=account_status,json=Account_Status,proto3" json:"account_status,omitempty"`
	LedgerBalance      string `protobuf:"bytes,7,opt,name=ledger_balance,json=LedgerBalance,proto3" json:"ledger_balance,omitempty"`
	AvailableBalance   string `protobuf:"bytes,8,opt,name=available_balance,json=AvailableBalance,proto3" json:"available_balance,omitempty"`
	FloatBalance       string `protobuf:"bytes,9,opt,name=float_balance,json=FloatBalance,proto3" json:"float_balance,omitempty"`
	FfdBalance         string `protobuf:"bytes,10,opt,name=ffd_balance,json=FFDBalance,proto3" json:"ffd_balance,omitempty"`
	UserDefinedBalance string `protobuf:"bytes,11,opt,name=user_defined_balance,json=UserDefinedBalance,proto3" json:"user_defined_balance,omitempty"`
	BalanceCurrency    string `protobuf:"bytes,12,opt,name=balance_currency,json=BalCurrencycode,proto3" json:"balance_currency,omitempty"`
	Response           string `protobuf:"bytes,13,opt,name=response,json=Response,proto3" json:"response,omitempty"`
	Reason             string `protobuf:"bytes,14,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
	CustId             string `protobuf:"bytes,15,opt,name=cust_id,json=CustId,proto3" json:"cust_id,omitempty"`
}

func (x *BalanceEnquiryResponse) Reset() {
	*x = BalanceEnquiryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceEnquiryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceEnquiryResponse) ProtoMessage() {}

func (x *BalanceEnquiryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceEnquiryResponse.ProtoReflect.Descriptor instead.
func (*BalanceEnquiryResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP(), []int{4}
}

func (x *BalanceEnquiryResponse) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetAccountStatus() string {
	if x != nil {
		return x.AccountStatus
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetLedgerBalance() string {
	if x != nil {
		return x.LedgerBalance
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetAvailableBalance() string {
	if x != nil {
		return x.AvailableBalance
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetFloatBalance() string {
	if x != nil {
		return x.FloatBalance
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetFfdBalance() string {
	if x != nil {
		return x.FfdBalance
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetUserDefinedBalance() string {
	if x != nil {
		return x.UserDefinedBalance
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetBalanceCurrency() string {
	if x != nil {
		return x.BalanceCurrency
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *BalanceEnquiryResponse) GetCustId() string {
	if x != nil {
		return x.CustId
	}
	return ""
}

// transaction details for which enquiry is requested
type EnquireTransactionStatusResponse_OriginalTransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique transaction reference
	Utr string `protobuf:"bytes,1,opt,name=utr,json=Utr,proto3" json:"utr,omitempty"`
	// account number from which amount is deducted
	RemitterAccountNumber string `protobuf:"bytes,2,opt,name=remitter_account_number,json=RemitterAccountNumber,proto3" json:"remitter_account_number,omitempty"`
	// account number to which amount is credited
	BeneficiaryAccountNumber string `protobuf:"bytes,3,opt,name=beneficiary_account_number,json=BeneficiaryAccountNumber,proto3" json:"beneficiary_account_number,omitempty"`
	// transacted amount
	TransactionAmount string `protobuf:"bytes,4,opt,name=transaction_amount,json=TranAmount,proto3" json:"transaction_amount,omitempty"`
	// transaction time
	CreatedTime string `protobuf:"bytes,5,opt,name=created_time,json=CreatedTime,proto3" json:"created_time,omitempty"`
	// response code from vendor
	ResponseCode string `protobuf:"bytes,6,opt,name=response_code,json=ResponseCode,proto3" json:"response_code,omitempty"`
	// response reason from vendor
	ResponseReason string `protobuf:"bytes,7,opt,name=response_reason,json=ResponseReason,proto3" json:"response_reason,omitempty"`
	// response action based on which user need to take call.
	// value could be SUCCESS/FAILURE/SUSPECT
	ResponseAction string `protobuf:"bytes,8,opt,name=response_action,json=ResponseAction,proto3" json:"response_action,omitempty"`
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) Reset() {
	*x = EnquireTransactionStatusResponse_OriginalTransactionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireTransactionStatusResponse_OriginalTransactionDetails) ProtoMessage() {}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireTransactionStatusResponse_OriginalTransactionDetails.ProtoReflect.Descriptor instead.
func (*EnquireTransactionStatusResponse_OriginalTransactionDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP(), []int{2, 0}
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetRemitterAccountNumber() string {
	if x != nil {
		return x.RemitterAccountNumber
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetBeneficiaryAccountNumber() string {
	if x != nil {
		return x.BeneficiaryAccountNumber
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetTransactionAmount() string {
	if x != nil {
		return x.TransactionAmount
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *EnquireTransactionStatusResponse_OriginalTransactionDetails) GetResponseAction() string {
	if x != nil {
		return x.ResponseAction
	}
	return ""
}

var File_api_vendors_federal_payment_b2c_b2cpayment_proto protoreflect.FileDescriptor

var file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDesc = []byte{
	0x0a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x62, 0x32,
	0x63, 0x2f, 0x62, 0x32, 0x63, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x32, 0x63, 0x22,
	0xb2, 0x03, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x32, 0x43, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x49,
	0x44, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x55, 0x54, 0x52, 0x12, 0x33, 0x0a, 0x17, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x4e, 0x4f, 0x12, 0x39, 0x0a, 0x1a, 0x62, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x42, 0x45,
	0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x4e, 0x4f, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x12, 0x1b, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x54, 0x52, 0x41, 0x4e, 0x5f,
	0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x12, 0x22, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x54, 0x52, 0x41, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x52, 0x45, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x22, 0xaf, 0x02, 0x0a, 0x1f, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a,
	0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x63, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x2e, 0x0a, 0x15, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x4f, 0x72, 0x67, 0x5f, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54, 0x78, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x78, 0x6e, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x72, 0x61,
	0x6e, 0x5f, 0x44, 0x61, 0x74, 0x65, 0x22, 0xd2, 0x05, 0x0a, 0x20, 0x45, 0x6e, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2e,
	0x0a, 0x15, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x4f,
	0x72, 0x67, 0x5f, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27,
	0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x94, 0x01, 0x0a, 0x14,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x32, 0x63, 0x2e, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x1c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x1a, 0xe6, 0x02, 0x0a, 0x1a, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x55, 0x74, 0x72, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x52, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x1a, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54, 0x72, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb6, 0x01, 0x0a, 0x15,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0b, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x63, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x22, 0xb7, 0x04, 0x0a, 0x16, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1d, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x63, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x66, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x46, 0x46, 0x44, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x42, 0x61, 0x6c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x75, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x43, 0x75, 0x73, 0x74, 0x49, 0x64, 0x42, 0x70,
	0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x32, 0x63, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x62, 0x32, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescOnce sync.Once
	file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescData = file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDesc
)

func file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescGZIP() []byte {
	file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescOnce.Do(func() {
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescData)
	})
	return file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDescData
}

var file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_vendors_federal_payment_b2c_b2cpayment_proto_goTypes = []interface{}{
	(*UpdateB2CTransactionRequest)(nil),                                 // 0: vendors.federal.payment.b2c.UpdateB2CTransactionRequest
	(*EnquireTransactionStatusRequest)(nil),                             // 1: vendors.federal.payment.b2c.EnquireTransactionStatusRequest
	(*EnquireTransactionStatusResponse)(nil),                            // 2: vendors.federal.payment.b2c.EnquireTransactionStatusResponse
	(*BalanceEnquiryRequest)(nil),                                       // 3: vendors.federal.payment.b2c.BalanceEnquiryRequest
	(*BalanceEnquiryResponse)(nil),                                      // 4: vendors.federal.payment.b2c.BalanceEnquiryResponse
	(*EnquireTransactionStatusResponse_OriginalTransactionDetails)(nil), // 5: vendors.federal.payment.b2c.EnquireTransactionStatusResponse.OriginalTransactionDetails
}
var file_api_vendors_federal_payment_b2c_b2cpayment_proto_depIdxs = []int32{
	5, // 0: vendors.federal.payment.b2c.EnquireTransactionStatusResponse.original_txn_details:type_name -> vendors.federal.payment.b2c.EnquireTransactionStatusResponse.OriginalTransactionDetails
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_vendors_federal_payment_b2c_b2cpayment_proto_init() }
func file_api_vendors_federal_payment_b2c_b2cpayment_proto_init() {
	if File_api_vendors_federal_payment_b2c_b2cpayment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateB2CTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireTransactionStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireTransactionStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceEnquiryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceEnquiryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireTransactionStatusResponse_OriginalTransactionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_federal_payment_b2c_b2cpayment_proto_goTypes,
		DependencyIndexes: file_api_vendors_federal_payment_b2c_b2cpayment_proto_depIdxs,
		MessageInfos:      file_api_vendors_federal_payment_b2c_b2cpayment_proto_msgTypes,
	}.Build()
	File_api_vendors_federal_payment_b2c_b2cpayment_proto = out.File
	file_api_vendors_federal_payment_b2c_b2cpayment_proto_rawDesc = nil
	file_api_vendors_federal_payment_b2c_b2cpayment_proto_goTypes = nil
	file_api_vendors_federal_payment_b2c_b2cpayment_proto_depIdxs = nil
}
