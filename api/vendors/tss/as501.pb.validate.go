// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/tss/as501.proto

package tss

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EncryptedRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EncryptedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EncryptedRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EncryptedRequestMultiError, or nil if none found.
func (m *EncryptedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EncryptedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EncryptedData

	// no validation rules for EncryptionKey

	// no validation rules for Signature

	// no validation rules for RequestId

	if len(errors) > 0 {
		return EncryptedRequestMultiError(errors)
	}

	return nil
}

// EncryptedRequestMultiError is an error wrapping multiple validation errors
// returned by EncryptedRequest.ValidateAll() if the designated constraints
// aren't met.
type EncryptedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EncryptedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EncryptedRequestMultiError) AllErrors() []error { return m }

// EncryptedRequestValidationError is the validation error returned by
// EncryptedRequest.Validate if the designated constraints aren't met.
type EncryptedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EncryptedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EncryptedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EncryptedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EncryptedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EncryptedRequestValidationError) ErrorName() string { return "EncryptedRequestValidationError" }

// Error satisfies the builtin error interface
func (e EncryptedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEncryptedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EncryptedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EncryptedRequestValidationError{}

// Validate checks the field values on EncryptedResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EncryptedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EncryptedResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EncryptedResponseMultiError, or nil if none found.
func (m *EncryptedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EncryptedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EncryptedData

	// no validation rules for EncryptionKey

	// no validation rules for Signature

	if len(errors) > 0 {
		return EncryptedResponseMultiError(errors)
	}

	return nil
}

// EncryptedResponseMultiError is an error wrapping multiple validation errors
// returned by EncryptedResponse.ValidateAll() if the designated constraints
// aren't met.
type EncryptedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EncryptedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EncryptedResponseMultiError) AllErrors() []error { return m }

// EncryptedResponseValidationError is the validation error returned by
// EncryptedResponse.Validate if the designated constraints aren't met.
type EncryptedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EncryptedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EncryptedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EncryptedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EncryptedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EncryptedResponseValidationError) ErrorName() string {
	return "EncryptedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EncryptedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEncryptedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EncryptedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EncryptedResponseValidationError{}

// Validate checks the field values on AS501Request with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AS501Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS501Request with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AS501RequestMultiError, or
// nil if none found.
func (m *AS501Request) ValidateAll() error {
	return m.validate(true)
}

func (m *AS501Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for SourceSystemName

	// no validation rules for Purpose

	// no validation rules for SessionKey

	// no validation rules for EncryptedData

	// no validation rules for Signature

	for idx, item := range m.GetCustomerList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS501RequestValidationError{
						field:  fmt.Sprintf("CustomerList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS501RequestValidationError{
						field:  fmt.Sprintf("CustomerList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS501RequestValidationError{
					field:  fmt.Sprintf("CustomerList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ApiToken

	if len(errors) > 0 {
		return AS501RequestMultiError(errors)
	}

	return nil
}

// AS501RequestMultiError is an error wrapping multiple validation errors
// returned by AS501Request.ValidateAll() if the designated constraints aren't met.
type AS501RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS501RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS501RequestMultiError) AllErrors() []error { return m }

// AS501RequestValidationError is the validation error returned by
// AS501Request.Validate if the designated constraints aren't met.
type AS501RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS501RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS501RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS501RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS501RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS501RequestValidationError) ErrorName() string { return "AS501RequestValidationError" }

// Error satisfies the builtin error interface
func (e AS501RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS501Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS501RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS501RequestValidationError{}

// Validate checks the field values on CustomerData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CustomerData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomerDataMultiError, or
// nil if none found.
func (m *CustomerData) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceSystemName

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for UniqueIdentifier

	// no validation rules for SourceSystemCustomerCreationDate

	// no validation rules for EkycOtpBased

	// no validation rules for Segment

	// no validation rules for SegmentStartDate

	// no validation rules for Products

	// no validation rules for Status

	// no validation rules for EffectiveDate

	// no validation rules for ConstitutionType

	// no validation rules for Prefix

	// no validation rules for FirstName

	// no validation rules for MiddleName

	// no validation rules for LastName

	// no validation rules for Alias

	// no validation rules for FatherPrefix

	// no validation rules for FatherFirstName

	// no validation rules for FatherMiddleName

	// no validation rules for FatherLastName

	// no validation rules for SpousePrefix

	// no validation rules for SpouseFirstName

	// no validation rules for SpouseMiddleName

	// no validation rules for SpouseLastName

	// no validation rules for MotherPrefix

	// no validation rules for MotherFirstName

	// no validation rules for MotherMiddleName

	// no validation rules for MotherLastName

	// no validation rules for Minor

	// no validation rules for Gender

	// no validation rules for MaritalStatus

	// no validation rules for OccupationType

	// no validation rules for OccupationTypeOther

	// no validation rules for NatureOfBusiness

	// no validation rules for NatureOfBusinessOther

	// no validation rules for ProofOfIdSubmitted

	// no validation rules for DateOfBirth

	// no validation rules for WorkEmail

	// no validation rules for PersonalEmail

	// no validation rules for WorkMobileIsd

	// no validation rules for WorkMobileNumber

	// no validation rules for PersonalMobileIsd

	// no validation rules for PersonalMobileNumber

	// no validation rules for PermanentAddressCountry

	// no validation rules for PermanentAddressZipCode

	// no validation rules for PermanentAddressLine1

	// no validation rules for PermanentAddressLine2

	// no validation rules for PermanentAddressLine3

	// no validation rules for PermanentAddressDistrict

	// no validation rules for PermanentAddressCity

	// no validation rules for PermanentAddressState

	// no validation rules for PermanentAddressDocument

	// no validation rules for PermanentAddressDocumentOthersValue

	// no validation rules for CorrespondenceAddressCountry

	// no validation rules for CorrespondenceAddressZipCode

	// no validation rules for CorrespondenceAddressLine1

	// no validation rules for CorrespondenceAddressLine2

	// no validation rules for CorrespondenceAddressLine3

	// no validation rules for CorrespondenceAddressDistrict

	// no validation rules for CorrespondenceAddressCity

	// no validation rules for CorrespondenceAddressState

	// no validation rules for CorrespondenceAddressDocument

	// no validation rules for CountryOfResidence

	// no validation rules for CountryOfBirth

	// no validation rules for BirthCity

	// no validation rules for PassportIssueCountry

	// no validation rules for PassportNumber

	// no validation rules for PassportExpiryDate

	// no validation rules for VoterIdNumber

	// no validation rules for DrivingLicenseNumber

	// no validation rules for DrivingLicenseExpiryDate

	// no validation rules for AadhaarNumber

	// no validation rules for AadhaarVaultReferenceNumber

	// no validation rules for NregaNumber

	// no validation rules for NprLetterNumber

	// no validation rules for DirectorIdentificationNumber

	// no validation rules for FormSixty

	// no validation rules for Pan

	// no validation rules for CkycNumber

	// no validation rules for PoliticallyExposed

	// no validation rules for AdverseReputationDetails

	// no validation rules for Notes

	// no validation rules for Tags

	// no validation rules for ScreeningProfile

	// no validation rules for ScreeningReportWhenNil

	// no validation rules for RiskProfile

	// no validation rules for AdverseReputation

	// no validation rules for AdverseReputationClassification

	for idx, item := range m.GetTaxDetailDtoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("TaxDetailDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("TaxDetailDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerDataValidationError{
					field:  fmt.Sprintf("TaxDetailDtoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PoliticallyExposedClassification

	// no validation rules for Citizenships

	// no validation rules for Nationalities

	for idx, item := range m.GetRegAmlRiskSpecialCategoryDtoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("RegAmlRiskSpecialCategoryDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("RegAmlRiskSpecialCategoryDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerDataValidationError{
					field:  fmt.Sprintf("RegAmlRiskSpecialCategoryDtoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRelatedPersonList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("RelatedPersonList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("RelatedPersonList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerDataValidationError{
					field:  fmt.Sprintf("RelatedPersonList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCustomerRelationDtoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("CustomerRelationDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("CustomerRelationDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerDataValidationError{
					field:  fmt.Sprintf("CustomerRelationDtoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ConstitutionTypeId

	// no validation rules for CompanyIdentificationNumber

	// no validation rules for CompanyRegistrationNumber

	// no validation rules for CompanyRegistrationCountry

	// no validation rules for GlobalIntermediaryIdentificationNumber

	// no validation rules for KycAttestationType

	// no validation rules for KycDateOfDeclaration

	// no validation rules for KycPlaceOfDeclaration

	// no validation rules for KycVerificationDate

	// no validation rules for KycEmployeeName

	// no validation rules for KycEmployeeDesignation

	// no validation rules for KycVerificationBranch

	// no validation rules for KycEmployeeCode

	// no validation rules for Listed

	// no validation rules for ApplicationRefNumber

	// no validation rules for DocumentRefNumber

	// no validation rules for RegAmlRisk

	// no validation rules for RegAmlRiskLastRiskReviewDate

	// no validation rules for RegAmlRiskNextRiskReviewDate

	// no validation rules for IncomeRange

	// no validation rules for ExactIncome

	// no validation rules for IncomeCurrency

	// no validation rules for IncomeEffectiveDate

	// no validation rules for IncomeDescription

	// no validation rules for IncomeDocument

	// no validation rules for ExactNetworth

	// no validation rules for NetworthCurrency

	// no validation rules for NetworthEffectiveDate

	// no validation rules for NetworthDescription

	// no validation rules for NetworthDocument

	// no validation rules for FamilyCode

	// no validation rules for Channel

	// no validation rules for ContactPersonFirstName1

	// no validation rules for ContactPersonMiddleName1

	// no validation rules for ContactPersonLastName1

	// no validation rules for ContactPersonDesignation1

	// no validation rules for ContactPersonFirstName2

	// no validation rules for ContactPersonMiddleName2

	// no validation rules for ContactPersonLastName2

	// no validation rules for ContactPersonDesignation2

	// no validation rules for ContactPersonMobileIsd

	// no validation rules for ContactPersonMobileNo

	// no validation rules for ContactPersonMobileIsd2

	// no validation rules for ContactPersonMobileNo2

	// no validation rules for ContactPersonEmailId1

	// no validation rules for ContactPersonEmailId2

	// no validation rules for CommencementDate

	// no validation rules for MaidenPrefix

	// no validation rules for MaidenFirstName

	// no validation rules for MaidenMiddleName

	// no validation rules for MaidenLastName

	// no validation rules for RelatedPersonCountForCkyc

	// no validation rules for EducationalQualification

	// no validation rules for CountryOfOperations

	for idx, item := range m.GetIdentityDocument() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("IdentityDocument[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerDataValidationError{
						field:  fmt.Sprintf("IdentityDocument[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerDataValidationError{
					field:  fmt.Sprintf("IdentityDocument[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CustomerDataMultiError(errors)
	}

	return nil
}

// CustomerDataMultiError is an error wrapping multiple validation errors
// returned by CustomerData.ValidateAll() if the designated constraints aren't met.
type CustomerDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDataMultiError) AllErrors() []error { return m }

// CustomerDataValidationError is the validation error returned by
// CustomerData.Validate if the designated constraints aren't met.
type CustomerDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDataValidationError) ErrorName() string { return "CustomerDataValidationError" }

// Error satisfies the builtin error interface
func (e CustomerDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDataValidationError{}

// Validate checks the field values on TaxDetail with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaxDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaxDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaxDetailMultiError, or nil
// if none found.
func (m *TaxDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *TaxDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaxResidencyCountry

	// no validation rules for TaxIdentificationNumber

	// no validation rules for TaxResidencyStartDate

	// no validation rules for TaxResidencyEndDate

	if len(errors) > 0 {
		return TaxDetailMultiError(errors)
	}

	return nil
}

// TaxDetailMultiError is an error wrapping multiple validation errors returned
// by TaxDetail.ValidateAll() if the designated constraints aren't met.
type TaxDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaxDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaxDetailMultiError) AllErrors() []error { return m }

// TaxDetailValidationError is the validation error returned by
// TaxDetail.Validate if the designated constraints aren't met.
type TaxDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaxDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaxDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaxDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaxDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaxDetailValidationError) ErrorName() string { return "TaxDetailValidationError" }

// Error satisfies the builtin error interface
func (e TaxDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaxDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaxDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaxDetailValidationError{}

// Validate checks the field values on RegAMLRiskSpecialCategory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegAMLRiskSpecialCategory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegAMLRiskSpecialCategory with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegAMLRiskSpecialCategoryMultiError, or nil if none found.
func (m *RegAMLRiskSpecialCategory) ValidateAll() error {
	return m.validate(true)
}

func (m *RegAMLRiskSpecialCategory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RegAmlRiskSpecialCategory

	// no validation rules for RegAmlRiskSpecialCategoryStartDate

	if len(errors) > 0 {
		return RegAMLRiskSpecialCategoryMultiError(errors)
	}

	return nil
}

// RegAMLRiskSpecialCategoryMultiError is an error wrapping multiple validation
// errors returned by RegAMLRiskSpecialCategory.ValidateAll() if the
// designated constraints aren't met.
type RegAMLRiskSpecialCategoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegAMLRiskSpecialCategoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegAMLRiskSpecialCategoryMultiError) AllErrors() []error { return m }

// RegAMLRiskSpecialCategoryValidationError is the validation error returned by
// RegAMLRiskSpecialCategory.Validate if the designated constraints aren't met.
type RegAMLRiskSpecialCategoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegAMLRiskSpecialCategoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegAMLRiskSpecialCategoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegAMLRiskSpecialCategoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegAMLRiskSpecialCategoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegAMLRiskSpecialCategoryValidationError) ErrorName() string {
	return "RegAMLRiskSpecialCategoryValidationError"
}

// Error satisfies the builtin error interface
func (e RegAMLRiskSpecialCategoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegAMLRiskSpecialCategory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegAMLRiskSpecialCategoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegAMLRiskSpecialCategoryValidationError{}

// Validate checks the field values on RelatedPerson with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RelatedPerson) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelatedPerson with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RelatedPersonMultiError, or
// nil if none found.
func (m *RelatedPerson) ValidateAll() error {
	return m.validate(true)
}

func (m *RelatedPerson) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for UniqueIdentifier

	// no validation rules for Prefix

	// no validation rules for FirstName

	// no validation rules for MiddleName

	// no validation rules for LastName

	// no validation rules for Gender

	// no validation rules for DateOfBirth

	// no validation rules for Nationality

	// no validation rules for PassportNumber

	// no validation rules for Pan

	// no validation rules for PoliticallyExposed

	// no validation rules for AdverseReputation

	// no validation rules for Notes

	if len(errors) > 0 {
		return RelatedPersonMultiError(errors)
	}

	return nil
}

// RelatedPersonMultiError is an error wrapping multiple validation errors
// returned by RelatedPerson.ValidateAll() if the designated constraints
// aren't met.
type RelatedPersonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelatedPersonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelatedPersonMultiError) AllErrors() []error { return m }

// RelatedPersonValidationError is the validation error returned by
// RelatedPerson.Validate if the designated constraints aren't met.
type RelatedPersonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelatedPersonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelatedPersonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelatedPersonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelatedPersonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelatedPersonValidationError) ErrorName() string { return "RelatedPersonValidationError" }

// Error satisfies the builtin error interface
func (e RelatedPersonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelatedPerson.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelatedPersonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelatedPersonValidationError{}

// Validate checks the field values on CustomerRelation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerRelationMultiError, or nil if none found.
func (m *CustomerRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for RelatedPersonSourceSystemCustomerCode

	// no validation rules for Relation

	// no validation rules for RelationNotes

	if len(errors) > 0 {
		return CustomerRelationMultiError(errors)
	}

	return nil
}

// CustomerRelationMultiError is an error wrapping multiple validation errors
// returned by CustomerRelation.ValidateAll() if the designated constraints
// aren't met.
type CustomerRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerRelationMultiError) AllErrors() []error { return m }

// CustomerRelationValidationError is the validation error returned by
// CustomerRelation.Validate if the designated constraints aren't met.
type CustomerRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerRelationValidationError) ErrorName() string { return "CustomerRelationValidationError" }

// Error satisfies the builtin error interface
func (e CustomerRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerRelationValidationError{}

// Validate checks the field values on IdentityDocument with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IdentityDocument) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IdentityDocument with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IdentityDocumentMultiError, or nil if none found.
func (m *IdentityDocument) ValidateAll() error {
	return m.validate(true)
}

func (m *IdentityDocument) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocumentType

	// no validation rules for DocumentNumber

	// no validation rules for DocumentIssueDate

	// no validation rules for DocumentExpiryDate

	// no validation rules for DocumentIssueCountry

	if len(errors) > 0 {
		return IdentityDocumentMultiError(errors)
	}

	return nil
}

// IdentityDocumentMultiError is an error wrapping multiple validation errors
// returned by IdentityDocument.ValidateAll() if the designated constraints
// aren't met.
type IdentityDocumentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdentityDocumentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdentityDocumentMultiError) AllErrors() []error { return m }

// IdentityDocumentValidationError is the validation error returned by
// IdentityDocument.Validate if the designated constraints aren't met.
type IdentityDocumentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdentityDocumentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdentityDocumentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdentityDocumentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdentityDocumentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdentityDocumentValidationError) ErrorName() string { return "IdentityDocumentValidationError" }

// Error satisfies the builtin error interface
func (e IdentityDocumentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdentityDocument.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdentityDocumentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdentityDocumentValidationError{}

// Validate checks the field values on AS501Response with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AS501Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS501Response with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AS501ResponseMultiError, or
// nil if none found.
func (m *AS501Response) ValidateAll() error {
	return m.validate(true)
}

func (m *AS501Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for OverallStatus

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	for idx, item := range m.GetCustomerResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS501ResponseValidationError{
						field:  fmt.Sprintf("CustomerResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS501ResponseValidationError{
						field:  fmt.Sprintf("CustomerResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS501ResponseValidationError{
					field:  fmt.Sprintf("CustomerResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AS501ResponseMultiError(errors)
	}

	return nil
}

// AS501ResponseMultiError is an error wrapping multiple validation errors
// returned by AS501Response.ValidateAll() if the designated constraints
// aren't met.
type AS501ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS501ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS501ResponseMultiError) AllErrors() []error { return m }

// AS501ResponseValidationError is the validation error returned by
// AS501Response.Validate if the designated constraints aren't met.
type AS501ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS501ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS501ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS501ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS501ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS501ResponseValidationError) ErrorName() string { return "AS501ResponseValidationError" }

// Error satisfies the builtin error interface
func (e AS501ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS501Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS501ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS501ResponseValidationError{}

// Validate checks the field values on CustomerResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerResponseMultiError, or nil if none found.
func (m *CustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for ValidationOutcome

	// no validation rules for SuggestedAction

	for idx, item := range m.GetPurposeResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerResponseValidationError{
						field:  fmt.Sprintf("PurposeResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerResponseValidationError{
						field:  fmt.Sprintf("PurposeResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerResponseValidationError{
					field:  fmt.Sprintf("PurposeResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	// no validation rules for ValidationFailureCount

	if len(errors) > 0 {
		return CustomerResponseMultiError(errors)
	}

	return nil
}

// CustomerResponseMultiError is an error wrapping multiple validation errors
// returned by CustomerResponse.ValidateAll() if the designated constraints
// aren't met.
type CustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerResponseMultiError) AllErrors() []error { return m }

// CustomerResponseValidationError is the validation error returned by
// CustomerResponse.Validate if the designated constraints aren't met.
type CustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerResponseValidationError) ErrorName() string { return "CustomerResponseValidationError" }

// Error satisfies the builtin error interface
func (e CustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerResponseValidationError{}

// Validate checks the field values on PurposeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PurposeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurposeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurposeResponseMultiError, or nil if none found.
func (m *PurposeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PurposeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Purpose

	// no validation rules for PurposeCode

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	// no validation rules for ValidationFailureCount

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurposeResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurposeResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurposeResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurposeResponseMultiError(errors)
	}

	return nil
}

// PurposeResponseMultiError is an error wrapping multiple validation errors
// returned by PurposeResponse.ValidateAll() if the designated constraints
// aren't met.
type PurposeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurposeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurposeResponseMultiError) AllErrors() []error { return m }

// PurposeResponseValidationError is the validation error returned by
// PurposeResponse.Validate if the designated constraints aren't met.
type PurposeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurposeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurposeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurposeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurposeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurposeResponseValidationError) ErrorName() string { return "PurposeResponseValidationError" }

// Error satisfies the builtin error interface
func (e PurposeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurposeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurposeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurposeResponseValidationError{}

// Validate checks the field values on PurposeData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PurposeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurposeData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PurposeDataMultiError, or
// nil if none found.
func (m *PurposeData) ValidateAll() error {
	return m.validate(true)
}

func (m *PurposeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HitsDetected

	// no validation rules for HitsCount

	// no validation rules for ConfirmedHit

	// no validation rules for ReportData

	if len(errors) > 0 {
		return PurposeDataMultiError(errors)
	}

	return nil
}

// PurposeDataMultiError is an error wrapping multiple validation errors
// returned by PurposeData.ValidateAll() if the designated constraints aren't met.
type PurposeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurposeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurposeDataMultiError) AllErrors() []error { return m }

// PurposeDataValidationError is the validation error returned by
// PurposeData.Validate if the designated constraints aren't met.
type PurposeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurposeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurposeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurposeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurposeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurposeDataValidationError) ErrorName() string { return "PurposeDataValidationError" }

// Error satisfies the builtin error interface
func (e PurposeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurposeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurposeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurposeDataValidationError{}
