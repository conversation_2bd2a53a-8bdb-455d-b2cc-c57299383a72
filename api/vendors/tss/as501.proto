syntax = "proto3";

package vendors.tss;

option go_package = "github.com/epifi/gamma/api/vendors/tss";
option java_package = "com.github.epifi.gamma.api.vendors.tss";

message EncryptedRequest {
  string encrypted_data = 1 [json_name = "EncryptedData"];
  string encryption_key = 2 [json_name = "EncryptionKey"];
  string signature = 3 [json_name = "Signature"];
  string request_id = 4 [json_name = "RequestId"];
}

message EncryptedResponse {
  string encrypted_data = 1 [json_name = "EncryptedData"];
  string encryption_key = 2 [json_name = "EncryptionKey"];
  string signature = 3 [json_name = "Signature"];
}

// AS501 API Request - Multi-Purpose Customer Information API
message AS501Request {
  string request_id = 1 [json_name = "requestId"];
  string source_system_name = 2 [json_name = "sourceSystemName"];
  string purpose = 3 [json_name = "purpose"];
  string session_key = 4 [json_name = "sessionKey"];
  string encrypted_data = 5 [json_name = "encryptedData"];
  string signature = 6 [json_name = "signature"];
  repeated CustomerData customer_list = 7 [json_name = "customerList"];
  string api_token = 8 [json_name = "apiToken"];
}

// Customer data structure for AS501 API
message CustomerData {
  string source_system_customer_code = 1 [json_name = "sourceSystemCustomerCode"];
  string unique_identifier = 2 [json_name = "uniqueIdentifier"];
  string source_system_customer_creation_date = 3 [json_name = "sourceSystemCustomerCreationDate"];
  string ekyc_otp_based = 4 [json_name = "ekycOTPbased"];
  string segment = 5 [json_name = "segment"];
  string segment_start_date = 6 [json_name = "segmentStartDate"];
  string products = 7 [json_name = "products"];
  string status = 8 [json_name = "status"];
  string effective_date = 9 [json_name = "effectiveDate"];
  string constitution_type = 10 [json_name = "constitutionType"];
  string prefix = 11 [json_name = "prefix"];
  string first_name = 12 [json_name = "firstName"];
  string middle_name = 13 [json_name = "middleName"];
  string last_name = 14 [json_name = "lastName"];
  string alias = 15 [json_name = "alias"];
  string father_prefix = 16 [json_name = "fatherPrefix"];
  string father_first_name = 17 [json_name = "fatherFirstName"];
  string father_middle_name = 18 [json_name = "fatherMiddleName"];
  string father_last_name = 19 [json_name = "fatherLastName"];
  string spouse_prefix = 20 [json_name = "spousePrefix"];
  string spouse_first_name = 21 [json_name = "spouseFirstName"];
  string spouse_middle_name = 22 [json_name = "spouseMiddleName"];
  string spouse_last_name = 23 [json_name = "spouseLastName"];
  string mother_prefix = 24 [json_name = "motherPrefix"];
  string mother_first_name = 25 [json_name = "motherFirstName"];
  string mother_middle_name = 26 [json_name = "motherMiddleName"];
  string mother_last_name = 27 [json_name = "motherLastName"];
  string minor = 28 [json_name = "minor"];
  string gender = 29 [json_name = "gender"];
  string marital_status = 30 [json_name = "maritalStatus"];
  string occupation_type = 31 [json_name = "occupationType"];
  string occupation_type_other = 32 [json_name = "occupationTypeOther"];
  string nature_of_business = 33 [json_name = "natureOfBusiness"];
  string nature_of_business_other = 34 [json_name = "natureOfBusinessOther"];
  string proof_of_id_submitted = 35 [json_name = "proofOfIdSubmitted"];
  string date_of_birth = 36 [json_name = "dateofBirth"];
  string work_email = 37 [json_name = "workEmail"];
  string personal_email = 38 [json_name = "personalEmail"];
  string work_mobile_isd = 39 [json_name = "workMobileISD"];
  string work_mobile_number = 40 [json_name = "workMobileNumber"];
  string personal_mobile_isd = 41 [json_name = "personalMobileISD"];
  string personal_mobile_number = 42 [json_name = "personalMobileNumber"];
  string permanent_address_country = 43 [json_name = "permanentAddressCountry"];
  string permanent_address_zip_code = 44 [json_name = "permanentAddressZipCode"];
  string permanent_address_line1 = 45 [json_name = "permanentAddressLine1"];
  string permanent_address_line2 = 46 [json_name = "permanentAddressLine2"];
  string permanent_address_line3 = 47 [json_name = "permanentAddressLine3"];
  string permanent_address_district = 48 [json_name = "permanentAddressDistrict"];
  string permanent_address_city = 49 [json_name = "permanentAddressCity"];
  string permanent_address_state = 50 [json_name = "permanentAddressState"];
  string permanent_address_document = 51 [json_name = "permanentAddressDocument"];
  string permanent_address_document_others_value = 52 [json_name = "permanentAddressDocumentOthersValue"];
  string correspondence_address_country = 53 [json_name = "correspondenceAddressCountry"];
  string correspondence_address_zip_code = 54 [json_name = "correspondenceAddressZipCode"];
  string correspondence_address_line1 = 55 [json_name = "correspondenceAddressLine1"];
  string correspondence_address_line2 = 56 [json_name = "correspondenceAddressLine2"];
  string correspondence_address_line3 = 57 [json_name = "correspondenceAddressLine3"];
  string correspondence_address_district = 58 [json_name = "correspondenceAddressDistrict"];
  string correspondence_address_city = 59 [json_name = "correspondenceAddressCity"];
  string correspondence_address_state = 60 [json_name = "correspondenceAddressState"];
  string correspondence_address_document = 61 [json_name = "correspondenceAddressDocument"];
  string country_of_residence = 62 [json_name = "countryOfResidence"];
  string country_of_birth = 63 [json_name = "countryOfBirth"];
  string birth_city = 64 [json_name = "birthCity"];
  string passport_issue_country = 65 [json_name = "passportIssueCountry"];
  string passport_number = 66 [json_name = "passportNumber"];
  string passport_expiry_date = 67 [json_name = "passportExpiryDate"];
  string voter_id_number = 68 [json_name = "voterIdNumber"];
  string driving_license_number = 69 [json_name = "drivingLicenseNumber"];
  string driving_license_expiry_date = 70 [json_name = "drivingLicenseExpiryDate"];
  string aadhaar_number = 71 [json_name = "aadhaarNumber"];
  string aadhaar_vault_reference_number = 72 [json_name = "aadhaarVaultReferenceNumber"];
  string nrega_number = 73 [json_name = "nregaNumber"];
  string npr_letter_number = 74 [json_name = "nprLetterNumber"];
  string director_identification_number = 75 [json_name = "directorIdentificationNumber"];
  string form_sixty = 76 [json_name = "formSixty"];
  string pan = 77 [json_name = "pan"];
  string ckyc_number = 78 [json_name = "ckycNumber"];
  string politically_exposed = 79 [json_name = "politicallyExposed"];
  string adverse_reputation_details = 80 [json_name = "adverseReputationDetails"];
  string notes = 81 [json_name = "notes"];
  string tags = 82 [json_name = "tags"];
  string screening_profile = 83 [json_name = "screeningProfile"];
  string screening_report_when_nil = 84 [json_name = "screeningreportwhennil"];
  string risk_profile = 85 [json_name = "riskProfile"];
  string adverse_reputation = 86 [json_name = "adverseReputation"];
  string adverse_reputation_classification = 87 [json_name = "adverseReputationClassification"];
  repeated TaxDetail tax_detail_dto_list = 88 [json_name = "taxDetailDtoList"];
  string politically_exposed_classification = 89 [json_name = "politicallyExposedClassification"];
  string citizenships = 90 [json_name = "citizenships"];
  string nationalities = 91 [json_name = "nationalities"];
  repeated RegAMLRiskSpecialCategory reg_aml_risk_special_category_dto_list = 92 [json_name = "regAMLRiskSpecialCategoryDtoList"];
  repeated RelatedPerson related_person_list = 93 [json_name = "relatedPersonList"];
  repeated CustomerRelation customer_relation_dto_list = 94 [json_name = "customerRelationDtoList"];
  string constitution_type_id = 95 [json_name = "constitutionTypeId"];
  string company_identification_number = 96 [json_name = "companyIdentificationNumber"];
  string company_registration_number = 97 [json_name = "companyRegistrationNumber"];
  string company_registration_country = 98 [json_name = "companyRegistrationCountry"];
  string global_intermediary_identification_number = 99 [json_name = "globalIntermediaryIdentificationNumber"];
  string kyc_attestation_type = 100 [json_name = "kycAttestationType"];
  string kyc_date_of_declaration = 101 [json_name = "kycDateOfDeclaration"];
  string kyc_place_of_declaration = 102 [json_name = "kycPlaceOfDeclaration"];
  string kyc_verification_date = 103 [json_name = "kycVerificationDate"];
  string kyc_employee_name = 104 [json_name = "kycEmployeeName"];
  string kyc_employee_designation = 105 [json_name = "kycEmployeeDesignation"];
  string kyc_verification_branch = 106 [json_name = "kycVerificationBranch"];
  string kyc_employee_code = 107 [json_name = "kycEmployeeCode"];
  string listed = 108 [json_name = "listed"];
  string application_ref_number = 109 [json_name = "applicationRefNumber"];
  string document_ref_number = 110 [json_name = "documentRefNumber"];
  string reg_aml_risk = 111 [json_name = "regAMLRisk"];
  string reg_aml_risk_last_risk_review_date = 112 [json_name = "regAMLRiskLastRiskReviewDate"];
  string reg_aml_risk_next_risk_review_date = 113 [json_name = "regAMLRiskNextRiskReviewDate"];
  string income_range = 114 [json_name = "incomeRange"];
  double exact_income = 115 [json_name = "exactIncome"];
  string income_currency = 116 [json_name = "incomeCurrency"];
  string income_effective_date = 117 [json_name = "incomeEffectiveDate"];
  string income_description = 118 [json_name = "incomeDescription"];
  string income_document = 119 [json_name = "incomeDocument"];
  double exact_networth = 120 [json_name = "exactNetworth"];
  string networth_currency = 121 [json_name = "networthCurrency"];
  string networth_effective_date = 122 [json_name = "networthEffectiveDate"];
  string networth_description = 123 [json_name = "networthDescription"];
  string networth_document = 124 [json_name = "networthDocument"];
  string family_code = 125 [json_name = "familyCode"];
  string channel = 126 [json_name = "channel"];
  string contact_person_first_name1 = 127 [json_name = "contactPersonFirstName1"];
  string contact_person_middle_name1 = 128 [json_name = "contactPersonMiddleName1"];
  string contact_person_last_name1 = 129 [json_name = "contactPersonLastName1"];
  string contact_person_designation1 = 130 [json_name = "contactPersonDesignation1"];
  string contact_person_first_name2 = 131 [json_name = "contactPersonFirstName2"];
  string contact_person_middle_name2 = 132 [json_name = "contactPersonMiddleName2"];
  string contact_person_last_name2 = 133 [json_name = "contactPersonLastName2"];
  string contact_person_designation2 = 134 [json_name = "contactPersonDesignation2"];
  string contact_person_mobile_isd = 135 [json_name = "contactPersonMobileISD"];
  string contact_person_mobile_no = 136 [json_name = "contactPersonMobileNo"];
  string contact_person_mobile_isd2 = 137 [json_name = "contactPersonMobileISD2"];
  string contact_person_mobile_no2 = 138 [json_name = "contactPersonMobileNo2"];
  string contact_person_email_id1 = 139 [json_name = "contactPersonEmailId1"];
  string contact_person_email_id2 = 140 [json_name = "contactPersonEmailId2"];
  string commencement_date = 141 [json_name = "commencementDate"];
  string maiden_prefix = 142 [json_name = "maidenPrefix"];
  string maiden_first_name = 143 [json_name = "maidenFirstName"];
  string maiden_middle_name = 144 [json_name = "maidenMiddleName"];
  string maiden_last_name = 145 [json_name = "maidenLastName"];
  int32 related_person_count_for_ckyc = 146 [json_name = "relatedPersonCountforCKYC"];
  string educational_qualification = 147 [json_name = "educationalQualification"];
  string country_of_operations = 148 [json_name = "countryOfOperations"];
  repeated IdentityDocument identity_document = 149 [json_name = "identityDocument"];
}

// Tax detail structure
message TaxDetail {
  string tax_residency_country = 1 [json_name = "taxResidencyCountry"];
  string tax_identification_number = 2 [json_name = "taxIdentificationNumber"];
  string tax_residency_start_date = 3 [json_name = "taxResidencyStartDate"];
  string tax_residency_end_date = 4 [json_name = "taxResidencyEndDate"];
}

// RegAML Risk Special Category structure
message RegAMLRiskSpecialCategory {
  string reg_aml_risk_special_category = 1 [json_name = "regAMLRiskSpecialCategory"];
  string reg_aml_risk_special_category_start_date = 2 [json_name = "regAMLRiskSpecialCategoryStartDate"];
}

// Related Person structure
message RelatedPerson {
  string source_system_customer_code = 1 [json_name = "sourceSystemCustomerCode"];
  string unique_identifier = 2 [json_name = "uniqueIdentifier"];
  string prefix = 3 [json_name = "prefix"];
  string first_name = 4 [json_name = "firstName"];
  string middle_name = 5 [json_name = "middleName"];
  string last_name = 6 [json_name = "lastName"];
  string gender = 7 [json_name = "gender"];
  string date_of_birth = 8 [json_name = "dateofBirth"];
  string nationality = 9 [json_name = "nationality"];
  string passport_number = 10 [json_name = "passportNumber"];
  string pan = 11 [json_name = "pan"];
  string politically_exposed = 12 [json_name = "politicallyExposed"];
  string adverse_reputation = 13 [json_name = "adverseReputation"];
  string notes = 14 [json_name = "notes"];
}

message CustomerRelation {
  string source_system_customer_code = 1 [json_name = "sourceSystemCustomerCode"];
  string related_person_source_system_customer_code = 2 [json_name = "relatedPersonSourceSystemCustomerCode"];
  string relation = 3 [json_name = "relation"];
  string relation_notes = 4 [json_name = "relationNotes"];
}

message IdentityDocument {
  string document_type = 1 [json_name = "documentType"];
  string document_number = 2 [json_name = "documentNumber"];
  string document_issue_date = 3 [json_name = "documentIssueDate"];
  string document_expiry_date = 4 [json_name = "documentExpiryDate"];
  string document_issue_country = 5 [json_name = "documentIssueCountry"];
}

message AS501Response {
  string request_id = 1 [json_name = "RequestId"];
  string overall_status = 2 [json_name = "OverallStatus"];
  string validation_code = 3 [json_name = "ValidationCode"];
  string validation_description = 4 [json_name = "ValidationDescription"];
  repeated CustomerResponse customer_response = 5 [json_name = "CustomerResponse"];
}

message CustomerResponse {
  string source_system_customer_code = 1 [json_name = "SourceSystemCustomerCode"];
  string validation_outcome = 2 [json_name = "ValidationOutcome"];
  string suggested_action = 3 [json_name = "SuggestedAction"];
  repeated PurposeResponse purpose_response = 4 [json_name = "PurposeResponse"];
  string validation_code = 5 [json_name = "ValidationCode"];
  string validation_description = 6 [json_name = "ValidationDescription"];
  int32 validation_failure_count = 7 [json_name = "ValidationFailureCount"];
}

message PurposeResponse {
  string purpose = 1 [json_name = "Purpose"];
  string purpose_code = 2 [json_name = "PurposeCode"];
  string validation_code = 3 [json_name = "ValidationCode"];
  string validation_description = 4 [json_name = "ValidationDescription"];
  int32 validation_failure_count = 5 [json_name = "ValidationFailureCount"];
  PurposeData data = 6 [json_name = "Data"];
}

message PurposeData {
  string hits_detected = 1 [json_name = "HitsDetected"];
  int32 hits_count = 2 [json_name = "HitsCount"];
  string confirmed_hit = 3 [json_name = "ConfirmedHit"];
  string report_data = 4 [json_name = "ReportData"];
}
