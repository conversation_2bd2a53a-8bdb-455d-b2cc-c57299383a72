// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/tss/as501.proto

package tss

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EncryptedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EncryptedData string `protobuf:"bytes,1,opt,name=encrypted_data,json=EncryptedData,proto3" json:"encrypted_data,omitempty"`
	EncryptionKey string `protobuf:"bytes,2,opt,name=encryption_key,json=EncryptionKey,proto3" json:"encryption_key,omitempty"`
	Signature     string `protobuf:"bytes,3,opt,name=signature,json=Signature,proto3" json:"signature,omitempty"`
	RequestId     string `protobuf:"bytes,4,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
}

func (x *EncryptedRequest) Reset() {
	*x = EncryptedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EncryptedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncryptedRequest) ProtoMessage() {}

func (x *EncryptedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncryptedRequest.ProtoReflect.Descriptor instead.
func (*EncryptedRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{0}
}

func (x *EncryptedRequest) GetEncryptedData() string {
	if x != nil {
		return x.EncryptedData
	}
	return ""
}

func (x *EncryptedRequest) GetEncryptionKey() string {
	if x != nil {
		return x.EncryptionKey
	}
	return ""
}

func (x *EncryptedRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *EncryptedRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type EncryptedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EncryptedData string `protobuf:"bytes,1,opt,name=encrypted_data,json=EncryptedData,proto3" json:"encrypted_data,omitempty"`
	EncryptionKey string `protobuf:"bytes,2,opt,name=encryption_key,json=EncryptionKey,proto3" json:"encryption_key,omitempty"`
	Signature     string `protobuf:"bytes,3,opt,name=signature,json=Signature,proto3" json:"signature,omitempty"`
}

func (x *EncryptedResponse) Reset() {
	*x = EncryptedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EncryptedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncryptedResponse) ProtoMessage() {}

func (x *EncryptedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncryptedResponse.ProtoReflect.Descriptor instead.
func (*EncryptedResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{1}
}

func (x *EncryptedResponse) GetEncryptedData() string {
	if x != nil {
		return x.EncryptedData
	}
	return ""
}

func (x *EncryptedResponse) GetEncryptionKey() string {
	if x != nil {
		return x.EncryptionKey
	}
	return ""
}

func (x *EncryptedResponse) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

// AS501 API Request - Multi-Purpose Customer Information API
type AS501Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId        string          `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	SourceSystemName string          `protobuf:"bytes,2,opt,name=source_system_name,json=sourceSystemName,proto3" json:"source_system_name,omitempty"`
	Purpose          string          `protobuf:"bytes,3,opt,name=purpose,proto3" json:"purpose,omitempty"`
	SessionKey       string          `protobuf:"bytes,4,opt,name=session_key,json=sessionKey,proto3" json:"session_key,omitempty"`
	EncryptedData    string          `protobuf:"bytes,5,opt,name=encrypted_data,json=encryptedData,proto3" json:"encrypted_data,omitempty"`
	Signature        string          `protobuf:"bytes,6,opt,name=signature,proto3" json:"signature,omitempty"`
	CustomerList     []*CustomerData `protobuf:"bytes,7,rep,name=customer_list,json=customerList,proto3" json:"customer_list,omitempty"`
	ApiToken         string          `protobuf:"bytes,8,opt,name=api_token,json=apiToken,proto3" json:"api_token,omitempty"`
}

func (x *AS501Request) Reset() {
	*x = AS501Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS501Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS501Request) ProtoMessage() {}

func (x *AS501Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS501Request.ProtoReflect.Descriptor instead.
func (*AS501Request) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{2}
}

func (x *AS501Request) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AS501Request) GetSourceSystemName() string {
	if x != nil {
		return x.SourceSystemName
	}
	return ""
}

func (x *AS501Request) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *AS501Request) GetSessionKey() string {
	if x != nil {
		return x.SessionKey
	}
	return ""
}

func (x *AS501Request) GetEncryptedData() string {
	if x != nil {
		return x.EncryptedData
	}
	return ""
}

func (x *AS501Request) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *AS501Request) GetCustomerList() []*CustomerData {
	if x != nil {
		return x.CustomerList
	}
	return nil
}

func (x *AS501Request) GetApiToken() string {
	if x != nil {
		return x.ApiToken
	}
	return ""
}

// Customer data structure for AS501 API
type CustomerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceSystemCustomerCode               string                       `protobuf:"bytes,1,opt,name=source_system_customer_code,json=sourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	UniqueIdentifier                       string                       `protobuf:"bytes,2,opt,name=unique_identifier,json=uniqueIdentifier,proto3" json:"unique_identifier,omitempty"`
	SourceSystemCustomerCreationDate       string                       `protobuf:"bytes,3,opt,name=source_system_customer_creation_date,json=sourceSystemCustomerCreationDate,proto3" json:"source_system_customer_creation_date,omitempty"`
	EkycOtpBased                           string                       `protobuf:"bytes,4,opt,name=ekyc_otp_based,json=ekycOTPbased,proto3" json:"ekyc_otp_based,omitempty"`
	Segment                                string                       `protobuf:"bytes,5,opt,name=segment,proto3" json:"segment,omitempty"`
	SegmentStartDate                       string                       `protobuf:"bytes,6,opt,name=segment_start_date,json=segmentStartDate,proto3" json:"segment_start_date,omitempty"`
	Products                               string                       `protobuf:"bytes,7,opt,name=products,proto3" json:"products,omitempty"`
	Status                                 string                       `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	EffectiveDate                          string                       `protobuf:"bytes,9,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	ConstitutionType                       string                       `protobuf:"bytes,10,opt,name=constitution_type,json=constitutionType,proto3" json:"constitution_type,omitempty"`
	Prefix                                 string                       `protobuf:"bytes,11,opt,name=prefix,proto3" json:"prefix,omitempty"`
	FirstName                              string                       `protobuf:"bytes,12,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	MiddleName                             string                       `protobuf:"bytes,13,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	LastName                               string                       `protobuf:"bytes,14,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Alias                                  string                       `protobuf:"bytes,15,opt,name=alias,proto3" json:"alias,omitempty"`
	FatherPrefix                           string                       `protobuf:"bytes,16,opt,name=father_prefix,json=fatherPrefix,proto3" json:"father_prefix,omitempty"`
	FatherFirstName                        string                       `protobuf:"bytes,17,opt,name=father_first_name,json=fatherFirstName,proto3" json:"father_first_name,omitempty"`
	FatherMiddleName                       string                       `protobuf:"bytes,18,opt,name=father_middle_name,json=fatherMiddleName,proto3" json:"father_middle_name,omitempty"`
	FatherLastName                         string                       `protobuf:"bytes,19,opt,name=father_last_name,json=fatherLastName,proto3" json:"father_last_name,omitempty"`
	SpousePrefix                           string                       `protobuf:"bytes,20,opt,name=spouse_prefix,json=spousePrefix,proto3" json:"spouse_prefix,omitempty"`
	SpouseFirstName                        string                       `protobuf:"bytes,21,opt,name=spouse_first_name,json=spouseFirstName,proto3" json:"spouse_first_name,omitempty"`
	SpouseMiddleName                       string                       `protobuf:"bytes,22,opt,name=spouse_middle_name,json=spouseMiddleName,proto3" json:"spouse_middle_name,omitempty"`
	SpouseLastName                         string                       `protobuf:"bytes,23,opt,name=spouse_last_name,json=spouseLastName,proto3" json:"spouse_last_name,omitempty"`
	MotherPrefix                           string                       `protobuf:"bytes,24,opt,name=mother_prefix,json=motherPrefix,proto3" json:"mother_prefix,omitempty"`
	MotherFirstName                        string                       `protobuf:"bytes,25,opt,name=mother_first_name,json=motherFirstName,proto3" json:"mother_first_name,omitempty"`
	MotherMiddleName                       string                       `protobuf:"bytes,26,opt,name=mother_middle_name,json=motherMiddleName,proto3" json:"mother_middle_name,omitempty"`
	MotherLastName                         string                       `protobuf:"bytes,27,opt,name=mother_last_name,json=motherLastName,proto3" json:"mother_last_name,omitempty"`
	Minor                                  string                       `protobuf:"bytes,28,opt,name=minor,proto3" json:"minor,omitempty"`
	Gender                                 string                       `protobuf:"bytes,29,opt,name=gender,proto3" json:"gender,omitempty"`
	MaritalStatus                          string                       `protobuf:"bytes,30,opt,name=marital_status,json=maritalStatus,proto3" json:"marital_status,omitempty"`
	OccupationType                         string                       `protobuf:"bytes,31,opt,name=occupation_type,json=occupationType,proto3" json:"occupation_type,omitempty"`
	OccupationTypeOther                    string                       `protobuf:"bytes,32,opt,name=occupation_type_other,json=occupationTypeOther,proto3" json:"occupation_type_other,omitempty"`
	NatureOfBusiness                       string                       `protobuf:"bytes,33,opt,name=nature_of_business,json=natureOfBusiness,proto3" json:"nature_of_business,omitempty"`
	NatureOfBusinessOther                  string                       `protobuf:"bytes,34,opt,name=nature_of_business_other,json=natureOfBusinessOther,proto3" json:"nature_of_business_other,omitempty"`
	ProofOfIdSubmitted                     string                       `protobuf:"bytes,35,opt,name=proof_of_id_submitted,json=proofOfIdSubmitted,proto3" json:"proof_of_id_submitted,omitempty"`
	DateOfBirth                            string                       `protobuf:"bytes,36,opt,name=date_of_birth,json=dateofBirth,proto3" json:"date_of_birth,omitempty"`
	WorkEmail                              string                       `protobuf:"bytes,37,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	PersonalEmail                          string                       `protobuf:"bytes,38,opt,name=personal_email,json=personalEmail,proto3" json:"personal_email,omitempty"`
	WorkMobileIsd                          string                       `protobuf:"bytes,39,opt,name=work_mobile_isd,json=workMobileISD,proto3" json:"work_mobile_isd,omitempty"`
	WorkMobileNumber                       string                       `protobuf:"bytes,40,opt,name=work_mobile_number,json=workMobileNumber,proto3" json:"work_mobile_number,omitempty"`
	PersonalMobileIsd                      string                       `protobuf:"bytes,41,opt,name=personal_mobile_isd,json=personalMobileISD,proto3" json:"personal_mobile_isd,omitempty"`
	PersonalMobileNumber                   string                       `protobuf:"bytes,42,opt,name=personal_mobile_number,json=personalMobileNumber,proto3" json:"personal_mobile_number,omitempty"`
	PermanentAddressCountry                string                       `protobuf:"bytes,43,opt,name=permanent_address_country,json=permanentAddressCountry,proto3" json:"permanent_address_country,omitempty"`
	PermanentAddressZipCode                string                       `protobuf:"bytes,44,opt,name=permanent_address_zip_code,json=permanentAddressZipCode,proto3" json:"permanent_address_zip_code,omitempty"`
	PermanentAddressLine1                  string                       `protobuf:"bytes,45,opt,name=permanent_address_line1,json=permanentAddressLine1,proto3" json:"permanent_address_line1,omitempty"`
	PermanentAddressLine2                  string                       `protobuf:"bytes,46,opt,name=permanent_address_line2,json=permanentAddressLine2,proto3" json:"permanent_address_line2,omitempty"`
	PermanentAddressLine3                  string                       `protobuf:"bytes,47,opt,name=permanent_address_line3,json=permanentAddressLine3,proto3" json:"permanent_address_line3,omitempty"`
	PermanentAddressDistrict               string                       `protobuf:"bytes,48,opt,name=permanent_address_district,json=permanentAddressDistrict,proto3" json:"permanent_address_district,omitempty"`
	PermanentAddressCity                   string                       `protobuf:"bytes,49,opt,name=permanent_address_city,json=permanentAddressCity,proto3" json:"permanent_address_city,omitempty"`
	PermanentAddressState                  string                       `protobuf:"bytes,50,opt,name=permanent_address_state,json=permanentAddressState,proto3" json:"permanent_address_state,omitempty"`
	PermanentAddressDocument               string                       `protobuf:"bytes,51,opt,name=permanent_address_document,json=permanentAddressDocument,proto3" json:"permanent_address_document,omitempty"`
	PermanentAddressDocumentOthersValue    string                       `protobuf:"bytes,52,opt,name=permanent_address_document_others_value,json=permanentAddressDocumentOthersValue,proto3" json:"permanent_address_document_others_value,omitempty"`
	CorrespondenceAddressCountry           string                       `protobuf:"bytes,53,opt,name=correspondence_address_country,json=correspondenceAddressCountry,proto3" json:"correspondence_address_country,omitempty"`
	CorrespondenceAddressZipCode           string                       `protobuf:"bytes,54,opt,name=correspondence_address_zip_code,json=correspondenceAddressZipCode,proto3" json:"correspondence_address_zip_code,omitempty"`
	CorrespondenceAddressLine1             string                       `protobuf:"bytes,55,opt,name=correspondence_address_line1,json=correspondenceAddressLine1,proto3" json:"correspondence_address_line1,omitempty"`
	CorrespondenceAddressLine2             string                       `protobuf:"bytes,56,opt,name=correspondence_address_line2,json=correspondenceAddressLine2,proto3" json:"correspondence_address_line2,omitempty"`
	CorrespondenceAddressLine3             string                       `protobuf:"bytes,57,opt,name=correspondence_address_line3,json=correspondenceAddressLine3,proto3" json:"correspondence_address_line3,omitempty"`
	CorrespondenceAddressDistrict          string                       `protobuf:"bytes,58,opt,name=correspondence_address_district,json=correspondenceAddressDistrict,proto3" json:"correspondence_address_district,omitempty"`
	CorrespondenceAddressCity              string                       `protobuf:"bytes,59,opt,name=correspondence_address_city,json=correspondenceAddressCity,proto3" json:"correspondence_address_city,omitempty"`
	CorrespondenceAddressState             string                       `protobuf:"bytes,60,opt,name=correspondence_address_state,json=correspondenceAddressState,proto3" json:"correspondence_address_state,omitempty"`
	CorrespondenceAddressDocument          string                       `protobuf:"bytes,61,opt,name=correspondence_address_document,json=correspondenceAddressDocument,proto3" json:"correspondence_address_document,omitempty"`
	CountryOfResidence                     string                       `protobuf:"bytes,62,opt,name=country_of_residence,json=countryOfResidence,proto3" json:"country_of_residence,omitempty"`
	CountryOfBirth                         string                       `protobuf:"bytes,63,opt,name=country_of_birth,json=countryOfBirth,proto3" json:"country_of_birth,omitempty"`
	BirthCity                              string                       `protobuf:"bytes,64,opt,name=birth_city,json=birthCity,proto3" json:"birth_city,omitempty"`
	PassportIssueCountry                   string                       `protobuf:"bytes,65,opt,name=passport_issue_country,json=passportIssueCountry,proto3" json:"passport_issue_country,omitempty"`
	PassportNumber                         string                       `protobuf:"bytes,66,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	PassportExpiryDate                     string                       `protobuf:"bytes,67,opt,name=passport_expiry_date,json=passportExpiryDate,proto3" json:"passport_expiry_date,omitempty"`
	VoterIdNumber                          string                       `protobuf:"bytes,68,opt,name=voter_id_number,json=voterIdNumber,proto3" json:"voter_id_number,omitempty"`
	DrivingLicenseNumber                   string                       `protobuf:"bytes,69,opt,name=driving_license_number,json=drivingLicenseNumber,proto3" json:"driving_license_number,omitempty"`
	DrivingLicenseExpiryDate               string                       `protobuf:"bytes,70,opt,name=driving_license_expiry_date,json=drivingLicenseExpiryDate,proto3" json:"driving_license_expiry_date,omitempty"`
	AadhaarNumber                          string                       `protobuf:"bytes,71,opt,name=aadhaar_number,json=aadhaarNumber,proto3" json:"aadhaar_number,omitempty"`
	AadhaarVaultReferenceNumber            string                       `protobuf:"bytes,72,opt,name=aadhaar_vault_reference_number,json=aadhaarVaultReferenceNumber,proto3" json:"aadhaar_vault_reference_number,omitempty"`
	NregaNumber                            string                       `protobuf:"bytes,73,opt,name=nrega_number,json=nregaNumber,proto3" json:"nrega_number,omitempty"`
	NprLetterNumber                        string                       `protobuf:"bytes,74,opt,name=npr_letter_number,json=nprLetterNumber,proto3" json:"npr_letter_number,omitempty"`
	DirectorIdentificationNumber           string                       `protobuf:"bytes,75,opt,name=director_identification_number,json=directorIdentificationNumber,proto3" json:"director_identification_number,omitempty"`
	FormSixty                              string                       `protobuf:"bytes,76,opt,name=form_sixty,json=formSixty,proto3" json:"form_sixty,omitempty"`
	Pan                                    string                       `protobuf:"bytes,77,opt,name=pan,proto3" json:"pan,omitempty"`
	CkycNumber                             string                       `protobuf:"bytes,78,opt,name=ckyc_number,json=ckycNumber,proto3" json:"ckyc_number,omitempty"`
	PoliticallyExposed                     string                       `protobuf:"bytes,79,opt,name=politically_exposed,json=politicallyExposed,proto3" json:"politically_exposed,omitempty"`
	AdverseReputationDetails               string                       `protobuf:"bytes,80,opt,name=adverse_reputation_details,json=adverseReputationDetails,proto3" json:"adverse_reputation_details,omitempty"`
	Notes                                  string                       `protobuf:"bytes,81,opt,name=notes,proto3" json:"notes,omitempty"`
	Tags                                   string                       `protobuf:"bytes,82,opt,name=tags,proto3" json:"tags,omitempty"`
	ScreeningProfile                       string                       `protobuf:"bytes,83,opt,name=screening_profile,json=screeningProfile,proto3" json:"screening_profile,omitempty"`
	ScreeningReportWhenNil                 string                       `protobuf:"bytes,84,opt,name=screening_report_when_nil,json=screeningreportwhennil,proto3" json:"screening_report_when_nil,omitempty"`
	RiskProfile                            string                       `protobuf:"bytes,85,opt,name=risk_profile,json=riskProfile,proto3" json:"risk_profile,omitempty"`
	AdverseReputation                      string                       `protobuf:"bytes,86,opt,name=adverse_reputation,json=adverseReputation,proto3" json:"adverse_reputation,omitempty"`
	AdverseReputationClassification        string                       `protobuf:"bytes,87,opt,name=adverse_reputation_classification,json=adverseReputationClassification,proto3" json:"adverse_reputation_classification,omitempty"`
	TaxDetailDtoList                       []*TaxDetail                 `protobuf:"bytes,88,rep,name=tax_detail_dto_list,json=taxDetailDtoList,proto3" json:"tax_detail_dto_list,omitempty"`
	PoliticallyExposedClassification       string                       `protobuf:"bytes,89,opt,name=politically_exposed_classification,json=politicallyExposedClassification,proto3" json:"politically_exposed_classification,omitempty"`
	Citizenships                           string                       `protobuf:"bytes,90,opt,name=citizenships,proto3" json:"citizenships,omitempty"`
	Nationalities                          string                       `protobuf:"bytes,91,opt,name=nationalities,proto3" json:"nationalities,omitempty"`
	RegAmlRiskSpecialCategoryDtoList       []*RegAMLRiskSpecialCategory `protobuf:"bytes,92,rep,name=reg_aml_risk_special_category_dto_list,json=regAMLRiskSpecialCategoryDtoList,proto3" json:"reg_aml_risk_special_category_dto_list,omitempty"`
	RelatedPersonList                      []*RelatedPerson             `protobuf:"bytes,93,rep,name=related_person_list,json=relatedPersonList,proto3" json:"related_person_list,omitempty"`
	CustomerRelationDtoList                []*CustomerRelation          `protobuf:"bytes,94,rep,name=customer_relation_dto_list,json=customerRelationDtoList,proto3" json:"customer_relation_dto_list,omitempty"`
	ConstitutionTypeId                     string                       `protobuf:"bytes,95,opt,name=constitution_type_id,json=constitutionTypeId,proto3" json:"constitution_type_id,omitempty"`
	CompanyIdentificationNumber            string                       `protobuf:"bytes,96,opt,name=company_identification_number,json=companyIdentificationNumber,proto3" json:"company_identification_number,omitempty"`
	CompanyRegistrationNumber              string                       `protobuf:"bytes,97,opt,name=company_registration_number,json=companyRegistrationNumber,proto3" json:"company_registration_number,omitempty"`
	CompanyRegistrationCountry             string                       `protobuf:"bytes,98,opt,name=company_registration_country,json=companyRegistrationCountry,proto3" json:"company_registration_country,omitempty"`
	GlobalIntermediaryIdentificationNumber string                       `protobuf:"bytes,99,opt,name=global_intermediary_identification_number,json=globalIntermediaryIdentificationNumber,proto3" json:"global_intermediary_identification_number,omitempty"`
	KycAttestationType                     string                       `protobuf:"bytes,100,opt,name=kyc_attestation_type,json=kycAttestationType,proto3" json:"kyc_attestation_type,omitempty"`
	KycDateOfDeclaration                   string                       `protobuf:"bytes,101,opt,name=kyc_date_of_declaration,json=kycDateOfDeclaration,proto3" json:"kyc_date_of_declaration,omitempty"`
	KycPlaceOfDeclaration                  string                       `protobuf:"bytes,102,opt,name=kyc_place_of_declaration,json=kycPlaceOfDeclaration,proto3" json:"kyc_place_of_declaration,omitempty"`
	KycVerificationDate                    string                       `protobuf:"bytes,103,opt,name=kyc_verification_date,json=kycVerificationDate,proto3" json:"kyc_verification_date,omitempty"`
	KycEmployeeName                        string                       `protobuf:"bytes,104,opt,name=kyc_employee_name,json=kycEmployeeName,proto3" json:"kyc_employee_name,omitempty"`
	KycEmployeeDesignation                 string                       `protobuf:"bytes,105,opt,name=kyc_employee_designation,json=kycEmployeeDesignation,proto3" json:"kyc_employee_designation,omitempty"`
	KycVerificationBranch                  string                       `protobuf:"bytes,106,opt,name=kyc_verification_branch,json=kycVerificationBranch,proto3" json:"kyc_verification_branch,omitempty"`
	KycEmployeeCode                        string                       `protobuf:"bytes,107,opt,name=kyc_employee_code,json=kycEmployeeCode,proto3" json:"kyc_employee_code,omitempty"`
	Listed                                 string                       `protobuf:"bytes,108,opt,name=listed,proto3" json:"listed,omitempty"`
	ApplicationRefNumber                   string                       `protobuf:"bytes,109,opt,name=application_ref_number,json=applicationRefNumber,proto3" json:"application_ref_number,omitempty"`
	DocumentRefNumber                      string                       `protobuf:"bytes,110,opt,name=document_ref_number,json=documentRefNumber,proto3" json:"document_ref_number,omitempty"`
	RegAmlRisk                             string                       `protobuf:"bytes,111,opt,name=reg_aml_risk,json=regAMLRisk,proto3" json:"reg_aml_risk,omitempty"`
	RegAmlRiskLastRiskReviewDate           string                       `protobuf:"bytes,112,opt,name=reg_aml_risk_last_risk_review_date,json=regAMLRiskLastRiskReviewDate,proto3" json:"reg_aml_risk_last_risk_review_date,omitempty"`
	RegAmlRiskNextRiskReviewDate           string                       `protobuf:"bytes,113,opt,name=reg_aml_risk_next_risk_review_date,json=regAMLRiskNextRiskReviewDate,proto3" json:"reg_aml_risk_next_risk_review_date,omitempty"`
	IncomeRange                            string                       `protobuf:"bytes,114,opt,name=income_range,json=incomeRange,proto3" json:"income_range,omitempty"`
	ExactIncome                            float64                      `protobuf:"fixed64,115,opt,name=exact_income,json=exactIncome,proto3" json:"exact_income,omitempty"`
	IncomeCurrency                         string                       `protobuf:"bytes,116,opt,name=income_currency,json=incomeCurrency,proto3" json:"income_currency,omitempty"`
	IncomeEffectiveDate                    string                       `protobuf:"bytes,117,opt,name=income_effective_date,json=incomeEffectiveDate,proto3" json:"income_effective_date,omitempty"`
	IncomeDescription                      string                       `protobuf:"bytes,118,opt,name=income_description,json=incomeDescription,proto3" json:"income_description,omitempty"`
	IncomeDocument                         string                       `protobuf:"bytes,119,opt,name=income_document,json=incomeDocument,proto3" json:"income_document,omitempty"`
	ExactNetworth                          float64                      `protobuf:"fixed64,120,opt,name=exact_networth,json=exactNetworth,proto3" json:"exact_networth,omitempty"`
	NetworthCurrency                       string                       `protobuf:"bytes,121,opt,name=networth_currency,json=networthCurrency,proto3" json:"networth_currency,omitempty"`
	NetworthEffectiveDate                  string                       `protobuf:"bytes,122,opt,name=networth_effective_date,json=networthEffectiveDate,proto3" json:"networth_effective_date,omitempty"`
	NetworthDescription                    string                       `protobuf:"bytes,123,opt,name=networth_description,json=networthDescription,proto3" json:"networth_description,omitempty"`
	NetworthDocument                       string                       `protobuf:"bytes,124,opt,name=networth_document,json=networthDocument,proto3" json:"networth_document,omitempty"`
	FamilyCode                             string                       `protobuf:"bytes,125,opt,name=family_code,json=familyCode,proto3" json:"family_code,omitempty"`
	Channel                                string                       `protobuf:"bytes,126,opt,name=channel,proto3" json:"channel,omitempty"`
	ContactPersonFirstName1                string                       `protobuf:"bytes,127,opt,name=contact_person_first_name1,json=contactPersonFirstName1,proto3" json:"contact_person_first_name1,omitempty"`
	ContactPersonMiddleName1               string                       `protobuf:"bytes,128,opt,name=contact_person_middle_name1,json=contactPersonMiddleName1,proto3" json:"contact_person_middle_name1,omitempty"`
	ContactPersonLastName1                 string                       `protobuf:"bytes,129,opt,name=contact_person_last_name1,json=contactPersonLastName1,proto3" json:"contact_person_last_name1,omitempty"`
	ContactPersonDesignation1              string                       `protobuf:"bytes,130,opt,name=contact_person_designation1,json=contactPersonDesignation1,proto3" json:"contact_person_designation1,omitempty"`
	ContactPersonFirstName2                string                       `protobuf:"bytes,131,opt,name=contact_person_first_name2,json=contactPersonFirstName2,proto3" json:"contact_person_first_name2,omitempty"`
	ContactPersonMiddleName2               string                       `protobuf:"bytes,132,opt,name=contact_person_middle_name2,json=contactPersonMiddleName2,proto3" json:"contact_person_middle_name2,omitempty"`
	ContactPersonLastName2                 string                       `protobuf:"bytes,133,opt,name=contact_person_last_name2,json=contactPersonLastName2,proto3" json:"contact_person_last_name2,omitempty"`
	ContactPersonDesignation2              string                       `protobuf:"bytes,134,opt,name=contact_person_designation2,json=contactPersonDesignation2,proto3" json:"contact_person_designation2,omitempty"`
	ContactPersonMobileIsd                 string                       `protobuf:"bytes,135,opt,name=contact_person_mobile_isd,json=contactPersonMobileISD,proto3" json:"contact_person_mobile_isd,omitempty"`
	ContactPersonMobileNo                  string                       `protobuf:"bytes,136,opt,name=contact_person_mobile_no,json=contactPersonMobileNo,proto3" json:"contact_person_mobile_no,omitempty"`
	ContactPersonMobileIsd2                string                       `protobuf:"bytes,137,opt,name=contact_person_mobile_isd2,json=contactPersonMobileISD2,proto3" json:"contact_person_mobile_isd2,omitempty"`
	ContactPersonMobileNo2                 string                       `protobuf:"bytes,138,opt,name=contact_person_mobile_no2,json=contactPersonMobileNo2,proto3" json:"contact_person_mobile_no2,omitempty"`
	ContactPersonEmailId1                  string                       `protobuf:"bytes,139,opt,name=contact_person_email_id1,json=contactPersonEmailId1,proto3" json:"contact_person_email_id1,omitempty"`
	ContactPersonEmailId2                  string                       `protobuf:"bytes,140,opt,name=contact_person_email_id2,json=contactPersonEmailId2,proto3" json:"contact_person_email_id2,omitempty"`
	CommencementDate                       string                       `protobuf:"bytes,141,opt,name=commencement_date,json=commencementDate,proto3" json:"commencement_date,omitempty"`
	MaidenPrefix                           string                       `protobuf:"bytes,142,opt,name=maiden_prefix,json=maidenPrefix,proto3" json:"maiden_prefix,omitempty"`
	MaidenFirstName                        string                       `protobuf:"bytes,143,opt,name=maiden_first_name,json=maidenFirstName,proto3" json:"maiden_first_name,omitempty"`
	MaidenMiddleName                       string                       `protobuf:"bytes,144,opt,name=maiden_middle_name,json=maidenMiddleName,proto3" json:"maiden_middle_name,omitempty"`
	MaidenLastName                         string                       `protobuf:"bytes,145,opt,name=maiden_last_name,json=maidenLastName,proto3" json:"maiden_last_name,omitempty"`
	RelatedPersonCountForCkyc              int32                        `protobuf:"varint,146,opt,name=related_person_count_for_ckyc,json=relatedPersonCountforCKYC,proto3" json:"related_person_count_for_ckyc,omitempty"`
	EducationalQualification               string                       `protobuf:"bytes,147,opt,name=educational_qualification,json=educationalQualification,proto3" json:"educational_qualification,omitempty"`
	CountryOfOperations                    string                       `protobuf:"bytes,148,opt,name=country_of_operations,json=countryOfOperations,proto3" json:"country_of_operations,omitempty"`
	IdentityDocument                       []*IdentityDocument          `protobuf:"bytes,149,rep,name=identity_document,json=identityDocument,proto3" json:"identity_document,omitempty"`
}

func (x *CustomerData) Reset() {
	*x = CustomerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerData) ProtoMessage() {}

func (x *CustomerData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerData.ProtoReflect.Descriptor instead.
func (*CustomerData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerData) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *CustomerData) GetUniqueIdentifier() string {
	if x != nil {
		return x.UniqueIdentifier
	}
	return ""
}

func (x *CustomerData) GetSourceSystemCustomerCreationDate() string {
	if x != nil {
		return x.SourceSystemCustomerCreationDate
	}
	return ""
}

func (x *CustomerData) GetEkycOtpBased() string {
	if x != nil {
		return x.EkycOtpBased
	}
	return ""
}

func (x *CustomerData) GetSegment() string {
	if x != nil {
		return x.Segment
	}
	return ""
}

func (x *CustomerData) GetSegmentStartDate() string {
	if x != nil {
		return x.SegmentStartDate
	}
	return ""
}

func (x *CustomerData) GetProducts() string {
	if x != nil {
		return x.Products
	}
	return ""
}

func (x *CustomerData) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CustomerData) GetEffectiveDate() string {
	if x != nil {
		return x.EffectiveDate
	}
	return ""
}

func (x *CustomerData) GetConstitutionType() string {
	if x != nil {
		return x.ConstitutionType
	}
	return ""
}

func (x *CustomerData) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *CustomerData) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerData) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *CustomerData) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerData) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *CustomerData) GetFatherPrefix() string {
	if x != nil {
		return x.FatherPrefix
	}
	return ""
}

func (x *CustomerData) GetFatherFirstName() string {
	if x != nil {
		return x.FatherFirstName
	}
	return ""
}

func (x *CustomerData) GetFatherMiddleName() string {
	if x != nil {
		return x.FatherMiddleName
	}
	return ""
}

func (x *CustomerData) GetFatherLastName() string {
	if x != nil {
		return x.FatherLastName
	}
	return ""
}

func (x *CustomerData) GetSpousePrefix() string {
	if x != nil {
		return x.SpousePrefix
	}
	return ""
}

func (x *CustomerData) GetSpouseFirstName() string {
	if x != nil {
		return x.SpouseFirstName
	}
	return ""
}

func (x *CustomerData) GetSpouseMiddleName() string {
	if x != nil {
		return x.SpouseMiddleName
	}
	return ""
}

func (x *CustomerData) GetSpouseLastName() string {
	if x != nil {
		return x.SpouseLastName
	}
	return ""
}

func (x *CustomerData) GetMotherPrefix() string {
	if x != nil {
		return x.MotherPrefix
	}
	return ""
}

func (x *CustomerData) GetMotherFirstName() string {
	if x != nil {
		return x.MotherFirstName
	}
	return ""
}

func (x *CustomerData) GetMotherMiddleName() string {
	if x != nil {
		return x.MotherMiddleName
	}
	return ""
}

func (x *CustomerData) GetMotherLastName() string {
	if x != nil {
		return x.MotherLastName
	}
	return ""
}

func (x *CustomerData) GetMinor() string {
	if x != nil {
		return x.Minor
	}
	return ""
}

func (x *CustomerData) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *CustomerData) GetMaritalStatus() string {
	if x != nil {
		return x.MaritalStatus
	}
	return ""
}

func (x *CustomerData) GetOccupationType() string {
	if x != nil {
		return x.OccupationType
	}
	return ""
}

func (x *CustomerData) GetOccupationTypeOther() string {
	if x != nil {
		return x.OccupationTypeOther
	}
	return ""
}

func (x *CustomerData) GetNatureOfBusiness() string {
	if x != nil {
		return x.NatureOfBusiness
	}
	return ""
}

func (x *CustomerData) GetNatureOfBusinessOther() string {
	if x != nil {
		return x.NatureOfBusinessOther
	}
	return ""
}

func (x *CustomerData) GetProofOfIdSubmitted() string {
	if x != nil {
		return x.ProofOfIdSubmitted
	}
	return ""
}

func (x *CustomerData) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

func (x *CustomerData) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *CustomerData) GetPersonalEmail() string {
	if x != nil {
		return x.PersonalEmail
	}
	return ""
}

func (x *CustomerData) GetWorkMobileIsd() string {
	if x != nil {
		return x.WorkMobileIsd
	}
	return ""
}

func (x *CustomerData) GetWorkMobileNumber() string {
	if x != nil {
		return x.WorkMobileNumber
	}
	return ""
}

func (x *CustomerData) GetPersonalMobileIsd() string {
	if x != nil {
		return x.PersonalMobileIsd
	}
	return ""
}

func (x *CustomerData) GetPersonalMobileNumber() string {
	if x != nil {
		return x.PersonalMobileNumber
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressCountry() string {
	if x != nil {
		return x.PermanentAddressCountry
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressZipCode() string {
	if x != nil {
		return x.PermanentAddressZipCode
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressLine1() string {
	if x != nil {
		return x.PermanentAddressLine1
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressLine2() string {
	if x != nil {
		return x.PermanentAddressLine2
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressLine3() string {
	if x != nil {
		return x.PermanentAddressLine3
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressDistrict() string {
	if x != nil {
		return x.PermanentAddressDistrict
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressCity() string {
	if x != nil {
		return x.PermanentAddressCity
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressState() string {
	if x != nil {
		return x.PermanentAddressState
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressDocument() string {
	if x != nil {
		return x.PermanentAddressDocument
	}
	return ""
}

func (x *CustomerData) GetPermanentAddressDocumentOthersValue() string {
	if x != nil {
		return x.PermanentAddressDocumentOthersValue
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressCountry() string {
	if x != nil {
		return x.CorrespondenceAddressCountry
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressZipCode() string {
	if x != nil {
		return x.CorrespondenceAddressZipCode
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressLine1() string {
	if x != nil {
		return x.CorrespondenceAddressLine1
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressLine2() string {
	if x != nil {
		return x.CorrespondenceAddressLine2
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressLine3() string {
	if x != nil {
		return x.CorrespondenceAddressLine3
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressDistrict() string {
	if x != nil {
		return x.CorrespondenceAddressDistrict
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressCity() string {
	if x != nil {
		return x.CorrespondenceAddressCity
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressState() string {
	if x != nil {
		return x.CorrespondenceAddressState
	}
	return ""
}

func (x *CustomerData) GetCorrespondenceAddressDocument() string {
	if x != nil {
		return x.CorrespondenceAddressDocument
	}
	return ""
}

func (x *CustomerData) GetCountryOfResidence() string {
	if x != nil {
		return x.CountryOfResidence
	}
	return ""
}

func (x *CustomerData) GetCountryOfBirth() string {
	if x != nil {
		return x.CountryOfBirth
	}
	return ""
}

func (x *CustomerData) GetBirthCity() string {
	if x != nil {
		return x.BirthCity
	}
	return ""
}

func (x *CustomerData) GetPassportIssueCountry() string {
	if x != nil {
		return x.PassportIssueCountry
	}
	return ""
}

func (x *CustomerData) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *CustomerData) GetPassportExpiryDate() string {
	if x != nil {
		return x.PassportExpiryDate
	}
	return ""
}

func (x *CustomerData) GetVoterIdNumber() string {
	if x != nil {
		return x.VoterIdNumber
	}
	return ""
}

func (x *CustomerData) GetDrivingLicenseNumber() string {
	if x != nil {
		return x.DrivingLicenseNumber
	}
	return ""
}

func (x *CustomerData) GetDrivingLicenseExpiryDate() string {
	if x != nil {
		return x.DrivingLicenseExpiryDate
	}
	return ""
}

func (x *CustomerData) GetAadhaarNumber() string {
	if x != nil {
		return x.AadhaarNumber
	}
	return ""
}

func (x *CustomerData) GetAadhaarVaultReferenceNumber() string {
	if x != nil {
		return x.AadhaarVaultReferenceNumber
	}
	return ""
}

func (x *CustomerData) GetNregaNumber() string {
	if x != nil {
		return x.NregaNumber
	}
	return ""
}

func (x *CustomerData) GetNprLetterNumber() string {
	if x != nil {
		return x.NprLetterNumber
	}
	return ""
}

func (x *CustomerData) GetDirectorIdentificationNumber() string {
	if x != nil {
		return x.DirectorIdentificationNumber
	}
	return ""
}

func (x *CustomerData) GetFormSixty() string {
	if x != nil {
		return x.FormSixty
	}
	return ""
}

func (x *CustomerData) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *CustomerData) GetCkycNumber() string {
	if x != nil {
		return x.CkycNumber
	}
	return ""
}

func (x *CustomerData) GetPoliticallyExposed() string {
	if x != nil {
		return x.PoliticallyExposed
	}
	return ""
}

func (x *CustomerData) GetAdverseReputationDetails() string {
	if x != nil {
		return x.AdverseReputationDetails
	}
	return ""
}

func (x *CustomerData) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *CustomerData) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *CustomerData) GetScreeningProfile() string {
	if x != nil {
		return x.ScreeningProfile
	}
	return ""
}

func (x *CustomerData) GetScreeningReportWhenNil() string {
	if x != nil {
		return x.ScreeningReportWhenNil
	}
	return ""
}

func (x *CustomerData) GetRiskProfile() string {
	if x != nil {
		return x.RiskProfile
	}
	return ""
}

func (x *CustomerData) GetAdverseReputation() string {
	if x != nil {
		return x.AdverseReputation
	}
	return ""
}

func (x *CustomerData) GetAdverseReputationClassification() string {
	if x != nil {
		return x.AdverseReputationClassification
	}
	return ""
}

func (x *CustomerData) GetTaxDetailDtoList() []*TaxDetail {
	if x != nil {
		return x.TaxDetailDtoList
	}
	return nil
}

func (x *CustomerData) GetPoliticallyExposedClassification() string {
	if x != nil {
		return x.PoliticallyExposedClassification
	}
	return ""
}

func (x *CustomerData) GetCitizenships() string {
	if x != nil {
		return x.Citizenships
	}
	return ""
}

func (x *CustomerData) GetNationalities() string {
	if x != nil {
		return x.Nationalities
	}
	return ""
}

func (x *CustomerData) GetRegAmlRiskSpecialCategoryDtoList() []*RegAMLRiskSpecialCategory {
	if x != nil {
		return x.RegAmlRiskSpecialCategoryDtoList
	}
	return nil
}

func (x *CustomerData) GetRelatedPersonList() []*RelatedPerson {
	if x != nil {
		return x.RelatedPersonList
	}
	return nil
}

func (x *CustomerData) GetCustomerRelationDtoList() []*CustomerRelation {
	if x != nil {
		return x.CustomerRelationDtoList
	}
	return nil
}

func (x *CustomerData) GetConstitutionTypeId() string {
	if x != nil {
		return x.ConstitutionTypeId
	}
	return ""
}

func (x *CustomerData) GetCompanyIdentificationNumber() string {
	if x != nil {
		return x.CompanyIdentificationNumber
	}
	return ""
}

func (x *CustomerData) GetCompanyRegistrationNumber() string {
	if x != nil {
		return x.CompanyRegistrationNumber
	}
	return ""
}

func (x *CustomerData) GetCompanyRegistrationCountry() string {
	if x != nil {
		return x.CompanyRegistrationCountry
	}
	return ""
}

func (x *CustomerData) GetGlobalIntermediaryIdentificationNumber() string {
	if x != nil {
		return x.GlobalIntermediaryIdentificationNumber
	}
	return ""
}

func (x *CustomerData) GetKycAttestationType() string {
	if x != nil {
		return x.KycAttestationType
	}
	return ""
}

func (x *CustomerData) GetKycDateOfDeclaration() string {
	if x != nil {
		return x.KycDateOfDeclaration
	}
	return ""
}

func (x *CustomerData) GetKycPlaceOfDeclaration() string {
	if x != nil {
		return x.KycPlaceOfDeclaration
	}
	return ""
}

func (x *CustomerData) GetKycVerificationDate() string {
	if x != nil {
		return x.KycVerificationDate
	}
	return ""
}

func (x *CustomerData) GetKycEmployeeName() string {
	if x != nil {
		return x.KycEmployeeName
	}
	return ""
}

func (x *CustomerData) GetKycEmployeeDesignation() string {
	if x != nil {
		return x.KycEmployeeDesignation
	}
	return ""
}

func (x *CustomerData) GetKycVerificationBranch() string {
	if x != nil {
		return x.KycVerificationBranch
	}
	return ""
}

func (x *CustomerData) GetKycEmployeeCode() string {
	if x != nil {
		return x.KycEmployeeCode
	}
	return ""
}

func (x *CustomerData) GetListed() string {
	if x != nil {
		return x.Listed
	}
	return ""
}

func (x *CustomerData) GetApplicationRefNumber() string {
	if x != nil {
		return x.ApplicationRefNumber
	}
	return ""
}

func (x *CustomerData) GetDocumentRefNumber() string {
	if x != nil {
		return x.DocumentRefNumber
	}
	return ""
}

func (x *CustomerData) GetRegAmlRisk() string {
	if x != nil {
		return x.RegAmlRisk
	}
	return ""
}

func (x *CustomerData) GetRegAmlRiskLastRiskReviewDate() string {
	if x != nil {
		return x.RegAmlRiskLastRiskReviewDate
	}
	return ""
}

func (x *CustomerData) GetRegAmlRiskNextRiskReviewDate() string {
	if x != nil {
		return x.RegAmlRiskNextRiskReviewDate
	}
	return ""
}

func (x *CustomerData) GetIncomeRange() string {
	if x != nil {
		return x.IncomeRange
	}
	return ""
}

func (x *CustomerData) GetExactIncome() float64 {
	if x != nil {
		return x.ExactIncome
	}
	return 0
}

func (x *CustomerData) GetIncomeCurrency() string {
	if x != nil {
		return x.IncomeCurrency
	}
	return ""
}

func (x *CustomerData) GetIncomeEffectiveDate() string {
	if x != nil {
		return x.IncomeEffectiveDate
	}
	return ""
}

func (x *CustomerData) GetIncomeDescription() string {
	if x != nil {
		return x.IncomeDescription
	}
	return ""
}

func (x *CustomerData) GetIncomeDocument() string {
	if x != nil {
		return x.IncomeDocument
	}
	return ""
}

func (x *CustomerData) GetExactNetworth() float64 {
	if x != nil {
		return x.ExactNetworth
	}
	return 0
}

func (x *CustomerData) GetNetworthCurrency() string {
	if x != nil {
		return x.NetworthCurrency
	}
	return ""
}

func (x *CustomerData) GetNetworthEffectiveDate() string {
	if x != nil {
		return x.NetworthEffectiveDate
	}
	return ""
}

func (x *CustomerData) GetNetworthDescription() string {
	if x != nil {
		return x.NetworthDescription
	}
	return ""
}

func (x *CustomerData) GetNetworthDocument() string {
	if x != nil {
		return x.NetworthDocument
	}
	return ""
}

func (x *CustomerData) GetFamilyCode() string {
	if x != nil {
		return x.FamilyCode
	}
	return ""
}

func (x *CustomerData) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *CustomerData) GetContactPersonFirstName1() string {
	if x != nil {
		return x.ContactPersonFirstName1
	}
	return ""
}

func (x *CustomerData) GetContactPersonMiddleName1() string {
	if x != nil {
		return x.ContactPersonMiddleName1
	}
	return ""
}

func (x *CustomerData) GetContactPersonLastName1() string {
	if x != nil {
		return x.ContactPersonLastName1
	}
	return ""
}

func (x *CustomerData) GetContactPersonDesignation1() string {
	if x != nil {
		return x.ContactPersonDesignation1
	}
	return ""
}

func (x *CustomerData) GetContactPersonFirstName2() string {
	if x != nil {
		return x.ContactPersonFirstName2
	}
	return ""
}

func (x *CustomerData) GetContactPersonMiddleName2() string {
	if x != nil {
		return x.ContactPersonMiddleName2
	}
	return ""
}

func (x *CustomerData) GetContactPersonLastName2() string {
	if x != nil {
		return x.ContactPersonLastName2
	}
	return ""
}

func (x *CustomerData) GetContactPersonDesignation2() string {
	if x != nil {
		return x.ContactPersonDesignation2
	}
	return ""
}

func (x *CustomerData) GetContactPersonMobileIsd() string {
	if x != nil {
		return x.ContactPersonMobileIsd
	}
	return ""
}

func (x *CustomerData) GetContactPersonMobileNo() string {
	if x != nil {
		return x.ContactPersonMobileNo
	}
	return ""
}

func (x *CustomerData) GetContactPersonMobileIsd2() string {
	if x != nil {
		return x.ContactPersonMobileIsd2
	}
	return ""
}

func (x *CustomerData) GetContactPersonMobileNo2() string {
	if x != nil {
		return x.ContactPersonMobileNo2
	}
	return ""
}

func (x *CustomerData) GetContactPersonEmailId1() string {
	if x != nil {
		return x.ContactPersonEmailId1
	}
	return ""
}

func (x *CustomerData) GetContactPersonEmailId2() string {
	if x != nil {
		return x.ContactPersonEmailId2
	}
	return ""
}

func (x *CustomerData) GetCommencementDate() string {
	if x != nil {
		return x.CommencementDate
	}
	return ""
}

func (x *CustomerData) GetMaidenPrefix() string {
	if x != nil {
		return x.MaidenPrefix
	}
	return ""
}

func (x *CustomerData) GetMaidenFirstName() string {
	if x != nil {
		return x.MaidenFirstName
	}
	return ""
}

func (x *CustomerData) GetMaidenMiddleName() string {
	if x != nil {
		return x.MaidenMiddleName
	}
	return ""
}

func (x *CustomerData) GetMaidenLastName() string {
	if x != nil {
		return x.MaidenLastName
	}
	return ""
}

func (x *CustomerData) GetRelatedPersonCountForCkyc() int32 {
	if x != nil {
		return x.RelatedPersonCountForCkyc
	}
	return 0
}

func (x *CustomerData) GetEducationalQualification() string {
	if x != nil {
		return x.EducationalQualification
	}
	return ""
}

func (x *CustomerData) GetCountryOfOperations() string {
	if x != nil {
		return x.CountryOfOperations
	}
	return ""
}

func (x *CustomerData) GetIdentityDocument() []*IdentityDocument {
	if x != nil {
		return x.IdentityDocument
	}
	return nil
}

// Tax detail structure
type TaxDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaxResidencyCountry     string `protobuf:"bytes,1,opt,name=tax_residency_country,json=taxResidencyCountry,proto3" json:"tax_residency_country,omitempty"`
	TaxIdentificationNumber string `protobuf:"bytes,2,opt,name=tax_identification_number,json=taxIdentificationNumber,proto3" json:"tax_identification_number,omitempty"`
	TaxResidencyStartDate   string `protobuf:"bytes,3,opt,name=tax_residency_start_date,json=taxResidencyStartDate,proto3" json:"tax_residency_start_date,omitempty"`
	TaxResidencyEndDate     string `protobuf:"bytes,4,opt,name=tax_residency_end_date,json=taxResidencyEndDate,proto3" json:"tax_residency_end_date,omitempty"`
}

func (x *TaxDetail) Reset() {
	*x = TaxDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaxDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxDetail) ProtoMessage() {}

func (x *TaxDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxDetail.ProtoReflect.Descriptor instead.
func (*TaxDetail) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{4}
}

func (x *TaxDetail) GetTaxResidencyCountry() string {
	if x != nil {
		return x.TaxResidencyCountry
	}
	return ""
}

func (x *TaxDetail) GetTaxIdentificationNumber() string {
	if x != nil {
		return x.TaxIdentificationNumber
	}
	return ""
}

func (x *TaxDetail) GetTaxResidencyStartDate() string {
	if x != nil {
		return x.TaxResidencyStartDate
	}
	return ""
}

func (x *TaxDetail) GetTaxResidencyEndDate() string {
	if x != nil {
		return x.TaxResidencyEndDate
	}
	return ""
}

// RegAML Risk Special Category structure
type RegAMLRiskSpecialCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegAmlRiskSpecialCategory          string `protobuf:"bytes,1,opt,name=reg_aml_risk_special_category,json=regAMLRiskSpecialCategory,proto3" json:"reg_aml_risk_special_category,omitempty"`
	RegAmlRiskSpecialCategoryStartDate string `protobuf:"bytes,2,opt,name=reg_aml_risk_special_category_start_date,json=regAMLRiskSpecialCategoryStartDate,proto3" json:"reg_aml_risk_special_category_start_date,omitempty"`
}

func (x *RegAMLRiskSpecialCategory) Reset() {
	*x = RegAMLRiskSpecialCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegAMLRiskSpecialCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegAMLRiskSpecialCategory) ProtoMessage() {}

func (x *RegAMLRiskSpecialCategory) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegAMLRiskSpecialCategory.ProtoReflect.Descriptor instead.
func (*RegAMLRiskSpecialCategory) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{5}
}

func (x *RegAMLRiskSpecialCategory) GetRegAmlRiskSpecialCategory() string {
	if x != nil {
		return x.RegAmlRiskSpecialCategory
	}
	return ""
}

func (x *RegAMLRiskSpecialCategory) GetRegAmlRiskSpecialCategoryStartDate() string {
	if x != nil {
		return x.RegAmlRiskSpecialCategoryStartDate
	}
	return ""
}

// Related Person structure
type RelatedPerson struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceSystemCustomerCode string `protobuf:"bytes,1,opt,name=source_system_customer_code,json=sourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	UniqueIdentifier         string `protobuf:"bytes,2,opt,name=unique_identifier,json=uniqueIdentifier,proto3" json:"unique_identifier,omitempty"`
	Prefix                   string `protobuf:"bytes,3,opt,name=prefix,proto3" json:"prefix,omitempty"`
	FirstName                string `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	MiddleName               string `protobuf:"bytes,5,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	LastName                 string `protobuf:"bytes,6,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Gender                   string `protobuf:"bytes,7,opt,name=gender,proto3" json:"gender,omitempty"`
	DateOfBirth              string `protobuf:"bytes,8,opt,name=date_of_birth,json=dateofBirth,proto3" json:"date_of_birth,omitempty"`
	Nationality              string `protobuf:"bytes,9,opt,name=nationality,proto3" json:"nationality,omitempty"`
	PassportNumber           string `protobuf:"bytes,10,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	Pan                      string `protobuf:"bytes,11,opt,name=pan,proto3" json:"pan,omitempty"`
	PoliticallyExposed       string `protobuf:"bytes,12,opt,name=politically_exposed,json=politicallyExposed,proto3" json:"politically_exposed,omitempty"`
	AdverseReputation        string `protobuf:"bytes,13,opt,name=adverse_reputation,json=adverseReputation,proto3" json:"adverse_reputation,omitempty"`
	Notes                    string `protobuf:"bytes,14,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *RelatedPerson) Reset() {
	*x = RelatedPerson{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelatedPerson) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedPerson) ProtoMessage() {}

func (x *RelatedPerson) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedPerson.ProtoReflect.Descriptor instead.
func (*RelatedPerson) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{6}
}

func (x *RelatedPerson) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *RelatedPerson) GetUniqueIdentifier() string {
	if x != nil {
		return x.UniqueIdentifier
	}
	return ""
}

func (x *RelatedPerson) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *RelatedPerson) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *RelatedPerson) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *RelatedPerson) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *RelatedPerson) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *RelatedPerson) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

func (x *RelatedPerson) GetNationality() string {
	if x != nil {
		return x.Nationality
	}
	return ""
}

func (x *RelatedPerson) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *RelatedPerson) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *RelatedPerson) GetPoliticallyExposed() string {
	if x != nil {
		return x.PoliticallyExposed
	}
	return ""
}

func (x *RelatedPerson) GetAdverseReputation() string {
	if x != nil {
		return x.AdverseReputation
	}
	return ""
}

func (x *RelatedPerson) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type CustomerRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceSystemCustomerCode              string `protobuf:"bytes,1,opt,name=source_system_customer_code,json=sourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	RelatedPersonSourceSystemCustomerCode string `protobuf:"bytes,2,opt,name=related_person_source_system_customer_code,json=relatedPersonSourceSystemCustomerCode,proto3" json:"related_person_source_system_customer_code,omitempty"`
	Relation                              string `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
	RelationNotes                         string `protobuf:"bytes,4,opt,name=relation_notes,json=relationNotes,proto3" json:"relation_notes,omitempty"`
}

func (x *CustomerRelation) Reset() {
	*x = CustomerRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerRelation) ProtoMessage() {}

func (x *CustomerRelation) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerRelation.ProtoReflect.Descriptor instead.
func (*CustomerRelation) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerRelation) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *CustomerRelation) GetRelatedPersonSourceSystemCustomerCode() string {
	if x != nil {
		return x.RelatedPersonSourceSystemCustomerCode
	}
	return ""
}

func (x *CustomerRelation) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *CustomerRelation) GetRelationNotes() string {
	if x != nil {
		return x.RelationNotes
	}
	return ""
}

type IdentityDocument struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocumentType         string `protobuf:"bytes,1,opt,name=document_type,json=documentType,proto3" json:"document_type,omitempty"`
	DocumentNumber       string `protobuf:"bytes,2,opt,name=document_number,json=documentNumber,proto3" json:"document_number,omitempty"`
	DocumentIssueDate    string `protobuf:"bytes,3,opt,name=document_issue_date,json=documentIssueDate,proto3" json:"document_issue_date,omitempty"`
	DocumentExpiryDate   string `protobuf:"bytes,4,opt,name=document_expiry_date,json=documentExpiryDate,proto3" json:"document_expiry_date,omitempty"`
	DocumentIssueCountry string `protobuf:"bytes,5,opt,name=document_issue_country,json=documentIssueCountry,proto3" json:"document_issue_country,omitempty"`
}

func (x *IdentityDocument) Reset() {
	*x = IdentityDocument{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityDocument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityDocument) ProtoMessage() {}

func (x *IdentityDocument) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityDocument.ProtoReflect.Descriptor instead.
func (*IdentityDocument) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{8}
}

func (x *IdentityDocument) GetDocumentType() string {
	if x != nil {
		return x.DocumentType
	}
	return ""
}

func (x *IdentityDocument) GetDocumentNumber() string {
	if x != nil {
		return x.DocumentNumber
	}
	return ""
}

func (x *IdentityDocument) GetDocumentIssueDate() string {
	if x != nil {
		return x.DocumentIssueDate
	}
	return ""
}

func (x *IdentityDocument) GetDocumentExpiryDate() string {
	if x != nil {
		return x.DocumentExpiryDate
	}
	return ""
}

func (x *IdentityDocument) GetDocumentIssueCountry() string {
	if x != nil {
		return x.DocumentIssueCountry
	}
	return ""
}

type AS501Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId             string              `protobuf:"bytes,1,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	OverallStatus         string              `protobuf:"bytes,2,opt,name=overall_status,json=OverallStatus,proto3" json:"overall_status,omitempty"`
	ValidationCode        string              `protobuf:"bytes,3,opt,name=validation_code,json=ValidationCode,proto3" json:"validation_code,omitempty"`
	ValidationDescription string              `protobuf:"bytes,4,opt,name=validation_description,json=ValidationDescription,proto3" json:"validation_description,omitempty"`
	CustomerResponse      []*CustomerResponse `protobuf:"bytes,5,rep,name=customer_response,json=CustomerResponse,proto3" json:"customer_response,omitempty"`
}

func (x *AS501Response) Reset() {
	*x = AS501Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS501Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS501Response) ProtoMessage() {}

func (x *AS501Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS501Response.ProtoReflect.Descriptor instead.
func (*AS501Response) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{9}
}

func (x *AS501Response) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AS501Response) GetOverallStatus() string {
	if x != nil {
		return x.OverallStatus
	}
	return ""
}

func (x *AS501Response) GetValidationCode() string {
	if x != nil {
		return x.ValidationCode
	}
	return ""
}

func (x *AS501Response) GetValidationDescription() string {
	if x != nil {
		return x.ValidationDescription
	}
	return ""
}

func (x *AS501Response) GetCustomerResponse() []*CustomerResponse {
	if x != nil {
		return x.CustomerResponse
	}
	return nil
}

type CustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceSystemCustomerCode string             `protobuf:"bytes,1,opt,name=source_system_customer_code,json=SourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	ValidationOutcome        string             `protobuf:"bytes,2,opt,name=validation_outcome,json=ValidationOutcome,proto3" json:"validation_outcome,omitempty"`
	SuggestedAction          string             `protobuf:"bytes,3,opt,name=suggested_action,json=SuggestedAction,proto3" json:"suggested_action,omitempty"`
	PurposeResponse          []*PurposeResponse `protobuf:"bytes,4,rep,name=purpose_response,json=PurposeResponse,proto3" json:"purpose_response,omitempty"`
	ValidationCode           string             `protobuf:"bytes,5,opt,name=validation_code,json=ValidationCode,proto3" json:"validation_code,omitempty"`
	ValidationDescription    string             `protobuf:"bytes,6,opt,name=validation_description,json=ValidationDescription,proto3" json:"validation_description,omitempty"`
	ValidationFailureCount   int32              `protobuf:"varint,7,opt,name=validation_failure_count,json=ValidationFailureCount,proto3" json:"validation_failure_count,omitempty"`
}

func (x *CustomerResponse) Reset() {
	*x = CustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerResponse) ProtoMessage() {}

func (x *CustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerResponse.ProtoReflect.Descriptor instead.
func (*CustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerResponse) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *CustomerResponse) GetValidationOutcome() string {
	if x != nil {
		return x.ValidationOutcome
	}
	return ""
}

func (x *CustomerResponse) GetSuggestedAction() string {
	if x != nil {
		return x.SuggestedAction
	}
	return ""
}

func (x *CustomerResponse) GetPurposeResponse() []*PurposeResponse {
	if x != nil {
		return x.PurposeResponse
	}
	return nil
}

func (x *CustomerResponse) GetValidationCode() string {
	if x != nil {
		return x.ValidationCode
	}
	return ""
}

func (x *CustomerResponse) GetValidationDescription() string {
	if x != nil {
		return x.ValidationDescription
	}
	return ""
}

func (x *CustomerResponse) GetValidationFailureCount() int32 {
	if x != nil {
		return x.ValidationFailureCount
	}
	return 0
}

type PurposeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Purpose                string       `protobuf:"bytes,1,opt,name=purpose,json=Purpose,proto3" json:"purpose,omitempty"`
	PurposeCode            string       `protobuf:"bytes,2,opt,name=purpose_code,json=PurposeCode,proto3" json:"purpose_code,omitempty"`
	ValidationCode         string       `protobuf:"bytes,3,opt,name=validation_code,json=ValidationCode,proto3" json:"validation_code,omitempty"`
	ValidationDescription  string       `protobuf:"bytes,4,opt,name=validation_description,json=ValidationDescription,proto3" json:"validation_description,omitempty"`
	ValidationFailureCount int32        `protobuf:"varint,5,opt,name=validation_failure_count,json=ValidationFailureCount,proto3" json:"validation_failure_count,omitempty"`
	Data                   *PurposeData `protobuf:"bytes,6,opt,name=data,json=Data,proto3" json:"data,omitempty"`
}

func (x *PurposeResponse) Reset() {
	*x = PurposeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurposeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurposeResponse) ProtoMessage() {}

func (x *PurposeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurposeResponse.ProtoReflect.Descriptor instead.
func (*PurposeResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{11}
}

func (x *PurposeResponse) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *PurposeResponse) GetPurposeCode() string {
	if x != nil {
		return x.PurposeCode
	}
	return ""
}

func (x *PurposeResponse) GetValidationCode() string {
	if x != nil {
		return x.ValidationCode
	}
	return ""
}

func (x *PurposeResponse) GetValidationDescription() string {
	if x != nil {
		return x.ValidationDescription
	}
	return ""
}

func (x *PurposeResponse) GetValidationFailureCount() int32 {
	if x != nil {
		return x.ValidationFailureCount
	}
	return 0
}

func (x *PurposeResponse) GetData() *PurposeData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PurposeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitsDetected string `protobuf:"bytes,1,opt,name=hits_detected,json=HitsDetected,proto3" json:"hits_detected,omitempty"`
	HitsCount    int32  `protobuf:"varint,2,opt,name=hits_count,json=HitsCount,proto3" json:"hits_count,omitempty"`
	ConfirmedHit string `protobuf:"bytes,3,opt,name=confirmed_hit,json=ConfirmedHit,proto3" json:"confirmed_hit,omitempty"`
	ReportData   string `protobuf:"bytes,4,opt,name=report_data,json=ReportData,proto3" json:"report_data,omitempty"`
}

func (x *PurposeData) Reset() {
	*x = PurposeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as501_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurposeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurposeData) ProtoMessage() {}

func (x *PurposeData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as501_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurposeData.ProtoReflect.Descriptor instead.
func (*PurposeData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as501_proto_rawDescGZIP(), []int{12}
}

func (x *PurposeData) GetHitsDetected() string {
	if x != nil {
		return x.HitsDetected
	}
	return ""
}

func (x *PurposeData) GetHitsCount() int32 {
	if x != nil {
		return x.HitsCount
	}
	return 0
}

func (x *PurposeData) GetConfirmedHit() string {
	if x != nil {
		return x.ConfirmedHit
	}
	return ""
}

func (x *PurposeData) GetReportData() string {
	if x != nil {
		return x.ReportData
	}
	return ""
}

var File_api_vendors_tss_as501_proto protoreflect.FileDescriptor

var file_api_vendors_tss_as501_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73,
	0x73, 0x2f, 0x61, 0x73, 0x35, 0x30, 0x31, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x22, 0x9d, 0x01, 0x0a, 0x10, 0x45,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x11, 0x45, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0xb8, 0x02, 0x0a, 0x0c,
	0x41, 0x53, 0x35, 0x30, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70,
	0x6f, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x70, 0x69,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70,
	0x69, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x9a, 0x3b, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x24, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x20, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6f, 0x74, 0x70, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6b, 0x79,
	0x63, 0x4f, 0x54, 0x50, 0x62, 0x61, 0x73, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x11,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x69, 0x74,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x6c, 0x69, 0x61, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x46, 0x69, 0x72, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x50, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73,
	0x70, 0x6f, 0x75, 0x73, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x70, 0x6f, 0x75,
	0x73, 0x65, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x4c, 0x61,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x2a, 0x0a, 0x11, 0x6d,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a,
	0x15, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x63,
	0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x74, 0x68, 0x65,
	0x72, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x66, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x37, 0x0a, 0x18, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x66, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x6f,
	0x66, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65,
	0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x4f, 0x66,
	0x49, 0x64, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x6f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12,
	0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x25, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x73, 0x64, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x53, 0x44, 0x12, 0x2c, 0x0a,
	0x12, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x69,
	0x73, 0x64, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x53, 0x44, 0x12, 0x34, 0x0a, 0x16, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x3a, 0x0a, 0x19, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x2b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x3b, 0x0a,
	0x1a, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x17, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65,
	0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x65, 0x72,
	0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e,
	0x65, 0x31, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x2e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65,
	0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x33, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x65, 0x72,
	0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e,
	0x65, 0x33, 0x12, 0x3c, 0x0a, 0x1a, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x12, 0x34, 0x0a, 0x16, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65,
	0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3c,
	0x0a, 0x1a, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x27,
	0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x23, 0x70,
	0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x44, 0x0a, 0x1e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x45, 0x0a, 0x1f, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x36, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x40, 0x0a, 0x1c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18,
	0x37, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65,
	0x31, 0x12, 0x40, 0x0a, 0x1c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65,
	0x32, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69,
	0x6e, 0x65, 0x32, 0x12, 0x40, 0x0a, 0x1c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x33, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x6e, 0x65, 0x33, 0x12, 0x46, 0x0a, 0x1f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x3e, 0x0a,
	0x1b, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x3b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x19, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x12, 0x40, 0x0a,
	0x1c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x3c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x46, 0x0a, 0x1f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4f, 0x66,
	0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x3f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4f, 0x66, 0x42, 0x69,
	0x72, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x40, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x43, 0x69,
	0x74, 0x79, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x41, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x73, 0x73, 0x75,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x42, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x6f,
	0x74, 0x65, 0x72, 0x49, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x64,
	0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x45, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x64, 0x72, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x46, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x47, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x1e, 0x61, 0x61, 0x64, 0x68, 0x61,
	0x61, 0x72, 0x5f, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x48, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x1b, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x56, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x6e, 0x72, 0x65, 0x67, 0x61, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x49, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6e, 0x72, 0x65, 0x67, 0x61, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x2a, 0x0a, 0x11, 0x6e, 0x70, 0x72, 0x5f, 0x6c, 0x65, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x70, 0x72, 0x4c,
	0x65, 0x74, 0x74, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x1e, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x4b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x1c, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x69, 0x78, 0x74, 0x79, 0x18,
	0x4c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x69, 0x78, 0x74, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x61, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6b, 0x79, 0x63, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x6c, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x4f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x5f,
	0x72, 0x65, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x50, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73,
	0x65, 0x52, 0x65, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x51, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x52, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x53, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69,
	0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x77, 0x68,
	0x65, 0x6e, 0x5f, 0x6e, 0x69, 0x6c, 0x18, 0x54, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x77, 0x68, 0x65,
	0x6e, 0x6e, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x55, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x69, 0x73, 0x6b,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x73, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x56, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x52, 0x65, 0x70, 0x75,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x21, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73,
	0x65, 0x5f, 0x72, 0x65, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x57, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x52, 0x65, 0x70, 0x75, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x13, 0x74, 0x61, 0x78, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x64, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x58, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x54, 0x61,
	0x78, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x10, 0x74, 0x61, 0x78, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x44, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x22, 0x70, 0x6f, 0x6c,
	0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64,
	0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x59, 0x20, 0x01, 0x28, 0x09, 0x52, 0x20, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x69, 0x74, 0x69, 0x7a,
	0x65, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x73, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x69, 0x74, 0x69, 0x7a, 0x65, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x5b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x78, 0x0a, 0x26, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x6d, 0x6c, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x64, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x5c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e,
	0x52, 0x65, 0x67, 0x41, 0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x20, 0x72, 0x65, 0x67, 0x41, 0x4d,
	0x4c, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x44, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x13, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x52, 0x11, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x1a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x74, 0x6f,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x17, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x74, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x5f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x1b, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x1c, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x62, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x1a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x59, 0x0a, 0x29, 0x67,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x26,
	0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x72, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x74,
	0x74, 0x65, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6b, 0x79, 0x63, 0x41, 0x74, 0x74, 0x65, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6b, 0x79, 0x63, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6b, 0x79, 0x63, 0x44, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x0a, 0x18, 0x6b, 0x79, 0x63, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x6f, 0x66, 0x5f,
	0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x66, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6b, 0x79, 0x63, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x66, 0x44, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x6b, 0x79, 0x63, 0x5f,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x67, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6b, 0x79, 0x63, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x6b, 0x79, 0x63, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x68, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x79, 0x63, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x65, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x6b, 0x79, 0x63, 0x5f,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x69, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6b, 0x79, 0x63, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x6b, 0x79, 0x63, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x6a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x6b, 0x79, 0x63, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x79,
	0x63, 0x5f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x6b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x79, 0x63, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x65, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64,
	0x18, 0x6c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x12, 0x34,
	0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x6d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x6e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x6d, 0x6c, 0x5f,
	0x72, 0x69, 0x73, 0x6b, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x41,
	0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x48, 0x0a, 0x22, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x6d,
	0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x70, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1c, 0x72, 0x65, 0x67, 0x41, 0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x61,
	0x73, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x48, 0x0a, 0x22, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x6d, 0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x71, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x72, 0x65,
	0x67, 0x41, 0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x69, 0x73, 0x6b,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x72, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x65, 0x78, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x73, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0b, 0x65, 0x78, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x74, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x75, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x76, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f,
	0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x77, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x61, 0x63, 0x74, 0x5f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x18, 0x78, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x65,
	0x78, 0x61, 0x63, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x12, 0x2b, 0x0a, 0x11,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x79, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x7a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x31, 0x0a, 0x14, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x7b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x7c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x7d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x7e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x3b, 0x0a, 0x1a,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x18, 0x7f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x12, 0x3e, 0x0a, 0x1b, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x64, 0x64,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x18, 0x80, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x12, 0x3a, 0x0a, 0x19, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x18, 0x81, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4c, 0x61, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x31, 0x12, 0x3f, 0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x31, 0x18, 0x82, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x31, 0x12, 0x3c, 0x0a, 0x1a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x32, 0x18, 0x83, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x32, 0x12, 0x3e, 0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f,
	0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x32, 0x18, 0x84, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x32, 0x12, 0x3a, 0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f,
	0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x32, 0x18, 0x85, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x32,
	0x12, 0x3f, 0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x18,
	0x86, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x12, 0x3a, 0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x73, 0x64, 0x18, 0x87,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x53, 0x44, 0x12, 0x38, 0x0a,
	0x18, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x88, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x6f, 0x12, 0x3c, 0x0a, 0x1a, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x5f, 0x69, 0x73, 0x64, 0x32, 0x18, 0x89, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x49, 0x53, 0x44, 0x32, 0x12, 0x3a, 0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x6f, 0x32, 0x18, 0x8a, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x6f,
	0x32, 0x12, 0x38, 0x0a, 0x18, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x31, 0x18, 0x8b, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x31, 0x12, 0x38, 0x0a, 0x18, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x32, 0x18, 0x8c, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x32, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x8d, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f, 0x70, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x18, 0x8e, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69,
	0x64, 0x65, 0x6e, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x61, 0x69,
	0x64, 0x65, 0x6e, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x8f,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e,
	0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x90, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x4d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x91, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x41, 0x0a, 0x1d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x6b, 0x79,
	0x63, 0x18, 0x92, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x66, 0x6f, 0x72, 0x43,
	0x4b, 0x59, 0x43, 0x12, 0x3c, 0x0a, 0x19, 0x65, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x93, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x65, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x33, 0x0a, 0x15, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6f, 0x66, 0x5f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x94, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4b, 0x0a, 0x11, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x95, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73,
	0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x22, 0xe9, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x78, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x74, 0x61, 0x78, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x3a, 0x0a, 0x19, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x74, 0x61, 0x78, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x74, 0x61, 0x78, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x74, 0x61,
	0x78, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x61, 0x78, 0x52,
	0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22,
	0xb3, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x67, 0x41, 0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x40, 0x0a,
	0x1d, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x6d, 0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x72, 0x65, 0x67, 0x41, 0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x54, 0x0a, 0x28, 0x72, 0x65, 0x67, 0x5f, 0x61, 0x6d, 0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x22, 0x72, 0x65, 0x67, 0x41, 0x4d, 0x4c, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xff, 0x03, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x22, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x6f, 0x66, 0x42,
	0x69, 0x72, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61,
	0x6e, 0x12, 0x2f, 0x0a, 0x13, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79,
	0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73,
	0x65, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x5f, 0x72, 0x65,
	0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x73, 0x65, 0x52, 0x65, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22, 0xef, 0x01, 0x0a, 0x10, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x1b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x18, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x59, 0x0a, 0x2a, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x25, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x6f, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x10, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34,
	0x0a, 0x16, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x22, 0x81, 0x02, 0x0a, 0x0d, 0x41, 0x53, 0x35, 0x30, 0x31, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4f,
	0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x11,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x10, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8e, 0x03, 0x0a, 0x10, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a,
	0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x12,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x73,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x10, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0f,
	0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x38, 0x0a, 0x18, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x16, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x96, 0x02, 0x0a, 0x0f, 0x50, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73,
	0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x97, 0x01, 0x0a, 0x0b, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x69, 0x74, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x48, 0x69, 0x74, 0x73, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x74, 0x73, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x48, 0x69, 0x74,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x65, 0x64, 0x5f, 0x68, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x48, 0x69, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x50, 0x0a, 0x26,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_tss_as501_proto_rawDescOnce sync.Once
	file_api_vendors_tss_as501_proto_rawDescData = file_api_vendors_tss_as501_proto_rawDesc
)

func file_api_vendors_tss_as501_proto_rawDescGZIP() []byte {
	file_api_vendors_tss_as501_proto_rawDescOnce.Do(func() {
		file_api_vendors_tss_as501_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_tss_as501_proto_rawDescData)
	})
	return file_api_vendors_tss_as501_proto_rawDescData
}

var file_api_vendors_tss_as501_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_vendors_tss_as501_proto_goTypes = []interface{}{
	(*EncryptedRequest)(nil),          // 0: vendors.tss.EncryptedRequest
	(*EncryptedResponse)(nil),         // 1: vendors.tss.EncryptedResponse
	(*AS501Request)(nil),              // 2: vendors.tss.AS501Request
	(*CustomerData)(nil),              // 3: vendors.tss.CustomerData
	(*TaxDetail)(nil),                 // 4: vendors.tss.TaxDetail
	(*RegAMLRiskSpecialCategory)(nil), // 5: vendors.tss.RegAMLRiskSpecialCategory
	(*RelatedPerson)(nil),             // 6: vendors.tss.RelatedPerson
	(*CustomerRelation)(nil),          // 7: vendors.tss.CustomerRelation
	(*IdentityDocument)(nil),          // 8: vendors.tss.IdentityDocument
	(*AS501Response)(nil),             // 9: vendors.tss.AS501Response
	(*CustomerResponse)(nil),          // 10: vendors.tss.CustomerResponse
	(*PurposeResponse)(nil),           // 11: vendors.tss.PurposeResponse
	(*PurposeData)(nil),               // 12: vendors.tss.PurposeData
}
var file_api_vendors_tss_as501_proto_depIdxs = []int32{
	3,  // 0: vendors.tss.AS501Request.customer_list:type_name -> vendors.tss.CustomerData
	4,  // 1: vendors.tss.CustomerData.tax_detail_dto_list:type_name -> vendors.tss.TaxDetail
	5,  // 2: vendors.tss.CustomerData.reg_aml_risk_special_category_dto_list:type_name -> vendors.tss.RegAMLRiskSpecialCategory
	6,  // 3: vendors.tss.CustomerData.related_person_list:type_name -> vendors.tss.RelatedPerson
	7,  // 4: vendors.tss.CustomerData.customer_relation_dto_list:type_name -> vendors.tss.CustomerRelation
	8,  // 5: vendors.tss.CustomerData.identity_document:type_name -> vendors.tss.IdentityDocument
	10, // 6: vendors.tss.AS501Response.customer_response:type_name -> vendors.tss.CustomerResponse
	11, // 7: vendors.tss.CustomerResponse.purpose_response:type_name -> vendors.tss.PurposeResponse
	12, // 8: vendors.tss.PurposeResponse.data:type_name -> vendors.tss.PurposeData
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_api_vendors_tss_as501_proto_init() }
func file_api_vendors_tss_as501_proto_init() {
	if File_api_vendors_tss_as501_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_tss_as501_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EncryptedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EncryptedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS501Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaxDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegAMLRiskSpecialCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelatedPerson); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityDocument); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS501Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurposeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as501_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurposeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_tss_as501_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_tss_as501_proto_goTypes,
		DependencyIndexes: file_api_vendors_tss_as501_proto_depIdxs,
		MessageInfos:      file_api_vendors_tss_as501_proto_msgTypes,
	}.Build()
	File_api_vendors_tss_as501_proto = out.File
	file_api_vendors_tss_as501_proto_rawDesc = nil
	file_api_vendors_tss_as501_proto_goTypes = nil
	file_api_vendors_tss_as501_proto_depIdxs = nil
}
