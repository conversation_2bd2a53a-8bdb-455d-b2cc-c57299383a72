// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/investment/indianstocks/service.proto

package indianstocks

import (
	rpc "github.com/epifi/be-common/api/rpc"
	header "github.com/epifi/gamma/api/frontend/header"
	ui "github.com/epifi/gamma/api/frontend/investment/ui"
	ui1 "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetOrderReceiptResponse_Status int32

const (
	GetOrderReceiptResponse_OK GetOrderReceiptResponse_Status = 0
	// internal server error
	GetOrderReceiptResponse_INTERNAL GetOrderReceiptResponse_Status = 13
	// reciept not found
	GetOrderReceiptResponse_NOT_FOUND GetOrderReceiptResponse_Status = 5
)

// Enum value maps for GetOrderReceiptResponse_Status.
var (
	GetOrderReceiptResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "NOT_FOUND",
	}
	GetOrderReceiptResponse_Status_value = map[string]int32{
		"OK":        0,
		"INTERNAL":  13,
		"NOT_FOUND": 5,
	}
)

func (x GetOrderReceiptResponse_Status) Enum() *GetOrderReceiptResponse_Status {
	p := new(GetOrderReceiptResponse_Status)
	*p = x
	return p
}

func (x GetOrderReceiptResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrderReceiptResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_investment_indianstocks_service_proto_enumTypes[0].Descriptor()
}

func (GetOrderReceiptResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_investment_indianstocks_service_proto_enumTypes[0]
}

func (x GetOrderReceiptResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrderReceiptResponse_Status.Descriptor instead.
func (GetOrderReceiptResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetInvestmentActivitiesResponse_Status int32

const (
	GetInvestmentActivitiesResponse_OK GetInvestmentActivitiesResponse_Status = 0
	// internal server error
	GetInvestmentActivitiesResponse_INTERNAL GetInvestmentActivitiesResponse_Status = 13
)

// Enum value maps for GetInvestmentActivitiesResponse_Status.
var (
	GetInvestmentActivitiesResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetInvestmentActivitiesResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetInvestmentActivitiesResponse_Status) Enum() *GetInvestmentActivitiesResponse_Status {
	p := new(GetInvestmentActivitiesResponse_Status)
	*p = x
	return p
}

func (x GetInvestmentActivitiesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetInvestmentActivitiesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_investment_indianstocks_service_proto_enumTypes[1].Descriptor()
}

func (GetInvestmentActivitiesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_investment_indianstocks_service_proto_enumTypes[1]
}

func (x GetInvestmentActivitiesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetInvestmentActivitiesResponse_Status.Descriptor instead.
func (GetInvestmentActivitiesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetInvestmentInstrumentDetailsResponse_Status int32

const (
	GetInvestmentInstrumentDetailsResponse_OK GetInvestmentInstrumentDetailsResponse_Status = 0
	// internal server error
	GetInvestmentInstrumentDetailsResponse_INTERNAL GetInvestmentInstrumentDetailsResponse_Status = 13
	// not found
	GetInvestmentInstrumentDetailsResponse_NOT_FOUND GetInvestmentInstrumentDetailsResponse_Status = 5
)

// Enum value maps for GetInvestmentInstrumentDetailsResponse_Status.
var (
	GetInvestmentInstrumentDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "NOT_FOUND",
	}
	GetInvestmentInstrumentDetailsResponse_Status_value = map[string]int32{
		"OK":        0,
		"INTERNAL":  13,
		"NOT_FOUND": 5,
	}
)

func (x GetInvestmentInstrumentDetailsResponse_Status) Enum() *GetInvestmentInstrumentDetailsResponse_Status {
	p := new(GetInvestmentInstrumentDetailsResponse_Status)
	*p = x
	return p
}

func (x GetInvestmentInstrumentDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetInvestmentInstrumentDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_investment_indianstocks_service_proto_enumTypes[2].Descriptor()
}

func (GetInvestmentInstrumentDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_investment_indianstocks_service_proto_enumTypes[2]
}

func (x GetInvestmentInstrumentDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetInvestmentInstrumentDetailsResponse_Status.Descriptor instead.
func (GetInvestmentInstrumentDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{5, 0}
}

type GetIndianStocksDashboardResponse_Status int32

const (
	GetIndianStocksDashboardResponse_OK               GetIndianStocksDashboardResponse_Status = 0
	GetIndianStocksDashboardResponse_INVALID_ARGUMENT GetIndianStocksDashboardResponse_Status = 3
	// internal server error
	GetIndianStocksDashboardResponse_INTERNAL GetIndianStocksDashboardResponse_Status = 13
	// not found
	GetIndianStocksDashboardResponse_NOT_FOUND GetIndianStocksDashboardResponse_Status = 5
)

// Enum value maps for GetIndianStocksDashboardResponse_Status.
var (
	GetIndianStocksDashboardResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		5:  "NOT_FOUND",
	}
	GetIndianStocksDashboardResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
		"NOT_FOUND":        5,
	}
)

func (x GetIndianStocksDashboardResponse_Status) Enum() *GetIndianStocksDashboardResponse_Status {
	p := new(GetIndianStocksDashboardResponse_Status)
	*p = x
	return p
}

func (x GetIndianStocksDashboardResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetIndianStocksDashboardResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_investment_indianstocks_service_proto_enumTypes[3].Descriptor()
}

func (GetIndianStocksDashboardResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_investment_indianstocks_service_proto_enumTypes[3]
}

func (x GetIndianStocksDashboardResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetIndianStocksDashboardResponse_Status.Descriptor instead.
func (GetIndianStocksDashboardResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{7, 0}
}

type GetOrderReceiptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// stringified OrderId defined in api/frontend/investment/indstocks/internal/order_id.proto
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetOrderReceiptRequest) Reset() {
	*x = GetOrderReceiptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderReceiptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderReceiptRequest) ProtoMessage() {}

func (x *GetOrderReceiptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderReceiptRequest.ProtoReflect.Descriptor instead.
func (*GetOrderReceiptRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetOrderReceiptRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetOrderReceiptRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetOrderReceiptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader   *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	OrderReceipt *ui.OrderReceipt       `protobuf:"bytes,2,opt,name=order_receipt,json=orderReceipt,proto3" json:"order_receipt,omitempty"`
}

func (x *GetOrderReceiptResponse) Reset() {
	*x = GetOrderReceiptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderReceiptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderReceiptResponse) ProtoMessage() {}

func (x *GetOrderReceiptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderReceiptResponse.ProtoReflect.Descriptor instead.
func (*GetOrderReceiptResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrderReceiptResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetOrderReceiptResponse) GetOrderReceipt() *ui.OrderReceipt {
	if x != nil {
		return x.OrderReceipt
	}
	return nil
}

type GetInvestmentActivitiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// optional filter applied while selecting investment activity
	// filter represent stringified instrument specific ActivityFilter defined in api/frontend/investment/indianstocks/internal/investment_activity_filter.proto
	Filter      string                  `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	PageContext *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetInvestmentActivitiesRequest) Reset() {
	*x = GetInvestmentActivitiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentActivitiesRequest) ProtoMessage() {}

func (x *GetInvestmentActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentActivitiesRequest.ProtoReflect.Descriptor instead.
func (*GetInvestmentActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetInvestmentActivitiesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetInvestmentActivitiesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *GetInvestmentActivitiesRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetInvestmentActivitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader           *header.ResponseHeader        `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	PageContext          *rpc.PageContextResponse      `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	InvestmentActivities []*ui.InvestmentActivityGroup `protobuf:"bytes,3,rep,name=investment_activities,json=investmentActivities,proto3" json:"investment_activities,omitempty"`
	// disclaimer to be shown at the end of the investment activities
	// disclaimer may be sent if :
	// 1. This is the last page for the activities https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?type=design&node-id=1089-1934&mode=design&t=7aTJYtfSh4czEqwH-4
	// 2. No investment activity is found https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?type=design&node-id=1089-1909&mode=design&t=7aTJYtfSh4czEqwH-4
	Disclaimer *ui1.IconTextComponent `protobuf:"bytes,4,opt,name=disclaimer,proto3" json:"disclaimer,omitempty"`
}

func (x *GetInvestmentActivitiesResponse) Reset() {
	*x = GetInvestmentActivitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentActivitiesResponse) ProtoMessage() {}

func (x *GetInvestmentActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentActivitiesResponse.ProtoReflect.Descriptor instead.
func (*GetInvestmentActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetInvestmentActivitiesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetInvestmentActivitiesResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetInvestmentActivitiesResponse) GetInvestmentActivities() []*ui.InvestmentActivityGroup {
	if x != nil {
		return x.InvestmentActivities
	}
	return nil
}

func (x *GetInvestmentActivitiesResponse) GetDisclaimer() *ui1.IconTextComponent {
	if x != nil {
		return x.Disclaimer
	}
	return nil
}

type GetInvestmentInstrumentDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// required filter to select relevant investment
	// it is stringified representation of defined in InvestmentInstrumentDetailsFilter defined in api/frontend/investment/indstocks/internal/investment_instrument_details_filter.proto
	Filter string `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetInvestmentInstrumentDetailsRequest) Reset() {
	*x = GetInvestmentInstrumentDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentInstrumentDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentInstrumentDetailsRequest) ProtoMessage() {}

func (x *GetInvestmentInstrumentDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentInstrumentDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetInvestmentInstrumentDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetInvestmentInstrumentDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetInvestmentInstrumentDetailsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

type GetInvestmentInstrumentDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader                  *header.ResponseHeader          `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	InvestmentInstrumentDetails *ui.InvestmentInstrumentDetails `protobuf:"bytes,2,opt,name=investment_instrument_details,json=investmentInstrumentDetails,proto3" json:"investment_instrument_details,omitempty"`
}

func (x *GetInvestmentInstrumentDetailsResponse) Reset() {
	*x = GetInvestmentInstrumentDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentInstrumentDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentInstrumentDetailsResponse) ProtoMessage() {}

func (x *GetInvestmentInstrumentDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentInstrumentDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetInvestmentInstrumentDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetInvestmentInstrumentDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetInvestmentInstrumentDetailsResponse) GetInvestmentInstrumentDetails() *ui.InvestmentInstrumentDetails {
	if x != nil {
		return x.InvestmentInstrumentDetails
	}
	return nil
}

type GetIndianStocksDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// filter_values_map contains for each filter the applied filter options values
	FilterValuesMap map[string]*ui1.FilterValue `protobuf:"bytes,2,rep,name=filter_values_map,json=filterValuesMap,proto3" json:"filter_values_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetIndianStocksDashboardRequest) Reset() {
	*x = GetIndianStocksDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIndianStocksDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIndianStocksDashboardRequest) ProtoMessage() {}

func (x *GetIndianStocksDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIndianStocksDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetIndianStocksDashboardRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetIndianStocksDashboardRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetIndianStocksDashboardRequest) GetFilterValuesMap() map[string]*ui1.FilterValue {
	if x != nil {
		return x.FilterValuesMap
	}
	return nil
}

type GetIndianStocksDashboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader  `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Dashboard  *ui.InstrumentDashboard `protobuf:"bytes,2,opt,name=dashboard,proto3" json:"dashboard,omitempty"`
}

func (x *GetIndianStocksDashboardResponse) Reset() {
	*x = GetIndianStocksDashboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIndianStocksDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIndianStocksDashboardResponse) ProtoMessage() {}

func (x *GetIndianStocksDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_indianstocks_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIndianStocksDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetIndianStocksDashboardResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetIndianStocksDashboardResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetIndianStocksDashboardResponse) GetDashboard() *ui.InstrumentDashboard {
	if x != nil {
		return x.Dashboard
	}
	return nil
}

var File_api_frontend_investment_indianstocks_service_proto protoreflect.FileDescriptor

var file_api_frontend_investment_indianstocks_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x69, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x69, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x75, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x69, 0x2f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x5a, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd5, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x0d, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x22, 0xa6, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xe9, 0x02, 0x0a,
	0x1f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x64, 0x0a, 0x15, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x14, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6c, 0x61, 0x69,
	0x6d, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64, 0x69,
	0x73, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x72, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x71, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x92, 0x02, 0x0a, 0x26,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x77, 0x0a, 0x1d, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x1b, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05,
	0x22, 0xb9, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53, 0x74,
	0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x56, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x1a, 0x5f, 0x0a, 0x14, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf4, 0x01, 0x0a,
	0x20, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x22, 0x43,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x32, 0xd1, 0x05, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x97, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x70, 0x74, 0x12, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88,
	0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xaf, 0x01, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69,
	0x61, 0x6e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e,
	0x64, 0x69, 0x61, 0x6e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xc4, 0x01, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e,
	0x64, 0x69, 0x61, 0x6e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0xb2, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12,
	0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x63,
	0x6b, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7,
	0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x7a, 0x0a, 0x3b, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_investment_indianstocks_service_proto_rawDescOnce sync.Once
	file_api_frontend_investment_indianstocks_service_proto_rawDescData = file_api_frontend_investment_indianstocks_service_proto_rawDesc
)

func file_api_frontend_investment_indianstocks_service_proto_rawDescGZIP() []byte {
	file_api_frontend_investment_indianstocks_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_investment_indianstocks_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_investment_indianstocks_service_proto_rawDescData)
	})
	return file_api_frontend_investment_indianstocks_service_proto_rawDescData
}

var file_api_frontend_investment_indianstocks_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_frontend_investment_indianstocks_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_frontend_investment_indianstocks_service_proto_goTypes = []interface{}{
	(GetOrderReceiptResponse_Status)(0),                // 0: frontend.investment.indianstocks.GetOrderReceiptResponse.Status
	(GetInvestmentActivitiesResponse_Status)(0),        // 1: frontend.investment.indianstocks.GetInvestmentActivitiesResponse.Status
	(GetInvestmentInstrumentDetailsResponse_Status)(0), // 2: frontend.investment.indianstocks.GetInvestmentInstrumentDetailsResponse.Status
	(GetIndianStocksDashboardResponse_Status)(0),       // 3: frontend.investment.indianstocks.GetIndianStocksDashboardResponse.Status
	(*GetOrderReceiptRequest)(nil),                     // 4: frontend.investment.indianstocks.GetOrderReceiptRequest
	(*GetOrderReceiptResponse)(nil),                    // 5: frontend.investment.indianstocks.GetOrderReceiptResponse
	(*GetInvestmentActivitiesRequest)(nil),             // 6: frontend.investment.indianstocks.GetInvestmentActivitiesRequest
	(*GetInvestmentActivitiesResponse)(nil),            // 7: frontend.investment.indianstocks.GetInvestmentActivitiesResponse
	(*GetInvestmentInstrumentDetailsRequest)(nil),      // 8: frontend.investment.indianstocks.GetInvestmentInstrumentDetailsRequest
	(*GetInvestmentInstrumentDetailsResponse)(nil),     // 9: frontend.investment.indianstocks.GetInvestmentInstrumentDetailsResponse
	(*GetIndianStocksDashboardRequest)(nil),            // 10: frontend.investment.indianstocks.GetIndianStocksDashboardRequest
	(*GetIndianStocksDashboardResponse)(nil),           // 11: frontend.investment.indianstocks.GetIndianStocksDashboardResponse
	nil,                                                // 12: frontend.investment.indianstocks.GetIndianStocksDashboardRequest.FilterValuesMapEntry
	(*header.RequestHeader)(nil),                       // 13: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil),                      // 14: frontend.header.ResponseHeader
	(*ui.OrderReceipt)(nil),                            // 15: frontend.investment.ui.OrderReceipt
	(*rpc.PageContextRequest)(nil),                     // 16: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),                    // 17: rpc.PageContextResponse
	(*ui.InvestmentActivityGroup)(nil),                 // 18: frontend.investment.ui.InvestmentActivityGroup
	(*ui1.IconTextComponent)(nil),                      // 19: api.typesv2.ui.IconTextComponent
	(*ui.InvestmentInstrumentDetails)(nil),             // 20: frontend.investment.ui.InvestmentInstrumentDetails
	(*ui.InstrumentDashboard)(nil),                     // 21: frontend.investment.ui.InstrumentDashboard
	(*ui1.FilterValue)(nil),                            // 22: api.typesv2.ui.FilterValue
}
var file_api_frontend_investment_indianstocks_service_proto_depIdxs = []int32{
	13, // 0: frontend.investment.indianstocks.GetOrderReceiptRequest.req:type_name -> frontend.header.RequestHeader
	14, // 1: frontend.investment.indianstocks.GetOrderReceiptResponse.resp_header:type_name -> frontend.header.ResponseHeader
	15, // 2: frontend.investment.indianstocks.GetOrderReceiptResponse.order_receipt:type_name -> frontend.investment.ui.OrderReceipt
	13, // 3: frontend.investment.indianstocks.GetInvestmentActivitiesRequest.req:type_name -> frontend.header.RequestHeader
	16, // 4: frontend.investment.indianstocks.GetInvestmentActivitiesRequest.page_context:type_name -> rpc.PageContextRequest
	14, // 5: frontend.investment.indianstocks.GetInvestmentActivitiesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	17, // 6: frontend.investment.indianstocks.GetInvestmentActivitiesResponse.page_context:type_name -> rpc.PageContextResponse
	18, // 7: frontend.investment.indianstocks.GetInvestmentActivitiesResponse.investment_activities:type_name -> frontend.investment.ui.InvestmentActivityGroup
	19, // 8: frontend.investment.indianstocks.GetInvestmentActivitiesResponse.disclaimer:type_name -> api.typesv2.ui.IconTextComponent
	13, // 9: frontend.investment.indianstocks.GetInvestmentInstrumentDetailsRequest.req:type_name -> frontend.header.RequestHeader
	14, // 10: frontend.investment.indianstocks.GetInvestmentInstrumentDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	20, // 11: frontend.investment.indianstocks.GetInvestmentInstrumentDetailsResponse.investment_instrument_details:type_name -> frontend.investment.ui.InvestmentInstrumentDetails
	13, // 12: frontend.investment.indianstocks.GetIndianStocksDashboardRequest.req:type_name -> frontend.header.RequestHeader
	12, // 13: frontend.investment.indianstocks.GetIndianStocksDashboardRequest.filter_values_map:type_name -> frontend.investment.indianstocks.GetIndianStocksDashboardRequest.FilterValuesMapEntry
	14, // 14: frontend.investment.indianstocks.GetIndianStocksDashboardResponse.resp_header:type_name -> frontend.header.ResponseHeader
	21, // 15: frontend.investment.indianstocks.GetIndianStocksDashboardResponse.dashboard:type_name -> frontend.investment.ui.InstrumentDashboard
	22, // 16: frontend.investment.indianstocks.GetIndianStocksDashboardRequest.FilterValuesMapEntry.value:type_name -> api.typesv2.ui.FilterValue
	4,  // 17: frontend.investment.indianstocks.Service.GetOrderReceipt:input_type -> frontend.investment.indianstocks.GetOrderReceiptRequest
	6,  // 18: frontend.investment.indianstocks.Service.GetInvestmentActivities:input_type -> frontend.investment.indianstocks.GetInvestmentActivitiesRequest
	8,  // 19: frontend.investment.indianstocks.Service.GetInvestmentInstrumentDetails:input_type -> frontend.investment.indianstocks.GetInvestmentInstrumentDetailsRequest
	10, // 20: frontend.investment.indianstocks.Service.GetIndianStocksDashboard:input_type -> frontend.investment.indianstocks.GetIndianStocksDashboardRequest
	5,  // 21: frontend.investment.indianstocks.Service.GetOrderReceipt:output_type -> frontend.investment.indianstocks.GetOrderReceiptResponse
	7,  // 22: frontend.investment.indianstocks.Service.GetInvestmentActivities:output_type -> frontend.investment.indianstocks.GetInvestmentActivitiesResponse
	9,  // 23: frontend.investment.indianstocks.Service.GetInvestmentInstrumentDetails:output_type -> frontend.investment.indianstocks.GetInvestmentInstrumentDetailsResponse
	11, // 24: frontend.investment.indianstocks.Service.GetIndianStocksDashboard:output_type -> frontend.investment.indianstocks.GetIndianStocksDashboardResponse
	21, // [21:25] is the sub-list for method output_type
	17, // [17:21] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_frontend_investment_indianstocks_service_proto_init() }
func file_api_frontend_investment_indianstocks_service_proto_init() {
	if File_api_frontend_investment_indianstocks_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderReceiptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderReceiptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentActivitiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentActivitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentInstrumentDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentInstrumentDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIndianStocksDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_indianstocks_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIndianStocksDashboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_investment_indianstocks_service_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_investment_indianstocks_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_investment_indianstocks_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_investment_indianstocks_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_investment_indianstocks_service_proto_msgTypes,
	}.Build()
	File_api_frontend_investment_indianstocks_service_proto = out.File
	file_api_frontend_investment_indianstocks_service_proto_rawDesc = nil
	file_api_frontend_investment_indianstocks_service_proto_goTypes = nil
	file_api_frontend_investment_indianstocks_service_proto_depIdxs = nil
}
