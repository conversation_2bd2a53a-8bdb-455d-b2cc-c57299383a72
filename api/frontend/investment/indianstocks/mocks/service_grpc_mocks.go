// Code generated by MockGen. DO NOT EDIT.
// Source: api/./frontend/investment/indianstocks/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	indianstocks "github.com/epifi/gamma/api/frontend/investment/indianstocks"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockServiceClient is a mock of ServiceClient interface.
type MockServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockServiceClientMockRecorder
}

// MockServiceClientMockRecorder is the mock recorder for MockServiceClient.
type MockServiceClientMockRecorder struct {
	mock *MockServiceClient
}

// NewMockServiceClient creates a new mock instance.
func NewMockServiceClient(ctrl *gomock.Controller) *MockServiceClient {
	mock := &MockServiceClient{ctrl: ctrl}
	mock.recorder = &MockServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceClient) EXPECT() *MockServiceClientMockRecorder {
	return m.recorder
}

// GetIndianStocksDashboard mocks base method.
func (m *MockServiceClient) GetIndianStocksDashboard(ctx context.Context, in *indianstocks.GetIndianStocksDashboardRequest, opts ...grpc.CallOption) (*indianstocks.GetIndianStocksDashboardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIndianStocksDashboard", varargs...)
	ret0, _ := ret[0].(*indianstocks.GetIndianStocksDashboardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndianStocksDashboard indicates an expected call of GetIndianStocksDashboard.
func (mr *MockServiceClientMockRecorder) GetIndianStocksDashboard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndianStocksDashboard", reflect.TypeOf((*MockServiceClient)(nil).GetIndianStocksDashboard), varargs...)
}

// GetInvestmentActivities mocks base method.
func (m *MockServiceClient) GetInvestmentActivities(ctx context.Context, in *indianstocks.GetInvestmentActivitiesRequest, opts ...grpc.CallOption) (*indianstocks.GetInvestmentActivitiesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvestmentActivities", varargs...)
	ret0, _ := ret[0].(*indianstocks.GetInvestmentActivitiesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentActivities indicates an expected call of GetInvestmentActivities.
func (mr *MockServiceClientMockRecorder) GetInvestmentActivities(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentActivities", reflect.TypeOf((*MockServiceClient)(nil).GetInvestmentActivities), varargs...)
}

// GetInvestmentInstrumentDetails mocks base method.
func (m *MockServiceClient) GetInvestmentInstrumentDetails(ctx context.Context, in *indianstocks.GetInvestmentInstrumentDetailsRequest, opts ...grpc.CallOption) (*indianstocks.GetInvestmentInstrumentDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvestmentInstrumentDetails", varargs...)
	ret0, _ := ret[0].(*indianstocks.GetInvestmentInstrumentDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentInstrumentDetails indicates an expected call of GetInvestmentInstrumentDetails.
func (mr *MockServiceClientMockRecorder) GetInvestmentInstrumentDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentInstrumentDetails", reflect.TypeOf((*MockServiceClient)(nil).GetInvestmentInstrumentDetails), varargs...)
}

// GetOrderReceipt mocks base method.
func (m *MockServiceClient) GetOrderReceipt(ctx context.Context, in *indianstocks.GetOrderReceiptRequest, opts ...grpc.CallOption) (*indianstocks.GetOrderReceiptResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderReceipt", varargs...)
	ret0, _ := ret[0].(*indianstocks.GetOrderReceiptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderReceipt indicates an expected call of GetOrderReceipt.
func (mr *MockServiceClientMockRecorder) GetOrderReceipt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderReceipt", reflect.TypeOf((*MockServiceClient)(nil).GetOrderReceipt), varargs...)
}

// MockServiceServer is a mock of ServiceServer interface.
type MockServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockServiceServerMockRecorder
}

// MockServiceServerMockRecorder is the mock recorder for MockServiceServer.
type MockServiceServerMockRecorder struct {
	mock *MockServiceServer
}

// NewMockServiceServer creates a new mock instance.
func NewMockServiceServer(ctrl *gomock.Controller) *MockServiceServer {
	mock := &MockServiceServer{ctrl: ctrl}
	mock.recorder = &MockServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceServer) EXPECT() *MockServiceServerMockRecorder {
	return m.recorder
}

// GetIndianStocksDashboard mocks base method.
func (m *MockServiceServer) GetIndianStocksDashboard(arg0 context.Context, arg1 *indianstocks.GetIndianStocksDashboardRequest) (*indianstocks.GetIndianStocksDashboardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndianStocksDashboard", arg0, arg1)
	ret0, _ := ret[0].(*indianstocks.GetIndianStocksDashboardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndianStocksDashboard indicates an expected call of GetIndianStocksDashboard.
func (mr *MockServiceServerMockRecorder) GetIndianStocksDashboard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndianStocksDashboard", reflect.TypeOf((*MockServiceServer)(nil).GetIndianStocksDashboard), arg0, arg1)
}

// GetInvestmentActivities mocks base method.
func (m *MockServiceServer) GetInvestmentActivities(arg0 context.Context, arg1 *indianstocks.GetInvestmentActivitiesRequest) (*indianstocks.GetInvestmentActivitiesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvestmentActivities", arg0, arg1)
	ret0, _ := ret[0].(*indianstocks.GetInvestmentActivitiesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentActivities indicates an expected call of GetInvestmentActivities.
func (mr *MockServiceServerMockRecorder) GetInvestmentActivities(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentActivities", reflect.TypeOf((*MockServiceServer)(nil).GetInvestmentActivities), arg0, arg1)
}

// GetInvestmentInstrumentDetails mocks base method.
func (m *MockServiceServer) GetInvestmentInstrumentDetails(arg0 context.Context, arg1 *indianstocks.GetInvestmentInstrumentDetailsRequest) (*indianstocks.GetInvestmentInstrumentDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvestmentInstrumentDetails", arg0, arg1)
	ret0, _ := ret[0].(*indianstocks.GetInvestmentInstrumentDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentInstrumentDetails indicates an expected call of GetInvestmentInstrumentDetails.
func (mr *MockServiceServerMockRecorder) GetInvestmentInstrumentDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentInstrumentDetails", reflect.TypeOf((*MockServiceServer)(nil).GetInvestmentInstrumentDetails), arg0, arg1)
}

// GetOrderReceipt mocks base method.
func (m *MockServiceServer) GetOrderReceipt(arg0 context.Context, arg1 *indianstocks.GetOrderReceiptRequest) (*indianstocks.GetOrderReceiptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderReceipt", arg0, arg1)
	ret0, _ := ret[0].(*indianstocks.GetOrderReceiptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderReceipt indicates an expected call of GetOrderReceipt.
func (mr *MockServiceServerMockRecorder) GetOrderReceipt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderReceipt", reflect.TypeOf((*MockServiceServer)(nil).GetOrderReceipt), arg0, arg1)
}

// MockUnsafeServiceServer is a mock of UnsafeServiceServer interface.
type MockUnsafeServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeServiceServerMockRecorder
}

// MockUnsafeServiceServerMockRecorder is the mock recorder for MockUnsafeServiceServer.
type MockUnsafeServiceServerMockRecorder struct {
	mock *MockUnsafeServiceServer
}

// NewMockUnsafeServiceServer creates a new mock instance.
func NewMockUnsafeServiceServer(ctrl *gomock.Controller) *MockUnsafeServiceServer {
	mock := &MockUnsafeServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeServiceServer) EXPECT() *MockUnsafeServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedServiceServer mocks base method.
func (m *MockUnsafeServiceServer) mustEmbedUnimplementedServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedServiceServer")
}

// mustEmbedUnimplementedServiceServer indicates an expected call of mustEmbedUnimplementedServiceServer.
func (mr *MockUnsafeServiceServerMockRecorder) mustEmbedUnimplementedServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedServiceServer", reflect.TypeOf((*MockUnsafeServiceServer)(nil).mustEmbedUnimplementedServiceServer))
}
