// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/investment/indianstocks/service.proto

package indianstocks

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetOrderReceiptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderReceiptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderReceiptRequestMultiError, or nil if none found.
func (m *GetOrderReceiptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	if len(errors) > 0 {
		return GetOrderReceiptRequestMultiError(errors)
	}

	return nil
}

// GetOrderReceiptRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrderReceiptRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptRequestMultiError) AllErrors() []error { return m }

// GetOrderReceiptRequestValidationError is the validation error returned by
// GetOrderReceiptRequest.Validate if the designated constraints aren't met.
type GetOrderReceiptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptRequestValidationError) ErrorName() string {
	return "GetOrderReceiptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptRequestValidationError{}

// Validate checks the field values on GetOrderReceiptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderReceiptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderReceiptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrderReceiptResponseMultiError, or nil if none found.
func (m *GetOrderReceiptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderReceiptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderReceipt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "OrderReceipt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderReceiptResponseValidationError{
					field:  "OrderReceipt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderReceipt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderReceiptResponseValidationError{
				field:  "OrderReceipt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderReceiptResponseMultiError(errors)
	}

	return nil
}

// GetOrderReceiptResponseMultiError is an error wrapping multiple validation
// errors returned by GetOrderReceiptResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrderReceiptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderReceiptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderReceiptResponseMultiError) AllErrors() []error { return m }

// GetOrderReceiptResponseValidationError is the validation error returned by
// GetOrderReceiptResponse.Validate if the designated constraints aren't met.
type GetOrderReceiptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderReceiptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderReceiptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderReceiptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderReceiptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderReceiptResponseValidationError) ErrorName() string {
	return "GetOrderReceiptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderReceiptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderReceiptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderReceiptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderReceiptResponseValidationError{}

// Validate checks the field values on GetInvestmentActivitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentActivitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentActivitiesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentActivitiesRequestMultiError, or nil if none found.
func (m *GetInvestmentActivitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentActivitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentActivitiesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentActivitiesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentActivitiesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Filter

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentActivitiesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentActivitiesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentActivitiesRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentActivitiesRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentActivitiesRequestMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentActivitiesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetInvestmentActivitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentActivitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentActivitiesRequestMultiError) AllErrors() []error { return m }

// GetInvestmentActivitiesRequestValidationError is the validation error
// returned by GetInvestmentActivitiesRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentActivitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentActivitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentActivitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentActivitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentActivitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentActivitiesRequestValidationError) ErrorName() string {
	return "GetInvestmentActivitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentActivitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentActivitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentActivitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentActivitiesRequestValidationError{}

// Validate checks the field values on GetInvestmentActivitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentActivitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentActivitiesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentActivitiesResponseMultiError, or nil if none found.
func (m *GetInvestmentActivitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentActivitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentActivitiesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentActivitiesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentActivitiesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentActivitiesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentActivitiesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentActivitiesResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInvestmentActivities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentActivitiesResponseValidationError{
						field:  fmt.Sprintf("InvestmentActivities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentActivitiesResponseValidationError{
						field:  fmt.Sprintf("InvestmentActivities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentActivitiesResponseValidationError{
					field:  fmt.Sprintf("InvestmentActivities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDisclaimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentActivitiesResponseValidationError{
					field:  "Disclaimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentActivitiesResponseValidationError{
					field:  "Disclaimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisclaimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentActivitiesResponseValidationError{
				field:  "Disclaimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentActivitiesResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentActivitiesResponseMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentActivitiesResponse.ValidateAll()
// if the designated constraints aren't met.
type GetInvestmentActivitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentActivitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentActivitiesResponseMultiError) AllErrors() []error { return m }

// GetInvestmentActivitiesResponseValidationError is the validation error
// returned by GetInvestmentActivitiesResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentActivitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentActivitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentActivitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentActivitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentActivitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentActivitiesResponseValidationError) ErrorName() string {
	return "GetInvestmentActivitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentActivitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentActivitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentActivitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentActivitiesResponseValidationError{}

// Validate checks the field values on GetInvestmentInstrumentDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetInvestmentInstrumentDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentInstrumentDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentInstrumentDetailsRequestMultiError, or nil if none found.
func (m *GetInvestmentInstrumentDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentInstrumentDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentInstrumentDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentInstrumentDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentInstrumentDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Filter

	if len(errors) > 0 {
		return GetInvestmentInstrumentDetailsRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentInstrumentDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestmentInstrumentDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentInstrumentDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentInstrumentDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentInstrumentDetailsRequestMultiError) AllErrors() []error { return m }

// GetInvestmentInstrumentDetailsRequestValidationError is the validation error
// returned by GetInvestmentInstrumentDetailsRequest.Validate if the
// designated constraints aren't met.
type GetInvestmentInstrumentDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentInstrumentDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentInstrumentDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentInstrumentDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentInstrumentDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentInstrumentDetailsRequestValidationError) ErrorName() string {
	return "GetInvestmentInstrumentDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentInstrumentDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentInstrumentDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentInstrumentDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentInstrumentDetailsRequestValidationError{}

// Validate checks the field values on GetInvestmentInstrumentDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetInvestmentInstrumentDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInvestmentInstrumentDetailsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetInvestmentInstrumentDetailsResponseMultiError, or nil if none found.
func (m *GetInvestmentInstrumentDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentInstrumentDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentInstrumentDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentInstrumentDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentInstrumentDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentInstrumentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentInstrumentDetailsResponseValidationError{
					field:  "InvestmentInstrumentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentInstrumentDetailsResponseValidationError{
					field:  "InvestmentInstrumentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentInstrumentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentInstrumentDetailsResponseValidationError{
				field:  "InvestmentInstrumentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentInstrumentDetailsResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentInstrumentDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestmentInstrumentDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentInstrumentDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentInstrumentDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentInstrumentDetailsResponseMultiError) AllErrors() []error { return m }

// GetInvestmentInstrumentDetailsResponseValidationError is the validation
// error returned by GetInvestmentInstrumentDetailsResponse.Validate if the
// designated constraints aren't met.
type GetInvestmentInstrumentDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentInstrumentDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentInstrumentDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentInstrumentDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentInstrumentDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentInstrumentDetailsResponseValidationError) ErrorName() string {
	return "GetInvestmentInstrumentDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentInstrumentDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentInstrumentDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentInstrumentDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentInstrumentDetailsResponseValidationError{}

// Validate checks the field values on GetIndianStocksDashboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetIndianStocksDashboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIndianStocksDashboardRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetIndianStocksDashboardRequestMultiError, or nil if none found.
func (m *GetIndianStocksDashboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIndianStocksDashboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetIndianStocksDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetIndianStocksDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetIndianStocksDashboardRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFilterValuesMap()))
		i := 0
		for key := range m.GetFilterValuesMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFilterValuesMap()[key]
			_ = val

			// no validation rules for FilterValuesMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetIndianStocksDashboardRequestValidationError{
							field:  fmt.Sprintf("FilterValuesMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetIndianStocksDashboardRequestValidationError{
							field:  fmt.Sprintf("FilterValuesMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetIndianStocksDashboardRequestValidationError{
						field:  fmt.Sprintf("FilterValuesMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetIndianStocksDashboardRequestMultiError(errors)
	}

	return nil
}

// GetIndianStocksDashboardRequestMultiError is an error wrapping multiple
// validation errors returned by GetIndianStocksDashboardRequest.ValidateAll()
// if the designated constraints aren't met.
type GetIndianStocksDashboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIndianStocksDashboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIndianStocksDashboardRequestMultiError) AllErrors() []error { return m }

// GetIndianStocksDashboardRequestValidationError is the validation error
// returned by GetIndianStocksDashboardRequest.Validate if the designated
// constraints aren't met.
type GetIndianStocksDashboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIndianStocksDashboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIndianStocksDashboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIndianStocksDashboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIndianStocksDashboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIndianStocksDashboardRequestValidationError) ErrorName() string {
	return "GetIndianStocksDashboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetIndianStocksDashboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIndianStocksDashboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIndianStocksDashboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIndianStocksDashboardRequestValidationError{}

// Validate checks the field values on GetIndianStocksDashboardResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetIndianStocksDashboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIndianStocksDashboardResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetIndianStocksDashboardResponseMultiError, or nil if none found.
func (m *GetIndianStocksDashboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIndianStocksDashboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetIndianStocksDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetIndianStocksDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetIndianStocksDashboardResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetIndianStocksDashboardResponseValidationError{
					field:  "Dashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetIndianStocksDashboardResponseValidationError{
					field:  "Dashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetIndianStocksDashboardResponseValidationError{
				field:  "Dashboard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetIndianStocksDashboardResponseMultiError(errors)
	}

	return nil
}

// GetIndianStocksDashboardResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetIndianStocksDashboardResponse.ValidateAll() if the designated
// constraints aren't met.
type GetIndianStocksDashboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIndianStocksDashboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIndianStocksDashboardResponseMultiError) AllErrors() []error { return m }

// GetIndianStocksDashboardResponseValidationError is the validation error
// returned by GetIndianStocksDashboardResponse.Validate if the designated
// constraints aren't met.
type GetIndianStocksDashboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIndianStocksDashboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIndianStocksDashboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIndianStocksDashboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIndianStocksDashboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIndianStocksDashboardResponseValidationError) ErrorName() string {
	return "GetIndianStocksDashboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetIndianStocksDashboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIndianStocksDashboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIndianStocksDashboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIndianStocksDashboardResponseValidationError{}
