syntax = "proto3";

package frontend.deeplink;

import "api/accounts/account_type.proto";
import "api/frontend/account/screening/ui_states/enum.proto";
import "api/frontend/analytics/analytics_screen_name.proto";
import "api/frontend/budgeting/reminder/meta/meta.proto";
import "api/frontend/connected_account/common/aa.proto";
import "api/frontend/deeplink/definitions/polling.proto";
import "api/frontend/deeplink/fittt_enums.proto";
import "api/frontend/deeplink/timeline/enter_amount_screen_options.proto";
import "api/frontend/deeplink/wealth_deeplink.proto";
import "api/frontend/deeplinkv2/version.proto";
import "api/frontend/document_upload/polling/polling_option.proto";
import "api/frontend/firefly/enums/enums.proto";
import "api/frontend/fittt/client_states/enums.proto";
import "api/frontend/fittt/rule/rule_params.proto";
import "api/frontend/investment/mutualfund/client_states/enums.proto";
import "api/frontend/p2pinvestment/jump_client_states/enums.proto";
import "api/frontend/preapprovedloan/pal_enums/enums.proto";
import "api/frontend/search/meta/meta.proto";
import "api/frontend/tiering/enum/enum.proto";
import "api/frontend/ui/versatile_elements.proto";
import "api/frontend/user/user_activity/user_activity.proto";
import "api/frontend/usstocks/client_states/enums.proto";
import "api/frontend/waitlist/waitlist_enums/enums.proto";
import "api/frontend/wealthonboarding/client_states/enums.proto";
import "api/typesv2/address.proto";
import "api/typesv2/chat.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/currency_code.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deeplink_screen_option/usstocks/metadata/metadata.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/income_slab.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/money.proto";
import "api/typesv2/multiple_choice_question.proto";
import "api/typesv2/npci_flow_type.proto";
import "api/typesv2/pay/user_identifier/user_identifier.proto";
import "api/typesv2/payment_protocol.proto";
import "api/typesv2/recurring_payment/autopay_details.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/user.proto";
import "google/protobuf/any.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/deeplink";
option java_package = "com.github.epifi.gamma.api.frontend.deeplink";
option java_multiple_files = true;

// A deep link URI is the specifier which tells which page the user is
// looking to land to upon clicking the notificationapi/frontend/deeplink/deeplink.proto
// More values will be added here depending on the use cases coming up
enum Screen {
  DEEP_LINK_URI_UNSPECIFIED = 0;

  // This will take user to create account page
  CREATE_ACCOUNT = 1;

  // This is will show the timeline for all transactions
  TRANSACTION_TIMELINE = 2;

  // This will show user his rewards timeline
  REWARDS_TIMELINE = 3;

  // Triggers CKYC flow. On Register CKYC Page, user is asked for PAN and DOB
  REGISTER_CKYC = 4;

  // Triggers EKYC flow
  START_EKYC = 5;

  SIGNUP_TERMS_AND_CONDITIONS = 6;

  // Triggers Manual KYC flow
  START_MANUAL_KYC = 7;

  // Home page of the app
  HOME = 8;

  // Liveness check Screen
  CHECK_LIVENESS = 9;

  // timeline screen
  TIMELINE = 10;

  // consent screen
  CONSENT = 11;

  // It presents a popup/dialog box on client. The popup confirms with the users
  // if they want to begin the Auth Factor Update (AFU) flow. This popup comes on
  // AddOAuthAccount screen when there is a mismatch in auth factor updates -
  // (phone num, email & device id). Confirmation triggers Auth Factor Update process.
  AFU_CONFIRM_START_POPUP = 12;

  // The screen where users verify their ATM PIN
  // AFU_ATM_PIN_VALIDATION is used for AFU flow
  ATM_PIN_VALIDATION = 13 [deprecated = true];

  // When users change either device or phone number or email.
  // The purpose of the screen is to takes final confirmation
  // from user before updating the new values.
  // UI: https://docs.google.com/document/d/1V83WIwb8JDQyVfmvfbEm9RstciDwvmxG4XTmdbVYAKk#bookmark=id.wjmw9e234dvg
  AFU_FINAL_CONFIRMATION = 14;

  // Explicit enum for No action. It refers to no available action for the client.
  NO_ACTION = 15;

  // Action for client to register device. The action 3 steps:
  // 1. Get Binding SMS
  // 2. Send SMS to recipient
  // 3. Make RegisterDevice() call to server
  REGISTER_DEVICE = 16;

  // Client calls the GetNextOnboardingAction API
  GET_NEXT_ONBOARDING_ACTION_API = 17;

  // Action to show a particular webpage to the user - used for showing different legal agreements to user
  WEB_PAGE = 18;

  // General error screen for Auth Factor Update error scenarios
  AFU_ERROR_SCREEN = 19;

  // Action to get bank savings account creation progress status
  // https://www.figma.com/file/3MOg7Doc0NK212gy3Uj8LJ/Onboarding?node-id=1%3A4871
  SAVINGS_ACCOUNT_SETUP_PROGRESS = 20;

  MOBILE_PROMPT_VERIFICATION = 21;

  // deeplink to go to pay landing page
  PAY_LANDING_SCREEN = 22;

  // deeplink to pay-qr screen
  PAY_QR_SCREEN = 23;

  // Mother father name collection page
  PARENTS_NAME_GETTER = 24;

  // deeplink for transaction receipt screen
  TRANSACTION_RECEIPT = 25;

  // deeplink for deposit details screen
  DEPOSIT_ACCOUNT_DETAILS = 26;

  // screen to confirm/add/edit address for delivery of the debit card
  CONFIRM_CARD_MAILING_ADDRESS = 27;

  // deeplink for deposit accounts landing/listing screen
  DEPOSIT_LANDING_SCREEN = 28;

  // deeplink for opening a deposit account
  DEPOSIT_OPEN_ACCOUNT = 29;

  // deeplink for deposit accounts landing/listing screen
  DEPOSIT_CLOSE_ACCOUNT = 30;

  // deeplink for deposit accounts landing/listing screen
  DEPOSIT_ACCOUNT_ADD_MONEY = 31;

  // deeplink for quick account details screen
  QUICK_ACCOUNT_DETAILS_SCREEN = 32;

  // deeplink for account creation
  SAVINGS_ACCOUNT_CREATION_PROGRESS_API = 33;

  // Screen in onboarding flow. It's shown once account setup is
  // complete and user is ready to set card pin. Adding prefix
  // 'onboarding' to differentiate it from setting it from settings screen.
  ONBOARDING_SET_DEBIT_CARD_PIN = 34;

  // Screen to add funds in the onboarding flow. This deeplink is only applicable to pre-onboarding add funds
  ONBOARDING_ADD_MONEY = 35;

  // my profile screen
  PROFILE_SCREEN = 36;

  // MyProfile>UPI QR Code screen
  PROFILE_UPI_QR_CODE_SCREEN = 37;

  // MyProfile>Accounts ; account settings screen
  PROFILE_ACCOUNT_SETTINGS = 38;

  // MyProfile>Privacy & Security
  PROFILE_PRIVACY_SCREEN = 39;

  // MyProfile>Privacy & Security >App permissions
  PROFILE_APP_PERMISSION_SCREEN = 40;

  // MyProfile>Settings
  PROFILE_SETTINGS = 41;

  // MyProfile>Settings>Personal Details
  PROFILE_PERSONAL_DETAILS = 42;

  // MyProfile>Settings>Notification
  PROFILE_SETTINGS_NOTIFICATION = 43;

  // MyProfile>Settings>Legal Agreements
  PROFILE_SETTINGS_LEGAL = 44;

  // MyProfile>Settings>About Fi
  PROFILE_ABOUT_FI = 45;

  // Check card pin set status
  DEBIT_CARD_PIN_SETUP_STATUS = 46;

  // FAQ search article deeplink
  FAQ_ARTICLE = 47;

  // The screen where users enter Debit Card PIN
  // in the Auth Factor Update Flow
  AFU_ATM_PIN_VALIDATION = 48;

  // Dob and Phone number needs to be collected if needed for dedupe check
  DEDUPE_DETAILS = 49;

  // Dedupe error screen (used more generically for full screen errors, not just account dedupe)
  DEDUPE_ERROR = 50;

  // Deep link to go to the help section of the app
  HELP_MAIN = 51;

  // Deeplink for AFU vendor update polling screen. The Client polls the
  // a backend API to check the status of profile update/ Auth factor update
  AFU_CHECK_UPDATE_STATUS = 52;

  // Deeplink for redirecting user to the UPI pin setup screen, in case user
  // hasn't done so. (Example implementation is to redirect from a UPI pin setup
  // notification on Home-screen, for new users)
  UPI_PIN_SETUP = 53;

  // deeplink to land to the reward sd creation screen.
  // sd creation flow for rewards is slightly different
  // from existing create sd flow.
  CREATE_NEW_REWARD_SD_SCREEN = 54;

  // Initiate payment using VPA address.
  PAY_VIA_UPI = 55;

  // Initiate payment using Bank details.
  PAY_VIA_BANK_TRANSFER = 56;

  // Initiate payment for an internal user using registered phone number.
  PAY_VIA_MOBILE_NUMBER = 57;

  // Pay-search screen.
  // User can search for existing timelines, contacts on Fi bank, VPA, etc
  // and initiate the payment.
  PAY_VIA_SEARCH = 58;

  // Trigger intent based payment.
  // It's normally initiated from external PSP.
  PAY_VIA_INTENT = 59;

  // Apply filters & Search for transactions of the user.
  SEARCH_TRANSACTIONS = 60;

  // Raise dispute for a transaction.
  // This screen collects input from the user for the discrepancy in transaction.
  RAISE_DISPUTE = 61;

  // Deeplink to open up help faq category with all folders inside it
  FAQ_CATEGORY = 62;

  // Deeplink to screen where user enters/chooses/edits shipping address
  // for physical offers/rewards.
  REWARD_SHIPPING_ADDRESS_INPUT_SCREEN = 63;

  // Deeplink to redeemed offers screen.
  // Displays all the redeemed offers for an actor.
  REDEEMED_OFFERS_SCREEN = 64;

  // Deeplink to trigger app logs upload
  PULL_APP_LOGS = 65;

  // VKYC landing screen. the screen will support starting
  // a live call & scheduling a call based on screen options
  VKYC_LANDING = 66;

  // fi account transfer-in deeplink. This deeplink is used to redirect to Add funds after user has onboarded on the app
  // (either added funds or skipped, does not matter)
  TRANSFER_IN = 67;

  // Deeplink for card usage screen where user can enable/disable card controls
  CARD_USAGE_SCREEN = 68;

  // Deeplink to card home screen
  CARD_HOME_SCREEN = 69;

  // Deeplink to my rewards screen
  // displays all the rewards for an actor.
  MY_REWARDS_SCREEN = 70;

  // Deeplink to card settings screen where user can reset atm pin, check card controls and limits and request new card
  CARD_SETTINGS_SCREEN = 71;

  // Deeplink to debit card limit home page
  // displays all the limits for an actor.
  CARD_LIMITS_HOME_SCREEN = 72;

  // Deeplink to view details of a scheduled call for Video KYC if applicable for a user
  VIEW_VKYC_SCHEDULE_SCREEN = 73;

  // Deeplink to view ways to earn rewards.
  REWARDS_WAYS_TO_EARN = 74;

  // Deeplink to view all the available offers.
  // Two different RPCs are called based on the client version after landing on this screen.
  // Older RPC: Loads only offers
  // Newer RPC: Loads CBR offers along with existing offers
  // todo(rohanchougule): update the client version onwards which the new RPC will be used
  OFFERS_LANDING_SCREEN = 75;

  // Deeplink to enter finite code screen
  ENTER_FINITE_CODE_SCREEN = 76 [deprecated = true];

  // Deeplink to show that finite code has been successfully verified
  FINITE_CODE_VERIFIED = 77;

  // Deeplink to Fit rule history screen.
  FIT_RULE_HISTORY_SCREEN = 78;

  // Client calls the GetCardAuthNextAction api to check card liveness and fm auth next action.
  GET_CARD_AUTH_NEXT_ACTION_API = 79;

  // Client calls the GenerateTxnId card api
  GENERATE_TXN_ID_CARD_API = 80;

  // FIT Landing page
  FIT_LANDING_SCREEN = 81;

  // FIT my rules page
  FIT_MY_RULES_SCREEN = 82 [deprecated = true];

  //FIT customise rule page
  FIT_CUSTOMISE_RULE_SCREEN = 83;

  // Deeplink to update name or dob when either mismatches with corresponding details in kyc
  // deprecated: Use USER_DETAILS_FORM instead.
  UPDATE_USER_DETAILS_API = 84 [deprecated = true];

  // Deeplink to the Invite friends screen in Referrals flow
  REFERRALS_INVITE_FRIENDS = 85;
  // Employment declaration
  EMPLOYMENT_DECLARATION = 86;
  // Deeplink for employment verification polling screen. The Client polls the
  // a API to check the status of employment verification.
  EMPLOYMENT_VERIFICATION_STATUS = 87;
  // Deeplink for credit report availability polling screen. The Client polls the
  // a API to check the status of credit report availability.
  CREDIT_REPORT_AVAILABILITY_STATUS = 88;
  // Deeplink for screen that collects user consent for downloading and verifying credit report
  CREDIT_REPORT_CONSENT = 89;
  // Deeplink for credit report verification polling screen. The Client polls the
  // a API to check the status of credit based screening checks.
  CREDIT_REPORT_VERIFICATION_STATUS = 90;
  // Screen to block user until consent is given to download the credit report.
  APP_SCREENING_MANDATE_CREDIT_REPORT_CONSENT = 91;
  // Screen to hold off the user until manual verification is in progress.
  APP_SCREENING_MANUAL_INTERVENTION = 92;
  // Screen to redirect user to a rejected state if it couldn't pass the screening checks.
  APP_SCREENING_REJECT = 93;
  // Deeplink to enter referral finite code screen
  ENTER_REFERRAL_FINITE_CODE_SCREEN = 94;
  // Deeplink for manual intervention errors in appscreening
  ERROR_APP_SCREENING = 95;
  // Deeplink for an intermediate screen when Referrals flow is started (from Rewards for ex., or wherever we don't have
  // the user's isActorEligibleForReferrals information). This destination will perform that check and then redirect to
  // either the Invite friends flow, or, start the qualifying action screen
  REFERRALS_ELIGIBILITY_LANDING_SCREEN = 96;
  // Deeplink for ETB (existing to bank user) pin set activity
  ETB_UPI_PIN_SET_ACTIVITY = 97;
  // Deeplink for starting Statement generation flow in client
  STATEMENT_REQUEST_SCREEN = 98;
  // Deeplink for debit card offers page.
  DEBIT_CARD_OFFERS_HOME_SCREEN = 99;
  // Deeplink for Referrals Tnc screen
  REFERRALS_TNC_SCREEN = 100;
  // Deeplink for debit card tracking screen, required [DebitCardTrackingScreenOptions]
  DEBIT_CARD_TRACKING_SCREEN = 101;
  // Deeplink to be used for external redirection to a url
  // This will be used majorly in notifications where we want to redirect the user to a URL
  // URL needs to be passed in screen options
  EXTERNAL_REDIRECTION = 102;
  // used to download data from digilocker
  // ref for old clients: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=7337%3A19314
  // ref for new clients: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43582
  DIGILOCKER_DOWNLOAD = 103;
  // this is the screen which will be shown to the when user wants to connect to a account post onboarding
  WEALTH_CONNECT_MY_ACCOUNT_SCREEN = 197;
  // screen for different types of wealth onboarding status
  // used for showing wealth onboarding statuses, like, future scope, etc.
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=290%3A602
  WEALTH_ONBOARDING_STATUS_SCREEN = 198;
  // customer signs agreement with epifi wealth
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=1293%3A2609
  WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN = 199;
  // to capture the missing data for customer like:
  // gender, marital status, pan photo, POA, POI, signature, live video etc
  // ref for old clients: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=290%3A546
  // ref for new clients: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43163
  WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN = 201; // intentional gap
  // final screen to get PEP and nationality confirmation
  // not used
  WEALTH_ONBOARDING_FINAL_CONFIRMATION_SCREEN = 202;
  // final document esign screen
  // not used
  WEALTH_ONBOARDING_DOCUMENT_ESIGN_SCREEN = 203;
  // document aadhaar esign screen if data is not present in KRA
  // this is to redirect to Aadhaar e-sign web-view, no client screen as such
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=290%3A663
  WEALTH_ONBOARDING_DOCUMENT_AADHAAR_ESIGN_SCREEN = 204;
  // Deeplink for gmail verification screen in app-screening
  APP_SCREENING_GMAIL_VERIFICATION = 205;
  // Deeplink for gmail verification polling screen
  APP_SCREENING_GMAIL_VERIFICATION_STATUS_POLLING = 206;
  // Deeplink for sending otp to email for verification
  SEND_WORK_EMAIL_OTP = 207;
  // Deeplink for verifying otp sent to email
  VERIFY_WORK_EMAIL_OTP = 208;
  // Linked in verification wait screen
  LINKEDIN_VERIFICATION_WAIT = 209;
  // Deeplink for redirecting user to npci common library for secure pin validation for card workflows.
  CARD_SECURE_PIN_VALIDATION = 210;
  // Deeplink for rewards promo banner
  REWARDS_PROMO_BANNER = 211;
  // Deeplink for search landing page
  ASK_FI_LANDING_PAGE = 212;
  // Deeplink for New screen showing vpa created successfully asking to set pin if user hasn't, triggered from in-app notification
  SUCCESSFUL_VPA_CREATION_SET_PIN = 213;
  // Deeplink for the liveness manual review screen
  LIVENESS_MANUAL_REVIEW = 214;
  // Client calls the InitiateCardAuthAction api
  INITIATE_CARD_AUTH_ACTION_API = 215;
  // Help search landing page deeplink
  HELP_SEARCH = 217;
  // subscription info page
  FIT_SUBSCRIPTION_INFO_PAGE = 218;
  // page displaying subscriptions grouped on tags
  FIT_SUBSCRIPTIONS_PREVIEW_PAGE = 219;
  // page displaying subscriptions grouped on rule
  FIT_ALL_SUBSCRIPTIONS_PAGE = 220;
  // Deeplink for all collections page
  FIT_ALL_COLLECTIONS_PAGE = 221;
  // Deeplink for collections info page, with a default tag selected
  FIT_COLLECTION_PAGE = 222;
  // Starts connected accounts SDK to connect an account
  CONNECTED_ACCOUNTS_SDK = 223;
  // Deeplink for handling error scenarios in wealth onboarding,
  // this is needed because based on different error cases we need to redirect users to different screen
  // this is also used in case user data is pending manual review
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=4712%3A15689
  WEALTH_ONBOARDING_ERROR_SCREEN = 224;
  // Deeplink for handling retry scenarios in case of transient or intermittent failures
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=290%3A653
  WEALTH_ONBOARDING_LANDING_SCREEN = 225;
  // Deeplink for chat with customer care
  CHAT_WITH_US_SCREEN = 226;
  // my rules page
  FIT_MY_RULES_PAGE = 227;
  // Deeplink for detailed error view screen
  DETAILED_ERROR_VIEW_SCREEN = 228;
  // Phone permission screen
  PHONE_PERMISSION_SCREEN = 229;
  // Deeplink for create recurring payment screen
  CREATE_RECURRING_PAYMENT_SCREEN = 230;
  // Deeplink for execute one time recurring payment screen.
  ONE_TIME_EXECUTE_RECURRING_PAYMENT_ORDER_SCREEN = 231;
  // Deeplink for landing user on account details screen for connected accounts
  // Since setting and UI is different for fi accounts and connected accounts we need a different deeplink
  CONNECTED_ACCOUNT_DETAILS_SCREEN = 232;

  // exchanger offer redemption screen
  REDEEM_EXCHANGER_OFFER = 250;

  // Below are individual data collection screens for wealth onboarding
  // Deeplink for collecting gender information
  COLLECT_GENDER_SCREEN = 251;

  // Deeplink for collecting marital status information
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=1566%3A1854
  COLLECT_MARITAL_STATUS_SCREEN = 252;

  // Deeplink for collecting income slab information
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=5874%3A17638
  COLLECT_INCOME_SLAB_SCREEN = 253 [deprecated = true];

  // Deeplink for collecting signature information
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=1566%3A1762
  COLLECT_SIGNATURE_SCREEN = 254;

  // Deeplink for collecting PAN details information
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=1566%3A1576
  COLLECT_PAN_SCREEN = 255;

  // Deeplink for collecting POA information
  // not used
  COLLECT_POA_SCREEN = 256;

  // Deeplink for call us feature
  // this is to take user to their Phone / Caller App
  // it is currently being used when verification of some documents failed for the user
  CALL_US_SCREEN = 257;

  // subscription history screen
  SUBSCRIPTION_HISTORY_SCREEN = 258;

  // help quick guides view all screen
  HELP_QUICK_GUIDES_VIEW_ALL_SCREEN = 259;

  // vkyc landing screen during onboarding
  ONBOARDING_VKYC_STATUS_SCREEN = 260;

  // FE vkyc notification API to mapped against this value to send notifications per metadata
  VKYC_NOTIFICATION_API = 261;

  // Info acknowledgement screen is used to display information or update to user
  // and, accept an acknowledgement to proceed further
  INFO_ACKNOWLEDGEMENT_SCREEN = 262;


  // Deeplink for View more screen from Search result's financial activities section
  SEARCH_VIEWMORE_FINANCIAL_ACTIVITIES = 263;

  // invested fund details screen is used to display information on user's investment in a mutual fund
  // info like current value, returns, autoinvest rules and fund activity
  INVESTED_FUND_DETAILS_SCREEN = 264;

  // mutual fund details screen will show user details about a mutual fund
  // details will include fund manages, returns of the mutual fund etc
  MUTUAL_FUND_DETAILS_SCREEN = 265;

  // screen to be shown when user's device is deemed incompatible by the backend
  // current screen option includes weblink to be shown to user which can be used to complete vkyc on different device
  VKYC_INCOMPATIBLE_DEVICE_SCREEN = 266;

  // Deeplink for displaying additional info to the user during wealth onboarding
  // not used
  WEALTH_ONBOARDING_INFO_SCREEN = 267;
  // recurring payment details screen is used to land on datails about the rp.
  RECURRING_PAYMENT_DETAILS_SCREEN = 268;
  // authorize recurring payment screen to be used to land where we authorize recurring payments
  AUTHORIZE_RECURRING_PAYMENT_SCREEN = 269;

  // Invest one time screen will enter user in one time investment flow
  ONETIME_MF_INVESTMENT_SCREEN = 271;

  // external story deeplink, msg content pointing to story URL
  STORY_SCREEN = 270;
  // sports challenge home screen
  FITT_SPORTS_CHALLENGE_HOME_SCREEN = 272;

  // deeplink to information pop-up to be displayed on the app
  // Ref- https://www.figma.com/file/FUObqwCZu7ShU7fhPH7QgA/FFF-%2F-Home-%26-Summary-%2F-Feb-2022?node-id=296%3A3673
  INFORMATION_POPUP = 273;

  FITTT_SPORTS_CHALLENGE_DETAILS_SCREEN = 274;

  // deprecated to reduce user drop-offs
  // deeplink for introductory information which will be provided to the user when they pass via the data collection flow
  // this would be shown either via digilocker flow or collecting missing data flow
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=7337%3A17942
  WEALTH_ONBOARDING_DATA_COLLECTION_INTRO_SCREEN = 275 [deprecated = true];

  // Deeplink to open filtered account view in all transactions screen
  ALL_TRANSACTIONS_ACCOUNT_FILTER = 276;

  // Deeplink to redirect to app/playstore to update the app
  UPDATE_APP_SCREEN = 277;

  // deeplink to open mutual funds list view screen
  // ref:https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2F-Wealth-%2F-v1.0-%2F-5-Aug?node-id=1663%3A17270
  MUTUAL_FUNDS_LIST_VIEW_SCREEN = 278;

  // deeplink to open mutual funds investment screen tab
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2F-Wealth-%2F-v1.0-%2F-5-Aug?node-id=1663%3A17947
  MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN = 279;

  // eligibility screen for p2p investment
  P2P_INVESTMENT_ELIGIBILITY_CHECK_SCREEN = 280;

  // will be used to show more info on p2p investment
  P2P_INVESTMENT_KNOW_MORE_SCREEN = 281;

  // will be used to show more prerequisites on p2p investment
  P2P_INVESTMENT_UNLOCK_ACCESS_SCREEN = 282;

  // eligibility check result screen for p2p investment
  P2P_INVESTMENT_ELIGIBILITY_CHECK_RESULT_SCREEN = 283;

  // Deeplink for P2P invest screen
  // ref: https://www.figma.com/file/FjiOYQ7c1jdM2WYKU2Jwv7/P2P-%2F-Workfile?node-id=943%3A25200
  P2P_INVESTMENT_INVEST_SCREEN = 284;

  // used to show p2p dashboard
  P2P_INVESTMENT_DASHBOARD_SCREEN = 285;

  // toggle screen where user can enable/disable vpa
  ENABLE_VPA = 286;

  // screen for forgot pin flow
  FORGOT_PIN_SCREEN = 287;

  // screen for reset pin flow
  RESET_PIN_SCREEN = 288;

  // Deeplink for P2P investment withdrawal screen
  P2P_WITHDRAW_INVESTMENT_SCREEN = 289;

  // Benefits screen for connected accounts for users who have already completed wealth onboarding
  // and repeatedly trying out connect new account feature
  CONNECTED_ACCOUNT_BENEFITS_SCREEN = 290;

  // Screen to show list of all savings accounts of user in a single view with balances on client including
  // connected accounts
  SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN = 291;

  // This starts a flow of connecting an account. This is not a screen flow because user could be
  // Wealth onboarded or not. Based on user state it will take the user to complete Wealth onboarding
  // or Connect an Account if already onboarded.
  CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW = 292;

  // This deeplink is for cases in case user wants to connect account with phone number p1 but has previously connected
  // accounts with phone number p2. We prompt user to delete all previous accounts as part of our reoobe handling
  // Options for this deeplink contains the list of accounts which will be deleted as part of this action.
  CONNECTED_ACCOUNT_HANDLE_REOOBE = 293;

  // Deeplink to let users know transactions are disabled for a particular bank/fip which is connected via AA ecosystem
  // Client will explicitly open a bottom sheet with title and subtitle to let users know this information.
  // Backend can pass this deeplink in various places for instance : home savings accounts summary screen.
  CONNECTED_ACCOUNT_TRANSACTIONS_COMING_SOON = 294;

  // screen to show p2p specific error/coming soon info to the customer
  // sample usage: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/P2P-%2F-FFF?node-id=8%3A4818
  P2P_INVESTMENT_ERROR_SCREEN = 295;

  // Screen to show if there was any issue with collecting data from the user
  // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=1566%3A1730
  WEALTH_ONBOARDING_DATA_COLLECTION_ERROR_SCREEN = 296;

  // This deeplink is used to land on the Amount entry screen on the App. This screen may be reached from various
  // sources like Collect request, User timeline, Search query, Pay intents from other apps
  ENTER_AMOUNT_SCREEN = 297;

  // this deeplink is used after vkyc landing screen
  VKYC_INSTRUCTION_SCREEN = 298;

  // vkyc income occupation update screen
  VKYC_PRE_REQUISITE_SCREEN = 299;

  // salary program intro page
  SALARY_PROGRAM_INTRO_SCREEN = 300;
  // salary program account benefits page
  SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN = 301;
  // employer confirmation screen for salary program
  SALARY_PROGRAM_EMPLOYER_CONFIRMATION_SCREEN = 302;

  // ACCOUNT_CLOSURE_TRANSFER_INITIATED deeplink
  ACCOUNT_CLOSURE_TRANSFER_INITIATED = 303;
  // vkyc status screen
  // this screen can be used by callers as CTA to move the screen to VKYC screen
  // where VKYC as a owner will decide the content of the screen
  VKYC_STATUS_SCREEN = 304;

  // deeplink for analyser
  ANALYSER_SCREEN = 305;

  // deeplink for Salary Program transactions page
  SALARY_PROGRAM_TRANSACTIONS_SCREEN = 306;

  // Finvu account disconnect web view for connected accounts
  FINVU_ACCOUNT_DISCONNECT_SCREEN = 307;

  // deeplink to mutual fund collections landing page with pre-selected collection
  MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN = 308;

  // deeplink to mutual fund filters screen with pre-selected filters
  MUTUAL_FUND_FILTERS_SCREEN = 309;

  // deeplink to mutual fund order receipt
  MUTUAL_FUND_ORDER_RECEIPT_SCREEN = 310;

  // offer details screen for pre approved loan
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3054
  PRE_APPROVED_LOAN_OFFER_DETAILS_SCREEN = 311;

  // application details screen for pre approved loan
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3122
  PRE_APPROVED_LOAN_APPLICATION_DETAILS_SCREEN = 312;

  // used to confirm loan application using OTP
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2830%3A3081
  PRE_APPROVED_LOAN_APPLICATION_CONFIRMATION_VIA_OTP_SCREEN = 313;

  // used to trigger application status poll
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2801%3A34647
  PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN = 314;

  // used to show loan application status
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2801%3A34673
  PRE_APPROVED_LOAN_APPLICATION_STATUS_SCREEN = 315;

  // deeplink for wealth profile page for a user             │
  WEALTH_PROFILE_SCREEN = 316;

  // this screen will be used to make navigation to pre-approved loan tab
  PRE_APPROVED_LOAN_LANDING_SCREEN = 317;

  // used to show bottom screen in AA account actions
  CA_ACCOUNT_ACTIONS_SCREEN = 318;

  // used when we want to show instructions before continuing to do an action
  // https://www.figma.com/file/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=2586%3A13347
  INSTRUCTIONS_SCREEN = 319;

  // used to show the dashboard for pre approved loan
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A12608
  PRE_APPROVED_LOAN_DASHBOARD_SCREEN = 320;

  // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43295
  WEALTH_ONBOARDING_COLLECT_INCOME_SCREEN = 321;

  // used to show error screen for OTP attempts exhausted
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A4934
  PRE_APPROVED_LOAN_ERROR_SCREEN = 322;

  // will be used to show more info on pre approved loan
  PRE_APPROVED_LOAN_KNOW_MORE_SCREEN = 323;

  PRE_APPROVED_LOAN_HOW_TO_APPLY_SCREEN = 324;

  // screen will be used to show loan details of a particular loan for a user
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A12681
  PRE_APPROVED_LOAN_DETAILS_SCREEN = 325;

  // will be used during user waiting for agent
  VKYC_USER_WAITING_SCREEN = 326;

  // screen options for preapproved loan cancel application deeplink
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3724%3A11307
  PRE_APPROVED_LOAN_CANCEL_APPLICATION_SCREEN = 327;

  // deeplink to salaryprogram referrals landing screen
  SALARY_PROGRAM_REFERRALS_LANDING_SCREEN = 328;

  // Collect nominee for user (used in wealth onboarding, investments)
  //ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=11106%3A46255
  COLLECT_NOMINEE_DETAILS_SCREEN = 329;

  // Entry Screen for preapproved loan Esign KFS deeplink
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=6108%3A17153
  PRE_APPROVED_LOAN_INITIATE_ESIGN_SCREEN = 330;

  // Screen for the In-app notifications center in the App
  NOTIFICATION_CENTER = 331;

  // collects Disclosure for usstocks OnBoarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_DISCLOSURE_SCREEN = 332;

  // collects Disclaimer for usstocks OnBoarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_DISCLAIMER_SCREEN = 333;

  // collects Employment Details for usstocks OnBoarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_EMPLOYMENT_DETAILS_SCREEN = 334;

  // Disclaimer are not meet  for usstocks OnBoarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_DISCLOSURE_STATUS_SCREEN = 335;

  // Auto Fetch Address screen for usstock onboarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_AUTO_FETCH_ADDRESS_SCREEN = 336;

  // SetUp Source of fund  for usstocks OnBoarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_SOURCE_OF_FUND_SCREEN = 337;

  // Analyser HUB screen which would be the analyser landing page which contains curation of several analyser
  ANALYSER_HUB = 338;

  // Represent success screen for onboarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_SUCCESS_SCREEN = 339;

  // Represent setup screen for onboarding
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_SETUP_SCREEN = 340;

  // screen will be used for checking status of any firefly request
  // Client will show waiting screen and poll for the status via this screen
  FIREFLY_GET_REQUEST_STATUS = 341;

  // Deeplink for card offers screen
  CARD_OFFERS_SCREEN = 342;

  // Screen for the QnA screen coming on hitting the "know more" link on the landing page
  CARD_KNOW_MORE_SCREEN = 343;

  // Screen for checking the bill generation date as well as the credit limit before hitting the cta for activating the card
  FIREFLY_CARD_ACTIVATION_SCREEN = 345;

  // Screen to enable online payments for the card . This screen is shown once the card is activated for digital use
  ONLINE_CARD_PAYMENTS_ENABLEMENT_SCREEN = 346;

  CARD_LIVENESS_MANUAL_REVIEW_SCREEN = 347;

  // Deeplink for polling status of auth flow and getting next action
  AUTH_STATUS_POLL_SCREEN = 348;

  // deeplink to record vpa migration consent and migrate to new vpa
  VPA_MIGRATION = 349;

  // Represents the pre-pay screen for pre-approved loans
  // ref: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A13559
  PRE_APPROVED_LOAN_PRE_PAY_SCREEN = 350;

  // Represent Investment range collection from user for USStocks
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
  USSTOCKS_ONBOARDING_INVESTMENT_RANGE_SCREEN = 351;

  // represents card dashboard screen
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8219%3A40690
  CREDIT_CARD_DASHBOARD_SCREEN = 352;

  // screen to display the options for bill generation
  // date and payment due date
  CREDIT_CARD_BILL_DATES_SELECTION_SCREEN = 353;

  // screen to select the address for card delivery
  // out of the given list of addresses
  CREDIT_CARD_ADDRESS_SELECTION_SCREEN = 354;

  // screener check selection screen in screener
  // ref: https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/Onboarding-%2F-Workfile?node-id=13399%3A119260
  SCREENER_CHOICE = 355;

  // employer selection screen in screener
  // ref: https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/Onboarding-%2F-Workfile?node-id=13513%3A119007
  SCREENER_EMPLOYER_SELECTION = 356;

  // screen to call the card creation API
  CREDIT_CARD_CARD_CREATION_SCREEN = 357;

  // screen to display that the user has permanently failed the auth required for credit card
  CREDIT_CARD_PERMANENT_FAILURE_SCREEN = 358;

  // VKYC_START_CALL screen indicates client to start vkyc call
  // at time of implementation client have to make call to registerUser, updateMetaData and InitiateLiveVKTC
  VKYC_START_CALL = 359;

  // screen to display list of consents
  GENERIC_RECORD_CONSENT = 360;

  // Represent Buy screen for stock
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A5294
  USSTOCKS_BUY_SCREEN = 361;

  // Represent Order Success Screen
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A5294
  USSTOCKS_ORDER_STATUS_SCREEN = 362;

  // Represent Sell Screen
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A5294
  USSTOCKS_SELL_SCREEN = 363;

  // Represent Order details Screen
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A5294
  USSTOCKS_ORDER_DETAILS_SCREEN = 364;

  // Fi Primary Savings summary screen
  PRIMARY_SAVINGS_SUMMARY_SCREEN = 365;

  // Landing Screen for Investments
  INVESTMENT_LANDING_SCREEN = 366;

  // screen to display all transactions for pre-approved loans
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=5528%3A24094
  PRE_APPROVED_LOAN_ALL_TRANSACTIONS_SCREEN = 367;

  // edit info on profile screen
  EDIT_PROFILE_INFO = 368;

  // generic screen for polling status on the request provisioned
  STATUS_POLLING = 369;

  // Screen to Display ETF/Stocks Holding
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1950%3A7398
  USSTOCKS_PORTFOLIO_SCREEN = 370;

  // Screen to display search and list of symbol
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1950%3A6993
  USSTOCKS_EXPLORE_SYMBOLS_SCREEN = 371;


  // Screen to Display Symbol Details
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5459
  USSTOCKS_SYMBOL_DETAILS_SCREEN = 372;

  // Screen to Display Account Activity
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1854%3A5480
  USSTOCKS_ACCOUNT_ACTIVITY_SCREEN = 373;

  // generic prompt
  GENERIC_PROMPT = 374;

  // Screen for credit card fetch card control limits
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9468%3A42159
  CREDIT_CARD_FETCH_LIMITS = 375;

  // Screen for credit card set control limits
  CREDIT_CARD_SET_LIMITS = 376;

  // Screen for verifying qr code of credit card
  CREDIT_CARD_VERIFY_QR_CODE = 377;

  // Screen for setting cc pin
  CREDIT_CARD_SET_CARD_PIN = 378;

  // Screen for setting cc preferences
  CREDIT_CARD_SET_CARD_PREFERENCES = 379;

  FEDERAL_SECURE_PIN = 380;

  // Screen options for jump available plans screen
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A11810
  P2P_INVESTMENT_AVAILABLE_PLANS_INFO = 381;

  // Screen options for jump choose plan screen
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A11260
  P2P_INVESTMENT_CHOOSE_PLAN = 382;

  // Screen options for jump unlock plan screen
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A11410
  P2P_INVESTMENT_UNLOCK_PLAN = 383;

  // Screen for new card request
  CREDIT_CARD_NEW_CARD_REQUEST = 384;

  // Screen to select or enter amount to withdraw
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A12025
  // Client is supposed to call P2PInvestments's 'GetDeeplink' rpc for rendering this
  P2P_WITHDRAW_MONEY_ENTER_AMOUNT = 385;

  // Screen to show withdrawal amount break across different scheme
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A12065
  // Client is supposed to call P2PInvestment's 'GetWithdrawMoneyAttributes' rpc for rendering this
  P2P_WITHDRAW_MONEY_SUMMARY = 386;

  // Screen for credit card usage
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9468%3A41945
  CREDIT_CARD_USAGE_SCREEN = 387;

  // Screen for credit card controls
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9932%3A49539
  CREDIT_CARD_CONTROLS_SCREEN = 388;

  // Screen for credit card success
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9468%3A42651
  CREDIT_CARD_SUCCESS_BOTTOM_VIEW_SCREEN = 389;

  // Screen for SD/FD template screens
  DEPOSIT_TEMPLATES_SCREEN = 390;

  // Screen for credit card VKYC landing screen
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=6530%3A51906&t=I07Mg1R1pSWHs5J8-1
  CREDIT_CARD_VKYC_LANDING_SCREEN = 391;
  // screen for deeplink to submit billing days of the month
  CREDIT_CARD_SUBMIT_BILLING_DATE_SCREEN = 392;

  // screen for freeze/unfreeze credit card
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9958%3A42943&t=NAgLptKHM52JhS6L-4
  CREDIT_CARD_FREEZE_UNFREEZE_SCREEN = 393;

  // screen for selecting address for new card request flow
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=10206%3A42451&t=6ZEzuRVnXmesaR5i-4
  CREDIT_CARD_NEW_CARD_REQUEST_ADDRESS_SELECTION = 394;

  // screen to start bill payment flow for credit card
  CREDIT_CARD_BILL_PAYMENT_SCREEN = 395;

  // screen to view the statement for the card
  CREDIT_CARD_STATEMENT_SCREEN = 396;

  // screen to view unmasked card details
  CREDIT_CARD_DETAILS_SCREEN = 397;

  // used to trigger loan pre-payment/closer payment status poll
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=7889%3A35831
  PRE_APPROVED_LOAN_ACTIVITY_STATUS_POLL_SCREEN = 398;

  // used to show loan activity status
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=9783%3A10358
  PRE_APPROVED_LOAN_ACTIVITY_STATUS_SCREEN = 399;

  // used to hit InitiateCardReq API
  INITIATE_CARD_REQUEST_SCREEN = 400;

  // deeplink for polling credit report download status and getting next action
  CREDIT_REPORT_POLL_STATUS = 401;

  // deeplink for showing credit report consent screen and recording the consent (New consent flow)
  CREDIT_REPORT_CONSENT_V2 = 402;

  // screen for validating npci secure pin
  AUTH_NPCI_PIN_VALIDATION = 403;

  // screen for verifying otp
  AUTH_SMS_OTP_VERIFICATION = 404;

  // Deeplink for home explore screen
  HOME_EXPLORE = 405;

  // screen to capture auth flow selected by user given the option
  CREDIT_CARD_AUTH_OPTIONS_SCREEN = 406;

  // Represent screen for cancel order
  // Note: it indicates a bottom sheet
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1341%3A5551&t=EoYsAyBXsrJvjkGG-4
  USSTOCKS_CANCEL_ORDER_SCREEN = 407;
  // screen where debit card related info is reflected along with order amount
  ORDER_PHYSICAL_DEBIT_CARD_INFO_SCREEN = 408;

  // used to show investment break up for the investor across different schemes
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A12694&t=Pzrym1gFvcHLb1oZ-0
  P2P_INVESTMENT_VIEW_BREAKUP_SCREEN = 409;

  // Represents screen for fund transfer status polling
  FUND_TRANSFER_STATUS_SCREEN = 410;

  // This will be used to start credit report download workflow for analyser when credit report is missing for an actor.
  INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER = 411;

  // This will be used to record auth completion
  RECORD_AUTH_FLOW_COMPLETION = 412;

  // this will be used to show failure reason for credit report download
  CREDIT_REPORT_DOWNLOAD_FAILURE_SCREEN = 413;

  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9224%3A42854&t=kFafEIK1DJ3rPuzy-0
  // Screen for view all credit card transactions
  CREDIT_CARD_VIEW_ALL_TRANSACTIONS = 414;

  // Deeplink to show tier introduction screen
  // https://www.figma.com/file/lkZbcqGu9g5p3dvkGcttWL/Account-Tier-%E2%80%A2-Workfile?node-id=834%3A58125&t=2T0nG0AytyLvkMWh-0
  TIER_INTRODUCTION = 415;

  // Deeplink to show tier all plans page, please use TIER_ALL_PLANS_REDIRECTION to load this page
  // https://www.figma.com/file/lkZbcqGu9g5p3dvkGcttWL/Account-Tier-%E2%80%A2-Workfile?node-id=744%3A55110&t=tAZCZcA0xlf0e2Sn-0
  TIER_ALL_PLANS = 416;

  // Deeplink to show particular tier plan
  TIER_PLAN = 417;

  // Deeplink to show bottomsheet for a particular tier benefit
  TIER_BENEFIT_BOTTOMSHEET = 418;

  // Deeplink to show bottomsheet summary for all benefits in a tier
  TIER_ALL_BENEFITS_BOTTOMSHEET = 419;

  // Deeplink to show loan transaction receipt
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=5263%3A15963&t=PkQl2kPMuvRwD6qw-4
  PRE_APPROVED_LOAN_TRANSACTION_RECEIPT_SCREEN = 421;

  // Screen for cc transactions
  CREDIT_CARD_TRANSACTION_RECEIPT = 422;

  // Deeplink to show bottom sheet on invest landing screen
  INVEST_ADD_MONEY_POP_UP = 423;

  // Screen for raising dispute for credit card transactions
  CREDIT_CARD_RAISE_DISPUTE = 424;

  // Screen for collecting details for credit card disputes
  CREDIT_CARD_DISPUTE_DETAILS = 425;

  // figma :- https://www.figma.com/file/g2x9BTVs01uHsAZ6D8g6ss/Video-KYC---FFF---26-Nov-2020?node-id=9716%3A31746&t=QWoCu3q5To2i8mZ8-0
  // represent screen with multiple tiles
  // tile size could be 50% or 100% of screen
  TILES_PROMPT = 426;

  // figma: https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32164&t=GrxlYiI3Y3YVc5uf-0
  // Screen for acknowleding user details and getting new chequebook
  ORDER_CHEQUEBOOK = 427;

  // figma: https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32571&t=GrxlYiI3Y3YVc5uf-0
  // Screen for showcasing processing details for the new chequebook
  CHEQUQBOOK_ORDER_DETAILS = 428;

  // collects PAN card image for US stocks onboarding
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=335%3A5524&t=S8wtzVQHLKLwaLg5-4
  USSTOCKS_ONBOARDING_COLLECT_PAN_CARD_SCREEN = 429;

  // Tier manual upgrade redirection deeplink to tell client that user wants to manually upgrade the tier
  TIER_MANUAL_UPGRADE = 430;

  // Success screen on tier upgrade
  TIER_UPGRADE_SUCCESS_SCREEN = 431;

  // screen where client will poll for the generated documents
  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4426%3A19164&t=UI9hshKaPpsmwltI-4
  USSTOCKS_ONBOARDING_DOCUMENT_POLLING_SCREEN = 432;

  // figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=11210%3A60317&t=U8okulmu868wdb5r-4
  // screen when user is showed the loan tab but has no valid offer, hence is a non qualified user
  PRE_APPROVED_LOAN_NON_QUALIFIED_USER_LANDING_SCREEN = 433;

  USSTOCKS_PAN_REUPLOAD_SCREEN = 434;

  INSIGHTS_STORIES_SCREEN = 435;

  // Deeplink to redirect users to all tier plan, this will call internally to GetAllTierPlan in tiering service
  TIER_ALL_PLANS_REDIRECTION = 436;

  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4426%3A19164&t=UI9hshKaPpsmwltI-4
  // success / polling screen for account on-boarding
  USSTOCKS_ONBOARDING_POLLING_SCREEN = 437 [deprecated = true];

  // Generic deeplink for all halt screens in the credit card flows
  CREDIT_CARD_GENERIC_HALT_SCREEN = 438;

  // screen where we show the list of stocks of the given collection
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3939%3A18319&t=wB7RpemkH89VKc9k-4
  USSTOCKS_COLLECTION_SCREEN = 439;

  // app download screen for web flow
  WEB_APP_DOWNLOAD = 440;

  // SCREENER_CONNECTED_ACCOUNTS_INFO is showed during onboarding flow as an
  // intro screen to connect accounts for screener.
  // figma: https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=20590%3A93127&t=c71d0KQ4wQcQ7q72-0
  SCREENER_CONNECTED_ACCOUNTS_INFO = 441;

  // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=21544%3A47471&t=tQQpwBVShTQhqoEL-4
  ACCOUNT_DELETION_ACKNOWLEDGEMENT = 442;

  // deeplink to send when we want to sign out the user
  USER_SIGN_OUT = 443;

  USSTOCKS_LANDING_SCREEN = 444;

  // Screen for showing user some messaging and reason for delay
  // Figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=5016%3A21052&t=K4t4aEkIROoZMzTQ-4
  USSTOCKS_CHECKING_INFORMATION_SCREEN = 445;

  // Screen used to show post consent approval activities
  // https://www.figma.com/file/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=3179%3A28667&t=pUV7SwJPsrDSR3u2-0
  CA_POST_CONSENT_POLLING_SCREEN = 446;

  // Screen used to show if account data pull is Success/Pending or Failure screen due to failure in consent request
  // https://www.figma.com/file/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=3179%3A28667&t=pUV7SwJPsrDSR3u2-0
  CA_POST_CONSENT_TERMINAL_STATE_SCREEN = 447;

  // Screen used to show custom amount screen for credit card bill payments.
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=11436%3A56480&t=FqrqWEngCJqthaq8-0
  CREDIT_CARD_CUSTOM_AMOUNT_REPAYMENT_SCREEN = 448;

  // Screen used to show validation error dialog in case of order or account validation
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3595%3A16719&t=kkYaVShWwvxVP1Yg-4
  US_STOCK_VALIDATION_DIALOG = 449;

  //Screen used to redirect to Credit card payment options screen
  CREDIT_CARD_BILL_REPAYMENT_SELECTION_SCREEN = 450;

  //Screen used to show in app credit card statements.
  // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582%3A176745&t=AGAdvVy6FXj2pIGt-0
  CREDIT_CARD_STATEMENT_VIEW_SCREEN = 451;

  //Screen used to export credit card statement
  CREDIT_CARD_EXPORT_STATEMENT = 452;

  // In app payment status polling screen
  CREDIT_CARD_PAYMENT_STATUS_POLLING_SCREEN = 453;

  //In app payment status screen
  CREDIT_CARD_PAYMENT_STATUS_SCREEN = 454;

  // GENERIC_TRANSITION_SCREEN is a nested deeplink which contains an image and text to show for
  // x seconds after which the next deeplink is shown
  // figma: https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=22564%3A48092&t=5JjajJpBBIwCkr18-0
  GENERIC_TRANSITION_SCREEN = 455;

  // this screen will be used to show physical card delivery status
  CREDIT_CARD_PHYSICAL_CARD_TRACKING_SCREEN = 456;

  // Screen used to display the details of the dispute raised by the user
  CREDIT_CARD_TXN_DISPUTE_DETAILS = 457;

  // will be used to show prerequisites check on usstocks investment
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=7665%3A26896&t=1WSWtF1JXOQEdG3r-0
  USSTOCKS_PRE_REQUISITE_CHECK_SCREEN = 458;

  // Insight hub represents analyser INSIGHT HUB
  INSIGHT_HUB = 459;

  // Rendered using 'GetGenerateOTPScreen' rpc in frontend.p2pinvestment
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11726%3A15473&t=BiCKQbpc2i2RVwPf-0
  P2P_INVESTMENT_OTP_SCREEN = 460;

  // Screen used to confirm the reinvestment in jump
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11889%3A14949&t=ftRY9JIOn9opQKmF-0
  // Rendered using 'GetRenewInvestmentAttributes' rpc
  P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN = 461;

  // Screen used to get liveness summary status from auth orchestrator
  AUTH_LIVENESS_SUMMARY_STATUS_POLL_SCREEN = 462;

  // Physical card dispatch success screen
  PHYSICAL_CARD_DISPATCH_SUCCESS_SCREEN = 463;

  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15670&t=CQGbtXPd1G8WbTAA-4
  // Rendered using P2PInvestmentCurrentStatusScreenOptions
  P2P_INVESTMENT_CURRENT_STATUS_SCREEN = 464;

  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=12356%3A22973&t=k4CuMDGdVMPFu0vi-4
  // Rendered using P2PInvestmentActivityDetailsScreenOptions
  P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN = 465;

  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=12425%3A17185&t=pK35XeQjNmkZjNiK-4
  // Rendered using 'GetAllUpcomingRenewals' rpc.
  // No screen option needed.
  P2P_INVESTMENT_ALL_UPCOMING_RENEWALS_SCREEN = 466;

  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15607&t=Sboiw3jG1WYq6w04-4
  // Rendered using P2PInvestmentRenewalCancellationScreenOptions
  P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN = 467;

  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=7021%3A24982&t=Au2B3yfiJigUN88O-0
  // Rendered using UsStocksSOFWaitingScreenOptions
  USSTOCKS_SOF_WAITING_SCREEN = 468;

  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=8950%3A72399&t=Ruv4Z2O0F2iAs7SS-4
  // Used for dynamic banner on home page for promoting save instruments
  SAVE_PROMOTIONAL_BANNER_HOME = 469;

  // Deeplink for debit card offer details page.
  DEBIT_CARD_OFFER_DETAILS_SCREEN = 470;

  // Deeplink for present dialog to user for cases where forex rate has changed while placing order
  NEW_FOREX_RATE_CONFIRMATION_DIALOG = 471;

  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3976%3A11043&t=S3pgSftImyEpyGl5-4
  // OTP collection and verification screen
  MF_HOLDINGS_IMPORT_OTP_SCREEN = 472;

  // MF holdings import Loading screen
  MF_HOLDINGS_IMPORT_LOADING_SCREEN = 473;

  // MF Holdings import initiate screen
  MF_HOLDINGS_IMPORT_INITIATE_SCREEN = 474;

  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3976%3A10911&t=S3pgSftImyEpyGl5-4
  // Consent collection screen
  MF_HOLDINGS_IMPORT_CONSENT_SCREEN = 475;

  // Deeplink to mane upi number screen
  MANAGE_UPI_NUMBER_SCREEN = 476;

  // Deeplink to introduce user to what upi number mean
  UPI_NUMBER_INTRO_SCREEN = 477;

  // Deeplink for cc rewards pie chart view
  // figma: https://www.figma.com/file/UO85a3b8ayLW9ZSFPhzRLi/CC-Rewards?node-id=813%3A33344&t=24SjfmiQG7Lcq4v0-0
  CREDIT_CARD_EXTRA_REWARDS_BOTTOM_VIEW_SCREEN = 478;

  // Deeplink for cc eligible merchant rewards screen
  CREDIT_CARD_ELIGIBLE_MERCHANT_REWARDS_SCREEN = 479;

  // Deeplink for cc emi dashboard screen
  CREDIT_CARD_EMI_DASHBOARD_SCREEN = 480;

  // Deeplink for cc emi view all active loan accounts screen
  CREDIT_CARD_EMI_VIEW_ALL_ACTIVE_LOAN_ACCOUNTS_SCREEN = 481;

  // Deeplink for cc emi view all eligible transactions screen
  CREDIT_CARD_EMI_VIEW_ALL_ELIGIBLE_TRANSACTIONS_SCREEN = 482;

  // Deeplink for cc emi transactions loan offers screen
  CREDIT_CARD_EMI_TRANSACTION_LOAN_OFFERS_SCREEN = 483;

  // Deeplink for cc emi loan account details screen
  CREDIT_CARD_EMI_LOAN_ACCOUNT_DETAILS_SCREEN = 484;

  // in effort to make vkyc next action driven and for easy rollback plan we don't want to continue with existing landing screen
  // intro screen contains offer tiles and introduction to vkyc process
  VKYC_INTRO = 485;

  // client makes call to GetVKYCNextAction Api
  GET_VKYC_NEXT_ACTION_API = 486;

  // Credit card benefits screen
  CREDIT_CARD_BENEFITS_SCREEN = 487;

  // Screen for cc rewards selection
  CREDIT_CARD_REWARDS_SELECTION_SCREEN = 488;

  // Screen for polling cc rewards transaction
  CREDIT_CARD_REWARDS_POLLING_SCREEN = 489;

  // VKYC pan type selection screen
  VKYC_PAN_TYPE_SELECTION = 490;

  // vkyc base screen mandatory for all caller
  VKYC_INSTRUCTIONS = 491;

  // Address confirmation screen for PL
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=4321%3A153632&t=8r8CkVDIkiosdHWL-1
  PRE_APPROVED_ADDRESS_CONFIRMATION_SCREEN = 492;
  // Entry Screen for preapproved loan E-NACH deeplink
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=3503%3A95604&t=dwLExwCMBhglJV3e-1
  PRE_APPROVED_MANDATE_INITIATE_SCREEN = 493;

  // Deeplink for cc emi preview pre close loan screen
  CREDIT_CARD_EMI_PREVIEW_PRE_CLOSE_LOAN_SCREEN = 494;

  // Deeplink for cc emi pre close loan success screen
  CREDIT_CARD_EMI_PRE_CLOSE_LOAN_SCREEN = 495;

  // Generic bottom sheet screen having title, subtitle and cta list
  // ref: https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/Video-KYC-Workfile?node-id=9853%3A55203&t=zT6a4S6VfKQ8VXh1-0
  GENERIC_BOTTOM_SHEET = 496;

  // Generic screen for showing order processing
  // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14237%3A50746&t=rsd1FqJIWmIgdjju-0
  ORDER_PROCESSING_SCREEN = 497;

  // Pre-approved loan e-sign url webview screen
  PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN = 498;

  // Represent bottom sheet to show when buy/sell flow is closed for the stock
  // Addociated screen options: USStocksBuySellClosedScreenOptions
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4430%3A19481&t=OfbwOvDvEkCWhSoI-4
  USSTOCKS_BUY_SELL_CLOSED_SCREEN = 499;

  // Deeplink to show user's account balance history
  BALANCE_HISTORY_SCREEN = 500;

  // Wealth onboarding investment advisory agreement screen
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14387%3A50967&t=Sh7Ge8sWA6qKaxfO-0
  WEALTH_ONBOARDING_IA_AGREEMENT_SCREEN = 501;


  // Employment Details Screen for PL
  // ref: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=4321%3A154191&t=M3bYpm8JFUgvRaOm-1
  PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN = 502;

  // Occupation Selection Screen for PL
  // ref: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=4321%3A154191&t=M3bYpm8JFUgvRaOm-1
  PRE_APPROVED_OCCUPATION_SELECTION_SCREEN = 503;

  // Income Selection Screen for PL
  // ref: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=4321%3A154191&t=M3bYpm8JFUgvRaOm-1
  PRE_APPROVED_INCOME_SELECTION_SCREEN = 504;

  // Entry point screen for Reminders
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A1989&t=jHlCa5pafzhqCSNF-0
  REMINDERS_ENTRY_POINT = 505;

  // Pre Approved Info Dialog for PL which is being currently used to show Hard Pull Info Dialog
  // ref: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=15220%3A22173&t=8skjkIfG3H53FlYi-1
  PRE_APPROVED_INFO_DIALOG_SCREEN = 506;

  // Screen to switch between the old and new QR SDK
  PAY_ML_KIT_QR_SCREEN = 507;

  // List of reminders subscriptions list
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=144%3A2167&t=LdIti6U7vSYmU6hi-0
  REMINDERS_SUBSCRIPTIONS_LIST = 508;

  // Reminders landing page screen
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A2704&t=LdIti6U7vSYmU6hi-0
  REMINDERS_LANDING_PAGE = 509;

  // salary program benefits calculator page
  SALARY_PROGRAM_BENEFITS_CALCULATOR_SCREEN = 510;

  // Deeplink for the new auto invest fitt collection redirection.
  AUTO_INVEST_COLLECTION_BOTTOM_VIEW_SCREEN = 511;

  // Entry screen for e-pan flow
  START_EPAN = 512;

  // Reminders supported topics page
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=55%3A2002&t=Jn3HhgY9fB4aCnvu-0
  REMINDERS_SUPPORTED_TOPICS_ENTRY_SCREEN = 513;

  // Reminders config screen
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A3025&t=Jn3HhgY9fB4aCnvu-0
  REMINDERS_CONFIG_SCREEN = 514;

  // Reminders CC config dates related screen
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A819&t=Jn3HhgY9fB4aCnvu-0
  REMINDERS_CC_CONFIG_DATES_SCREEN = 515;

  // Reminders supported topics list screen
  // ref: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A3115&t=Jn3HhgY9fB4aCnvu-0
  REMINDERS_SUPPORTED_TOPICS_LIST_SCREEN = 516;

  // Screen for showing details of a reward
  REWARD_DETAILS_SCREEN = 517;

  // Represents the screen where we show user the updated interest rate details as per the details entered by user.
  // ref: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=15453%3A29350&t=BXI22tBlNVl1W6Sg-4
  PL_UPDATED_RATE_SCREEN = 518;

  // client makes call to UpdateCallInfo Api
  UPDATE_CALL_INFO_API = 519;

  // Generic screen for showing OTP
  // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14180%3A51960&t=rsd1FqJIWmIgdjju-0
  OTP_SCREEN = 520;

  // screen options will contain question and answer list and other details
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14687%3A51549&t=rF1iAgl9NIiEjm2R-0
  INVESTMENT_RISK_PROFILE_QNA_SCREEN = 521;

  // Screen options will contain just the investment strategy card
  // Client calls frontend.investment.profile.GetRiskProfileDashboard RPC
  // to get the remaining details to be populated in this screen
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14853%3A52339&t=rF1iAgl9NIiEjm2R-0
  INVESTMENT_RISK_PROFILE_DASHBOARD_SCREEN = 522;

  // Deeplink to UPI Safety tips screen, responsible for displaying videos of UPI safety tips
  // Figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=12848-87062&t=LW0oRIhjJkp7lYqP-0
  UPI_SAFETY_TIPS_SCREEN = 523;

  // Polling screen to get next deeplink for investments risk profile
  // Client calls frontend.investment.profile.GetNextScreenForRiskProfile RPC to get next deeplink and display that
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14940-52901&t=Xh03H0P8mV5HzyZM-0
  INVESTMENT_RISK_PROFILE_POLLING_SCREEN = 524;

  // generic screen for powering all the waitlist related screens org wide .
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=19876-78852&t=fjPyfeKHgdW5z10A-0
  IN_APP_WAITLIST_SCREEN = 525;
  // generic screen to display after successful addition of the user to the waitlist
  IN_APP_WAITLIST_CONFIRMATION_SCREEN = 526;

  // Shared screen - information bottom sheet when disconnecting and deleting account
  DISCONNECT_ACCOUNT_INFO_BOTTOM_SHEET = 527;

  // Shared screen - confirmation bottom sheet when disconnecting and deleting account
  DISCONNECT_ACCOUNT_CONFIRM_BOTTOM_SHEET = 528;

  // Screen for "Proceed" on the DISCONNECT_ACCOUNT_INFO_BOTTOM_SHEET screen
  API_GET_RELATED_ACCOUNTS_FOR_DISCONNECT = 529;

  // Deeplink for card (credit card/debit card) offers catalog screen
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17992%3A77153&t=aNM0EGyiu7H6M1LE-1
  CARD_OFFERS_CATALOG_SCREEN = 530;

  // Deeplink for debit/credit card offer details page.
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17992%3A78662&t=Pes6Mb6Sq2bGYYTE-1
  CARD_OFFER_DETAILS_SCREEN = 531;

  // Deeplink for salaryprogram health insurance policy information screen
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=10078-79364&t=E8ETd9h3n6NYp5qa-0
  SALARY_PROGRAM_HEALTH_INSURANCE_POLICY_INFO_SCREEN = 532;

  // Deeplink for modify recurring payment screen
  MODIFY_RECURRING_PAYMENT_SCREEN = 533;

  // Call GetRelatedAccountForDelete after DISCONNECT_ACCOUNT_INFO_BOTTOM_SHEET screen for Delete
  API_GET_RELATED_ACCOUNTS_FOR_DELETE = 534;

  // Call Delete Account API after confirmation
  API_DELETE_ACCOUNT = 535;

  // call update reminder API and change state of reminder
  // will require RemindersUpdateStateOption as params
  REMINDERS_UPDATE_STATE = 536;

  // screen to display the brands on which we would be giving 1x, 2x or 5x rewards
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=21567-82462&t=cdqMAxIaK9e7i2bE-0
  CREDIT_CARD_INTRO_SCREEN_BRANDS = 537;

  // screen where we show an estimation of rewards that the user gets for a particular amount in transactions
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=21496-82605&t=cdqMAxIaK9e7i2bE-0
  CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN = 538;

  // screen to show the welcome vouchers that we display to the user at the intro screen
  CREDIT_CARD_WELCOME_VOUCHERS_SCREEN = 539;

  // Usstocks introduction screen(will be used in PNs and Nudges)
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=11903-30010&t=nUwt5lhRO9MllKsL-0
  USSTOCKS_INTRODUCTION_SCREEN = 540;

  // Screen for confirmation of offer selection of the user
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=19782-82423&t=QRrpOxcDgr7qPFHW-0
  CREDIT_CARD_SUCCESS_CONFIRMATION_SCREEN = 541;

  // screen to show the deposit statement generation bottom sheet
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?node-id=9696-37065&t=CIqEjwNKxLMZqNGM-4
  DEPOSIT_STATEMENT_GENERATION_SCREEN = 542;

  // screen to show credit card bottom view error screen
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582-176745&t=HciXmKTqx3Wy4JmM-0
  CREDIT_CARD_FAILURE_BOTTOM_VIEW_SCREEN = 543;

  // screen to power the landing screen of credit cards.
  // this will lead the user to the appropriate screen as per the onboarding stage
  // for successfully onboarded user, it wil lead to cc dashboard
  CREDIT_CARD_LANDING_SCREEN = 544;

  // v2 offer details screen for pre approved loan to accomodate experimentation framework
  PRE_APPROVED_LOAN_OFFER_DETAILS_V2_SCREEN = 545;

  // screen to download digital cancelled cheque for user.
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=9976%3A77663&t=5fuulRvfcRu5dALk-1
  DOWNLOAD_DIGITAL_CANCELLED_CHEQUE = 546;

  // screen to show all p2p investment activities
  // client should call the p2p GetAllActivities RPC on encountering this deeplink
  // and use the response to construct the screen
  // screen options - api.typesv2.deeplink_screen_option.p2pinvestment.AllActivitiesScreenOptions
  // figma - https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12134&t=oGI1EneJDMIaa0YO-0
  P2P_INVESTMENT_ALL_ACTIVITY_SCREEN = 547;

  // GetSalaryProgramLandingScreenRedirectionInfo rpc will be called corresponding to this deeplink
  // and this rpc will return redirection info to redirect user to an appropriate salary program screen based on their registration status
  SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION = 548;

  // screen to show when user click on cc reminder screen and don't have credit card
  FALLBACK_CREDIT_CARD_REMINDER_SCREEN = 549;

  // screen to show the vkyc call failure error reason
  VKYC_ERROR_SCREEN = 550;

  // API to call by client when initiating the renewal flow. This can be called from multiple entrypoints for renewal
  API_GET_LANDING_PAGE_FOR_RENEWAL = 551;

  // SDK entrypoint screen when initiating the consent renewal
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9168%3A104038&t=1nHxu44cjRq40YI6-1
  CONSENT_RENEWAL_SDK_LOGIN_SCREEN = 552;

  // intro screen for upi pin set
  // now users can set upi pin using both debit card
  // and aadhaar number
  // Fi non tpap should still be taken to old screen (UPI_PIN_SETUP)
  UPI_PIN_SETUP_V2 = 553;

  // Landing page for an analyser
  ANALYSER_LANDING_PAGE = 554;

  // screen which allows user to convert fi coins to club vistara points
  // this flow will be triggered when from the vistara offer on offers catalog
  VISTARA_AIR_MILES_REDEMPTION_SCREEN = 555;

  // screen which shows instructions in a popup
  // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%F0%9F%8F%81-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=25703%3A60367&t=fJUwBetxbsFQWXLR-1
  FALLBACK_POPUP = 556;

  // Deeplink for New screen showing vpa created successfully for a Tpap account
  // asking to set pin if user hasn't, triggered from in-app notification
  SUCCESSFUL_VPA_CREATION_SET_PIN_V2 = 557;

  // s1 screen to let user choose amount
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36470&t=L5WOwrBPFf7FxhUr-4
  EARLY_SALARY_OFFER_SCREEN = 558;

  // s2 screen to show user all the application details before applying for early salary
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36660&t=L5WOwrBPFf7FxhUr-4
  EARLY_SALARY_APPLICATION_DETAILS_SCREEN = 559;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36388&t=L5WOwrBPFf7FxhUr-4
  EARLY_SALARY_NON_QUALIFIED_USER_SCREEN = 560;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36018&t=L5WOwrBPFf7FxhUr-4
  EARLY_SALARY_DASHBOARD_SCREEN = 561;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35605&t=L5WOwrBPFf7FxhUr-4
  EARLY_SALARY_CUSTOM_OFFER_SELECTION_SCREEN = 562;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18494%3A35155&t=L5WOwrBPFf7FxhUr-4
  EARLY_SALARY_LOAN_DETAILS_SCREEN = 563;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36300&t=XMEEp74SAjesOpks-4
  EARLY_SALARY_LANDING_SCREEN = 564;

  // v1 referral screen during onboarding for entering finite-code
  ENTER_REFERRAL_FINITE_CODE_V1_SCREEN = 565;

  // Screen for credit card lounge access
  CREDIT_CARD_LOUNGE_ACCESS_SCREEN = 566;

  // Bottom sheet screen for mutual fund holdings import sync progress
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=7392-19424&t=I823W2wvcIh77dD5-4
  MF_HOLDINGS_IMPORT_SYNC_PROGRESS_SCREEN = 567;

  // Screen where user is redirected post payments
  // https://www.figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=7516-16427&t=O9jpie6jkzMXPB8I-0
  POST_PAYMENT_SCREEN = 568;

  // Screen for early salary polling
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35860&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_STATUS_POLL_SCREEN = 569;

  // Screen for early salary auto pay confirmation screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35767&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_AUTO_PAY_CONFIRMATION_SCREEN = 570;

  // Screen for early salary address confirmation screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36527&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_ADDRESS_CONFIRMATION_SCREEN = 571;

  // Screen for early salary loan agreement confirmation screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35747&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_LOAN_AGREEMENT_CONFIRMATION_SCREEN = 572;

  // Screen for early salary document viewer screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35797&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_DOCUMENT_VIEW_SCREEN = 573;

  // Screen for early salary OTP screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A35804&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_OTP_CONFIRMATION_SCREEN = 574;

  // Screen for early salary status
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18494%3A35474&t=5Gp8q8EToWlKp84F-4
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A36072&t=5Gp8q8EToWlKp84F-4
  EARLY_SALARY_STATUS_SCREEN = 575;

  //Screen for international payments request status
  UPI_INTERNATIONAL_REQUEST_STATUS_SCREEN = 576;

  // ACTIVATE_ACCOUNTS_FOR_INTERNATIONAL_UPI_PAYMENTS_SCREEN -
  // represents the list of accounts eligible for
  // international upi payments activation
  ACTIVATE_ACCOUNTS_FOR_INTERNATIONAL_UPI_PAYMENTS_SCREEN = 577;

  // ENTER_AMOUNT_FOR_INTERNATIONAL_UPI_PAYMENTS_SCREEN -
  // represents the amount screen for international upi payments
  ENTER_AMOUNT_FOR_INTERNATIONAL_UPI_PAYMENTS_SCREEN = 588;

  // Transition screen for bonus post screener for affluent user
  // https://www.figma.com/proto/HDrcQuiYNLE7nTIN6ltAmd/%F0%9F%8F%81-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?page-id=20001%3A66397&type=design&node-id=26618-66227&viewport=1203%2C-2745%2C0.34&scaling=min-zoom&starting-point-node-id=20001%3A76160
  AFFLUENT_USER_BONUS_TRANSITION_SCREEN = 589;

  // Screen for displaying credit card details and benefits on CC dashboard
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?type=design&node-id=23643-102517&t=uIbl4BAT6C98V2Xn-4
  CREDIT_CARD_DETAILS_AND_BENEFITS_SCREEN = 590;

  // Screen for early salary eligible for offer
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=18313%3A34753&t=GGRqDiWgfqs5hPqY-4
  EARLY_SALARY_ELIGIBILITY_SCREEN = 591;

  // Screen for early salary faq
  EARLY_SALARY_FAQ_SCREEN = 592;

  // Screen for intermediate status
  EARLY_SALARY_INTERMEDIATE_STATUS_SCREEN = 593;


  // screen for displaying salaryprogram benefit info.
  // figma : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=500-43411&t=sJE2tsVffTrio1Om-0
  SALARY_PROGRAM_BENEFIT_INFO_SCREEN = 594;

  // screen for displaying details of a rewardOffer
  REWARD_OFFER_DETAILS_SCREEN = 595;

  // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=10-2000&t=j8U1Tmxce4gmo0KH-4
  FI_LITE_DROP_OFF = 597;

  PRE_APPROVED_LOAN_APPLICATION_DETAILS_V2_SCREEN = 598;

  // Screen for request choice screen in alfred
  //https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=7365-32032&t=sHAKOV4GCwrewFQJ-0
  // when client receives this deeplink then client will make call to GetRequestChoices rpc
  ALFRED_REQUEST_CHOICE = 599;

  // Screen to show all request summaries for filters passed in screen options
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=7365-32528&t=sHAKOV4GCwrewFQJ-0
  // when client receives this deeplink then client will make call to GetFilteredRequestSummaries
  ALFRED_REQUEST_SUMMARY = 600;


  // bottom sheet for deposit add money which is shown when user clicks on add money button
  // it'll enable user to add money to deposit account either via auto-save or manual
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=11198-41120&t=NA3sJ02uDKc9Q8gz-4
  DEPOSIT_ADD_MONEY_BOTTOM_SHEET = 602;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=19433-37057&t=RvYbZklviobsfvKj-4
  PL_ENTER_DETAILS_SCREEN = 603;

  // bottom sheet to show goal discovery in existing investment instrument
  // example usage: ADD GOAL CTA on deposit summary screen
  // calls frontend.goals.GetGoalDiscoveryInExistingInvestmentInstrument
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=6350-29391&t=Ge5kh97oPJWSqdoB-4
  GOAL_DISCOVERY_IN_EXISTING_INVESTMENT_INSTRUMENT = 604;

  // bottom sheet to show goal alter options
  // example usage: EDIT GOAL CTA on deposit summary screen
  // calls frontend.goals.GetGoalAlterOptions
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=6350-29551&t=Ge5kh97oPJWSqdoB-4
  GOAL_ALTER_OPTIONS = 605;

  // UPI_INTERNATIONAL_PAYMENT_ACTIVATION represents initiation of activate international
  // payment from account manager screen
  UPI_INTERNATIONAL_PAYMENT_ACTIVATION = 606;

  // UPI_INTERNATIONAL_PAYMENT_DEACTIVATION represents initiation of deactivate international
  // payment from account manager screen
  UPI_INTERNATIONAL_PAYMENT_DEACTIVATION = 607;

  // Used for showing the Maturity consent form
  P2P_INVESTMENT_MATURITY_CONSENT_SCREEN = 608;

  // Used for calling p2pinvestment's 'UpdateMaturityConsentForInvestment' api
  P2P_INVESTMENT_UPDATE_MATURITY_CONSENT = 609;

  // Deeplink for the new onboarding add funds screen
  ONBOARDING_ADD_FUNDS_V2 = 610;

  PL_REVIEW_DETAILS_SCREEN = 611;

  // Used for initiating Fi to Fi Flow, on receiving this screen, API call for get landing page for Fi to Fi is made.
  CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN = 612;

  // Using for initialising AA SDK for starting Fi to Fi flow.
  CA_INIT_SDK_FI_TO_FI_FLOW_SCREEN = 613;

  // deeplink to call provision new service request api
  ALFRED_PROVISION_SERVICE_REQUEST = 614;

  // screen to show balance of a tpap account
  BALANCE_ENQUIRY_SCREEN = 615;

  // deeplink to update default merchant payment of a CC linked tpap account
  UPDATE_DEFAULT_MERCHANT_PAYMENT_ACCOUNT_SCREEN = 616;

  // deeplink to open screen to select bank
  LIST_ACCOUNT_PROVIDER_SCREEN = 617;

  // deeplink for feature benefits screen
  // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1814-97801&t=V7roHG7WMgzivsES-4
  FEATURE_BENEFITS = 618;

  // screen used for deeplink for the post consent approval, screen for polling final consent status
  //  ref: https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=11201-113965&t=aCs7DgCBV1weuzIA-0
  CA_FI_TO_FI_POST_CONSENT_APPROVAL_POLLING_SCREEN = 619;

  // screen used for deeplink for all the Fi to Fi Flow terminal screen
  //  ref: https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-118757&t=wXROjbWLPfJOtrir-0
  CA_FI_TO_FI_FLOW_TERMINAL_SCREEN = 620;

  // screen used to show vendor's downtime to users.
  // ref : https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=21974-44764&t=Cu4GoAacVglsBT6d-4
  PL_DOWNTIME_SCREEN = 621;
  // Screen that initiates the debit card activation flow via the secure pin . This screen will be enabled for the user 5-7 days post
  // the card receipt for the user.
  // figma : https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=13492-45866&t=bivqEM47wKuleaUh-0
  DEBIT_CARD_PIN_ACTIVATION_SCREEN = 622;
  // screen to be displayed when the card activation has been done successfully
  // figma : https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=13492-45415&t=qF7TlJ7SUcpEwR15-0
  DEBIT_CARD_SUCCESSFUL_ACTIVATION_SCREEN = 623;
  // screen which will contain the qr scanner used to scan the qr code for card activation
  // figma : https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=13492-45487&t=j8RGNDLSY155PDiv-0
  DEBIT_CARD_QR_CODE_SCAN_SCREEN = 624;

  // screen where user can add or choose the preferred delivery address of credit card
  // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Workfile-%E2%80%A2-Unsecured-CC?type=design&node-id=26818-90337&t=lg6NlfecPOFuzMsa-4
  CREDIT_CARD_ADDRESS_SELECTION_V2_SCREEN = 625;

  // screen used for deeplink for cases where bank don't have signature
  // ref: https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-•-FFF-•-v1.2?type=design&node-id=9589-37949&t=kBY9hPooBiCHQ6YM-0
  SAVINGS_SIGN_PROMPT = 626;

  // indicates entry for legality signature flow
  LEGALITY_SIGN_FLOW = 627;
  // Deeplink used as a placeholder for client to know when to call client callback
  // Loans
  PL_CLIENT_CALLBACK_SCREEN = 628;
  // Early Salary
  ES_CLIENT_CALLBACK_SCREEN = 629;
  // screen for showing federal savings account info
  FEDERAL_SAVINGS_ACCOUNT_INFO = 630;
  // Screen for Connected Account screener check in progress Bottom Sheet
  SCREENER_CA_CHECK_IN_PROGRESS = 631 [deprecated = true];
  // screen for real time eligibility check flow introduction
  // ref : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Workfile-%E2%80%A2-Unsecured-CC?type=design&node-id=27648-82126&mode=design&t=YjdMdlRvNEVhqqti-4
  CREDIT_CARD_REAL_TIME_ELIGIBILITY_CHECK_INTRO_SCREEN = 632;
  // deeplink to call get profile page section
  REPORTS_AND_DOWNLOADS_SCREEN = 633;
  // screen which contains fd calculation and opening details for secured card onboardin
  // figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=1-4040&t=i0zaKGjGbT1EbzYi-0
  SECURED_CREDIT_CARD_DEPOSIT_SCREEN = 634;
  // deeplink for bottom sheet to schedule a call
  VKYC_SCHEDULE_CALL = 635;
  // Client calls the SkipOnboardingStage API
  SKIP_ONBOARDING_STAGE_API = 636;
  // Screen for CX Tickets List in Client Apps
  CX_TICKET_LIST_SCREEN = 637;
  // Screen for CX Ticket Details in Client Apps.
  CX_TICKET_DETAIL_SCREEN = 638;
  // Screen for showing all unopened rewards that a user has
  UNREDEEMED_REWARDS_SCREEN = 639;
  // Screen to handle Deposit creation. This is meant for the GetDepositCreationScreen rpc.
  CREATE_DEPOSIT = 640;
  // Screen for changing phone number if the mf investments are linked to a
  // different from what the user has onboarded with
  MF_HOLDINGS_IMPORT_CHANGE_PHONE_NUMBER = 641;
  // Screen for confirm phone number if the number provides is what
  // investments are linked to
  MF_HOLDINGS_IMPORT_CONFIRM_PHONE_NUMBER = 642;
  // referee actions screen which lists the actions performed/to-be-performed by the referee
  // such that the referrer and referee earn the stated rewards.
  // Note: To be used with the assumption that the referrer will see this screen.
  REFEREE_ACTIONS_INFO_SCREEN = 643;
  // generic bottomsheet for upi actions
  // e.g. delete upi lite
  UPI_ACTION_BOTTOMSHEET = 644;
  // Screen to show home shortcut options
  HOME_SHORTCUT_OPTIONS_SCREEN = 645;
  // screen to show otp and qr in BKYC
  BKYC_HANDSHAKE_OTP = 646;
  // screen to show detail received from get kyc status
  // client is expected to call get kyc record to fetch details
  KYC_RECORD = 647;
  // jump loading screen to decide which plan to show to the user based on what is being promoted currently
  // screenoptions: api.typesv2.deeplink_screen_option.p2pinvestment.PromotionLoadingScreenOptions
  // RPC to be called on loading this screen frontend.p2pinvestment.GetInvestPageFromPromotionalUseCase
  JUMP_PROMOTION_LOADING_SCREEN = 648;

  // This screen will be used to start the BKYC process by providing information of the process and asking for consent.
  BKYC_CONSENT = 649;

  // This deeplink will be used to show user the option to select between KYC method during onboarding.
  ONBOARDING_KYC_OPTION_SELECTION_SCREEN = 650;

  // This deeplink will be used to show user the secured card FD Details after the FD is created
  // figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=779-17822&mode=design&t=BKP2Fi5xGyw1Swqs-4
  SECURED_CREDIT_CARD_FD_DETAILS_SCREEN = 651;

  // landing deeplink for users who have not invested in jump
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=18678-11323&mode=design&t=YRDwhdw2amsS9yUn-0
  // screen options: api.typesv2.deeplink_screen_option.p2pinvestment
  P2P_ZERO_STATE_LANDING_SCREEN = 652;

  // Screen for showing order processing. The intend for this deeplink is for client to invoke CreateDeposit rpc and show
  // processing screen till the call is completed.
  DEPOSIT_ORDER_PROCESSING_SCREEN = 653;

  // US Stocks wallet landing page. Contians wallet balance and options to add/withdraw funds from wallet.
  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-35008&mode=design&t=x9f3s3BafVxMnLaY-0
  USSTOCKS_WALLET_PAGE = 654;

  // Screen to place US Stocks wallet add funds order. Users enters the amount that is to be added in the wallet.
  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=17153%3A37079&mode=dev
  USSTOCKS_WALLET_ADD_FUNDS_SCREEN = 655;

  // Screen to place US Stocks wallet withdraw funds order. Users enters the amount that is to be withdrawn from the wallet.
  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=17153%3A37003&mode=dev
  USSTOCKS_WALLET_WITHDRAW_FUNDS_SCREEN = 656;

  // UPI_LITE_ADD_MONEY screen - screen to show all the info regarding upi lite like balance etc,
  // and gives options to users to add money or delete upi lite account.
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=16126-99474&mode=design&t=99pn8oYmOzPZHrqP-4
  // screen options: api.typesv2.deeplink_screen_option.upi
  UPI_LITE_ADD_MONEY = 657;

  // Screen for setting up enach mandate for salary lite program
  // Screen for setting up mandate for salary lite program
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13509%3A102092&mode=design&t=YPkRdVYwPe3sVQkw-1
  SALARY_LITE_MANDATE_SETUP_SCREEN = 658;

  // Screen for cancelling mandate setup for salary lite program
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13350%3A98579&mode=design&t=YPkRdVYwPe3sVQkw-1
  SALARY_LITE_CANCEL_MANDATE_SCREEN = 659;

  // Screen to check status of mandate setup for salary lite program
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=12804%3A94550&mode=design&t=YPkRdVYwPe3sVQkw-1
  SALARY_LITE_MANDATE_STATUS_SCREEN = 660;

  // Salary lite intro screen
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13233-121072&mode=design&t=BLmPKnXEsuqQ1gRY-0
  SALARY_LITE_INTRO_SCREEN = 661;

  // Upgrade to full salary program screen
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=12804%3A96479&mode=design&t=YPkRdVYwPe3sVQkw-1
  UPGRADE_TO_FULL_SALARY_PROGRAM_SCREEN = 662;

  // Credit card bill generation date selection options screen
  // Figma : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=31069-94375&mode=design&t=50q8tBKERprEYawb-4
  CC_BILL_GENERATION_DATE_SELECTION_SCREEN = 663;

  // Screen to take PAN and DOB from the user. Currently used in loan_eligibility_check flow in Fi-Lite
  // Figma : https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31293&mode=design&t=Bw4XC5GoGcyr4tOg-4
  PERSONAL_LOANS_PAN_DOB_ADDITION_SCREEN = 664;

  // Screen to take Name and gender from the user. Currently used in loan_eligibility_check flow in Fi-Lite
  // Figma : https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31318&mode=design&t=OPDVQmKEaPBKLjcw-4
  PERSONAL_LOANS_NAME_AND_GENDER_ADDITION_SCREEN = 665;

  // Screen to take the user consent before fetching the credit report of the user
  // Figma : https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31729&mode=design&t=fhCwL9jA9KKrunEp-4
  PL_CREDIT_REPORT_FETCH_CONSENT_SCREEN = 666;

  // This deeplink will be used to show the screen used for the tenure selection in Secured Credit Card Onboarding flow
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=1321-19102&mode=design&t=ByqgROFNQjYr8QU9-4
  SECURED_CC_DEPOSIT_TENURE_SELECTION_BOTTOM_SHEET_SCREEN = 667;

  // This deeplink will be used to show the details of the FD being created for the Secured Credit Card Onboarding Flow
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=84-16721&mode=design&t=ByqgROFNQjYr8QU9-4
  SECURED_CC_DETAILS_BOTTOM_SHEET_SCREEN = 668;

  // Screen to take user's banking details to perform penny drop in the account. This is done to verify if the account
  // is active. The same account could be used for mandate as well.
  // Figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26919-7679&mode=design&t=naqu1Bf507B6qZu1-4
  PL_BANKING_DETAILS_SCREEN = 669;

  // Screen to create us stocks buy order
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17157-38154&mode=dev
  USSTOCKS_BUY_ORDER_PROCESSING_SCREEN = 670;

  // Screen to create us stocks sell order
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17157-38555&mode=dev
  USSTOCKS_SELL_ORDER_PROCESSING_SCREEN = 671;

  // Screen to improve retention for investment instruments
  // Deeplink for retention screens. This single deeplink can return multiple screen variants depending on the screen options.
  // Figma : https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=9ArwPmfBvv8C5crj-0
  // Screenshot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
  INVESTMENT_RETENTION_SCREEN = 672;

  // Generic full screen loading screen meant for DMF
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5426&mode=design&t=9JS1sYmmUUqfu4RB-4
  DMF_GENERIC_LOADING_SCREEN = 673;

  // Screen to confirm the phone number
  // Figma : https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5456&mode=design&t=9JS1sYmmUUqfu4RB-4
  EPF_PASSBOOK_IMPORT_CONFIRM_PHONE_NUMBER_SCREEN = 674;

  // EPF pass book import, uan list screen
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5405&mode=design&t=9JS1sYmmUUqfu4RB-4
  EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN = 675;

  // OTP screen when importing EPF Passbook
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5790&mode=design&t=BxTFr1BuKkRbEYzp-4
  EPF_PASSBOOK_IMPORT_OTP_SCREEN = 676;

  // Hub screen for networth
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=157-18277&mode=design&t=BxTFr1BuKkRbEYzp-4
  NET_WORTH_HUB_SCREEN = 677;

  // screen to poll the status of the action taken on a recurring payment
  RECURRING_PAYMENT_POLLING_SCREEN = 678;

  // screen to initate the authorisation for the creation of a recurring payment
  RECURRING_PAYMENT_INITIATE_CREATION_AUTHORISATION = 679;

  // screen to redirect to external web app with payload for making the post request
  // some of the use cases are (but not limited to):
  // 1. redirect to vendor web page with prefilled details in the payload
  EXTERNAL_REDIRECTION_WITH_PAYLOAD = 680;


  // Entry point screen to show user to check loan eligibility. This will be a part of the landing info response screens
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31638&mode=design&t=vtNSCKIj8kuYWZd7-4
  LOAN_ELIGIBILITY_LANDING_SCREEN = 681;

  // Screen for using PAN for authentication
  // currently used in closed accounts reopening flow
  // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=31895%3A59029&mode=design&t=PvVZeMkvknfGrxg1-1
  PAN_FOR_AUTH = 682;

  // Screen to manually capture information for a Deposit
  // https://drive.google.com/file/d/1KQI6qM3FMttsoIrHpPsRF49F7CqTw8I0/view?usp=drive_link
  DEPOSIT_DECLARATION = 683;

  // Screen for showing Net Worth deposits page, consists of FI FD/SD and AA FD/RD
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=317-27417&mode=design&t=HuQ03qdtpjux2Qpu-0
  NET_WORTH_DEPOSITS_SCREEN = 684;

  // deeplink for deposit accounts landing/listing screen for new screen
  // https://drive.google.com/file/d/157g2qYNqGowrULjbw5n3wVXCB5eD8zxg/view?usp=sharing
  DEPOSIT_CLOSE_ACCOUNT_V2 = 685;

  // Screen to decide the onboarding flow (which product/feature) depending on intent
  // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=20001%3A66397&mode=dev
  ONBOARDING_INTENT_SELECTION = 686;

  // Screen for deposit account(TD and RD) not discovered in net worth
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=317%3A28334&mode=dev
  NET_WORTH_DEPOSITS_CA_NO_ACC_DISCOVERED_SCREEN = 687;

  // Screen for epf dashboard
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=1-5604&mode=dev
  EPF_DASHBOARD = 688;

  // Loan Application/Eligibility Review details screen with all the application data filled by the user till now
  // figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32000&mode=design&t=vh1XIQSkhr8pc92K-4
  PL_APPLICATION_REVIEW_DETAILS_SCREEN = 689;

  // Prescribes client to call LoginWithOAuth RPC
  LOGIN_WITH_OAUTH = 690;

  // Screen for intro bottom sheet screen
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-19681&mode=dev
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-19754&mode=dev
  CC_INTRO_BOTTOM_SHEET = 691;

  // Screen for credit card intro
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-20302&mode=dev
  CC_INTRO_SCREEN = 692;

  // Polling screen to fetch next action as per the kyc status. The client calls the FE RPC `GetKYCStatus` to get the
  // next action deeplink
  KYC_STATUS_POLLING_SCREEN = 693;

  // Onboarding faq bottom sheet
  // https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?type=design&node-id=20001-165831&mode=design&t=Np1kbKnlFbarx2SY-0
  ONB_FAQ_BOTTOM_SHEET = 694;

  // New Vkyc Instruction screen
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=13410-30847&mode=design&t=bjnS1DjGeGPNA8gO-0
  VKYC_INSTRUCTIONS_V2 = 695;

  // Screen to show overlay on top of VKYC_INSTRUCTIONS_V2 along with media options
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=13410-30085&mode=design&t=rQfQ4uYx7tKXcIwi-0
  VKYC_INSTRUCTIONS_OVERLAY = 696;

  // bottom sheet in case user press back button on vkyc instructions overlay screen
  // it is used to introduce some friction for user when tries to drop off
  // we will show various reasons for which the user may drop off
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=13410-30354&mode=design&t=1vzS7ifTPOBYrNl4-0
  VKYC_FEEDBACK_BOTTOM_SHEET = 697;

  // VKYC_ACKNOWLEDGEMENT_SCREEN can be used as a acknowledgement screen for vkyc eg. schedule call
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=13411-43461&mode=design&t=cV0EE2fhJdpC9YO2-0
  VKYC_ACKNOWLEDGEMENT_SCREEN = 698;

  // Loan Application/Eligibility loading screen after submission of all the application data filled by the user
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32100&mode=dev
  PL_CHECK_ELIGIBILITY_LOADING_SCREEN = 699;
  // Loan offer available success screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31262&mode=dev
  PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN = 700;
  // Loan offer not available screen
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29325-52592&mode=dev
  PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN = 701;

  // figma: https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9387-34931&mode=design&t=TaktmlMe2ep2EwcM-0
  // screen for request new debit card screen
  DEBIT_CARD_REQUEST_NEW_CARD_SCREEN = 702;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=7697-26950&mode=design&t=ohlCCexI5CqtVe8n-0
  // screen when user is showed the loan tab but has no valid offer, hence is a non qualified user
  PL_NON_ELIGIBLE_USER_LANDING_SCREEN = 703;
  // figma: https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=7323-29088&mode=design&t=ooK3HnqLGOD2uxUI-0
  INFO_ACKNOWLEDGEMENT_V2 = 704;
  // Client calls RecordConsentForProfileUpdate
  RECORD_CONSENT_PROFILE_UPDATE_API = 705;
  // Screen used for polling
  FIREFLY_SYNC_POLL_STATUS_SCREEN = 706;

  // Figma: https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=9701-42332&mode=design&t=2EHzNmCp0NDQRrgc-4
  CC_AMPLI_FI_SCREEN = 707;

  // figma: https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9387-35124&mode=design&t=y72h0nRe8Ja80Gt2-0
  // deeplink to screen where user enters/edit shipping address for physical debit card
  DEBIT_CARD_SHIPPING_ADDRESS_SELECT_SCREEN = 708;
  // generic screen to initiate a debit card flow. It can be a renew flow, create card flow, etc.
  DEBIT_CARD_INITIATE_FLOW_SCREEN = 709;
  // generic screen to poll for status in debit card flow. It can be a check renew card status, check create card status, etc.
  DEBIT_CARD_CHECK_FLOW_STATUS_SCREEN = 710;
  // Client calls SaveDobForProfileUpdate
  SAVE_DOB_FOR_PROFILE_UPDATE = 711;
  // HelpLanding V2 - SDUI
  HELP_LANDING_SCREEN_V2 = 712;
  // Bottom sheet to show option to request chequebook or cancelled cheque
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=12734-18969&mode=design&t=qzAoLu8fxd8FIcMC-0
  REQUEST_CHOICE_BOTTOM_SHEET = 713;
  // Client calls provision new request with request type
  PROVISION_NEW_REQUEST = 714;
  // When a user is deemed ineligible for credit card onboarding,
  // This screen will be shown.
  // figma: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=2539-20531&mode=design&t=G1MP0tw5tHn8d1n0-0
  CC_INELIGIBLE_USER_SCREEN = 715;
  // Screen type to be used in case feedback cta flow from feedback engine is used
  // to trigger a cta based feedback
  FEEDBACK_ENGINE_CTA_FLOW_SCREEN = 716;
  // Feedback Engine Screen, to be utilized by feedback engine service from inapphelp
  FEEDBACK_ENGINE_SCREEN = 717;
  // debit card new card request success screen
  DEBIT_CARD_REQUEST_NEW_CARD_SUCCESS_SCREEN = 718;
  // screen to advertise fi's savings account
  // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=34462-59053&mode=design&t=XFj9NqLrmbQmddK2-0
  ONBOARDING_SAVINGS_ACCOUNT_INTRO = 719;
  // Client calls the ResetOnboardingStage RPC
  RESET_ONBOARDING_STAGE_RPC = 720;
  // Deeplink to a generic dialog box to display any dynamic information to user
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=18258-17222&mode=design&t=N9jnqcV5S6KXdJx8-0
  // https://drive.google.com/file/d/1z3KYyCug5leYRLRQsvFX8yzDC20NpHGP/view?usp=sharing
  BOTTOM_SHEET_INFO_VIEW = 721;
  // Deeplink to withdraw investments from mutual funds.
  MUTUAL_FUND_WITHDRAWAL = 722;
  // this deeplink calls a rpc with mentioned payload and redirects based on next_action sent back.
  // the rpc being called should have a next_deeplink in response.
  RPC_BASED_REDIRECTION = 723;
  // https://www.figma.com/file/x1hL90FILdP836CGYpOQwZ/Fi-lite-Onboarding?type=design&node-id=2267-87461&mode=design&t=319NTHmrko9s6Foy-4
  PL_ACQ_TO_LEND_LANDING_SCREEN = 724;
  // screen where debit card related info is reflected along with order amount, rewards details and
  // user is asked to confirm shipping address for physical card
  // figma: https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=15402-37427&mode=design&t=bvIt9IB69kFbUd8H-4
  DEBIT_CARD_ORDER_PHYSICAL_CARD_SCREEN = 725;
  // screen to show users with some information on the benefits of upgrading from standard plan
  DEBIT_CARD_UPGRADE_PLAN_SCREEN = 726;
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54172&mode=dev
  LAMF_LANDING_SCREEN = 727;
  // screen to show successful migration of vpa from @fbl to @fifederal
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=6377%3A67415&mode=design&t=cmQL3Z4nhYh5IfO0-1
  VPA_MIGRATION_SUCCESS_SCREEN = 728;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43461&mode=dev
  PL_EDIT_LOAN_BOTTOM_SHEET = 729;
  // LoansInfoScreenOptions used as a generic intro/error/popup screen
  // which might have center image, title, desc, bullet points, term infos, Cta for deeplink navigation
  // e.g.-
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=31119-26825&mode=design&t=6T29915B1N6Q7zST-4
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29358-62846&mode=design&t=6T29915B1N6Q7zST-4
  LOANS_INFO_SCREEN = 730;
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54134&mode=dev
  LOANS_JOURNEY_SCREEN = 731;
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54609&mode=design&t=JYrIaMGmibgom2Zx-4
  LOANS_VIEW_OFFER_SCREEN = 732;
  // configurable connected accounts benefits screen
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13544-11164&mode=design&t=EJpDU7R6VwOeAW6t-4
  CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2 = 733;
  // screen that will be used while searching for FAQ categories
  // figma: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=6757-63692&mode=dev
  CX_CATEGORY_SEARCH_SCREEN = 734;
  // deeplink to open vpa migration introduction screen
  VPA_MIGRATION_INTRO_SCREEN = 735;
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=14157-13734&mode=design&t=znCin5O0V3JnrmaH-0
  VKYC_REVIEW_SCREEN = 736;
  // screen to take user input in order to update some account details
  LOAN_ACCOUNT_DETAILS_UPDATE_USER_INPUT_SCREEN = 737;
  // screen to take additional details from user to start loan application
  LOAN_APPLICATION_ADDITIONAL_DETAILS_USER_INPUT_SCREEN = 738;
  // screen to show loan application details based on the loan details selected by the user on offer details page
  LOAN_APPLICATION_DETAIL_SCREEN = 739;
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=13165-13250&mode=design&t=PMCRMKxvrvTRnIC4-0
  // bottom sheet to upload epan
  UPLOAD_EPAN_BOTTOM_SHEET = 740;
  // screen to perform vkyc with the vendor
  LAMF_VENDOR_KYC_SCREEN = 741;
  // screen to perform e-sign step for loan disbursal
  // Client needs to open a web-view and poll status of the step in background.
  LAMF_ESIGN_SCREEN = 742;
  // screen opens a web-view and polls loan request status check rpc to
  // get the next action to be taken.
  // Client is expected to remain on the same screen as long as same screen and url are returned
  // screen is used to perform e-mandate/e-sign step for LAMF loan disbursal
  LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN = 743;
  // screen to show info about the mandate process. It contains a CTA
  // which starts the actual e-mandate process.
  LOANS_MANDATE_INTRO_SCREEN = 744;
  // Screen to be shown on successful loan application processing
  LOANS_APPLICATION_COMPLETION_SUCCESS_SCREEN = 745;
  // Screen to show the progress of a process.
  LOANS_PROGRESS_UPDATE_SCREEN = 746;
  // Folio level breakup of a particular MF Scheme held by user
  LOANS_MF_FOLIO_BREAKUP_SCREEN = 747;
  // List of eligible and in-eligible MF Schemes for keeping as security against a loan
  LOANS_MF_PORTFOLIO_ELIGIBILITY_DETAILS_SCREEN = 748;
  // Screen for user to select details of the loan (like principle, loan tenure, security etc.).
  // These details should be eligible as per the offer generated by the lender.
  LOANS_USER_LOAN_DETAILS_SELECTION_SCREEN = 749;
  // Screen to start asset details fetch process
  LOANS_INITIATE_ASSET_DETAILS_FETCH_SCREEN = 750;
  // bottom sheet to show details on subject in a list format.
  // Each item in list can have a visual element, main text and description text
  // figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=315-38685&mode=design&t=gLZPcHbizlMUM5cc-0
  LOANS_SUBJECT_INFO_WITH_DETAILS_LIST_SCREEN = 751;
  // screen to show CX category details
  // figma: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=6499-48969&mode=dev
  CX_CATEGORY_DETAILS_SCREEN = 752;
  // Loans dashboard screen is a generic screen which will be used to give details of all the loans of the user.
  // This is being used for fiftyfin lamf loan dashboard at the time.
  LOANS_DASHBOARD_SCREEN = 753;
  // screen to power the sync polling screen for credit cards.
  // Figma : https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=11292-14439&mode=design&t=vxGt3EcjkPbARnEv-0
  CREDIT_CARD_SYNC_POLL_STATUS_SCREEN = 754;
  // Screen to show terminal failure for a process along with some description and CTA
  // Screen with additional description: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=356-50072&mode=design&t=WDfFPySvpBiGCTT7-0
  // Screen with CTA: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=284-14097&mode=design&t=WDfFPySvpBiGCTT7-0
  LOANS_FAILURE_SCREEN = 755;
  // Loan overview screen is a generic screen which will be used to give overview of a given loan.
  // This is being used for fiftyfin lamf loan details at the time.
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16463&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_OVERVIEW_SCREEN = 756;
  // Loan payment details screen is a generic screen which will be used to give details of payments done for a loan.
  // This is being used for fiftyfin lamf loan payment details at the time.
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=1304-13603&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_PAYMENT_DETAILS_SCREEN = 757;
  // Loan details screen is a generic screen which will be used to give all the details about the loan
  // This is being used for fiftyfin lamf loan payment details at the time.
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15163&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_DETAILS_SCREEN = 758;
  // this deeplink informs client to call InitiateSiSetup FE RPC
  INITIATE_LOANS_SI_SETUP = 759;
  // mutual fund import consent screen v2 https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3759&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_CONSENT_SCREEN_V2 = 760;
  // loading screen that triggers otp generation for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3927&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_GENERATE_OTP_SCREEN = 761;
  // screen to input mf holdings import otp https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3810&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN = 762;
  // screen to show import progress post otp submission https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-4143&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_OTP_SUCCESS_LOADING_SCREEN = 763;
  // screen for user to manually enter phone number for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3945&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_PHONE_NUMBER_SUBMISSION_SCREEN = 764;
  // screen for user to manually enter email for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3992&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_EMAIL_SUBMISSION_SCREEN = 765;
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=15143-18604&mode=design&t=woYX9XiNBN1oObov-0
  // screen to upload epan
  UPLOAD_EPAN_SCREEN = 766;
  // https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?type=design&node-id=21402-43069&mode=dev
  // screen to sent sms for re-kyc flow
  SEND_RE_KYC_SMS = 767;
  // figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=3712-51859&mode=dev
  MANUAL_ASSET_FORM_SCREEN = 768;
  // figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=3712-51904&mode=dev
  MANUAL_ASSET_DASHBOARD_SCREEN = 769;

  // This screen will be used to prompt user to connect a bank account.
  // E.g. In Fi Lite Pay, if no account is connected then user will be prompted
  // to connect a bank account which then could be used to Pay.
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=19320-18968&mode=design&t=Sval55XxIC9inTWk-0
  CONNECT_BANK_ACCOUNTS_INTRO_SCREEN = 770;

  // user will see an activate account cta
  // for all the inactive accounts in the
  // account selector. This deeplink will
  // be used to trigger activation flow
  // for the upi account.
  ACTIVATE_UPI_ACCOUNT_SCREEN = 771;

  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=15143-18604&mode=design&t=woYX9XiNBN1oObov-0
  // screen to upload FILE
  UPLOAD_FILE = 772;

  // client will call InitiateAuthForBeneficiaryActivation in the given screen
  INITIATE_AUTH_FOR_BENEFICIARY_ACTIVATION = 773;
  // this screen is usefult to fetch the auth status for the activation of the beneficiary
  // client will call GetBeneficiaryActivationPostAuthAction in the given screen
  BENEFICIARY_ACTIVATION_POST_AUTH_STATUS = 774;
  // screen to display the current bill gen and due dates
  CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN = 775;
  // Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=36659-29100&mode=dev
  CC_LOUNGE_ACCESS_V2_SCREEN = 776;
  // screen to display the lounge passes collected so far. This will also display the currently active
  // lounge and also give an option to claim a new lounge pass in case the current quarter lounge access
  // conditions are met
  // Figma : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27502-82619&mode=dev
  CC_COLLECTED_LOUNGE_PASSES_SCREEN = 777;
  // screen to check loan eligibility for a user. Client needs to call CheckEligibility rpc on navigating to this screen.
  LOANS_CHECK_ELIGIBILITY_SCREEN = 778;
  // Screen to take selfie image for lending flows
  LOANS_SELFIE_SCREEN = 779;
  // Screen to collect form data from user
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=35395-22733&mode=design&t=Db39zXl0zYjKb3lG-4
  LOANS_FORM_DETAILS_SCREEN = 780;
  // Screen to provide user options to verify their income
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40718&mode=design&t=fEGbs85eYqgpYdX1-0
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9996-49595&mode=design&t=fEGbs85eYqgpYdX1-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.LoansIncomeVerificationIntroScreenOptions
  LOANS_INCOME_VERIFICATION_INTRO_SCREEN = 781;
  // MF Holdings import initiate screen V2
  MF_HOLDINGS_IMPORT_INITIATE_SCREEN_V2 = 782;

  CC_WEB_ELIGIBILITY_USER_DETAILS_SCREEN = 783;

  CC_WEB_ELIGIBILITY_CHECK_STATUS_SCREEN = 784;
  // Screen to show user the result of income verification process
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40545&mode=design&t=fEGbs85eYqgpYdX1-0
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10109-41931&mode=design&t=fEGbs85eYqgpYdX1-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.LoansIncomeVerificationResultScreenOptions
  LOANS_INCOME_VERIFICATION_RESULT_SCREEN = 785;

  // screen to allow user to edit their pan and dob in wealth onboarding flow
  // figma: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?type=design&node-id=11054-3372&mode=design&t=U3KCdmlbslxDGtO8-4
  WEALTH_ONBOARDING_PAN_DOB_SCREEN = 786;
  // Screen where user is asked to input data for creating us stocks profile
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=20995%3A3910&mode=dev
  USSOCKS_ONBOARDING_CREATE_PROFILE_SCREEN = 787;
  // Risk Level Selection Screen where user select risk level i.e high, low, medium
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A26231&mode=dev
  USSTOCKS_ONBOARDING_COLLECT_RISK_LEVEL_SCREEN = 788;
  // Collect Risk Declaration where user declare if willing to take risk
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=20114%3A21909&mode=dev
  USSTOCKS_ONBOARDING_RISK_DISCLOSURE_SCREEN = 789;
  // US Stocks onboarding polling screen where user have to wait for some time for account creation
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=19742%3A14099&mode=dev
  USSTOCKS_ONBOARDING_POLLING_SCREEN_V2 = 790;
  // US Stocks onboarding success screen when user is ready to invest
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21015%3A4575&mode=dev
  USSTOCKS_ONBOARDING_SUCCESS_SCREEN_V2 = 791;
  // US Stocks onboarding risky profile terminal screen which is shown when user profile is risky for us stocks investment
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A26702&mode=dev
  USSTOCKS_ONBOARDING_RISKY_PROFILE_TERMINAL_SCREEN = 792;
  // V2 screen deeplink for mandate intro/initate step in LOS journey
  // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10445-46583&mode=design&t=GRyn8WVk9G8XIEld-0
  LOANS_MANDATE_INITIATE_SCREEN_V2 = 793;
  // Screen to provide user list of alternate accounts and an option to add information for new account
  // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50117&mode=design&t=GRyn8WVk9G8XIEld-0
  LOANS_ALTERNATE_ACCOUNTS_SCREEN = 794;
  // Screen where user sets up mandate for their loan
  // this screen can be a SDK or a webview triggered on the client side
  LOANS_MANDATE_SETUP_SCREEN = 795;
  // screen to indicate to start the InitiatePayment flow to allow TPAP payment sources
  CREDIT_CARD_TPAP_PAYMENT_INIT_SCREEN = 796;

  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4743-29973&mode=design&t=61MUz6ktBYAlcm8Z-4
  CC_ALL_ELIGIBLE_CARDS_SCREEN = 797;
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4751-30530&mode=design&t=BwXyZn6Xce97eOjp-4
  CC_NETWORK_SELECTION_SCREEN = 798;
  // Screen for Savings account closure - benefits page
  // page to show the benefits user is about to miss by closing the account
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=316-19762&mode=design&t=d6fSyshwKwwbCWJ8-4
  SA_CLOSURE_BENEFITS_SCREEN = 799;
  // Screen to get feedback from user on why they are closing the savings account
  // screen options: api.typesv2.deeplink_screen_option.sa_closure.SaClosureUserFeedbackScreenOptions
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=233-8188&mode=design&t=d6fSyshwKwwbCWJ8-4
  SA_CLOSURE_USER_FEEDBACK_SCREEN = 800;
  // Screen to list the criteria user has to complete before submitting the closure request
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=313-14779&mode=design&t=YkbGp6iMfUmYLSLx-4
  SA_CLOSURE_CRITERIA_SCREEN = 801;
  // generic screen to display any info to user
  // screen options: api.typesv2.deeplink_screen_option.info_view.FullScreenInfoViewScreenOptions
  // eg: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=408-22399&mode=design&t=YkbGp6iMfUmYLSLx-4
  FULL_SCREEN_INFO_VIEW_SCREEN = 802;
  // screen to get pan and dob from user for any purpose (eg. user validation on PAN and DOB)
  // screen_options: api.typesv2.deeplink_screen_option.sa_closure.PanDobInputScreenOptions
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=218-3163&mode=design&t=dL7LJ7PujX6uecF3-4
  PAN_DOB_INPUT_SCREEN = 803;
  // screen to get user consent to submit closure via a swipe action
  // screen_options: api.typesv2.deeplink_screen_option.sa_closure.SaClosureSubmitRequestSwipeActionScreenOptions
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=576-20238&mode=design&t=dL7LJ7PujX6uecF3-4
  SA_CLOSURE_SUBMIT_REQUEST_SWIPE_ACTION_SCREEN = 804;
  // api for manual cancellation of account closure request
  API_SA_CLOSURE_CANCEL_REQUEST = 805;
  // api to load landing page for savings account closure flows
  // orchestrator api will be called which decides on the screen to land user
  API_SA_CLOSURE_LANDING_PAGE = 806;
  // screen shown after closure request is submitted by the user
  // Shows the number of days remaining for the user before cancelling the closure request
  // screen_options: api.typesv2.deeplink_screen_option.sa_closure.SaClosureDaysLeftToCancelRequestScreenOptions
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=316-25022&mode=design&t=KY0eWA9R7bg7Gaw9-4
  // deprecated: use SA_CLOSURE_REQUEST_SUBMITTED_SCREEN with screen option SaClosureCancelAfterSubmissionScreenOptions
  SA_CLOSURE_CANCEL_AFTER_SUBMISSION_SCREEN = 807 [deprecated = true];
  // screen to show after user has submitted the closure request
  // screen_options: api.typesv2.deeplink_screen_option.sa_closure.SaClosureCancelAfterSubmissionScreenOptions
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=576-20284&mode=design&t=1RkGyvWdOaAeOkbg-4
  SA_CLOSURE_REQUEST_SUBMITTED_SCREEN = 809;
  // Intermediate screen to be shown to the user for landing them on the PWA flow.
  // On receiving a deeplink with his screen name, the client needs to call the GetPWARedirectionDeeplink Loans FE rpc with the request params (loan_request_id/loan_account_id) to get appropriate PWA deeplink for redirecting to the PWA flow.
  // screen_options: api.typesv2.deeplink_screen_option.preapprovedloans.LoansPWALandingScreenOptions
  LOANS_PWA_LANDING_SCREEN = 810;
  // screen to redirect the user to a PWA flow, the screen options contain a web url which the client should open in a web view for redirecting the user to the PWA flow.
  // screen_options: api.typesv2.deeplink_screen_option.pkg.PWARedirectionScreenOptions
  // sample pwa screen figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10651-39414&mode=design&t=HaJNCIwtunJUHdcu-0
  PWA_REDIRECTION_SCREEN = 811;
  // screen to display bottom sheet for loan's usecases.
  // screen_options: api.typesv2.deeplink_screen_option.pkg.LoansBottomSheetScreenOptions
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-40109&mode=design&t=HaJNCIwtunJUHdcu-0
  LOANS_BOTTOM_SHEET_SCREEN = 812;
  // ApplyForLoan() Rpc will be called whenever client receives this deeplink
  // screen_options: api.typesv2.deeplink_screen_option.pkg.ApplyForLoanScreenOptions
  APPLY_FOR_LOAN_SCREEN = 813;
  //  https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=408-22399&mode=dev
  // screen_options: api.typesv2.deeplink_screen_option.sa_closure.SaClosureResolveIssuesScreen
  SA_CLOSURE_RESOLVE_ISSUE_SCREEN = 814;
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=316-17627&mode=dev
  // screen options: api.typesv2.deeplink_screen_option.info_view.FullScreenInfoViewScreenOptions
  SA_CLOSURE_ACCOUNT_FREEZE_SCREEN = 815;
  // screen to capture image using camera and upload
  // figma: https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=15653-53046&mode=dev
  CAPTURE_IMAGE = 816;
  //  https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=408-22409&mode=dev
  // screen options: api.typesv2.deeplink_screen_option.info_view.FullScreenInfoViewScreenOptions
  SA_CLOSURE_FEEDBACK_TICKET_SUBMITTED_SCREEN = 817;
  // calls SavingsAccountClosure.SubmitClosureRequest rpc
  API_SA_CLOSURE_SUBMIT_FEEDBACK = 818;
  // calls SavingsAccountClosure.SubmitClosureRequest rpc with create_support_ticket flag
  API_SA_CLOSURE_SUBMIT_FEEDBACK_SUPPORT_TICKET = 819;
  // screen to show tiering success screen in onboarding add funds flow
  // figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=271-4523&mode=design&t=6QdRNpZBHIWJfxn7-0
  // screen options: api.typesv2.deeplink_screen_option.pay.add_funds.tiering.OnboardingAddFundsTieringSuccessScreenOptions
  ONBOARDING_ADD_FUNDS_TIERING_SUCCESS_SCREEN = 820;
  // deeplink for AskFi based search screen catering for Pay use-cases
  PAY_ASK_FI_SEARCH_SCREEN = 821;
  // api to submit sa closure request
  API_SA_CLOSURE_SUBMIT_REQUEST = 822;
  // submit deeplink for frozen accounts
  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4868&mode=dev
  API_SA_CLOSURE_SUBMIT_REQUEST_FOR_ACCOUNTS_WITH_FREEZE = 823;
  //Deposit Renewal action
  DEPOSIT_RENEWAL_CONFIRMATION_SCREEN = 824;
  // shows a loader screen for the given time
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=11980-39468&mode=design&t=aY7iBNs7wWpacNRY-4
  LOANS_TIMED_LOADER_SCREEN = 825;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9532-43792&mode=dev
  LOANS_LANDING_INFO_V2_SCREEN = 826;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9532-43735&mode=dev
  LOANS_AMOUNT_SELECTION_SCREEN = 827;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9747-163118&mode=dev
  LOANS_DETAILS_SELECTION_SCREEN_V2 = 828;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10831-58325&mode=dev
  LOANS_APPLICATION_DETAILS_SCREEN_V2 = 829;
  // bottom sheet which allows a user to convert fi coins against a reward, eg Club Vistara Points, ITC Club Points, Bill eraser
  // this may or may not be followed by a user details input bottom sheet to take additional user details for redemption process
  // https://www.figma.com/file/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=10339%3A32784&mode=design&t=VwKGtLh3Ttwohbj4-1
  CONVERT_FI_COINS_OFFER_REDEMPTION_BOTTOM_SHEET = 830;
  // Screen to be shown during mutual funds verification.
  LAMF_LOAN_DETAILS_VERIFICATION_SCREEN = 831;
  // https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?type=design&node-id=1-388&mode=design&t=mSu1CD9hZMpFDV40-4
  INDIAN_STOCKS_DASHBOARD_SCREEN = 832;
  // https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?type=design&node-id=33-3104&mode=design&t=mSu1CD9hZMpFDV40-4
  INDIAN_STOCKS_INSTRUMENT_DETAILS_SCREEN = 833;
  // https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?type=design&node-id=33-210&mode=design&t=mSu1CD9hZMpFDV40-4
  INDIAN_STOCKS_ORDER_RECEIPT_SCREEN = 834;
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6724-36821&mode=design&t=4Ty1R1EyXldxbJ7q-0
  LAMF_FUND_VERIFICATION_BOTTOM_SHEET = 835;
  // Cards tabscreen for credit card and debit card
  CARD_TABS_SCREEN = 836;
  // https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?type=design&node-id=21402-43069&mode=dev
  // screen to for re-kyc flow
  RE_KYC = 837;
  // Client calls the PeriodicKYCCallback API
  PERIODIC_KYC_CALL_BACK_API = 838;
  // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=13015-57250&mode=design&t=UQTeuem2EFxku9TQ-0
  CC_CREDIT_REPORT_ADDRESS_SCREEN = 839;
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-AmpliFi-Credit-Card-%E2%80%A2-FFF?type=design&node-id=6226-23209&mode=design&t=qRSwyCrbsNCWRuU4-0
  // screen to collect the consent from the user on the intro credit card screen
  CC_CONSENT_BOTTOM_SHEET = 840;
  // payment options full-screen bottom-sheet.
  // full-screen here refers to the possibility of this bottom-sheet taking up the whole screen.
  // Reason to call it bottom-sheet? the screen might not have enough elements to take the whole screen.
  PAYMENT_OPTIONS_FULL_SCREEN_BOTTOM_SHEET = 841;
  // Screen in lending flow to fetch debit card details required to complete mandate step
  // figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=39156-13848&mode=design&t=W03zfTMXwYDWoqvq-0#672660734
  LOANS_DEBIT_CARD_DETAILS_SCREEN = 842;
  // figma - https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=17329-43208&mode=design&t=7uDHGwWcRE6QqejS-4
  // screen to show user with options for card replacement eg - 1.Virtual card, 2.Physical card
  DC_CARD_RENEWAL_TYPE_SELECTION_SCREEN = 843;
  // screen to show user web content from url and support to fetch & show card details.
  // figma - https://www.figma.com/file/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=10010%3A14735&mode=dev
  WEB_PAGE_WITH_CARD_DETAILS_SCREEN = 844;
  // Digio SDK screen surfaces in PL LL mandate flow
  LOANS_MANDATE_DIGIO_SDK_SCREEN = 845;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10481-46100&mode=design&t=lQDePsjB2iwNFvf1-0
  CIBIL_OTP_VERIFICATION = 846;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=13174-69036&mode=design&t=8zvr9VKpQ0KUAUys-0
  ENTER_MOBILE_NUMBER = 847;
  // Screen to poll next action for a workflow. User is shown some polling text/visual.
  // This screen can be considered as v2 for PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN. It supports all functionality present in PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN screen.
  LOANS_STATUS_POLL_SCREEN = 848;
  // AutoPay Hub screen to display high-level overview of all auto-payments
  // Figma: https://www.figma.com/file/1J5E3hHsngZvuYLts9xuD6/AutoPay-%2F-Workfile?type=design&node-id=6129-34383&mode=design&t=B9phU0aw1qJSoykA-0
  AUTOPAY_HUB = 849;
  // New deposit screen for secured card onboarding
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4021-9874&mode=design&t=AtwqdcJuU4BRleWh-0
  SECURED_CREDIT_CARD_DEPOSIT_SCREEN_V2 = 850;
  // Video Player Screen for playing videos from a amazon s3 url
  FULL_SCREEN_VIDEO_PLAYER = 851;
  // https://www.figma.com/file/g2x9BTVs01uHsAZ6D8g6ss/%E2%9A%A1%EF%B8%8F-Video-KYC---FFF---26-Nov-2020?type=design&node-id=15275-92179&mode=design&t=SwyDcgfTprn7G6Tm-0
  // corresponding screen options: api.typesv2.deeplink_screen_option.form.UserDetailsFormScreenOptions at api/types/deeplink_screen_option/form/generic_form.proto
  USER_DETAILS_FORM = 852;
  // Screen to confirm UAN
  // Figma : https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=1-5700&mode=design&t=NuTOgKDnX8ksWHdB-4
  EPF_PASSBOOK_IMPORT_CONFIRM_UAN_SCREEN = 853;

  // Screen to create custom upi number for the
  // vpa of an account
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=9631-81501&mode=design&t=axsdjqXhz5qnqnJs-0
  CREATE_CUSTOM_UPI_NUMBER_SCREEN = 854;

  // Screen to link connect accounts of users via TPAP
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=22006-21657&mode=design&t=z5tpIyRahH44vHr5-0#*********
  LINK_CONNECTED_ACCOUNTS_VIA_TPAP = 855;

  // Screen to add the details for account for Penny Drop
  // figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-52008&mode=design&t=Kjl16q813TlV7owJ-4
  PENNY_DROP_ACCOUNT_DETAILS_SCREEN = 856;

  // Screen to poll the verification of the Penny Drop and reflect the same on UI
  // Corresponding screen options : CardsPollingScreenOptions
  // figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-52288&mode=design&t=Kjl16q813TlV7owJ-4
  PENNY_DROP_ACCOUNT_DETAILS_POLL_SCREEN = 857;

  // Screen to track the card status in NTB Secured Onboarding flow
  // Corresponding screen options : CardsPollingScreenOptions
  // figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4888-75847&mode=design&t=Kjl16q813TlV7owJ-4
  CARD_STATUS_POLL_SCREEN = 858;

  // Screen to show in fi lite when the user is not eligible for one type of card, but is eligible for other screens
  // figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4810-54748&mode=design&t=s16D6IUkCTgMlDIc-4
  CC_USER_INELIGIBLE_TRANSITION_SCREEN = 859;

  // Screen to render the ineligible reasons in a bottom sheet
  // Corresponding screen options : CcIneligibleUserScreenOptions
  // figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4841-19001&mode=design&t=s16D6IUkCTgMlDIc-4
  CC_INELIGIBLE_BOTTOM_SHEET = 860;

  // Screen to view all rewards earned by user
  // figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3318-45802&mode=design&t=XDQGTVKdJVvTqjvl-4
  TIERING_EARNED_BENEFIT_SCREEN = 861;

  // Screen to link phone email in LAMF
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5674-30676&mode=design&t=9fvkFSsYXGLMsk2O-0
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5679-31208&mode=design&t=9fvkFSsYXGLMsk2O-0
  LAMF_LINK_MF_SCREEN = 862;

  // Bottom sheet to skip linking mutual funds in LAMF
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5722-42359&mode=design&t=9fvkFSsYXGLMsk2O-0
  LAMF_SKIP_LINKING_MF_SCREEN = 863;

  // deeplink to call RecordUserAction RPC with the params passed in request
  LOANS_RECORD_USER_ACTION_SCREEN = 864;

  // deeplink to call GetVkycDeeplink RPC with the params passed in request
  LOANS_VKYC_REDIRECTION_SCREEN = 865;

  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=16241-46903&mode=dev
  LOANS_CALCULATION_BOTTOM_SHEET = 866;
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=2083-54863&mode=design&t=GgW16c26T4XzKVKT-4
  // deeplink to show full screen version of Payment Options bottom sheet
  PAYMENT_OPTIONS_FULL_SCREEN = 867;

  // Screen for calling 'GetNextNetWorthRefreshAction' api with a loading screen
  NET_WORTH_REFRESH_GET_NEXT_ACTION = 868;
  // Screen for showing bottom sheet when multiple instruments needs to be refreshed
  NET_WORTH_REFRESH_INIT_BOTTOM_SHEET = 869;
  // Screen for showing form to update all manual assets
  NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH = 870;
  // Screen for calling 'UpdateManualAssets' api to update all manual assets with a loading screen
  NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS = 871;
  // Screen for showing success status after all assets are refreshed
  NET_WORTH_REFRESH_SUCCESS_SCREEN = 872;

  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=40754-7114&mode=design&t=8IAddUkbmT8aV2k8-4
  // Deeplink to show to get user consent for credit limit and TnC during CC onboarding.
  CC_CARD_TNC_CONSENT_SCREEN = 873;

  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=41507-40335&mode=design&t=8IAddUkbmT8aV2k8-4
  // Deeplink to show when there is a change in credit limit during CC onboarding.
  CC_CREDIT_LIMIT_UPDATE_SCREEN = 874;

  // https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fwealthonboarding?type=design&node-id=11560-18291&mode=design&t=3ZTen7qL34SQzIIJ-4
  // Deeplink to show the survey to create an investment risk profile
  WEALTH_ONBOARDING_INVESTMENT_RISK_SURVEY_SCREEN = 875;

  // https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fwealthonboarding?type=design&node-id=11572-4200&mode=design&t=3ZTen7qL34SQzIIJ-4
  // Deeplink to show user investment profile for confirmation and e-signing investment advisory agreement if not already done
  WEALTH_ONBOARDING_INVESTMENT_RISK_PROFILE_SCREEN = 876;

  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-7332&mode=design&t=C8MVAZ1k7PtG8bnN-4
  // screen option - types/deeplink_screen_options/popup/screen_options.proto -> CelebrationPopupScreenOptions
  CELEBRATION_POPUP = 877;

  // Deeplink to show earn benefits history
  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6909&mode=design&t=adMrAaVS1582ag9m-4
  TIERING_EARNED_BENEFIT_HISTORY_SCREEN = 878;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41416-154329&mode=design&t=zpA35OflGRRe5Hr0-4
  LOANS_DOCUMENTS_SCREEN = 879;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52155&mode=design&t=fTYpRhjsruhHypBT-4
  LOANS_PREPAY_V2_SCREEN = 880;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52183&mode=design&t=fTYpRhjsruhHypBT-4
  LOANS_REPAYMENT_METHODS_SCREEN = 881;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52243&mode=design&t=vBzUvqfnnSiSHxXC-4
  LOANS_PAY_VIA_CX_SCREEN = 882;

  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=20262-17277&mode=design&t=PJsQK5cCDMKwaS6c-4
  // Deeplink for calling 'GetInvestmentSipsModificationScreen'
  GET_INVESTMENT_SIPS_MODIFICATION_SCREEN = 883;

  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=18175-21947&mode=dev
  EPAN_INSTRUCTION = 884;

  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=17831-21378&mode=design&t=zb44oWDBDqhGJCRK-0
  // ScreenOptions: api/types/deeplink_screen_option/vkyc/vkyc_call_quality.proto
  // Proto: VKYCCallQualityScreenOptions
  VKYC_CALL_QUALITY_CHECK = 885;

  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=17932-80236&mode=design&t=UnvNYIjboa4k8vnY-4
  LOANS_GENERIC_INFO_BOTTOM_SHEET = 886;

  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=23674-48951&mode=design&t=z5e0eOyYyzFYuN2E-0
  // ScreenOptions: api/types/deeplink_screen_option/pay/screen_options.proto
  // Proto: RecurringPaymentCancellationDisclaimerScreenOptions
  RECURRING_PAYMENT_CANCELLATION_DISCLAIMER_SCREEN = 887;

  // Screen for entering pin flow to update SI instructions
  // Note: This would not show any info regarding the updating. That is the responsibility
  // of the user of this to ensure User has seen all information when updating SI
  // Figma: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=20262-17276&mode=design&t=MpXlnPMqEd5FnHKk-4
  UPDATE_STANDING_INSTRUCTION_PIN_SCREEN = 888;

  // Screen for entering Language Preference flow for user
  // Figma: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=4037-25142&mode=design&t=yj3hKSAsmCRtQNta-0
  LANGUAGE_PREFERENCE_BOTTOM_SHEET = 889;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=44338-40419&mode=design&t=CLBGpXyEbE6thRlm-4
  LOANS_MOBILE_NUMBER_INTRO_SCREEN = 890;

  // Screen for any intro screen, which is not binded with any rpc calls, Figma:
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=14169%3A14092&mode=design&t=bnzFoLGjV9atbrM5-1
  LOANS_GENERIC_INTRO_SCREEN = 891;

  // Deeplink to navigate to a Reward details screen, which has been claimed by the user:
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=12244-79466&mode=design&t=CmtuBRklVbY3rWEx-0
  CLAIMED_REWARD_DETAILS_SCREEN = 892;

  // Screen for taking lat long from the user
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/SimpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=6080-20230&mode=design&t=bjzJMZ3EIMa49ZKV-4
  MAP_ADDRESS_POINTER_SCREEN = 893;

  // Screen for adding new address
  // this screen existed before but was not mapped to any deeplink
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/SimpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=6082-21119&mode=design&t=bjzJMZ3EIMa49ZKV-4
  ADD_NEW_ADDRESS_DETAILS_SCREEN = 894;


  // Screen to show terminal state(either success/failure) for aa salary program flows like -
  // registration flows : income estimation from account check polls exhausted/ thus failure state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51726&mode=design&t=8hb8sMUkCPzdPEJq-0
  // verification flows : money transfer failure state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51292&mode=design&t=8hb8sMUkCPzdPEJq-0
  // activation flows : salary activated success terminal state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51258&mode=design&t=8hb8sMUkCPzdPEJq-0
  AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN = 895;

  // AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN indicates the screen where user declares the amount of money to be transferred.
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51447&mode=design&t=LCaSFfUaiubQKZSm-0
  AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN = 896;

  // api to load landing page for aa salary flow
  // orchestrator api is called which decides on the screen to land user
  AA_SALARY_LANDING_SCREEN = 897;

  // Client invokes the ProcessUserAckApi API to trigger an acknowledgement to the backend
  // SCREEN OPTIONS: api/types/deeplink_screen_option/consent/ack_screen_options.proto
  PROCESS_USER_ACK_API = 898;

  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=21121-108057&mode=design&t=48uwAqezJBArRkbb-4
  // screen options: api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto
  AA_SALARY_ADD_FUNDS_VIA_OFF_APP_TRANSFER = 899;

  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=29250-53141&mode=design&t=0yKDKXhDtylNwz2r-4
  // https://drive.google.com/file/d/16NBDLjcB_wNhTaZwjLadM2OyZfKcna7h/view?usp=drive_link
  // screen options: api/types/deeplink_screen_option/usstocks/screen_options.proto
  USSTOCKS_WALLET_BOTTOM_SHEET = 900;

  // Screen for showing all opened/redeemed rewards that a user has
  // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6478-18494&t=3KQ1F13a1uGPqXKW-0
  REDEEMED_REWARDS_SCREEN = 901;

  // figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-64651&m=dev
  LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET = 902;
  // Screen for showing loans offer details
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48296&t=fvrDnDMXVVtjw7ls-4
  LOANS_OFFER_DETAILS_SCREEN = 903;

  // Polling screen for liveness status
  LIVENESS_POLL_SCREEN = 904;

  // Screen to show new loan offer if user's previous application got rejected
  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=46393-26295&t=q6zuheZrcZJvN1GB-4
  LOANS_ALTERNATE_OFFER_SCREEN = 905;

  // bottom sheet with options. On submit/continue the corresponding option deeplink is opened or any custom client handled api is called
  // corr screen options: RequestChoiceScrollableBottomSheetScreenOptions
  // https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10655&t=EdZ92LttpzP4EDW3-4
  REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET = 906;
  // corr screen options: InfoAcknowledgementBottomSheetScreenOption
  // https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10700&t=EdZ92LttpzP4EDW3-4
  INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET = 907;

  // figma: https://www.figma.com/design/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=5018-7851&t=jjbTUBwXfMNsLkGv-0
  CONNECT_FI_TO_FI_ENTRY_SCREEN = 908;

  // asset landing page
  // figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1401&t=Wj2bIQWtVkFUKYcg-4
  // drive: https://drive.google.com/file/d/1xVMUwkYN7u17hSh6OMn-4ihR6-E_YWKx/view?usp=drive_link
  ASSET_LANDING_PAGE = 909;

  // US stocks account creation initiated screen
  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=30205-29091&t=tNrEZaCh13KMfPZZ-0
  US_STOCKS_ACCOUNT_CREATION_INITIATED_SCREEN = 910;

  // US stocks account creation success screen
  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=30205-29045&t=tNrEZaCh13KMfPZZ-0
  US_STOCKS_ACCOUNT_CREATION_SUCCESS_SCREEN = 911;

  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18840-60250&t=0L1qYKhUCZdrPcKW-4
  // DC Landing screen v2
  DC_DASHBOARD_V2_SCREEN = 912;

  // Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22372-2275&t=3Ipf2AmvgSrNChos-4
  // AA salary source of fund screen
  // screen options: AaSalarySourceOfFundScreenOptions
  AA_SALARY_SOURCE_OF_FUND_SCREEN = 913;

  // Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22453-84137&t=3OHqlyoyGx45FdbP-4
  // https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22453-83901&t=3OHqlyoyGx45FdbP-4
  // AA salary screen to download data
  // screen options: AASalaryDataPullScreenOptions
  AA_SALARY_DATA_PULL = 914;


  // Figma : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12199&t=<RQHbhg3cscJCSfYo-0></RQHbhg3cscJCSfYo-0>
  // Screen to view all recent activities of a user
  HELP_RECENT_ACTIVITIES = 915;

  // Figma : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12519&t=RQHbhg3cscJCSfYo-0
  // Screen to view details of a single recent activity
  HELP_RECENT_ACTIVITY_DETAILS = 916;

  // this deeplink calls a debit card rpc with mentioned in payload and redirects based on next_action sent back.
  // the rpc being called should have a next_deeplink in response.
  DEBIT_CARD_RPC_BASED_REDIRECTION = 917;

  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18840-58366&t=Fs7r5x36oykQDcVu-4
  // Fully SDUI driven bottom sheet
  SDUI_BOTTOM_SHEET = 918;

  // Figma: https://www.figma.com/design/kLX73V4pRucy8JM71ajH50/%F0%9F%9B%A0-US-stocks%2Fworkfile?node-id=7099-12536&t=yu4yZvNAbaubz3BB-4
  USS_FUNDS_TRANSFER_PROGRESS_SCREEN = 919;

  // Screen in the onboarding flow to allow users to choose among a variety of soft intents and personalise their app experience
  // https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1778-22348&t=1LHThNz1BtxXaVKz-1
  ONBOARDING_SOFT_INTENT_SELECTION = 920;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49837&t=AfIAgEeR8jJ3Srx2-4
  // this will be used to fetch user current location in loan application journey
  LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN = 921;

  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=8757-22777&t=8zvoJPiTVII30psg-4
  // Used for rendering different types of details in a bottom sheet
  // Screen options 'DetailsModalScreenOptions'
  ANALYSER_DETAILS_MODAL_BOTTOM_SHEET = 922;

  // Figma: https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1687-19926&t=JABqebPfcXrPl2ZB-1
  // Bottom sheet to display actionable journey steps
  JOURNEY_BOTTOM_SHEET = 923;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-50114&t=9ZqzZkIVaGc6qZwD-4
  // this will be used to add the address of users, in this values will be auto populated based on user's current location or address selected
  // by the user from search results.
  LOAN_ADD_NEW_ADDRESS_DETAILS_SCREEN = 924;
  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48233-51792&t=vsE6GEVpUMInkzdf-4
  LOANS_MULTIPLE_OFFER_DETAILS_SCREEN = 925;
  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48233-51791&t=vsE6GEVpUMInkzdf-4
  LOANS_MULTIPLE_OFFER_SELECTION_SCREEN = 926;
  // This screen will start vendor sdk(uqudo) to capture image and perform NFC scan of emirates id for nr onboarding.
  // https://docs.uqudo.com/docs
  INITIATE_UQUDO_SDK = 927;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49912&t=L7KoARBVyyxuiEd7-4
  LOAN_ADDRESS_AUTOCOMPLETE_BOTTOMSHEET = 928;

  // https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4020-15440&t=rk3SjeRfBauBGuGI-1
  JOURNEYS_LANDING_PAGE = 929;
  // Displays a meeting between the user and agent for performing vkyc checks:
  // https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=28192-26785&t=jMGDFwSyVt3FFU5X-0
  VIDEO_KYC_CALL_SCREEN = 930;
  // Secret Analyser Screen where we will show different analyser with charts and line items
  // Figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16174-29092&t=DPWf9k3dz2r3ATsl-1
  SECRET_ANALYSER_SCREEN = 931;
  // screen to initiate epf import session
  // client to call InitiateEpfImportSession rpc on this screen
  INITIATE_EPF_IMPORT_SESSION_SCREEN = 932;
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=305-13142&t=S9XAH2X60XUfyYdb-0
  IMAGE_CAPTURE_INFO = 933;
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=605-9795&t=S9XAH2X60XUfyYdb-0
  SUBMIT_DOC = 934;
  // https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10088&t=tyjHNPf1L614K26I-0
  CONTACT_US_LANDING_SCREEN = 935;
  // https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?t=tyjHNPf1L614K26I-0
  CONTACT_US_TERMINAL_SCREEN = 936;
  // https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22964-120344&t=zusV9HPSk9xcBnFS-0
  SALARY_PROGRAM_HEALTH_INS_ONSURITY_INPUT_FORM = 937;
  // figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=44478-31821&t=dapksmIHHdi4QDKg-4
  GENERIC_SDUI_SCREEN = 938;
  // Secret Analyser library Screen where all secret are shown to user
  // Figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-23989&t=51LZx7Z3lXC14GZP-4
  SECRET_ANALYSER_LIBRARY_SCREEN = 939;
  // Below 3 screens allow user to re-select and submit the loan parameters (amount, tenure etc) during an application journey when the offer is revised
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48296&t=ineZGXSL5c6MuaPK-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferDetailsScreenOptions
  REVISED_LOAN_OFFER_DETAILS_SCREEN = 940;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48596&t=ineZGXSL5c6MuaPK-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferCustomPlanSelectionBottomSheet
  REVISED_LOAN_OFFER_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET = 941;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48955&t=ineZGXSL5c6MuaPK-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferApplicationDetailsScreen
  REVISED_LOAN_OFFER_APPLICATION_DETAIL_SCREEN = 942;
  // figma : https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=44478-31821&t=v9ywqmOhRMQpXQrV-4
  CC_MILESTONE_BENEFITS_SCREEN = 943;
  // figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=44479-31964&t=keJo7MyFJw8f89EI-4
  CC_MILESTONE_BENEFITS_DETAILS_SCREEN = 944;
  // Figma : https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5199-46670&t=6mKN7q19PSHVnJUk-4
  // api.typesv2.deeplink_screen_option.connectedaccount.BankSelectionScreenOptions
  CA_BANK_SELECTION = 945;
  // screen is used to collect user's consent for using AA data for lending use-cases.
  LOANS_AA_CONSENT_COLLECTION_SCREEN = 946;
  // Figma : https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27821-27806&t=tdmegSLjAwLHV3Zw-4
  // api.typesv2.deeplink_screen_option.consent.ConsentV2ScreenOptions
  CONSENT_V2 = 947;
  // Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31238-5895&t=2rlztOighhcK6foN-4
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksCollectProofOfIdentityAddressScreenOptions
  US_STOCKS_COLLECT_PROOF_OF_IDENTITY_ADDRESS_SCREEN = 948;
  // Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31240-6611&t=YGf4IUuCjOEqo688-4
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksSubmitAdhaarScreenOptions
  US_STOCKS_SUBMIT_DIGILOCKER_AADHAAR_SCREEN = 949;
  // Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31238-5966&t=YGf4IUuCjOEqo688-4
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksUploadPANBottomSheetOptions
  US_STOCKS_UPLOAD_PAN_BOTTOM_SHEET = 950;
  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31722-17713&t=vMLKQSpfJF43TxUe-0
  // api.typesv2.deeplink_screen_option.fittt.screen_options.USStocksSummaryScreenOptions
  US_STOCKS_SIP_SUMMARY_BOTTOM_SHEET = 951;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=9qzkLma7WCLdgcSg-0
  // screen options: api.typesv2.deeplink_screen_option.contactus.ContactUsCategorySelectionScreenOptions
  CONTACT_US_CATEGORY_SELECTION_SCREEN = 952;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7946-7650&t=FsqgsDlfNkLSN4ch-0
  // screen options: api.typesv2.deeplink_screen_option.contactus.ContactUsCategorySelectionViewMoreScreenOptions
  CONTACT_US_CATEGORY_SELECTION_VIEW_MORE_BOTTOM_SHEET = 953;

  // A deeplink to showcase different methods available for investing in US stocks, e.g., one-time, monthly SIP, etc.
  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31722-13643&t=5Cxc6pMOW3kPaSlh-0
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.ChooseInvestmentMethodScreenOptions
  US_STOCKS_CHOOSE_INVESTMENT_METHOD_BOTTOM_SHEET = 954;
  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31722-13261&t=pNiLQsNTcAYuWMoa-1
  // types/deeplink_screen_option.fittt.screen_options.FITTTSIScreenOptions
  // Screen will be used to create or update SI Mandate for FITTT Rules
  FITTT_SI_SETUP_SCREEN = 955;

  // api/types/deeplink_screen_option/pan/screen_options.proto
  // GET_PAN_UPDATE_NEXT_ACTION_API will be a transition screen, and client makes call to GetPanUpdateNextAction Api during transition.
  GET_PAN_UPDATE_NEXT_ACTION_API = 956;

  // DC quick limits setting screen, used to update card usage and limits settings directly in single screen.
  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=9484-44493&t=etEy0nrXd3fU5Rts-4
  DC_USAGE_AND_LIMIT_SETTINGS_SCREEN = 957;

  // Screen used for searching users to initiate transactions with
  // Figma: https://www.figma.com/design/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=13537-22284&node-type=SECTION&t=zORhsFakLkxGkIQu-0
  // V2 since screen exists on client already but without a deeplink. Avoids confusion
  PAY_SEARCH_SCREEN_V2 = 958;

  // Where to find pan? bottom sheet, used to send email to search ITR notification emails contain PAN info.
  // Figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=30983-34232&node-type=FRAME&t=HA0z67WfbpjUL5IW-0
  // Screen options: HelperBottomSheetScreenOptions
  HELPER_BOTTOM_SHEET = 959;

  // This will be used to open default email app.
  OPEN_EMAIL_APP_ACTION = 960;
  // screen options: PanVerificationScreenOptions in api/typesv2/deeplink_screen_option/pan/pan_screen_options.proto
  // Figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=31028-255234&node-type=frame&t=l8pqchLHIyfjpXCs-0
  PAN_VERIFICATION = 961;

  DC_ONBOARDING_INTRO = 962;

  // generic bottom sheets present on the offers catalog page V2
  // Figma: https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9073&node-type=frame&t=3mz9KqdYwv0sjXia-0
  // screen options: CatalogOfferRedemptionBottomSheetScreenOptions in package api.typesv2.deeplink_screen_option.rewards.proto
  CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET = 963;

  // Addrress Selection Bottom sheet for rewards entities (offer, exchanger, rewards) use cases, ex- offer redemption flow
  // figma -> https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9073&node-type=frame&t=3mz9KqdYwv0sjXia-0
  REWARDS_ADDRESS_SELECTION_BOTTOM_SHEET = 964;

  // Figma: https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=ocYBnZsxF4kdCkhM-0
  // screen options: OfferDetailsBottomSheetScreenOptions in package api.typesv2.deeplink_screen_option.rewards.proto
  OFFER_DETAILS_BOTTOM_SHEET = 965;

  //https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=10292-110396&t=wp3ISt7F66eiaob3-4
  SMS_READER_CONSENT = 966;

  // https://www.figma.com/design/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=14325-3113&t=bOa4Uhj5w3BFiEEM-1
  // screen options: AutoPayFrequencySelectionBottomSheetScreenOptions in api/typesv2/deeplink_screen_option/pay/screen_options.proto
  AUTO_PAY_FREQUENCY_SELECTION_BOTTOM_SHEET = 967;

  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17853&node-type=frame&t=mwzhgZXcWAcKun4o-0
  // screen options: USSTradeDetailsScreenOptions
  US_STOCKS_TRADE_DETAILS_SCREEN = 968;

  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63454-34056&t=WXHzMq429kA2DLtx-4
  LOANS_CONSENT_SCREEN = 969;

  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11330-21495&t=eNzRYvO8zMq10N9E-4
  DC_TOGGLE_TRAVEL_MODE_CONFIRMATION_BOTTOM_SHEET = 970;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54513-23747&t=FX4qqjMEPyFyF9IU-4
  // This screen is used in Loans for Option Selection(radio button list)
  LOANS_OPTION_SELECTION_BOTTOM_SHEET = 971;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54466-46642&t=cfiBff7SU9PUgvS7-4
  // This Full screen is for single option selection(radio button list)
  LOANS_OPTION_SELECTION_FULL_SCREEN = 972;

  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11906-21003&t=0bFEokg4ICMG3rUW-4
  DC_ORDER_PHYSICAL_CARD_V2_SCREEN = 973;

  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11949-25101&t=0bFEokg4ICMG3rUW-4
  DELIVERY_ADDRESS_SELECTION_BOTTOM_SHEET = 974;

  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11938-21917&t=0bFEokg4ICMG3rUW-4
  DC_BENEFITS_DETAILS_BOTTOM_SHEET = 975;

  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=10031-19148&t=gd97SMDFHPNq2tmK-4
  DC_PHYSICAL_CARD_ORDER_SUCCESS_SCREEN_V2 = 976;

  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=6903-66655&t=Ev1IUWBdqN8wZ4Ef-4
  // This Full screen is for pending charges that need to be cleared by user to proceed with account closure
  SA_CLOSURE_ACCOUNT_PENDING_CHARGES = 977;

  //https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24472&t=xlHnPFgaiXPDzKbb-4
  //Generic loan offer screen all loan program
  LOANS_OFFER_INTRO_SCREEN = 978;
  // Figma: - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24674&t=iKj4ZPnj2ACVlVfs-4
  LOANS_SINGLE_VENDOR_MULTI_OFFER_SCREEN = 979;
  //https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39739&t=v83a5Lp6TOWOy1bL-1
  //Asset Report Screen to show report of different assets
  //Use WealthAnalyserReportScreenOptions with this deeplink
  WEALTH_ANALYSER_REPORT_SCREEN = 980;

  // figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=29200-28293&node-type=section&t=PiwUJO972rd8COZj-0
  // screen options: SharePostPaymentScreenOptions
  SHARE_POST_PAYMENT_SCREEN = 981;

  // This screen used to collect the user's email and call the auth RPC(loginWithOauth) as part of the login process into app
  EMAIL_VERIFICATION = 982;

  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39715&t=mEhezfbYMVWCUIA9-4
  // Displays all assets to be added
  NET_WORTH_ADD_ASSETS_SCREEN = 983;

  // This screen can be used to invoke scienaptic sdk after getting user consent for sms parsing
  // ScreenOptions : api/typesv2/deeplink_screen_option/scienaptic/screen_options.proto
  INVOKE_SCIENAPTIC_SMS_SDK = 984;

  // This screen can be used for transferring money between the user's own accounts
  SELF_TRANSFER_SCREEN = 985;
  // Generic deeplink to trigger mobile OS share flow to send any content to whatsapp, sms etc
  // client make a rpc call to fetch the content to be shared based on use case. rpc: frontend.referral.GetOsShareFlowContent
  // ScreenOptions : api/types/deeplink_screen_option/pkg/trigger_os_share_flow_screen_options.proto
  TRIGGER_OS_SHARE_FLOW = 986;

  // Success screen to be displayed after linking a upi number
  // ScreenOptions: UpiNumberLinkingSuccessScreenOptions
  UPI_NUMBER_LINKING_SUCCESS_SCREEN = 987;

  // This screen can be used to manage UPI settings such as UPI numbers, bank accounts etc
  MANAGE_UPI_SETTINGS = 988;

  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14463-22745&t=xBd4KGpMQqgvzsJz-4
  ATM_LOCATOR_SCREEN = 989;

  // This screen can be used for AA data Sharing.
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=55659-32858&t=IGqvyv0YRKv3kis1-4
  AA_DATA_SHARE_SCREEN = 990;

  // Figma:- https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-6517&t=pZ5GzsPEOILv4z4z-4
  LAMF_REPAYMENT_METHODS_SCREEN = 991;

  // Screen for selecting a country for international debit card widget
  // https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=15093-31421&t=JzkSMdmHJvoFuvJG-4
  DC_COUNTRY_SELECTION_BOTTOM_SHEET = 992;

  // This screen can be used to allow user to update the linked account associated with a UPI number or numeric ID.
  // ScreenOptions: UpiNumberAccountSelectionBottomSheetScreenOptions
  // Figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=31176-79063&t=x92XU7u0gJhZRpji-1
  UPI_NUMBER_ACCOUNT_SELECTION_BOTTOM_SHEET = 993;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=1735-50761&t=UADT6C7pHwXIlSEM-4
  // ScreenOptions: NetWorthHubScreenOptions
  ASSETS_DASHBOARD = 994;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10949&t=vbmOBUVASYRI3ZIN-4
  VERIFY_INCOME_HOME_SCREEN = 995;

  // Tier loader screen
  // A lottie animation gets displayed when user tries to add fund or add funds to us stocks wallet or create FD/SD
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125794&m=dev
  TIER_LOADER_SCREEN = 996;
  // Tier all plans screen v2
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10002-54939&t=e3eOtSONnhAQGR5B-4
  TIER_ALL_PLANS_SCREEN_V2 = 997;
  // Tier upgrade success screen v2
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125818&m=dev
  TIER_UPGRADE_SUCCESS_SCREEN_V2 = 998;
  // Loans know more screen - https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-6620&t=tA6ko9TFqnCRRkpg-4
  LOANS_KNOW_MORE_BOTTOM_SHEET = 999;
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-12038&t=0DJRrOF2NKL08GtL-0
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10853&t=0DJRrOF2NKL08GtL-0
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10889&t=0DJRrOF2NKL08GtL-0
  INCOME_ANALYSIS_STATUS_SCREEN = 1000;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61577-41955&t=I2PzLlAbmpATOjGU-4
  SALARY_ACCOUNT_SELECTION_SCREEN = 1001;
  // Generic screen to be used for any flow completion, success or failure screen
  // e.g. Money secrets next screen after story completion
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=5580-46284&t=ZEWUctFYgsnUYce5-4
  GENERIC_FLOW_COMPLETION_SCREEN = 1002;
  // Generic share screen options to be used for sharing any content on whatsapp, sms etc
  // https://drive.google.com/file/d/1UHC-Hg9EbY2neCXqSbqY9gO4b-WQuvcH/view?usp=sharing
  GENERIC_SHARE_SCREEN = 1003;
  // Screen that collect old passport file number or ARN number of the passports that is issued outside india for NR onboarding
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=3183-10195&t=Fa7AeskhVRGFWPRI-4
  GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION = 1004;
  // Tier drop off bottom sheet
  // Bottom sheet which gets displayed when user clicks back from add funds or us stocks or create FD/SD
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-72545&t=mXS9zsieZAJj8r2h-4
  TIER_DROP_OFF_BOTTOM_SHEET = 1005;

  // VKYC generic bottom sheet for SDUI, this is use as vkyc drop off bottom sheet and showing some generic info
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-37053&t=lTeXolgSjyKy1n0S-4
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1212-33521&t=lTeXolgSjyKy1n0S-4
  VKYC_GENERIC_SDUI_BOTTOMSHEET = 1006;
  // Vkyc intro screen, driven by SDUI:
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34489&t=EreivkjdPffOwwix-4
  VKYC_INTRO_V2_SCREEN = 1007;
  // Vkyc steps info screen, driven by SDUI:
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34617&t=EreivkjdPffOwwix-4
  VKYC_STEPS_INFO_SCREEN = 1008;
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
  // deeplink for tiering landing screen - rpc will be called to get the screen options to construct the screen
  // screen options: TieringLandingScreenOptions
  // rpc called: GetTierFlowScreen
  TIERING_LANDING_SCREEN = 1009;
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
  // deeplink for tiering drop off full screen - screen options will be passed to construct the screen
  // screen options: DropOffFullScreenOptions
  TIERING_DROP_OFF_FULL_SCREEN = 1010;
  // Tier detailed benefits bottom sheet
  // Bottom sheet which gets displayed when user clicks on view details of any tier
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10043-11580&t=t76RUnvyrif5rq8v-4
  TIER_DETAILED_BENEFITS_BOTTOM_SHEET = 1011;

  // deeplink for wealth builder landing screen
  WEALTH_BUILDER_LANDING_SCREEN = 1012;

  // deeplink for mapper quick link - user's phone number will be auto-linked to their primary account VPA on this screen
  // https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=32620-6753&t=mXgnC0v6GdZoE8cY-1
  UPI_MAPPER_QUICK_LINK = 1013;
  // Screen to show otp entering screen if the user is not logged in
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-27445&t=GcJnfdqpiktGH8eP-4
  WEB_LOGIN_WITH_OTP_SCREEN = 1014;
  // Screen to show pan form for loans eligibility check
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-29282&t=kZdxaFjCqBj1IdPO-4
  WEB_LOANS_ELIGIBILITY_PAN_FORM_SCREEN = 1015;
  // Screen to show employment form for loans eligibility check
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-29538&t=kZdxaFjCqBj1IdPO-4
  WEB_LOANS_ELIGIBILITY_EMPLOYMENT_FORM_SCREEN = 1016;
  // Screen to show tnc screen for loans eligibility check
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-28692&t=kZdxaFjCqBj1IdPO-4
  WEB_LOANS_ELIGIBILITY_TNC_SCREEN = 1017;
  // Screen to show polling screen for loans eligibility check
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-29775&t=kZdxaFjCqBj1IdPO-4
  WEB_LOANS_ELIGIBILITY_POLLING_SCREEN = 1018;
  // Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9255-54117&t=t2K4QJevtLdS8EZ1-4
  // Screen for selecting a bank account Auto Verification
  LOANS_AUTO_PAY_AUTH_METHOD_SELECTION_BOTTOM_SHEET = 1019;
  // Common polling screen for assets import flows
  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=121-9682&t=TMDdzjxiprl4u9IS-4
  ASSET_IMPORT_STATUS_POLLING_SCREEN = 1020;

  // Polling screen to get the next action after the physical card dispatch.
  GET_PHYSICAL_CARD_DISPATCH_NEXT_ACTION = 1021;

  // Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9295-39322&t=cpkfGPIy5hMHmKhH-4
  LOAN_APPLICATION_ERROR_STATUS_SCREEN = 1022;
  // PAN prefill and mutual fund import consent screen for the wealth builder 2.0 flow
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=328-16776&t=ftAnK2nwPrGYwISh-0
  MF_HOLDINGS_IMPORT_PAN_CONSENT_SCREEN = 1023;
  // Screen to show welcome message to the user after logging in
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-28917&t=ujMktj5IZfPyqjyR-4
  WEB_WELCOME_SCREEN = 1024;
  // Screen to show the current loan offer for the user after loans eligibility check
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-29977&t=ujMktj5IZfPyqjyR-4
  WEB_LOANS_ELIGIBILITY_LOAN_OFFER_SCREEN = 1025;
  // Screen to show the waitlist result for the user after loans eligibility check
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-30194&t=ujMktj5IZfPyqjyR-4
  WEB_LOANS_ELIGIBILITY_WAITLIST_SCREEN = 1026;
  // Screen to show phone number entering screen if the user is not logged in
  // https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-27026&t=kZdxaFjCqBj1IdPO-4
  WEB_LOGIN_PHONE_FORM_SCREEN = 1027;
  // Screen for gathering nominee details for savings account
  // Figma: https://www.figma.com/design/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=14542-41231&t=v4ZpQQ6EBYM9M6fh-4
  ADD_NOMINEE_DETAILS_SCREEN = 1028;
  // Success Screen for updating nominee details for savings account
  // Figma: https://www.figma.com/design/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=14542-41244&t=UabtNsT83emYz239-4
  SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN = 1029;
  // Screen to show home walkthrough to user
  // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-60286&p=f&t=UbesNP58UgvnGejK-0
  WALKTHROUGH_SCREEN = 1030;
  // Screen for calling SendSmsData rpc while showing a loading screen
  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=413-14023&t=QnqXdSa3Zalejzao-4
  SEND_SMS_DATA = 1031;
  // Screen to show connect more assets on wealth builder landing page
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7351-45880&t=hw262jvGNUlVPeSO-4
  WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN = 1032;
  // Deeplink to navigate to a reward claim screen
  //https://www.figma.com/design/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=5535-47031&t=Cm0hCOjgfAkh5aCK-4
  REWARD_CLAIM_SCREEN = 1033;
  // Deeplink to navigate to Daily Portfolio tracker screen
  // Api 'GetPortfolioTrackerLandingPage'
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=Q9eQjZjJg10nxoIA-4
  PORTFOLIO_TRACKER_LANDING_SCREEN = 1034;
  // Deeplink to navigate to Daily Portfolio tracker's Asset Distribution screen
  // Api 'GetAssetDetailsPage'
  // ScreenOptions:
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20157&t=Q9eQjZjJg10nxoIA-4
  PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN = 1035;
  // Deeplink to list UPI accounts for the particular bank.
  // https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=5833-60135&t=ACkg7vR6UsRgtuTu-0
  LIST_UPI_ACCOUNTS = 1036;
  // To show system dialog for adding app shortcut for QR Scan
  SCAN_QR_APP_SHORTCUT_CREATION_DIALOG = 1037;
  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
  LOANS_FORM_ENTRY_SCREEN = 1038;
  // represents credit card dashboard screen v2
  // https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5716&t=NpQCwd13A6FBIgnw-4
  CREDIT_CARD_DASHBOARD_SCREEN_V2 = 1039;
  // user has no pan and gives consent to share form 60 declaration
  // as of the time of writing this comment, this screen is only being used for taking consent; no additional details are being collected
  FORM_60_DECLARATION = 1040;
  // In Loans, whenever we need to take the user's confirmation for any action, we can use this bottom sheet
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=54513-23027&t=kg9M6hgq9KNlUf7x-0
  LOANS_CONFIRMATION_BOTTOM_SHEET = 1041;
  // Deeplink to show Reward Claim Success Screen
  // Currently will only be used for Fi-Coins flow.
  // Figma:- https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=8073-19980&t=MY1jWYlSz8xJsL1Z-4
  REWARD_CLAIM_SUCCESS_SCREEN = 1042;
  // Client calls the SkipMatrixStage API
  SKIP_STOCKGUARDIAN_MATRIX_STAGE_API = 1043;
  // Client calls the ResetMatrixStage API
  RESET_STOCKGUARDIAN_MATRIX_STAGE_API = 1044;
  // Client redirects user to web view for digilocker authentication
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29575&t=e072nNSo04USyk3D-4
  INITIATE_DIGILOCKER_AUTH = 1045;
  // User chooses the kyc method for stockguardian - CKYC,DIGILOCKER, etc...
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29655&t=e072nNSo04USyk3D-4
  STOCKGUARDIAN_KYC_CHOICE_SCREEN = 1046;
  // Screen to show the digilocker steps and benefits
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29596&t=FEPmn6AdR7Urh3qU-1
  DIGILOCKER_INTRO_SCREEN = 1047;

  // Screen to render the Credit Cards SDK. The specific destinations inside
  // the SDK can be controlled by the screen options (TODO on options ?? ).
  CREDIT_CARD_SDK_SCREEN = 1048;

  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=69553-38807&t=xKsnIo792toAzLeX-4
  LOANS_CONSENT_V2_SCREEN = 1049;

  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68069-73077&t=90iYYECXBXlSRE0Q-4
  WEB_SECRET_ANALYSER_SCREEN = 1050;

  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68069-74348&t=90iYYECXBXlSRE0Q-4
  WEB_ERROR_SCREEN = 1051;

  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68069-72838&t=uokSvAgcVOYA824k-4
  WEB_PAN_FORM_SCREEN = 1052;

  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68069-74110&t=RARDQFYxe5ZoO3LR-4
  WEB_POLLING_SCREEN = 1053;

  // Figma - https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay---Workfile?node-id=36676-31931&t=DHrVlfHubQnxWK4A-0
  // Screen to show the Average Monthly Balance details for the user
  AMB_DETAILS_SCREEN = 1054;

  // Figma - https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2391-259&t=B4JCaNn35YGRKPdq-4
  // screen options: api/typesv2/deeplink_screen_option/firefly/screen_options.proto -> CcIntroScreenV2ScreenOptions
  CC_INTRO_SCREEN_V2 = 1055;

  // Figma - https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748&t=4SzjTwXgOKZ9sTE9-4
  // New Contact Us Landing screen for the user to resolve any issues faced in the app.
  // This is the V2 of CONTACT_US_LANDING_SCREEN.
  // Example: A deeplink that navigates the user directly to the new contact us page.
  CONTACT_US_LANDING_SCREEN_V2 = 1056;

  // Deeplink to navigate to the screen listing open/active incidents.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
  OPEN_INCIDENTS_SCREEN = 1057;
  // Deeplink to navigate to the Search Query Screen
  // Figma: https://www.figma.com/design/HqMQEG4wBLPvhjdq1VEj3j/%E2%98%8E%EF%B8%8F-CX-workfile?node-id=70-187&t=Qrg5D7UsFKIkXMc9-0
  CONTACT_US_QUERY_SCREEN = 1058;

  // Screen to show the magic import screen for networth which displays scan and review screens
  // Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
  NETWORTH_MAGIC_IMPORT_SCREEN = 1059;

  // [Deprecated]: Screen to show the analysed result of magic import uploads
  // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12516-8011&t=vjav20xKlpC3t1ui-4
  MAGIC_IMPORT_ASSETS_LIST_SCREEN = 1060 [deprecated = true];

  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37832&t=JHfSdzSbmlwPJwbn-0
  LOANS_PERMISSION_BOTTOM_SHEET_SCREEN = 1061;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37325&t=KW7tl7yDgNqbY4lQ-0
  LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET = 1062;
  // Deeplink to show waitlist screen for products
  // Figma:
  GENERIC_PRELAUNCH_SCREEN = 1063;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=75523-46825&t=nxmxSGLBM1kmeBal-4
  LOANS_KEY_VALUE_ROWS_BOTTOM_SHEET = 1064;

  // Generic screen for auth totp
  // eg: Net worth MCP totp token
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12332-24178&p=f&t=37cqneHx6DieE19b-0
  // Screen options: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions
  AUTH_TOTP = 1065;

  // Figma : https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3107-12167&t=tpdZmbOYu5cVusPo-0
  // api.typesv2.deeplink_screen_option.consent.SaDeclarationScreenOptions
  SA_DECLARATION = 1066;

  // Figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=4131-2696&t=b6oHw177LpZdJg5i-4
  // Screen options: api.typesv2/deeplink_screen_options.rewards.EarnedRewardsHistoryScreenOptions
  EARNED_REWARDS_HISTORY_SCREEN = 1067;
  // Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-13207&t=Gyk1mAcTx6b3G9sr-4
  // Screen options: api.typesv2/deeplink_screen_options.assetandanalysis.WealthSduiBottomSheetScreenOptions
  WEALTH_SDUI_BOTTOM_SHEET = 1068;
  // loading screen for GetNetworthDataFile api, this will fetch the networth data for user in specific file format and download it
  DOWNLOAD_NETWORTH_DATA = 1069;
  // loading screen for GetNetworthDataFile api, this will fetch the networth data for user in specific file format and export it to AI app's
  EXPORT_NETWORTH_DATA = 1070;

  // Figma - https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3007-12881&t=y7Jv1lCWlM1R2xOV-0
  // screen options: api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto
  CONFIRM_CARD_MAILING_ADDRESS_SCREEN_V2 = 1071;

  // On this screen, Web will call GenerateDigilockerAuthUrl API to get the digilocker auth url
  WEB_GENERATE_DIGILOCKER_AUTH_URL_API = 1072;

  // On this screen, Web will call StartSgKycApi API to start the stockguardian kyc flow
  // As Discussed with client, these APIs deeplink will not be loading any screen
  // and directly calling the apis. so, navigation will not be affected due to this.
  WEB_START_SG_KYC_API = 1073;

  // Screen to show the retry screen for KYC
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=66085-9215&m=dev
  WEB_KYC_RETRY_SCREEN = 1074;

  // On this screen, Web will call GetKycStatus API to get the status
  WEB_SG_KYC_STATUS_API = 1076;

  // used to confirm loan application using OTP
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=76543-95399&m=dev
  WEB_KYC_CONFIRMATION_VIA_OTP_SCREEN = 1077;

  // https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2262-4544&t=CQ4BU3IWZxj822Ih-4
  CC_DETAILS_AND_BENEFITS_SCREEN = 1078;

  // Polling screen for fetching status and next action for Recharges flow
  RECHARGE_POLLING_SCREEN = 1079;

  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-96670&t=XhP8BlFamoV8q77J-4
  RECHARGE_INTRO_SCREEN = 1080;

  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-97597&t=XhP8BlFamoV8q77J-4
  RECHARGE_PLANS_SCREEN = 1081;

  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-99237&t=XhP8BlFamoV8q77J-4
  BILL_DETAILS_CONFIRMATION_SCREEN = 1082;

  // screen for cancellation option bottom sheet to be used in salary estimation service
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77530-89679&t=QYfqQ7fleiJx5xaF-4
  SALARY_EST_CANCELLATION_BOTTOM_SHEET = 1083;

  // Deeplink to call CollectCsatSurvey RPC
  COLLECT_CSAT_SURVEY = 1084;

  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=qmTzSq6nX4YOgW1k-0
  // Client calls GetMagicImportIdeasBanner to render the above design
  NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN = 1085;

  // screen to display list of consents, this will be scrollable page
  // ScreenOptions: GenericScrollableRecordConsentScreenOptions, Api of consent submit: frontend.consent.RecordConsent
  // https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=sIV9m0wWxgcuqPsq-4
  // https://drive.google.com/file/d/13VeL_lDoKH8BcyB4oNXu9uzoIJjvusOV/view?usp=sharing
  WEALTH_GENERIC_RECORD_CONSENT = 1086;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77728-356593&p=f&t=BHedV8jy6vEfO0hS-0
  LOANS_DASHBOARD_V4_SCREEN = 1087;
}

message CreditCardWebEligibilityCheckStatusScreenOptions {
  // client request id for the flow
  string client_req_id = 1;
}

message RefereeActionsInfoScreenOptions {
  string referee_actor_id = 1;
}

message BackAction {
  // flag to show back button
  api.typesv2.common.BooleanEnum show_button = 1;
  // deeplink to be used for back action
  // if empty, client can fall back to default handling
  // we can have an entry point field in screen options which can be used for default handling at client side if required
  deeplink.Deeplink deeplink = 2;
}

message PanForAuthScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  Cta cta = 3;
  api.typesv2.common.VisualElement icon = 4;
}

message AlfredRequestSummaryScreenOptions {
  // category_type is a mandatory field
  string category_type = 1;
  // request_types is optional, by default all request_types corresponding to a category will be used
  repeated string request_types = 2;
  // statuses is optional field, by default only IN_PROGRESS and SUCCESS statues will be applied
  repeated string statuses = 3;
  // sort_order is optional, by default it will sort in descending order of created at
  string sort_order = 4;
}

message InvestmentRiskProfileMultiChoiceQuestion {
  // identifier for the question, to be sent to BE when submitting answers
  string question_id = 1;
  // question text to be shown on top of the answer choices
  api.typesv2.common.Text question_text = 2;
  // question sub text to be shown just below the question_text
  api.typesv2.common.Text question_sub_text = 3;
  // list of answer choices
  repeated InvestmentRiskProfileAnswerChoice answer_choices = 4;
  // selection type of the multiple choice
  api.typesv2.MultiChoiceSelectType multi_choice_select_type = 5;
  // NOTE: Below CTAs are to be populated used by client only if populated
  // Below combinations of CTAs are valid
  // 1. Next
  // 2. Next and Back
  // 3. Submit and Back
  //
  // On clicking next cta, client should handle taking user to next question in the list
  // next cta should be enabled only when user selects at least one answer choice
  deeplink.Cta next_cta = 6;
  // On clicking back cta, client should handle taking the user to the previous question
  deeplink.Cta back_cta = 7;
  // cta to submit all answers together
  // client will call SubmitRiskQuestionnaireAnswers rpc to submit these answers and gets the next depplink to show
  // cta to be enabled once user selects at least one answer choice
  deeplink.Cta submit_cta = 8;
  // consent checkbox to share the questionnaire data with epifi wealth
  // to be displayed when not nil
  api.typesv2.common.ui.widget.CheckboxItem bottom_check_box = 9;
}

message InvestmentRiskProfileAnswerChoice {
  // identifier for the answer choice, to be sent to BE when submitting answers
  string answer_id = 1;
  // display text for the answer
  api.typesv2.common.Text display_text = 2;
  // in case user is taking the questionnaire again
  // this will be true for the answer choices selected previously
  bool is_selected = 3;
}

// screen options for INVESTMENT_RISK_PROFILE_QNA_SCREEN
message InvestmentRiskProfileQnAScreenOptions {
  // common title to be displayed for all the questions
  api.typesv2.common.Text title = 1;
  // list of questions to be displayed, each question one after the other in the given order
  repeated InvestmentRiskProfileMultiChoiceQuestion questions = 2;
}

// screen options for INVESTMENT_RISK_PROFILE_DASHBOARD_SCREEN
message InvestmentRiskProfileDashboardScreenOptions {
  // card to be displayed on the top, represents suggests investment strategy
  // remaining details (investment distribution) should be fetched by calling RPC
  api.typesv2.common.ui.widget.ImageTitleSubtitleElement risk_category_card = 1;
  // title for the dashboard screen
  api.typesv2.common.Text title = 2;
}

// screen options for INVESTMENT_RISK_PROFILE_POLLING_SCREEN
message InvestmentRiskProfilePollingScreenOptions {
  // text to be displayed below the loading icon
  api.typesv2.common.Text loading_text = 1;
}

// screen options for UPDATE_CALL_INFO_API
message UpdateCallInfoApiScreenOptions {
  frontend.deeplink.Deeplink next_action = 1;
  string entry_point = 2;
}

// Deeplink defines a deeplink URI. It stores the screen to land on and
// the payload(screen options) that can be used by the screen
// page/Activity/ViewController on client.
message Deeplink {
  // Screen type to land on
  Screen screen = 1;

  // screen_options is payload that can be used by the screen
  // Different Screens map to different Screen Options
  // Deprecated: in favour of screen_options_v2
  oneof screen_options {
    // Sample - used in testing. Maps to no Screen.
    SampleScreenOptions sample_screen_options = 2;

    // Maps to REGISTER_CKYC Screen.
    RegisterCKYCScreenOptions register_ckyc_screen_options = 3;

    // Maps to CHECK_LIVENESS Screen.
    CheckLivenessScreenOptions check_liveness_screen_options = 4;

    // maps to TIMELINE screen
    TimelineScreenOptions timeline_screen_options = 5;

    // Screen Options for CONSENT screen
    ConsentScreenOptions consent_screen_options = 6;

    // maps AFU_CONFIRM_START_POPUP
    AFUConfirmStartOptions afu_confirm_start = 7;

    // send mobile prompt for customer verification
    MobilePromptVerificationScreenOptions mobile_prompt_verification_screen_options = 8;

    // transactions timeline screen
    TransactionsTimelineScreenOptions transaction_timeline_screen_options = 9;

    // transaction receipt screen
    TransactionReceiptScreenOptions transaction_receipt_screen_options = 10;

    // Deposit details screen
    DepositAccountDetailsScreenOptions deposit_details_screen_options = 11;

    // Deposit landing screen
    DepositAccountLandingScreenOptions deposit_account_landing_screen_option = 12;

    // Deposit open account screen
    DepositOpenAccountOptions deposit_open_account_options = 13;

    // Deposit close account screen
    DepositCloseAccountOptions deposit_close_account_options = 14;

    // Deposit add money screen
    DepositAddMoneyOptions deposit_add_money_options = 15;

    // screen options for SAVINGS_ACCOUNT_SETUP_PROGRESS
    SavingsAccountSetupProgress savings_account_setup_progress = 16;

    FaqArticleOptions faq_article_options = 17;

    // AFU_ATM_PIN_VALIDATION
    AfuAtmPinValidationOptions afu_atm_pin_validation_options = 18;

    // Dedupe error screen
    DedupeErrorScreenOptions dedupe_error_options = 19;

    // Debit card pin set screen options
    DebitCardPinSetScreenOptions debit_card_pin_set_options = 20;

    // Options/params required for the Upi pin setup deeplink
    UpiPinSetupOptions upi_pin_setup_options = 21;

    // Create reward sd screen options.
    CreateRewardSDScreenOptions create_reward_sd_Screen_options = 22;

    // Screen options for Intent based payment.
    PayViaIntentScreenOptions pay_via_intent_options = 23;

    // Raise disute screen options.
    RaiseDisputeScreenOptions raise_dispute_options = 24;

    FaqCategoryOptions faq_category_options = 25;

    ProfileUpiQrCodeScreenOptions profile_upi_qr_code_screen_options = 26;

    RewardShippingAddressInputScreenOptions reward_shipping_address_input_screen_options = 27;

    VKYCLandingOptions vkyc_landing_options = 28;

    CardUsageScreenOptions card_usage_screen_options = 29;

    CardHomeScreenOptions card_home_screen_options = 30;

    ConfirmCardMailingAddressOptions confirm_card_mailing_address_options = 31;

    StartEKYCOptions start_ekyc_options = 32;

    CardSettingsScreenOptions card_settings_screen_options = 33;

    CardLimitHomeScreenOptions card_limit_home_screen_options = 34;

    ViewVKYCScheduleScreenOptions view_vkyc_schedule_screen_options = 35;

    FiniteCodeVerifiedScreenOptions finite_code_verified_screen_options = 36;

    FitRuleHistoryScreenOptions fit_rule_history_screen_options = 37;

    GetCardAuthNextActionApiOptions get_card_auth_next_action_api_options = 38;

    FitCustomiseRuleScreenOptions fit_customise_rule_screen_options = 39;

    UpdateUserDetailsApiScreenOptions update_user_details_api_options = 40;

    HomeScreenOptions home_screen_options = 41;

    // screen options for EMPLOYMENT_DECLARATION
    EmploymentDeclarationOptions employment_declaration_options = 42;

    EnterReferralFiniteCodeScreenOptions enter_referral_finite_code_screen_options = 44;

    ErrorAppScreeningOptions error_app_screening_options = 45;

    UpiETBPinSetActivityOptions upi_etb_pin_set_activity_options = 46;

    StatementRequestOptions statement_request_options = 47;

    DebitCardOffersHomeScreenOptions debit_card_offers_home_screen_options = 48;

    DebitCardTrackingScreenOptions debit_card_tracking_screen_options = 49;

    ExternalRedirectionScreenOptions external_redirection_screen_options = 50;

    // used for waiting, failed, notify-me screens
    // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43132
    WealthOnboardingStatusScreenOptions wealth_onboarding_status_screen_options = 100;

    // for old clients, ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=7337%3A17979
    // for new clients, ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43163
    WealthOnboardingCaptureMissingDataScreenOptions wealth_onboarding_capture_missing_data_screen_options = 101;

    WealthOnboardingDocumentAadhaarEsignScreenOptions wealth_onboarding_document_aadhaar_esign_screen_options = 102;

    // for old clients, ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=7337%3A18248
    // for new clients,
    // 1. when consent has already been taken, ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43149
    // 2. when consent is yet to be taken, ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43113
    WealthOnboardingSignAgreementScreenOptions wealth_onboarding_sign_agreement_screen_options = 103;

    AppScreeningGmailVerificationOptions app_screening_gmail_verification_options = 104;

    LinkedInVerificationWaitScreenOptions linked_in_verification_wait_screen_options = 105;

    GenerateTxnIdCardApiOptions generate_txn_id_card_api_options = 51;

    CardSecurePinValidationOptions card_secure_pin_validation_options = 52;

    FitExploreRulesPageOptions fit_explore_rules_page_options = 53;

    VerifyWorkOtpEmailOptions verify_work_otp_email_options = 54;

    LivenessManualReviewOptions liveness_manual_review_options = 55;

    AskFiLandingPageOptions ask_fi_landing_page_options = 56;

    HelpSearchOptions help_search_options = 57;

    FitSubscriptionInfoRuleScreenOptions fit_subscription_info_rule_screen_options = 58;

    FitSubscriptionsPreviewScreenOptions fit_subscriptions_preview_screen_options = 59;

    FitAllSubscriptionsScreenOptions fit_all_subscriptions_screen_options = 60;

    FitAllCollectionsPageScreenOptions fit_all_collections_page_screen_options = 61;

    FitCollectionPageScreenOptions fit_collection_page_screen_options = 62;

    ConnectedAccountsOptions Connected_accounts_options = 63;

    // maps to wealth onboarding error screen
    WealthOnboardingErrorScreenOptions wealth_onboarding_error_screen_options = 64;

    // maps to screen options for landing screens
    WealthOnboardingLandingScreenOptions wealth_onboarding_landing_screen_options = 65;

    FitMyRulesPage fit_my_rules_page = 66;

    DetailedErrorViewScreenOptions detailed_error_view_screen_options = 67;

    PhonePermissionScreenOptions wa_consent_screen_option = 68;

    CreateRecurringPaymentScreenOptions create_recurring_payment_screen_options = 69;

    OneTimeExecuteRecurringPaymentScreenOptions one_time_execute_recurring_payment_options = 70;

    ConnectedAccountDetailsScreenOptions connected_account_details_screen_options = 71;

    CollectGenderScreenOptions collect_gender_screen_options = 72;

    CollectMaritalStatusScreenOptions collect_marital_status_screen_options = 73;

    CollectIncomeSlabScreenOptions collect_income_slab_screen_options = 74 [deprecated = true];

    CollectSignatureScreenOptions collect_signature_screen_options = 75;

    CollectPanScreenOptions collect_pan_screen_options = 76;

    CollectPoaScreenOptions collect_poa_screen_options = 77;

    SubscriptionHistoryScreenOptions subscription_history_screen_options = 78;

    OnboardingVKYCStatusScreenOptions onboarding_vkyc_status_screen_options = 79;

    RedeemExchangerOfferScreenOptions redeem_exchanger_offer_screen_options = 80;

    VKYCNotificationScreenOptions vkyc_notification_screen_options = 81;

    // Info acknowledgement screen
    InfoAcknowledgementScreenOptions info_acknowledgement_screen_options = 82;

    SearchFinancialActivitiesViewMoreOptions search_financial_activities_view_more = 83;

    InvestedFundDetailsScreenOptions invested_fund_details_screen_options = 84;

    MutualFundDetailsScreenOptions mutual_fund_details_screen_options = 85;

    VKYCIncompatibleDeviceScreenOptions vkyc_incompatible_device_screen_options = 86;

    CreditReportConsentScreenOptions credit_report_consent_screen_options = 87;

    WealthOnboardingInfoScreenOptions wealth_onboarding_info_screen_options = 88;

    RecurringPaymentDetailsScreenOptions recurring_payment_details_screen_options = 89;

    AuthorizeRecurringPaymentScreenOptions authorize_recurring_payment_screen_options = 90;

    // old refs: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fv1.0-%2F5-Aug?node-id=7337%3A18675
    // latest ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43582
    DigilockerDownloadScreenOptions digilocker_download_screen_options = 91;

    OnetimeMutualFundInvestmentOptions onetime_mutualfund_investment_options = 93;

    StoryScreenOptions story_screen_options = 92;

    FitttSportsChallengeHomeScreenOptions sports_challenge_home_screen_options = 94;

    InformationPopupOptions information_popup_options = 95;

    FitttSportsChallengeDetailsScreenOptions sports_challenge_details_screen_options = 96;

    WealthOnboardingDataCollectionInfoScreenOptions wealth_onboarding_data_collection_info_screen_options = 97;

    AllTransactionsAccountFilterOptions all_transactions_account_filter_options = 98;

    MutualFundsListScreenOptions mutual_funds_list_screen_options = 99;

    P2PInvestmentEligibilityCheckScreenOptions p2p_investment_eligibility_check_screen_options = 106;

    P2PInvestmentKnowMoreScreenOptions p2p_investment_know_more_screen_options = 107;

    P2PInvestmentUnlockAccessScreenOptions p2p_investment_unlock_access_screen_options = 108;

    P2PInvestmentEligibilityCheckResultScreenOptions p2p_investment_eligibility_check_result_screen_options = 109;

    P2PInvestScreenOptions p2p_invest_screen_options = 110;

    P2PWithdrawInvestmentScreenOptions p2p_withdraw_investment_screen_options = 111;

    ConnectedAccountBenefitsOptions connected_account_benefits_options = 112;

    SavingsAccountsHomeSummaryOptions savings_accounts_home_summary_options = 113;

    ConnectedAccountHandleReoobeOptions connected_account_handle_reoobe_options = 114;

    ConnectedAccountTransactionsComingSoonOptions connected_account_transactions_coming_soon = 115;

    P2PInvestmentErrorScreenOptions p2p_investment_error_screen_options = 116;

    // used for showing error screens for PAN and signature
    // ref: https://www.figma.com/file/S2kEkgG06XbwoYzISYfkuC/FFF-%2F-Wealth-%2F-v1.0-%2F-5-Aug?node-id=1566%3A1730
    WealthOnboardingDataCollectionErrorScreenOptions wealth_onboarding_data_collection_error_screen_options = 117;

    timeline.EnterAmountScreenOptions enter_amount_screen_options = 118;

    VkycInstructionScreenOptions vkyc_instruction_screen_options = 119;

    VkycPreRequisiteScreenOptions vkyc_pre_requisite_screen_options = 120;

    // maps to PAY_VIA_BANK_TRANSFER screen
    PayViaBankTransferScreenOptions pay_via_bank_transfer_screen_options = 121;

    // maps to ACCOUNT_CLOSURE_TRANSFER_INITIATED
    AccountClosureTransferInitiatedScreenOptions account_closure_transfer_initiated_screen_options = 122;

    // maps to VKYC_STATUS_SCREEN
    VKYCStatusScreenOptions vkyc_status_screen_options = 123;

    SalaryProgramEmployerConfirmationScreenOptions salary_program_employer_confirmation_screen_options = 130;

    AnalyserScreenOptions analyser_screen_options = 131;

    // Options to disconnect connected account via finvu
    // do not use: deprecated
    FinvuAccountDisconnectOptions finvu_account_disconnect_options = 132 [deprecated = true];

    // option to pass mutual fund collection id
    MutualFundCollectionsLandingScreenOptions mutual_fund_collections_landing_screen_options = 133;

    // option to pass mutual fund filter ids
    MutualFundFiltersScreenOptions mutual_fund_filters_screen_options = 134;

    // option to pass mutual fund order id
    MutualFundOrderReceiptScreenOptions mutual_fund_order_receipt_screen_options = 135;

    // option to show content for Webpage screen
    // ex: showing finvu disconnect url for AA usecase
    WebpageScreenOptions web_page_screen_options = 136;
    // offer details screen for pre approved loan
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3054
    PreApprovedLoanOfferDetailsScreenOptions pre_approved_loan_offer_details_screen_options = 137;
    // application details screen for pre approved loan
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3122
    PreApprovedLoanApplicationDetailsScreenOptions pre_approved_loan_application_details_screen_options = 138;
    // used to confirm loan application using OTP
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2830%3A3081
    PreApprovedLoanApplicationConfirmationViaOtpScreenOptions pre_approved_loan_application_confirmation_via_otp_screen_options = 139;
    // used to show loan application status
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2801%3A34673
    PreApprovedLoanApplicationStatusScreenOptions pre_approved_loan_application_status_screen_options = 140;
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2801%3A34647
    PreApprovedLoanApplicationStatusPollScreenOptions pre_approved_loan_application_status_poll_screen_options = 141;

    // option to show for AA account actions bottom sheet
    CaAccountActionsScreenOptions ca_account_actions_screen_options = 142;

    // used when we want to show instructions before continuing to do an action
    // https://www.figma.com/file/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=2586%3A13347
    InstructionsScreenOptions instructions_screen_options = 143;

    // used to show the dashboard for pre approved loan
    PreApprovedLoanDashboardScreenOptions pre_approved_loan_dashboard_screen_options = 144;

    PreApprovedLoanLandingScreenOptions pre_approved_loan_landing_screen_options = 145;

    PreApprovedLoanErrorScreenOptions pre_approved_loan_error_screen_options = 146;

    // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43295
    WealthOnboardingCollectIncomeScreenOptions wealth_onboarding_collect_income_screen_options = 147;

    SendWorkEmailOtpScreenOptions send_work_email_otp_screen_options = 148;

    PreApprovedLoanHowToApplyScreenOptions pre_approved_loan_how_to_apply_screen_options = 149;

    PreApprovedLoanKnowMoreScreenOptions pre_approved_loan_know_more_screen_options = 150;

    // screen will be used to show loan details of a particular loan for a user
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A12681
    PreApprovedLoanDetailsScreenOptions pre_approved_loan_details_screen_options = 151;

    // screen will be used to show waiting screen option for a user
    VKYCUserWaitingScreenOptions vkyc_user_waiting_screen_options = 152;

    // screen options for OFFER_CATALOG_SCREEN deeplink
    OfferCatalogScreenOptions offer_catalog_screen_options = 153;

    // screen options for preapproved loan cancel application deeplink
    PreApprovedLoanCancelApplicationScreenOptions pre_approved_loan_cancel_application_screen_options = 154;

    // used as a part of MissingDataWithStatus field within WealthOnboardingCaptureMissingDataScreenOptions
    // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=11106%3A46255
    CollectNomineeDetailsScreenOptions collect_nominee_details_screen_options = 155;

    // Entry Screen for preapproved loan Esign KFS screen options
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=6108%3A17153
    PreApprovedLoanInitiateEsignScreenOptions pre_approved_loan_initiate_esign_screen_options = 156;

    // Onboarding screenOption for USStocks
    // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1%3A11
    USStocksOnboardingScreenOptions usstocks_onboarding_screen_options = 157;

    // screen options for FIREFLY_GET_REQUEST_STATUS deeplink
    FireflyGetRequestStatusScreenOptions firefly_get_request_status_screen_options = 158;

    // screen options for displaying the offers on the card and also to begin the card onboarding for the user
    GetOffersScreenOptions get_offers_screen_options = 159;

    // screen options for the "know more" screen for displaying the queries and their resolutions
    FireflyGetInfoScreenOptions firefly_get_info_screen_options = 160;

    // screen options to select the delivery address for the delivery of the card .
    // The card will be delivered on the address selected
    FireflySetDeliveryAddressScreenOptions firefly_set_delivery_address_screen_options = 161;

    // screen options for the card activation screen
    FireflyCardActivationScreenOptions firefly_card_activation_screen_options = 164;

    // screen options for the Online Card Payments Enablement Screen
    OnlineCardPaymentsEnablementScreenOptions online_card_payments_enablement_screen_options = 165;

    // screen for manual review
    FireflyCardLivenessManualReviewScreenOptions firefly_card_liveness_manual_review_screen_options = 166;

    // screen options for AUTH_STATUS_POLL_SCREEN deeplink
    AuthStatusPollScreenOptions auth_status_poll_screen_options = 167;

    // screen options for pre approved loan prepay screen
    PreApprovedLoanPrePayScreenOptions pre_approved_loan_pre_pay_screen_options = 168;

    // screen options for credit card dashboard
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8219%3A40690
    CreditCardDashboardScreenOptions credit_card_dashboard_screen_options = 169;

    CreditCardBillDatesSelectionScreenOptions firefly_bill_date_selection_screen_options = 170;

    // screen options for chat screen
    ChatWithUsScreenOptions chat_with_us_screen_options = 171;

    // screen options for SCREENER_CHOICE
    ScreenerChoiceScreenOptions screener_choice_options = 172;

    // screen options for SCREENER_EMPLOYER_SELECTION
    ScreenerEmployerSelectionScreenOptions screener_employer_selection_options = 173;

    // screen options for CreateCard RPC
    CreditCardCreateCardScreenOptions firefly_create_card_screen_options = 174;

    // screen options for permanenet failure in credit card . For eg. in case of freaud
    CreditCardPermanentUserFailureScreenOptions firefly_permanent_failure_screen_options = 175;

    CreditCardOffersPageScreenOptions credit_card_offers_page_screen_options = 176;

    GenericRecordConsentOptions generic_record_consent_options = 177;

    // represent success screen data for order in usstocks
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A5294
    USStocksOrderStatusScreenOptions usstocks_order_status_screen_options = 178;

    // Represent screen option for buy screen
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4586
    USStocksBuyOrderScreenOptions usstocks_buy_order_screen_options = 179;

    // Represent screen option for sell screen
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4646
    USStocksSellOrderScreenOptions usstocks_sell_order_screen_options = 180;

    // Represent screen option for order receipt screen
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A4524
    USStocksOrderReceiptScreenOptions usstocks_order_receipt_screen_options = 181;

    // screen to display all transactions for pre-approved loans
    PreApprovedLoanAllTransactionsScreenOptions pre_approved_loan_all_transactions_screen_options = 182;

    // screen options for EDIT_PROFILE_INFO
    // https://www.figma.com/file/vzdt7XBmClIN07bgIAf3TE/Profile-%E2%80%A2-Workfile?node-id=6212%3A60769
    EditProfileInfoScreenOptions edit_profile_info_screen_options = 183;

    // screen options for STATUS_POLLING
    StatusPollScreenOptions status_poll_screen_options = 184;

    // Represent screen option for Symbol Details screen
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5459
    USStocksSymbolDetailsScreenOptions usstocks_symbol_details_screen_options = 185;

    // screen option for GenericPrompt
    GenericPromptOptions generic_prompt_options = 186;

    // screen options for Add fund screen with custom amount
    TransferInScreenOptions transfer_in_screen_options = 187;

    // screen options for credit card fetch limits
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9468%3A42159
    CreditCardFetchLimitsOptions credit_card_fetch_limits_options = 188;

    // screen options for credit card set limits
    CreditCardSetLimitsOptions credit_card_set_limits_options = 189;

    // screen options for credit card verify qr
    CreditCardVerifyQRCodeOptions credit_card_verify_qr_code_options = 190;

    // screen options for credit card set pin
    CreditCardSetCardPinOptions credit_card_set_card_pin_options = 191;

    // screen options for credit card set preferences
    CreditCardSetCardPreferencesOptions credit_card_set_card_preferences_options = 192;

    FederalSecurePinScreenOptions federal_secure_pin_screen_options = 193;

    // Screen options for jump available plans screen
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8214%3A10089
    P2PInvestmentAvailablePlansInfoScreenOption p2p_investment_available_plans_info_screen_option = 194;

    // Screen options for jump choose plan screen
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8214%3A10089
    P2PInvestmentChoosePlanInfoScreenOption p2p_investment_choose_plan_info_screen_option = 195;

    // Screen options for jump unlock plan screen
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A11410
    P2PInvestmentUnlockPlanScreenOption p2p_investment_unlock_plan_screen_option = 196;

    // screen options for credit card request new card screen
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8981%3A37134
    CreditCardNewCardRequestScreenOptions card_new_card_request_screen_options = 197;

    P2PWithdrawMoneyEnterAmountScreenOptions withdraw_money_enter_amount_screen_options = 198;

    // Represent screen option for USSTOCKS_ACCOUNT_ACTIVITY_SCREEN
    USStocksAccountActivityScreenOptions usstocks_account_activity_screen_options = 199;

    // screen options for credit card usage screen
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9468%3A41945
    CreditCardUsageScreenOptions credit_card_usage_screen_options = 200;

    // screen options for credit card controls screen
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9932%3A49539
    CreditCardControlsScreenOptions credit_card_controls_screen_options = 201;

    // screen options for credit card success screen
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9468%3A42651
    CreditCardSuccessBottomViewScreenOptions credit_card_success_bottom_view_screen_options = 202;

    // screen options for deposit templates screen
    DepositTemplatesScreenOptions deposit_templates_screen_options = 203;

    // screen options for credit card VKYC landing screen
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=6530%3A51906&t=I07Mg1R1pSWHs5J8-1
    CreditCardVKYCLandingScreenOptions credit_card_vkyc_landing_screen_options = 204;

    CreditCardSubmitBillingDatesScreenOptions credit_card_submit_billing_dates_screen_options = 205;

    // screen options for freeze/unfreeze credit card
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9958%3A42943&t=NAgLptKHM52JhS6L-4
    CreditCardFreezeUnfreezeScreenOptions credit_card_freeze_unfreeze_screen_options = 206;

    // screen options for the bill payment screen
    CreditCardBillPaymentScreenOptions credit_card_bill_payment_screen_options = 207;

    // screen option for screen to view the statement for the credit card
    CreditCardStatementScreenOptions credit_card_statement_sreen_options = 208;

    // screen options for screen to view card details for a card
    CreditCardDetailsScreenOptions credit_card_details_screen_options = 209;

    // used to trigger loan activity (pre-payment/closer) payment status poll
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=9783%3A10378
    PreApprovedLoanActivityStatusPollScreenOptions pre_approved_loan_activity_status_poll_screen_options = 210;

    // used to show loan activity status
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=9783%3A10358
    PreApprovedLoanActivityStatusScreenOptions pre_approved_loan_activity_status_screen_options = 211;

    // used to initiate card request workflow
    InitiateCardRequestScreenOptions initiate_card_request_screen_options = 212;

    CreditReportDownloadStatusPollScreenOptions credit_report_download_status_poll_screen_options = 213;

    CreditReportConsentV2ScreenOptions credit_report_consent_v2_screen_options = 214;

    // screen options for npci pin validation
    // options for AUTH_NPCI_PIN_VALIDATION screen
    AuthNpciPinValidationOptions auth_npci_pin_validation_options = 215;

    // screen options for sms otp verification
    // options for AUTH_SMS_OTP_VERIFICATION screen
    AuthSmsOtpVerificationOptions auth_sms_otp_verification_options = 216;


    CreditCardAuthOptionsScreenOptions credit_card_auth_options_screen_options = 217;

    // Represent screen-option for cancel order screen
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1341%3A5551&t=EoYsAyBXsrJvjkGG-4
    USStocksCancelOrderScreenOptions usstocks_cancel_order_screen_options = 218;

    OrderPhysicalDebitCardInfoScreenOptions order_physical_debit_card_info_screen_options = 219;

    FundTransferStatusScreenOptions fund_transfer_status_screen_options = 220;

    // screen options for RECORD_AUTH_FLOW_COMPLETION screen
    RecordAuthFlowCompletionOptions record_auth_flow_completion_options = 221;

    CreditReportDownloadFailureScreenOptions credit_report_download_failure_screen_options = 222;

    // screen options to show parameters on tier introduction screen
    // https://www.figma.com/file/lkZbcqGu9g5p3dvkGcttWL/Account-Tier-%E2%80%A2-Workfile?node-id=834%3A58125&t=2T0nG0AytyLvkMWh-0
    // TIER_INTRODUCTION
    TierIntroductionOptions tier_introduction_options = 223;

    // Screen options to return all available tier plans
    // TIER_ALL_PLANS
    TierAllPlansOptions tier_all_plans_options = 224;

    // https://www.figma.com/file/lkZbcqGu9g5p3dvkGcttWL/Account-Tier-%E2%80%A2-Workfile?node-id=744%3A55110&t=2T0nG0AytyLvkMWh-0
    // TIER_PLAN
    TierPlanOptions tier_plan_options = 225;

    // Options to return parameters to be shown in benefits bottom sheet for a particular benefit
    // TIER_BENEFIT_BOTTOMSHEET
    TierBenefitBottomSheetOptions tier_benefit_bottom_sheet_options = 226;

    // Options to return parameters to be shown in all benefits bottom sheet for a tier
    // TIER_ALL_BENEFITS_BOTTOMSHEET
    TierAllBenefitsBottomSheetOptions tier_all_benefits_bottom_sheet_options = 227;

    // Deeplink to show loan transaction receipt
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=5263%3A15963&t=PkQl2kPMuvRwD6qw-4
    PreApprovedLoanTransactionReceiptScreenOptions pre_approved_loan_transaction_receipt_screen_options = 229;
    // screen options for cc transaction receipt
    CreditCardTransactionReceiptScreenOptions credit_card_transaction_receipt_screen_options = 230;
    // screen options for onboarding_add_money
    OnboardingAddMoneyScreenOptions onboarding_add_money_screen_options = 231;
    // screen options for credit card raise dispute
    CreditCardRaiseDisputeOptions credit_card_raise_dispute_options = 232;
    // screen options for credit card dispute details
    CreditCardDisputeDetailsOptions credit_card_dispute_details_options = 233;
    // TILES_PROMPT options
    TilesPromptOptions tiles_prompt_options = 234;

    //screen options for ORDER_CHEQUEBOOK screen
    OrderChequebookOptions order_chequebook_options = 235;
    // screen options for CHEQUEBOOK_ORDER_DETAILS screen
    ChequebookOrderDetails cheque_book_order_details = 236;

    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=343%3A3619&t=CHf9Q5GhBbBYJHyt-4
    // deprecated in favour of CollectPANCard
    USStocksErrorScreenOptions usstocks_error_screen_options = 237 [deprecated = true];

    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=2868%3A8894&t=LV2JEcPPx08Tlpjy-4
    USStocksPANSourcesScreenOptions usstocks_pan_sources_screen_options = 238;

    // ref : https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?node-id=159%3A9290&t=BvhMlG1qumwXUkYt-0
    // TIER_UPGRADE_SUCCESS_SCREEN
    TierUpgradeSuccessScreenOptions tier_upgrade_success_screen_options = 239;

    // https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?node-id=159%3A6962&t=BvhMlG1qumwXUkYt-0
    // TIER_MANUAL_UPGRADE
    TierManualUpgradeOptions tier_manual_upgrade_options = 240;

    // figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=11210%3A60317&t=U8okulmu868wdb5r-4
    // screen when user is showed the loan tab but has no valid offer, hence is a non qualified user
    PreApprovedLoansNonQualifiedUsersScreenOptions pre_approved_loans_non_qualified_users_screen_options = 241;

    USStocksPANReuploadScreenOptions usstocks_pan_reupload_screen_options = 242;

    InsightsStoriesScreenOptions InsightsStories_screen_options = 243;

    // Option which contains ui context for TIER_ALL_PLANS_REDIRECTION
    TierAllPlansRedirectionOptions tier_all_plans_redirection_options = 244;

    // Generic halt screen options for credit card flows
    CreditCardHaltScreenOptions credit_card_halt_screen_options = 245;

    UsStocksCollectionScreenOptions usstocks_collection_screen_options = 246;

    // screen options for SCREENER_CONNECTED_ACCOUNTS_INFO
    ScreenerConnectedAccountsInfoScreenOptions screener_connected_accounts_info_screen_options = 247;

    // screen options for ACCOUNT_DELETION_ACKNOWLEDGEMENT
    AccountDeletionAcknowledgementScreenOptions account_deletion_acknowledgement_screen_options = 248;

    // screen options for USSTOCKS_CHECKING_INFORMATION_SCREEN
    UsStocksCheckingInfoScreenOptions usstocks_check_info_screen_options = 249;

    // Screen options used to show post consent approval activities
    CAPostConsentPollingScreenOptions ca_post_consent_polling_screen_options = 250;
    // Screen options used to show if account data pull is Success/Pending or Failure screen due to failure in consent request
    CAPostConsentTerminalStateScreenOptions ca_consent_terminal_state_screen_options = 251;

    //Screen options for credit card custom amount repayment screen
    //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=11436%3A56480&t=FqrqWEngCJqthaq8-0
    CreditCardCustomAmountRepaymentScreenOptions credit_card_custom_amount_repayment_screen_options = 252;
    // Screen options used to show dialog in case of pre-requisite checks not satisfied before placing an order
    USStockValidationDialogScreenOptions us_stock_validation_dialog_screen_options = 253;
    //Screen for credit card payment options screen
    //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582%3A176745&t=BrEwI8BmYLE6n4Of-0
    CreditCardBillRepaymentSelectionScreenOptions credit_card_bill_repayment_selection_screen_options = 254;
    //Screen options for credit card statements view
    CreditCardStatementViewScreenOptions credit_card_statement_view_screen_options = 255;
    //Screen options for credit card export statement screen.
    CreditCardExportStatementScreenOptions credit_card_export_statement_screen_options = 256;

    //Screen options for credit card payment status polling
    CreditCardPaymentStatusPollingScreenOptions credit_card_payment_status_polling_screen_options = 257;
    //Screen options for credit card payment status.
    CreditCardPaymentStatusScreenOptions credit_card_payment_status_screen_options = 258;

    // GENERIC_TRANSITION_SCREEN options
    GenericTransitionScreenOptions generic_transition_screen_options = 259;

    // this screen will be used to show physical card delivery status
    CreditCardPhysicalCardTrackingScreenOptions credit_card_physical_card_tracking_screen_options = 260;
    // Screen options for credit card txn dispute details screen
    CreditCardTxnDiputeDetailsScreenOptions credit_card_txn_dipute_details_screen_options = 261;

    // screen option to show pre-requisite checks for us-stocks
    UsStocksPreRequisiteCheckScreenOptions us_stocks_pre_requisite_check_screen_options = 262;

    // Contains request params for calling 'GetGenerateOTPScreen' which would be used to render verify otp screen
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11726%3A15473&t=BiCKQbpc2i2RVwPf-0
    P2PInvestmentOtpScreenOptions p2p_investment_otp_screen_options = 263;

    // Screen options for AUTH_LIVENESS_SUMMARY_STATUS_POLL_SCREEN
    // Used to poll liveness summary status api of auth orchestrator
    AuthLivenessSummaryStatusPollScreenOptions auth_liveness_summary_status_poll_screen_options = 264;
    // screen options for PHYSICAL_CARD_DISPATCH_SUCCESS_SCREEN
    PhysicalCardDispatchSuccessScreenOptions physical_card_dispatch_success_screen_options = 265;

    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15670&t=CQGbtXPd1G8WbTAA-4
    // P2P_INVESTMENT_CURRENT_STATUS_SCREEN
    P2PInvestmentCurrentStatusScreenOptions p2p_current_status_screen_options = 266;
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=12356%3A22973&t=k4CuMDGdVMPFu0vi-4
    // P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN
    P2PInvestmentActivityDetailsScreenOptions p2p_activity_details_screen_options = 267;
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15607&t=Sboiw3jG1WYq6w04-4
    // P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN
    P2PInvestmentRenewalCancellationScreenOptions p2p_renewal_cancellation_nudge_screen_options = 268;
    // USSTOCKS_SOF_WAITING_SCREEN
    UsStocksSOFWaitingScreenOptions us_stocks_sof_waiting_screen_options = 269;
    //CC VIEW TXNS SCREEN OPTIONS
    CreditCardViewAllTransactionScreenOptions credit_card_view_all_transaction_screen_options = 270;

    // DEBIT_CARD_OFFER_DETAILS_SCREEN
    DebitCardOfferDetailsScreenOptions debit_card_offer_details_screen_options = 271;

    // there can be cases when forex rate which was shown to user is no longer available
    // new forex rate should be shown to user and order should be created post confirmation on the new fx rate
    // newFxRateConfirmationDialog holds display fields to be shown to user in such case
    NewForexRateConfirmationDialogScreenOptions new_forex_rate_confirmation_dialog_screen_option = 272;
    // P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11889%3A14949&t=ftRY9JIOn9opQKmF-0
    // Used for calling 'GetRenewInvestmentAttributes' rpc
    P2PConfirmInvestmentRenewalScreenOptions p2p_confirm_investment_renewal_screen_options = 273;

    // MF_HOLDINGS_IMPORT_INITIATE_SCREEN
    MFHoldingsImportInitiateScreenOptions mf_holdings_import_initiate_screen_options = 274;

    // MF_HOLDINGS_IMPORT_OTP_SCREEN
    MFHoldingsImportOTPScreenOptions mf_holdings_import_otp_screen_options = 275;

    // MF_HOLDINGS_IMPORT_LOADING_SCREEN
    MFHoldingsImportLoadingScreenOptions mf_holdings_import_loading_screen_options = 276;

    // MF_HOLDINGS_IMPORT_CONSENT_SCREEN
    MFHoldingsImportConsentFlowScreenOptions mf_holdings_import_consent_flow_screen_options = 277;

    // screen to introduce user to what upi number is
    UpiNumberIntroScreenOptions upi_number_intro_screen_options = 278;

    // screen options for manage upi number screen
    ManageUpiNumberScreenOptions manage_upi_number_screen_options = 279;

    // screen options for rewards pie chart on cc dashboard
    // figma: https://www.figma.com/file/UO85a3b8ayLW9ZSFPhzRLi/CC-Rewards?node-id=813%3A33344&t=24SjfmiQG7Lcq4v0-0
    CreditCardExtraRewardsBottomViewScreenOptions card_extra_rewards_bottom_view_screen_options = 280;

    // screen options for cc EMI dashboard
    // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17336%3A75209&t=bnzHR0p1rNKzhZ4Q-4
    CreditCardEmiDashboardScreenOptions credit_card_emi_dashboard_screen_options = 281;

    // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=16896%3A72866&t=bnzHR0p1rNKzhZ4Q-4
    // screen options for cc EMI conversion view all active loan accounts
    CreditCardEmiViewAllActiveLoanAccountsScreenOptions credit_card_emi_view_all_active_loan_accounts_screen_options = 282;

    // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17404%3A73841&t=bnzHR0p1rNKzhZ4Q-4
    // screen options for cc EMI conversion view all eligible transactions
    CreditCardEmiViewAllEligibleTransactionsScreenOptions credit_card_emi_view_all_eligible_transactions_screen_options = 283;

    // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17389%3A71261&t=bnzHR0p1rNKzhZ4Q-4
    // screen options for cc EMI conversion loan offers screen
    CreditCardEmiTransactionLoanOffersScreenOptions credit_card_emi_transaction_loan_offers_screen_options = 284;

    // figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=15576%3A66172&t=bnzHR0p1rNKzhZ4Q-4
    // screen options for cc EMI conversion get loan account details page
    CreditCardEmiLoanAccountDetailsScreenOptions credit_card_emi_loan_account_details_screen_options = 285;

    // Us-stocks On-boarding landing screen
    // screen: USSTOCKS_ONBOARDING_SETUP_SCREEN
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2159&t=KajAsRvJf9aQDOLN-4
    USStockSetupAccountScreenOptions us_stock_setup_account_screen_options = 286;

    // screen: GET_NEXT_ONBOARDING_ACTION_API
    GetNextOnboardingActionScreenOptions get_next_onboarding_action_screen_options = 287;

    // VKYC Introductory screen having offer tiles.
    // V2 created in effort to make vkyc next action driven and for easy rollback plan
    // screen: VKYC_INTRO
    // ref : https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/Video-KYC-Workfile?node-id=10715%3A66935&t=TPBKZDryqVU60U0L-0
    VKYCIntroOptions vkyc_intro_options = 288;

    // screen: GET_VKYC_NEXT_ACTION_API
    GetVKYCNextActionApiScreenOptions get_vkyc_next_action_api_screen_options = 289;
    // screen options for cc benefits screen
    // ref: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=16900%3A73816&t=dN2tCc5oaZ6ouuJc-0
    CreditCardBenefitsScreenOptions credit_card_benefits_screen_options = 290;
    // Screen options for cc rewards selection
    CreditCardRewardsSelectionScreenOptions card_rewards_selection_screen_options = 291;
    // Screen options for CREDIT_CARD_REWARDS_POLLING_SCREEN
    CreditCardRewardsPollingScreenOptions card_rewards_polling_screen_options = 292;

    // VKYC pan selection screen asking user if he want to go with epan or physical pan
    // screen: VKYC_PAN_TYPE_SELECTION
    // ref : https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/Video-KYC-Workfile?node-id=10688%3A66056&t=QYh0ezZJ73swYJsI-0
    VKYCPanTypeSelectionScreenOptions vkyc_pan_type_selection_screen_options = 293;

    // VKYC instruction screen having instructions
    // base screen in mandatory for all vkyc callers
    // works as base screen for income occupation and ekyc bottom sheet
    // screen: VKYC_INSTRUCTIONS
    // ref: https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/Video-KYC-Workfile?node-id=10715%3A68323&t=C70FIZC5w9t4TpjI-0
    VKYCInstructionsScreenOptions vkyc_instructions_screen_options = 294;

    // screen options for pl address confirmation screen
    // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=4321%3A153632&t=8r8CkVDIkiosdHWL-1
    PreApprovedAddressConfirmationScreenOptions pre_approved_address_confirmation_screen_options = 295;

    // Entry Screen for preapproved loan E-NACH screen options
    // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=3503%3A95604&t=dwLExwCMBhglJV3e-1
    PreApprovedLoanInitiateMandateScreenOptions pre_approved_mandate_initiate_screen_options = 296;


    //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=15971%3A68591&t=9gxBKJIvtOlHy1u7-0
    // Screen options for cc EMI conversion preview pre close screen options
    CreditCardEmiPreviewPreCloseScreenOptions credit_card_emi_preview_pre_close_screen_options = 297;

    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=15993%3A67391&t=9gxBKJIvtOlHy1u7-0
    // Screen options for cc EMI conversion  pre close screen options
    CreditCardEmiPreCloseScreenOptions credit_card_emi_pre_close_screen_options = 298;

    // screen options for BOTTOM_SHEET
    GenericBottomSheetScreenOptions generic_bottom_sheet_screen_options = 299;

    // Options for Order Processing Screen.
    // This is not currently being used. The idea for this was to have meta-data for the rpc that is invoked for the deeplink.
    // But this means that we will have to duplicate and re-define the request object of the rpc that needs to be invoked which
    // can get easily complicated and add this as a touch point for any un-related change in the rpc. The solution here is
    // to migrated to deeplink v3 and directly import the request as a screen option without re-defining. We cannot import it
    // here currently because of circular dependency.
    OrderProcessingScreenOptions order_processing_screen_options = 300;

    // Pre-approved loan e-sign url webview screen
    PreApprovedLoanESignViewDocumentScreenOptions pre_approved_loan_e_sign_view_document_screen_options = 301;

    // Screen options for bottom sheet when buy/sell is closed
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4430%3A19481&t=OfbwOvDvEkCWhSoI-4
    USStocksBuySellClosedScreenOptions usstocks_buy_sell_closed_screen_options = 302;

    // WEALTH_ONBOARDING_IA_AGREEMENT_SCREEN
    // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14387%3A50967&t=Sh7Ge8sWA6qKaxfO-0
    WealthOnboardingIAAgreementScreenOptions wealth_onboarding_ia_agreement_screen_options = 303;

    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=15993%3A67391&t=9gxBKJIvtOlHy1u7-0
    // Screen options for pre approved employment details screen options
    PreApprovedEmploymentDetailsScreenOptions pre_approved_employment_details_screen_options = 304;

    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=15993%3A67391&t=9gxBKJIvtOlHy1u7-0
    // Screen options for pre approved income selection screen options
    PreApprovedIncomeSelectionScreenOptions pre_approved_income_selection_screen_options = 305;

    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=15993%3A67391&t=9gxBKJIvtOlHy1u7-0
    // Screen options for pre approved occupation screen options
    PreApprovedOccupationSelectionScreenOptions pre_approved_occupation_selection_screen_options = 306;

    // Pre Approved Info Dialog screeon options for PL which is being currently used to in Hard Pull Info Dialog
    // ref: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=15220%3A22173&t=8skjkIfG3H53FlYi-1
    PreApprovedInfoBottomDialogScreenOptions pre_approved_info_bottom_dialog_screen_options = 307;

    // Screen options for REMINDERS_SUBSCRIPTIONS_LIST Screen
    RemindersSubscriptionsListScreenOptions reminders_subscriptions_screen_options = 308;

    // AUTO_INVEST_COLLECTION_BOTTOM_VIEW_SCREEN
    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=2659%3A20274&t=6ft6iCPJYRUl5VAW-0
    AutoInvestCollectionBottomViewScreenOptions auto_invest_collection_bottom_view_screen_options = 309;

    // Screen options for START_EPAN
    // https://www.figma.com/file/g2x9BTVs01uHsAZ6D8g6ss/Video-KYC---FFF---26-Nov-2020?node-id=11735%3A47166&t=29sBqpxonIfGiNsG-0
    StartEPANScreenOptions start_e_pan_screen_options = 310;

    // Screen options for REMINDERS_CONFIG_SCREEN
    RemindersConfigScreenOptions reminders_config_screen_options = 311;

    // Screen options for REMINDERS_SUPPORTED_TOPICS_LIST_SCREEN
    RemindersSupportedTopicsListScreenOptions reminders_supported_topics_list_screen_options = 312;

    PlUpdatedRateScreenOptions pl_updated_rate_screen_options = 313;

    // screen: UPDATE_CALL_INFO_API
    UpdateCallInfoApiScreenOptions update_call_info_api_screen_options = 314;

    // INVESTMENT_RISK_PROFILE_QNA_SCREEN
    InvestmentRiskProfileQnAScreenOptions investment_risk_profile_qna_screen_options = 315;

    // INVESTMENT_RISK_PROFILE_DASHBOARD_SCREEN
    InvestmentRiskProfileDashboardScreenOptions investment_risk_profile_dashboard_screen_options = 316;

    // INVESTMENT_RISK_PROFILE_POLLING_SCREEN
    InvestmentRiskProfilePollingScreenOptions investment_risk_profile_polling_screen_options = 317;

    // screen options for the IN_APP_WAITLIST_SCREEN
    // This will be composed of a list of images which has to be appended on the client side one after another
    // without any gaps
    InAppWaitlistScreenOptions in_app_waitlist_screen_options = 318;
    // screen options for IN_APP_WAITLIST_CONFIRMATION_SCREEN
    // This will be the screen the user sees once the user has been added to the waitlist successfully
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=19876-79051&t=yPgt5JTreXNIdy51-0
    InAppWaitlistConfirmationScreenOptions in_app_waitlist_confirmation_screen_options = 319;

    // screen: DISCONNECT_ACCOUNT_INFO_BOTTOM_SHEET
    DisconnectAccountInfoScreenOptions disconnect_account_info_screen_options = 320;

    // screen: DISCONNECT_ACCOUNT_CONFIRM_BOTTOM_SHEET
    DisconnectAccountConfirmScreenOptions disconnect_account_confirm_screen_options = 321;

    // screen options for CARD_OFFERS_CATALOG_SCREEN
    CardOffersCatalogScreenOptions card_offers_catalog_screen_options = 322;

    // screen options for CARD_OFFER_DETAILS_SCREEN
    CardOfferDetailsScreenOptions card_offer_details_screen_options = 323;

    // screen options for MODIFY_RECURRING_PAYMENT_SCREEN
    ModifyRecurringPaymentScreenOptions modify_recurring_payment_screen_options = 324;

    // screen options for REMINDERS_UPDATE_STATE
    RemindersUpdateStateOptions update_reminder_state_options = 325;

    // screen options for CREDIT_CARD_INTRO_SCREEN_BRANDS
    CreditCardIntroScreenBrandScreenOptions credit_card_intro_screen_brand_screen_options = 326;

    // screen options for CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN
    CreditCardIntroRewardEstimationScreenOptions credit_card_intro_reward_estimation_screen_options = 327;

    // screen options for CREDIT_CARD_WELCOME_VOUCHERS_SCREEN
    CreditCardWelcomeVouchersScreenOptions credit_card_welcome_vouchers_screen_options = 328;

    // screen options for CREDIT_CARD_SUCCESS_CONFIRMATION_SCREEN
    CreditCardSuccessConfirmationScreen credit_card_success_confirmation_screen = 330;

    // screen options for DEPOSIT_STATEMENT_GENERATION_SCREEN
    DepositStatementGenerationScreenOptions deposit_statement_generation_screen_options = 331;

    // screen to show credit card bottom view error screen
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582-176745&t=HciXmKTqx3Wy4JmM-0
    CreditCardFailureBottomViewScreen credit_card_failure_bottom_view_screen = 332;
    // screen options for CREDIT_CARD_LANDING_SCREEN
    CreditCardLandingScreenOptions credit_card_landing_screen_options = 333;

    // screen options for PRE_APPROVED_LOAN_OFFER_DETAILS_SCREEN_V2
    PreApprovedLoanOfferDetailsV2ScreenOptions pre_approved_loan_offer_details_v2_screen_options = 334;

    // screen options for DOWNLOAD_DIGITAL_CANCELLED_CHEQUE
    DownloadDigitalCancelledChequeScreenOptions download_digital_cancelled_cheque_screen_options = 335;

    // screen options for the VKYC_ERROR_SCREEN
    // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%8E%A5-Video-KYC-Workfile?node-id=11293-71504&t=tKP5WieqYTeaY7yo-0
    VKYCErrorScreenOptions vkyc_error_screen_options = 336;

    // screen options for CONSENT_RENEWAL_SDK_LOGIN_SCREEN
    ConsentRenewalSdkLoginScreenOptions consent_renewal_sdk_login_screen_options = 337;

    // Options/params required for the Upi pin setup v2 deeplink
    UpiPinSetupOptionsV2 upi_pin_setup_options_v2 = 338;

    // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%F0%9F%8F%81-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=25703-60367&t=6zhiMDutuIUdVNd9-0
    FallbackPopupScreenOptions fallback_popup_screen_options = 339;

    // screen options for ENTER_REFERRAL_FINITE_CODE_V1_SCREEN
    EnterReferralFiniteCodeV1ScreenOptions enter_referral_finite_code_v1_screen_options = 340;

    // screen options for CREDIT_CARD_LOUNGE_ACCESS_SCREEN
    CreditCardLoungeAccessScreenOptions credit_card_lounge_access_screen_options = 341;

    // screen options for AFFLUENT_USER_BONUS_TRANSITION_SCREEN
    AffluentUserBonusTransitionScreenOptions affluent_user_transition_screen_options = 342;

    // Screen options for FI_LITE_DROP_OFF
    FiLiteDropOffScreenOptions fi_lite_drop_off_screen_options = 343;

    // screen option for REQUEST_SUMMARY_SCREEN
    AlfredRequestSummaryScreenOptions alfred_request_summary_screen_options = 344;

    // screen options for ALFRED_PROVISION_SERVICE_REQUEST
    AlfredProvisionServiceRequestOptions alfred_provision_service_request_options = 345;

    // screen options for ONBOARDING_ADD_FUNDS_V2
    OnboardingAddFundsV2ScreenOptions onboarding_add_funds_v2_screen_options = 346;

    // option for SKIP_ONBOARDING_STAGE_API
    // added as v1 as client does not support unpacking v2 options without nav node
    SkipOnboardingStageApiOption skip_onboarding_stage_api_option = 347;

    // screen options for UPDATE_APP_SCREEN
    UpdateAppScreenOptions update_app_screen_options = 348;

    // screen options for the REFEREE_ACTIONS_INFO_SCREEN
    RefereeActionsInfoScreenOptions referee_actions_info_screen_options = 349;

    PanForAuthScreenOptions pan_for_auth_screen_options = 350;

    CreateAccountScreenOptions create_account_screen_options = 351;

    BeneficiaryActivationPostAuthScreenOptions beneficiary_activation_post_auth_screen_options = 352;

    CreditCardWebEligibilityCheckStatusScreenOptions credit_card_web_eligibility_check_status_screen_options = 353;
  }

  message UpdateAppScreenOptions {
    // deeplink to be visited by the app post the update-app operation is performed
    Deeplink callback_deeplink = 1;

    UpdateType update_type = 2;

    enum UpdateType {
      UPDATE_TYPE_TYPE_UNSPECIFIED = 0;
      // This type of In-app update shows a fuill-screen blocking Ui and prompts user to update the app
      UPDATE_TYPE_IMMEDIATE = 1;
      // This shows a bottom sheet to start app update and users can continue using the app after the update has started downloading
      UPDATE_TYPE_FLEXIBLE = 2;
    }
  }

  message CreditCardLandingScreenOptions {
    api.typesv2.CreditCardRequestHeader credit_card_request_header = 1;
  }

  // Different Screens could take use of different Screen Options for rendering or making corresponding api call
  // devs need to type cast screen_options_v2 using pack/unpack methods that come with Any
  // Screen options are defined inside types/deeplink_screen_options
  // go ref: https://pkg.go.dev/google.golang.org/protobuf/types/known/anypb#pkg-overview
  // java ref: https://protobuf.dev/reference/java/java-generated/#any
  // c# ref: https://protobuf.dev/reference/csharp/api-docs/class/google/protobuf/well-known-types/any.html
  google.protobuf.Any screen_options_v2 = 329;
}

message BeneficiaryActivationPostAuthScreenOptions {
  // unique identifier for the beneficiary activation auth
  string client_request_id = 1;
}

message OnboardingAddFundsV2ScreenOptions {
  // Boolean option to disable showing the skip button on add funds v2 screen
  // Note: if this is false, it takes precedence over the `show_account_balance_check_cta_on_landing_page`
  bool disable_skip_button = 1;
  // boolean to decide whether to show the cta for manually checking/updating of account balance
  bool show_account_balance_check_cta_on_landing_page = 2;
  // string to denote which minor version of the screen is being loaded
  // if this is empty or "ZERO", the base add funds v2 page should be shown
  // string version of the enum present in frontend/pay/add_funds_v2/onboarding/enums.proto
  string minor_version = 3;
  // frontend.pay.transaction.UIEntryPoint.string() value to denote the entry point for add funds screen
  string ui_entry_point = 4;
}

message EnterReferralFiniteCodeV1ScreenOptions {
  // can be used to autofill and proceed if present
  string finite_code = 1;
  // key to be used for accessing finite-code from attribution link params for autofill of finite-code.
  // Note: if field is empty or the corresponding key is not found in attribution params,
  // fallback to `ru` / `referralCode` params.
  string finite_code_from_attribution_params_key = 2;
}

// https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%F0%9F%8F%81-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=25703%3A60367&t=fJUwBetxbsFQWXLR-1
message FallbackPopupScreenOptions {
  api.typesv2.common.Image image = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text subtitle = 3;
  message Instruction {
    api.typesv2.common.Text instruction_header = 1;
    // contains set of instruction that will be displayed as bullet/ unordered list in the body
    repeated api.typesv2.common.Text instructions_list = 2;
  }
  repeated Instruction instructions = 4;
}

// Screen options for SDK login screen when initiating renewal of consent
// Screen - CONSENT_RENEWAL_SDK_LOGIN_SCREEN
message ConsentRenewalSdkLoginScreenOptions {
  api.typesv2.common.Text title = 1;
  enum AaEntity {
    AA_ENTITY_UNSPECIFIED = 0;
    AA_ENTITY_ONE_MONEY = 1;
    AA_ENTITY_FINVU = 2;
  }
  message RenewAccountDetails {
    string account_id = 1;
    string masked_account_number = 2;
    string account_type = 3;
    string account_display_name = 4;
    string fip_logo_url = 5;
    AaEntity aa_entity = 6;
  }
  repeated RenewAccountDetails account_details_list = 2;
  ConnectedAccountsOptions sdk_login_info = 3;
  repeated api.typesv2.common.Text footer_info_list = 4;
}

// confirmation bottom sheet for deposit statement generation before sending the email
// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?node-id=9696-37066&t=4lwvA8sZhwxXqP9J-4
message DepositStatementGenerationScreenOptions {
  // deposit account id for which the statement is to be generated
  string deposit_account_id = 1;
  // e.g: "Deposit statement"
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Image image = 3;
  // e.g: "Your statement includes all transactions and interest earned."
  api.typesv2.common.Text description = 4;
  // will be displayed near the proceed cta
  // e.g: "The statement will be <NAME_EMAIL>"
  api.typesv2.common.Text note_text = 5;
  // on click of this cta, the statement will be generated and sent to the user's email
  // e.g: "Send Email"
  // client calls frontend.deposit.GenerateStatement RPC to generate statement
  frontend.deeplink.Cta proceed_cta = 6;
}

message CreditCardSuccessConfirmationScreen {
  string title = 1;
  InfoItem reward_info = 2;
  api.typesv2.common.Text description = 3;
  Cta cta = 4;
}

message CreditCardWelcomeVouchersScreenOptions {
  InfoBlock voucher_info = 1;
  InfoItem bottom_info_item = 2;
}

message CreditCardIntroRewardEstimationScreenOptions {
  string screen_title = 1;
  string description = 2;
  string subtitle = 3;
  Slider slider = 4;
  message Slider {
    double currently_selected_value = 4;
    double slider_lower_limit = 5;
    double slider_upper_limit = 6;
    double skip_value = 7;
  }
}

message CreditCardIntroScreenBrandScreenOptions {
  InfoBlock reward_descriptions = 1;
  string top_brands_image = 2;
  InfoBlock all_brand_list = 3;
  api.typesv2.common.VisualElement brands_lottie = 4;
}

message RemindersUpdateStateOptions {
  string reminder_subscription_id = 1;
  frontend.budgeting.reminder.meta.ReminderStatus reminder_status = 8;
}



message InAppWaitlistConfirmationScreenOptions {
  string top_image_url = 1;
  string text = 2;
  string sub_text = 3;
  Cta cta = 4;
}

message InAppWaitlistScreenOptions {
  // enum to specify for which feature is the waitlist for .
  // This is being added to enable the client to call the waitlist generic api
  // with the appropriate waitlist feature name
  waitlist.waitlist_enums.WaitlistFeature waitlist_feature = 1;
  // list of image urls which have to be added one on top of another without spacing on the client side .
  // The image breakup should be such that the image quality is not compromised for different devices
  repeated string static_component_image_urls = 2;
  // deeplink pointed to the screen after the waitlist api call has completed with status OK
  Deeplink redirect_action = 3;
  // Cta to call the AddToWaitlist RPC with the appropriate waitlist feature
  Cta cta = 4;
  // can be populated with any urls that are needed to be placed at the bottom of the screen .
  // could be partner url, tnc url, etc.
  string bottom_component_image_url = 5;
}

message PlUpdatedRateScreenOptions {
  InfoItem header_info_item = 1;
  EmiInfo emi_info = 2;
  repeated TermInfo term_infos = 3;
  Cta cta = 4;
  string bottom_text = 5;
  string loan_request_id = 6;
  preapprovedloan.pal_enums.LoanHeader loan_header = 7;
  message EmiInfo {
    InfoItem interest = 1;
    InfoItem tenure = 2;
    InfoItem total_repayment = 3;
    InfoItem emi_amount = 4;
    // APR Rate which has the rate
    // and tooltip details
    InfoItem apr_rate = 5;
  }
  // This will give the terms and condition text
  // Along with deeplink if needed on click of text.
  message TermInfo {
    string term_text = 1;
    Cta term_cta = 2;
    bool is_term_clickable = 3;
    bool is_term_link_underlined = 4;
  }
}

message RemindersSupportedTopicsListScreenOptions {
  repeated frontend.budgeting.reminder.meta.ReminderType reminder_type = 2;
  frontend.budgeting.reminder.meta.SupportedTopicProvenance supported_topic_provenance = 3;
}

message CreateReminderConfig {
  frontend.budgeting.reminder.meta.ReminderType type = 1;
  oneof create_reminder_config_params {
    CategoryReminderConfig category_config = 2;
    AmountReminderConfig amount_config = 3;
    CreditCardDueDateReminderConfig credit_card_due_date_config = 4;
  }
}

message CategoryReminderConfig {
  CategoryDetail category = 1;
  api.typesv2.Money amount = 2;
  frontend.budgeting.reminder.meta.ReminderFrequency frequency = 3;
}

message AmountReminderConfig {
  api.typesv2.Money amount = 1;
  frontend.budgeting.reminder.meta.ReminderFrequency frequency = 2;
}

message CreditCardDueDateReminderConfig {
  int64 configured_date = 1;
}

message CategoryDetail {
  // Display category enum to string field
  string display_category = 1;
}

message UpdateReminderConfig {
  string reminder_subscription_id = 1;
}

message RemindersConfigScreenOptions {
  frontend.budgeting.reminder.meta.ReminderConfigType config_type = 2;
  frontend.budgeting.reminder.meta.ReminderType reminder_type = 3;
  oneof config_flow {
    CreateReminderConfig create_config = 4;
    UpdateReminderConfig update_config = 5;
  }
  string source = 6;
}

// RemindersSubscriptionsListScreenOptions are the screen options that are required for REMINDERS_SUBSCRIPTIONS_LIST screen
message RemindersSubscriptionsListScreenOptions {
  repeated frontend.budgeting.reminder.meta.ReminderStatus reminder_statuses = 1;
}

// Used to present a bottom sheet on stock details screen when buy/sell orders are disabled
// Figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4430%3A19481&t=OfbwOvDvEkCWhSoI-4
message USStocksBuySellClosedScreenOptions {
  api.typesv2.common.ui.widget.ImageTitleSubtitleElement image_title_subtitle = 1;
  deeplink.Cta cta = 2; // primary CTA eg: Ok, got it
  deeplink.Cta secondary_cta = 3; // secondary CTA eg: Get help, learn more etc
}

message WealthOnboardingIAAgreementScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text description = 2;
  // eg: takes less than 2 min
  api.typesv2.common.Text wait_info = 3;
  // icon to be shown to the left of wait info
  api.typesv2.common.Image wait_icon = 4;
  // to show the benefits of accepting IA agreement
  repeated WealthInfoBox benefits = 5;
  // eg: coming soon
  // to be shown at the bottom of the benefits element
  api.typesv2.common.Text benefits_tag = 6;
  // info about where user is being redirected
  // contains an embedded url to the advisory document enclosed in ^^
  api.typesv2.common.Text bottom_text = 7;
  // button which takes the user to web view to sign
  // Cta.weblink to be used to redirect user to the webview
  Cta sign_cta = 8;
  // callback url used by vendor after webview task is done
  string callback_url = 9;
  // 'Skip now' button which comes below sign_cta
  // On clicking skip button, client has to call CollectDataFromCustomer with skip_advisory_agreement_step as true
  // NOTE: if nil, it implies that this button should not be shown on the screen
  api.typesv2.common.Text skip_cta = 10;
  // default fields present in wealth onboarding screen to identify the entrypoints
  DataCollectionFlow flow = 11;
  DataCollectionFlowData flow_data = 12;
  // eg: compliance image
  // figma: https://www.figma.com/design/S2kEkgG06XbwoYzISYfkuC/FFF-%2FWealth-%2FMutual-Funds-%2Fwealthonboarding?node-id=12309-4798&t=vF0JmsMC57y4t3QX-1
  // to be shown at the bottom of the benefits element
  api.typesv2.common.VisualElement compliance_element = 13;
}

message WealthInfoBox {
  // text to be displayed in the box
  api.typesv2.common.Text info = 1;
  // image to be displayed on the left of the text
  api.typesv2.common.Image info_image = 2;
}

message CreditCardBenefitsScreenOptions {
  string header_icon_url = 1;
  repeated InfoItem benefits = 2;
  InfoItemWithCta faq = 3;
}

message CreditCardRewardsPollingScreenOptions {
  // card request id which will be required for redirection to cc
  string card_request_id = 1;
  // id for polling of reward disbursement by rewards service
  string client_request_id = 2;
  // type of offer for which the rewards polling is taking place
  firefly.enums.CCRewardOfferType reward_offer_type = 3;
  // this incremented retry number needed for pulling users out of long polling
  // periods
  int32 retry_attempt_number = 4;
  // message to be displayed as a part of the polling process
  string display_message = 5;
}

message CreditCardRewardsSelectionScreenOptions {
  oneof RewardAttributes {
    CreditCardWelcomeOfferScreenOptions welcome_offer_screen_options = 1;
  }
}

// ref: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17861%3A73641&t=fxqXyghVEAMKGlsf-0
message CreditCardWelcomeOfferScreenOptions {
  string header_text = 1;
  repeated CreditCardRewardAttributes reward_attributes = 2;
  Cta redeem = 3;
  string card_request_id = 4;
  string subtitle = 5;
}

message CreditCardRewardAttributes {
  string reward_id = 1;
  string reward_option_id = 2;
  string icon = 3;
  string bg_color = 4;
  string before_claim_title = 5;
  string after_claim_title = 6;
  message HtmlFormattedDetail {
    string header = 1;
    string body = 2;
  }
  /*
  html formatted details
   for a Gift Hamper Reward, a detail entry can look like
   header : "Description"
   body :"<b>Gift Box Contains :</b> <br>
   1 Toasted Millet Muesli <br>
   1 Choco chip oat clusters and Ragi flakes with banana <br>
   1 Box of 12 Crunchy granola bars - dark chocolate & espresso."
   */
  repeated HtmlFormattedDetail html_formatted_details = 7;
  // banner text displayed on top of the reward option card at the time of choosing the option e.g. "10% EXTRA REWARD"
  // Design : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=5953%3A65408
  // can be empty if banner text does not needs to be displayed.
  string before_claim_banner_text = 8;

  oneof PopUpComponent {
    CreditCardWelcomeVoucherRewardPopUp credit_card_gift_basket_pop_up = 9;
    CreditCardMetalCardRewardPopUp credit_card_metal_card_reward_pop_up = 10;
    CreditCardFiCoinRewardPopUp credit_card_fi_coin_reward_pop_up = 11;
  }
  frontend.firefly.enums.CCRewardType reward_type = 12;
}

message CreditCardFiCoinRewardPopUp {
  InfoItem reward_info = 1;
  string bottom_note = 2;
  Cta confirm_cta = 3;
}

message CreditCardMetalCardRewardPopUp {
  InfoItem pop_up_image = 1;
  string text = 2;
  string sub_text = 3;
  Cta confirm_cta = 4;
}

message CreditCardWelcomeVoucherRewardPopUp {
  string pop_up_image = 1 [deprecated = true];
  InfoBlock vouchers = 2;
  string bottom_note = 3;
  Cta confirm_cta = 4;
  ImageInfo image_info = 5;
  message ImageInfo {
    api.typesv2.common.Text text = 1;
    string icon = 2;
    string bg_color = 3;
  }
}

message CreditCardEmiDashboardScreenOptions {
  InfoItemWithCta emi_calculator = 1;
  api.typesv2.common.Text eligible_transaction_title = 2;
}

message CreditCardEmiLoanAccountDetailsScreenOptions {
  // Loan Id to fetch the loan account details
  string loan_account_id = 1;
}

message CreditCardEmiTransactionLoanOffersScreenOptions {
  // External Transaction id to fetch the loan offers for transaction
  string external_transaction_id = 1;
}

message CreditCardEmiViewAllActiveLoanAccountsScreenOptions {
  InfoItemWithCta closed_loan_accounts = 1;
  api.typesv2.common.Text toolbar_title = 2;
}

message CreditCardEmiViewAllEligibleTransactionsScreenOptions {
  string title = 1;
  Cta confirm_selection_cta = 2;
}

message CreditCardEmiPreviewPreCloseScreenOptions {
  // Loan account id to preview pre close loan
  string loan_account_id = 1;
}

message CreditCardEmiPreCloseScreenOptions {
  // Loan account id to obtain pre close loan summary
  string loan_account_id = 1;

  // deprecated in favour of merchant_info
  string merchant_name = 2 [deprecated = true];
  // deprecated in favour of merchant_info
  string merchant_logo = 3 [deprecated = true];
  // deprecated in favour of merchant_info
  api.typesv2.Money emi_amount = 4 [deprecated = true];
  // deprecated in favour of title_description
  string title = 5 [deprecated = true];
  // deprecated in favour of title_description
  string sub_title = 6 [deprecated = true];
  Cta closure_certificate_cta = 7 [deprecated = true];

  // Title: merchant name
  // Icon: merchant logo
  // SubTitle: transaction amount
  InfoItemV3 merchant_info = 8;
  api.typesv2.common.TitleDescriptionComponent title_description = 9;
  api.typesv2.common.VisualElement ve_bank_info = 10;
}

message UsStocksSOFWaitingScreenOptions {
  USStocksOnboardingScreenOptions.CommonOnboardingUIComponent common_ui = 1;
  // eg. Close cta
  Cta cta = 5;
}

message UsStocksPreRequisiteCheckScreenOptions {
  string img_url = 1;
  // Unlock US stocks by completing a 3-min KYC call
  api.typesv2.common.Text title = 2;
  // Your account needs to a Full KYC account to enable international transfers.
  api.typesv2.common.Text sub_title = 3;
  // Upgrade to Full KYC account & maintain balance of ₹5000 to unlock
  api.typesv2.common.Text highlighted_text = 4;
  // Represent list of CTAs to get help and perform next action to complete pre-requisite check with proper deeplink
  // e.g. finish video call, add funds, get help etc
  // Client should render ctas from left to right.
  repeated Cta ctas = 5;
}

// Validation of user for placing an order is done before moving to Buy/Sell details page, few validation failures are listed below
// 1. SOF document expiry
// 2. limit reached (can be no of orders, daily/monthly/yearly amount limit)
// 3. LRS limit breached
// some cases may require blocking of order creation for a period of time (eg: daily/monthly/yearly limit is breached). here order creation will be unblocked automatically
// whereas some cases may require user intervention to unblock order (eg: SOF document expiry requires permission for account statement)
// USStockValidationErrorScreenOptions is a generic deeplink screen options for validation errors
// It defines all the fields required to be presented to user and action CTAs if required in a bottom sheet
message USStockValidationDialogScreenOptions {
  // image to be displayed
  api.typesv2.common.Image img = 1;
  // title of the error dialog
  // eg: Your source of funds document has expired
  api.typesv2.common.Text title = 2;
  // sub-title of the error dialog
  // eg: Your Source of funds is valid only for 2 months. Please update to re-enable international transfers
  api.typesv2.common.Text sub_title = 3;
  // IconText defines is a basic message for non-clickable display icon text component
  message IconText {
    // Icon to be shown to the left of the text
    api.typesv2.common.Image left_icon = 1;
    // Text to be shown along with icon
    api.typesv2.common.Text text = 2;
    // background color of the container
    string bg_color = 3;
  }
  // tip on any action to be performed for unblocking order creation
  // eg: Takes less than 1 min
  IconText tip = 4;
  // footer note
  // eg: Your data is safe & will be used only to set-up your account
  IconText footer = 5;
  // redirection link is used when user is required to be taken to page where he/she can take required action to unblock order creation
  // eg: Establish SOF permission page
  deeplink.Deeplink redirection_link = 6;
}

message NewForexRateConfirmationDialogScreenOptions {
  // icon to be displayed on the dialog. eg: Exclamation icon
  api.typesv2.common.Image icon = 1;
  // eg: Your USD to INR conversion price got  updated
  api.typesv2.common.Text title = 2;
  // eg: Confirm the new price details and try again.
  api.typesv2.common.Text sub_title = 3;
  // eg: NEW EXCHANGE RATE
  api.typesv2.common.Text new_exchange_rate_title = 4;
  // eg: $1 = ₹80.55
  api.typesv2.common.Text new_exchange_rate_sub_title = 5;
}

message AccountDeletionAcknowledgementScreenOptions {
  string image_url = 1;
  string title = 2;
  string subtitle = 3;
  Cta cta = 4;
  string deletion_reason = 5;
}

message PhysicalCardDispatchSuccessScreenOptions {
  string image_url = 1 [deprecated = true];
  string title = 2 [deprecated = true];
  string desc = 3 [deprecated = true];
  Cta cta = 4;
  Cta view_receipt_cta = 5;
  api.typesv2.common.ui.widget.BackgroundColour background_color = 6;

  // Dark tick icon
  // Eg: "https://epifi-icons.pointz.in/card-images/dark-tick-icon.png"
  api.typesv2.common.VisualElement card_image = 7;

  // Eg: "Card order successful!"
  api.typesv2.common.Text heading = 8;

  // Eg: "Your physical card will arrive within 7-14 days based on your location.   Until then, use your digital card for all online spends."
  api.typesv2.common.Text sub_heading = 9;
}

message UsStocksCollectionScreenOptions {
  string collection_id = 1;
}

message ChequebookOrderDetails {
  // summary block represent the status of the chequebook order
  ui.SectionElement summary_block = 1;

  // shipping details:
  string shipping_details_label = 2;
  string tracking_id_label = 3;
  string tracking_id = 4;
  string tracking_url = 5;
  string courier_label = 6;
  string courier = 7;
  string ticket_id_label = 8;
  string ticket_id = 9;
  string requested_on_label = 10;
  string requested_on = 11;
  string shipping_address_label = 12;
  string shipping_address = 13;
}

message OrderChequebookOptions {
  message BottomUpSheet {
    string title = 1;
    string sub_title = 2;
    Cta cta = 3;
  }
  message PriceDetails {
    string title = 1;
    string sub_title = 2;
    uint32 price = 3 [deprecated = true];
    string charges = 4;
  }
  message AddressDetails {
    string title = 1;
    string sub_title = 2;
    string address = 3;
    string cta_text = 4;
  }
  string illustration_url = 1;
  string title = 2;
  string subtitle = 3;
  // PriceDetails and AddressDetails are displayed as a unit
  PriceDetails price_details = 4;
  AddressDetails address_details = 5;
  string check_box_msg = 6;
  // cta takes user to enter upi pin screen
  string cta_text = 7;
  // bottom up sheet appears on clicking the AddressDetails cta
  BottomUpSheet bottom_up_sheet = 8;

  // alfred request identifier, to be used in order chequebook API
  string request_id = 20;

  // request id to be used in salt for upi credblock generation
  string credblock_request_id = 21;
}

message USStocksCancelOrderScreenOptions {
  string order_id = 1;
  // header text of the bottom sheet
  // eg: Before you go...
  api.typesv2.common.Text header = 2;
  // eg: Are you sure you want to cancel your order?
  api.typesv2.common.Text title = 3;
  // eg: you might miss on returns
  api.typesv2.common.Text sub_title = 4;
  // image properties
  api.typesv2.common.Image img = 5;
  // text to be displayed on cancel CTA
  // eg: Cancel Order
  string cancel_cta_text = 6;
  // text to be displayed on dismiss CTA
  // eg: Not Now
  string dismiss_cta_text = 7;
}

message USStocksAccountActivityScreenOptions {
  // Represent internal identifier for stock
  string stock_id = 1;
}

message TransferInScreenOptions {
  // [Optional] custom amount that will be added to Funds
  api.typesv2.Money custom_amount = 1;
  // Field for UI entry point - api/frontend/pay/transaction/service.proto UIEntryPoint
  string ui_entry_point = 2;
  // [Optional] User identifier of the beneficiary account(since user can have multiple accounts)
  // If empty, identifier from GetUserSessionDetails response will be used.
  // Client can also follow a priority where REGULAR > NRO > NRE for adding funds.
  api.typesv2.pay.user_identifier.UserIdentifier payee_user_identifier = 3;
}

message USStocksSymbolDetailsScreenOptions {
  // Represent internal identifier for stock
  string stock_id = 1;
  // default selected tab type, If unspecified should redirect to first tab
  frontend.usstocks.clientstate.SymbolDetailsTabType selected_tab_type = 2;
  // flag to determine if on press of back go to us stocks landing page or do just go back to previous screen
  // false: go back to previous screen
  // true: show us stocks landing page on pressing back
  bool show_usstocks_landing_on_back_navigation = 3;
}

message StatusPollScreenOptions {
  // polling target tells client which api to poll
  definitions.PollingTarget polling_target = 1;
  // placeholder illustration on the poll screen
  string img_url = 2;
  // primary placeholder text on the poll screen
  string title = 3;
  // secondary placeholder text on the poll screen
  string subtitle = 4;
  // some status polling screen have cta and we don't want loader on those screen
  bool skipLoader = 5;
  // some polling screen might take some time to fetch final status,
  // We want to give the user the option to move to some other screen
  repeated Cta ctas = 6;
  // show timer on polling screen, if zero client is expected to hide element
  int32 timer = 7;
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 8;
  // payload options to be used in the polling target api
  oneof request_payload_options {
    definitions.AlfredRequestPayloadOptions alfred_request_payload_options = 100;
    definitions.EPANStatusPayloadOptions epan_status_payload_options = 101;
    // tied with polling_target as POLLING_VKYC_NEXT_ACTION_API
    GetVKYCNextActionApiScreenOptions get_vkyc_next_action_api = 102;
    definitions.KYCStatusPayloadOptions k_y_c_status_payload_options = 103;
    definitions.GenerateBKYCOTPPayloadOptions generate_b_k_y_c_otp_payload_options = 104;
  }
}

message CreditCardFetchLimitsOptions {
  string credit_card_id = 1;
}

message CreditCardSetLimitsOptions {
  string title = 1;
  string subtitle = 2;
  api.typesv2.Money daily_limit_value = 3;
  api.typesv2.Money min_limit_value = 4;
  api.typesv2.Money max_limit_value = 5;
  string credit_card_id = 6;
  frontend.firefly.enums.CardControlType card_control_type = 7;
  bool is_device_unlock_required = 8;
}

message CreditCardVerifyQRCodeOptions {
  string credit_card_id = 1;
  string card_request_id = 2;
}

message CreditCardSetCardPinOptions {
  string credit_card_id = 1;
  string card_request_id = 3;
  string image_url = 4;
  string title = 5;
  string description = 6;
}

message CardControl {
  string title = 1;
  string subtitle = 2;
  frontend.firefly.enums.CardControlType card_control_type = 3;
  bool is_active = 4;
  bool is_device_unlock_required = 5;
}

message CreditCardSetCardPreferencesOptions {
  string credit_card_id = 1;
  string title = 2;
  string subtitle = 3;
  repeated CardControl card_controls = 4;
  string card_request_id = 5;
  api.typesv2.common.VisualElement card_image_visual_element = 6;
}

message CreditCardUsageScreenOptions {
  string credit_card_id = 1;
}

message CreditCardControlsScreenOptions {
  string credit_card_id = 1;
}

message CreditCardSuccessBottomViewScreenOptions {
  string credit_card_id = 1;
  InfoItemWithCta info_item_with_cta = 2;
  string bg_color = 3;
}

message EditProfileInfoScreenOptions {
  string icon_url = 1;
  string title = 2;
  string subtitle = 3;
  // for ui box to contain highlighted message like current value or updated value
  // SectionElement element_title field to be used for labeling
  // SectionElement element_id field to be used as key for finding info to be shown
  ui.SectionElement element = 4;
  repeated Cta ctas = 5;
  string check_box_text = 6;
}

message USStocksOnboardingDocumentPollingScreenOption {
  // if polling option is nil client do not need to poll
  repeated document_upload.polling.DocumentPollingOption polling_options = 1;
}


// Represent Order Status Screen Data for USStocks
message USStocksOrderStatusScreenOptions {
  string img_url = 1;

  // Eg: Order received
  api.typesv2.common.Text title_text = 2;

  // Eg: Your order is successfully sent to our partner & it will be executed when market opens.
  api.typesv2.common.Text subtitle_text = 3;


  // Represent info about order
  // Infoitem.title represent order status text
  // Eg: Sell in process
  // Infoitem.desc represent ETA text
  // Eg: ETA 03 Oct
  // [Deprecated] use OrderStatusInfoItem instead
  InfoItem info_item = 4 [deprecated = true];

  // Eg: Your money will reach your Fi Account within 2 business days after order is placed in the market.
  api.typesv2.common.Text info_text = 5;

  // Represent background image in status screen
  string background_img_url = 6;

  // CTA to exit user from current flow
  Cta done_cta = 7;

  // OrderStatusInfoItem is used on order success state to show current status of order
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=6744%3A22219&t=mcqGLNVW7dpY0Xtb-4
  // It is a clickable component used to redirect user to order details page
  message OrderStatusInfoItem {
    // eg: Buying in process
    api.typesv2.common.Text title = 1;
    // eg: ETA 03 Oct
    api.typesv2.common.Text sub_title = 2;
    // clipboard icon
    api.typesv2.common.Image img = 3;
    // deeplink to redirect user to order details page
    Deeplink deeplink = 4;
  }

  // Provides info about current status of order, redirects to order details page on click
  OrderStatusInfoItem status_info = 8;

  // footer text for us stocks order status screen
  // eg: After order placement, the money will reach your Federal Bank account in 2 business days
  api.typesv2.common.Text footer = 9;

  // flag to show confetti on success
  bool show_confetti = 10;
}

message USStocksBuyOrderScreenOptions {
  // Represent internal identifier for stock
  string stock_id = 1;
}

message USStocksSellOrderScreenOptions {
  // Represent internal identifier for stock
  string stock_id = 1;
}

message USStocksOrderReceiptScreenOptions {
  string order_id = 1 [deprecated = true];
  oneof identifier {
    // orderId for Buying/Selling of stocks
    string stock_order_id = 2;
    // Add/Withdraw funds order id.
    string wallet_order_id = 3;
    string account_activity_id = 4;
  }
}

// screen options for GENERIC_RECORD_CONSENT
message GenericRecordConsentOptions {
  message ContentObject {
    string content_text = 1;
    string icon_url = 2;
  }

  string title = 1 [deprecated = true];
  string subtitle = 2 [deprecated = true];
  repeated ContentObject content_list = 3 [deprecated = true];


  message ConsentData {
    repeated ContentObject content_list = 100;
    string title = 101;
    string subtitle = 102;
    string lang_selected_text = 103;
    string lang_unselected_text = 104;
    Cta cta = 105;
  }

  // consent_data is being sent as a map of language to ContentList
  map<string, ConsentData> consent_data = 6;

  repeated string consents = 4;

  string title_image_url = 5;

  int32 consent_version = 7;
}

//Represent ScreenOption for Usstock onboarding
message USStocksOnboardingScreenOptions {

  // Represent common UI elements
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3552&t=S8wtzVQHLKLwaLg5-4
  message CommonOnboardingUIComponent {
    string img_url = 1;
    api.typesv2.common.Text title = 2;
    api.typesv2.common.Text sub_title = 3;
    api.typesv2.common.Image privacy_image = 4;
    api.typesv2.common.Text privacy_text = 5;
  }

  // provide the screen data with respect to each onboarding step
  message NextStepOnboardingData {
    oneof onboarding_screen_data {

      // For showing already available documents of user that will be re-used for onboarding
      // e.g. Fi partner bank statement as proof of address, PAN card image as proof of identity
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3551&t=S8wtzVQHLKLwaLg5-4
      CollectAddressIdentityProof address_identity_proof = 1;

      // For user to disclose if they are politically associated, senior executive of publicly traded company, etc.
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2341&t=S8wtzVQHLKLwaLg5-4
      CollectDisclosureApproval disclosure_approval = 2;

      // Optional step: user can disclose their workplace, role and company address, etc. so that Fi can customize their experience
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2727&t=S8wtzVQHLKLwaLg5-4
      CollectEmployerData employer_data = 3;

      // Terms and conditions for opening account with stock broker, e.g. non-US citizen, etc.
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3222&t=S8wtzVQHLKLwaLg5-4
      CollectDisclaimerApproval disclaimer_approval = 4;

      // For collecting source of funds, if user's Fi account is less than 1 year old
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2789&t=S8wtzVQHLKLwaLg5-4
      CollectAccountInfo account_info = 5;

      // Waiting screen to show when we are processing opening a user's account
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2401&t=S8wtzVQHLKLwaLg5-4
      NotifyUSStocksReadyInfo notify_us_stocks_ready_info = 6;

      // Success screen when user is onboarded and should be able to buy and sell stocks
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=2972%3A10929&t=S8wtzVQHLKLwaLg5-4
      CollectOnboardingSuccessInfo onboarding_success_info = 7;

      // Optional step: To collect user's planned investment amount for a year, so that Fi can customize their experience
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=861%3A3894&t=S8wtzVQHLKLwaLg5-4
      CollectInvestmentRange investment_range = 8;

      // Collecting the PAN card from user
      // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=335%3A5524&t=S8wtzVQHLKLwaLg5-4
      CollectPANCard collect_pan_card = 9;

      // polling/success screen where user can poll for document generation and success screen for document generation
      PollingScreenData polling_screen_data = 10;
    }
  }

  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4426%3A19164&t=UI9hshKaPpsmwltI-4
  message PollingScreenData {
    // URL string for image to be shown on top
    api.typesv2.common.Image image = 1;

    // e.g. "Well done! Your account is getting ready"
    api.typesv2.common.Text title = 2;

    // e.g. "We'll notify you when your account is ready. Meanwhile, check few important things to know."
    api.typesv2.common.Text description = 3;

    // Takes less than 5 mins
    // Upto 3-4 days
    api.typesv2.common.Text wait_info = 4;

    api.typesv2.common.Image wait_icon = 5;

    Cta primary_cta = 6;

    // e.g. : Learn about US stocks
    // if text is present, client should use UIContext.UI_CONTEXT_US_STOCKS_LANDING_PAGE_STORIES
    // for fetching the stories
    api.typesv2.common.Text learn_text = 7;

    // if polling option is nil client do not need to poll
    repeated document_upload.polling.DocumentPollingOption polling_options = 8;
  }

  message CollectPANCard {
    CommonOnboardingUIComponent common_ui = 1;

    // Error message shown to user when uploading image from device storage fails
    // e.g. Uh oh! There’s an issue with your image. Please try again
    api.typesv2.common.Text upload_error = 2;

    // Icon shown to user when uploading image from device storage fails
    // e.g. danger icon, etc.
    string upload_error_icon_url = 3;

    // Component to navigate user in to explain EPAN upload via story
    EPANStoryRedirectionComponent epan_redirection_component = 4;
  }

  // Used to fetch UI Data for Investment range collection screen
  // Such as Title,img,text for range of investment..etc
  // this screen capture the investment behaviour of user
  message CollectInvestmentRange {
    CommonOnboardingUIComponent common_ui = 1;

    // Represent options investment bracket text and
    // unique identifier for each option
    repeated ConsentItem investment_range_consents = 2;

    // CTA to submit form and continue onboarding
    Cta proceed_cta = 3;
  }

  // Represent static data for AddressIdentityProof screen
  message CollectAddressIdentityProof {
    CommonOnboardingUIComponent common_ui = 1;

    // contains the list of documents that are to be fetched for onboarding user
    OnBoardingDocumentInfo document_info = 2;

    // Represent static data for  footer checkbox
    ConsentItem remark_consent_item = 3;

    // contains information on documents to poll and settings for polling
    // note: all documents needs to be polled in parallel
    repeated document_upload.polling.DocumentPollingOption polling_options = 4;

    // CTA to submit form and continue onboarding
    Cta proceed_cta = 5;
  }

  // Represent static data for Disclosure Screen
  message CollectDisclosureApproval {
    CommonOnboardingUIComponent common_ui = 1;
    repeated ConsentItem list_consent_items = 2;
    // Represent static data for  footer checkbox
    ConsentItem remark_consent_item = 3;

    // CTA to submit form and continue onboarding
    Cta proceed_cta = 4;

    // Repeated Conset Items with sub text for new combined items
    repeated ConsentItemWithSubtext consent_items = 5;
  }

  message EmployerInfo {
    string employer_name = 1;
    string employer_address = 2;
    string role = 3;
  }

  // Represent static data in employment details Screen
  message CollectEmployerData {
    CommonOnboardingUIComponent common_ui = 1;
    EmployerInfo employer_info = 2;

    // CTA to skip submitting user inputs in form and continue onboarding
    Cta skip_cta = 3;

    // CTA to submit form and continue onboarding
    Cta proceed_cta = 4;
  }

  // Represent category of disclaimer
  message DisclaimerItem {
    string id = 1;
    api.typesv2.common.Text title = 2;
    repeated ConsentItem list_consent_items = 3 [deprecated = true];
    repeated HyperlinkConsentItem hyperlink_consent_items = 4;
  }

  // Represent Disclaimer screen data
  message CollectDisclaimerApproval {
    CommonOnboardingUIComponent common_ui = 1;
    repeated DisclaimerItem list_disclaimer_items = 2;

    // CTA to submit form and continue onboarding
    Cta proceed_cta = 3;
  }

  message CollectAccountInfo {
    CommonOnboardingUIComponent common_ui = 1;
    ConnectedAccountInfo connected_account_info = 2;
    ConsentItem download_statement_consent_item = 3;
    message CtaRedirectionInfo {
      enum RedirectionType {
        // Not expected. Client to show error screen in this case
        REDIRECTION_TYPE_UNSPECIFIED = 0;
        // Client to redirect to Connected account sdk flow for this
        ADD_CONNECTED_ACCOUNT = 1;
        /*
          Client to use the 'document_upload.DocumentPollingOption' to call
          'PollDocumentGeneration' and 'UploadDocument'.
          Client has to do this sequence on same screen.
          Refer
          Figma SS: https://drive.google.com/file/d/1YjP84JuvYRzKzlldbJefwIssspvtbq0-/view?usp=share_link
          Figma : https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4801%3A22963&t=TIAnviLoA2BXKcOI-4
        */
        UPLOAD_CONNECTED_ACCOUNT_STATEMENT_SAME_SCREEN = 2;
      }
      RedirectionType redirection_type = 1;
      /*
        For
        - 'ADD_CONNECTED_ACCOUNT' deeplink would be to redirect to Connected Account SDK flow
        - 'UPLOAD_CONNECTED_ACCOUNT_STATEMENT_SAME_SCREEN' deeplink would be the screen to go to after successful upload.
           For server failures in uploading or polling api, this has to be ignored.
       */
      frontend.deeplink.Deeplink next_screen = 2;
      /*
        Present only for 'UPLOAD_CONNECTED_ACCOUNT_STATEMENT_SAME_SCREEN'
       */
      document_upload.polling.DocumentPollingOption documentPollingOption = 3;
      /*
        Present only for 'ADD_CONNECTED_ACCOUNT'
        Tells the client which screen to redirect to post SDK flow completion for
        Connected Account
      */
      frontend.deeplink.Deeplink post_add_account_screen = 4;
    }
    CtaRedirectionInfo cta_redirection_info = 4;

    // CTA to submit form and continue onboarding
    Cta proceed_cta = 5;
  }

  message ConnectedAccountInfo {
    // Represent masked account number
    string mask_account_id = 1;
    // Represent financial institute logo
    string fip_logo_url = 2;
    // Represent financial institute name
    string fip_name = 3;
    // Represent data in section
    // subtitle represent corner element of component
    // title represent title of component
    CommonOnboardingUIComponent common_ui = 4;
    // this is not for ui purpose when user will click on submit button then pass this id in connectedAccountPayload
    string account_reference_id = 5;
    // eg: Account must be minimum 1 year old
    api.typesv2.common.Text bottom_text = 6;
    // URL string representing an image of entire bottom-right compliance item - "Powered by epifi Wealth"
    string powered_by_img_url = 7;
  }

  message NotifyUSStocksReadyInfo {
    CommonOnboardingUIComponent common_ui = 1;

    // CTA to stop onboarding the user as US stocks is not ready for them
    Cta done_cta = 2;
  }

  message CollectOnboardingSuccessInfo {
    CommonOnboardingUIComponent common_ui = 1;
  }

  message OnBoardingDocumentInfo {
    // ex: Available documents from user's accounts with Fi
    api.typesv2.common.Text title = 1;
    repeated DocumentItemInfo list_document_info = 2;
    // this component is used for displaying data on (i) button data on the screen
    InformationPopupOptions info_popup_options = 3;
  }

  // Represent auto fetch document
  message DocumentItemInfo {
    // id is used for identify which document is consent
    string id = 1;
    string icon_url = 2;
    api.typesv2.common.Text document_name_text = 3;
    string document_download_url = 4;
  }

  // it represent consent data
  // id is used for uniquely identify consent
  // example if user click on i am politician
  // so client might send id correspond to it
  message ConsentItem {
    string id = 1;
    api.typesv2.common.Text consent_text = 2;
    // represent if the checkbox check or not
    bool is_checked = 3;
  }

  // it represent consent data which has text and subtext
  // e.ge text: Low risk, subtext: I don't want any investment loss but expect a minimum profit of up to 5%
  // id is used for uniquely identify consent
  // example if user click on i am politician
  // so client might send id correspond to it
  message ConsentItemWithSubtext {
    string id = 1;
    api.typesv2.common.Text consent_text = 2;
    // represent if the checkbox check or not
    bool is_checked = 3;
    api.typesv2.common.Text consent_subtext = 4;
  }

  message HyperlinkConsentItem {
    string id = 1;
    // consent text with hyperlink
    TextWithHyperlinks consent_text = 2;
    // represent if the checkbox check or not
    bool is_checked = 3;
  }

  frontend.usstocks.clientstate.OnboardingStepType onboarding_step = 1;
  NextStepOnboardingData onboarding_screen_data = 2;
  api.typesv2.deeplink_screen_option.usstocks.metadata.USStocksScreenMetadata us_stocks_screen_metadata = 3;
}

// TextWithHyperlinks provide the sub text that need to be treated as hyper link
// Where the hyperlink_map key act as sub text from main text that require click behaviour
// and hyperlink_map value provide the info such deeplink for click
// eg: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=9675%3A28742&t=OUwONLcRXzjWSJ4w-4
// in above figma link terms and remittance are the sub text that require custom hyper links behaviour
message TextWithHyperlinks {
  api.typesv2.common.Text text = 1;

  // Key will represent the sub text that will be treated as underlined hyper link.
  // Value will represent hyper link proto, providing the url extra data.
  map<string, Deeplink> hyperlink_map = 9;
}

// this provides info for a clickable item on app.
message Cta {
  enum Type {
    // No action can be taken on the event.
    CTA_UNSPECIFIED = 0;

    // Retry let's the user retry a failed timeline event.
    // e.g. user can retry a failed payment.
    RETRY = 1;

    // Contact Customer Support CTA
    CUSTOMER_SUPPORT = 2 [deprecated = true];

    // follows deeplink action
    CUSTOM = 3;

    // Done lets the user to exit the current screen and move to the next screen.
    DONE = 4;

    // Logout CTA logs out the user and takes to sign in/sign up screen
    LOGOUT = 5;

    // Feedback CTA is an option for the user to provide feedback
    FEEDBACK = 6;

    // CHAT CTA is an option for the user to chat with customer support agent
    CX_CHAT = 7 [deprecated = true];

    // CALL CTA is for the user to call the customer support agent
    CX_CALL = 8 [deprecated = true];

    // CONTINUE CTA lets the user continue with the current process
    CONTINUE = 9;

    // Cancels or Dismisses the hosted screen. Useful for dismissing dialogs/bottom-sheets, without any further action,
    // e.g from the Top bar cross button
    CANCEL = 10;
  }

  // Type of CTA
  Type type = 3;

  // clickable item text
  string text = 1 [deprecated = true];

  // deeplink for click
  Deeplink deeplink = 2;

  enum DisplayTheme {
    // unspecified DisplayTheme
    DISPLAY_TYPE_UNSPECIFIED = 0;
    // PRIMARY is a CTA of green colour button that has more emphasis,
    PRIMARY = 1;
    // next comes SECONDARY CTA which is grey in colour or handled by client
    SECONDARY = 2;
    // TERTIARY CTA has the least emphasis
    TERTIARY = 3;
    // next button is disabled for a few seconds, so the user can read the info on the screen
    DISPLAY_THEME_DELAY_ENABLE = 4;
    // Plain text CTA
    TEXT = 5;
    // Use this to have custom support for cta
    CUSTOM_THEME = 6;
  }

  //DisplayType represents how the CTA is to be displayed
  DisplayTheme display_theme = 4;

  // used to add status on cta like whether button is enabled or disabled
  enum CtaStatus {
    // keeping default behaviour enabled
    CTA_STATUS_ENABLED = 0;
    CTA_STATUS_DISABLED = 1;
  }
  // to specify whether button or cta is enabled or disabled
  CtaStatus status = 5;

  // takes user to a website or in-app web-view
  string weblink = 6;

  // to specify time(in second) after which cta will be shown up to user
  // default will be 0 second, cta shown up instantly
  int32 visible_after_seconds = 7;

  // this url is used for displaying icon with cta text. if empty, don't show icon image.
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=12314%3A33052&t=T5nsJgOjzKlSxOOT-0
  string icon_url = 8;

  // id can be used to trigger events on client to track why cta was clicked
  string id = 9;

  // use this if you want to show any leading image in cta .
  // BE should pass the gap between the leading image and text
  api.typesv2.common.VisualElement cta_leading_image = 10;

  // use this if you want to show any trailing image in cta .
  // BE should pass the gap between the trailing image and text
  api.typesv2.common.VisualElement cta_trailing_image = 11;

  /// Padding for the gap between the left image and cta text
  /// Can be picked directly from the figma
  int32 leftImgTxtPadding = 12;

  /// Padding for the gap between the right image and cta text
  /// Can be picked directly from the figma
  int32 rightImgTxtPadding = 13;

  // Apply this only if display theme is custom
  CustomStyles cta_style = 14;
  // client should prioritise this over normal text
  api.typesv2.common.Text cta_text = 15;

  message CustomStyles {
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
    api.typesv2.common.ui.widget.BackgroundColour border_color = 2;
  }
}

// SampleScreenOptions - for testing purpose only. Maps to no Screen.
message SampleScreenOptions {
  string field1 = 1;
  int32 field2 = 2;
  google.type.Date field3 = 3;
}

message AlfredProvisionServiceRequestOptions {
  string request_type = 1;
}

message GetOffersScreenOptions {
  string card_image_url = 1;
  InfoBlock info_block = 2 [deprecated = true];
  Cta know_more = 3 [deprecated = true];
  string tnc_url = 4;
  string tnc_string = 5;
  string partnership_url = 6;
  Cta get_credit_card = 7;
  string title = 8;
  api.typesv2.common.Name customer_name = 9;
  InfoItemWithCta pop_up_text_block = 10 [deprecated = true];
  string credit_limit_str = 11;
  InfoItemWithCta brands = 12 [deprecated = true];
  InfoItemWithCta valueback_cheatsheet = 13 [deprecated = true];
  InfoItemWithCta welcome_vouchers = 14 [deprecated = true];
  repeated InfoItem static_images = 15;
  string fees_info_header = 16;
  message InfoItemWithBadgeInfoAndCta {
    InfoItemWithCta info = 17;
    api.typesv2.common.Text badge_text = 18;
  }
  repeated InfoItemWithBadgeInfoAndCta fees_info = 19 [deprecated = true];
  string header_description = 20;
  InfoItemWithCtaV2 rewards_worth = 21;
  api.typesv2.common.VisualElement broad_visual_element = 22;
  repeated InfoItemWithCtaV2WithInfoItemV2AndBadge fee_info_v2 = 23;
  Cta all_fee_cta = 24;
  message AcceleratedRewardInfo {
    api.typesv2.common.Text accelerated_reward_title = 25;
    InfoItemWithCtaV2 accelerated_reward_v2 = 26;
  }
  AcceleratedRewardInfo accelerated_reward_info = 27;
  api.typesv2.common.Text fi_collection_title = 28;
  InfoItemWithCtaV2 fi_collection_card = 29;
}

message InfoItemWithCtaV2WithInfoItemV2AndBadge {
  InfoItemV2 info = 1;
  api.typesv2.common.Text badge_info = 2;
  InfoItemWithCtaV2 info_with_cta = 3;
}
message FireflyGetRequestStatusScreenOptions {
  string card_request_id = 1;
  string display_message = 2;
  int32 retry_delay = 3;
  int32 retry_attempt_number = 4;
}

message FireflyGetInfoScreenOptions {
  string image_url = 1;
  string title = 2;
  repeated InfoBlock qna_block = 3;
}


message FireflyCardActivationScreenOptions {
  // deprecated in favour of card_image
  string card_image_url = 1 [deprecated = true];
  // deprecated in favour of heading
  string text = 2 [deprecated = true];
  // deprecated in favour of sub_heading
  string sub_text = 3 [deprecated = true];
  // deprecated in favour of card_info_items
  InfoBlock bill_dates = 7 [deprecated = true];
  // deprecating the existing fields with string as the new elements added gives more control over the
  // text and image attributes
  api.typesv2.common.VisualElement card_image = 9;
  api.typesv2.common.Text heading = 10;
  api.typesv2.common.Text sub_heading = 11;
  repeated deeplink.InfoItemV2 card_info_items = 12;
  string tnc = 4;
  Cta activate_digital_card_cta = 5;
  string card_request_id = 6;
  api.typesv2.common.Name customer_name = 8;
  int64 default_bill_gen_date = 13;
  int64 default_payment_due_date = 14;
  api.typesv2.common.VisualElement toolbar_visual_element = 15;
  api.typesv2.common.VisualElement ve_bank_logo = 16;
}

message ScreenInstructions {
  string text = 1;
  string image_url = 2;
}

message OnlineCardPaymentsEnablementScreenOptions {
  string title = 1;
  string image_url = 2;
  string desc = 3;
  Cta enable_online_payments = 4;
  string card_request_id = 5;
}

enum AddressFlow {
  ADDRESS_FLOW_UNSPECIFIED = 0;
  ADDRESS_FLOW_CREDIT_CARD = 1;
}

message FireflySetDeliveryAddressScreenOptions {
  string text = 1;
  string sub_text = 2;
  StepInfo step_info = 3;
  AddressFlow address_flow = 4;
  string delivery_address_icon_url = 5;
  Cta continue_cta = 6;
  string card_request_id = 7;
  api.typesv2.common.Name customer_name = 8;
  string footer_text = 9;
  bool is_add_address_enabled = 10;
}


message FireflyCardLivenessManualReviewScreenOptions {
  string manual_review_img_url = 1;
  string title_text = 2;
  string body_text = 3;
  string card_request_id = 4;
  InfoBlock offers = 5;
}

message AuthStatusPollScreenOptions {
  string client_request_id = 1;
  string display_message = 2;
  int32 retry_delay = 3;
  int32 retry_attempt_number = 4;

  api.typesv2.common.Text title = 5;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 6;
}

message AuthNpciPinValidationOptions {
  string client_request_id = 1;
  string txn_id = 2;
}

message AuthSmsOtpVerificationOptions {
  string client_request_id = 1;
  string token = 2;
}

message AuthLivenessSummaryStatusPollScreenOptions {
  // client request id of auth request
  string client_request_id = 1;
  LivenessFlow liveness_flow = 3;
  api.typesv2.common.Text loader_message = 4;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 5;
}

// RegisterCKYCScreenOptions keeps the options that can be used in
// REGISTER_CKYC page on client.
message RegisterCKYCScreenOptions {
  string pan = 1;
  google.type.Date dob = 2;
  string feedback_screen = 3;
  // widget to be shown at the top (top section of the screen) to highlight the applicable offer for the user,
  // for e.g. "<icon> Offer applied! Get flat ₹100 when you sign up".
  // Note: can be nil
  IconTextWidget reward_offer_widget = 4;
  FiLiteDropOffScreenOptions fi_lite_drop_off_screen_options = 5;
  string title = 6;
  string subtitle = 7;
  deeplink.Cta cta = 8;
  // flag to control pan and dob value modification, if flag is unspecified then make the placeholders editable
  api.typesv2.common.BooleanEnum is_editable = 9;
  repeated Consent consents = 10;
  // client takes first priority for using ExitRedirection and then FiLiteDropOffScreenOptions
  // deprecated in favour of back_action_v2
  deeplink.Deeplink back_action = 11 [deprecated = true];

  string entry_point = 12;
  // Blob containing the deeplink which needs to be sent to SavePanAndDobForFiLite RPC which whill return a next action upon success
  bytes blob = 13;

  BackAction back_action_v2 = 14;

  // third party vendor logo to be shown below the subtitle in case we are fetching credit report
  // through an external vendor along with pan dob collection
  api.typesv2.common.VisualElement credit_report_logo = 15;
}

message Consent {
  // hyperlink to any documents for a consent
  string url = 1;
  // text to show to user for consent
  // deprecated in favour of text_v2
  string text = 2 [deprecated = true];
  // value to be sent to BE when recording consent
  string consent = 3;

  // We show information about each consent in a dialog popup
  // Dialog popup shows up whenever user clicks on a consent
  // ConsentInfo contains information about each instruction in these dialog popup
  message ConsentInfo {
    // deprecated in favour of title_v2
    string title = 1 [deprecated = true];
    // deprecated in favour of description_v2
    string description = 2 [deprecated = true];

    api.typesv2.common.Text title_v2 = 3;
    api.typesv2.common.Text description_v2 = 4;
  }
  repeated ConsentInfo consent_infos = 4;

  api.typesv2.common.Text text_v2 = 5;
}

// Bottom sheet for Fi Lite drop-offs during onboarding
// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=10-2000&t=j8U1Tmxce4gmo0KH-4
message FiLiteDropOffScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text description = 2;
  api.typesv2.common.Text benefit_title = 3;

  repeated api.typesv2.common.ui.widget.ImageTitleSubtitleElement benefits_list = 4;

  api.typesv2.common.Text cta_text = 5;
  api.typesv2.common.Text bottom_text = 6;

  // Source ENUM for drop off reason
  string source = 7;
}

message CaAccountActionsScreenOptions {
  // title, description, cta for bottom sheet
  string title = 1;
  string description = 2;
  Cta cta = 3;
}

enum ErrorLivenessFailure {
  ERROR_LIVENESS_FAILURE_UNSPECIFIED = 0;
  ERROR_LIVENESS_FAILURE_OTP = 1;
  ERROR_LIVENESS_FAILURE_FACE_NOT_DETECTED = 2;
  ERROR_LIVENESS_CHANGE_BACKGROUND = 3;
  ERROR_LIVENESS_FAILURE_MULTIPLE_FACES_DETECTED = 4;
  ERROR_LIVENESS_FAILURE_FACE_POORLY_DETECTED = 5;
  ERROR_LIVENESS_FAILURE_FACE_TOO_FAR = 6;
  ERROR_LIVENESS_FAILURE_FACE_TOO_CLOSE = 7;
  ERROR_LIVENESS_FAILURE_FACE_TOO_DARK = 8;
  ERROR_LIVENESS_FAILURE_FACE_TOO_BRIGHT = 9;
  ERROR_LIVENESS_FAILURE_NO_FACE_DETECTED = 10;
}

// Enum defining various type of flows for CHECK_LIVENESS consent screen on client.
// Primarily used to set UI.
enum LivenessFlow {
  LIVENESS_FLOW_UNSPECIFIED = 0;
  ONBOARDING = 1;
  SET_PIN = 2;
  RESET_PIN = 3;
  AUTH_FACTOR_UPDATE = 4;
  WEALTH_ONBOARDING = 5;
  PRE_APPROVED_LOAN = 6;
  CREDIT_CARD = 7;
  DEVICE_BIOMETRIC = 8;
  BENEFICIARY_ACTIVATION = 9;
  NON_RESIDENT_ONBOARDING = 10;
}

// CheckLivenessScreenOptions keeps the options that can be used in
// CHECK_LIVENESS page on client.
message CheckLivenessScreenOptions {
  string attempt_id = 1;
  string otp = 2;
  // Error of last liveness failure
  ErrorLivenessFailure error_last_liveness_failure = 4;

  // action to perform after video is streamed to the server.
  Deeplink next_action = 3;

  // Used to control the UI on consent screen for CHECK_LIVENESS page on client.
  LivenessFlow liveness_flow = 5;

  // flag to turn off face tracking library on android client.
  // once a user has tried and generated liveness more than 3 times,
  // we will disable face tracking on android as per feedback of issues on some devices.
  // [Important Note]: This flag is deprecated and has no effect on the client side when [use_new_liveness_flow] is enabled.
  bool disable_face_tracking_android = 6 [deprecated = true];

  StepInfo step_info = 7;

  api.typesv2.common.VisualElement image = 8;
  api.typesv2.common.Text title = 9;
  api.typesv2.common.Text subtitle = 10;
  repeated api.typesv2.common.Text list_items = 11;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 12;
  // flag to control if VPN check needs to be enabled by android
  bool enable_vpn_check = 13;
  // Flag to control whether the new liveness flow should be used, which includes the CameraX library migration and the removal of hardcoded polling logic.
  // When this flag is enabled, [next_action] becomes mandatory, and the client will navigate to [next_action] after a successful CheckLiveness RPC.
  // Note: This flag applies only to Android.
  bool use_new_liveness_flow = 14;
}

message StepInfo {
  int64 current_step = 1;
  int64 total_steps = 2;
}

// HomeScreenOptions keeps the option that can be used in HOME page on client
message HomeScreenOptions {
  // flag to notify that navigation is from onboarding Add funds
  bool isFromOnboardingAddFunds = 1;
}

//timeline page deeplink
// screen : `TIMELINE`
// options: timeline_id : id to fetch timeline between 2 actors
message TimelineScreenOptions {
  string timeline_id = 1;
  // pi id of payee, to which payment needs to be done
  string payee_pi_id = 2;
}

// screen options for AFU_CONFIRM_START_POPUP
message AFUConfirmStartOptions {
  // PopUpType defines the types of dialog boxes supported by the client.
  // They're different UI components that do not occupy full screen.
  enum PopUpType {
    POP_UP_TYPE_UNSPECIFIED = 0;

    // This is shown when there is a mismatch in email with phone
    // number & device ID. It confirms with the users if they want to
    // update the email or cancel. Confirmation triggers Auth factor
    // update flow.
    EMAIL_UPDATE_CONFIRMATION = 1;

    // email & device ID. It confirms with the users if they want to
    // update the email or cancel. Confirmation triggers Auth factor
    // update flow.
    PHONE_UPDATE_CONFIRMATION = 2;
  }

  PopUpType pop_up_type = 1;
}

// screen options for CONSENT screen
message ConsentScreenOptions {
  message CheckboxConsent {
    // deprecated in favour of consent_types
    string consent_type = 1 [deprecated = true];

    // Backend identifier for the consent type. this should be sent in consent.RecordConsent RPC.
    // It maps to consent.ConsentType in api/consent/consent_type.proto
    repeated string consent_types = 3;

    // text to be shown besides the checkbox
    string text = 2;
  }

  // Consent page title
  string title = 2;
  // Consent page subtitle
  string subtitle = 3;

  // map will have mapping of consent type to the consent urls, where key will be frontend.consent.consent_type.string()
  repeated ConsentTypeUrl consent_type_url = 1;

  // In client, we've support for only 2 consents. Hence, not using a repeated type.
  CheckboxConsent checkbox_consent1 = 4;
  CheckboxConsent checkbox_consent2 = 5;
  // consent3 is used to show Experian Tnc
  // figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27874-28289&t=sJBSYM7oTFmFQF46-4
  CheckboxConsent checkbox_consent3 = 7;

  // this flag decides if we want to show the minimal version of the screen (with just 2 consent options and not 4)
  // figma: https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=24090-58275&mode=design&t=sYEzEJgOv0eqS6f1-0
  bool show_minimal_screen = 6;

  // Indicates whether the client should verify the presence of NFC on the user's device.
  // NFC functionality is required during non-resident onboarding for scanning the Emirates ID.
  bool enable_nfc_check = 8;
}

message ConsentTypeUrl {
  // Type of consent
  ConsentType consent_type = 1;

  // Consent types - to be in sync with BE consent type
  // TODO (keerthana): come up with better design to have remove duplication of consent_type enum in FE
  enum ConsentType {
    ConsentType_UNSPECIFIED = 0;
    TnC = 1;
    cKYC = 2;
    eKYC = 3;
    FI_TNC = 6;
    FED_TNC = 7;
    FI_PRIVACY_POLICY = 8;
    FI_WEALTH_TNC = 9;
  }
  // Client redirects to this Url to show consent data
  string consent_url = 2;

  // String name of consent. This string will be used to display consent on UI
  string name = 3;
}

// Screen option for Webpage deeplink to show content from s3 link
message WebpageScreenOptions {
  // Title for the webpage
  string webpage_title = 1;
  // Url for the page content
  string webpage_url = 2;
  //Java script that need to execute on page load
  string java_script = 3;
  // [Optional] Flag to request clients to disable hardware back press in devices,
  // when displaying this web-page
  bool disable_hardware_back_press = 4;
  WebPageFlowIdentifier flow_identifier = 5;
  // client should use this bool to decide whether to hide the navigation bar or not .
  bool hide_navigation_bar = 6;
  // Background color for the navigation bar .
  string nav_bg_color = 7;
}

enum WebPageFlowIdentifier {
  WEB_PAGE_FLOW_IDENTIFIER_UNSPECIFIED = 0;
  WEB_PAGE_FLOW_IDENTIFIER_SHOW_CIBIL_REPORT = 1;
}

// screen option to verify customer via mobile prompt
// If the customer taps yes, then he is verified
message MobilePromptVerificationScreenOptions {
  // id to be passed back to server when user taps
  string prompt_id = 1;
  // name of user to be set in title
  api.typesv2.common.Name user_name = 2;
  // phone number from which the user called to be set in body
  api.typesv2.common.PhoneNumber phone_number = 3;
  // sent at time to be set in body text
  google.protobuf.Timestamp sent_at = 4;
  // expire at timer to be set in body text
  google.protobuf.Timestamp expire_at = 5;
}
message RequestFilter {
  enum FilterField {
    FILTER_FIELD_UNSPECIFIED = 0;
    // `MERCHANT` filter field will filter the transactions for selected merchants only
    MERCHANT = 1;
    // this filter tells the user_account for which transactions are to be shown
    ACCOUNT = 2;
    // the `to_people` filter which will filter transactions to specified contact
    PEOPLE = 3;
    // from_time to to_time filter
    TIME = 4;
    // this will filter out transactions for a selected location
    LOCATION = 5;
    // from_amount to to_amount filter
    AMOUNT = 6;
    // this will be applied on the user tagged transactions
    CATEGORY = 7;
  }
  // option field denote one selectable option inside a filter
  message FilterValue {
    // title of the option,
    //example = Merchant filter will have "Netflix" as one of the option
    string title = 1;
    // if selected by the user
    bool selected = 2;
    // is visible on the ui
    bool visible = 3;
  }
  // filter field selected by the user
  FilterField filter_field = 1;
  // option that is selected by the user
  repeated FilterValue options = 2;
}

message TransactionsTimelineScreenOptions {
  // query for transaction search
  string query = 1;
  // applied filters in the request
  RequestFilter filters = 2;
}

message TransactionReceiptScreenOptions {
  // order id for the transaction
  string order_id = 1 [deprecated = true];

  oneof identifier {
    string orders_id = 2;
    string aa_txn_id = 3;
  }

  // flag to indicate if dispute flow needs to be opened as soon as you land on transaction receipt
  bool open_raise_dispute = 4;
}

// screen options for deposit account details deeplink
message DepositAccountDetailsScreenOptions {
  // account id for the deposit account
  string account_id = 1;
  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 2;
}

// screen options for deposit landing screen deeplink
message DepositAccountLandingScreenOptions {
  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 1;
}

// screen options for opening a deposit account
message DepositOpenAccountOptions {
  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 1;

  // [Optional] The amount to pre-fill in open deposit screen
  api.typesv2.Money amount_to_add = 2;

  // [Optional] The maturity term to pre-fill while opening account
  api.typesv2.DepositTerm term = 3;

  analytics.AnalyticsScreenName entry_point = 4;
}

// screen options for close deposit account
message DepositCloseAccountOptions {
  // account id for the deposit account
  string account_id = 1;

  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 2;
}

// screen options for adding money to deposit account
message DepositAddMoneyOptions {
  // account id for the deposit account
  string account_id = 1;

  // [Optional] The amount to pre-fill in add money screen
  api.typesv2.Money amount_to_add = 2;

  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 3;
}

// screen options for SAVINGS_ACCOUNT_SETUP_PROGRESS
message SavingsAccountSetupProgress {
  // SavingsAccountSetupProgress Screen in client does 2 things
  // 1. Device Registration
  // 2. AccountSetup() & poll CheckAccountSetupStatus()
  enum Step {
    STEP_UNSPECIFIED = 0;
    REGISTER_DEVICE = 1;
    ACCOUNT_SETUP = 2;
  }
  Step step = 1;
  // Repeated field to display trust markers, which consist of icons and text,
  // helping to build user confidence in the service/product.
  repeated IconTextWidget bottom_info_widget = 2;
}

// screen options for FAQ search article
message FaqArticleOptions {
  // article id
  string article_id = 1;

  // title of the article page
  string title = 2;
}

// Screen options for AFU_ATM_PIN_VALIDATION
message AfuAtmPinValidationOptions {
  // request id or transaction is used in cred block generation
  string request_id = 1;

  string card_id = 2;

  string masked_card_number = 3;
}

// screen options for DEDUPE_ERROR
message DedupeErrorScreenOptions {
  // error message to be displayed to user
  string error_msg = 1;
  // bool which states if we have chat with us option or not
  bool has_chat_with_us = 2;
  // subtitle message to be displayed under the error message
  string subtitle_msg = 3;
  // bool which states if we have call us option or not
  bool has_call_us = 4;
  // bool which states if we have logout button or not
  bool has_log_out = 5;
  // bool which states whether to show feedback button or not
  bool has_feedback = 6;
  // screen_identifier is to identify a terminal screen
  string screen_identifier = 7;
  // list of CTAs to be displayed
  repeated Cta ctas = 8;
  // cta_header is the screen specific text shown at the bottom of the screen
  string cta_header = 9;
}


message DebitCardPinSetScreenOptions {
  // account number belonging to the account,
  // is non empty in only if account is created
  string account_number = 1;

  // This is the account id of the newly created account.
  // This is different from account_number. This is what
  // is gonna be used, for frontend-backend communication
  // with reference to an account.
  // It will be non empty in only if account is created
  string account_id = 2;

  // card identifier once the card is created successfully. This is unique across all
  // actors, card types, card forms
  string card_id = 3;

  // Basic card data to be displayed on the card. As soon as a card is created, the
  // card number, expiry and the name on the card is displayed on the UI.
  string card_number = 4;
  string expiry = 5;
  string cvv = 6;
  string name = 7;
  string masked_card_number = 8;

  // Card pin set otp token expire time. If expiry time is passed,
  // then pin set can be done via triggering OTP and passing OTP in cred block in pin set RPC.
  google.protobuf.Timestamp pin_set_token_expire_at = 9;
  // Card form determines if the current card is a physical or virtual card
  enum CardForm {
    CARD_FORM_UNSPECIFIED = 0;
    // PHYSICAL card to be used for ATM, POS transactions
    PHYSICAL = 1;
    // DIGITAL card to be used for online/e-com transactions
    DIGITAL = 2;
  }
  CardForm card_form = 10;
}

message UpiPinSetupOptions {
  // The type of Npci flow, for which, this UpiPinSet deep link will be used
  api.typesv2.NpciFlowType npci_flow_type = 1;
  // The account id for which the UPI pin is being set
  string account_id = 2 [deprecated = true];
  // created vpa for which pin need to be set
  string vpa = 3;
  // derived_account_id stores all the ids for the account, whether it be tpap,internal or connected account
  string derived_account_id = 4;
}

message UpiPinSetupOptionsV2 {
  // The type of Npci flow, for which, this UpiPinSet deep link will be used
  api.typesv2.NpciFlowType npci_flow_type = 1;
  // created vpa for which pin need to be set
  string vpa = 2;
  // derived_account_id stores all the ids for the account, whether it be tpap,internal or connected account
  string derived_account_id = 3;
  // client req id - to be used while initiating pin set / reset request with vendor
  // we need to pass this through client because user's consent need to be stored with us
  // and client req id will be the common parameter used throughout the flow
  string client_req_id = 4;
}

message CreateRewardSDScreenOptions {
  // amount that would be deposited in sd.
  api.typesv2.Money deposit_amount = 1;
  // Interest rate applicable on the sd account.
  string interest_rate = 2;
  // The date on which the sd will mature.
  google.protobuf.Timestamp maturity_date = 3;
  // name of sd that would be created
  string sd_name = 4;
}

message PayViaIntentScreenOptions {
  // Valid URN for Payment as per NPCI standards.
  string urn = 1;
}

message RaiseDisputeScreenOptions {
  // ID internally used for the transaction against which user wants to raise the dispute.
  string internal_transaction_id = 1;

  // order ID for the transaction against which user wants to raise the dispute.
  string external_order_id = 2;
}

// screen options for FAQ category
message FaqCategoryOptions {
  // category id
  string category_id = 1;
}

// options for opening UPI code code reader screen
message ProfileUpiQrCodeScreenOptions {
  string name = 1;
  string vpa = 2;
}

// screen options for REWARD_SHIPPING_ADDRESS_INPUT_SCREEN
message RewardShippingAddressInputScreenOptions {
  repeated api.typesv2.PostalAddress addresses = 1;
}

// VKYC Call type
// apologies for generic naming, missed during review
enum Call {
  CALL_UNSPECIFIED = 0;
  LIVE_CALL = 1;
  SCHEDULE_CALL = 2;
  SKIP_CALL = 3;
  // denotes an user is already a full kyc user
  FULL_KYC = 4;
}

// VKYC entry point
// apologies for generic naming, missed during review
enum EntryPoint {
  ENTRY_POINT_UNSPECIFIED = 0;
  ENTRY_POINT_VKYC_HOME = 1;
  ENTRY_POINT_DEPOSIT_PRECLOSURE = 2;
  ENTRY_POINT_DEPOSIT_MIN_KYC_COMMS = 3;
  ENTRY_POINT_ONBOARDING = 4;
  // users blocked due to L, S, O ckyc number during onboarding
  ENTRY_POINT_ONBOARDING_LSO = 5;
  // when user crosses X days threshold after which their account will freeze
  ENTRY_POINT_ACCOUNT_FREEZE_THRESHOLD_REACH = 6;
  ENTRY_POINT_SAVINGS_BALANCE_LIMIT_BREACH = 7;
  ENTRY_POINT_CREDIT_LIMIT_BREACH = 8;
  // when user is a min kyc and not able to add funds
  ENTRY_POINT_ADD_FUNDS_MIN_KYC_CHECK_FAIL = 9;
  // for LSO users who have onboarded with full kyc
  ENTRY_POINT_FORCE_LSO_USERS = 10;
  // entry point for salaried user
  ENTRY_POINT_SALARY_PROGRAM = 11;
  // entry point for pre-approved loans
  ENTRY_POINT_PRE_APPROVED_LOANS = 12;
  // entry point for rewards
  ENTRY_POINT_REWARDS = 13;
  // entry point for student force vkyc
  ENTRY_POINT_ONBOARDING_STUDENT = 14;
  // entry point for sd goals
  ENTRY_POINT_SD_GOALS = 15;
  // entry point for credit cards
  ENTRY_POINT_CREDIT_CARD = 16;
  // entrypoint for partial kyc dedupe users
  ENTRY_POINT_DEDUPE_PARTIAL_KYC = 17;
  // entry point for chequebook
  ENTRY_POINT_CHEQUEBOOK = 18;
}

// screen options for VKYC_LANDING
message VKYCLandingOptions {
  enum Call {
    CALL_UNSPECIFIED = 0;
    LIVE_CALL = 1;
    SCHEDULE_CALL = 2;
    SKIP_CALL = 3;
    // denotes an user is already a full kyc user
    FULL_KYC = 4;
  }
  Call call = 1;
  // added new field for call options as onboarding needs list of vkyc flows supported
  // same field will be used for normal flow as well
  repeated Call call_options = 2;
  // true if ekyc is expired based on which client will initiate ekyc
  bool is_ekyc_expired = 3;
  // text denoting when vkyc is next available at
  string vkyc_next_available_start = 4;
  // true denotes client to call GetVKYCInfo rpc before processing the deeplink
  bool get_vkyc_info = 5;

  enum EntryPoint {
    ENTRY_POINT_UNSPECIFIED = 0;
    ENTRY_POINT_VKYC_HOME = 1;
    ENTRY_POINT_DEPOSIT_PRECLOSURE = 2;
    ENTRY_POINT_DEPOSIT_MIN_KYC_COMMS = 3;
    ENTRY_POINT_ONBOARDING = 4;
    // users blocked due to L, S, O ckyc number during onboarding
    ENTRY_POINT_ONBOARDING_LSO = 5;
    // when user crosses X days threshold after which their account will freeze
    ENTRY_POINT_ACCOUNT_FREEZE_THRESHOLD_REACH = 6;
    ENTRY_POINT_SAVINGS_BALANCE_LIMIT_BREACH = 7;
    ENTRY_POINT_CREDIT_LIMIT_BREACH = 8;
    // when user is a min kyc and not able to add funds
    ENTRY_POINT_ADD_FUNDS_MIN_KYC_CHECK_FAIL = 9;
    // for LSO users who have onboarded with full kyc
    ENTRY_POINT_FORCE_LSO_USERS = 10;
    // for users with min KYC applying for loan
    ENTRY_POINT_PRE_APPROVED_LOAN = 11;
    // for users with employment type student trying to onboard
    ENTRY_POINT_ONBOARDING_STUDENT = 12;
    // for dedupe users with partial kyc dedupe
    ENTRY_POINT_DEDUPE_PARTIAL_KYC = 13;
    // entry point for rewards
    ENTRY_POINT_REWARDS = 14;
    // entry point for sd goals
    ENTRY_POINT_SD_GOALS = 15;
    // entry point for credit cards
    ENTRY_POINT_CREDIT_CARD = 16;
    // entry point for salaried user
    ENTRY_POINT_SALARY_PROGRAM = 17;
    // entry point for chequebook
    ENTRY_POINT_CHEQUEBOOK = 18;
  }
  EntryPoint entry_point = 6;

  // title will be of html type contains color also
  string title = 7;
  // Description for the page
  string description = 8;
  // background color of whole page in hex color
  string background_color_hex = 9;
  // each array have row of blocks
  repeated LandingPageBlock landing_page_block_list = 10;
  // CTA
  repeated Cta cta_list = 11;
  // bottom sheet have message to be shown when user press back button on vkyc landing page
  VKYCBottomSheet vkyc_bottom_sheet = 12;
  // skip timer denotes seconds after which skip option is enable on client side (Applicable only in case of call options has skip flag)
  int32 skip_option_timer = 13;
}

message VKYCIntroOptions {
  // title will be of html type contains color also
  string title = 1;
  // Description for the page
  string description = 2;
  // background color of whole page in hex color
  string background_color_hex = 3;
  // each array have row of blocks
  repeated LandingPageBlock blocks = 4;
  // CTA list
  repeated Cta ctas = 5;
  // skip cta info contains stage name needs to be skipped
  SkipCTAInfo skip_cta_info = 6;
  // needs to places just above ctas
  repeated BottomBanner bottom_banners = 7;
}

message BottomBanner {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  // android have difficulty in consuming radial gradient hence adding block color
  api.typesv2.common.ui.widget.BackgroundColour bg_color_block = 4;
  api.typesv2.common.Image icon = 2;
  api.typesv2.common.Text text = 3;
}

message LandingPageBlock {
  string icon = 1;
  //  text in html format contains color also
  string text = 2;
  // background color in hexcode form
  string background_color_hex = 3;
  // tag contains extra info for block
  LandingPageBlockTag landing_page_block_tag = 4;
  // tile type
  LandingPageBlockType landing_page_block_type = 5;
}

enum LandingPageBlockTag {
  LANDING_PAGE_BLOCK_TAG_UNSPECIFIED = 0;
  LANDING_PAGE_BLOCK_TAG_SOON = 1;
}

enum LandingPageBlockType {
  LANDING_PAGE_BLOCK_TYPE_UNSPECIFIED = 0;
  LANDING_PAGE_BLOCK_TYPE_HALF = 1;
  LANDING_PAGE_BLOCK_TYPE_FULL = 2;
}

message CardUsageScreenOptions {
  string card_id = 1;
}

message CardHomeScreenOptions {
  string cardId = 1;
}

// screen options for CONFIRM_CARD_MAILING_ADDRESS
message ConfirmCardMailingAddressOptions {
  api.typesv2.KYCLevel kyc_level = 1;
  // Deprecated: in favor of screen_title
  string title = 2 [deprecated = true];
  // Deprecated: in favor of screen_subtitle
  string subtitle = 3 [deprecated = true];
  string place_holder_for_name = 4;
  string place_holder_for_address = 5;
  // Corresponding to the address type selected by the user,
  // check box text should be updated accordingly.
  // If no check box text is present for the address type,
  // check box should not be shown.
  message CheckBoxText {
    api.typesv2.AddressType type = 1;
    string text = 2;
  }
  repeated CheckBoxText check_box_texts = 6;
  Flow flow = 7;
  enum Flow {
    FLOW_UNSPECIFIED = 0;
    FLOW_ONBOARDING = 1;
    // Figma link corresponding to debit card flow: https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=0%3A1&t=9qKFpwq2IcmVuFs0-0
    FLOW_DEBIT_CARD = 2;
  }
  // total amount to be paid by the user (inclusive of gst charges if any)
  api.typesv2.Money amount = 8;
  // unique identifier for card
  string card_id = 9;
  // Image corresponding to the CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  api.typesv2.common.Image image = 10;
  // color associated with checkbox texts
  string checkbox_text_color = 11;
  // placeholder to be used to display the amount to be paid by user
  string place_holder_for_amount = 12;
  // Corresponding to the amount placeholder, a hint text is associated which can be stored as part of this field.
  string hint_text_amount = 13;
  // color associated with place holders
  string place_holder_color = 14;
  // color associated with the placeholder content
  string content_color = 15;
  // color associated with hint
  string hint_color = 16;
  // color associated with divider line
  string divider_color = 17;
  // color associated with edit icon
  string edit_icon_color = 18;
  // color associated with the card in the background encapsulating placeholders & related content
  string card_color = 19;
  // background color for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  string background_color = 20;
  // this encapsulates the title related details for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  api.typesv2.common.Text screen_title = 21;
  // this encapsulates the sub title related details for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  api.typesv2.common.Text screen_subtitle = 22;
  // the amount to be displayed to user as a break up of the card fee & gst value. This is set from backend.
  string display_amount = 23;
  // cta associated with CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  Cta cta = 24;
  // name to be shown on card mailing address screen
  // this will be used in case of debit card charges flow to show the name to be printed on the card
  string name = 25;
  // the message to be shown for address confirmation
  string address_confirmation_message = 26;
  // flag decides whether to hide the address field or not. eg, for NR onb address field won't be shown.
  bool hide_address_field = 27;
}

// screen options for START_EKYC
message StartEKYCOptions {
  // enum denotes deeplink source
  enum EKYCFlow {
    EKYC_FLOW_UNSPECIFIED = 0;
    EKYC_FLOW_ONBOARDING = 1;
    EKYC_FLOW_VKYC = 3;
  }
  api.typesv2.KYCLevel kyc_level = 1;
  // deprecated since added ekyc_source as its counterpart
  EKYCFlow ekyc_flow = 2 [deprecated = true];
  // deeplink to redirect user after ekyc flow ends
  frontend.deeplink.Deeplink next_action = 3;
  // entry point indicates source for ekyc kyc/kyc/ekycSource
  string ekyc_source = 4;
  // widget to be shown at the top (top section of the screen) to highlight the applicable offer for the user,
  // for e.g. "<icon> Offer applied! Get flat ₹100 when you sign up"
  // Note: can be nil
  IconTextWidget offer_widget = 5;
  api.typesv2.common.Text title = 6;
  api.typesv2.common.VisualElement image = 7;
  api.typesv2.common.Text description = 8;
  message EKYCDescriptionInfo {
    api.typesv2.common.Text bottom_sheet_title = 2;
    api.typesv2.common.Text bottom_sheet_description = 3;
  }
  // ekyc_description_info is clickable area, client is expected to launch bottom sheet
  EKYCDescriptionInfo ekyc_description_info = 9;
  api.typesv2.common.Text consent_text = 10;
  // skip_ekyc_info_screen is used to skip the ekyc info screen and directly start
  // the Ekyc process. E.g. we skip the Ekyc Intro screen Ui for Loans
  bool skip_ekyc_info_screen = 11;
}

// IconTextWidget: a stripped down version to push an Icon+Text component via screen-options.
// Note:
// 1. height & width can be assumed at the client.
// 2. currently doesn't account for tap action. can add deeplink support if needed.
message IconTextWidget {
  // image to be shown on the left of the text.
  // Note: can be nil
  api.typesv2.common.Image left_img = 1;
  // image to be shown on the right of the text.
  // Note: can be nil
  api.typesv2.common.Image right_img = 2;
  // text to be shown
  api.typesv2.common.Text text = 3;
  // padding between left image and text
  int32 left_img_text_padding = 5;
  // padding between right image and text
  int32 right_img_text_padding = 6;
  // bg color of the widget
  string bg_color = 7;
}

message CardSettingsScreenOptions {
  string card_id = 1;
}

message CardLimitHomeScreenOptions {
  string card_id = 1;
}

message ViewVKYCScheduleScreenOptions {

}

message FiniteCodeVerifiedScreenOptions {
  string finite_code = 1;
}

message EnterReferralFiniteCodeScreenOptions {
  // Referral finite code for client to auto-fill if present.
  string finite_code = 1;

  // Skip finite code button should be visible only if this is true
  bool show_skip_finite_code_button = 2;

  // static offers to be shown to the user as an alternative to finite-codes.
  // Note: these are dummy codes. NO API CALL should be made using these codes for referral.
  // Note: can be empty as well.
  // current use case: to show these offer-codes only if the referrals input field is not pre-filled.
  repeated OfferCode offer_codes = 3;

  // key to be used for accessing finite-code from attribution link params for autofill of finite-code.
  // Note: if field is empty or the corresponding key is not found in attribution params,
  // fallback to `ru` / `referralCode` params.
  string finite_code_from_attribution_params_key = 4;

  // subtext to be shown just below the referral code input box which should disappear as soon as the user enters anything
  // Ex - "Forgot referral code? Enter your friend's number 📞"
  // Note - this can be nil, in which case the subtext should not be shown
  api.typesv2.common.Text referral_code_input_subtext = 5;

  message OfferCode {
    // title to be shown before the offer code is applied
    api.typesv2.common.Text before_applied_title = 1;
    // title to be shown after the offer code is applied, i.e. selected by the user
    api.typesv2.common.Text after_applied_title = 2;

    // desc of the offer before the offer code is applied
    api.typesv2.common.Text before_applied_desc = 3;
    // desc of the offer after the offer code is applied
    api.typesv2.common.Text after_applied_desc = 4;

    // icon to be shown before the offer code is applied
    api.typesv2.common.Image before_applied_icon = 5;
    // icon to be shown after the offer code is applied
    api.typesv2.common.Image after_applied_icon = 6;

    // actual code to be used for filling the referral input field, for e.g. FI200
    string code = 7;
    // finite-code with which we want the user to onboard with.
    // make the ClaimFiniteCode API call with this code if its present.
    string underlying_finite_code = 8;

  }
}

message GetCardAuthNextActionApiOptions {
  string attempt_id = 1;
}

message FitRuleHistoryScreenOptions {
  string subscription_id = 1;
}

message FitCustomiseRuleScreenOptions {
  //  Either rule_id or subscription_id can set based on page_type.
  // Set subscription_id for updating existing subscription of a rule.
  string rule_id = 1;
  string subscription_id = 2;
  frontend.fittt.clientstate.SubscriptionPageType page_type = 3;
  // default selection for mutual fund would be param with id as `mutual_fund_id`
  // mutual fund wont be editable if mutual_fund_id is non empty
  string mutual_fund_id = 4;
  // includes all the default params required for the rule
  frontend.fittt.rule.RuleParamValues param_values = 5;
  // entry point from where the "FIT Customise Rule" screen is opened
  // (optional)
  frontend.analytics.AnalyticsScreenName entry_point = 6;
}

message FitSubscriptionInfoRuleScreenOptions {
  string subscription_id = 1;
}

message FitAllSubscriptionsScreenOptions {
  string rule_id = 1;
  repeated RuleSubscriptionState sub_states = 2;
}

message FitSubscriptionsPreviewScreenOptions {
  string tag_id = 1;
  string rule_type = 3;
  repeated RuleSubscriptionState sub_states = 2;
}

message FitAllCollectionsPageScreenOptions {
  string collection_type = 1;
}

message FitCollectionPageScreenOptions {
  string collection_id = 1;
  string selected_tag_id = 2;
  // optional: string form of the enum rule RuleCategory.
  string rule_category = 3;
}

message FitMyRulesPage {
  string rule_category = 1;
  repeated RuleSubscriptionState sub_states = 2;
}

message FitExploreRulesPageOptions {
  // [OPTIONAL]
  // all rules belonging to prioritized_tag_id will be placed at the top of the list
  string prioritized_tag_id = 1;
}

message UpdateUserDetailsApiScreenOptions {
  // true if user entered and kyc Name does not match
  bool name_mismatch = 1;
  // true if user entered and kyc DOB does not match
  bool dob_mismatch = 2;
  // title to be displayed to user
  string title = 3 [deprecated = true];
  // subtitle to be displayed under the title
  string subtitle = 4 [deprecated = true];
  // total retry attempts allowed before taking the user to terminal error screen.
  int32 max_attempts = 5;
  // attempts made by the user. This number is always less than max_attempts.
  int32 current_attempts = 6;
  // entry point for the screen, client uses this to decide which RPC to call on cta
  UpdateUserDetailsApiEntryPoint entry_point = 7;
  api.typesv2.common.Text consent = 8;
  Cta cta = 9;
  api.typesv2.common.Text title_text = 10;
  api.typesv2.common.Text subtitle_text = 11;
}

enum UpdateUserDetailsApiEntryPoint {
  UPDATE_USER_DETAILS_API_ENTRYPOINT_UNSPECIFIED = 0;
  UPDATE_USER_DETAILS_API_ENTRYPOINT_KYC_DOB_MISMATCH = 1;
  UPDATE_USER_DETAILS_API_ENTRYPOINT_DEDUPE_DOB_MISMATCH = 2;
}

// screen options for EMPLOYMENT_DECLARATION
// EmploymentDeclarationOptions consists of data needed to setup declaration screen. There are 3 major elements.
// 1. The list of employment types for users to choose from
// 2. The dynamic user input UI based on the type chosen in 1.
// 3. Annual salary of user.
message EmploymentDeclarationOptions {
  EmploymentTypeUiView employment_type_view = 1;
  EmploymentProofUiView employment_proof_view = 2;
  enum EmploymentScreenView {
    EMPLOYMENT_SCREEN_VIEW_UNSPECIFIED = 0;
    FULL_SCREEN = 1;
    BOTTOM_SHEET = 2;
  }
  EmploymentScreenView screen_view = 4;
  AnnualSalaryListUiView annual_salary_list_view = 3;

  // absolute income is deprecated; use AnnualSalaryListUiView for income range
  AnnualSalaryView annual_salary_view = 5 [deprecated = true];

  IncomeDiscrepancyView income_discrepancy_view = 6;
  // client pass source to process employment data rpc
  // it maps to update source enum
  // sample value UPDATE_SOURCE_ONBOARDING
  string source = 7;

  message OccupationTypeUiView {
    message OccupationTypeInfo {
      // id is the backend identifier for occupation type. String form of employment.employment_data.occupationType
      string id = 1;
      // string to display for the occupation type
      string display_string = 2;
    }
    repeated OccupationTypeInfo infos = 1;
    OccupationTypeInfo default = 2;
    string header = 3;
  }
  // list of occupation types to show
  OccupationTypeUiView occupation_type_ui_view = 8;

  // config values as per https://docs.google.com/document/d/1F4-qnQzlFUw3VxuOgcgctx-OKtn9SL25mwpJQQiDrIs/edit

  // Flag to indicate if we want to use new version of edit employment bottom sheet
  bool use_bottom_sheet_v2 = 9;

  // holds title to be displayed
  string title = 10;

  // Name of Android activity in which the employment_declaration fragment should be hosted.
  // Android has different activities for onboarding & profile flows. Applicable only for Android app platform.
  string android_activity = 11;

  // Client should handle "CONTINUE" cta type to call ProcessEmployment RPC
  Cta cta = 12;

  api.typesv2.common.Text subtitle = 13;
  // Header bar used in onboarding stages
  HeaderBar header_bar = 14;

  QualificationTypeUiView qualification_type_ui_view = 15;

  SourceOfIncomeTypeUiView source_of_income_type_ui_view = 16;

  AnnualTransactionVolumeTypeUiView annual_transaction_volume = 17;
}

message QualificationTypeUiView {
  // string to be shown on top of the list
  string header = 1;
  repeated api.typesv2.common.Text qualification = 2 [deprecated = true];
  repeated QualificationTypeInfo qualification_info = 3;
}

message QualificationTypeInfo {
  string display_string = 1;
  // id is the backend identifier for qualification type. String form of typesv2.qualification
  string id = 2;
}

message SourceOfIncomeTypeUiView {
  // string to be shown on top of the list
  string header = 1;
  repeated api.typesv2.common.Text source_of_income = 2 [deprecated = true];
  repeated SourceOfIncomeTypeInfo source_of_income_info = 3;
}

message SourceOfIncomeTypeInfo {
  string display_string = 1;
  // id is the backend identifier for sourceof income type. String form of typesv2.sourceOfFunds
  string id = 2;
}

message AnnualTransactionVolumeTypeUiView {
  // string to be shown on top of the list
  string header = 1;
  repeated AnnualSalary annual_transaction_volume = 2;
  api.typesv2.common.Text description = 3;
}

message EmploymentTypeUiView {
  repeated EmploymentTypeInfo infos = 1;
  EmploymentTypeInfo default = 2;
  // display string for employment type
  // current UI: "Employment type"
  string header = 3;
}

message EmploymentProofUiView {
  repeated EmploymentProofInfo infos = 2;
}

message AnnualSalaryListUiView {
  // string to be shown on top of the list
  string title = 1;
  // deprecated in favour EmploymentTypeUi.AnnualSalaryList
  // annual salary list can have different range for different employment or age
  // so we dont need global list
  repeated AnnualSalary annual_salary_list = 2 [deprecated = true];
}

message AnnualSalaryView {
  // Text to show on the box before user clicks on it
  string hint = 1;
  // Text to show on the bottom-screen pop up
  string title = 2;
  // Max salary amount to show in the slider
  int32 max_amount = 3;
  // Size of each step of slider
  int32 interval_amount = 4;
}

message EmploymentTypeInfo {
  frontend.account.screening.EmploymentType type = 1;
  // display string for the enum defined by `type` above
  // eg: "Salaried", "Self employed"
  string type_display = 2;

  // For a given employment type, there could be multiple proofs that can be accepted. Default to first entry in the list.
  repeated frontend.account.screening.EmploymentProofType supported_proofs = 3;

  // Max income for no Discrepancy
  int64 max_normal_income = 4;

  // supported_occupation_ids is the list of occupations that can be selected for an employment type.
  // It is the same id as OccupationTypeInfo -> Id
  repeated string supported_occupation_ids = 5;

  // Flag to indicate the requirement of an employer for the selected employment type
  bool requires_employer = 6;

  // Flag to indicate the requirement of an occupation for the selected employment type.
  bool requires_occupation = 7;

  // annual salary range list depands on age and employment type
  // earlier we used to have global annual salary list that was deprecated, common for all employment type and age
  repeated AnnualSalary annual_salary_list = 8;
}

message EmploymentProofInfo {
  frontend.account.screening.EmploymentProofType type = 1;
  // for salaried employment type: "Company name"
  // other type: "Your work detail"
  string display = 2;
  // for salaried employment type: "Please enter full company name, without abbreviations"
  // for all other types, "Ex: Linkedin profile, personal website etc."
  string tool_tip = 3;
  // If true, user has to provide the employment proof before proceeding.
  // If false, user can skip providing proof here and proceed further.
  bool mandatory = 4;
}

// AnnualSalary represents salary range of a user
message AnnualSalary {
  // string to be displayed in option
  string display_string = 1;
  // min value of salary range
  int32 min_val = 2;
  // max value of salary range
  int32 max_val = 3;
  // currency_code of the annual salary
  // Supported: INR, AED
  // Note: in case more enums are added to currency code, a client release is required to support this.
  api.typesv2.CurrencyCode currency_code = 4;

  // alternate annual salary value that is showed to the user.
  // This alternate values that need to be displayed to the user is a part in display_string
  message AlternateDisplay {
    // min value of salary range
    int32 min_val = 1;
    // max value of salary range
    int32 max_val = 2;
    // currency_code of the annual salary
    // Supported: INR, AED
    // Note: in case more enums are added to currency code, a client release is required to support this.
    api.typesv2.CurrencyCode currency_code = 3;
  }
  AlternateDisplay alternate_display = 5;
}

message IncomeDiscrepancyView {
  // string to be displayed as warning(in html) only in case of income discrepancy
  string warning_text = 1;
  // string to be displayed as consent(in html) only in case of income discrepancy
  string consent_text = 2;
}

// Screen options for app screening error screen
message ErrorAppScreeningOptions {
  // error message to be displayed to user
  string error_msg_title = 1;
  // bool which states if we have chat with us option or not
  bool has_chat_with_us = 2;
  // subtitle message to be displayed under the error message
  string subtitle_msg = 3;
}

message UpiETBPinSetActivityOptions {
  // title of the message to show on user screen
  string title = 1;
  // body of the message to show on user screen
  string body = 2;
  // The account id for which the ETB pin set activity need to be triggered
  string account_id = 3 [deprecated = true];
  // user activity to post on frontend.user.PostUserActivity rpc
  frontend.user.user_activity.UserActivity user_activity = 4;
  // derived_account_id stores all the ids for the account, whether it be tpap,internal or connected account
  string derived_account_id = 5;
}

// The options proto object for the Statement request deeplink
message StatementRequestOptions {
  // The account id string
  string account_id = 1;
  // type of the account (e.g., SAVINGS, SMART_DEPOSIT etc.)
  accounts.Type account_type = 3;
}

message DebitCardOffersHomeScreenOptions {
  string card_id = 1;
  // optional parameter, if provided will load card offer details screen
  string offer_id = 2;
}

message DebitCardOfferDetailsScreenOptions {
  // mandatory parameter, will load card offer details screen
  string offer_id = 1;
}

message DebitCardTrackingScreenOptions {
  string card_id = 1;
}

// Proto object for GenerateTxnId api deeplink
message GenerateTxnIdCardApiOptions {
  // Unique id corresponding to a auth attempt initiated for the card pin actions.
  // This acts as a second factor validation for performing pin actions
  string auth_attempt_id = 1;
}

message CardSecurePinValidationOptions {
  // Unique id corresponding to a auth attempt initiated for the card pin actions.
  // This acts as a second factor validation for performing pin actions
  string auth_attempt_id = 1;
  // unique identifier for which cred block needs to be generated
  string txn_id = 2;
}

message ExternalRedirectionScreenOptions {
  // It can be either a
  // - URL to open external browser
  // - For email, we can use mailto:toEmail?subject=subject&body=body
  // - For phone, we can use tel://0123456789
  string external_url = 1;
}

// ListItem is a generic list item
message ListItem {
  string title = 1;
  string subtitle = 2;
  string image_url = 3;
}

// figma: https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/Onboarding-%2F-Workfile?node-id=13676%3A153822&t=GywivyiqlaGYPWpV-0
message AppScreeningGmailVerificationOptions {
  // user will be taken to this web view for gmail verification
  string gmail_verification_web_view_url = 1;
  string title = 2;
  string subtitle = 3;
  repeated ListItem instructions = 4;
  string privacy_policy_text = 5;
  string privacy_policy_web_view_url = 6;
  deeplink.BackAction back_action = 7;
}

message SalaryProgramEmployerConfirmationScreenOptions {
  // current employer info
  EmployerInfo current_employer = 1;
  // salary program eligibility statement,
  // for e.g."You are eligible for a Fi salary account if you earn ₹25,000 or more per month".
  // Note: Can be empty when is_verified_employer is false
  string salary_program_eligibility_statement = 2;

  message EmployerInfo {
    // whether employer is verified internally or not
    bool is_verified_employer = 1;
    // internal id of the employer.
    // note: can be empty if employer is not verified at our end
    string employer_id = 2;
    // name of the employer
    string employer_name = 3;
  }
}

message LinkedInVerificationWaitScreenOptions {
  // Title to be shown on linked in waiting screen
  string title = 1;
  // Content to be shown on linked in waiting screen
  string body = 2;
}

message WealthOnboardingCaptureMissingDataScreenOptions {
  // this will encapsulate data needed to missed data collection from customer
  message MissingDataWithStatus {
    Deeplink missing_data_deeplink = 1;
    WealthOnboardingDataStatus status = 2;

    // ex: Marital status, Income, etc.
    string display_text = 3;

    // ex: Single, 15 Lakh, etc.
    string display_val = 4;

    // ex: "SIGN", "UPLOAD", "ADD", etc.
    // use only when status = WEALTH_ONBOARDING_DATA_STATUS_ENABLED
    // for other cases, clients should fallback to "+" symbol
    // ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=9055%3A43163
    string fresh_input_text = 5;
  }
  repeated MissingDataWithStatus missing_data = 1;
  // deeplink contains WEALTH_ONBOARDING_DOCUMENT_AADHAAR_ESIGN_SCREEN
  Deeplink sign_docket_deeplink = 2;
  wealthonboarding.clientstate.WealthFlow flow = 3;
  string title = 4;
  string description = 5;
  // indicates whether to enable or disable the cta that takes the user to WEALTH_ONBOARDING_DOCUMENT_AADHAAR_ESIGN_SCREEN
  bool is_sign_docket_cta_enabled = 6;
  // indicates whether to show the WEALTH_ONBOARDING_DOCUMENT_AADHAAR_ESIGN_SCREEN cta
  // when cta is not visible, user will be navigated to the next screen directly from the last missing data item screen
  bool is_sign_docket_cta_visible = 7;
  // text field to be shown just above the sign docket cta
  api.typesv2.common.Text bottom_info_text = 8;
  // illustration URL on top of screen
  string illustration_url = 9;
}

message WealthOnboardingSignAgreementScreenOptions {
  string tnc_url = 1 [deprecated = true];
  bool is_consent_taken = 2;
  string title = 3;
  string description = 4;
  wealthonboarding.clientstate.WealthFlow flow = 5;
  string illustration_url = 6;
  int32 progress_bar_duration_in_secs = 7;
  // if consents are not present, the client fall back to progress_bar
  repeated api.typesv2.common.ui.widget.CheckboxItem consents = 8;
}

message WealthOnboardingDocumentAadhaarEsignScreenOptions {
  string sign_id = 1;
  string esign_url = 2;
  wealthonboarding.clientstate.WealthFlow flow = 3;
  string callback_url = 4;
}

message WealthOnboardingLivenessScreenOptions {
  string sign_id = 1;
  string esign_url = 2;
  wealthonboarding.clientstate.WealthFlow flow = 3;
}

message WealthOnboardingStatusScreenOptions {
  WealthOnboardingAction onboarding_action = 1;
  string message = 2;
  string title = 3;
  string description = 4;
  Cta cta = 5;
  wealthonboarding.clientstate.WealthFlow flow = 6;
  string illustration_url = 7;
}

// VerifyWorkOtpEmailOptions to be used by client if parameters are not already present on client side
message VerifyWorkOtpEmailOptions {
  // Unique identifier of GenerateOtp request
  string token = 1;
  // A timer(in seconds) after which client can sent new request
  uint32 retry_after = 2;
  // A timer(in seconds) for the client post which it should raise a NEW request for Otp i.e. token = ""
  // Any attempt prior to this timer should send the token received in first call
  uint32 otp_expiry = 3;
  // user email
  string email = 4;
  // client_req_id is the unique identifier of a work email verification process
  string client_req_id = 5;
  ToggleableScreenerCta cta = 6;
  BackAction back_action = 7;
  // error text that will be showed below the otp entry segment of the screen
  api.typesv2.common.Text inline_error_text = 8;
  // client is the string form of employment.VerificationProcessClient enum
  string client = 9;
}

// AskFiLandingPageOptions to create deeplink for ASK_FI_LANDING_PAGE
message AskFiLandingPageOptions {
  // search query to be shown on landing page
  // OPTIONAL
  string query = 1;
  // Transaction Filters
  repeated frontend.search.meta.TransactionFilter transaction_filters = 3;
  // if landing_tab is not empty and valid, user will be landed on to requested tab
  string landing_tab = 4;
  // if source is not empty and valid, will be sent in GetAskFiResultRequest and ActionBarRequest
  string source = 5;
}

// HelpSearchOptions to create deeplink for HELP_SEARCH
message HelpSearchOptions {
  // help-search to be shown on landing page
  // OPTIONAL
  string query = 1;
}

message LivenessManualReviewOptions {
  string title = 1;
  string subtitle = 2;
  repeated string failure_reasons = 3;
  string footer = 4;
}

// Args required to intitate AA SDK
message ConnectedAccountsOptions {
  // Phone number to be used when registering with AA
  api.typesv2.common.PhoneNumber mobileNumber = 1;
  // User full name, to be used when registering with AA
  string name = 2;
  // AA entity
  // This enum is separately added in this deeplink object because we cannot import frontend/connected_account/* in deeplink proto due to cyclic dependency.
  enum AaEntity {
    AA_ENTITY_UNSPECIFIED = 0;

    AA_ENTITY_ONE_MONEY = 1;

    AA_ENTITY_FINVU = 2;
  }
  AaEntity aa_entity = 3;
  // FIU initialising AA Sdk eg: EPIFIPROD. This is the FIU id given to epiFi by sahamati as a registered participant
  string fiu_id = 4;
  // represents html encoded message with corresponding AA Tnc link
  // ex: Onemoney Tnc message if aa entity is Onemoney
  string aa_tnc_message = 5;
  // flag to specify client whether to use v2 or not
  // if true use v2 flow, v1(exisiting) flow otherwise
  bool use_v2_flow = 6;
  // flag to specify whether token authentication should be used for session persistence or not
  // Only supported by Finvu SDK as of now
  api.typesv2.common.BooleanEnum use_token_authentication = 7;
  // ca_flow_name represents identifier of other service which is trying to use connected account flow
  // The value for this string will the identifier for service which is trying to use CA flow
  string ca_flow_name = 9;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 10;

  // List of FIPs for which we need to start discovery.
  // If empty discover all FIP's else discover only the FIP's in the list
  repeated string fip_list = 11;
  // version to specify client whether to use from available versions like V1, V2, V3
  frontend.deeplinkv2.Version version = 12;
}

message WealthOnboardingErrorScreenOptions {
  // illustration image to be shown on the app
  string illustration_url = 1;
  string title = 2;
  string description = 3;
  Cta cta = 4;
  // onboarding_action is to be used for mapping the exact image url for illustration and the CTA
  // TODO(ismail): need to remove this as part of refactoring in future
  WealthOnboardingAction onboarding_action = 5;
  wealthonboarding.clientstate.WealthFlow flow = 6;
}

// landing screen options for wealth onboarding
message WealthOnboardingLandingScreenOptions {
  wealthonboarding.clientstate.WealthFlow flow = 1;
}

// figma link for sample view : https://www.figma.com/file/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=13576%3A28021
message DetailedErrorViewScreenOptions {
  string title = 1 [deprecated = true];
  string subtitle = 2 [deprecated = true];
  // in failure_reason html tags can be passed.
  string failure_reason = 3;
  // url of the image
  string imageUrl = 4 [deprecated = true];
  // bool which states if we need to have logout button or not
  bool has_log_out = 5;
  // bool which states whether to show feedback button or not
  bool has_feedback = 6;
  // screen_identifier is to identify a terminal screen
  string screen_identifier = 7;
  // list of CTAs to be displayed
  repeated Cta ctas = 8;
  // cta_header is the screen specific text shown at the bottom of the screen
  string cta_header = 9;

  api.typesv2.common.Text title_text = 10;
  api.typesv2.common.Text subtitle_text = 11;
  api.typesv2.common.VisualElement image = 12;
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 13;
  // properties that needs to logged along with the generic events at client side
  map<string, string> event_properties = 14;
  // https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?type=design&node-id=19896-13777&mode=design&t=Jd6JF4OGCcMWW98H-0
  FaqInfo faq_info = 15;
  // Swipe CTA
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=7084-119728&t=CrcQAM5V8DkAWRID-4
  // If swipe cta is there, then consider this otherwise fallback to ctas;
  Cta swipe_cta = 16;
  // Common header bar to display any logo or do some back action
  HeaderBar header_bar = 17;
  // SDUI sections to configure the screen
  api.typesv2.ui.sdui.sections.Section sections = 18;
}

// faq related info with next action
message FaqInfo {
  api.typesv2.common.VisualElement faq_icon = 1;
  deeplink.Deeplink faq_action = 2;
}

// Screen which can used as intermediate information screen in the flow
message InfoAcknowledgementScreenOptions {

  enum BodyInstructionType {
    BODY_INSTRUCTION_TYPE_UNSPECIFIED = 0;
    // for displaying instructions as bullet/ unordered list
    BODY_INSTRUCTION_TYPE_UNORDERED = 1;
    // for displaying instructions as numbered/ ordered list
    BODY_INSTRUCTION_TYPE_ORDERED = 2;
  }

  string title = 1 [deprecated = true];

  string subtitle = 2 [deprecated = true];

  Body body = 3 [deprecated = true];

  string image_url = 4 [deprecated = true];

  // a medium for acceptance of user confirmation
  string checkbox_text = 5 [deprecated = true];

  repeated Cta ctas = 6;

  message Body {
    // contains text to be displayed plainly in the body
    string body_description = 1;
    // contains set of instruction that will be displayed as bullet/ unordered list in the body
    repeated string body_instructions = 2;
    // type flag to control the listing style of instructions
    BodyInstructionType body_instruction_type = 3;
  }

  // text to be shown above the cta button
  string cta_header = 7;

  enum ScreenTheme {
    // unspecified screen theme
    SCREEN_THEME_UNSPECIFIED = 0;
    // theme where title, subtitle is passed as a string and body content is passed in body message
    // figma: https://www.figma.com/file/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=12584%3A28112
    SCREEN_THEME_1 = 1;
    // theme where the title, subtitle is passed as string and body content is passed as an array of objects
    // figma: https://www.figma.com/file/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=15634%3A32877
    SCREEN_THEME_2 = 2;
  }

  message ScreenContentTheme1 {
    string title = 1 [deprecated = true];

    string subtitle = 2 [deprecated = true];

    // contains the main body, includes explanations or instructions
    Body body = 3;

    string image_url = 4;

    // a medium for acceptance of user confirmation
    string checkbox_text = 5;

    api.typesv2.common.ui.widget.BackgroundColour bg_colour = 6;

    api.typesv2.common.Text title_text = 7;

    api.typesv2.common.Text subtitle_text = 8;

    api.typesv2.common.VisualElement screen_image = 9;

    HeaderBar header_bar = 10;

    IconTextWidget info_message = 11;
  }

  message ScreenContentTheme2 {
    string title = 1;

    string subtitle = 2;

    message ContentObject {
      string content_text = 1;
      string icon_url = 2;
    }
    repeated ContentObject content_list = 3;
  }

  oneof ScreenContent {
    ScreenContentTheme1 screen_content_theme1 = 9;
    ScreenContentTheme2 screen_content_theme2 = 10;
  }

  // Structure of the object used for sending screen content is decided by screen_theme
  ScreenTheme screen_theme = 8;

  SkipCTAInfo skip_cta_info = 11;
}



message PhonePermissionScreenOptions {
  bool show_wa_consent_option = 1;
  string wa_consent_message = 2;
  // flag to decide whether ATT prompt has to be shown to the user or not
  // reference: https://docs.google.com/document/d/173cTxMIGSv_19O-cmGQfV1lf_dgxy7wSP2RRUnkbRFQ/edit?usp=sharing
  bool show_att_prompt_ios = 3;

  api.typesv2.common.Text w_a_consent_title = 4;
  api.typesv2.common.Text w_a_consent_subtitle = 5;

  message PermissionItem {
    api.typesv2.common.VisualElement permission_image = 1;
    api.typesv2.common.Text permission_title = 2;
    api.typesv2.common.Text permission_subtitle = 3;
  }

  repeated PermissionItem w_a_consent_permission_items = 6;
  api.typesv2.common.Text w_a_consent_text = 7;
  // whether SMS permission is mandatory or not.
  // figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27874-28289&t=sJBSYM7oTFmFQF46-4
  bool is_sms_permission_mandatory = 8;
  Cta cta = 9;
}

// create recurring page deeplink
// screen : `CREATE_RECURRING_PAYMENT`
// options: recurring_payment_id : id to fetch recurring payment details.
message CreateRecurringPaymentScreenOptions {
  string recurring_payment_id = 1 [deprecated = true];

  // vpa of the other actor involved in the payment
  string other_actor_vpa = 2;

  // amount to be transferred
  api.typesv2.Money amount = 3;

  // recurrence rule for the recurring payment
  RecurrenceRule recurrence_rule = 4;

  // start date of recurring payment
  api.typesv2.Date start_date = 5;

  // end date of recurring payment
  api.typesv2.Date end_date = 6;

  // flag to determine if need to notify payee regarding the recurring payment
  bool notify_payee = 7;

  // Maximum allowed transaction withing the allowed frequency range
  // NOTE : Need not to be passed for mandate creation
  int32 maximum_allowed_transactions = 8;

  // other actor name to be shown to the user
  string other_actor_name = 9;

  // image url of the other actor
  string other_actor_image_url = 10;

  // bg colour for the other actor chat head in case image url is not present
  string other_actor_bg_colour = 11;
  // remarks for the mandate creation
  string remarks = 12;
  // urn to be passed for QR/Intent based payments
  string urn = 13;

  // autoPay details to be shown to the user
  // e.g. start date, end date, frequency, amount etc.
  api.typesv2.recurring_payment.AutoPayDetails auto_pay_details = 14;
  // In the case of a recurring UPI mandate, user can modify the autopay amount in following scenarios:
  // - If the mandate QR URL includes 'am' parameter with multiple amount values, separated by '|'.
  // - If the mandate QR URL contains both 'am' and 'mam' parameters. User can manually enter any amount between 'am' and 'mam'.
  // Client should show the change amount option only if this component is present in screen options
  ChangeAmountComponent change_amount_component = 15;
  // checkbox to take consent from user
  // for eg: For upi mandates, explicit consent has to be taken from user
  // regarding acceptance of merchant T&C and passing mobile number to merchant
  api.typesv2.common.ui.widget.CheckboxItem consent_checkbox = 16;

  // app detected initiation mode for the recurring payment
  string app_detected_initiation_mode = 17;
}

// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=28966-28171&t=HT0UyDWj1AfQVjRZ-1
message ChangeAmountComponent {
  // title for the cta. eg: "Change Amount"
  api.typesv2.common.Text cta_title = 1;
  // Change amount bottom sheet to be shown on clicking the cta
  ChangeAmountBottomSheet change_amount_bottom_sheet = 2;
}

message ChangeAmountBottomSheet {
  // Title of the bottom sheet (eg: "Pick your autopay plan" or "Enter your autopay amount")
  api.typesv2.common.Text title = 1;
  // Default autopay amount. Client can use this;
  // 1- to show default selected amount in PresetAmounts
  // 2- to show default amount in custom amount input field
  api.typesv2.Money default_amount = 2;

  // user can select an amount from the list of preset options or manually enter a custom amount.
  oneof change_amount_option {
    // List of preset amounts to choose from
    PresetAmounts preset_amounts = 3;
    // Input field for the user to enter a custom amount
    CustomAmountInput custom_amount_input = 4;
  }
}

// Bottom sheet with preset amounts
message PresetAmounts {
  // List of available preset amounts. User can choose one amount from this list
  repeated api.typesv2.Money amount_options = 1;
}

// Bottom sheet with a custom amount input field
message CustomAmountInput {
  // Text style for currency symbol and amount input field (font, text color etc)
  api.typesv2.common.Text amount_text_style = 1;
  // Minimum amount the user can enter ('mam' tag in QR url)
  api.typesv2.Money min_amount = 2;
  // Maximum amount the user can enter ('am' tag in QR url)
  api.typesv2.Money max_amount = 3;
  // Cta for user to confirm the entered amount
  frontend.deeplink.Cta continue_cta = 4;
}

// Execute one time recurring payment screen.
// screen : `ONE_TIME_EXECUTE_RECURRING_PAYMENT_ORDER_SCREEN`
// options: order_id : id to fetch payment details
message OneTimeExecuteRecurringPaymentScreenOptions {
  // order id for which the execution payment details needs to be fetched
  string order_id = 1;
}

// Account id for the connected account
// Client is expected to call account details API on receiving this deeplink and it's options
message ConnectedAccountDetailsScreenOptions {
  string account_id = 1;
}

// id of the exchanger offer
message RedeemExchangerOfferScreenOptions {
  string offer_id = 1;
}

// Collect missing gender data option
message CollectGenderScreenOptions {
  message GenderOption {
    api.typesv2.Gender gender = 1;
    string display_text = 2;
  }
  repeated GenderOption gender_options = 1;
  string title = 2;
  DataCollectionFlow flow = 3;
  DataCollectionFlowData flow_data = 4;
}

// Collect missing marital status data option
message CollectMaritalStatusScreenOptions {
  message MaritalStatusOption {
    api.typesv2.MaritalStatus marital_status = 1;
    string display_text = 2;
  }
  repeated MaritalStatusOption marital_status_options = 1;
  string title = 2;
  DataCollectionFlow flow = 3;
  DataCollectionFlowData flow_data = 4;
}

// Collect missing income slab data option
message CollectIncomeSlabScreenOptions {
  message IncomeSlabOption {
    api.typesv2.IncomeSlab income_slab = 1;
    string display_text = 2;
  }
  repeated IncomeSlabOption income_slab_options = 1;
  string title = 2;
  DataCollectionFlow flow = 3;
  DataCollectionFlowData flow_data = 4;
}

message WealthOnboardingCollectIncomeScreenOptions {
  string title = 1;
  int32 interval = 2;
  int32 min_val = 3;
  int32 max_val = 4;
  Cta done_cta = 5;
  DataCollectionFlow flow = 6;
  DataCollectionFlowData flow_data = 7;
}

// Collect missing signature data option
message CollectSignatureScreenOptions {
  string title = 1;
  DataCollectionFlow flow = 2;
  DataCollectionFlowData flow_data = 3;
  string camera_capture_hint = 4;
}

// Collect missing PAN data option
message CollectPanScreenOptions {
  string title = 1;
  DataCollectionFlow flow = 2;
  DataCollectionFlowData flow_data = 3;
}

// Collect missing POA data option
message CollectPoaScreenOptions {
  string title = 1;
  DataCollectionFlow flow = 2;
  DataCollectionFlowData flow_data = 3;
}

message SubscriptionHistoryScreenOptions {
  string subscription_id = 1;
}

// screen options for ONBOARDING_VKYC_STATUS_SCREEN during onboarding
message OnboardingVKYCStatusScreenOptions {
  string icon_url = 1;
  string title = 2;
  // html text to be supported by client
  string subtitle = 3;
  // list of possible ctas
  repeated Cta cta_list = 4;
  // cta_header is the screen specific text shown at the bottom of the screen
  string cta_header = 5;
  // text to be shown when vkyc is disabled
  string vkyc_next_available_start = 6;
}


// screen options for VKYC_STATUS_SCREEN
message VKYCStatusScreenOptions {
  // when false, client is expected to call GetVKYCStatus rpc to get the screen with screen options populated
  // when true, do not call GetVKYCStatus rpc
  bool ignore_rpc_call = 1 [deprecated = true];
  // VKYC entry point
  EntryPoint entry_point = 2 [deprecated = true];
  // icon/image to be displayed
  string icon_url = 3 [deprecated = true];
  // title to be displayed
  string title = 4 [deprecated = true];
  // html text to be supported by client
  string subtitle = 5 [deprecated = true];
  // list of possible ctas
  repeated Cta cta_list = 6 [deprecated = true];
  // deeplink sent by caller to redirect full kyc user
  // client forward this deeplink to request its optional
  // at time of implementation BE have fallback to generic message for full kyc users
  deeplink.Deeplink deeplink = 7 [deprecated = true];
  // deeplink sent by caller to redirect user when client receives call_finished event
  // at time of implementation client have fallback for document review screen
  deeplink.Deeplink call_finished_event_deeplink = 8 [deprecated = true];
  // entry point indicates caller for vkyc call attempt
  // to achieve backward compatibility we updated entry point to string
  string entry_point_str = 9;
  // blob containing deeplink used at entry exit of vkyc flow
  // passed by caller
  // structure for blob struct defined in pkg/vkyc/serialize_screen_option.go
  bytes blob = 10;
}

message VKYCNotificationScreenOptions {
  // notification type due to which notification is to be sent
  enum NotificationType {
    UNSPECIFIED = 0;
    // send notification when vkyc is avl inside business hours
    OUT_OF_BUSINESS_HOURS = 1;
  }
  NotificationType notification_type = 1;
}

message RecurrenceRule {
  AllowedFrequency allowed_frequency = 1;

  oneof RuleParams {
    // day on which recurring payment will be executed
    // To be filled only if allowed frequency is WEEKLY or MONTHLY
    // Eg : For recurrence pattern as WEEKLY and day as 2 payment execution will be made on every Tuesday
    // For recurrence pattern as MONTHLY and day as 10 payment will be executed on 10th of each month
    int32 day = 2;
  }

  // Parameter to define if payment is to be done on/before/after
  RecurrenceRuleType rule_type = 3;
}

enum AllowedFrequency {
  ALLOWED_FREQUENCY_UNSPECIFIED = 0;
  DAILY = 1;
  WEEKLY = 2;
  MONTHLY = 3;
  AS_PRESENTED = 4;
  ONE_TIME = 5;
  FORTNIGHTLY = 6;
  BI_MONTHLY = 7;
  QUARTERLY = 8;
  HALF_YEARLY = 9;
  YEARLY = 10;
}

enum RecurrenceRuleType {
  // unspecified
  RECURRENCE_RULE_TYPE_UNSPECIFIED = 0;
  // payment to be done on the recurring payment day
  ON = 1;
  // payment to be done before the recurring payment day
  BEFORE = 2;
  // payment to be done after the recurring payment day
  AFTER = 3;
}

// screen option for MUTUAL_FUND_DETAILS_SCREEN
message MutualFundDetailsScreenOptions {
  string mutual_fund_id = 1;
  frontend.investment.mutualfund.clientstates.MutualFundListEntryPoint entry_point = 2;
}

// screen option for INVESTED_FUND_DETAILS_SCREEN
message InvestedFundDetailsScreenOptions {
  string mutual_fund_id = 1;
  frontend.investment.mutualfund.clientstates.MutualFundListEntryPoint entry_point = 2;
}

// Deep link options to render the search results view more screen
message SearchFinancialActivitiesViewMoreOptions {
  // The user entered search query, from which this financial activity was displayed
  string search_query = 1;
  // fip-id for which this financial activities was displayed
  string fip_id = 2;
  // Transaction Filters
  repeated frontend.search.meta.TransactionFilter transaction_filters = 3;
}

message VKYCIncompatibleDeviceScreenOptions {
  string weblink = 1;
  // text to be shown to user denoting when the link will expire
  string weblink_expiry_text = 2;
  // list of possible ctas
  repeated Cta cta_list = 3;
  // cta_header is the screen specific text shown at the bottom of the screen
  string cta_header = 4;
}

message CreditReportConsentScreenOptions {
  bool is_user_pan_available = 1;
  bool is_b2b_user = 2;
  ToggleableScreenerCta cta = 3;
  deeplink.BackAction back_action = 4;
}

// screen option for WEALTH_ONBOARDING_INFO_SCREEN
message WealthOnboardingInfoScreenOptions {
  string title = 1;
  string description = 2;
  wealthonboarding.clientstate.WealthFlow flow = 3;
}

message RecurringPaymentDetailsScreenOptions {
  string recurring_payment_id = 1;
}

message AuthorizeRecurringPaymentScreenOptions {
  // Used to fetch recurring payment details and allow user to authorize payment
  // This will also be used to dismiss invalid notifications. Dismissible notifs will have ref id {recurring_payment_id}_DISMISSIBLE
  string recurring_payment_id = 1;
}

// Enum defining various type of flows for DIGILOCKER_DOWNLOAD.
// Primarily used to set UI.
enum DigilockerFlow {
  DIGILOCKER_FLOW_UNSPECIFIED = 0;
  DIGILOCKER_FLOW_WEALTH_ONBOARDING = 1;
}

message DigilockerFlowData {
  oneof data {
    wealthonboarding.clientstate.WealthFlow wealth_flow = 4;
  }
}

message DigilockerDownloadScreenOptions {
  // login_url and callback_url are used if the user has digilocker account
  // no digilocker account button should be implemented on the client to do appropriate next action based on the flow
  // 1. DIGILOCKER_FLOW_WEALTH_ONBOARDING - call CollectDataFromCustomer rpc with has_digilocker_account as false
  string login_url = 1;
  string callback_url = 2;
  // flow and flow_data indicating the flow from which digilocker screen is being shown, currently only wealth onboarding flow
  DigilockerFlow flow = 3;
  DigilockerFlowData flow_data = 4;
  // title and description for the screen
  string title = 5;
  string description = 6;
  string illustration_url = 7;
  string sub_title = 8;
  string security_info = 9;

  // to reinforce user to act on the screen
  // e.g. "INSTANT APPROVAL"
  string reinforcement_text = 10;
}

// Enum defining various type of flows for data collection screens.
// Primarily used to set UI.
enum DataCollectionFlow {
  DATA_COLLECTION_FLOW_UNSPECIFIED = 0;
  DATA_COLLECTION_FLOW_WEALTH_ONBOARDING = 1;
  // used in CollectNomineeDetailsScreenOptions for deposit creation flow nominee selection
  DATA_COLLECTION_FLOW_DEPOSIT_CREATION = 2;
}


message DataCollectionFlowData {
  oneof data {
    wealthonboarding.clientstate.WealthFlow wealth_flow = 4;
  }
}

message OnetimeMutualFundInvestmentOptions {
  string mutual_fund_id = 1;
  api.typesv2.Money amount = 2;
}

message StoryScreenOptions {
  // story title
  string story_title = 1;
  // story url
  string story_url = 2;
  // story id
  string story_id = 3;
}


message FitttSportsChallengeHomeScreenOptions {
  string tournament_id = 1;
  string entry_point_id = 2;
}

message FitttSportsChallengeDetailsScreenOptions {
  string match_id = 1;
}

// screen option corresponding to a information pop-up to be displayed on the app
// Ref- https://www.figma.com/file/FUObqwCZu7ShU7fhPH7QgA/FFF-%2F-Home-%26-Summary-%2F-Feb-2022?node-id=296%3A3673
message InformationPopupOptions {
  // brief description regarding the information present in the pop-up body
  // use 'text_title' instead
  string title = 1 [deprecated = true];

  // optional: sub-heading for the information to be displayed
  // use 'text_sub_title' instead
  string sub_title = 2 [deprecated = true];

  // detailed descriptive information body
  // use 'bodies' instead
  string body = 3 [deprecated = true];

  // url for the icon to be displayed in the pop-up. Deprecated in favor of VisualElement icon
  string icon_url = 4 [deprecated = true];

  // ctas to be displayed on the pop-up screen
  // cta ideally are either used to dismiss the pop-up of redirect the
  // user to a different screen when clicked.
  repeated Cta ctas = 5;

  // A dismissible pop-up can be closed by the user without explicit click on CTA.
  //  a) It can be closed by either clicking on "cross" button on the top-left of the popup dialogue
  //  b) It can be closed by a back press or even by tapping outside the popup dialogue window.
  // A non-dismissible pop-up is opposite of above scenario and is generally used to force user to take
  // some action on the app. e.g. force upgrades, complete vkyc to activate account, etc.
  bool is_non_dismissible = 6;
  // brief description regarding the information present in the pop-up body
  // Deprecated in favor of [Info]
  api.typesv2.common.Text text_title = 7 [deprecated = true];
  // optional: sub-heading for the information to be displayed
  // Deprecated in favor of [Info]
  api.typesv2.common.Text text_sub_title = 8 [deprecated = true];
  // detailed descriptive information body.
  // Each body content would be displayed as separate boxes
  // Deprecated in favor of [Info]
  repeated api.typesv2.common.Text body_texts = 9 [deprecated = true];
  // background color for the entire pop up
  string bg_color = 10;

  // display screen for the service requesting pop-up
  enum DisplayScreen {
    DISPLAY_SCREEN_UNSPECIFIED = 0;
    DISPLAY_SCREEN_LAMF = 1;
    DISPLAY_SCREEN_TIERING_EARNED_BENEFITS = 2;
  }

  DisplayScreen display_screen = 11;
  // icon to be shown in the popup
  api.typesv2.common.VisualElement icon = 12;

  // Section contains title, subtitle, body texts which we will use to display list of infos
  // Figma for reference : https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-7315&mode=design&t=mCNcsNq4utKDPmWs-4
  message Info {
    // brief description regarding the information present in the pop-up body
    api.typesv2.common.Text text_title = 1;
    // optional: sub-heading for the information to be displayed
    api.typesv2.common.Text text_sub_title = 2;
    // detailed descriptive information body.
    // Each body content would be displayed as separate boxes
    repeated api.typesv2.common.Text body_texts = 3;
  }
  // List of sections with a separator
  repeated Info info_list = 13;
  // Divider color to be displayed between sections.
  // Note : If no valid color sent, then divider will not be drawn in client
  string divider_color = 14;
  // gradient background color
  api.typesv2.common.ui.widget.BackgroundColour gradient_bg_color = 15;
}

message WealthOnboardingDataCollectionInfoScreenOptions {
  string title = 1;
  string description = 2;
  // redirect user screen to appropriate one with cta
  Cta cta = 3;
  wealthonboarding.clientstate.WealthFlow flow = 4;
}

// Options to load transactions for a particular account only in all transactions screen
message AllTransactionsAccountFilterOptions {
  // Primary id of the account
  string account_id = 1 [deprecated = true];
  // account type
  accounts.Type account_type = 2;
  // derived account id for which txn needs to be fetched
  string derived_account_id = 3;

  TransactionFlowType transaction_flow_type = 4;

  enum TransactionFlowType {
    TRANSACTION_FLOW_TYPE_UNSPECIFIED = 0;
    TRANSACTION_FLOW_TYPE_DEBIT_CARD = 1;
    TRANSACTION_FLOW_TYPE_CREDIT_CARD = 2;
    TRANSACTION_FLOW_TYPE_ALL_TRANSACTIONS = 3;
    TRANSACTION_FLOW_TYPE_DEPOSITS = 4;
  }
}


// screen option for MUTUAL_FUNDS_LIST_VIEW_SCREEN
message MutualFundsListScreenOptions {
  frontend.investment.mutualfund.clientstates.MutualFundListEntryPoint entry_point = 1;
}

// this will be used to show a block on information
// by default info block will be collapsable
message InfoBlock {
  string title = 1;
  string desc = 2;
  repeated InfoItem info_items = 3;
}

// this will be used to show info like bullet points or title description with icon
message InfoItem {
  string icon = 1 [deprecated = true];
  // will handle title color using html tags
  string title = 2 [deprecated = true];
  string desc = 3 [deprecated = true];
  InfoToolTip tool_tip = 4;
  bool copy_allowed = 5;
}

message InfoToolTip {
  string icon_url = 1;
  InfoBlock info = 2;
}

message P2PInvestmentEligibilityCheckScreenOptions {
  message WaitingInfo {
    string text = 1;
    string footer_text = 2;
    repeated InfoItem fyiList = 3;
  }
  string title = 1;
  // image url to show % roi
  string sub_title_image_url = 2;
  repeated InfoItem desc = 3;
  // this need to be shown as link with text
  // only text and deeplink should be used
  Cta know_more_cta = 4;
  // deprecated in favour of consent_check_box
  string consent_text = 5 [deprecated = true];
  string check_eligibility_button_text = 6;
  WaitingInfo waiting_info = 7;
  // tiles to show clickable tile like know more and unlock info
  repeated Tile bottom_info_tiles = 8;
  // used to display partnership info below sub_title_image
  string partnership_image_url = 9;
  // deprecated in favour of consent_check_box
  string tnc_url = 10 [deprecated = true];
  message TopBanner {
    string title = 1;
    string title_text_color = 2;
    string background_color = 3;
  }
  // top banner to show on top of the screen
  // for now the use case is to show info for slots like `SLOTS CLOSING IN 3 DAYS`
  TopBanner top_banner = 11;

  // Jump V2
  // Top Element
  P2PLandingValueProposition value_proposition = 12;
  // Explainer component
  P2PLandingExplainer explainer = 13;
  // checkbox to be displayed if availble, otherwise should not be displayed
  api.typesv2.common.ui.widget.CheckboxItem consent_check_box = 14;
}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8873%3A12632
message P2PLandingValueProposition {
  // Image next to the text
  string img_url = 1;
  // Text for value proposition
  api.typesv2.common.Text text = 2;
}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A11602
message P2PLandingExplainer {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text description = 2;
  // Cta to redirect to story explaining jump
  Cta story_explainer = 3;
  message ImageWithDescription {
    api.typesv2.common.Image img = 1;
    repeated api.typesv2.common.Text description = 2;
  }
  repeated ImageWithDescription img_with_description = 4;
}

// this will be used to show display element like cta with icon
message Tile {
  string icon = 1;
  // only text and CTA to be used
  Cta cta = 2;
}

message P2PInvestmentEligibilityCheckResultScreenOptions {
  string icon = 1;
  string title = 2;
  string sub_title = 3;
  // list of benefits with title
  InfoBlock benefits = 4;
  Cta cta = 5;
  // background color to accommodate the case where bg image is not passed
  string default_bg_color = 6;
  // tiles to show clickable tile like know more and unlock info
  repeated Tile bottom_info_tiles = 7;
  // used to support auto redirect to deeplink given in cta
  message AutoRedirectInfo {
    // signifies if auto redirect needs to be done or not
    // default is false, i.e no auto redirect
    bool do_auto_redirect = 1;
    // delay in seconds after which redirection needs to be done
    int32 auto_redirect_delay_in_secs = 2;
  }
  // redirection info using which app needs to redirect to deeplink in cta
  AutoRedirectInfo auto_redirect_info = 8;
  // if bg image url present then use it or fallback to default_bg_color
  string bg_image_url = 9;
  // used to display partnership info at the bottom of the screen
  string partnership_image_url = 10;
  // to show border around benefits
  string benefits_border_color = 11;
  // notify info string for the cases when CTA is not given
  // this should be used only when cta is not given
  string notify_info_string = 12;
  // flag to show confetti
  bool show_confetti = 13;
  message TopBanner {
    string title = 1;
    string title_text_color = 2;
    string background_color = 3;
  }
  // top banner to show on top of the screen
  // for now the use case is to show info for slots like `SLOTS CLOSING IN 3 DAYS`
  TopBanner top_banner = 14;

  // Jump V2
  // Top Element
  P2PLandingValueProposition value_proposition = 15;
  // Explainer component
  P2PLandingExplainer explainer = 16;
  // event properties that should be sent by the client along with P2pIntroScreenLoaded event
  // this will contain prpoerties like unlocked plans, eligibility state etc
  // ref - https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit#gid=1102015672
  map<string, string> event_properties = 17;
}

// will be used to power more info on p2p investment
message P2PInvestmentKnowMoreScreenOptions {
  string icon = 1;
  string title = 2;
  repeated InfoBlock additional_info = 3;
}

// will be used to power show prerequisites for p2p investment
message P2PInvestmentUnlockAccessScreenOptions {
  string icon = 1;
  string title = 2;
  string sub_title = 3;
  // list of constraints or requirements
  repeated InfoItem prerequisites = 4;
  // bottom note that show how customers can increase their chance to get access to jump
  string note = 5;
}

// TODO: Remove after July, 2023
message P2PInvestScreenOptions {
  option deprecated = true;
  // to be used at client for computing maturity amount
  float interest_rate = 1;
  // Minimum allowed amount for p2p investment
  api.typesv2.Money min_allowed_amount = 2;
  // Maximum allowed amount for p2p investment
  api.typesv2.Money max_allowed_amount = 3;
  // Represents message shown on top of the screen, something like "Invest in Jump"
  string title = 5;
  // Represents message shown on top of the screen and below title. Could hold information about investment passes
  // deprecated in jump v2
  string sub_title = 6 [deprecated = true];
  // Represents the screen area holding details about maturity amount, maturity duration, etc.
  // deprecated in jump v2
  InfoItem growth_details = 7 [deprecated = true];
  // Represents the screen area holding details about withdrawal policies
  // deprecated in jump v2
  repeated InfoItem withdrawal_details = 8 [deprecated = true];
  // Represents footer message holding info about masked operative/savings account
  string operative_account_message = 9;
  // current account balance of customer's saving account
  api.typesv2.Money current_balance = 10;
  string insufficient_balance_text = 11;
  // CTA to be appended to insufficient balance text - https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/P2P-%2F-FFF?node-id=6329%3A8987
  // On tap of CTA, leads to add funds flow. Post completion of add funds, user should come back to P2P flow
  Cta insufficient_balance_cta = 12;
  // It represents the info for the particular plan, for eg. if early withdrawal allowed, max returns etc.
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A12893
  string info = 13;
  // e.g: "1 year", "Up to 9% p.a", "Withdraw early with a fee"
  repeated P2PInvestInfoChip info_chips = 14;
  // badge image url
  // e.g: Long-term 9%
  string badge_image = 15;
  // e.g: "In an year, your money will jump up to"
  api.typesv2.common.Text maturity_amount_title = 16;
  // e.g: "Swipe to Invest"
  string invest_button_text = 17;
  // e.g: "I understand that I cannot withdraw this amount for 1 month"
  // if not given, then no checkbox will be shown
  P2PCheckBoxOption consent_checkbox = 18;
  // scheme in which user wants to invest
  // supported schemes right now: SCHEME_NAME_LL_FLEXI, SCHEME_NAME_LL_SHORT_TERM, SCHEME_NAME_LL_LONG_TERM
  string scheme_name = 19;
  // Scheme tenure in months.
  // For 9% and 7% scheme this would be 12
  // For 8% it would be 3
  // To be used for calculating maturity amount
  int32 scheme_tenure_in_months = 20;
}

// P2PCheckBoxOption is used to represent a checkbox option
message P2PCheckBoxOption {
  option deprecated = true;
  string text = 1;
  // if true, then this option is selected by default
  bool is_selected = 2;
}

// P2PInvestInfoChip is used to show info chips on P2P Invest screen
message P2PInvestInfoChip {
  option deprecated = true;
  api.typesv2.common.Text title = 1;
  // if empty, then no (i) icon will be shown
  // if not empty, then (i) icon will be shown and on tap of it, a center sheet will be shown with the P2PInfoBox content
  P2PInfoBox info_box = 2;
}

message P2PInfoBox {
  option deprecated = true;
  string title = 1;
  string description = 2;
  string image_url = 3;
}

message P2PWithdrawInvestmentScreenOptions {
  // Represents message shown on top of the screen, something like "Withdraw investment"
  string title = 1;
  string sub_title = 2;
  // amount which can be withdrawn without any penalty
  api.typesv2.Money penalty_free_withdrawal_amount = 3;
  // total amount that can be withdrawn
  api.typesv2.Money total_withdrawable_amount = 4;
  // Represents footer message holding info about masked operative/savings account
  string operative_account_message = 5;
  // Penalty message to be shown if amount selected is more than penalty free amount
  string penalty_message = 6;
  // the below field is deprecated
  deeplink.Deeplink info_deeplink = 7 [deprecated = true];
  // represents the i icon on the withdraw screen
  repeated InfoItem info_item = 8;
  // warning message to be shown on 2nd screen of withdrawal
  string warning_message = 9;
}

// Deeplink options for connected account benefits screen
message ConnectedAccountBenefitsOptions {
  // Proceed button on benefits screen with deeplink
  Cta proceed_cta = 1;
  // will be empty if wealth Tnc consent is not required
  // otherwise message with wealth Tnc link
  string wealth_tnc_message = 2 [deprecated = true];
  // TnC checkbox for epifi wealth
  api.typesv2.common.ui.widget.CheckboxItem wealth_tnc_check_box = 3;
  // TnC checkbox for Fi lite users
  api.typesv2.common.ui.widget.CheckboxItem fi_lite_check_box = 4;
  // ca_flow_name represents identifier of other service which is trying to use connected account flow
  // The value for this string will the identifier for service which is trying to use CA flow
  string ca_flow_name = 5;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 6;
}

// Deeplink options for savings account summary on home
// All accounts including connected accounts are shown to th user here
message SavingsAccountsHomeSummaryOptions {}

// options for CONNECTED_ACCOUNT_HANDLE_REOOBE deeplink
message ConnectedAccountHandleReoobeOptions {
  // Details of the account which are needed by client to show on UI
  // when user is shown the options to handle phone number re onboarding on connect account flow
  message AccountDetail {
    string account_id = 1;
    string masked_account_number = 2;
    string account_type = 5;
    string fip_logo_url = 7;
    string fip_name = 8;
    string fi_type = 4;
  }
  repeated AccountDetail account_detail_list = 1;
  // Post successful completion of the re-oob operation this is the screen where user should be taken to.
  deeplink.Deeplink next_screen = 2;
  // bottom-sheet title
  api.typesv2.common.Text title = 3;
  // bottom-sheet info body details
  api.typesv2.common.Text body = 4;
  // footer for connected accounts T&C
  api.typesv2.common.Text footer = 5;
  // cta list for proceed and back
  repeated Cta cta_list = 6;
}

// Screen options for screen CONNECTED_ACCOUNT_TRANSACTIONS_COMING_SOON
message ConnectedAccountTransactionsComingSoonOptions {
  string title = 1;
  string sub_title = 2;
  Cta cta = 3;
}

message P2PInvestmentErrorScreenOptions {
  // screen title shown on the top the screen
  string title = 1;
  // error specific icon url
  string icon_url = 2;
  // cause/reason for the error
  string reason = 3;
  // cause/reason description for the error
  string reason_desc = 4;
}

// options for WEALTH_ONBOARDING_DATA_COLLECTION_ERROR_SCREEN deeplink
message WealthOnboardingDataCollectionErrorScreenOptions {
  string title = 1;
  string description = 2;
  // any additional hints that we want to show to the user when they try to recapture the same data so that data capture succeeds
  repeated string additional_hints = 3;
  // try_later cta redirects user to WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN for capturing other missing data
  Cta try_later = 4;
  // retry cta redirects user back to capturing the data screen
  Cta retry = 5;
  wealthonboarding.clientstate.WealthFlow flow = 6;
}

message VkycInstructionScreenOptions {
  Call call = 1;
  // added new field for call options as onboarding needs list of vkyc flows supported
  // same field will be used for normal flow as well
  repeated Call call_options = 2;
  // true if ekyc is expired based on which client will initiate ekyc
  bool is_ekyc_expired = 3;
  // text denoting when vkyc is next available at
  string vkyc_next_available_start = 4;
  // true denotes client to call GetVKYCInfo rpc before processing the deeplink
  bool get_vkyc_info = 5;
  EntryPoint entry_point = 6;
  // title will be of html type contains color also
  string title = 7;
  // Description for the page
  string description = 8;
  // background color of whole page in hex color
  string background_color_hex = 9;
  // blocks
  repeated VkycInstructionPageBlock vkyc_instruction_page_block = 10;
  // CTA
  repeated Cta cta_list = 11;
  // bottom description of the instruction screen
  string bottom_description = 12;
  // bottom sheet have message to be shown when user press back button on instruction page
  VKYCBottomSheet vkyc_bottom_sheet = 13;
  // skip timer denotes seconds after which skip option is enable on client side (Applicable only in case of call options has skip flag)
  int32 skip_option_timer = 14;
}

message VkycInstructionPageBlock {
  // block image
  string image = 1;
  // block title
  string title = 2;
  // background color of collapsed block
  string background_color_hex_collapse = 3;
  // header text for block
  string header_text = 4;
  // checkbox text
  string checkbox_text = 5;
  // subtitle
  string subtite = 6;
  // background color of expanded block
  string backgroud_color_hex_expanded = 7;
}

message VkycPreRequisiteScreenOptions {
  // title of pre-requisite screen in html format
  string title = 1;
  // description in the screen in html format
  string subtitle = 2;
  // alias for income edit screen
  string income_interval_alias = 3;
  // gap between income edit option
  int32 income_interval = 4;
  // max income for income slider
  int32 max_income = 5;
  // cta
  repeated Cta cta_list = 6;
  // alias for occupation in pre-requisite screen
  string occupation_alias = 7;
  // alias for occupation list
  string occupation_list_alias = 8;
  // occupation list
  repeated EmploymentTypeInfo occupation_list = 9;
  // alias for annual income
  string annual_income_alias = 10;
  // initial palceholder for annual income
  string annual_income_placeholder = 11;
  // entry point in pre-requisite screen
  EntryPoint entry_point = 12;
}

message PayViaBankTransferScreenOptions {
  // PayViaBankTransferFlow enum denotes entry point of the screen
  enum PayViaBankTransferFlow {
    BANK_TRANSFER_FLOW_UNSPECIFIED = 0;
    BANK_TRANSFER_FLOW_PAY = 1;
    BANK_TRANSFER_FLOW_MIN_KYC_ACCOUNT_CLOSURE = 2;
    // enum to show bank transfer flow via fit. On this enum FIT will populate the add payee page on frontend.
    BANK_TRANSFER_FLOW_VIA_FIT = 3;
  }
  PayViaBankTransferFlow pay_via_bank_transfer_flow = 1;
}

message AccountClosureTransferInitiatedScreenOptions {
  string image_url = 1;
  string title = 2;
  string subtitle = 3;
  // list of CTAs to be displayed
  repeated Cta ctas = 4;
  // cta_header is the screen specific text shown at the bottom of the screen
  string cta_header = 5;
  message KnowMoreObject {
    string title = 1;
    string subtitle = 2;
  }
  KnowMoreObject know_more_object = 6;
  message AccountDetails {
    // contains text in form "The alternate account ifsc is XYZ"
    api.typesv2.common.Text line1 = 1;
    api.typesv2.common.Text line2 = 2;
  }
  AccountDetails account_details = 7;
}

message AnalyserScreenOptions {
  string analyser_name = 1;
  // base64 encoded json of GetAnalyserRequest containing required
  // lenses and selected filter values.
  string encoded_request = 2;
}

// do not use: deprecated in favor of web screen options
message FinvuAccountDisconnectOptions {
  string web_url = 1;
}

// used to confirm address during LL flow
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=4321%3A153632&t=8r8CkVDIkiosdHWL-1
message PreApprovedAddressConfirmationScreenOptions {
  InfoItem header_info_item = 1 [deprecated = true];
  string customer_name = 2 [deprecated = true];
  Cta continue_cta = 3;
  string confirmation_text = 4;
  string loan_request_id = 5;
  preapprovedloan.pal_enums.LoanHeader loan_header = 6;
  api.typesv2.common.Text toolbar_title = 7;
  // to show user type of address (communication address etc,)
  api.typesv2.common.Text address_type = 8;
  api.typesv2.common.Text customer_name_text = 9;
  api.typesv2.common.ui.widget.CheckboxItem use_address_checkbox = 10;

  // flag to be sent in the subsequent rpc call
  bool add_address_details_in_sync = 11;
  // flag to denote if we need to fetch the user's address or not on the screen
  // client will pass this flag for getting addresses
  bool skip_address_auto_fill = 12;
  // flag which denotes to not pre-fill the
  // current address from the address list on the screen
  bool skip_pre_filled_address = 13;

  // Used to show the disclaimer banner below address input field
  DisclaimerComponent disclaimer_banner = 14;

  api.typesv2.common.VisualElement header_image = 15 [deprecated = true];

  api.typesv2.common.Text selected_address_style = 16;
  // This will be passed to next screen while opening the add address screen
  api.typesv2.common.Text add_address_title = 17;

  InfoItemV3 header_info_item_v2 = 18;

  api.typesv2.common.Text address_in_focus_text = 19;

  api.typesv2.common.Text bottom_info_message = 20;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=52473-174907&t=Tvc1hYTtkPQoBYO7-4
message DisclaimerComponent {
  api.typesv2.common.VisualElement icon = 1;
  api.typesv2.common.Text title = 2;
  deeplink.Deeplink deeplink = 3;
  string bg_color = 4;
}

// offer details screen for pre approved loan
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3054
message PreApprovedLoanOfferDetailsScreenOptions {
  message OfferInfo {
    api.typesv2.Money min_loan_amount = 1;
    api.typesv2.Money max_loan_amount = 2;
    double interest_rate = 3;
    int32 min_tenure_in_months = 4;
    int32 max_tenure_in_months = 5;
    api.typesv2.Money max_emi_amount = 6;
    // APR Rate which has the rate
    // and tooltip details
    InfoItem apr_rate = 7;
  }
  message LoanInfo {
    api.typesv2.Money amount = 1;
    int32 tenure_in_months = 2;
    api.typesv2.Money disbursal_amount = 3;
    api.typesv2.Money emi_amount = 4;
    Deductions deductions = 5;
    Constrains constrains = 6;
    message Deductions {
      api.typesv2.Money total_deductions = 1;
      api.typesv2.Money gst = 2;
      api.typesv2.Money processing_fee = 3;
      api.typesv2.Money advance_interest = 4;
    }
    message Constrains {
      api.typesv2.Money min_loan_amount = 1;
      api.typesv2.Money max_loan_amount = 2;
      int32 min_tenure_in_months = 3;
      int32 max_tenure_in_months = 4;
    }
  }
  string offer_id = 1;
  OfferInfo offer_info = 2;
  LoanInfo loan_info = 3;
  string partnership_url = 4;
  Button submit_cta = 5;
  bool is_constraint_refresh_needed = 6;
  preapprovedloan.pal_enums.LoanHeader loan_header = 7;
  InfoItemV2 bottom_message = 8;
  api.typesv2.common.Text toolbar_title = 9;
  api.typesv2.common.Text bottom_sheet_title = 10;
  api.typesv2.common.Text deduction_info = 11;
  api.typesv2.common.Text amount_selection_label = 12;
  api.typesv2.common.Text tenure_selection_label = 13;
  api.typesv2.common.Text tenure_container_label = 14;
  api.typesv2.common.Text emi_container_label = 15;
  api.typesv2.common.Text interest_label = 16;
  api.typesv2.common.Text api_label = 17;
  InfoItemV2 deduction_tooltip = 18;
}

message PreApprovedLoanOfferDetailsV2ScreenOptions {}

message Button {
  api.typesv2.common.Text text = 2;
  // bool to identify whether wrap the cta according to its content or match the parent
  bool wrap_content = 3;
  Padding padding = 4;
  Margin margin = 5;
  Cta cta = 6;
  message Padding {
    int32 left_padding = 1;
    int32 right_padding = 2;
    int32 top_padding = 3;
    int32 bottom_padding = 4;
  }
  message Margin {
    int32 left_margin = 1;
    int32 right_margin = 2;
    int32 bottom_margin = 3;
    int32 top_margin = 4;
  }
}

// can be used to create custom info item where title, subtitle and desc text are customizable
message InfoItemV2 {
  string icon = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text desc = 3;
  InfoToolTip tooltip = 4;
  bool copy_allowed = 5;
  api.typesv2.common.Text sub_title = 6;
  TextWithHyperlinks hyper_link_sub_title = 7;
  VisualElementCta icon_cta = 8;
  InfoType info_type = 9;
  string bg_color = 10;

  // To be used in the DC order physical card component
  // https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=19160-53175&t=mrj0LRLUqq4QPeQu-4
  AdditionalInfo additional_info = 11;

  message AdditionalInfo {
    api.typesv2.common.Text striked_off_amount = 1;
    api.typesv2.common.Text final_amount = 2;
    api.typesv2.common.Text discount_badge = 3;
  }
}

message InfoItemV3 {
  api.typesv2.common.VisualElement icon = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text desc = 3;
  InfoToolTipV2 tooltip = 4;
  bool copy_allowed = 5;
  api.typesv2.common.Text sub_title = 6;
  api.typesv2.common.ui.widget.BackgroundColour bgColor = 7;
}

message InfoToolTipV2 {
  api.typesv2.common.VisualElement icon = 1;
  InfoBlockV2 info = 2;
}

// this will be used to show a block on information
// by default info block will be collapsable
message InfoBlockV2 {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text desc = 2;
  repeated InfoItemV2 info_items = 3;
  api.typesv2.common.VisualElement dialog_icon = 4;
}

// application details screen for pre approved loan
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2844%3A3122
message PreApprovedLoanApplicationDetailsScreenOptions {
  string offer_id = 1;
  string partnership_url = 2;
  LoanInfo loan_info = 3;
  EmiInfo emi_info = 4;
  TncInfo tnc_info = 5;
  preapprovedloan.pal_enums.LoanHeader loan_header = 6;
  InfoItemV2 benefit_text = 7;
  InfoItemV2 guideline_text = 8;
  api.typesv2.Money loan_amount = 9;
  int32 loan_tenure = 10;
  api.typesv2.common.VisualElement partnership_logo = 11;
  api.typesv2.common.Text toolbar_title = 12;
  message LoanInfo {
    InfoItem loan_amount = 1;
    InfoItem fee_and_gst_charges = 2;
    InfoItem advance_interest = 3;
    InfoItem amount_to_be_disbursed = 4;
  }
  message EmiInfo {
    InfoItem interest = 1;
    InfoItem tenure = 2;
    InfoItem total_amount_payable = 3;
    InfoItem emi_amount = 4;
    InfoItem emi_schedule = 5;
    InfoItem emi_deduction_source = 6;
    InfoItem apr_rate = 7;
    // represent emi info for a loan
    repeated InfoItem details = 8;
  }
  message TncInfo {
    string title = 1;
    InfoItem auto_pay_consent_info = 2 [deprecated = true];
    InfoItem tnc = 3 [deprecated = true];
    Cta cta = 4;
    repeated TermInfo term_infos = 5;
  }

  // This will give the terms and condition text
  // Along with deeplink if needed on click of text.
  message TermInfo {
    string term_text = 1;
    Cta term_cta = 2;
    bool is_term_clickable = 3;
    bool is_term_link_underlined = 4;
  }
}

// used to confirm loan application using OTP
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2830%3A3081
message PreApprovedLoanApplicationConfirmationViaOtpScreenOptions {
  // used to show the header on Loan OTP screen
  InfoItem header = 1;
  // waiting time before client will show resend otp option to the user
  int32 waiting_time_in_secs = 2;
  // represents the CTA to resend the OTP, client needs to call GenerateConfirmationCode API when a user clicks on resend
  // if nil, client doesn't send show Resend OTP at all.
  Cta resend_otp = 3;
  // loan request ID to be used in all BE calls
  string loan_request_id = 4;
  // flag to be used by client to auto read OTP from SMS or not.
  bool do_auto_read = 5;
  // used to show the center text on the OTP screen. e.g. "Last Step,Verify the OTP"
  InfoItem otp_info = 6;
  // flag to represent if OTP Generation API is to be called via client or not.
  // True when user continue with applying for loan from loan dashboard/management screen.
  // Need to call generate Otp from client to continue with the flow, hence, force generated will be true
  bool force_generate_otp = 7;
  // flag to represent if otp is already generated via BE
  // used when Otp is generated from BE and client doesn't need to call generate Otp again
  bool is_otp_generated = 8;
  // message to be shown to user in case of incorrect otp
  // if empty, user hasn't entered an incorrect OTP or user hasn't entered any OTP yet
  string incorrect_otp_message = 9;
  // message displayed along with resend Cta
  string resend_message = 10;
  preapprovedloan.pal_enums.LoanHeader loan_header = 11;
  // auth token for the generated OTP.
  // If OTP is generated internally, this field will not be empty and is to be used in verifying OTP
  string token = 12;
  api.typesv2.common.Text toolbar_title = 13;
  // optional fields to define otp verification journey status, will be used where multiple OTPs are required for verification.
  // otp_steps_info -- to show the sequence number of the current otp out of the total OTPs to be sent
  // otp_identifier -- phone number/email to which current otp is sent
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=356-46489&mode=dev
  api.typesv2.common.Text otp_steps_info = 14;
  api.typesv2.common.Text otp_identifier = 15;
  // length of otp to be entered by user
  int32 otp_length = 16;
  string loan_step_execution_id = 17;
  // this field can be used to override the analyticdscreen name
  analytics.AnalyticsScreenName analytics_screen_name = 18;
  // flag to be used to show the resend OTP option
  bool hide_resend_otp = 19;
  // to identify type of request for otp generation
  preapprovedloan.pal_enums.OtpFlow otp_flow = 20;
}

// used to show loan application status
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2801%3A34673
message PreApprovedLoanApplicationStatusScreenOptions {
  InfoItem loan_status = 1 [deprecated = true];
  Cta cta = 2 [deprecated = true];
  string loan_request_id = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
  string bg_color = 5;
  oneof AdditionalInfo {
    InfoItem emi_info = 6 [deprecated = true];
    InfoItemV2 emi_info_v2 = 7 [deprecated = true];
    InfoItemV3 emi_info_v3 = 8;
  }
  // intentional index gap
  InfoItemV2 loan_status_v2 = 11 [deprecated = true];
  Button button = 12;
  bool show_lottie = 13;

  InfoItemV3 loan_status_v3 = 14;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=2801%3A34647
message PreApprovedLoanApplicationStatusPollScreenOptions {
  string loan_request_id = 1;
  // retry count will be passed to the client to specify the number of retries already done
  // client will increment this count and pass it in the status poll API so that backend
  // can control the next delay accordingly
  int32 retry_attempt_number = 2;
  int32 retry_delay = 3;
  InfoItemV2 polling_text = 4;
  preapprovedloan.pal_enums.LoanHeader loan_header = 5;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 6;
  // total duration for which client should keep polling.
  // if no other screen is returned before this time then client will redirect user back to loans landing screen
  int32 retry_duration = 7;
  // image/lottie to be shown on the centre of the page just above the polling text.
  api.typesv2.common.VisualElement centre_icon = 8;
  // if true, then client should hide the polling loader
  bool hide_loader = 9;
  // cta to be rendered at the bottom of the screen
  // if empty, hide
  frontend.deeplink.Button bottom_cta = 10;
  // flag to be used if next action needs to be fetched in sync using the sync proxy workflow.
  // NOTE : WorkflowId is mandatory to be passed in case this flag is set to true.
  bool get_next_action_in_sync = 11;
  // deprecated as this can be fetched using loan_request_id
  string workflow_id = 12 [deprecated = true];
}

// Screen options for Enach
// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=3503%3A95604&t=dwLExwCMBhglJV3e-1
message PreApprovedLoanInitiateMandateScreenOptions {
  InfoItem header_info_item = 1;
  repeated InfoItem bullet_points = 2;
  Cta continue = 3;
  string loan_request_id = 4;
  string bottom_sheet_text = 5;
  string entry_url = 6;
  string exit_url = 7;
  deeplink.Deeplink next_screen = 8;
  api.typesv2.common.PhoneNumber contact_number = 9;
  string mandate_id = 10;
  preapprovedloan.pal_enums.LoanHeader loan_header = 11;
  InfoItemV2 disclaimerInfo = 12;
  repeated TermInfo termInfos = 13;
  string base64_encoded_html = 14;
  PlInitiateCtaAction cta_action = 15;
  api.typesv2.common.Text toolbar_title = 16;
  enum PlInitiateCtaAction {
    UNSPECIFIED = 0;
    E_SIGN_FLOW = 1;
    DIGIO_FLOW = 2;
    HTML_WEBPAGE_FLOW = 3;
  }
  // flag to show card details as overlay on top of the mandate view
  bool enable_card_details_overlay = 17;
  // title component to show the
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50734&mode=dev
  IconTextWidget mandate_overlay_title = 18;
  // Deeplink to nudge the user to continue the mandate process when user tries to go back
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50534&mode=dev
  // can repurpose BOTTOM_SHEET_INFO_VIEW for this since the UI structure is same
  Deeplink mandate_dropoff_deeplink = 19;
  // delay duration which client has to use after which cta has to be loaded
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50388&mode=dev
  google.protobuf.Duration view_card_details_cta_delay = 20;
  // debit card which client has to use to trigger pin verification and fetch card cvv details
  string debit_card_id = 21;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A12623
message PreApprovedLoanDashboardScreenOptions {
  // represents the top banner on the dashboard including aggregated information about all loans such as total EMI
  TopBanner top_banner = 1;
  // Basic Loan details for all loan accounts
  // represents both active and in-progress/in-active loan applications
  repeated LoanDetails loan_details = 2;
  // CTA to get another loan
  Cta get_another_loan_cta = 3;
  // represents the section to show basic details about past loans
  InfoItem past_loan_details = 4;
  // Tiles like FAQ at the bottom
  repeated Tile bottom_info_tiles = 5;
  string partnership_url = 6;
  preapprovedloan.pal_enums.LoanHeader loan_header = 7;
  // navigate to the deeplink on hardware back press or on tap of back cta. If this is empty, navigate back to previous screen.
  frontend.deeplink.Deeplink back_deeplink = 8;

  message TopBanner {
    string title = 1;
    string sub_title = 2;
    api.typesv2.Money total_emi_for_curr_month = 3;
    string icon_url = 4;
  }

  // Will be used to represent loan accounts, both active and in-progress/in-active ones
  message LoanDetails {
    // Title of the loan, e.g. Loan#1
    string title = 1;
    // Loan amount
    api.typesv2.Money loan_amount = 2;
    // represents state of the loan, e.g. Under Review, etc.
    LoanState loan_state = 3;
    // Used to represent tile holding tenure, interest rate, emi info
    repeated InfoItem details = 4;
    // In case the loan is active, it will hold emi details
    EmiDetails emi_details = 5;
    Cta more_details = 6;
    // For cases where the loan account application is in progress, this message will be shown
    InProgressMessage in_progress_message = 7;
    // To represent overflow items
    repeated Cta overflow_items = 8;
    // For case the loan is active, it holds the loan id
    string loan_id = 9;
    // added cta for reload button on loan application tile so as to enable user to refresh the status for their application
    InfoItemWithCta reload_cta = 10;
    message LoanState {
      // title will have html tag to represent the colour of the text
      string title = 1;
      string bg_colour = 2;
    }
    message EmiDetails {
      // holds the title for EMI details, e.g. EMI Payments
      string title = 1;
      repeated EmiEntity emi_entities = 2;

      message EmiEntity {
        string title = 1;
        InfoItem desc = 2;
        string desc_bg_colour = 3;
      }
    }
    message InProgressMessage {
      // icon to be shown beside the message
      string icon_url = 1;
      string message = 2;
      string bg_colour = 3;
      // will be nil for all cases, expect when loan application is ready for verification
      Cta next_step = 4;
    }
    // optional, denotes the text which needs to be shown under the title, in loanDetails section of dashboard screen e.g "Loan from Moneyview"
    // if this field is populated then loan_amount field, which currently occupies the subtitle position shouldn't be displayed.
    // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-39982&mode=design&t=pClRj4i6BwS5vQAk-4
    api.typesv2.common.Text subtitle = 11;
  }
}

message PreApprovedLoanLandingScreenOptions {
  // top banner to show on top of the screen
  // for now the use case is to show info like `FOR SELECTED USERS`
  TopBanner top_banner = 1;
  string title = 2;
  string icon_url = 3;
  repeated InfoItem details = 4;
  Cta cta = 5 [deprecated = true];
  string bg_colour = 6;
  // tiles to show clickable tile like FAQ and how to apply
  repeated Tile bottom_info_tiles = 7;
  string partnership_url = 8 [deprecated = true];
  string offer_id = 9;
  // for Early Salary
  InfoItemV2 past_loan_details = 10;
  preapprovedloan.pal_enums.LoanHeader loan_header = 11;
  Button next = 12;
  frontend.deeplink.InfoItemV2 toolbar_info = 13;
  InfoItemV2 additional_message = 14 [deprecated = true];
  InfoItemWithCtaV2 additional_message_with_cta = 15;
  BottomTextBanner bottom_text_banner = 16;
  api.typesv2.common.VisualElement partnership_logo = 17;

  // entry point from where landing screen was called.
  string entry_point = 18;

  // denotes the list of consents that needs to be mandatorily accepted by the user.
  // The client should not enable the cta/button click until all the required consents are accepted by the user.
  // Once all the consents are accepted by the user, the client should enable the cta and on cta click call the RecordConsent api first (with the checkbox ids passed as consent names)
  // and then serially (NOT parallelly) trigger the cta/button action.
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=11770-54318&mode=design&t=EANxwVHqt2HtDeKK-0
  repeated api.typesv2.common.ui.widget.CheckboxItem mandatory_consents = 19;

  // meta data to be sent in GetLandingInfoRequest
  // filters to be used to modify landing info computation
  repeated string landing_info_filters = 20;

  message TopBanner {
    string title = 1;
    string title_text_color = 2;
    string background_color = 3;
  }
}

message BottomTextBanner {
  api.typesv2.common.VisualElement start_icon = 1;
  InfoItemWithCtaV2 middle_section = 2;
  api.typesv2.common.VisualElement end_icon = 3;
  string container_color = 4;
}

//  screen options for preapproved loan cancel application deeplink
message PreApprovedLoanCancelApplicationScreenOptions {
  string loan_req_id = 1;
  string title = 2;
  string icon_url = 3;
  string message = 4;
  preapprovedloan.pal_enums.LoanHeader loan_header = 5;
}

message PreApprovedLoanErrorScreenOptions {
  string icon_url = 1;
  repeated InfoItem details = 2;
  Cta cta = 3;
  string bg_colour = 4;
  preapprovedloan.pal_enums.LoanHeader loan_header = 5;
}

message MutualFundCollectionsLandingScreenOptions {
  string collection_id = 1;
  frontend.investment.mutualfund.clientstates.CollectionType collection_type = 2;
  frontend.investment.mutualfund.clientstates.MutualFundListEntryPoint entry_point = 3;
}

message MutualFundFiltersScreenOptions {
  repeated string filter_ids = 1;
  string collection_id = 2;
  frontend.investment.mutualfund.clientstates.MutualFundListEntryPoint entry_point = 3;
}

message MutualFundOrderReceiptScreenOptions {
  string order_id = 1;
}

message InstructionsScreenOptions {
  string title = 1;
  // list of instructions
  repeated Instruction instructions = 2;
  string image_url = 3;
  // the CTAs that need to be shown
  repeated Cta ctas = 4;
  string cta_header = 5;
  // description for the title
  string description = 6;
}

message Instruction {
  string title = 1;
  string description = 2;
  string icon_url = 3;
}

message SendWorkEmailOtpScreenOptions {
  // title of send work email otp
  string title = 1;
  // description of send work email otp
  string subtitle = 2;
  // placeholder for email field
  string email_placeholder = 3;
  // employment_type to help client decide Cta texts
  frontend.account.screening.EmploymentType employment_type = 4;
  // client_req_id is the unique identifier of a work email verification process
  string client_req_id = 5;
  ToggleableScreenerCta cta = 6;
  deeplink.BackAction back_action = 7;
  // client is the string form of employment.VerificationProcessClient enum
  string client = 8;
}

message PreApprovedLoanKnowMoreScreenOptions {
  string icon = 1;
  string title = 2;
  repeated InfoBlock additional_info = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
  Button more_details_button = 5;
  preapprovedloan.pal_enums.FaqTopic faq_topic = 6;
}

message PreApprovedLoanHowToApplyScreenOptions {
  string icon = 1;
  string title = 2;
  repeated InfoBlock additional_info = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
}

message InfoItemsRow {
  // Repeated for multiple elements in one row
  repeated InfoItem items = 1;
}

message InfoItemsRowV2 {
  // Repeated for multiple elements in one row
  repeated InfoItemV2 items = 1;
}

message InfoItemTile {
  // Loan Details, Repayment Details, Account Details, transaction details
  InfoItem title = 1;
  // repeated for multiple rows
  repeated InfoItemsRow item_rows = 2;
}

message InfoItemTileV2 {
  // Loan Details, Repayment Details, Account Details, transaction details
  InfoItemV2 title = 1;
  // repeated for multiple rows
  repeated InfoItemsRowV2 item_rows = 2;
}

// this will represent a financial performed by customer
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A13005
message PreApprovedLoanPaymentActivity {
  string date_string = 1;
  // Repeated as to add late fees also when due date is crossed
  repeated InfoItem info_list = 2;
  string status = 3;
  string status_bg_color = 4;
  // Different message due to different handling for case of success and failure
  string success_message = 5;
  string failure_message = 6;
  string loan_activity_id = 7;
  Cta transaction_receipt = 8;
  preapprovedloan.pal_enums.LoanHeader loan_header = 9;
}

// screen will be used to show loan details of a particular loan for a user
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A12681
message PreApprovedLoanDetailsScreenOptions {
  // id to identify loan
  string loan_id = 1;
  // e.g. Loan #1
  string loan_name = 2;
  double repaid_percentage = 3;
  double remaining_percentage = 4;
  // remaining amount to repay
  InfoItem amount_to_repay = 5;
  Tile pre_pay_tile = 6;
  EmiAndPayments emi_and_payments = 7;
  LoanDetails loan_details = 8;
  string partnership_url = 9 [deprecated = true];
  preapprovedloan.pal_enums.LoanHeader loan_header = 10;
  api.typesv2.common.Text repaid_text = 11;
  api.typesv2.common.Text remaining_amount_text = 12;
  api.typesv2.common.Text prepay_toast_message = 13;
  repeated Cta overflow_items = 14;
  api.typesv2.common.VisualElement partnership_logo = 15;
  // Emis & Payments Tab View
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A12681
  message EmiAndPayments {
    repeated InfoItem upcoming_emis = 1;
    PreApprovedLoanPaymentActivity last_payment = 2;
    Cta all_transactions_cta = 3;
    string no_past_transaction_message = 4;
    api.typesv2.common.Text title = 5;
    api.typesv2.common.Text upcoming_emi_title = 6;
    api.typesv2.common.Text past_transactions_title = 7;
    api.typesv2.common.Text upcoming_emi_desc = 8;
  }
  // LoanDetails Tab View
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=3526%3A13340
  message LoanDetails {
    InfoItemTile basic_details = 1;
    InfoItemTile repayment_Details = 2;
    InfoItemTile account_details = 3;
    Cta tnc_cta = 4;
    string tnc_url = 5;
    api.typesv2.common.Text title = 6;
  }
}

// Figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46585&mode=dev
message PreApprovedLoanInitiateEsignScreenOptions {
  string icon_url = 1;
  string title = 2;
  string sub_title = 3;
  repeated string bullet_points = 4;
  string partner_icon_url = 5;
  Cta Continue = 6;
  string loan_request_id = 7;

  // Bottom sheet text deprecated in favor of a more extensible image that can take care of vendor logo
  string bottom_sheet_text = 8;

  preapprovedloan.pal_enums.LoanHeader loan_header = 9;
  // This will give the terms and condition text
  repeated TermInfo term_infos = 10;
  api.typesv2.common.Text toolbar_title = 11;

  // To show "Loan agreement is generated by Liquiloans via BE-driven image (to accomodate the logo)
  api.typesv2.common.VisualElement bottom_visual_element = 12;
}

message PreApprovedLoanPrePayScreenOptions {
  // title string to be shown at the top of the screen
  // e.g. Pre-pay a lumpsum
  string title = 1;
  // description to be shown at the top of the screen
  string desc = 2;
  // Will hold the details of the details that in header appearance
  // For prepay will hold amount to repay
  // For pre-closure will hold amount you will need to repay
  InfoItemsRow details_header = 3;
  // Will hold the details regarding pre-pay or pre-close depending if the user has selected the entire amount of not
  // For pre-pay will hold details such as EMIs reduced, interest saved etc.
  // For pre-closure will hold details such as outstanding amount, to be paid amount, etc.
  InfoItemsRow details_footer = 4;
  // boolean flag to represent if the amount selected by the user is leading to pre-close of loan account
  bool is_pre_close = 5;
  // send both partnership_url and partnership_logo due to parity in platforms (ios/android)
  string partnership_url = 6;
  // Represents the widget where the user can select the loan amount using a slider
  AmountSelector amount_selector = 7;
  // Represents the list of payment options which can used by user to pre-pay the loan amount
  // for now will just hold details of Fi account
  PaymentTypeToPaymentsOptionsMap payments_map = 8;
  Cta payment_cta = 9;
  // payment info to be shown if the user clicks on payment cta
  PaymentInfo payment_info = 10;
  // loan account id used to track/fetch the loan for which the info is needed to be processed
  string loan_account_id = 11;
  // Error message to be shown in case of low funds above the slider
  ErrorMessage error_message = 12;
  // For insufficient balance container in case if user has low balance
  InfoItemWithCta insufficientBalance = 13;
  // For showing error message in make payment screen. Used in case when making payment user presses back before entering pin
  ErrorMessage make_payment_error_message = 14;
  preapprovedloan.pal_enums.LoanHeader loan_header = 15;
  DetailsHeaderExtraParams details_header_extra_params = 16;
  api.typesv2.common.VisualElement partnership_logo = 17;
  // user's eligible accounts (TPAP) should be fetched by client to override payments_map or not
  bool fetch_user_accounts = 18;
  message DetailsHeaderExtraParams {
    // field to show the benefit text in case user is pre-paying the loan within the cool-off period and interest is waived off
    InfoItemV2 benefits_text = 1;
    // field to show the striked through amount to the user
    string strike_through_text = 2;
  }

  message ErrorMessage {
    // icon to be shown in front of message
    string icon_url = 1;
    string message = 2;
    string bg_colour = 3;
  }

  message PaymentInfo {
    string title = 1;
    string desc = 2;
    repeated InfoItemsRow item_rows = 3;
    string bottom_message = 4;
    Cta chat_with_us = 5;
  }
  message AmountSelector {
    string title = 1;
    // Minimum amount the user can pay in lump-sum
    api.typesv2.Money min_amount = 2;
    // Maximum amount the user can pay
    api.typesv2.Money max_amount = 3;
    // Default amount that will displayed to the user
    api.typesv2.Money default_amount = 4;
    bool is_pre_close = 5;
    api.typesv2.common.Text min_amount_text = 6;
    api.typesv2.common.Text max_amount_text = 7;
  }

  message PaymentTypeToPaymentsOptionsMap {
    string payment_type = 1;
    repeated PaymentOption payment_options_list = 2;
  }

  // It can have multiple payment options such as debit card, UPI etc.
  message PaymentOption {
    oneof payment_identifier {
      Account account = 1;
    }
  }

  message Account {
    string icon_url = 1;
    string name = 2;
    string account_number = 3;
    Bank bank = 4;
    enum Bank {
      BANK_UNSPECIFIED = 0;
      FI = 1;
    }
  }
}

// screen option for user waiting for agent during vkyc call
message VKYCUserWaitingScreenOptions {
  // denotes timer start time in seconds
  int32 duration = 1;
  // title for user waiting screen page
  string title = 2;
  // text to warn user if he switch to other screen
  string timer_text = 3;
  // text to advice user to have docs ready
  string advice_text = 4;
  // background color of the waiting page
  string background_color = 5;
  // advice block have text image bg color for blocks for docs readiness
  repeated VKYCUserWaitingScreenAdviceBlock vkyc_user_waiting_screen_advice_block = 6;
  // warning block have text image bg color for bottom warning block
  repeated VKYCUserWaitingScreenWarningBlock vkyc_user_waiting_screen_warning_block = 7;
  // bottom sheet have message to be shown when user press back button on waiting screen
  VKYCBottomSheet vkyc_bottom_sheet = 8;

  string attempt_id = 9;

  string weblink = 10;

  // this flag provides info if there is weblink webview compatibility issue based on device
  // ex: on Redme 8A we are getting audio out issues if weblink is opened in webview
  // default value will be false so by default all the links will be opened in webview mentioned otherwise
  bool unsupported_webview = 11;
  // this flag is used to remove the waiting screen overlay and show the karza overlay instead
  bool disable_fi_overlay = 12;
}

// advice block for vkyc user waiting screen
message VKYCUserWaitingScreenAdviceBlock {
  string icon = 1;
  string message = 2;
  //background color of the block
  string background_color = 3;
}

// warning block for vkyc user waiting screen
message VKYCUserWaitingScreenWarningBlock {
  string icon = 1;
  string message = 2;
  // background color of the block
  string background_color = 3;
}

// bottom sheet in case user press back button from any vkyc screen
// it is used to introduce some friction for user when tries to drop off
// white background will be hardcoded for this on client side
message VKYCBottomSheet {
  string title = 1;
  string description = 2;
  repeated Cta cta_list = 3;
}

message OfferCatalogScreenOptions {
  // optional param to denote which offers are to be shown first in the catalog.
  // for all the offers in display first list ordering would be same as ordering in this list.
  repeated string display_first_offer_ids = 1;
  // Optional field to specify whether we want to apply tag filters via the Deeplink.
  // It's a list of string value of tags that can be applied to a catalog offer.
  repeated string tag_filters = 2;
  // Optional field to specify whether we want to sort in results in a particular way
  string sort_by = 3;
}

// ref: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=11106%3A46255
message CollectNomineeDetailsScreenOptions {
  // to specify which service to call from this deeplink
  DataCollectionFlow flow = 1;

  // to pass additional arguments needed by the service to be called
  DataCollectionFlowData flow_data = 2;

  // e.g. "Choose a nominee"
  string title = 3;

  // max number of nominees a person can have
  int32 max_nominees = 4;

  // if this is empty, don't show checkbox either
  // e.g. "I want to continue without nominee"
  string opt_out_text = 5;

  // Used in cases where an authentication is needed for nominee collection like an OTP.
  // For eg: For wealth onboarding nominee collection, 2FA is mandatory.
  NomineeCollectionAuthenticationType nominee_collection_authentication_type = 6;

  // Deeplink to navigate to add nominee
  Deeplink add_nominee_deeplink = 7;
}

enum NomineeCollectionAuthenticationType {
  // For AuthenticationType_UNSPECIFIED, client needs perform SMS authentication. This is done for backward compatibility.
  AuthenticationType_UNSPECIFIED = 0;
  AuthenticationType_NO_AUTH_REQUIRED = 1;
  AuthenticationType_SMS = 2;
  AuthenticationType_EMAIL = 3;
}

message CreditCardBillDatesSelectionScreenOptions {
  StepInfo step_info = 1;
  Cta continue_cta = 3;
  string card_request_id = 4;
  InfoItemV3 header_details = 9;
  api.typesv2.common.Text footer_text_v2 = 10;
  // deprecated in favour of header_details
  string text = 5 [deprecated = true];
  // deprecated in favour of header_details
  string sub_text = 6 [deprecated = true];
  // deprecated in favour of footer_text_v2
  string footer_text = 7 [deprecated = true];
  // deprecated
  repeated InfoItem date_options = 2 [deprecated = true];
  // [OPTIONAL] Card id will be required for cases where the billing info has to be
  // displayed for update after card creation
  string card_id = 11;
  // bool to specify if the billing dates selection is for a newly onboarding user or
  // is it an update for an already onboarded user
  bool is_update = 12;
}

message ChatWithUsScreenOptions {
  // the type of chat view to be loaded
  api.typesv2.InAppChatViewType chat_view_type = 1;
  // Identifier of the context to be loaded on screen
  string context_code = 2;
  // descriptor of context/ issue user faced.
  // Deprecating this to use only context_code.
  // For more details ref: https://docs.google.com/document/d/15A9XIZRPD0omXeBTIm36WXU3GuQU46zt32zagqB2wrs/edit
  string context_key = 3 [deprecated = true];
  message InitialMessage {
    string text = 1;
  }
  InitialMessage initial_message = 4;
  // metadata stores information required for chat init
  map<string, string> metadata = 5;
  // Clients should pass this screen-level metadata when calling getCXChatInitInformation
  map<string, string> screen_metadata = 6;
}

message GenericBottomSheet {
  string title = 1;
  string description = 2;
  repeated Cta cta_list = 3;
}

message ScreenerCheckChoice {
  string title = 1;
  string description = 2;
  string image_url = 3;
  bool is_disabled = 4;
  // identifier for backend to know which check the user wants to retry
  string check_identifier = 5;
  // bottom_sheet will give the user more granular information if a choice is not available
  // e.g. You can retry verifying with this option in 20 days
  GenericBottomSheet bottom_sheet = 6;
  // This will be used to display verification progress in checks that take a long time. This will allow users to go back to screener checks to try verifying
  // by another way.
  IconTextWidget status = 7;
  // flag to indicate whether info bottom sheet needs to be shown
  bool show_info_bottom_sheet = 8;
  // This will be used to display footer image on the bottom sheet
  string bottom_sheet_footer_image_url = 9;
}

// ref: https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=28854-76445&mode=design&t=AkZGK7BQ5MtICdsP-0
message ScreenerChoiceScreenOptions {
  string title = 1;
  string subtitle = 2;
  repeated ScreenerCheckChoice screener_check_choice = 3;
  repeated Cta ctas = 4;
  string warning_icon = 5;
  string warning_message = 6;
  message ShareFeedbackBlock {
    string image_url = 1;
    string text = 2;
    // string version of inapphelp.app_feedback.AppScreen
    string feedback_page_identifier = 3;
  }
  ShareFeedbackBlock share_feedback_block = 7;
  // cta header is the text showed above a tertiary CTA
  string cta_header = 8 [deprecated = true];
  BackAction back_action = 9;
  api.typesv2.common.Text cta_header_v2 = 10;
}

// ref: https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/Onboarding-%2F-Workfile?node-id=13513%3A119007
message ScreenerEmployerSelectionScreenOptions {
  string title = 1;
  string subtitle = 2;
  repeated Cta ctas = 3;
  // Source identifies the flow in which the screen is shown. The client passes this value to
  // ProcessEmploymentData RPC as there is some custom logic based on different flows.
  // It maps to employment.UpdateSource enum string value e.g. "UPDATE_SOURCE_ONBOARDING".
  string source = 4;
  // client_req_id is the unique identifier of a process entry for a client
  string client_req_id = 5;
}

message CreditCardCreateCardScreenOptions {
  string card_request_id = 1;
}

message CreditCardPermanentUserFailureScreenOptions {
  string image_url = 1;
  string text = 2;
  string sub_text = 3;
  Cta eligibility_check_cta = 4;
}

// will be used to server below types of elements
// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8684%3A34907
message InfoItemWithCta {
  InfoItem info = 1;
  Cta cta = 2;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 3;
}

message InfoItemWithCtaV2 {
  InfoItemV2 info = 1;
  Cta cta = 2;
}

message InfoItemWithCtaV3 {
  InfoItemV3 info = 1;
  Cta cta = 2;
}

// represents card dashboard screen
// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8219%3A40690
message CreditCardDashboardScreenOptions {
  string rewards_card_type_id = 1;
}


message CreditCardOffersPageScreenOptions {
  string image_url = 1;
  string text = 2;
  repeated frontend.deeplink.InfoItem offers = 3;
  string tnc = 4;
  frontend.deeplink.Cta next_cta = 5;
  string partner_image_url = 6;
  frontend.deeplink.Cta know_more_cta = 7;
}

message PreApprovedLoanAllTransactionsScreenOptions {
  string title = 1;
  repeated PreApprovedLoanPaymentActivity transaction_tiles = 2;
  string loan_id = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
}

message CreditReportDownloadStatusPollScreenOptions {
  string client_request_id = 1;
  // heading to be shown on the polling screen
  string title = 2 [deprecated = true];
  // description text to be shown below title
  string body = 3 [deprecated = true];
  // image to be shown below body
  string image_url = 4;
  // Lottie url for the loading animation
  // here lottie url gets priority over image_url
  string lottie_loading_url = 5;

  api.typesv2.common.Text title_v2 = 6;
  api.typesv2.common.Text body_v2 = 7;

  api.typesv2.common.ui.widget.BackgroundColour background_colour = 8;
}

// figma link - https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/All-Analysers-%E2%80%A2-FFF?node-id=1469%3A5094&t=SVXYlvGuwp7oSH4O-0
message CreditReportConsentV2ScreenOptions {
  string client_request_id = 1;
  // deprecated, use title_v2
  string title = 2 [deprecated = true];
  // deprecated, use sub_title_v2
  string sub_title = 3 [deprecated = true];
  VendorDetail vendor_detail = 4;
  // deprecated, use body_v2
  // body can contain <link url="">...</link> tags which are needed to be shown as link.
  string body = 5 [deprecated = true];
  // CTA to be shown to the user to
  CreditReportConsentCta cta = 6;
  // tnc options to be checked before proceeding with consent flow
  // checkboxItem->Text can contain ^^<link_url>^^<link_text>^^ to represent hyperlinks
  repeated api.typesv2.common.ui.widget.CheckboxItem tnc_items = 7;

  api.typesv2.common.Text toolbar_title = 8;
  api.typesv2.common.VisualElement centre_image = 9;
  api.typesv2.common.Text title_v2 = 10;
  api.typesv2.common.Text sub_title_v2 = 11;
  // using bodyV2 for cases when we don't want to show check boxes, but just consent text
  // if tnc_items length is null, use body V2
  api.typesv2.common.Text body_v2 = 12;
  string bg_color = 13;
  // component to be shown on the toolbar right corner
  ToolbarRightCta top_right_cta = 14;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77939-331457&t=cZDpW2QRLYDmyKYf-4
message ToolbarRightCta {
  api.typesv2.common.VisualElement icon = 1;
  api.typesv2.common.Text title = 2;
  frontend.deeplink.Deeplink deeplink = 3;
  string border_color = 4;
  string bg_color = 5;
}

enum CreditReportConsentCtaType {
  CREDIT_REPORT_CONSENT_CTA_TYPE_UNSPECIFIED = 0;
  CREDIT_REPORT_CONSENT_CTA_TYPE_SLIDER = 1;
  CREDIT_REPORT_CONSENT_CTA_TYPE_PRIMARY_BUTTON = 2;
}

message CreditReportConsentCta {
  CreditReportConsentCtaType type = 1;
  // deprecated, use text_v2
  string text = 2 [deprecated = true];
  api.typesv2.common.Text text_v2 = 3;
}

message VendorDetail {
  // deprecated, use text_v2
  string text = 1 [deprecated = true];
  // deprecated, use vendor_icons_v2
  // credit report vendor icon urls
  repeated string vendor_icon_urls = 2 [deprecated = true];

  api.typesv2.common.Text text_v2 = 3;
  repeated api.typesv2.common.VisualElement vendor_icons_v2 = 4;
}

message GenericPromptOptions {
  // s3 url for prompt icon
  string image_url = 1;
  // background hex code for prompt
  string background_color = 2;
  // title for prompt(in html)
  string title = 3;
  // description for prompt(in html)
  string description = 4;
  // true indicates pop is non dismissible
  // false indicates pop is dismissible
  bool is_non_dismissible = 5;
  repeated frontend.deeplink.Cta cta_list = 6;
}


message TilesPromptOptions {
  // background hex code for prompt
  string background_color = 1;
  // title for prompt(in html)
  string title = 2;
  // true indicates pop is non dismissible
  // false indicates pop is dismissible
  bool is_non_dismissible = 3;
  // each array have row of tiles
  repeated LandingPageBlock tiles = 4;
  repeated frontend.deeplink.Cta ctas = 5;

}

message FederalSecurePinScreenOptions {
  TransactionAttributes txn_attributes = 1;
}

message TransactionAttributes {
  string payer_account_id = 1;

  string request_id = 2;

  string merchant_ref_id = 3;

  api.typesv2.PaymentProtocol payment_protocol = 4;

  string reference_url = 5;

  string payee_actor_name = 6;

  string payer_masked_account_number = 7;

  api.typesv2.Money amount = 8;

  string remarks = 9;

  string payer_payment_instrument = 10;

  string payee_payment_instrument = 11;

  string display_payee_payment_instrument = 12;

  string payer_payment_instrument_id = 13;

  string payee_payment_instrument_id = 14;

  string client_request_id = 15;
}

message CreditCardNewCardRequestScreenOptions {
  string title = 1;

  string description = 2;

  message BlockCardReason {
    int32 id = 1;
    string display_string = 2;
  }

  repeated BlockCardReason block_card_reasons = 3;

  string credit_card_id = 4;
}

message P2PInvestmentAvailablePlansInfoScreenOption {
  // tile for available plan  screen eg: Congrats, Sarun!
  string title = 1;
  // desc of availble plan screen eg: You’ve unlocked a plan.
  string description = 2;
  // text with loader eg: Taking you to invest..
  string loader_desc = 3;
  // Enum for eligible scheme
  P2PEligibleSchemes plan_tiles = 4 [deprecated = true];
  // used to support auto redirect to deeplink given
  message AutoRedirectInfo {
    // signifies if auto redirect needs to be done or not
    // default is false, i.e no auto redirect
    bool do_auto_redirect = 1;
    // delay in seconds after which redirection needs to be done
    int32 auto_redirect_delay_in_secs = 2;
    // delay in seconds after which redirection needs to be done
    Deeplink deeplink = 3;
  }
  // redirection info using which app needs to redirect to deeplink in cta
  AutoRedirectInfo auto_redirect_info = 6;
  // Json string for eligible scheme animation
  string p2p_eligible_scheme_animation_json = 7;
  // event properties that should be sent by the client along with p2pEligibleSuccessScreenDisplayed event
  // this will contain prpoerties like unlocked_plans etc
  // ref - https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit#gid=1102015672
  map<string, string> event_properties = 8;
}

message P2PInvestmentChoosePlanInfoScreenOption {
  // title for choose plan screen eg: Available plans on Jump
  string title = 1;
  // tiles showing available plans
  repeated P2PChoosePlanTile plan_tiles = 2;
  enum ScreenType {
    // standard screen
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=8930-13211&mode=design&t=esWGZNIRh7a7epyk-0
    STANDARD = 0;
    // quick selection screen used to navigate back to the existing invest screen with the new plan instead of navigating to a new invest screen
    // this is to avoid going to the plan selection screen when user clicks back on invest screen after coming from plan selection screen
    // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=18001-25755&mode=design&t=esWGZNIRh7a7epyk-0
    QUICK_SELECTION = 1;
  }
  ScreenType screen_type = 3;
}

message P2PInvestmentUnlockPlanScreenOption {
  // title for unlock screen eg: Get higher returns for a shorter investment period
  string title = 1;
  // reasons to tell user why plan is locked eg: Maintain minimum ₹10,000 account balance for 10 days to unlock
  repeated string reasons = 2;
  // close cta of the middle sheet
  Cta close_cta = 3;
  // cta for an action
  // e.g: "Add funds"
  Cta action_cta = 4;
  // icon for the plan
  // deprecated in favour of scheme_icon
  string scheme_icon_url = 5 [deprecated = true];
  // info for plan eg: [3 months, No early withdrawals, Upto 8% p.a]
  repeated string tags = 6;
  api.typesv2.common.VisualElement scheme_icon = 7;
}

message P2PChoosePlanTile {
  // eg: Flexi
  api.typesv2.common.Text title = 1;
  //eg: Great for parking a lumpsum amount temporarily
  api.typesv2.common.Text description = 2;
  // eg: [1 year, Withdraw any time]
  repeated P2PInvestInfoChip tags = 3;
  // eg: Invest/Unlock
  Cta cta = 5;
  // Icon of the tile
  // deprecated in favour of scheme_icon
  string scheme_icon_url = 6 [deprecated = true];
  // tell us if the plan is locked or not
  bool is_user_eligible_for_plan = 7;
  // scheme icon to show on the tile
  api.typesv2.common.VisualElement scheme_icon = 8;
}

enum P2PEligibleSchemes {
  P2P_ELIGIBLE_PLANS_UNSPECIFIED = 0;
  // User only eligible for 7% scheme
  P2P_ELIGIBLE_PLANS_7_PERCENT_SCHEME = 1;
  // User eligible for 7,8% scheme
  P2P_ELIGIBLE_PLANS_8_PERCENT_SCHEME = 2;
  // User eligible for 7,8,9% scheme
  P2P_ELIGIBLE_PLANS_9_PERCENT_SCHEME = 3;
  // User in-eligible for all schemes
  P2P_ELIGIBLE_PLANS_NONE = 4;
}

message P2PWithdrawMoneyEnterAmountScreenOptions {
  // Represents message shown on top of the screen, something like "Withdraw investment"
  api.typesv2.common.Text title = 1;
  // eg: Enter amount
  api.typesv2.common.Text sub_title = 2;
  // amount which can be withdrawn without any penalty
  api.typesv2.Money penalty_free_withdrawal_amount = 3;
  // total amount that can be withdrawn
  api.typesv2.Money total_withdrawable_amount = 4;
  // amount under lock in
  api.typesv2.Money lock_in_amount = 5;
  // represents the i icon with lock in amount
  repeated InfoItem lock_in_info_items = 6;
  // Penalty message to be shown if amount selected is more than penalty free amount
  api.typesv2.common.Text penalty_message = 7;
  // represents the i icon on the withdraw screen
  repeated InfoItem info_items = 8;
  deeplink.Cta proceed_cta = 10;
  // eg: Amount under lock in
  api.typesv2.common.Text lock_in_text = 11;
}

message DepositTemplatesScreenOptions {
  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 1;
}

message CreditCardVKYCLandingScreenOptions {
  StepInfo step_info = 1;
  string logo_url = 2;
  string title = 3;
  string sub_title = 4;
  Cta kyc_button = 5;
}

message CreditCardSubmitBillingDatesScreenOptions {
  string card_request_id = 1;
}

message CreditCardFreezeUnfreezeScreenOptions {
  string credit_card_id = 1;
  string title = 2;
  string description = 3;
  string image_url = 4;
  deeplink.Cta cta = 5;
  // request type to determine if its freeze/unfreeze request
  frontend.firefly.enums.CardRequestType card_request_type = 6;
  bool is_device_unlock_required = 7;
}

message CreditCardBillPaymentScreenOptions {}

message CreditCardStatementScreenOptions {}

message CreditCardDetailsScreenOptions {
  string card_request_id = 1;
  string rewards_card_type_id = 2;
}

message CreditCardAuthOptionsScreenOptions {
  message CtaWithAuthFlow {
    Cta cta = 1;
    frontend.firefly.enums.CardAuthFlow auth_flow = 2;
  }
  string card_req_id = 1;
  InfoItem info_item = 2;
  CtaWithAuthFlow primary_auth_cta = 3;
  // CTA text will have href for linking it to deeplink
  CtaWithAuthFlow secondary_auth_cta = 4;
}

// used to trigger loan activity (pre-payment/closer) payment status poll
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=9783%3A10378
message PreApprovedLoanActivityStatusPollScreenOptions {
  // retry count will be passed to the client to specify the number of retries already done
  // client will increment this count and pass it in the status poll API so that backend
  // can control the next delay accordingly
  int32 retry_attempt_number = 1;
  int32 retry_delay = 2;
  string ref_id = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
}

// used to show loan activity status
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=9783%3A10358
message PreApprovedLoanActivityStatusScreenOptions {
  InfoItem status_info = 1;
  repeated InfoItem more_details = 2;
  Cta cta = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 5;
}

message InitiateCardRequestScreenOptions {
  string card_id = 1;
  frontend.firefly.enums.CardRequestWorkflow workflow = 2;
  bool is_device_unlock_required = 3;
  oneof RequestData {
    BillPaymentData bill_payment_data = 8;
  }
  message BillPaymentData {
    //Amount to be paid
    api.typesv2.Money amount = 3;
  }
}

// used to show order physical debit card screen
message OrderPhysicalDebitCardInfoScreenOptions {
  string image_url = 1;
  string title = 2;
  string desc = 3;
  repeated InfoItem details = 4;
  Cta order_card_cta = 5;
  Cta upgrade_tier_cta = 6;
  InfoItem upgrade_tier = 7;
  // Represents amount to be paid to place a physical debit card order
  api.typesv2.Money amount = 8;
  // Represents description text corresponding to the gst charges to be incurred
  string gst_desc = 9;
}

message FundTransferStatusScreenOptions {
  // Unique identifier for the pay order using which fund transfer status can be enquired from the backend.
  string order_id = 1;
}

message RecordAuthFlowCompletionOptions {
  string client_request_id = 1;
}

message CreditReportDownloadFailureScreenOptions {
  string title = 1;
  string body = 2;
  // list of ctas to be shown to the user. Client should render them from left to right.
  repeated Cta ctas = 3;
}

// Deeplink options for tier introduction screen shown on the app on first time introducing tiers to users
// https://www.figma.com/file/lkZbcqGu9g5p3dvkGcttWL/Account-Tier-%E2%80%A2-Workfile?node-id=834%3A58125&t=2T0nG0AytyLvkMWh-0
message TierIntroductionOptions {
  // Tier enum for which tier to introduce
  frontend.tiering.enum.Tier tier = 1 [deprecated = true];
  // Lottie json for launch animation
  string launch_animation_json = 2;
  // Hero benefit for the tier introduction
  message HeroBenefit {
    // left text of the hero benefit image
    api.typesv2.common.Text image_text_one = 1;
    // Right text first line in the circle on the image
    api.typesv2.common.Text image_text_two = 2;
    // Right text second line in the circle on the image
    api.typesv2.common.Text image_text_three = 3;
    string background_colour = 4;
    string circle_colour = 5;
  }
  HeroBenefit hero_benefit = 6;
  // List of benefits
  repeated BenefitItem benefit_item_list = 7;
  // Cta's to be shown on the screen
  // Cta type is standard and will be sent from backend
  repeated Cta cta_list = 11;
  // Background color for benefits container
  api.typesv2.common.ui.widget.BackgroundColour benefits_background_color = 12;
  // Plan Icon
  api.typesv2.common.Image plan_icon = 13;
  // Introducing
  api.typesv2.common.Text text_line_1 = 14;
  // Plan name
  api.typesv2.common.Text text_line_2 = 15;
  // Plan desc
  api.typesv2.common.Text text_line_3 = 16;
  // Tier identifier for events purpose
  string identifier = 17;
}

// Screen options to return all available tier plans
message TierAllPlansOptions {
  // List of available tiering plans
  repeated TierPlanOptions tier_plan_options_list = 1;
  // tier plan to focus on in all tier plans
  // this will be an index for a tier plan in list of tier plans
  int32 tier_index_to_focus = 2;
  // Key value pairs for analytics purposes
  // eg:
  // current_tier: infinite
  map<string, string> analytics_key_value_pairs = 3;
}

// https://www.figma.com/file/lkZbcqGu9g5p3dvkGcttWL/Account-Tier-%E2%80%A2-Workfile?node-id=744%3A55110&t=2T0nG0AytyLvkMWh-0
message TierPlanOptions {
  // Background color of tier plan show in first half
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 1;
  // Title to be shown for this plan at the top
  api.typesv2.common.Text title = 2;
  // Scrollable tier plan cards to be shown to navigate between plans
  message Card {
    // Card icon
    api.typesv2.common.Image icon = 1;
    // texts to be shown in first half
    api.typesv2.common.Text first_half_title = 2;
    api.typesv2.common.Text first_half_subtitle = 3;
    // Background color of first half of card
    string first_half_bg_color = 4;
    // texts to be shown in second half
    api.typesv2.common.Text second_half_title = 5;
    api.typesv2.common.Text second_half_subtitle = 6;
    // Background color of second half of card
    string second_half_bg_color = 7;
    // Deeplink for upgrade on card
    // Can be nil if upgrade is not possible
    frontend.deeplink.Deeplink upgrade_deeplink = 8;
  }
  // Tier plan card
  Card card = 3;
  // Text to be show for benefits eg : What you get
  api.typesv2.common.Text benefits_text = 4;
  // CTA for benefits link
  Cta benefits_cta = 5;

  // List of all available benefits for a plan
  // Deprecated in favour of BenefitsComponent
  repeated BenefitItem benefit_item_list = 6 [deprecated = true];

  // TnC object goes here
  string tnc_html = 9 [deprecated = true];
  // Details to show on plan page for upgrade component
  TierPlanBottomInfo upgrade_details = 7;
  // CTA for the plan
  Cta cta = 8;
  // Background color for benefits container
  api.typesv2.common.ui.widget.BackgroundColour benefits_background_color = 10;
  // Tier enum for that plan
  frontend.tiering.enum.Tier tier = 11 [deprecated = true];
  // Deeplink for help and support
  // In case this is empty client will not show support icon on tier plan page
  frontend.deeplink.Deeplink help_deeplink = 12;
  // Tnc object with type as text
  api.typesv2.common.Text tnc_text = 13;
  // Tier identifier for events purpose
  string identifier = 14;
  // Key value pairs for analytics purposes
  // eg:
  // is_criteria_passed: true
  // reason_if_not_passed: "min kyc user"
  map<string, string> analytics_key_value_pairs = 15;
  // List of all benefits to highlight for a plan
  repeated BenefitItem hero_benefit_item_list = 16;
  // Background color for hero benefits container
  api.typesv2.common.ui.widget.BackgroundColour hero_benefits_bg_colour = 17;
  // Shadow for hero benefits container
  api.typesv2.common.ui.widget.Shadow hero_benefits_shadow = 18;
  // List of benefits components to display
  // eg: Can be list of locked benefits component, unlocked benefits component etc.,
  // https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?type=design&node-id=4844-13561&mode=design&t=IntSH6r4EdtCQZkc-0
  repeated BenefitsComponent benefits_components = 19;
  HeroBenefitComponentV2 hero_benefit_component_v2 = 20;
  // Hero benefit component v3
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=8125-9043&t=lFoS0mB1ZDcYyEnV-4
  HeroBenefitComponentV3 hero_benefit_component_v3 = 21;
  // Upgrade details text
  // If this is present, then upgrade_details will be ignored
  api.typesv2.common.Text upgrade_details_text = 22;
}
// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20557-15317&mode=design&t=MsFE0YOkD5iiBlai-4
message HeroBenefitComponentV2 {
  // Background color for hero benefits container
  api.typesv2.common.ui.widget.BackgroundColour hero_benefits_bg_colour = 1;
  // Shadow for hero benefits container
  api.typesv2.common.ui.widget.Shadow hero_benefits_shadow = 2;
  // Title : Get 3% cashback on your spends
  api.typesv2.common.Text title = 3;
  // Learn more
  api.typesv2.common.Text learn_more = 4;
  // Learn more deeplink
  Deeplink learn_more_deeplink = 5;
  // Icon which is shown to the right
  api.typesv2.common.VisualElement icon = 6;
  // Header divider
  string divider_color = 7;
  // Benefits list
  repeated IconTextWidget benefits_list = 8;
  // Border color for the whole component
  api.typesv2.common.ui.widget.BackgroundColour hero_benefits_border_colour = 9;
}

// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=8125-9043&t=kbk36JaN1yM5YGKz-4
message HeroBenefitComponentV3 {
  // Background color for hero benefits container
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 1;
  // Shadow for hero benefits container
  api.typesv2.common.ui.widget.Shadow shadow = 2;
  // Border color for the whole component
  api.typesv2.common.ui.widget.BackgroundColour border_colour = 3;
  // Header divider
  string divider_color = 4;
  // Header selected divider color
  api.typesv2.common.ui.widget.BackgroundColour selected_divider_color = 5;
  // Selected tab title color
  string selected_tab_title_color = 8;
  // Default tab title color
  string default_tab_title_color = 9;

  message BenefitData {
    // Unique identifier for the tab
    string id = 1;
    // Icon which is shown to the right
    api.typesv2.common.VisualElement icon = 2;
    // Title : Get 3% cashback on your spends
    api.typesv2.common.Text title = 3;
    // Benefits list
    repeated IconTextWidget benefits_list = 4;
  }
  // Id of selected tab to be displayed by default
  string selected_tab_id = 6;
  // List of hero benefits
  repeated BenefitData hero_benefits = 7 [(validate.rules).repeated = {min_items: 2, max_items: 3}];
}

message BenefitsComponent {
  // Sticky header component which specifies the status of the benefits list
  // If empty then just show the normal benefits list without any header
  // https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?type=design&node-id=4844-13561&mode=design&t=IntSH6r4EdtCQZkc-0
  message BenefitsHeader {
    // Visual element(Image icon) left of Header
    api.typesv2.common.VisualElement left_text_visual_element = 1;
    // Left text
    api.typesv2.common.Text left_text = 2;
    // Right text first line in the circle on the image
    api.typesv2.common.Text right_top_text = 3;
    // Right text second line in the circle on the image
    api.typesv2.common.Text right_bottom_text = 4;
    // Background colour for the whole header
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 5;
    // Background colour for the right circle
    api.typesv2.common.ui.widget.BackgroundColour circle_colour = 6;
  }
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20557-15353&mode=design&t=MsFE0YOkD5iiBlai-4
  message CollapsibleHeader {
    api.typesv2.common.Text title = 1;
    api.typesv2.common.VisualElement arrow_icon = 2;
    bool is_expanded = 3;
  }
  BenefitsHeader benefits_header = 1;
  // List of benefits to show below the header
  repeated BenefitItem benefit_items = 2;
  // Collapsible header
  CollapsibleHeader collapsible_header = 3;
  // If true, show only title of the benefits as bullet points
  api.typesv2.common.BooleanEnum benefits_as_bullet = 4;
}

// Upgrade details object
message TierPlanBottomInfo {
  // Left Title of upgrade details
  api.typesv2.common.Text left_title = 1;
  // Left Subtitle of upgrade details
  api.typesv2.common.Text left_sub_title = 2;
  // Background color of the object
  string background_colour = 3;
  // Right Title of upgrade details
  api.typesv2.common.Text right_title = 4;
  // Right Subtitle of upgrade details
  api.typesv2.common.Text right_sub_title = 5;
  // Error image
  api.typesv2.common.Image right_icon = 6;
}

// Proto message to represent one benefit item on tier plan screen
message BenefitItem {
  // Name/heading of the benefit
  api.typesv2.common.Text heading = 1;
  // Short description of the benefit
  api.typesv2.common.Text sub_heading = 2;
  // Icon to be shown for the benefit
  api.typesv2.common.Image icon = 3;
  // Dl with options to open card, will be only screen name for now
  // In case screen options are not present client will get them via api call
  Deeplink deeplink = 5;
  // Identifier to be sent by backend for each benefit item
  // Useful for analytics or to get data from backend while client being dumb
  // Will be a plain string passed and client will simply send it and write no logic on this field
  string identifier = 6;
}

// Options to return parameters to be shown in benefits bottom sheet for a particular benefit
message TierBenefitBottomSheetOptions {
  // Icon to be show at the top of bottomsheet
  api.typesv2.common.Image icon = 1;
  // Heading of bottom sheet below icon
  api.typesv2.common.Text heading = 2;
  // Sub heading below heading
  api.typesv2.common.Text sub_heading = 3;
  // Cta to take user to next available action
  Cta cta = 4 [deprecated = true];
  // Background color of bottomsheet
  string background_colour = 5;
  // Identifier to be sent by backend for each benefit item
  // Useful for analytics or to get data from backend while client being dumb
  // Will be a plain string passed and client will simply send it and write no logic on this field
  string identifier = 6;
  // Tier identifier for events purpose
  string tier_identifier = 7;
  // list of Ctas to take user to next available action
  repeated Cta cta_list = 8;
}

// Options to return parameters to be shown in all benefits bottom sheet for a tier
message TierAllBenefitsBottomSheetOptions {
  // Icon to be show at the top of bottomsheet
  api.typesv2.common.Image icon = 1;
  // Heading of bottom sheet below icon
  api.typesv2.common.Text heading = 2;
  // List of items show with icon and text
  repeated InfoItem info_item_list = 3;
  // Cta to take user to next available action
  Cta cta = 4;
  // Background color of bottomsheet
  string background_colour = 5;
  // Tier identifier for events purpose
  string tier_identifier = 6;
}

// Tier manual upgrade options is redirection deeplink hence if in case api call fails
// what is the expected error message goes in these options
// These will override UpgradeDetails on the same screen where CTA is present for manual upgrade
message TierManualUpgradeOptions {
  TierPlanBottomInfo error_detail = 1;
  // CTA shown in case of API call failure
  Cta cta = 2;
  // No of retries for the API call
  int32 no_of_retries = 3;
  // Error state when user reaches max retries for API call
  TierPlanBottomInfo max_retry_error_detail = 4;
  // CTA shown in case max retires for API call
  Cta max_retry_cta = 5;
  // Key value pairs for analytics purposes
  // eg:
  // manual_upgrade_event_from_tier: plus
  map<string, string> analytics_key_value_pairs = 6;
}

message TierUpgradeSuccessScreenOptions {
  // Tier enum for which tier to introduce
  frontend.tiering.enum.Tier tier = 1 [deprecated = true];
  // Lottie json for success animation
  string success_animation_json = 2;
  // List of benefits
  repeated BenefitItem benefit_item_list = 3;
  // Cta's to be shown on the screen
  // Cta type is standard and will be sent from backend
  repeated Cta cta_list = 4;
  // Background color for benefits container
  api.typesv2.common.ui.widget.BackgroundColour benefits_background_color = 5;
  // Plan Icon
  api.typesv2.common.Image plan_icon = 6;
  // Congrats
  api.typesv2.common.Text text_line_1 = 7;
  // Plan name
  api.typesv2.common.Text text_line_2 = 8;
  // Plan desc
  api.typesv2.common.Text text_line_3 = 9;
  // Tier identifier for events purpose
  string identifier = 14;
  // Key value pairs for analytics purposes
  map<string, string> analytics_key_value_pairs = 15;
}

// Screen options to show loan transaction receipt
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=5263%3A15963&t=PkQl2kPMuvRwD6qw-4
message PreApprovedLoanTransactionReceiptScreenOptions {
  // image, loan name, transaction type
  InfoItem top_details = 1;
  // transaction amount
  string amount = 2;
  // loan activity
  Activity activity = 3;
  // issuing bank, payment type, transaction id, emi number
  InfoItemTile details = 4;
  // emi expected time message display
  string bottom_tile_text = 5;
  // "Something wrong?" text with "Get Help" hyperlink
  TextWithHyperLink help = 6;
  string activity_id = 7;
  preapprovedloan.pal_enums.LoanHeader loan_header = 8;
  message Activity {
    string title = 1;
    PreApprovedLoanPaymentActivity transaction_tile = 2;
  }
}

message TextWithHyperLink {
  string message = 1;
  string hyperlink_text = 2;
  string hyperlink_url = 3;
}

message CreditCardTransactionReceiptScreenOptions {
  string txn_id = 1;
}

message OnboardingAddMoneyScreenOptions {
  // Boolean option to disable showing the skip button on add money screen
  bool disable_skip_button = 1;
}

message CreditCardRaiseDisputeOptions {
  string title = 1;
  string description = 2;
  repeated Cta ctas = 3;
  string txn_id = 4;
  string card_id = 5;
  api.typesv2.Money max_dispute_amount_allowed = 6;
}

message CreditCardDisputeDetailsOptions {
  string txn_id = 1;
}

message USStocksErrorScreenOptions {
  // e.g. Uh oh! There’s an issue with your image
  api.typesv2.common.Text title = 1;

  // e.g. When you re-take the image, make sure...
  api.typesv2.common.Text description = 2;

  // e.g. Your original PAN card is clear and legible, Your take the photo in a brightly lit place, etc.
  repeated api.typesv2.common.Text additional_hints = 3;

  Cta retry = 4;

  Cta later = 5;
}

message USStocksPANSourcesScreenOptions {
  // e.g. Few more ways to upload PAN
  api.typesv2.common.Text title = 1;

  // e.g. You can fetch using from central repository or download your E-PAN & upload manually
  api.typesv2.common.Text description = 2;

  message PANSource {
    // e.g button for "Download E-PAN from NSDL", etc.
    Cta source_cta = 1;

    // e.g. Download E-PAN from NSDL
    api.typesv2.common.Text source_info = 2;
  }

  repeated PANSource pan_sources = 3;
}

// Figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4428%3A19345&t=uhXWUKa5bsKDSHWf-4
// Show user PAN verification error and allow them to reupload or get help
message USStocksPANReuploadScreenOptions {

  // URL string for image to be shown on top
  string img_url = 1;

  // e.g. "Uh ho! We couldn’t fetch your PAN"
  api.typesv2.common.Text title = 2;

  // e.g. "We have some issues with fetching your PAN document. Please upload again"
  api.typesv2.common.Text description = 3;

  Cta upload_pan = 4;
}

// Figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=5016%3A21052&t=K4t4aEkIROoZMzTQ-4
message UsStocksCheckingInfoScreenOptions {

  // Image to be shown on top of title
  api.typesv2.common.Image image = 1;

  // e.g. "Checking your information ..."
  api.typesv2.common.Text title = 2;

  // e.g. "We’ll notify you once your source of funds verification is ready."
  api.typesv2.common.Text description = 3;

  // Eg: Done
  Cta action_cta = 4;
}

// figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=11210%3A60317&t=U8okulmu868wdb5r-4
// screen when user is showed the loan tab but has no valid offer, hence is a non qualified user
message PreApprovedLoansNonQualifiedUsersScreenOptions {
  string bg_color = 1;
  string title_icon = 2;
  string title = 3;
  string sub_title = 4;
  string tips_outline_color = 5;
  string tips_title = 6;
  repeated InfoItemWithCta tips_infos = 7;
  Cta notify = 8;
  preapprovedloan.pal_enums.LoanHeader loan_header = 9;
}

message InsightsStoriesScreenOptions {
  string story_group = 1;
}

// Option which contains ui context for TIER_ALL_PLANS_REDIRECTION
message TierAllPlansRedirectionOptions {
  string ui_context = 1;
}

message CreditCardHaltScreenOptions {
  string icon_url = 1;
  string text = 2;
  string sub_text = 3;
  repeated InfoItem details = 4;
  Cta cta = 5;
  string bg_colour = 6;
}

// figma: https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=20590%3A93127&t=c71d0KQ4wQcQ7q72-0
message ScreenerConnectedAccountsInfoScreenOptions {
  string title = 1;
  string sub_title = 2;
  message InformationBlock {
    string title = 1;
    string sub_title = 2;
    string image_url = 3;
  }
  repeated InformationBlock information_blocks = 3;
  string checkbox_text = 4;
  // wealth_tnc_url is the url for epifi wealth terms and conditions
  string wealth_tnc_url = 7;
  // will contain primary CTA for "Continue" action
  // and tertiary CTA for "verify another way"
  repeated Cta ctas = 5;
  // tertiary_cta_header is text shown above the tertiary CTA
  string tertiary_cta_header = 6;
  // list of consents to be recorded
  repeated Consent consents = 8;
  // flow for which this screen is being shown
  // note - this is required because this intro screen is being re-used in other flows such as itr intimation
  enum Flow {
    FLOW_UNSPECIFIED = 0;
    // connected accounts in screener
    FLOW_SCREENER_CONNECTED_ACCOUNTS = 1;
    // itr intimation in screener
    FLOW_SCREENER_ITR_INTIMATION = 2;
    // itr intimation in Lending
    FLOW_LENDING_ITR_INTIMATION = 3;
  }
  // flow for which this screen is being shown in string format
  string flow = 9;
  // back button behaviour
  deeplink.BackAction back_action = 10;
}

// Screen options used to show post consent approval activities
// https://www.figma.com/file/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=3179%3A28667&t=pUV7SwJPsrDSR3u2-0
message CAPostConsentPollingScreenOptions {
  // heading of the screen below icon
  api.typesv2.common.Text heading = 1;
  // these are set of texts which are displayed below loader according to the progress
  message LoaderTexts {
    api.typesv2.common.Text fetch_acc_text = 1;
    api.typesv2.common.Text import_txns_text = 2;
  }
  LoaderTexts loader_texts = 2;
  // background color of the screen
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  // network_error_deeplink is used to the terminal screen in case when the user is facing network issue and when further API calls can't be made
  frontend.deeplink.Deeplink network_error_deeplink = 4;
  // No of retries allowed for the API call in case of network error.
  int32 max_retry_count_allowed = 5;
  // initial_delay_data_pull_status_poll represents inital delay in seconds(say) that is added for polling the data pull status after consent approval
  google.protobuf.Timestamp initial_delay_data_pull_status_poll = 6;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 7;
  // logo url to show while linking accounts
  api.typesv2.common.VisualElement account_linking_logo = 10;
}

// Screen options used to show if account data pull is Success/Pending or Failure screen due to failure in consent request
// https://www.figma.com/file/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=3179%3A28667&t=pUV7SwJPsrDSR3u2-0
message CAPostConsentTerminalStateScreenOptions {
  // background color of the screen
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  // url for the icon to be displayed
  api.typesv2.common.Image icon = 2;
  // heading of the screen below icon
  api.typesv2.common.Text heading = 3;
  // text to show more information related to the status of the data pull or failure in consent request
  api.typesv2.common.Text sub_heading = 4;
  // redirect user screen to appropriate one with cta
  Cta cta = 6;
  // boolean to check if the terminal state is successful or not, if it is boolean true, do operations like show confetti,etc
  bool is_success_terminal_state = 7;
  // CA flow name for which terminal screen loaded. Will be helpful for tracking events
  string ca_flow_name = 8;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 9;
}

message CreditCardCustomAmountRepaymentScreenOptions {
  //Cta for redirecting to init card req
  Cta pay_cta = 1;
  //Account info item
  AccountDetails account_details = 2;
  string partner_logo_url = 3;
  InfoItem top_bar_details = 4;

  message AccountDetails {
    deeplink.InfoItem account_info = 1;
    api.typesv2.Money balance_amount = 2;
  }
  string card_id = 5;
  Cta low_fund_cta = 6;
  // bool to enable/disable tpap linking. If false, client should
  // not display the list of third party accounts for linking
  // and paying
  bool enable_tpap = 7;
}

message CreditCardBillRepaymentSelectionScreenOptions {
  string card_id = 1;
  string bg_color_light = 2;
  string bg_color_dark = 3;
}
// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=11852%3A58851&t=PLs40kaW0rlmaaGf-0
message CreditCardTxnDiputeDetailsScreenOptions {
  string title = 1;
  repeated InfoItem questions = 2;
}

message CreditCardStatementViewScreenOptions {
  //Default latest statement window will be sent here.
  StatementDuration statement_duration = 1;
  //Card id to fetch associated account
  string card_id = 2;
}

message CreditCardExportStatementScreenOptions {
  //statement window for which statement has to be exported will be sent here.
  StatementDuration statement_duration = 1;
  string email = 3;
}

message StatementDuration {
  api.typesv2.Date from_date = 1;
  api.typesv2.Date to_date = 2;
}


//Screen options for credit card payment status screen
message CreditCardPaymentStatusPollingScreenOptions {
  int32 retry_attempt_number = 1;
  int32 retry_delay = 2;
  string payment_req_id = 3;
  // required in cases where the polling is getting done to the order
  // instead of the fund transfer workflow
  string order_id = 4;
}

message CreditCardPaymentStatusScreenOptions {
  InfoItem status_info = 1;
  repeated InfoItem more_details = 2;
  Cta cta = 3;
  // This PaymentProtocol field will be used in iOS for
  // checking if we can play the UPI transaction success sound or not.
  // This field will be populated from the client side itself.
  api.typesv2.PaymentProtocol payment_protocol = 4;
}

// figma: https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=22564%3A48092&t=5JjajJpBBIwCkr18-0
message GenericTransitionScreenOptions {
  string image_url = 1;
  string title = 2;
  string subtitle = 3;
  // show_for is the time (in seconds) for which we want to show this transition screen
  int32 show_for = 4;
  deeplink.Deeplink next_screen = 5;
  repeated api.typesv2.common.VisualElement compliance_logo = 6;
}


message CreditCardPhysicalCardTrackingScreenOptions {
  string card_id = 1;
}

message P2PInvestmentOtpScreenOptions {
  frontend.p2pinvestment.jump_client_states.RenewalType renewal_type = 1;
  string order_external_id = 2;
}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15670&t=CQGbtXPd1G8WbTAA-4
// Client to reuse their implementation of 'GetP2POrderStatus'
message P2PInvestmentCurrentStatusScreenOptions {
  // Information about P2P renewal to be shown to the user
  // e.g.
  // Title: Transaction Successful
  // Desc: Your Investment request has been sent to partner
  deeplink.InfoItem order_status_info = 2;
  // Order timeline to be shown to the user
  repeated deeplink.InfoItem order_timeline = 3;
  // CTA to be shown to user based on order status
  deeplink.Cta cta = 4;
  // flag to show confetti
  bool show_confetti = 5;
  // will be used to show extra info in case of withdrawal like
  // You will receive 2 separate deposits into your Fi account. Check My Activity for details.
  string bottom_info = 6;
  // scheme name in which user has taken action
  string scheme_name = 7;
  // flow_type(renewal/cancellation/cancellation_modification/renewal_modification)
  // required for client event
  string flow_type = 8;

}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=12356%3A22973&t=k4CuMDGdVMPFu0vi-4
// Used for rendering P2P_INVESTMENT_ACTIVITY_DETAILS screen
message P2PInvestmentActivityDetailsScreenOptions {
  // Request payload to be used for calling GetBanners rpc
  BannerRequestPayload banner_request_payload = 1;
  // order identifier to be used for calling GetActivityDetails rpc
  string order_external_id = 2;
  bool show_maturity_consent_form = 3;
}
message BannerRequestPayload {
  oneof BannerReq {
    RenewInvestmentBannerReq renew_investment_banner_req = 2;
  }
}

message RenewInvestmentBannerReq {
  string order_external_id = 1;
}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15607&t=Sboiw3jG1WYq6w04-4
// Based on 'NudgeDetails' in 'GetWithdrawMoneyAttributesResponse'
// P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN
message P2PInvestmentRenewalCancellationScreenOptions {
  // Maturity Payout/Withdraw Investment
  api.typesv2.common.Text screen_title = 1;
  // Renewal Amount/Withdrawal Amount
  api.typesv2.common.Text header_title = 2;
  // ₹43,200
  api.typesv2.common.Text amount = 3;
  // eg: You'll miss out on ₹8,000 in returns!
  api.typesv2.common.Text nudge_title = 4;
  // background color for nudge title
  string nudge_title_bg_color = 5;
  // eg: ₹20,000 in Jump for 1 year will be ₹28,000
  api.typesv2.common.Text desc = 6;
  // nudge graph image
  api.typesv2.common.Image img = 7;
  // CTA for abandoning the withdrawal/renewal cancellation
  deeplink.Cta later_cta = 8;
  // CTA for confirming the action
  // For renewal this would be GetOtpScreen deeplink
  // For withdrawal this would be withdrawal confirmation screen
  deeplink.Cta proceed_cta = 9;
  // Name of the plan. eg) Flexi, Short_Term, Long_Term
  // Client use this field for Analytics event
  string plan_name = 10;
  // Flow from where user comes to this screen. eg) renewal/renewal_modification
  // Client use this field for Analytics event
  string flow_type = 11;
  enum RenewalCancellationScreenType {
    RENEWAL_CANCELLATION_SCREEN_TYPE_FULL_SCREEN = 0;
    RENEWAL_CANCELLATION_SCREEN_TYPE_BOTTOM_SHEET = 1;
  }
  RenewalCancellationScreenType screen_type = 12;

  // event properties that should be sent by the client along with p2PAutoRenewReconsiderBottomSheetLoaded, p2PAutoRenewReconsiderBottomSheetActioned event
  // this will contain prpoerties like activity_type, flow_name, selected_plan, reconsideration_period etc
  // ref - https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit#gid=1102015672
  map<string, string> event_properties = 13;
}

// Added to navigate to a billing window in view all txns screen.
message CreditCardViewAllTransactionScreenOptions {
  deeplink.StatementDuration statement_duration = 1;
  // boolean to determine if all txns should be fetched via the pagination api or the older one
  bool enable_view_all_txn_pagination = 2;
}
// P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11889%3A14949&t=ftRY9JIOn9opQKmF-0
// Used for calling 'GetRenewInvestmentAttributes' rpc
message P2PConfirmInvestmentRenewalScreenOptions {
  string order_external_id = 1;
}

// UpiNumberIntroScreenOptions - shows all the details of what upi number means to user
message UpiNumberIntroScreenOptions {
  string logo_url = 1;
  api.typesv2.common.Text title = 2;
  repeated api.typesv2.common.Text description = 3;
  deeplink.Cta set_upi_number_cta = 4;
  repeated OverflowOption overflow_options = 5;
  string vpa = 6;
  string phone_number = 7;
  string derived_account_id = 8;
}

// List of consents should be given preference over consent_text. If list of consents is empty then consent_text should be shown
// else consent check boxes should be shown.
message MFHoldingsImportConsentFlowScreenOptions {
  api.typesv2.common.Text screen_title = 1;
  repeated InfoItem info_items = 2;
  string consent_text = 3;
  string swipe_button_text = 4;
  string external_id = 5;
  // This is required to support multiple consents
  // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=6334-73991&mode=design&t=cNmLrOooeNG45Byw-0
  repeated api.typesv2.common.ui.widget.CheckboxItem consents = 6;
}

message MFHoldingsImportInitiateScreenOptions {
  // exit_deeplink to which the user is redirected after a successful mf holdings import
  Deeplink exit_deeplink = 1;
  string provenance = 2;
}

message MFHoldingsImportOTPScreenOptions {
  reserved 4, 5;
  // verify OTP screen title
  api.typesv2.common.Text screen_title = 1;
  string message = 2;
  string external_id = 3;
  // templated message string with variable phone number
  api.typesv2.common.Text message_template = 6;
  api.typesv2.common.PhoneNumber phone_number = 7;
}

message MFHoldingsImportLoadingScreenOptions {
  api.typesv2.common.Text screen_title = 1;
  api.typesv2.common.Text loading_screen_description = 2;
  string external_id = 3;
  // Timeout for the loading screen. Client should stop polling after the timeout
  google.protobuf.Duration loading_timeout = 4;
  // delay for the polling duration
  google.protobuf.Duration polling_duration_delay = 5;
  // this is the url for animations
  string loading_animation_url = 6;
}

// Overflow option on the Manage Upi Numbers screen
message OverflowOption {
  enum OverflowAction {
    OVERFLOW_ACTION_UNSPECIFIED = 0;
    OVERFLOW_ACTION_CREATE_A_CUSTOM_UPI_NUMBER = 1;
  }

  OverflowAction action_type = 1;

  // text to be shown for the overflow option
  api.typesv2.common.Text display_text = 2;

  // [Optional]: Added for extensibility
  // If we add some options in future whose destination screen requires some info, we need to send that through deeplink
  deeplink.Deeplink deeplink = 3;
}

// ManageUpiNumberScreenOptions - shows all the details of what upi number means to user
message ManageUpiNumberScreenOptions {
  // vpa for which upi number details to be fetched
  string vpa = 1;
  // account id associated with that vpa
  string derived_account_id = 2;
}

// Screen options for cc rewards bottom sheet view
message CreditCardExtraRewardsBottomViewScreenOptions {
  string title = 1;
  api.typesv2.common.Text rewards_text = 2;
  api.typesv2.common.TextWithIcon projected_rewards_coins = 3;
  repeated RewardChartLegend reward_chart_legends = 4;
  api.typesv2.common.Text bottom_text = 5;
}

message RewardChartLegend {
  string reward_text = 1;
  string reward_legend_color = 2;
  api.typesv2.common.TextWithIcon total_coins = 3;
  float reward_percentage = 4;
}

message USStockSetupAccountScreenOptions {
  // image to be displayed
  api.typesv2.common.Image img = 1;
  // title of the error dialog
  // eg: Your source of funds document has expired
  api.typesv2.common.Text title = 2;
  // sub-title of the error dialog
  // eg: Your Source of funds is valid only for 2 months. Please update to re-enable international transfers
  api.typesv2.common.Text sub_title = 3;
  // Takes less than 5 mins
  api.typesv2.common.Text wait_info = 4;
  // icon for wait info
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2178&t=KajAsRvJf9aQDOLN-4
  api.typesv2.common.Image wait_icon = 5;

  // Your data is safe & will be used only to set-up your account
  api.typesv2.common.Text security_info = 6;
  // icon for security info
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2175&t=KajAsRvJf9aQDOLN-4
  api.typesv2.common.Image security_icon = 7;
}

// screen options for GET_NEXT_ONBOARDING_ACTION_API
message GetNextOnboardingActionScreenOptions {
  // deprecated in favour of image, title_V2 & subtitle_v2
  string image_url = 1 [deprecated = true];
  string title = 2 [deprecated = true];
  string subtitle = 3 [deprecated = true];
  // onboarding feature for which the next action has to be fetched
  // Client should pass this in the GetNextOnboardingAction request
  string feature = 4;
  string feature_onboarding_entry_point = 5;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 6;
  api.typesv2.common.Text title_v2 = 7;
  api.typesv2.common.Text subtitle_v2 = 8;
  api.typesv2.common.VisualElement image = 9;
  // Repeated field to display trust markers, which consist of icons and text,
  // helping to build user confidence in the service/product.
  repeated IconTextWidget bottom_info_widget = 10;
}

// screen options for GET_VKYC_ACTION_API
message GetVKYCNextActionApiScreenOptions {
  // enum map to VKYCClientState
  string client_last_state = 1;
  // enum map to api/kyc/vkyc/internal.proto entry point
  string entry_point = 2;
  // if true client needs to show loader instead of transition screen
  // else client needs to show transition screen for MAX(2S, latency)
  bool show_cta_loader = 3;
  string title = 4;
  string subtitle = 5;
  api.typesv2.common.Image illustration = 6;
  bytes blob = 7;
}

// screen options VKYC_PAN_TYPE_SELECTION
message VKYCPanTypeSelectionScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  repeated PanSelectionBlock blocks = 3;
  repeated Cta ctas = 4;
  SkipCTAInfo skip_cta_info = 5;
}

message PanSelectionBlock {
  api.typesv2.common.Text title = 1;
  // not using api.typesv2.ui.TextIconComponent since it creates circular dependency
  repeated GetNextOnboardingActionScreenOptions labels = 2;
  // deeplink used on cta tap
  deeplink.Deeplink deeplink = 3;
  // event name to passed as event property when user tap on CTA
  string event_name = 4;
}

message SkipCTAInfo {
  // if true client needs to show skip CTA
  bool show_cta = 1;
  // stage name maps to onboarding stage
  string stage = 2;
  // visible after second
  int32 visible_after_seconds = 3;
}

message VKYCInstructionsScreenOptions {
  string title = 1;
  string subtitle = 2;
  repeated LandingPageBlock blocks = 3;
  // vkyc_available_at indicates if current time falls in business hours or not
  // if client receives empty string they are expected to enable the cta
  // if client receives string with some value they are expected to show that and disable the cta
  string vkyc_available_at = 4;
  repeated Cta ctas = 5;
  SkipCTAInfo skip_cta_info = 6;
  string bg_color_hex = 7;
  api.typesv2.common.VisualElement icon = 8;
  ScheduleBlock schedule_block = 9;
  // is_schedule_flow indicates client to take decision whether they have to show freeze screen or not
  // if true they wont show screen else they will
  bool is_schedule_flow = 10;
}

message ScheduleBlock {
  api.typesv2.common.Text time = 1;
  api.typesv2.common.VisualElement icon = 2;
  string bg_color = 3;
  // event title and description are used to specify heading of blocked event
  string event_title = 4;
  string event_description = 5;
  // start time and end time used by client to block calendar
  google.protobuf.Timestamp start_time = 6;
  google.protobuf.Timestamp end_time = 7;
}

message GenericBottomSheetScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  repeated Cta ctas = 3;
}

message PreApprovedLoanESignViewDocumentScreenOptions {
  string document_url = 1;
  // should be sticky on the bottom of the screen
  // if check_boxes is empty, the CTA should be enabled by default
  // if check_boxes is not empty, the CTA should be disabled by default and would be enabled only when user checks all the check_boxes
  Cta agree = 2;
  preapprovedloan.pal_enums.LoanHeader loan_header = 3;
  // should be sticky on the bottom of the screen above the CTA
  // if empty, client should not show any check boxes
  // e.g: ["By continuing, I agree to the terms and conditions of the loan agreement of IDFC FIRST Bank", ...]
  repeated api.typesv2.common.ui.widget.CheckboxItem check_boxes = 4;
  string loan_request_id = 5;
  preapprovedloan.pal_enums.LoanDocType loan_doc_type = 6;
}

// screen options for pre approved employment details screen
message PreApprovedEmploymentDetailsScreenOptions {
  InfoItem header_info_item = 1 [deprecated = true];
  Cta continue_cta = 2;
  string loan_req_id = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
  api.typesv2.common.Text toolbar_title = 5;
  // tnc / consents to be shown at the bottom of the screen right above the continue button
  repeated api.typesv2.common.ui.widget.CheckboxItem mandatory_consents = 6;
  // flag to be sent in the subsequent rpc call
  bool add_employment_details_in_sync = 7;

  // This will be passed to next screen while opening the add address screen
  api.typesv2.common.Text add_address_title = 8;

  InfoItemV3 header_info_item_v2 = 9;
}

// screen options for PreApprovedIncomeSelectionScreen
message PreApprovedIncomeSelectionScreenOptions {
  string title = 1;
  string bottom_message = 2;
  api.typesv2.Money default_value = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
  Button submit_cta = 5;
  api.typesv2.common.Text income_frequency_label = 6;
  api.typesv2.Money min_value = 7;
  api.typesv2.Money max_value = 8;
}

// screen options for PreApprovedOccupationSelectionScreen
message PreApprovedOccupationSelectionScreenOptions {
  string title = 1;
  repeated EmploymentData employment_data_list = 2;
  EmploymentData default_employment_type = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;

  message EmploymentData {
    api.typesv2.EmploymentType employment_type = 1;
    string employment_text = 2;
  }
}

// Screen options for PreApprovedHardPullInfoScreen
message PreApprovedInfoBottomDialogScreenOptions {
  string title = 1;
  string intro_text = 2;
  repeated InfoItem question_answers = 3;
  preapprovedloan.pal_enums.LoanHeader loan_header = 4;
}

message AutoInvestCollectionBottomViewScreenOptions {
  // Invest regularly
  api.typesv2.common.Text title = 1;
  // Invest using Daily, Weekly, Monthly SIPs
  api.typesv2.common.Text subtitle = 2;
  // eg. Choose where to invest
  api.typesv2.common.Text choose_investment_text = 3;
  api.typesv2.common.Image icon_url = 4;
  repeated AutoInvestmentInstrument investment_instruments = 5;
  string bg_color = 6;
  string line_color = 7;
}


message AutoInvestmentInstrument {
  // eg. Smart deposits
  api.typesv2.common.Text title = 1;
  // eg. Safe place to park your extra cash.
  api.typesv2.common.Text sub_text = 2;
  deeplink.Deeplink deeplink = 3;
  api.typesv2.common.Image icon_url = 4;
}

message StartEPANScreenOptions {
  // blob contains deeplink for polling, success, failure screen
  bytes blob = 1;
  // client req id will be used as case id by client to pass in sdk
  // client is expected to pass same in initate epan req
  string client_request_id = 2;
}

message OrderProcessingScreenOptions {
  oneof options {
    MFCreateOneTimeBuyOrderProcessingScreenOptions mf_create_one_time_order_processing_screen_options = 1;
    MFRegisterSIPOrderProcessingScreenOptions mf_register_sip_order_processing_screen_options = 2;
  }
}

message MFCreateOneTimeBuyOrderProcessingScreenOptions {}

message MFRegisterSIPOrderProcessingScreenOptions {}

// Screen options for information bottom sheet when disconnecting account
// Figma URL : https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/Workfile-%2F-Connected-Accounts?node-id=2757-55087&t=0uILt8chdUupdMhP-0
message DisconnectAccountInfoScreenOptions {
  // title of the bottom sheet
  api.typesv2.common.Text title = 1;
  // subtitle of the bottom sheet
  api.typesv2.common.Text sub_title = 2;
  // list of information text to be displayed
  repeated InfoItem info_list = 3;
  // account id
  string account_id = 4;
  // two cta buttons to be displayed - "Proceed" and "Back"
  repeated Cta cta_list = 5;
}

// Screen options for confirmation bottom sheet when disconnecting account
// Figma URL : https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/Workfile-%2F-Connected-Accounts?node-id=2757-55133&t=lLQ5PE6pztJG6Yof-0
message DisconnectAccountConfirmScreenOptions {
  // title of the bottom sheet
  api.typesv2.common.Text title = 1;
  // text for displaying hint
  api.typesv2.common.Text hint_text = 2;
  // confirm button
  Cta confirm = 3;

  enum AaEntity {
    AA_ENTITY_UNSPECIFIED = 0;
    AA_ENTITY_ONE_MONEY = 1;
    AA_ENTITY_FINVU = 2;
  }
  message AccountDetail {
    string account_id = 1;
    string masked_account_number = 2;
    string account_type = 3;
    string account_display_name = 4;
    string fip_logo_url = 5;
    AaEntity aa_entity = 6;
  }
  // List of accounts which need to be disconnected with current one
  repeated AccountDetail account_detail_list = 4;
  // List of consent handles which need to be revoked to disconnect the accounts
  repeated string consent_handle_list = 5;
  // List of consent ids
  repeated string consent_id_list = 6;
  // aa_entity_meta contains metadata of AA which is taken from account id.
  frontend.connected_account.common.AaEntityMeta aa_entity_meta = 7;
}

message CardOffersCatalogScreenOptions {
  enum CardType {
    CARD_TYPE_UNSPECIFIED = 0;
    // For Debit card offer catalog screen
    DEBIT_CARD = 1;
    // For Credit card offer catalog screen
    CREDIT_CARD = 2;
  }
  // deprecated in favor of card_type_id
  CardType card_type = 1 [deprecated = true];

  // card type id for which card offers should be fetched: For eg: AMPLIFI_CREDIT_CARD_ID for amplifi CC
  // it will resolve to a combination of CardType and Tag: For eg: AMPLIFI_CREDIT_CARD_ID will resolve to CREDIT_CARD_TYPE and AMPLIFI_CREDIT_CARD_EXCLUSIVE tag
  // using string type instead of enum for ease of adding new card types with client backward compatibility
  // It is a mandatory param.
  string card_type_id = 2;
}

message CardOfferDetailsScreenOptions {
  string offer_id = 1;
}

// modify recurring page deeplink
// screen : `MODIFY_RECURRING_PAYMENT`
message ModifyRecurringPaymentScreenOptions {
  // urn to be passed for QR/Intent based mandates modification
  string urn = 1;

  // amount to be updated
  api.typesv2.Money updated_amount = 2;

  // recurrence rule for the recurring payment
  RecurrenceRule recurrence_rule = 3;

  // start date of recurring payment
  api.typesv2.Date start_date = 4;

  // end date of recurring payment
  api.typesv2.Date end_date = 5;

  // Maximum allowed transaction withing the allowed frequency range
  // NOTE : Need not to be passed for mandate creation
  int32 maximum_allowed_transactions = 6;

  // other actor name to be shown to the user
  string other_actor_name = 7;

  // image url of the other actor
  string other_actor_image_url = 8;

  // bg colour for the other actor chat head in case image url is not present
  string other_actor_bg_colour = 9;
  // remarks for the mandate creation
  string remarks = 10;

  // recurring payment id
  string recurring_payment_id = 11;

  // derived account id
  string derived_account_id = 12;

  // autoPay details to be shown to the user
  // e.g. start date, end date, frequency, amount etc.
  api.typesv2.recurring_payment.AutoPayDetails auto_pay_details = 13;
}


message CreditCardFailureBottomViewScreen {
  string icon = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text description = 3;
  deeplink.Cta cta = 4;
}

message DownloadDigitalCancelledChequeScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text description = 2;
  // cta text details, text bg_color can be used as CTA bg_color
  // not using deeplink.Cta because there is no deeplink redirection on CTA click.
  api.typesv2.common.Text cta_text = 3;
}

// https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%8E%A5-Video-KYC-Workfile?node-id=10202-59658&t=tKP5WieqYTeaY7yo-0
message VKYCErrorScreenOptions {
  api.typesv2.common.Image image = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text description = 3;
  // InstructionItemsBlock will contain back ground color of block and instructionItems
  InstructionItemsBlock instruction_items_block = 4;
  // by default it will be false and client need to show the back button and when it is true client will not show back button
  bool hide_back_button = 5;
  repeated Cta ctas = 6;
}

message InstructionItemsBlock {
  // InstructionItem will denote the list of instructions
  repeated InstructionItem instruction_items = 1;
  // background color for the instruction item block
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 2;
}

message InstructionItem {
  api.typesv2.common.Text text = 1;
  // weblinkBlock is the object of weblink related info
  WeblinkBlock weblink_block = 2;
}

message WeblinkBlock {
  string weblink = 1;
  // text to be shown to user denoting when the link will expire
  string weblink_expiry_text = 2;
  // background color for the block
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  api.typesv2.common.Image icon = 4;
}

// Along with deeplink if needed on click of text.
message TermInfo {
  api.typesv2.common.Text term_text = 1;
  frontend.deeplink.Cta term_cta = 2;
  bool is_term_clickable = 3;
  bool is_term_link_underlined = 4;
  // unique id for the consent, needs to be passed as part of the ModifyLoanTerms/ApplyForLoan request
  string consent_id = 5;
}

message CreditCardLoungeAccessScreenOptions {
  api.typesv2.common.Text toolbar_title = 1;
  api.typesv2.common.Image screen_icon = 2;
  api.typesv2.common.Text screen_title = 3;
  repeated api.typesv2.common.Text information_texts = 4;
  Button action_button = 5;
  api.typesv2.common.Text footer_text = 6;
}

message AffluentUserBonusTransitionScreenOptions {
  api.typesv2.common.Image screen_icon = 1;
  api.typesv2.common.Text screen_title = 2;
  // Type of bonus transition screen to be shown
  // It is a string type for the enum api/frontend/user/onboarding/bonus_transition_screen.proto
  // Possible values are :
  // BONUS_TRANSITION_SCREEN_TYPE_UNSPECIFIED : unspecified, don't show any transition
  // BONUS_TRANSITION_SCREEN_TYPE_AFFLUENT_REFEREE : transition screen for affluent referees
  // BONUS_TRANSITION_SCREEN_TYPE_AFFLUENT_NON_REFEREE : transition screen for affluent non-referees
  string screen_type = 3;
}

message InfoItemBox {
  InfoItemV2 title = 1;
  repeated InfoItemsRowV2 rows = 2;
  string bg_color = 3;
}

message SkipOnboardingStageApiOption {
  // mapped to user/onboarding/internal/onboarding_details/OnboardingStage
  string stage = 1;
}

message EPANStoryRedirectionComponent {
  // eg : Don’t have PAN now? Download E-PAN
  api.typesv2.common.Text title = 1;
  // eg : PAN provider may apply charges
  api.typesv2.common.Text sub_title = 2;
  // Deeplink to redirect
  frontend.deeplink.Deeplink deeplink = 3;
}

message VisualElementCta {
  api.typesv2.common.VisualElement icon = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

// This will identify the specific InfoItemTypeV2 object
// Currently this is being used on credit card part
enum InfoType {
  CARD_ACTIVATION_INFO_TYPE_UNSPECIFIED = 0;
  CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT = 1;
  CARD_ACTIVATION_INFO_TYPE_BILL_GENERATION = 2;
  CARD_ACTIVATION_INFO_TYPE_WELCOME_OFFER = 3;
  CARD_ACTIVATION_INFO_TYPE_ADDRESS = 4;
  CARD_ACTIVATION_INFO_TYPE_FEE_INFO = 5;
  CARD_ACTIVATION_DEPOSIT_AMOUNT = 6;
}

// ToggleableScreenerCta is the "verify another way" cta that has different copy and behaviour depending on
// the screener stage and screen.
message ToggleableScreenerCta {
  bool show_cta = 1;
  // cta_text is the copy showed that acts as "verify another way" for screener
  api.typesv2.common.Text cta_text = 2;
  // clickable text is the part of cta_text that triggers BE call that does the "verify another way" action
  string clickable_cta_text = 3;
}

message CreateAccountScreenOptions {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  api.typesv2.common.Text tooltip_description = 3;
  api.typesv2.common.Text cta_text = 4;

  // Source for device registration. This refers to api.auth.DeviceRegistrationSource
  string source = 5;
  // blobs field is used to send serialised data that can be passed around.
  bytes blob = 6;
  // flag to remove the 'Your KYC details are available' section from register device deeplink
  bool remove_kyc_details = 7;
  // Header bar used in onboarding stages
  HeaderBar header_bar = 8;
}

message HeaderBar {
  // Image to be shown at the middle of the bar.
  api.typesv2.common.VisualElement center_logo = 1;
  // Image to be shown at the end of the bar.
  api.typesv2.common.VisualElement right_logo = 2;
  BackAction back_action = 3;
}
