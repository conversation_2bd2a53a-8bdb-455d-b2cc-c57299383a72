// protolint:disable MAX_LINE_LENGTH

// RPCs related to connected_account FE service .
// Ref material - https://www.onemoney.in/docs/api/

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/connected_account/service.proto

package connected_account

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common1 "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	accounts "github.com/epifi/gamma/api/accounts"
	common "github.com/epifi/gamma/api/frontend/connected_account/common"
	features "github.com/epifi/gamma/api/frontend/connected_account/features"
	screens "github.com/epifi/gamma/api/frontend/connected_account/screens"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	home "github.com/epifi/gamma/api/frontend/home"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	analyser "github.com/epifi/gamma/api/typesv2/analyser"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Purpose for Consent Request
type ConsentRequestPurpose int32

const (
	ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_UNSPECIFIED ConsentRequestPurpose = 0
	// Consent Request made for the normal consent flow
	ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW ConsentRequestPurpose = 1
	// Consent Request made for authentication purposes
	// Currently only used by Finvu AA, since it requires a consent handle while taking OTP requests
	ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW ConsentRequestPurpose = 2
	// Consent purpose made for renewal
	ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_RENEWAL ConsentRequestPurpose = 3
)

// Enum value maps for ConsentRequestPurpose.
var (
	ConsentRequestPurpose_name = map[int32]string{
		0: "CONSENT_REQUEST_PURPOSE_UNSPECIFIED",
		1: "CONSENT_REQUEST_PURPOSE_CONSENT_FLOW",
		2: "CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW",
		3: "CONSENT_REQUEST_PURPOSE_RENEWAL",
	}
	ConsentRequestPurpose_value = map[string]int32{
		"CONSENT_REQUEST_PURPOSE_UNSPECIFIED":  0,
		"CONSENT_REQUEST_PURPOSE_CONSENT_FLOW": 1,
		"CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW": 2,
		"CONSENT_REQUEST_PURPOSE_RENEWAL":      3,
	}
)

func (x ConsentRequestPurpose) Enum() *ConsentRequestPurpose {
	p := new(ConsentRequestPurpose)
	*p = x
	return p
}

func (x ConsentRequestPurpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsentRequestPurpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[0].Descriptor()
}

func (ConsentRequestPurpose) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[0]
}

func (x ConsentRequestPurpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsentRequestPurpose.Descriptor instead.
func (ConsentRequestPurpose) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{0}
}

// Unit of frequency at which data is queried
type FrequencyUnit int32

const (
	FrequencyUnit_FREQUENCY_UNIT_UNSPECIFIED FrequencyUnit = 0
	FrequencyUnit_FREQUENCY_UNIT_HOUR        FrequencyUnit = 1
	FrequencyUnit_FREQUENCY_UNIT_DAY         FrequencyUnit = 2
	FrequencyUnit_FREQUENCY_UNIT_MONTH       FrequencyUnit = 3
	FrequencyUnit_FREQUENCY_UNIT_YEAR        FrequencyUnit = 4
)

// Enum value maps for FrequencyUnit.
var (
	FrequencyUnit_name = map[int32]string{
		0: "FREQUENCY_UNIT_UNSPECIFIED",
		1: "FREQUENCY_UNIT_HOUR",
		2: "FREQUENCY_UNIT_DAY",
		3: "FREQUENCY_UNIT_MONTH",
		4: "FREQUENCY_UNIT_YEAR",
	}
	FrequencyUnit_value = map[string]int32{
		"FREQUENCY_UNIT_UNSPECIFIED": 0,
		"FREQUENCY_UNIT_HOUR":        1,
		"FREQUENCY_UNIT_DAY":         2,
		"FREQUENCY_UNIT_MONTH":       3,
		"FREQUENCY_UNIT_YEAR":        4,
	}
)

func (x FrequencyUnit) Enum() *FrequencyUnit {
	p := new(FrequencyUnit)
	*p = x
	return p
}

func (x FrequencyUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FrequencyUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[1].Descriptor()
}

func (FrequencyUnit) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[1]
}

func (x FrequencyUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FrequencyUnit.Descriptor instead.
func (FrequencyUnit) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{1}
}

type DataPullStatus int32

const (
	DataPullStatus_DATA_PULL_STATUS_UNSPECIFIED DataPullStatus = 0
	DataPullStatus_DATA_PULL_STATUS_SUCCESSFUL  DataPullStatus = 1
	DataPullStatus_DATA_PULL_STATUS_IN_PROGRESS DataPullStatus = 2
	DataPullStatus_DATA_PULL_STATUS_FAILED      DataPullStatus = 3
)

// Enum value maps for DataPullStatus.
var (
	DataPullStatus_name = map[int32]string{
		0: "DATA_PULL_STATUS_UNSPECIFIED",
		1: "DATA_PULL_STATUS_SUCCESSFUL",
		2: "DATA_PULL_STATUS_IN_PROGRESS",
		3: "DATA_PULL_STATUS_FAILED",
	}
	DataPullStatus_value = map[string]int32{
		"DATA_PULL_STATUS_UNSPECIFIED": 0,
		"DATA_PULL_STATUS_SUCCESSFUL":  1,
		"DATA_PULL_STATUS_IN_PROGRESS": 2,
		"DATA_PULL_STATUS_FAILED":      3,
	}
)

func (x DataPullStatus) Enum() *DataPullStatus {
	p := new(DataPullStatus)
	*p = x
	return p
}

func (x DataPullStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataPullStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[2].Descriptor()
}

func (DataPullStatus) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[2]
}

func (x DataPullStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataPullStatus.Descriptor instead.
func (DataPullStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{2}
}

// ExitOperation represents the last operation performed while user exited the SDK.
// This is used to signal the client that what action/deeplink should be provided to land on next screen after exit from SDK.
type ExitOperation int32

const (
	ExitOperation_EXIT_OPERATION_UNSPECIFIED   ExitOperation = 0
	ExitOperation_EXIT_OPERATION_LOGIN_OTP     ExitOperation = 1
	ExitOperation_EXIT_OPERATION_ACC_DISCOVERY ExitOperation = 2
	ExitOperation_EXIT_OPERATION_ACC_LINK      ExitOperation = 3
	ExitOperation_EXIT_OPERATION_CONSENT       ExitOperation = 4
)

// Enum value maps for ExitOperation.
var (
	ExitOperation_name = map[int32]string{
		0: "EXIT_OPERATION_UNSPECIFIED",
		1: "EXIT_OPERATION_LOGIN_OTP",
		2: "EXIT_OPERATION_ACC_DISCOVERY",
		3: "EXIT_OPERATION_ACC_LINK",
		4: "EXIT_OPERATION_CONSENT",
	}
	ExitOperation_value = map[string]int32{
		"EXIT_OPERATION_UNSPECIFIED":   0,
		"EXIT_OPERATION_LOGIN_OTP":     1,
		"EXIT_OPERATION_ACC_DISCOVERY": 2,
		"EXIT_OPERATION_ACC_LINK":      3,
		"EXIT_OPERATION_CONSENT":       4,
	}
)

func (x ExitOperation) Enum() *ExitOperation {
	p := new(ExitOperation)
	*p = x
	return p
}

func (x ExitOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExitOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[3].Descriptor()
}

func (ExitOperation) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[3]
}

func (x ExitOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExitOperation.Descriptor instead.
func (ExitOperation) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{3}
}

// ExitReason represents the reason for the exit from SDK.
// While signalling the client about the next action on exit, client also provide the reason of exit within an operation.
// for e.g. If user Accounts are not discovered then the reason can be either Timeout issue or No accounts discovered
type ExitReason int32

const (
	ExitReason_EXIT_REASON_UNSPECIFIED             ExitReason = 0
	ExitReason_EXIT_REASON_OTP_VERIFICATION_FAILED ExitReason = 2
	ExitReason_EXIT_REASON_ACC_DISCOVERY_TIMEOUT   ExitReason = 3
	ExitReason_EXIT_REASON_NO_ACC_DISCOVERED       ExitReason = 4
	ExitReason_EXIT_REASON_REQUEST_CONSENT_FAILURE ExitReason = 5
	ExitReason_EXIT_REASON_APPROVE_CONSENT_FAILURE ExitReason = 6
)

// Enum value maps for ExitReason.
var (
	ExitReason_name = map[int32]string{
		0: "EXIT_REASON_UNSPECIFIED",
		2: "EXIT_REASON_OTP_VERIFICATION_FAILED",
		3: "EXIT_REASON_ACC_DISCOVERY_TIMEOUT",
		4: "EXIT_REASON_NO_ACC_DISCOVERED",
		5: "EXIT_REASON_REQUEST_CONSENT_FAILURE",
		6: "EXIT_REASON_APPROVE_CONSENT_FAILURE",
	}
	ExitReason_value = map[string]int32{
		"EXIT_REASON_UNSPECIFIED":             0,
		"EXIT_REASON_OTP_VERIFICATION_FAILED": 2,
		"EXIT_REASON_ACC_DISCOVERY_TIMEOUT":   3,
		"EXIT_REASON_NO_ACC_DISCOVERED":       4,
		"EXIT_REASON_REQUEST_CONSENT_FAILURE": 5,
		"EXIT_REASON_APPROVE_CONSENT_FAILURE": 6,
	}
)

func (x ExitReason) Enum() *ExitReason {
	p := new(ExitReason)
	*p = x
	return p
}

func (x ExitReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExitReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[4].Descriptor()
}

func (ExitReason) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[4]
}

func (x ExitReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExitReason.Descriptor instead.
func (ExitReason) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{4}
}

type RegisterFiTncConsentResponse_Status int32

const (
	// fi consent recorded successfully
	RegisterFiTncConsentResponse_OK RegisterFiTncConsentResponse_Status = 0
	// internal server error
	RegisterFiTncConsentResponse_INTERNAL RegisterFiTncConsentResponse_Status = 13
)

// Enum value maps for RegisterFiTncConsentResponse_Status.
var (
	RegisterFiTncConsentResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	RegisterFiTncConsentResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x RegisterFiTncConsentResponse_Status) Enum() *RegisterFiTncConsentResponse_Status {
	p := new(RegisterFiTncConsentResponse_Status)
	*p = x
	return p
}

func (x RegisterFiTncConsentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegisterFiTncConsentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[5].Descriptor()
}

func (RegisterFiTncConsentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[5]
}

func (x RegisterFiTncConsentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegisterFiTncConsentResponse_Status.Descriptor instead.
func (RegisterFiTncConsentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetBenefitsScreenParamsResponse_Status int32

const (
	GetBenefitsScreenParamsResponse_OK               GetBenefitsScreenParamsResponse_Status = 0
	GetBenefitsScreenParamsResponse_INVALID_ARGUMENT GetBenefitsScreenParamsResponse_Status = 3
	GetBenefitsScreenParamsResponse_INTERNAL         GetBenefitsScreenParamsResponse_Status = 13
)

// Enum value maps for GetBenefitsScreenParamsResponse_Status.
var (
	GetBenefitsScreenParamsResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	GetBenefitsScreenParamsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x GetBenefitsScreenParamsResponse_Status) Enum() *GetBenefitsScreenParamsResponse_Status {
	p := new(GetBenefitsScreenParamsResponse_Status)
	*p = x
	return p
}

func (x GetBenefitsScreenParamsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetBenefitsScreenParamsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[6].Descriptor()
}

func (GetBenefitsScreenParamsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[6]
}

func (x GetBenefitsScreenParamsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetBenefitsScreenParamsResponse_Status.Descriptor instead.
func (GetBenefitsScreenParamsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetBenefitsScreenParamsResponse_BenefitsScreenType int32

const (
	GetBenefitsScreenParamsResponse_BENEFITS_SCREEN_TYPE_UNSPECIFIED      GetBenefitsScreenParamsResponse_BenefitsScreenType = 0
	GetBenefitsScreenParamsResponse_BENEFITS_SCREEN_TYPE_ANALYSER_CONSENT GetBenefitsScreenParamsResponse_BenefitsScreenType = 1
)

// Enum value maps for GetBenefitsScreenParamsResponse_BenefitsScreenType.
var (
	GetBenefitsScreenParamsResponse_BenefitsScreenType_name = map[int32]string{
		0: "BENEFITS_SCREEN_TYPE_UNSPECIFIED",
		1: "BENEFITS_SCREEN_TYPE_ANALYSER_CONSENT",
	}
	GetBenefitsScreenParamsResponse_BenefitsScreenType_value = map[string]int32{
		"BENEFITS_SCREEN_TYPE_UNSPECIFIED":      0,
		"BENEFITS_SCREEN_TYPE_ANALYSER_CONSENT": 1,
	}
)

func (x GetBenefitsScreenParamsResponse_BenefitsScreenType) Enum() *GetBenefitsScreenParamsResponse_BenefitsScreenType {
	p := new(GetBenefitsScreenParamsResponse_BenefitsScreenType)
	*p = x
	return p
}

func (x GetBenefitsScreenParamsResponse_BenefitsScreenType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetBenefitsScreenParamsResponse_BenefitsScreenType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[7].Descriptor()
}

func (GetBenefitsScreenParamsResponse_BenefitsScreenType) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[7]
}

func (x GetBenefitsScreenParamsResponse_BenefitsScreenType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetBenefitsScreenParamsResponse_BenefitsScreenType.Descriptor instead.
func (GetBenefitsScreenParamsResponse_BenefitsScreenType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{3, 1}
}

type InitiateConsentResponse_Status int32

const (
	// consent initiated successfully
	InitiateConsentResponse_OK InitiateConsentResponse_Status = 0
	// internal server error
	InitiateConsentResponse_INTERNAL InitiateConsentResponse_Status = 13
)

// Enum value maps for InitiateConsentResponse_Status.
var (
	InitiateConsentResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	InitiateConsentResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x InitiateConsentResponse_Status) Enum() *InitiateConsentResponse_Status {
	p := new(InitiateConsentResponse_Status)
	*p = x
	return p
}

func (x InitiateConsentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateConsentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[8].Descriptor()
}

func (InitiateConsentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[8]
}

func (x InitiateConsentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateConsentResponse_Status.Descriptor instead.
func (InitiateConsentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{7, 0}
}

type FetchConsentParamsResponse_Status int32

const (
	// consent params fetched successfully
	FetchConsentParamsResponse_OK FetchConsentParamsResponse_Status = 0
	// internal server error
	FetchConsentParamsResponse_INTERNAL FetchConsentParamsResponse_Status = 13
)

// Enum value maps for FetchConsentParamsResponse_Status.
var (
	FetchConsentParamsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	FetchConsentParamsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x FetchConsentParamsResponse_Status) Enum() *FetchConsentParamsResponse_Status {
	p := new(FetchConsentParamsResponse_Status)
	*p = x
	return p
}

func (x FetchConsentParamsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchConsentParamsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[9].Descriptor()
}

func (FetchConsentParamsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[9]
}

func (x FetchConsentParamsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchConsentParamsResponse_Status.Descriptor instead.
func (FetchConsentParamsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{9, 0}
}

type GetLinkedAaAccountsResponse_Status int32

const (
	// Financial information accounts fetched successfully for a user
	GetLinkedAaAccountsResponse_OK GetLinkedAaAccountsResponse_Status = 0
	// no linked accounts found for user
	GetLinkedAaAccountsResponse_NOT_FOUND GetLinkedAaAccountsResponse_Status = 5
	// internal server error
	GetLinkedAaAccountsResponse_INTERNAL GetLinkedAaAccountsResponse_Status = 13
)

// Enum value maps for GetLinkedAaAccountsResponse_Status.
var (
	GetLinkedAaAccountsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetLinkedAaAccountsResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetLinkedAaAccountsResponse_Status) Enum() *GetLinkedAaAccountsResponse_Status {
	p := new(GetLinkedAaAccountsResponse_Status)
	*p = x
	return p
}

func (x GetLinkedAaAccountsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetLinkedAaAccountsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[10].Descriptor()
}

func (GetLinkedAaAccountsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[10]
}

func (x GetLinkedAaAccountsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetLinkedAaAccountsResponse_Status.Descriptor instead.
func (GetLinkedAaAccountsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{14, 0}
}

type GetAccountDetailsResponse_Status int32

const (
	// Financial information accounts fetched successfully for a user
	GetAccountDetailsResponse_OK GetAccountDetailsResponse_Status = 0
	// no linked accounts found for user
	GetAccountDetailsResponse_NOT_FOUND GetAccountDetailsResponse_Status = 5
	// internal server error
	GetAccountDetailsResponse_INTERNAL GetAccountDetailsResponse_Status = 13
)

// Enum value maps for GetAccountDetailsResponse_Status.
var (
	GetAccountDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetAccountDetailsResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetAccountDetailsResponse_Status) Enum() *GetAccountDetailsResponse_Status {
	p := new(GetAccountDetailsResponse_Status)
	*p = x
	return p
}

func (x GetAccountDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[11].Descriptor()
}

func (GetAccountDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[11]
}

func (x GetAccountDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountDetailsResponse_Status.Descriptor instead.
func (GetAccountDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{16, 0}
}

type GetConnectedAccountsResponse_Status int32

const (
	// Financial information accounts fetched successfully for a user
	GetConnectedAccountsResponse_OK GetConnectedAccountsResponse_Status = 0
	// no linked accounts found for user
	GetConnectedAccountsResponse_NOT_FOUND GetConnectedAccountsResponse_Status = 5
	// internal server error
	GetConnectedAccountsResponse_INTERNAL GetConnectedAccountsResponse_Status = 13
)

// Enum value maps for GetConnectedAccountsResponse_Status.
var (
	GetConnectedAccountsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetConnectedAccountsResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetConnectedAccountsResponse_Status) Enum() *GetConnectedAccountsResponse_Status {
	p := new(GetConnectedAccountsResponse_Status)
	*p = x
	return p
}

func (x GetConnectedAccountsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetConnectedAccountsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[12].Descriptor()
}

func (GetConnectedAccountsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[12]
}

func (x GetConnectedAccountsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetConnectedAccountsResponse_Status.Descriptor instead.
func (GetConnectedAccountsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{18, 0}
}

type GetRelatedAccountsForDisconnectResponse_Status int32

const (
	// Financial information accounts fetched successfully for a user
	GetRelatedAccountsForDisconnectResponse_OK GetRelatedAccountsForDisconnectResponse_Status = 0
	// no related accounts for disconnection
	GetRelatedAccountsForDisconnectResponse_NOT_FOUND GetRelatedAccountsForDisconnectResponse_Status = 5
	// internal server error
	GetRelatedAccountsForDisconnectResponse_INTERNAL GetRelatedAccountsForDisconnectResponse_Status = 13
)

// Enum value maps for GetRelatedAccountsForDisconnectResponse_Status.
var (
	GetRelatedAccountsForDisconnectResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetRelatedAccountsForDisconnectResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetRelatedAccountsForDisconnectResponse_Status) Enum() *GetRelatedAccountsForDisconnectResponse_Status {
	p := new(GetRelatedAccountsForDisconnectResponse_Status)
	*p = x
	return p
}

func (x GetRelatedAccountsForDisconnectResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRelatedAccountsForDisconnectResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[13].Descriptor()
}

func (GetRelatedAccountsForDisconnectResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[13]
}

func (x GetRelatedAccountsForDisconnectResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRelatedAccountsForDisconnectResponse_Status.Descriptor instead.
func (GetRelatedAccountsForDisconnectResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{20, 0}
}

type GetRelatedAccountsForDeleteResponse_Status int32

const (
	// Financial information accounts fetched successfully for a user
	GetRelatedAccountsForDeleteResponse_OK GetRelatedAccountsForDeleteResponse_Status = 0
	// internal server error in fetching accounts
	GetRelatedAccountsForDeleteResponse_INTERNAL GetRelatedAccountsForDeleteResponse_Status = 13
)

// Enum value maps for GetRelatedAccountsForDeleteResponse_Status.
var (
	GetRelatedAccountsForDeleteResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetRelatedAccountsForDeleteResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetRelatedAccountsForDeleteResponse_Status) Enum() *GetRelatedAccountsForDeleteResponse_Status {
	p := new(GetRelatedAccountsForDeleteResponse_Status)
	*p = x
	return p
}

func (x GetRelatedAccountsForDeleteResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRelatedAccountsForDeleteResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[14].Descriptor()
}

func (GetRelatedAccountsForDeleteResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[14]
}

func (x GetRelatedAccountsForDeleteResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRelatedAccountsForDeleteResponse_Status.Descriptor instead.
func (GetRelatedAccountsForDeleteResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{22, 0}
}

type DeleteAccountResponse_Status int32

const (
	// Account deletion request successfully accepted by the server
	DeleteAccountResponse_OK DeleteAccountResponse_Status = 0
	// account not found in system
	DeleteAccountResponse_NOT_FOUND DeleteAccountResponse_Status = 5
	// account was not in disconnected state hence not deleted by the server
	DeleteAccountResponse_FAILED_PRECONDITION DeleteAccountResponse_Status = 9
	// internal server error
	DeleteAccountResponse_INTERNAL DeleteAccountResponse_Status = 13
)

// Enum value maps for DeleteAccountResponse_Status.
var (
	DeleteAccountResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		9:  "FAILED_PRECONDITION",
		13: "INTERNAL",
	}
	DeleteAccountResponse_Status_value = map[string]int32{
		"OK":                  0,
		"NOT_FOUND":           5,
		"FAILED_PRECONDITION": 9,
		"INTERNAL":            13,
	}
)

func (x DeleteAccountResponse_Status) Enum() *DeleteAccountResponse_Status {
	p := new(DeleteAccountResponse_Status)
	*p = x
	return p
}

func (x DeleteAccountResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteAccountResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_connected_account_service_proto_enumTypes[15].Descriptor()
}

func (DeleteAccountResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_connected_account_service_proto_enumTypes[15]
}

func (x DeleteAccountResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteAccountResponse_Status.Descriptor instead.
func (DeleteAccountResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{24, 0}
}

type RegisterFiTncConsentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req        *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	ConsentIds []string              `protobuf:"bytes,2,rep,name=consent_ids,json=consentIds,proto3" json:"consent_ids,omitempty"`
}

func (x *RegisterFiTncConsentRequest) Reset() {
	*x = RegisterFiTncConsentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterFiTncConsentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterFiTncConsentRequest) ProtoMessage() {}

func (x *RegisterFiTncConsentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterFiTncConsentRequest.ProtoReflect.Descriptor instead.
func (*RegisterFiTncConsentRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterFiTncConsentRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *RegisterFiTncConsentRequest) GetConsentIds() []string {
	if x != nil {
		return x.ConsentIds
	}
	return nil
}

type RegisterFiTncConsentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *RegisterFiTncConsentResponse) Reset() {
	*x = RegisterFiTncConsentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterFiTncConsentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterFiTncConsentResponse) ProtoMessage() {}

func (x *RegisterFiTncConsentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterFiTncConsentResponse.ProtoReflect.Descriptor instead.
func (*RegisterFiTncConsentResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterFiTncConsentResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetBenefitsScreenParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// ca_flow_name represents the name of other service which is trying to use connected account flow
	CaFlowName string `protobuf:"bytes,2,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// A unique identifier of the flow started to connect accounts
	CaFlowId string `protobuf:"bytes,3,opt,name=ca_flow_id,json=caFlowId,proto3" json:"ca_flow_id,omitempty"`
}

func (x *GetBenefitsScreenParamsRequest) Reset() {
	*x = GetBenefitsScreenParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBenefitsScreenParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBenefitsScreenParamsRequest) ProtoMessage() {}

func (x *GetBenefitsScreenParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBenefitsScreenParamsRequest.ProtoReflect.Descriptor instead.
func (*GetBenefitsScreenParamsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetBenefitsScreenParamsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetBenefitsScreenParamsRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *GetBenefitsScreenParamsRequest) GetCaFlowId() string {
	if x != nil {
		return x.CaFlowId
	}
	return ""
}

type GetBenefitsScreenParamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader         *header.ResponseHeader                             `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	BenefitsScreenType GetBenefitsScreenParamsResponse_BenefitsScreenType `protobuf:"varint,2,opt,name=benefits_screen_type,json=benefitsScreenType,proto3,enum=frontend.connected_account.GetBenefitsScreenParamsResponse_BenefitsScreenType" json:"benefits_screen_type,omitempty"`
	// screen rendering and behaviour will change on the basis of the oneof param
	//
	// Types that are assignable to Params:
	//
	//	*GetBenefitsScreenParamsResponse_AnalyserConsent
	Params isGetBenefitsScreenParamsResponse_Params `protobuf_oneof:"params"`
	// redirect deeplink to another screen if consent is not required
	// client should ignore benefits screen params and jump to redirect deeplink
	RedirectDeeplink *deeplink.Deeplink `protobuf:"bytes,4,opt,name=redirect_deeplink,json=redirectDeeplink,proto3" json:"redirect_deeplink,omitempty"`
}

func (x *GetBenefitsScreenParamsResponse) Reset() {
	*x = GetBenefitsScreenParamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBenefitsScreenParamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBenefitsScreenParamsResponse) ProtoMessage() {}

func (x *GetBenefitsScreenParamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBenefitsScreenParamsResponse.ProtoReflect.Descriptor instead.
func (*GetBenefitsScreenParamsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetBenefitsScreenParamsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetBenefitsScreenParamsResponse) GetBenefitsScreenType() GetBenefitsScreenParamsResponse_BenefitsScreenType {
	if x != nil {
		return x.BenefitsScreenType
	}
	return GetBenefitsScreenParamsResponse_BENEFITS_SCREEN_TYPE_UNSPECIFIED
}

func (m *GetBenefitsScreenParamsResponse) GetParams() isGetBenefitsScreenParamsResponse_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *GetBenefitsScreenParamsResponse) GetAnalyserConsent() *analyser.ConsentScreen {
	if x, ok := x.GetParams().(*GetBenefitsScreenParamsResponse_AnalyserConsent); ok {
		return x.AnalyserConsent
	}
	return nil
}

func (x *GetBenefitsScreenParamsResponse) GetRedirectDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.RedirectDeeplink
	}
	return nil
}

type isGetBenefitsScreenParamsResponse_Params interface {
	isGetBenefitsScreenParamsResponse_Params()
}

type GetBenefitsScreenParamsResponse_AnalyserConsent struct {
	AnalyserConsent *analyser.ConsentScreen `protobuf:"bytes,3,opt,name=analyser_consent,json=analyserConsent,proto3,oneof"`
}

func (*GetBenefitsScreenParamsResponse_AnalyserConsent) isGetBenefitsScreenParamsResponse_Params() {}

type GetLandingPageForConnectingFiToFiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// MANDATORY, Reason: we have finalised which AA Entity to be used for the flow at the entry point level itself,
	// now here while sending SDK initialisation params, the same AA Entity should be used.
	AaEntity typesv2.AaEntity `protobuf:"varint,2,opt,name=aa_entity,json=aaEntity,proto3,enum=api.typesv2.AaEntity" json:"aa_entity,omitempty"`
}

func (x *GetLandingPageForConnectingFiToFiRequest) Reset() {
	*x = GetLandingPageForConnectingFiToFiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingPageForConnectingFiToFiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingPageForConnectingFiToFiRequest) ProtoMessage() {}

func (x *GetLandingPageForConnectingFiToFiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingPageForConnectingFiToFiRequest.ProtoReflect.Descriptor instead.
func (*GetLandingPageForConnectingFiToFiRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetLandingPageForConnectingFiToFiRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetLandingPageForConnectingFiToFiRequest) GetAaEntity() typesv2.AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return typesv2.AaEntity(0)
}

type GetLandingPageForConnectingFiToFiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink to initiate connecting Fi to Fi flow
	InitFiToFiFlowDeeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=init_fi_to_fi_flow_deeplink,json=initFiToFiFlowDeeplink,proto3" json:"init_fi_to_fi_flow_deeplink,omitempty"`
}

func (x *GetLandingPageForConnectingFiToFiResponse) Reset() {
	*x = GetLandingPageForConnectingFiToFiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingPageForConnectingFiToFiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingPageForConnectingFiToFiResponse) ProtoMessage() {}

func (x *GetLandingPageForConnectingFiToFiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingPageForConnectingFiToFiResponse.ProtoReflect.Descriptor instead.
func (*GetLandingPageForConnectingFiToFiResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetLandingPageForConnectingFiToFiResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetLandingPageForConnectingFiToFiResponse) GetInitFiToFiFlowDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.InitFiToFiFlowDeeplink
	}
	return nil
}

type InitiateConsentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// vua generated at the AA end for the user
	Vua string `protobuf:"bytes,2,opt,name=vua,proto3" json:"vua,omitempty"`
	// entity for which request is being made, defaults to one money aa if unspecified
	AaEntity AaEntity `protobuf:"varint,3,opt,name=aa_entity,json=aaEntity,proto3,enum=frontend.connected_account.AaEntity" json:"aa_entity,omitempty"`
	// purpose for which request is being made, defaults to consent flow if unspecified
	ConsentRequestPurpose ConsentRequestPurpose `protobuf:"varint,4,opt,name=consent_request_purpose,json=consentRequestPurpose,proto3,enum=frontend.connected_account.ConsentRequestPurpose" json:"consent_request_purpose,omitempty"`
	// ca_flow_name represents identifier of other service which is trying to use connected account flow
	// The value for this string has to be passed as is which is given with deeplink at the time of initializing SDK
	// In case string does not match with respective service identifier, then by default connected account service flow will be followed
	CaFlowName string `protobuf:"bytes,5,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// number of consent handles to generate
	// We usually take consent at FIP level. In this case this parameter will have value 1.
	// But for some FIPs like NSDL we take consent at account level. In this case this parameter will have value equal to number of accounts for that FIP
	// In future when we take consent for all FIPs in one step, this parameter will be mostly equal to number of FIPs ( Though In some cases like NSDL, we will take consent at account level )
	NumOfConsentHandlesToGenerate int32 `protobuf:"varint,6,opt,name=num_of_consent_handles_to_generate,json=numOfConsentHandlesToGenerate,proto3" json:"num_of_consent_handles_to_generate,omitempty"`
	// Unique identifier of a flow to connect an external account
	ConnectionFlowId string `protobuf:"bytes,7,opt,name=connection_flow_id,json=connectionFlowId,proto3" json:"connection_flow_id,omitempty"`
}

func (x *InitiateConsentRequest) Reset() {
	*x = InitiateConsentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateConsentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateConsentRequest) ProtoMessage() {}

func (x *InitiateConsentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateConsentRequest.ProtoReflect.Descriptor instead.
func (*InitiateConsentRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{6}
}

func (x *InitiateConsentRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *InitiateConsentRequest) GetVua() string {
	if x != nil {
		return x.Vua
	}
	return ""
}

func (x *InitiateConsentRequest) GetAaEntity() AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return AaEntity_AA_ENTITY_UNSPECIFIED
}

func (x *InitiateConsentRequest) GetConsentRequestPurpose() ConsentRequestPurpose {
	if x != nil {
		return x.ConsentRequestPurpose
	}
	return ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_UNSPECIFIED
}

func (x *InitiateConsentRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *InitiateConsentRequest) GetNumOfConsentHandlesToGenerate() int32 {
	if x != nil {
		return x.NumOfConsentHandlesToGenerate
	}
	return 0
}

func (x *InitiateConsentRequest) GetConnectionFlowId() string {
	if x != nil {
		return x.ConnectionFlowId
	}
	return ""
}

type InitiateConsentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// consent_handle generated from the consent request.
	// Deprecated in favour of consent_handle_list
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	ConsentHandle string                 `protobuf:"bytes,2,opt,name=consent_handle,json=consentHandle,proto3" json:"consent_handle,omitempty"`
	RespHeader    *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink to land on the post consent approval screen
	NextAction *deeplink.Deeplink `protobuf:"bytes,3,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// list of consent handles generated
	ConsentHandleList []string `protobuf:"bytes,4,rep,name=consent_handle_list,json=consentHandleList,proto3" json:"consent_handle_list,omitempty"`
}

func (x *InitiateConsentResponse) Reset() {
	*x = InitiateConsentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateConsentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateConsentResponse) ProtoMessage() {}

func (x *InitiateConsentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateConsentResponse.ProtoReflect.Descriptor instead.
func (*InitiateConsentResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{7}
}

func (x *InitiateConsentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *InitiateConsentResponse) GetConsentHandle() string {
	if x != nil {
		return x.ConsentHandle
	}
	return ""
}

func (x *InitiateConsentResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *InitiateConsentResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *InitiateConsentResponse) GetConsentHandleList() []string {
	if x != nil {
		return x.ConsentHandleList
	}
	return nil
}

type FetchConsentParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// aa_entity is required for forming AA Entity specific consent parameters, e.g. footer note in response is different for Finvu and Onemoney
	// Optional param
	AaEntity typesv2.AaEntity `protobuf:"varint,2,opt,name=aa_entity,json=aaEntity,proto3,enum=api.typesv2.AaEntity" json:"aa_entity,omitempty"`
}

func (x *FetchConsentParamsRequest) Reset() {
	*x = FetchConsentParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchConsentParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchConsentParamsRequest) ProtoMessage() {}

func (x *FetchConsentParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchConsentParamsRequest.ProtoReflect.Descriptor instead.
func (*FetchConsentParamsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{8}
}

func (x *FetchConsentParamsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *FetchConsentParamsRequest) GetAaEntity() typesv2.AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return typesv2.AaEntity(0)
}

type FetchConsentParamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	Consent *Consent `protobuf:"bytes,2,opt,name=consent,proto3" json:"consent,omitempty"`
	// more details about the consent
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	ConsentMeta *ConsentMeta `protobuf:"bytes,3,opt,name=consent_meta,json=consentMeta,proto3" json:"consent_meta,omitempty"`
	// ConsentParameter is a map of consent parameters which consist of key (name/title of the consent parameter) and value of the consent parameter,
	// all the consent parameters are displayed in sequence of keys.
	ConsentParams map[string]string `protobuf:"bytes,4,rep,name=consent_params,json=consentParams,proto3" json:"consent_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// aa_entity_consent_bottom_info_text denotes the consent note displayed according to the AA entity.
	AaEntityConsentBottomInfoText string                 `protobuf:"bytes,5,opt,name=aa_entity_consent_bottom_info_text,json=aaEntityConsentBottomInfoText,proto3" json:"aa_entity_consent_bottom_info_text,omitempty"`
	RespHeader                    *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// consent_details_html is the html string used to display consent details with a tappable view details text
	ConsentDetailsHtmlString string `protobuf:"bytes,6,opt,name=consent_details_html_string,json=consentDetailsHtmlString,proto3" json:"consent_details_html_string,omitempty"`
}

func (x *FetchConsentParamsResponse) Reset() {
	*x = FetchConsentParamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchConsentParamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchConsentParamsResponse) ProtoMessage() {}

func (x *FetchConsentParamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchConsentParamsResponse.ProtoReflect.Descriptor instead.
func (*FetchConsentParamsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{9}
}

func (x *FetchConsentParamsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *FetchConsentParamsResponse) GetConsent() *Consent {
	if x != nil {
		return x.Consent
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *FetchConsentParamsResponse) GetConsentMeta() *ConsentMeta {
	if x != nil {
		return x.ConsentMeta
	}
	return nil
}

func (x *FetchConsentParamsResponse) GetConsentParams() map[string]string {
	if x != nil {
		return x.ConsentParams
	}
	return nil
}

func (x *FetchConsentParamsResponse) GetAaEntityConsentBottomInfoText() string {
	if x != nil {
		return x.AaEntityConsentBottomInfoText
	}
	return ""
}

func (x *FetchConsentParamsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *FetchConsentParamsResponse) GetConsentDetailsHtmlString() string {
	if x != nil {
		return x.ConsentDetailsHtmlString
	}
	return ""
}

type Consent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date of data fetch range start
	From *typesv2.Date `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// date of data fetch range end
	To *typesv2.Date `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	// text for displaying range of data fetch
	DisplayTextRange string `protobuf:"bytes,3,opt,name=display_text_range,json=displayTextRange,proto3" json:"display_text_range,omitempty"`
	// text for displaying validity time period of a consent
	DisplayTextValidPeriod string `protobuf:"bytes,4,opt,name=display_text_valid_period,json=displayTextValidPeriod,proto3" json:"display_text_valid_period,omitempty"`
}

func (x *Consent) Reset() {
	*x = Consent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Consent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Consent) ProtoMessage() {}

func (x *Consent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Consent.ProtoReflect.Descriptor instead.
func (*Consent) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{10}
}

func (x *Consent) GetFrom() *typesv2.Date {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *Consent) GetTo() *typesv2.Date {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *Consent) GetDisplayTextRange() string {
	if x != nil {
		return x.DisplayTextRange
	}
	return ""
}

func (x *Consent) GetDisplayTextValidPeriod() string {
	if x != nil {
		return x.DisplayTextValidPeriod
	}
	return ""
}

type ConsentMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// purpose of the consent
	DisplayTextConsentPurpose string `protobuf:"bytes,1,opt,name=display_text_consent_purpose,json=displayTextConsentPurpose,proto3" json:"display_text_consent_purpose,omitempty"`
	// types of consent shared, ex - Transactions, Summary, Profile
	DisplayTextConsentTypesShared string   `protobuf:"bytes,2,opt,name=display_text_consent_types_shared,json=displayTextConsentTypesShared,proto3" json:"display_text_consent_types_shared,omitempty"`
	ConsentTypesShared            []string `protobuf:"bytes,3,rep,name=consent_types_shared,json=consentTypesShared,proto3" json:"consent_types_shared,omitempty"`
	// frequency of data fetch
	DisplayTextFetchFrequency string     `protobuf:"bytes,4,opt,name=display_text_fetch_frequency,json=displayTextFetchFrequency,proto3" json:"display_text_fetch_frequency,omitempty"`
	FetchFrequency            *Frequency `protobuf:"bytes,5,opt,name=fetch_frequency,json=fetchFrequency,proto3" json:"fetch_frequency,omitempty"`
}

func (x *ConsentMeta) Reset() {
	*x = ConsentMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentMeta) ProtoMessage() {}

func (x *ConsentMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentMeta.ProtoReflect.Descriptor instead.
func (*ConsentMeta) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{11}
}

func (x *ConsentMeta) GetDisplayTextConsentPurpose() string {
	if x != nil {
		return x.DisplayTextConsentPurpose
	}
	return ""
}

func (x *ConsentMeta) GetDisplayTextConsentTypesShared() string {
	if x != nil {
		return x.DisplayTextConsentTypesShared
	}
	return ""
}

func (x *ConsentMeta) GetConsentTypesShared() []string {
	if x != nil {
		return x.ConsentTypesShared
	}
	return nil
}

func (x *ConsentMeta) GetDisplayTextFetchFrequency() string {
	if x != nil {
		return x.DisplayTextFetchFrequency
	}
	return ""
}

func (x *ConsentMeta) GetFetchFrequency() *Frequency {
	if x != nil {
		return x.FetchFrequency
	}
	return nil
}

type Frequency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unit FrequencyUnit `protobuf:"varint,1,opt,name=unit,proto3,enum=frontend.connected_account.FrequencyUnit" json:"unit,omitempty"`
	// Define how many times consumer can access the financial information
	Val int32 `protobuf:"varint,2,opt,name=val,proto3" json:"val,omitempty"`
}

func (x *Frequency) Reset() {
	*x = Frequency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Frequency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Frequency) ProtoMessage() {}

func (x *Frequency) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Frequency.ProtoReflect.Descriptor instead.
func (*Frequency) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{12}
}

func (x *Frequency) GetUnit() FrequencyUnit {
	if x != nil {
		return x.Unit
	}
	return FrequencyUnit_FREQUENCY_UNIT_UNSPECIFIED
}

func (x *Frequency) GetVal() int32 {
	if x != nil {
		return x.Val
	}
	return 0
}

type GetLinkedAaAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetLinkedAaAccountsRequest) Reset() {
	*x = GetLinkedAaAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkedAaAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkedAaAccountsRequest) ProtoMessage() {}

func (x *GetLinkedAaAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkedAaAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetLinkedAaAccountsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetLinkedAaAccountsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetLinkedAaAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of synced accounts of the user
	Accounts []*Account `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
}

func (x *GetLinkedAaAccountsResponse) Reset() {
	*x = GetLinkedAaAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkedAaAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkedAaAccountsResponse) ProtoMessage() {}

func (x *GetLinkedAaAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkedAaAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetLinkedAaAccountsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetLinkedAaAccountsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLinkedAaAccountsResponse) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type GetAccountDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Account id as returned in get accounts API for which details are to be fetched
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetAccountDetailsRequest) Reset() {
	*x = GetAccountDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountDetailsRequest) ProtoMessage() {}

func (x *GetAccountDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetAccountDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetAccountDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAccountDetailsRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetAccountDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// details of the account
	AccountDetail *AccountDetail `protobuf:"bytes,2,opt,name=account_detail,json=accountDetail,proto3" json:"account_detail,omitempty"`
	// List of all available actions for this account
	AccountActionOptionList []*AccountActionOption `protobuf:"bytes,3,rep,name=account_action_option_list,json=accountActionOptionList,proto3" json:"account_action_option_list,omitempty"`
	// optional: popup for consent renewal
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&mode=dev
	ConsentRenewalPopup *features.AaConsentRenewalPopupOptions `protobuf:"bytes,4,opt,name=consent_renewal_popup,json=consentRenewalPopup,proto3" json:"consent_renewal_popup,omitempty"`
	// optional: action banner in account details page
	//
	//	https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=12564%3A112648&mode=dev
	ActionBanner *ui.IconTextComponent `protobuf:"bytes,5,opt,name=action_banner,json=actionBanner,proto3" json:"action_banner,omitempty"`
}

func (x *GetAccountDetailsResponse) Reset() {
	*x = GetAccountDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountDetailsResponse) ProtoMessage() {}

func (x *GetAccountDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetAccountDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetAccountDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountDetailsResponse) GetAccountDetail() *AccountDetail {
	if x != nil {
		return x.AccountDetail
	}
	return nil
}

func (x *GetAccountDetailsResponse) GetAccountActionOptionList() []*AccountActionOption {
	if x != nil {
		return x.AccountActionOptionList
	}
	return nil
}

func (x *GetAccountDetailsResponse) GetConsentRenewalPopup() *features.AaConsentRenewalPopupOptions {
	if x != nil {
		return x.ConsentRenewalPopup
	}
	return nil
}

func (x *GetAccountDetailsResponse) GetActionBanner() *ui.IconTextComponent {
	if x != nil {
		return x.ActionBanner
	}
	return nil
}

type GetConnectedAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Mandatory parameter to pass with minimum length 1
	AccountStatusList []AccountStatus `protobuf:"varint,2,rep,packed,name=account_status_list,json=accountStatusList,proto3,enum=frontend.connected_account.AccountStatus" json:"account_status_list,omitempty"`
}

func (x *GetConnectedAccountsRequest) Reset() {
	*x = GetConnectedAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectedAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectedAccountsRequest) ProtoMessage() {}

func (x *GetConnectedAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectedAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetConnectedAccountsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetConnectedAccountsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetConnectedAccountsRequest) GetAccountStatusList() []AccountStatus {
	if x != nil {
		return x.AccountStatusList
	}
	return nil
}

type GetConnectedAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status            *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AccountDetailList []*AccountDetail `protobuf:"bytes,2,rep,name=account_detail_list,json=accountDetailList,proto3" json:"account_detail_list,omitempty"`
}

func (x *GetConnectedAccountsResponse) Reset() {
	*x = GetConnectedAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectedAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectedAccountsResponse) ProtoMessage() {}

func (x *GetConnectedAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectedAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetConnectedAccountsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetConnectedAccountsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetConnectedAccountsResponse) GetAccountDetailList() []*AccountDetail {
	if x != nil {
		return x.AccountDetailList
	}
	return nil
}

type GetRelatedAccountsForDisconnectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Account id for which related accounts are to be fetched for disconnect
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetRelatedAccountsForDisconnectRequest) Reset() {
	*x = GetRelatedAccountsForDisconnectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelatedAccountsForDisconnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelatedAccountsForDisconnectRequest) ProtoMessage() {}

func (x *GetRelatedAccountsForDisconnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelatedAccountsForDisconnectRequest.ProtoReflect.Descriptor instead.
func (*GetRelatedAccountsForDisconnectRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetRelatedAccountsForDisconnectRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetRelatedAccountsForDisconnectRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetRelatedAccountsForDisconnectResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of accounts which need to be disconnected with current one
	AccountDetailList []*AccountDetail `protobuf:"bytes,2,rep,name=account_detail_list,json=accountDetailList,proto3" json:"account_detail_list,omitempty"`
	// List of consent handles which need to be revoked to disconnect the accounts
	ConsentHandleList []string `protobuf:"bytes,3,rep,name=consent_handle_list,json=consentHandleList,proto3" json:"consent_handle_list,omitempty"`
	// List of consent ids
	ConsentIdList []string `protobuf:"bytes,4,rep,name=consent_id_list,json=consentIdList,proto3" json:"consent_id_list,omitempty"`
	// Bottom-sheet screen for disconnect confirmation
	ConfirmBottomSheet *deeplink.Deeplink `protobuf:"bytes,5,opt,name=confirm_bottom_sheet,json=confirmBottomSheet,proto3" json:"confirm_bottom_sheet,omitempty"`
}

func (x *GetRelatedAccountsForDisconnectResponse) Reset() {
	*x = GetRelatedAccountsForDisconnectResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelatedAccountsForDisconnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelatedAccountsForDisconnectResponse) ProtoMessage() {}

func (x *GetRelatedAccountsForDisconnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelatedAccountsForDisconnectResponse.ProtoReflect.Descriptor instead.
func (*GetRelatedAccountsForDisconnectResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetRelatedAccountsForDisconnectResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRelatedAccountsForDisconnectResponse) GetAccountDetailList() []*AccountDetail {
	if x != nil {
		return x.AccountDetailList
	}
	return nil
}

func (x *GetRelatedAccountsForDisconnectResponse) GetConsentHandleList() []string {
	if x != nil {
		return x.ConsentHandleList
	}
	return nil
}

func (x *GetRelatedAccountsForDisconnectResponse) GetConsentIdList() []string {
	if x != nil {
		return x.ConsentIdList
	}
	return nil
}

func (x *GetRelatedAccountsForDisconnectResponse) GetConfirmBottomSheet() *deeplink.Deeplink {
	if x != nil {
		return x.ConfirmBottomSheet
	}
	return nil
}

type GetRelatedAccountsForDeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Account id for which related accounts are to be fetched for delete
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetRelatedAccountsForDeleteRequest) Reset() {
	*x = GetRelatedAccountsForDeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelatedAccountsForDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelatedAccountsForDeleteRequest) ProtoMessage() {}

func (x *GetRelatedAccountsForDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelatedAccountsForDeleteRequest.ProtoReflect.Descriptor instead.
func (*GetRelatedAccountsForDeleteRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetRelatedAccountsForDeleteRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetRelatedAccountsForDeleteRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetRelatedAccountsForDeleteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// List of accounts which need to be deleted with current one
	AccountDetailList []*AccountDetail `protobuf:"bytes,2,rep,name=account_detail_list,json=accountDetailList,proto3" json:"account_detail_list,omitempty"`
	// Bottom-sheet screen for delete confirmation
	ConfirmBottomSheet *deeplink.Deeplink `protobuf:"bytes,3,opt,name=confirm_bottom_sheet,json=confirmBottomSheet,proto3" json:"confirm_bottom_sheet,omitempty"`
}

func (x *GetRelatedAccountsForDeleteResponse) Reset() {
	*x = GetRelatedAccountsForDeleteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelatedAccountsForDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelatedAccountsForDeleteResponse) ProtoMessage() {}

func (x *GetRelatedAccountsForDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelatedAccountsForDeleteResponse.ProtoReflect.Descriptor instead.
func (*GetRelatedAccountsForDeleteResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetRelatedAccountsForDeleteResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetRelatedAccountsForDeleteResponse) GetAccountDetailList() []*AccountDetail {
	if x != nil {
		return x.AccountDetailList
	}
	return nil
}

func (x *GetRelatedAccountsForDeleteResponse) GetConfirmBottomSheet() *deeplink.Deeplink {
	if x != nil {
		return x.ConfirmBottomSheet
	}
	return nil
}

type DeleteAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Account id for the account which is to be deleted
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *DeleteAccountRequest) Reset() {
	*x = DeleteAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAccountRequest) ProtoMessage() {}

func (x *DeleteAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAccountRequest.ProtoReflect.Descriptor instead.
func (*DeleteAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteAccountRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *DeleteAccountRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type DeleteAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteAccountResponse) Reset() {
	*x = DeleteAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAccountResponse) ProtoMessage() {}

func (x *DeleteAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAccountResponse.ProtoReflect.Descriptor instead.
func (*DeleteAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CheckReoobeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// No longer needed to be passed by client
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	Vua string `protobuf:"bytes,2,opt,name=vua,proto3" json:"vua,omitempty"`
}

func (x *CheckReoobeRequest) Reset() {
	*x = CheckReoobeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckReoobeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckReoobeRequest) ProtoMessage() {}

func (x *CheckReoobeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckReoobeRequest.ProtoReflect.Descriptor instead.
func (*CheckReoobeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{25}
}

func (x *CheckReoobeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *CheckReoobeRequest) GetVua() string {
	if x != nil {
		return x.Vua
	}
	return ""
}

type CheckReoobeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of accounts which need to be deleted in case of reoobe by a user
	// will be returned only in case user has done reoobe
	AccountDetailList []*AccountDetail `protobuf:"bytes,3,rep,name=account_detail_list,json=accountDetailList,proto3" json:"account_detail_list,omitempty"`
	// In case user went through reoobe, we will return old vua of the user
	OldVua string `protobuf:"bytes,4,opt,name=old_vua,json=oldVua,proto3" json:"old_vua,omitempty"`
}

func (x *CheckReoobeResponse) Reset() {
	*x = CheckReoobeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckReoobeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckReoobeResponse) ProtoMessage() {}

func (x *CheckReoobeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckReoobeResponse.ProtoReflect.Descriptor instead.
func (*CheckReoobeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{26}
}

func (x *CheckReoobeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckReoobeResponse) GetAccountDetailList() []*AccountDetail {
	if x != nil {
		return x.AccountDetailList
	}
	return nil
}

func (x *CheckReoobeResponse) GetOldVua() string {
	if x != nil {
		return x.OldVua
	}
	return ""
}

type HandleReoobeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// No longer needed to be passed by client
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	Vua string `protobuf:"bytes,2,opt,name=vua,proto3" json:"vua,omitempty"`
	// Backend will determine which accounts to delete automatically, no need to pass
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	AccountDetailList []*AccountDetail `protobuf:"bytes,3,rep,name=account_detail_list,json=accountDetailList,proto3" json:"account_detail_list,omitempty"`
}

func (x *HandleReoobeRequest) Reset() {
	*x = HandleReoobeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleReoobeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleReoobeRequest) ProtoMessage() {}

func (x *HandleReoobeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleReoobeRequest.ProtoReflect.Descriptor instead.
func (*HandleReoobeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{27}
}

func (x *HandleReoobeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *HandleReoobeRequest) GetVua() string {
	if x != nil {
		return x.Vua
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *HandleReoobeRequest) GetAccountDetailList() []*AccountDetail {
	if x != nil {
		return x.AccountDetailList
	}
	return nil
}

type HandleReoobeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *HandleReoobeResponse) Reset() {
	*x = HandleReoobeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleReoobeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleReoobeResponse) ProtoMessage() {}

func (x *HandleReoobeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleReoobeResponse.ProtoReflect.Descriptor instead.
func (*HandleReoobeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{28}
}

func (x *HandleReoobeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// account metadata
type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of Financial Information, ex - DEPOSIT, TERM_DEPOSIT, RECURRING_DEPOSIT
	FiType string `protobuf:"bytes,1,opt,name=fi_type,json=fiType,proto3" json:"fi_type,omitempty"`
	// Identifier for Financial information provider
	FipId string `protobuf:"bytes,2,opt,name=fip_id,json=fipId,proto3" json:"fip_id,omitempty"`
	// Linked ref number of the account
	LinkedRefNumber string `protobuf:"bytes,3,opt,name=linked_ref_number,json=linkedRefNumber,proto3" json:"linked_ref_number,omitempty"`
	// Account number in masked format
	MaskedAccNumber string `protobuf:"bytes,4,opt,name=masked_acc_number,json=maskedAccNumber,proto3" json:"masked_acc_number,omitempty"`
	// account id of the account, internally generated
	// Will be returned only if data was successfully persisted for the account
	AccountId string `protobuf:"bytes,5,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// FIP logo
	FipLogoUrl string `protobuf:"bytes,6,opt,name=fip_logo_url,json=fipLogoUrl,proto3" json:"fip_logo_url,omitempty"`
	// FIP name, each FIP ID will be mapped to FIP name if needed
	FipName string `protobuf:"bytes,7,opt,name=fip_name,json=fipName,proto3" json:"fip_name,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{29}
}

func (x *Account) GetFiType() string {
	if x != nil {
		return x.FiType
	}
	return ""
}

func (x *Account) GetFipId() string {
	if x != nil {
		return x.FipId
	}
	return ""
}

func (x *Account) GetLinkedRefNumber() string {
	if x != nil {
		return x.LinkedRefNumber
	}
	return ""
}

func (x *Account) GetMaskedAccNumber() string {
	if x != nil {
		return x.MaskedAccNumber
	}
	return ""
}

func (x *Account) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Account) GetFipLogoUrl() string {
	if x != nil {
		return x.FipLogoUrl
	}
	return ""
}

func (x *Account) GetFipName() string {
	if x != nil {
		return x.FipName
	}
	return ""
}

type GetAvailableFipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetAvailableFipsRequest) Reset() {
	*x = GetAvailableFipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableFipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableFipsRequest) ProtoMessage() {}

func (x *GetAvailableFipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableFipsRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableFipsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetAvailableFipsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetAvailableFipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FipMetaList []*FipMeta  `protobuf:"bytes,2,rep,name=fip_meta_list,json=fipMetaList,proto3" json:"fip_meta_list,omitempty"`
}

func (x *GetAvailableFipsResponse) Reset() {
	*x = GetAvailableFipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableFipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableFipsResponse) ProtoMessage() {}

func (x *GetAvailableFipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableFipsResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableFipsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetAvailableFipsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAvailableFipsResponse) GetFipMetaList() []*FipMeta {
	if x != nil {
		return x.FipMetaList
	}
	return nil
}

type CreateBankPreferenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req      *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	BankList []typesv2.Bank        `protobuf:"varint,2,rep,packed,name=bank_list,json=bankList,proto3,enum=api.typesv2.Bank" json:"bank_list,omitempty"`
}

func (x *CreateBankPreferenceRequest) Reset() {
	*x = CreateBankPreferenceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBankPreferenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBankPreferenceRequest) ProtoMessage() {}

func (x *CreateBankPreferenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBankPreferenceRequest.ProtoReflect.Descriptor instead.
func (*CreateBankPreferenceRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{32}
}

func (x *CreateBankPreferenceRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *CreateBankPreferenceRequest) GetBankList() []typesv2.Bank {
	if x != nil {
		return x.BankList
	}
	return nil
}

type CreateBankPreferenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateBankPreferenceResponse) Reset() {
	*x = CreateBankPreferenceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBankPreferenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBankPreferenceResponse) ProtoMessage() {}

func (x *CreateBankPreferenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBankPreferenceResponse.ProtoReflect.Descriptor instead.
func (*CreateBankPreferenceResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{33}
}

func (x *CreateBankPreferenceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ResumeAccountSyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req       *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	AccountId string                `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *ResumeAccountSyncRequest) Reset() {
	*x = ResumeAccountSyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeAccountSyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeAccountSyncRequest) ProtoMessage() {}

func (x *ResumeAccountSyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeAccountSyncRequest.ProtoReflect.Descriptor instead.
func (*ResumeAccountSyncRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{34}
}

func (x *ResumeAccountSyncRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ResumeAccountSyncRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type ResumeAccountSyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of consent handles which need to be made active
	ConsentHandleList []string `protobuf:"bytes,2,rep,name=consent_handle_list,json=consentHandleList,proto3" json:"consent_handle_list,omitempty"`
}

func (x *ResumeAccountSyncResponse) Reset() {
	*x = ResumeAccountSyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeAccountSyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeAccountSyncResponse) ProtoMessage() {}

func (x *ResumeAccountSyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeAccountSyncResponse.ProtoReflect.Descriptor instead.
func (*ResumeAccountSyncResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{35}
}

func (x *ResumeAccountSyncResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ResumeAccountSyncResponse) GetConsentHandleList() []string {
	if x != nil {
		return x.ConsentHandleList
	}
	return nil
}

type GetDataPullStatusFromConsentHandleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// consent handle for which data pull status has to be fetched
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	ConsentHandle string `protobuf:"bytes,2,opt,name=consent_handle,json=consentHandle,proto3" json:"consent_handle,omitempty"`
	// current_poll_count represents what is the current polling count of data pull status
	// Currently, the data pull status poll and retry logic is handled at the client side, and thus to make it backend driven,
	// we have to maintain a field that stores the current count of the poll which will be checked against the max allowed attempt.
	// Thus, backend can handle the logic when maximum attempts are exhausted
	// Initially, field value will be 0, and in further polls, client will re-send the field in request received from backend.
	CurrentPollCount int32 `protobuf:"varint,3,opt,name=current_poll_count,json=currentPollCount,proto3" json:"current_poll_count,omitempty"`
	// consent handle list for which data pull status has to be fetched
	ConsentHandleList []string `protobuf:"bytes,4,rep,name=consent_handle_list,json=consentHandleList,proto3" json:"consent_handle_list,omitempty"`
	// Unique identifier of a flow to connect an external account
	ConnectionFlowId string `protobuf:"bytes,5,opt,name=connection_flow_id,json=connectionFlowId,proto3" json:"connection_flow_id,omitempty"`
}

func (x *GetDataPullStatusFromConsentHandleRequest) Reset() {
	*x = GetDataPullStatusFromConsentHandleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataPullStatusFromConsentHandleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataPullStatusFromConsentHandleRequest) ProtoMessage() {}

func (x *GetDataPullStatusFromConsentHandleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataPullStatusFromConsentHandleRequest.ProtoReflect.Descriptor instead.
func (*GetDataPullStatusFromConsentHandleRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetDataPullStatusFromConsentHandleRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *GetDataPullStatusFromConsentHandleRequest) GetConsentHandle() string {
	if x != nil {
		return x.ConsentHandle
	}
	return ""
}

func (x *GetDataPullStatusFromConsentHandleRequest) GetCurrentPollCount() int32 {
	if x != nil {
		return x.CurrentPollCount
	}
	return 0
}

func (x *GetDataPullStatusFromConsentHandleRequest) GetConsentHandleList() []string {
	if x != nil {
		return x.ConsentHandleList
	}
	return nil
}

func (x *GetDataPullStatusFromConsentHandleRequest) GetConnectionFlowId() string {
	if x != nil {
		return x.ConnectionFlowId
	}
	return ""
}

type GetDataPullStatusFromConsentHandleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// data pull status
	DataPullStatus DataPullStatus `protobuf:"varint,2,opt,name=data_pull_status,json=dataPullStatus,proto3,enum=frontend.connected_account.DataPullStatus" json:"data_pull_status,omitempty"`
	// number of seconds client needs to wait before polling this API again
	// In case backend returns -1, client can stop polling. In case backend returns 0, poll immediately
	NextPollSeconds int32 `protobuf:"varint,3,opt,name=next_poll_seconds,json=nextPollSeconds,proto3" json:"next_poll_seconds,omitempty"`
	// deeplink to land on the proper page
	Deeplink *deeplink.Deeplink `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// max number of attempts allowed for polling data pull status for consent handle. For example : If max attempts are 5 and next poll duration is 3 client will wait for 15 seconds on the screen for data pull to be moved to terminal state.
	MaxAllowedAttempts int32 `protobuf:"varint,5,opt,name=max_allowed_attempts,json=maxAllowedAttempts,proto3" json:"max_allowed_attempts,omitempty"`
	// current_poll_count represents what is the current polling count of data pull status
	// Currently, the data pull status poll and retry logic is handled at the client side, and thus to make it backend driven,
	// we have to maintain a field that stores the current count of the poll which will be checked against the max allowed attempt.Thus, backend can handle the logic when maximum attempts are exhausted
	// The current poll count will be incremented by 1, in each polling response.
	CurrentPollCount int32 `protobuf:"varint,6,opt,name=current_poll_count,json=currentPollCount,proto3" json:"current_poll_count,omitempty"`
}

func (x *GetDataPullStatusFromConsentHandleResponse) Reset() {
	*x = GetDataPullStatusFromConsentHandleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataPullStatusFromConsentHandleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataPullStatusFromConsentHandleResponse) ProtoMessage() {}

func (x *GetDataPullStatusFromConsentHandleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataPullStatusFromConsentHandleResponse.ProtoReflect.Descriptor instead.
func (*GetDataPullStatusFromConsentHandleResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetDataPullStatusFromConsentHandleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDataPullStatusFromConsentHandleResponse) GetDataPullStatus() DataPullStatus {
	if x != nil {
		return x.DataPullStatus
	}
	return DataPullStatus_DATA_PULL_STATUS_UNSPECIFIED
}

func (x *GetDataPullStatusFromConsentHandleResponse) GetNextPollSeconds() int32 {
	if x != nil {
		return x.NextPollSeconds
	}
	return 0
}

func (x *GetDataPullStatusFromConsentHandleResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *GetDataPullStatusFromConsentHandleResponse) GetMaxAllowedAttempts() int32 {
	if x != nil {
		return x.MaxAllowedAttempts
	}
	return 0
}

func (x *GetDataPullStatusFromConsentHandleResponse) GetCurrentPollCount() int32 {
	if x != nil {
		return x.CurrentPollCount
	}
	return 0
}

type GetAllowedConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// optional argument - list of fip ids to get config - if empty, all bank metadata is returned
	FipIdList []string `protobuf:"bytes,2,rep,name=fip_id_list,json=fipIdList,proto3" json:"fip_id_list,omitempty"`
	// optional argument - list of aa entities to get config - if empty, all aa metadata is returned
	AaEntityList []typesv2.AaEntity `protobuf:"varint,3,rep,packed,name=aa_entity_list,json=aaEntityList,proto3,enum=api.typesv2.AaEntity" json:"aa_entity_list,omitempty"`
	// ca_flow_name represents the name of other service which is trying to use connected account flow
	CaFlowName string `protobuf:"bytes,5,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
}

func (x *GetAllowedConfigRequest) Reset() {
	*x = GetAllowedConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllowedConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllowedConfigRequest) ProtoMessage() {}

func (x *GetAllowedConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllowedConfigRequest.ProtoReflect.Descriptor instead.
func (*GetAllowedConfigRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetAllowedConfigRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAllowedConfigRequest) GetFipIdList() []string {
	if x != nil {
		return x.FipIdList
	}
	return nil
}

func (x *GetAllowedConfigRequest) GetAaEntityList() []typesv2.AaEntity {
	if x != nil {
		return x.AaEntityList
	}
	return nil
}

func (x *GetAllowedConfigRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

type GetAllowedConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FipMetaList []*FipMeta  `protobuf:"bytes,2,rep,name=fip_meta_list,json=fipMetaList,proto3" json:"fip_meta_list,omitempty"`
	// Backend driven title text on account discovery screen
	AccountDiscoveryTitleText string `protobuf:"bytes,3,opt,name=account_discovery_title_text,json=accountDiscoveryTitleText,proto3" json:"account_discovery_title_text,omitempty"`
	// Backend driven subtitle text on account discovery screen
	AccountDiscoverySubtitleText string `protobuf:"bytes,4,opt,name=account_discovery_subtitle_text,json=accountDiscoverySubtitleText,proto3" json:"account_discovery_subtitle_text,omitempty"`
	// Timeout for finvu account discovery API calls.
	// Example : Finvu has a separate API call per FIP hence if we want to discover account for users in N FIPs
	// client initiates N parallel calls. Some calls get completed in X seconds while some take Y. We do not want
	// user to remain blocked due to some particular FIP call taking large times hence client will abort the operation
	// after this value returned by backend.
	// Deprecated in favour of AaEntityMeta.account_discovery_timeout_seconds
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	FinvuAccountDiscoveryTimeoutSeconds int32 `protobuf:"varint,5,opt,name=finvu_account_discovery_timeout_seconds,json=finvuAccountDiscoveryTimeoutSeconds,proto3" json:"finvu_account_discovery_timeout_seconds,omitempty"`
	// Timeout for onemoney account discovery API calls.
	// Example : Onemoney has a Single discovery API call per FIP hence if we want to discover account for users in N FIPs
	// client initiates N parallel calls. Some calls get completed in X seconds while some take Y. We do not want
	// user to remain blocked due to some particular FIP call taking large times hence client will abort the operation
	// after this value returned by backend.
	// Deprecated in favour of AaEntityMeta.account_discovery_timeout_seconds
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	OnemoneyAccountDiscoveryTimeoutSeconds int32 `protobuf:"varint,6,opt,name=onemoney_account_discovery_timeout_seconds,json=onemoneyAccountDiscoveryTimeoutSeconds,proto3" json:"onemoney_account_discovery_timeout_seconds,omitempty"`
	// parameters needed for Connected accounts v2 flow
	V2FlowParams *V2FlowParams `protobuf:"bytes,7,opt,name=v2_flow_params,json=v2FlowParams,proto3" json:"v2_flow_params,omitempty"`
	// flag to be used by client to decide whether to use async discovery end point of finvu or not
	// Async discovery endpoint fires discovery calls in parallel at finvu AA's end and makes sure websocket connection
	// is not choked due to some bank taking long time in discovery
	// Deprecated in favour of AaEntityMeta.async_discovery_enable
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	UseFinvuAsyncDiscovery bool `protobuf:"varint,8,opt,name=use_finvu_async_discovery,json=useFinvuAsyncDiscovery,proto3" json:"use_finvu_async_discovery,omitempty"`
	// list of metadata related to all AA entities (account aggregator) as provided in the request argument
	// if aa_entity_list is empty, returns metadata of all entities
	AaEntityMetaList []*common.AaEntityMeta `protobuf:"bytes,9,rep,name=aa_entity_meta_list,json=aaEntityMetaList,proto3" json:"aa_entity_meta_list,omitempty"`
	// NoAccountsDiscoveredTextParams will be used to show textual components on No accounts discovered screen
	NoAccountsDiscoveredTextParams *NoAccountsDiscoveredTextParams `protobuf:"bytes,10,opt,name=no_accounts_discovered_text_params,json=noAccountsDiscoveredTextParams,proto3" json:"no_accounts_discovered_text_params,omitempty"`
	// accounts_discovery_identifiers denotes the map of FIP id and the identifiers required for the account discovery
	AccountsDiscoveryIdentifiers map[string]*AccountDiscoveryIdentifiers `protobuf:"bytes,11,rep,name=accounts_discovery_identifiers,json=accountsDiscoveryIdentifiers,proto3" json:"accounts_discovery_identifiers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// SDKFUiFlowParams can be used for all the UI parameters needed for CA SDK.
	SdkFlowParams *SDKUiFlowParams `protobuf:"bytes,12,opt,name=sdk_flow_params,json=sdkFlowParams,proto3" json:"sdk_flow_params,omitempty"`
	// grouped FIP data
	GroupedFipMetaDataByTypes []*GroupedFipMetaDataByTypes `protobuf:"bytes,13,rep,name=grouped_fip_meta_data_by_types,json=groupedFipMetaDataByTypes,proto3" json:"grouped_fip_meta_data_by_types,omitempty"`
	// Auto read timeout for reading all discovered FIPs OTP's, this is only used in android for now
	AutoReadTimeoutAllDiscoveredFipsOtp int32 `protobuf:"varint,14,opt,name=auto_read_timeout_all_discovered_fips_otp,json=autoReadTimeoutAllDiscoveredFipsOtp,proto3" json:"auto_read_timeout_all_discovered_fips_otp,omitempty"`
}

func (x *GetAllowedConfigResponse) Reset() {
	*x = GetAllowedConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllowedConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllowedConfigResponse) ProtoMessage() {}

func (x *GetAllowedConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllowedConfigResponse.ProtoReflect.Descriptor instead.
func (*GetAllowedConfigResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetAllowedConfigResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetFipMetaList() []*FipMeta {
	if x != nil {
		return x.FipMetaList
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetAccountDiscoveryTitleText() string {
	if x != nil {
		return x.AccountDiscoveryTitleText
	}
	return ""
}

func (x *GetAllowedConfigResponse) GetAccountDiscoverySubtitleText() string {
	if x != nil {
		return x.AccountDiscoverySubtitleText
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *GetAllowedConfigResponse) GetFinvuAccountDiscoveryTimeoutSeconds() int32 {
	if x != nil {
		return x.FinvuAccountDiscoveryTimeoutSeconds
	}
	return 0
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *GetAllowedConfigResponse) GetOnemoneyAccountDiscoveryTimeoutSeconds() int32 {
	if x != nil {
		return x.OnemoneyAccountDiscoveryTimeoutSeconds
	}
	return 0
}

func (x *GetAllowedConfigResponse) GetV2FlowParams() *V2FlowParams {
	if x != nil {
		return x.V2FlowParams
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *GetAllowedConfigResponse) GetUseFinvuAsyncDiscovery() bool {
	if x != nil {
		return x.UseFinvuAsyncDiscovery
	}
	return false
}

func (x *GetAllowedConfigResponse) GetAaEntityMetaList() []*common.AaEntityMeta {
	if x != nil {
		return x.AaEntityMetaList
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetNoAccountsDiscoveredTextParams() *NoAccountsDiscoveredTextParams {
	if x != nil {
		return x.NoAccountsDiscoveredTextParams
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetAccountsDiscoveryIdentifiers() map[string]*AccountDiscoveryIdentifiers {
	if x != nil {
		return x.AccountsDiscoveryIdentifiers
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetSdkFlowParams() *SDKUiFlowParams {
	if x != nil {
		return x.SdkFlowParams
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetGroupedFipMetaDataByTypes() []*GroupedFipMetaDataByTypes {
	if x != nil {
		return x.GroupedFipMetaDataByTypes
	}
	return nil
}

func (x *GetAllowedConfigResponse) GetAutoReadTimeoutAllDiscoveredFipsOtp() int32 {
	if x != nil {
		return x.AutoReadTimeoutAllDiscoveredFipsOtp
	}
	return 0
}

// AccountDiscoveryIdentifiers denotes all the identifier details that are required in request parameter to initiate account discovery
type AccountDiscoveryIdentifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccDiscoveryIdentifiers []*AccountDiscoveryIdentifiers_AccDiscoveryIdentifier `protobuf:"bytes,1,rep,name=acc_discovery_identifiers,json=accDiscoveryIdentifiers,proto3" json:"acc_discovery_identifiers,omitempty"`
}

func (x *AccountDiscoveryIdentifiers) Reset() {
	*x = AccountDiscoveryIdentifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDiscoveryIdentifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDiscoveryIdentifiers) ProtoMessage() {}

func (x *AccountDiscoveryIdentifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDiscoveryIdentifiers.ProtoReflect.Descriptor instead.
func (*AccountDiscoveryIdentifiers) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{40}
}

func (x *AccountDiscoveryIdentifiers) GetAccDiscoveryIdentifiers() []*AccountDiscoveryIdentifiers_AccDiscoveryIdentifier {
	if x != nil {
		return x.AccDiscoveryIdentifiers
	}
	return nil
}

type NoAccountsDiscoveredTextParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleText           string `protobuf:"bytes,1,opt,name=title_text,json=titleText,proto3" json:"title_text,omitempty"`
	SubTitleText        string `protobuf:"bytes,2,opt,name=sub_title_text,json=subTitleText,proto3" json:"sub_title_text,omitempty"`
	FipIcon             string `protobuf:"bytes,3,opt,name=fip_icon,json=fipIcon,proto3" json:"fip_icon,omitempty"`
	FipNotDiscoveryText string `protobuf:"bytes,4,opt,name=fip_not_discovery_text,json=fipNotDiscoveryText,proto3" json:"fip_not_discovery_text,omitempty"`
	ProceedDeeplinkText string `protobuf:"bytes,5,opt,name=proceed_deeplink_text,json=proceedDeeplinkText,proto3" json:"proceed_deeplink_text,omitempty"`
}

func (x *NoAccountsDiscoveredTextParams) Reset() {
	*x = NoAccountsDiscoveredTextParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoAccountsDiscoveredTextParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoAccountsDiscoveredTextParams) ProtoMessage() {}

func (x *NoAccountsDiscoveredTextParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoAccountsDiscoveredTextParams.ProtoReflect.Descriptor instead.
func (*NoAccountsDiscoveredTextParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{41}
}

func (x *NoAccountsDiscoveredTextParams) GetTitleText() string {
	if x != nil {
		return x.TitleText
	}
	return ""
}

func (x *NoAccountsDiscoveredTextParams) GetSubTitleText() string {
	if x != nil {
		return x.SubTitleText
	}
	return ""
}

func (x *NoAccountsDiscoveredTextParams) GetFipIcon() string {
	if x != nil {
		return x.FipIcon
	}
	return ""
}

func (x *NoAccountsDiscoveredTextParams) GetFipNotDiscoveryText() string {
	if x != nil {
		return x.FipNotDiscoveryText
	}
	return ""
}

func (x *NoAccountsDiscoveredTextParams) GetProceedDeeplinkText() string {
	if x != nil {
		return x.ProceedDeeplinkText
	}
	return ""
}

// V2FlowParams will contain all the parameters(backend driven) for the V2 flow
// going forward we may deprecate v1 entirely
type V2FlowParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Backend driven title text on account discovery screen in V2 flow
	AccountDiscoveryTitleText string `protobuf:"bytes,1,opt,name=account_discovery_title_text,json=accountDiscoveryTitleText,proto3" json:"account_discovery_title_text,omitempty"`
	// Backend driven subtitle text on account discovery screen in V2 flow
	AccountDiscoverySubtitleText string `protobuf:"bytes,2,opt,name=account_discovery_subtitle_text,json=accountDiscoverySubtitleText,proto3" json:"account_discovery_subtitle_text,omitempty"`
	// text to show on CTA
	CtaText string `protobuf:"bytes,3,opt,name=cta_text,json=ctaText,proto3" json:"cta_text,omitempty"`
	// subtitle text to show when account discovery is still happening
	AccountDiscoverySubtitleSearchingText string `protobuf:"bytes,4,opt,name=account_discovery_subtitle_searching_text,json=accountDiscoverySubtitleSearchingText,proto3" json:"account_discovery_subtitle_searching_text,omitempty"`
	// Text to show for discovery loading.
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=14233-46818&mode=dev
	AccountDiscoveryLoadingText string `protobuf:"bytes,7,opt,name=account_discovery_loading_text,json=accountDiscoveryLoadingText,proto3" json:"account_discovery_loading_text,omitempty"`
	// Text to be shown on the bottom of discovery page when user not able to find his bank/demat account
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=14179-18175&mode=dev
	CantSeeYourAccountsText string `protobuf:"bytes,8,opt,name=cant_see_your_accounts_text,json=cantSeeYourAccountsText,proto3" json:"cant_see_your_accounts_text,omitempty"`
}

func (x *V2FlowParams) Reset() {
	*x = V2FlowParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *V2FlowParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V2FlowParams) ProtoMessage() {}

func (x *V2FlowParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V2FlowParams.ProtoReflect.Descriptor instead.
func (*V2FlowParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{42}
}

func (x *V2FlowParams) GetAccountDiscoveryTitleText() string {
	if x != nil {
		return x.AccountDiscoveryTitleText
	}
	return ""
}

func (x *V2FlowParams) GetAccountDiscoverySubtitleText() string {
	if x != nil {
		return x.AccountDiscoverySubtitleText
	}
	return ""
}

func (x *V2FlowParams) GetCtaText() string {
	if x != nil {
		return x.CtaText
	}
	return ""
}

func (x *V2FlowParams) GetAccountDiscoverySubtitleSearchingText() string {
	if x != nil {
		return x.AccountDiscoverySubtitleSearchingText
	}
	return ""
}

func (x *V2FlowParams) GetAccountDiscoveryLoadingText() string {
	if x != nil {
		return x.AccountDiscoveryLoadingText
	}
	return ""
}

func (x *V2FlowParams) GetCantSeeYourAccountsText() string {
	if x != nil {
		return x.CantSeeYourAccountsText
	}
	return ""
}

type SDKUiFlowParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// text for showing flow header
	FlowHeader string `protobuf:"bytes,1,opt,name=flow_header,json=flowHeader,proto3" json:"flow_header,omitempty"`
	// tells the completion status in percentage
	ProgressBarStatus int32 `protobuf:"varint,2,opt,name=progress_bar_status,json=progressBarStatus,proto3" json:"progress_bar_status,omitempty"`
	// bool to decide displaying progress bar
	DisplayProgressBar bool `protobuf:"varint,3,opt,name=display_progress_bar,json=displayProgressBar,proto3" json:"display_progress_bar,omitempty"`
	// text to be displayed during account discovery loading
	AccountDiscoverySearchingText string `protobuf:"bytes,4,opt,name=account_discovery_searching_text,json=accountDiscoverySearchingText,proto3" json:"account_discovery_searching_text,omitempty"`
	// text to show on cta to connect discovered accounts, Looks good, Connect
	ConnectDiscoveredAccountsCtaText string `protobuf:"bytes,5,opt,name=connect_discovered_accounts_cta_text,json=connectDiscoveredAccountsCtaText,proto3" json:"connect_discovered_accounts_cta_text,omitempty"`
	// cta text for retrying accounts failed to generate otp
	RetryCtaText string `protobuf:"bytes,6,opt,name=retry_cta_text,json=retryCtaText,proto3" json:"retry_cta_text,omitempty"`
	// cta text to ignore accounts failed to generate otp
	SkipFailedAccountsCtaText string `protobuf:"bytes,7,opt,name=skip_failed_accounts_cta_text,json=skipFailedAccountsCtaText,proto3" json:"skip_failed_accounts_cta_text,omitempty"`
	// text to show the title when no accounts discovered
	NoAccountsFoundTitle string `protobuf:"bytes,8,opt,name=no_accounts_found_title,json=noAccountsFoundTitle,proto3" json:"no_accounts_found_title,omitempty"`
	// text to show the sub title when no accounts discovered
	NoAccountsFoundSubTitle string `protobuf:"bytes,9,opt,name=no_accounts_found_sub_title,json=noAccountsFoundSubTitle,proto3" json:"no_accounts_found_sub_title,omitempty"`
	// text to show on CTA when no accounts found
	NoAccountFoundCtaText string `protobuf:"bytes,10,opt,name=no_account_found_cta_text,json=noAccountFoundCtaText,proto3" json:"no_account_found_cta_text,omitempty"`
	// icon url for no accounts found
	NoAccountsFoundIconUrl string `protobuf:"bytes,11,opt,name=no_accounts_found_icon_url,json=noAccountsFoundIconUrl,proto3" json:"no_accounts_found_icon_url,omitempty"`
	// message for showing disconnect account
	DisconnectAccountText string `protobuf:"bytes,12,opt,name=disconnect_account_text,json=disconnectAccountText,proto3" json:"disconnect_account_text,omitempty"`
}

func (x *SDKUiFlowParams) Reset() {
	*x = SDKUiFlowParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKUiFlowParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKUiFlowParams) ProtoMessage() {}

func (x *SDKUiFlowParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKUiFlowParams.ProtoReflect.Descriptor instead.
func (*SDKUiFlowParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{43}
}

func (x *SDKUiFlowParams) GetFlowHeader() string {
	if x != nil {
		return x.FlowHeader
	}
	return ""
}

func (x *SDKUiFlowParams) GetProgressBarStatus() int32 {
	if x != nil {
		return x.ProgressBarStatus
	}
	return 0
}

func (x *SDKUiFlowParams) GetDisplayProgressBar() bool {
	if x != nil {
		return x.DisplayProgressBar
	}
	return false
}

func (x *SDKUiFlowParams) GetAccountDiscoverySearchingText() string {
	if x != nil {
		return x.AccountDiscoverySearchingText
	}
	return ""
}

func (x *SDKUiFlowParams) GetConnectDiscoveredAccountsCtaText() string {
	if x != nil {
		return x.ConnectDiscoveredAccountsCtaText
	}
	return ""
}

func (x *SDKUiFlowParams) GetRetryCtaText() string {
	if x != nil {
		return x.RetryCtaText
	}
	return ""
}

func (x *SDKUiFlowParams) GetSkipFailedAccountsCtaText() string {
	if x != nil {
		return x.SkipFailedAccountsCtaText
	}
	return ""
}

func (x *SDKUiFlowParams) GetNoAccountsFoundTitle() string {
	if x != nil {
		return x.NoAccountsFoundTitle
	}
	return ""
}

func (x *SDKUiFlowParams) GetNoAccountsFoundSubTitle() string {
	if x != nil {
		return x.NoAccountsFoundSubTitle
	}
	return ""
}

func (x *SDKUiFlowParams) GetNoAccountFoundCtaText() string {
	if x != nil {
		return x.NoAccountFoundCtaText
	}
	return ""
}

func (x *SDKUiFlowParams) GetNoAccountsFoundIconUrl() string {
	if x != nil {
		return x.NoAccountsFoundIconUrl
	}
	return ""
}

func (x *SDKUiFlowParams) GetDisconnectAccountText() string {
	if x != nil {
		return x.DisconnectAccountText
	}
	return ""
}

type GroupedFipMetaDataByTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// text to diaplay for account type groups
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	// tells the FIP belongs to which entity type e.g., bank, demat, NPS, insurance etc
	EntityType string `protobuf:"bytes,2,opt,name=entity_type,json=entityType,proto3" json:"entity_type,omitempty"`
	// contains the Fip meta of set of Fi's
	FipMeta []*FipMeta `protobuf:"bytes,3,rep,name=fip_meta,json=fipMeta,proto3" json:"fip_meta,omitempty"`
}

func (x *GroupedFipMetaDataByTypes) Reset() {
	*x = GroupedFipMetaDataByTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupedFipMetaDataByTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupedFipMetaDataByTypes) ProtoMessage() {}

func (x *GroupedFipMetaDataByTypes) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupedFipMetaDataByTypes.ProtoReflect.Descriptor instead.
func (*GroupedFipMetaDataByTypes) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{44}
}

func (x *GroupedFipMetaDataByTypes) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *GroupedFipMetaDataByTypes) GetEntityType() string {
	if x != nil {
		return x.EntityType
	}
	return ""
}

func (x *GroupedFipMetaDataByTypes) GetFipMeta() []*FipMeta {
	if x != nil {
		return x.FipMeta
	}
	return nil
}

type GetHomeSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetHomeSummaryRequest) Reset() {
	*x = GetHomeSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHomeSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHomeSummaryRequest) ProtoMessage() {}

func (x *GetHomeSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHomeSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetHomeSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetHomeSummaryRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetHomeSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// collective balance to show to user
	CollectiveBalance *BalanceMeta `protobuf:"bytes,2,opt,name=collective_balance,json=collectiveBalance,proto3" json:"collective_balance,omitempty"`
	// List of tiles to be shown in home summary in order
	HomeAccountTileList []*HomeAccountTile `protobuf:"bytes,3,rep,name=home_account_tile_list,json=homeAccountTileList,proto3" json:"home_account_tile_list,omitempty"`
	// deprecated in favour of ConsentRenewalPopup
	// Popup for renewal
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&t=eCpIS0IiiCrWdshM-1
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	Popup *BalanceDashboardRenewalPopupOptions `protobuf:"bytes,4,opt,name=popup,proto3" json:"popup,omitempty"`
	// renewal below balance
	RenewalCta *ui.IconTextComponent `protobuf:"bytes,5,opt,name=renewal_cta,json=renewalCta,proto3" json:"renewal_cta,omitempty"`
	// Popup for renewal
	// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&t=eCpIS0IiiCrWdshM-1
	ConsentRenewalPopup *features.AaConsentRenewalPopupOptions `protobuf:"bytes,6,opt,name=consent_renewal_popup,json=consentRenewalPopup,proto3" json:"consent_renewal_popup,omitempty"`
}

func (x *GetHomeSummaryResponse) Reset() {
	*x = GetHomeSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHomeSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHomeSummaryResponse) ProtoMessage() {}

func (x *GetHomeSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHomeSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetHomeSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{46}
}

func (x *GetHomeSummaryResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetHomeSummaryResponse) GetCollectiveBalance() *BalanceMeta {
	if x != nil {
		return x.CollectiveBalance
	}
	return nil
}

func (x *GetHomeSummaryResponse) GetHomeAccountTileList() []*HomeAccountTile {
	if x != nil {
		return x.HomeAccountTileList
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *GetHomeSummaryResponse) GetPopup() *BalanceDashboardRenewalPopupOptions {
	if x != nil {
		return x.Popup
	}
	return nil
}

func (x *GetHomeSummaryResponse) GetRenewalCta() *ui.IconTextComponent {
	if x != nil {
		return x.RenewalCta
	}
	return nil
}

func (x *GetHomeSummaryResponse) GetConsentRenewalPopup() *features.AaConsentRenewalPopupOptions {
	if x != nil {
		return x.ConsentRenewalPopup
	}
	return nil
}

type GetLandingPageOnConnectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// ca_flow_name represents the name of other service which is trying to use connected account flow
	CaFlowName string `protobuf:"bytes,5,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// A unique identifier of the flow started to connect accounts
	CaFlowId string `protobuf:"bytes,6,opt,name=ca_flow_id,json=caFlowId,proto3" json:"ca_flow_id,omitempty"`
}

func (x *GetLandingPageOnConnectRequest) Reset() {
	*x = GetLandingPageOnConnectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingPageOnConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingPageOnConnectRequest) ProtoMessage() {}

func (x *GetLandingPageOnConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingPageOnConnectRequest.ProtoReflect.Descriptor instead.
func (*GetLandingPageOnConnectRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{47}
}

func (x *GetLandingPageOnConnectRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetLandingPageOnConnectRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *GetLandingPageOnConnectRequest) GetCaFlowId() string {
	if x != nil {
		return x.CaFlowId
	}
	return ""
}

type GetLandingPageOnConnectResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Deeplink to tell client which screen to load
	// Deprecated in favour of new deeplink added which supports all the flows like reoobe etc.
	// Backend will keep on populating this only with wealth onb dl or benefits dl for backward compatibility
	//
	// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// New deeplink to be used going forward by client to understand where to land on connect action
	// Few examples :
	// In case user has not completed wealth onboarding return deeplink to complete that
	// In case user has completed wealth onboarding return deeplink for benefits screen with proceed CTA
	// Proceed CTA on benefits can land user directly on SDK in case user passes re oobe check in case they do not
	// return deeplink to handle re oobe first and ask user to complete that action and than open SDK
	NewDeeplink *deeplink.Deeplink `protobuf:"bytes,3,opt,name=new_deeplink,json=newDeeplink,proto3" json:"new_deeplink,omitempty"`
}

func (x *GetLandingPageOnConnectResponse) Reset() {
	*x = GetLandingPageOnConnectResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingPageOnConnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingPageOnConnectResponse) ProtoMessage() {}

func (x *GetLandingPageOnConnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingPageOnConnectResponse.ProtoReflect.Descriptor instead.
func (*GetLandingPageOnConnectResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetLandingPageOnConnectResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
func (x *GetLandingPageOnConnectResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *GetLandingPageOnConnectResponse) GetNewDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.NewDeeplink
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
type ConnectAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// entity to check aa heartbeat status for
	AaEntity AaEntity `protobuf:"varint,2,opt,name=aa_entity,json=aaEntity,proto3,enum=frontend.connected_account.AaEntity" json:"aa_entity,omitempty"`
}

func (x *ConnectAccountRequest) Reset() {
	*x = ConnectAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectAccountRequest) ProtoMessage() {}

func (x *ConnectAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectAccountRequest.ProtoReflect.Descriptor instead.
func (*ConnectAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{49}
}

func (x *ConnectAccountRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ConnectAccountRequest) GetAaEntity() AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return AaEntity_AA_ENTITY_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
type ConnectAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Deeplink to tell client which screen to load
	// Few examples :
	// In case user has not completed wealth onboarding return deeplink to complete that
	// In case user has completed wealth onboarding return deeplink for benefits screen
	// return deeplink to handle re oobe first and ask user to complete that action and than open SDK
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *ConnectAccountResponse) Reset() {
	*x = ConnectAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectAccountResponse) ProtoMessage() {}

func (x *ConnectAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectAccountResponse.ProtoReflect.Descriptor instead.
func (*ConnectAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{50}
}

func (x *ConnectAccountResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *ConnectAccountResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type GetConnectedAccountEntryPointsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetConnectedAccountEntryPointsRequest) Reset() {
	*x = GetConnectedAccountEntryPointsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectedAccountEntryPointsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectedAccountEntryPointsRequest) ProtoMessage() {}

func (x *GetConnectedAccountEntryPointsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectedAccountEntryPointsRequest.ProtoReflect.Descriptor instead.
func (*GetConnectedAccountEntryPointsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetConnectedAccountEntryPointsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetConnectedAccountEntryPointsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// map of EntryPointType(string) as key to value defined via EntryPointOptions
	// For eg:
	//
	//	{
	//	   "HOME" : {"enabled":true,"text":"text1"}
	//	   "PROFILE" : {"enabled":true,"text":"text2"}
	//	}
	//
	// All keys in this map will be one of Enum values in EntryPointType always and client should do a enum.String() to access map values
	EntryPointMap map[string]*EntryPointOptions `protobuf:"bytes,2,rep,name=entry_point_map,json=entryPointMap,proto3" json:"entry_point_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Global connected account flag which denotes whether connected account is enabled/disabled for all users
	IsConnectedAccountEnabled common1.BooleanEnum `protobuf:"varint,3,opt,name=is_connected_account_enabled,json=isConnectedAccountEnabled,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_connected_account_enabled,omitempty"`
}

func (x *GetConnectedAccountEntryPointsResponse) Reset() {
	*x = GetConnectedAccountEntryPointsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectedAccountEntryPointsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectedAccountEntryPointsResponse) ProtoMessage() {}

func (x *GetConnectedAccountEntryPointsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectedAccountEntryPointsResponse.ProtoReflect.Descriptor instead.
func (*GetConnectedAccountEntryPointsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{52}
}

func (x *GetConnectedAccountEntryPointsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetConnectedAccountEntryPointsResponse) GetEntryPointMap() map[string]*EntryPointOptions {
	if x != nil {
		return x.EntryPointMap
	}
	return nil
}

func (x *GetConnectedAccountEntryPointsResponse) GetIsConnectedAccountEnabled() common1.BooleanEnum {
	if x != nil {
		return x.IsConnectedAccountEnabled
	}
	return common1.BooleanEnum(0)
}

type GetAuthTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// aa entity to be passed in the request
	// if auth token is not supported for the entity, invalid status is returned
	AaEntity AaEntity `protobuf:"varint,2,opt,name=aa_entity,json=aaEntity,proto3,enum=frontend.connected_account.AaEntity" json:"aa_entity,omitempty"`
}

func (x *GetAuthTokenRequest) Reset() {
	*x = GetAuthTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthTokenRequest) ProtoMessage() {}

func (x *GetAuthTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthTokenRequest.ProtoReflect.Descriptor instead.
func (*GetAuthTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{53}
}

func (x *GetAuthTokenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAuthTokenRequest) GetAaEntity() AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return AaEntity_AA_ENTITY_UNSPECIFIED
}

type GetAuthTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// auth token
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// expiry duration of token in minutes with delta
	ExpiryDurationMinutes int32 `protobuf:"varint,3,opt,name=expiry_duration_minutes,json=expiryDurationMinutes,proto3" json:"expiry_duration_minutes,omitempty"`
}

func (x *GetAuthTokenResponse) Reset() {
	*x = GetAuthTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthTokenResponse) ProtoMessage() {}

func (x *GetAuthTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthTokenResponse.ProtoReflect.Descriptor instead.
func (*GetAuthTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetAuthTokenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetAuthTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetAuthTokenResponse) GetExpiryDurationMinutes() int32 {
	if x != nil {
		return x.ExpiryDurationMinutes
	}
	return 0
}

type GetConnectedAccountsSummaryForHomeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Request param to control which Dashboard UI version is shown on clients. This param will be not set for old clients
	// and may/may not be set for new clients based on experimentation. See DashboardVersion docs for default handling
	DashboardVersion home.DashboardVersion `protobuf:"varint,2,opt,name=dashboard_version,json=dashboardVersion,proto3,enum=frontend.home.DashboardVersion" json:"dashboard_version,omitempty"`
	// Optional field to control zero state card variant to be shown for DASHBOARD_VERSION_V2.
	// This may only be set if dashboard_version is set to DASHBOARD_VERSION_V2
	// Even if not set, the base variant of zero state dashboard cards would be shown
	ZeroStateDashboardVariant home.ZeroStateDashboardCardVariant `protobuf:"varint,3,opt,name=zero_state_dashboard_variant,json=zeroStateDashboardVariant,proto3,enum=frontend.home.ZeroStateDashboardCardVariant" json:"zero_state_dashboard_variant,omitempty"`
}

func (x *GetConnectedAccountsSummaryForHomeRequest) Reset() {
	*x = GetConnectedAccountsSummaryForHomeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectedAccountsSummaryForHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectedAccountsSummaryForHomeRequest) ProtoMessage() {}

func (x *GetConnectedAccountsSummaryForHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectedAccountsSummaryForHomeRequest.ProtoReflect.Descriptor instead.
func (*GetConnectedAccountsSummaryForHomeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{55}
}

func (x *GetConnectedAccountsSummaryForHomeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetConnectedAccountsSummaryForHomeRequest) GetDashboardVersion() home.DashboardVersion {
	if x != nil {
		return x.DashboardVersion
	}
	return home.DashboardVersion(0)
}

func (x *GetConnectedAccountsSummaryForHomeRequest) GetZeroStateDashboardVariant() home.ZeroStateDashboardCardVariant {
	if x != nil {
		return x.ZeroStateDashboardVariant
	}
	return home.ZeroStateDashboardCardVariant(0)
}

type GetConnectedAccountsSummaryForHomeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// dashboard info in new home summary format
	DashboardInfo *home.HomeDashboard `protobuf:"bytes,2,opt,name=dashboard_info,json=dashboardInfo,proto3" json:"dashboard_info,omitempty"`
}

func (x *GetConnectedAccountsSummaryForHomeResponse) Reset() {
	*x = GetConnectedAccountsSummaryForHomeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectedAccountsSummaryForHomeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectedAccountsSummaryForHomeResponse) ProtoMessage() {}

func (x *GetConnectedAccountsSummaryForHomeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectedAccountsSummaryForHomeResponse.ProtoReflect.Descriptor instead.
func (*GetConnectedAccountsSummaryForHomeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetConnectedAccountsSummaryForHomeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetConnectedAccountsSummaryForHomeResponse) GetDashboardInfo() *home.HomeDashboard {
	if x != nil {
		return x.DashboardInfo
	}
	return nil
}

type GetSdkExitDeeplinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// ExitOperation represents the last performed operation before user exited the SDK
	ExitOperation ExitOperation `protobuf:"varint,2,opt,name=exit_operation,json=exitOperation,proto3,enum=frontend.connected_account.ExitOperation" json:"exit_operation,omitempty"`
	// ExitReason signifies the reason for exit from SDK
	ExitReason ExitReason `protobuf:"varint,3,opt,name=exit_reason,json=exitReason,proto3,enum=frontend.connected_account.ExitReason" json:"exit_reason,omitempty"`
	// ca_flow_name represents identifier of other service which is trying to use connected account flow
	// The value for this string has to be passed as is which is given with deeplink at the time of initializing SDK
	// In case string does not match with respective service identifier, then by default connected account service flow will be followed
	CaFlowName string `protobuf:"bytes,5,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// AaEntity represents which AA was used for starting the connected account flow,
	// it will be used in cases to show AA specific error screen or bottom sheet after exiting SDK
	AaEntity typesv2.AaEntity `protobuf:"varint,6,opt,name=aa_entity,json=aaEntity,proto3,enum=api.typesv2.AaEntity" json:"aa_entity,omitempty"`
	// Unique identifier of a flow to connect an external account
	ConnectionFlowId string `protobuf:"bytes,7,opt,name=connection_flow_id,json=connectionFlowId,proto3" json:"connection_flow_id,omitempty"`
}

func (x *GetSdkExitDeeplinkRequest) Reset() {
	*x = GetSdkExitDeeplinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSdkExitDeeplinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSdkExitDeeplinkRequest) ProtoMessage() {}

func (x *GetSdkExitDeeplinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSdkExitDeeplinkRequest.ProtoReflect.Descriptor instead.
func (*GetSdkExitDeeplinkRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{57}
}

func (x *GetSdkExitDeeplinkRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSdkExitDeeplinkRequest) GetExitOperation() ExitOperation {
	if x != nil {
		return x.ExitOperation
	}
	return ExitOperation_EXIT_OPERATION_UNSPECIFIED
}

func (x *GetSdkExitDeeplinkRequest) GetExitReason() ExitReason {
	if x != nil {
		return x.ExitReason
	}
	return ExitReason_EXIT_REASON_UNSPECIFIED
}

func (x *GetSdkExitDeeplinkRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *GetSdkExitDeeplinkRequest) GetAaEntity() typesv2.AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return typesv2.AaEntity(0)
}

func (x *GetSdkExitDeeplinkRequest) GetConnectionFlowId() string {
	if x != nil {
		return x.ConnectionFlowId
	}
	return ""
}

type GetSdkExitDeeplinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink to land on screen on exit from SDK
	ExitScreenDeeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=exit_screen_deeplink,json=exitScreenDeeplink,proto3" json:"exit_screen_deeplink,omitempty"`
}

func (x *GetSdkExitDeeplinkResponse) Reset() {
	*x = GetSdkExitDeeplinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSdkExitDeeplinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSdkExitDeeplinkResponse) ProtoMessage() {}

func (x *GetSdkExitDeeplinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSdkExitDeeplinkResponse.ProtoReflect.Descriptor instead.
func (*GetSdkExitDeeplinkResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{58}
}

func (x *GetSdkExitDeeplinkResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSdkExitDeeplinkResponse) GetExitScreenDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.ExitScreenDeeplink
	}
	return nil
}

// deprecated in favour of features.AAConsentRenewalPopupOptions
// pop-up screen to show when user lands on balance dashboard page and they are eligible for renewal
// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&t=1nHxu44cjRq40YI6-1
//
// Deprecated: Marked as deprecated in api/frontend/connected_account/service.proto.
type BalanceDashboardRenewalPopupOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShowPopup     bool            `protobuf:"varint,1,opt,name=show_popup,json=showPopup,proto3" json:"show_popup,omitempty"`
	HeaderIconUrl *common1.Image  `protobuf:"bytes,2,opt,name=header_icon_url,json=headerIconUrl,proto3" json:"header_icon_url,omitempty"`
	Title         *common1.Text   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle      *common1.Text   `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	Body          *common1.Text   `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
	CtaList       []*deeplink.Cta `protobuf:"bytes,6,rep,name=cta_list,json=ctaList,proto3" json:"cta_list,omitempty"`
	// background color for popup
	BgColor *widget.BackgroundColour `protobuf:"bytes,7,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// time after which popup would be shown to user after dismissing or remind me later
	DismissPopupDuration *durationpb.Duration `protobuf:"bytes,8,opt,name=dismiss_popup_duration,json=dismissPopupDuration,proto3" json:"dismiss_popup_duration,omitempty"`
}

func (x *BalanceDashboardRenewalPopupOptions) Reset() {
	*x = BalanceDashboardRenewalPopupOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceDashboardRenewalPopupOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceDashboardRenewalPopupOptions) ProtoMessage() {}

func (x *BalanceDashboardRenewalPopupOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceDashboardRenewalPopupOptions.ProtoReflect.Descriptor instead.
func (*BalanceDashboardRenewalPopupOptions) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{59}
}

func (x *BalanceDashboardRenewalPopupOptions) GetShowPopup() bool {
	if x != nil {
		return x.ShowPopup
	}
	return false
}

func (x *BalanceDashboardRenewalPopupOptions) GetHeaderIconUrl() *common1.Image {
	if x != nil {
		return x.HeaderIconUrl
	}
	return nil
}

func (x *BalanceDashboardRenewalPopupOptions) GetTitle() *common1.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BalanceDashboardRenewalPopupOptions) GetSubtitle() *common1.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *BalanceDashboardRenewalPopupOptions) GetBody() *common1.Text {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *BalanceDashboardRenewalPopupOptions) GetCtaList() []*deeplink.Cta {
	if x != nil {
		return x.CtaList
	}
	return nil
}

func (x *BalanceDashboardRenewalPopupOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *BalanceDashboardRenewalPopupOptions) GetDismissPopupDuration() *durationpb.Duration {
	if x != nil {
		return x.DismissPopupDuration
	}
	return nil
}

type GetLandingPageForRenewalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// optional argument - list of fip ids
	FipIdList []string `protobuf:"bytes,2,rep,name=fip_id_list,json=fipIdList,proto3" json:"fip_id_list,omitempty"`
}

func (x *GetLandingPageForRenewalRequest) Reset() {
	*x = GetLandingPageForRenewalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingPageForRenewalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingPageForRenewalRequest) ProtoMessage() {}

func (x *GetLandingPageForRenewalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingPageForRenewalRequest.ProtoReflect.Descriptor instead.
func (*GetLandingPageForRenewalRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{60}
}

func (x *GetLandingPageForRenewalRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetLandingPageForRenewalRequest) GetFipIdList() []string {
	if x != nil {
		return x.FipIdList
	}
	return nil
}

type GetLandingPageForRenewalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink to initiate consent renewal flow
	RenewalDeeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=renewal_deeplink,json=renewalDeeplink,proto3" json:"renewal_deeplink,omitempty"`
}

func (x *GetLandingPageForRenewalResponse) Reset() {
	*x = GetLandingPageForRenewalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLandingPageForRenewalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLandingPageForRenewalResponse) ProtoMessage() {}

func (x *GetLandingPageForRenewalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLandingPageForRenewalResponse.ProtoReflect.Descriptor instead.
func (*GetLandingPageForRenewalResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{61}
}

func (x *GetLandingPageForRenewalResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetLandingPageForRenewalResponse) GetRenewalDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.RenewalDeeplink
	}
	return nil
}

type GetConsentHandleStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// consent handle for which consent handle status has to be checked
	ConsentHandle string `protobuf:"bytes,2,opt,name=consent_handle,json=consentHandle,proto3" json:"consent_handle,omitempty"`
	// current_poll_count represents what is the current polling count for checking consent handle status
	// Initially, field value will be 0, the value will be increased by 1 in further poll counts.
	// This poll count max value can reach a max allowed poll count.
	// if consent handle status remained pending and polling max value is breached then failure consent handle status deeplink,
	// else if consent handle status value is failed or success corresponding deeplink is returned according to designs
	// ref: https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-118757&t=DKswshjsJq3CwCv3-0
	CurrentPollCount int32 `protobuf:"varint,3,opt,name=current_poll_count,json=currentPollCount,proto3" json:"current_poll_count,omitempty"`
	// Unique identifier of a flow to connect an external account
	ConnectionFlowId string `protobuf:"bytes,4,opt,name=connection_flow_id,json=connectionFlowId,proto3" json:"connection_flow_id,omitempty"`
}

func (x *GetConsentHandleStatusRequest) Reset() {
	*x = GetConsentHandleStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConsentHandleStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConsentHandleStatusRequest) ProtoMessage() {}

func (x *GetConsentHandleStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConsentHandleStatusRequest.ProtoReflect.Descriptor instead.
func (*GetConsentHandleStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{62}
}

func (x *GetConsentHandleStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetConsentHandleStatusRequest) GetConsentHandle() string {
	if x != nil {
		return x.ConsentHandle
	}
	return ""
}

func (x *GetConsentHandleStatusRequest) GetCurrentPollCount() int32 {
	if x != nil {
		return x.CurrentPollCount
	}
	return 0
}

func (x *GetConsentHandleStatusRequest) GetConnectionFlowId() string {
	if x != nil {
		return x.ConnectionFlowId
	}
	return ""
}

type GetConsentHandleStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// consent handle status
	ConsentHandleStatus common.ConsentHandleStatus `protobuf:"varint,2,opt,name=consent_handle_status,json=consentHandleStatus,proto3,enum=frontend.connected_account.common.ConsentHandleStatus" json:"consent_handle_status,omitempty"`
	// the delay duration in two consecutive poll
	// initially polling value will be returned as 0, and if value returned is -1 stop polling
	NextPollDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=next_poll_duration,json=nextPollDuration,proto3" json:"next_poll_duration,omitempty"`
	// deeplink to land on the appropriate page according to failure/success
	Deeplink *deeplink.Deeplink `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// current_poll_count represents the current polling count of consent handle status
	CurrentPollCount int32 `protobuf:"varint,5,opt,name=current_poll_count,json=currentPollCount,proto3" json:"current_poll_count,omitempty"`
}

func (x *GetConsentHandleStatusResponse) Reset() {
	*x = GetConsentHandleStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConsentHandleStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConsentHandleStatusResponse) ProtoMessage() {}

func (x *GetConsentHandleStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConsentHandleStatusResponse.ProtoReflect.Descriptor instead.
func (*GetConsentHandleStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{63}
}

func (x *GetConsentHandleStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetConsentHandleStatusResponse) GetConsentHandleStatus() common.ConsentHandleStatus {
	if x != nil {
		return x.ConsentHandleStatus
	}
	return common.ConsentHandleStatus(0)
}

func (x *GetConsentHandleStatusResponse) GetNextPollDuration() *durationpb.Duration {
	if x != nil {
		return x.NextPollDuration
	}
	return nil
}

func (x *GetConsentHandleStatusResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *GetConsentHandleStatusResponse) GetCurrentPollCount() int32 {
	if x != nil {
		return x.CurrentPollCount
	}
	return 0
}

type GetAllDepositAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetAllDepositAccountsRequest) Reset() {
	*x = GetAllDepositAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllDepositAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDepositAccountsRequest) ProtoMessage() {}

func (x *GetAllDepositAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDepositAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetAllDepositAccountsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetAllDepositAccountsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetAllDepositAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deposit_account_details consist of details of all the accounts of all deposit type i.e. Fixed Deposit(FI created, AA fetched, manually added)
	// Smart Deposit(Fi created) and Recurring Deposit(AA fetched and manually added)
	DepositAccountsSections []*DepositAccountsSection `protobuf:"bytes,2,rep,name=deposit_accounts_sections,json=depositAccountsSections,proto3" json:"deposit_accounts_sections,omitempty"`
	// total_balance_text represents the title of the balance to be shown
	TotalBalanceText *common1.Text `protobuf:"bytes,3,opt,name=total_balance_text,json=totalBalanceText,proto3" json:"total_balance_text,omitempty"`
	// total_balance represents the collective balance across all the deposits
	TotalBalance *BalanceMeta `protobuf:"bytes,4,opt,name=total_balance,json=totalBalance,proto3" json:"total_balance,omitempty"`
	// CTA to connect further deposit accounts
	ConnectDepositAccCta *deeplink.Cta `protobuf:"bytes,5,opt,name=connect_deposit_acc_cta,json=connectDepositAccCta,proto3" json:"connect_deposit_acc_cta,omitempty"`
	// bottom_banner represents banner to track net worth
	BottomBanner *ui.IconTextComponent `protobuf:"bytes,6,opt,name=bottom_banner,json=bottomBanner,proto3" json:"bottom_banner,omitempty"`
}

func (x *GetAllDepositAccountsResponse) Reset() {
	*x = GetAllDepositAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllDepositAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDepositAccountsResponse) ProtoMessage() {}

func (x *GetAllDepositAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDepositAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetAllDepositAccountsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetAllDepositAccountsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetAllDepositAccountsResponse) GetDepositAccountsSections() []*DepositAccountsSection {
	if x != nil {
		return x.DepositAccountsSections
	}
	return nil
}

func (x *GetAllDepositAccountsResponse) GetTotalBalanceText() *common1.Text {
	if x != nil {
		return x.TotalBalanceText
	}
	return nil
}

func (x *GetAllDepositAccountsResponse) GetTotalBalance() *BalanceMeta {
	if x != nil {
		return x.TotalBalance
	}
	return nil
}

func (x *GetAllDepositAccountsResponse) GetConnectDepositAccCta() *deeplink.Cta {
	if x != nil {
		return x.ConnectDepositAccCta
	}
	return nil
}

func (x *GetAllDepositAccountsResponse) GetBottomBanner() *ui.IconTextComponent {
	if x != nil {
		return x.BottomBanner
	}
	return nil
}

type DepositAccountsSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title *common1.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// type of the DepositAccount (can be Smart Deposit, Recurring Deposit, Fixed Deposit), can be more in future
	Type accounts.Type `protobuf:"varint,2,opt,name=type,proto3,enum=accounts.Type" json:"type,omitempty"`
	// List of tiles to be shown in home summary in order
	HomeAccountTileList []*HomeAccountTile `protobuf:"bytes,3,rep,name=home_account_tile_list,json=homeAccountTileList,proto3" json:"home_account_tile_list,omitempty"`
	// info_cta used to deposit information per deposit type
	InfoCta *ui.IconTextComponent `protobuf:"bytes,4,opt,name=info_cta,json=infoCta,proto3" json:"info_cta,omitempty"`
}

func (x *DepositAccountsSection) Reset() {
	*x = DepositAccountsSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositAccountsSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositAccountsSection) ProtoMessage() {}

func (x *DepositAccountsSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositAccountsSection.ProtoReflect.Descriptor instead.
func (*DepositAccountsSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{66}
}

func (x *DepositAccountsSection) GetTitle() *common1.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *DepositAccountsSection) GetType() accounts.Type {
	if x != nil {
		return x.Type
	}
	return accounts.Type(0)
}

func (x *DepositAccountsSection) GetHomeAccountTileList() []*HomeAccountTile {
	if x != nil {
		return x.HomeAccountTileList
	}
	return nil
}

func (x *DepositAccountsSection) GetInfoCta() *ui.IconTextComponent {
	if x != nil {
		return x.InfoCta
	}
	return nil
}

type GetBenefitScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// ca_flow_name represents the name of other service which is trying to use connected account flow
	CaFlowName string `protobuf:"bytes,2,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// A unique identifier of the flow started to connect accounts
	CaFlowId string `protobuf:"bytes,3,opt,name=ca_flow_id,json=caFlowId,proto3" json:"ca_flow_id,omitempty"`
}

func (x *GetBenefitScreenRequest) Reset() {
	*x = GetBenefitScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBenefitScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBenefitScreenRequest) ProtoMessage() {}

func (x *GetBenefitScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBenefitScreenRequest.ProtoReflect.Descriptor instead.
func (*GetBenefitScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{67}
}

func (x *GetBenefitScreenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetBenefitScreenRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *GetBenefitScreenRequest) GetCaFlowId() string {
	if x != nil {
		return x.CaFlowId
	}
	return ""
}

type GetBenefitScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,2,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Benefit screen to be rendered
	Screen *screens.BenefitScreen `protobuf:"bytes,3,opt,name=screen,proto3" json:"screen,omitempty"`
}

func (x *GetBenefitScreenResponse) Reset() {
	*x = GetBenefitScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBenefitScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBenefitScreenResponse) ProtoMessage() {}

func (x *GetBenefitScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBenefitScreenResponse.ProtoReflect.Descriptor instead.
func (*GetBenefitScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{68}
}

func (x *GetBenefitScreenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetBenefitScreenResponse) GetScreen() *screens.BenefitScreen {
	if x != nil {
		return x.Screen
	}
	return nil
}

type GetBanksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// ca_flow_name represents the name of other service which is trying to use connected account flow
	CaFlowName string `protobuf:"bytes,2,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// A unique identifier of the flow started to connect accounts
	CaFlowId string `protobuf:"bytes,3,opt,name=ca_flow_id,json=caFlowId,proto3" json:"ca_flow_id,omitempty"`
}

func (x *GetBanksRequest) Reset() {
	*x = GetBanksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBanksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBanksRequest) ProtoMessage() {}

func (x *GetBanksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBanksRequest.ProtoReflect.Descriptor instead.
func (*GetBanksRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{69}
}

func (x *GetBanksRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetBanksRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *GetBanksRequest) GetCaFlowId() string {
	if x != nil {
		return x.CaFlowId
	}
	return ""
}

type GetBanksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Popular fips list title
	PopularBankListTitle *common1.Text `protobuf:"bytes,2,opt,name=popular_bank_list_title,json=popularBankListTitle,proto3" json:"popular_bank_list_title,omitempty"`
	// Popular fips
	PopularBankList []*GetBanksResponse_BankInfo `protobuf:"bytes,3,rep,name=popular_bank_list,json=popularBankList,proto3" json:"popular_bank_list,omitempty"`
	// All fips list title
	AllBankListTitle *common1.Text `protobuf:"bytes,4,opt,name=all_bank_list_title,json=allBankListTitle,proto3" json:"all_bank_list_title,omitempty"`
	// All the fips which we want user to select
	AllBankList []*GetBanksResponse_BankInfo `protobuf:"bytes,5,rep,name=all_bank_list,json=allBankList,proto3" json:"all_bank_list,omitempty"`
	// Issue to message mapping which will be shown to user
	// e.g, if fip is not supported, fip is facing some issues
	// Client will replace key {BANK_DISPLAY_NAME} with the bank display name.
	// Values e.g, NOT_SUPPORTED - IssueMessage, FIP_DOWN - IssueMessage
	FipIssue map[string]*GetBanksResponse_IssueMessage `protobuf:"bytes,6,rep,name=fip_issue,json=fipIssue,proto3" json:"fip_issue,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Default issue message to be displayed if not found in fip_issue
	DefaultIssueMessage *GetBanksResponse_IssueMessage `protobuf:"bytes,7,opt,name=default_issue_message,json=defaultIssueMessage,proto3" json:"default_issue_message,omitempty"`
	// either onemoney tnc or finvu tnc will be showed in bank selection screen based on what AA entity we use for a bank
	FinvuTnc    *widget.CheckboxItem `protobuf:"bytes,8,opt,name=finvu_tnc,json=finvuTnc,proto3" json:"finvu_tnc,omitempty"`
	OnemoneyTnc *widget.CheckboxItem `protobuf:"bytes,9,opt,name=onemoney_tnc,json=onemoneyTnc,proto3" json:"onemoney_tnc,omitempty"`
}

func (x *GetBanksResponse) Reset() {
	*x = GetBanksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBanksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBanksResponse) ProtoMessage() {}

func (x *GetBanksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBanksResponse.ProtoReflect.Descriptor instead.
func (*GetBanksResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{70}
}

func (x *GetBanksResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetBanksResponse) GetPopularBankListTitle() *common1.Text {
	if x != nil {
		return x.PopularBankListTitle
	}
	return nil
}

func (x *GetBanksResponse) GetPopularBankList() []*GetBanksResponse_BankInfo {
	if x != nil {
		return x.PopularBankList
	}
	return nil
}

func (x *GetBanksResponse) GetAllBankListTitle() *common1.Text {
	if x != nil {
		return x.AllBankListTitle
	}
	return nil
}

func (x *GetBanksResponse) GetAllBankList() []*GetBanksResponse_BankInfo {
	if x != nil {
		return x.AllBankList
	}
	return nil
}

func (x *GetBanksResponse) GetFipIssue() map[string]*GetBanksResponse_IssueMessage {
	if x != nil {
		return x.FipIssue
	}
	return nil
}

func (x *GetBanksResponse) GetDefaultIssueMessage() *GetBanksResponse_IssueMessage {
	if x != nil {
		return x.DefaultIssueMessage
	}
	return nil
}

func (x *GetBanksResponse) GetFinvuTnc() *widget.CheckboxItem {
	if x != nil {
		return x.FinvuTnc
	}
	return nil
}

func (x *GetBanksResponse) GetOnemoneyTnc() *widget.CheckboxItem {
	if x != nil {
		return x.OnemoneyTnc
	}
	return nil
}

type GetSdkDeeplinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// ca_flow_name represents the name of other service which is trying to use connected account flow
	CaFlowName string `protobuf:"bytes,2,opt,name=ca_flow_name,json=caFlowName,proto3" json:"ca_flow_name,omitempty"`
	// A unique identifier of the flow started to connect accounts
	CaFlowId string `protobuf:"bytes,3,opt,name=ca_flow_id,json=caFlowId,proto3" json:"ca_flow_id,omitempty"`
	// Fip id user selected
	FipId string `protobuf:"bytes,4,opt,name=fip_id,json=fipId,proto3" json:"fip_id,omitempty"`
}

func (x *GetSdkDeeplinkRequest) Reset() {
	*x = GetSdkDeeplinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSdkDeeplinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSdkDeeplinkRequest) ProtoMessage() {}

func (x *GetSdkDeeplinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSdkDeeplinkRequest.ProtoReflect.Descriptor instead.
func (*GetSdkDeeplinkRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{71}
}

func (x *GetSdkDeeplinkRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSdkDeeplinkRequest) GetCaFlowName() string {
	if x != nil {
		return x.CaFlowName
	}
	return ""
}

func (x *GetSdkDeeplinkRequest) GetCaFlowId() string {
	if x != nil {
		return x.CaFlowId
	}
	return ""
}

func (x *GetSdkDeeplinkRequest) GetFipId() string {
	if x != nil {
		return x.FipId
	}
	return ""
}

type GetSdkDeeplinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *GetSdkDeeplinkResponse) Reset() {
	*x = GetSdkDeeplinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSdkDeeplinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSdkDeeplinkResponse) ProtoMessage() {}

func (x *GetSdkDeeplinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSdkDeeplinkResponse.ProtoReflect.Descriptor instead.
func (*GetSdkDeeplinkResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{72}
}

func (x *GetSdkDeeplinkResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSdkDeeplinkResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// AccDiscoveryIdentifier consist of three params:
// category: signifies if identifier type is WEAK or STRONG
// value: signifies the actual value of the identifier
// type: signifies the identifier type e.g. PAN, MOBILE (phone number), DOB (date of birth)
type AccountDiscoveryIdentifiers_AccDiscoveryIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Value    string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Type     string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) Reset() {
	*x = AccountDiscoveryIdentifiers_AccDiscoveryIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) ProtoMessage() {}

func (x *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDiscoveryIdentifiers_AccDiscoveryIdentifier.ProtoReflect.Descriptor instead.
func (*AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{40, 0}
}

func (x *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type GetBanksResponse_BankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique fip id
	FipId string `protobuf:"bytes,1,opt,name=fip_id,json=fipId,proto3" json:"fip_id,omitempty"`
	// display name for the fip - human-readable name for the user
	DisplayName *common1.Text `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// url logo
	LogoUrl *common1.VisualElement `protobuf:"bytes,3,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// Issue if any e.g, fip is not supported, fip is facing some issues
	// Values e.g, NOT_SUPPORTED, FIP_DOWN
	Issue    string           `protobuf:"bytes,4,opt,name=issue,proto3" json:"issue,omitempty"`
	AaEntity typesv2.AaEntity `protobuf:"varint,5,opt,name=aa_entity,json=aaEntity,proto3,enum=api.typesv2.AaEntity" json:"aa_entity,omitempty"`
}

func (x *GetBanksResponse_BankInfo) Reset() {
	*x = GetBanksResponse_BankInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBanksResponse_BankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBanksResponse_BankInfo) ProtoMessage() {}

func (x *GetBanksResponse_BankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBanksResponse_BankInfo.ProtoReflect.Descriptor instead.
func (*GetBanksResponse_BankInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{70, 1}
}

func (x *GetBanksResponse_BankInfo) GetFipId() string {
	if x != nil {
		return x.FipId
	}
	return ""
}

func (x *GetBanksResponse_BankInfo) GetDisplayName() *common1.Text {
	if x != nil {
		return x.DisplayName
	}
	return nil
}

func (x *GetBanksResponse_BankInfo) GetLogoUrl() *common1.VisualElement {
	if x != nil {
		return x.LogoUrl
	}
	return nil
}

func (x *GetBanksResponse_BankInfo) GetIssue() string {
	if x != nil {
		return x.Issue
	}
	return ""
}

func (x *GetBanksResponse_BankInfo) GetAaEntity() typesv2.AaEntity {
	if x != nil {
		return x.AaEntity
	}
	return typesv2.AaEntity(0)
}

// Figma : https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5201-51768&t=6mKN7q19PSHVnJUk-4
// https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5201-51735&t=6mKN7q19PSHVnJUk-4
type GetBanksResponse_IssueMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message *widget.VisualElementTitleSubtitleElement `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Cta     *deeplink.Cta                             `protobuf:"bytes,2,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *GetBanksResponse_IssueMessage) Reset() {
	*x = GetBanksResponse_IssueMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_connected_account_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBanksResponse_IssueMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBanksResponse_IssueMessage) ProtoMessage() {}

func (x *GetBanksResponse_IssueMessage) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_connected_account_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBanksResponse_IssueMessage.ProtoReflect.Descriptor instead.
func (*GetBanksResponse_IssueMessage) Descriptor() ([]byte, []int) {
	return file_api_frontend_connected_account_service_proto_rawDescGZIP(), []int{70, 2}
}

func (x *GetBanksResponse_IssueMessage) GetMessage() *widget.VisualElementTitleSubtitleElement {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *GetBanksResponse_IssueMessage) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

var File_api_frontend_connected_account_service_proto protoreflect.FileDescriptor

var file_api_frontend_connected_account_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x61, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x73, 0x2f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x5f,
	0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x61,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x62,
	0x61, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75,
	0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x84, 0x01, 0x0a, 0x1b,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x69, 0x54, 0x6e, 0x63, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x1c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x46,
	0x69, 0x54, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x9c, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x46, 0x6c,
	0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x63, 0x61, 0x5f, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x46, 0x6c,
	0x6f, 0x77, 0x49, 0x64, 0x22, 0xa9, 0x04, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a,
	0x10, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x00, 0x52, 0x0f,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12,
	0x48, 0x0a, 0x11, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0x65, 0x0a, 0x12, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54,
	0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x42,
	0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0xa4, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6e, 0x67,
	0x46, 0x69, 0x54, 0x6f, 0x46, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x09, 0x61, 0x61, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x61,
	0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xc7, 0x01, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x4c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x54, 0x6f, 0x46, 0x69, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x1b, 0x69, 0x6e, 0x69, 0x74, 0x5f,
	0x66, 0x69, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x16, 0x69, 0x6e, 0x69, 0x74, 0x46,
	0x69, 0x54, 0x6f, 0x46, 0x69, 0x46, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x22, 0xaf, 0x03, 0x0a, 0x16, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x75, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x75, 0x61, 0x12, 0x41, 0x0a, 0x09, 0x61, 0x61,
	0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x69, 0x0a,
	0x17, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x22, 0x6e, 0x75,
	0x6d, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1d, 0x6e, 0x75, 0x6d, 0x4f, 0x66, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x22, 0xb9, 0x02, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0x8b, 0x01, 0x0a, 0x19, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x32, 0x0a, 0x09, 0x61, 0x61, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xf4, 0x04,
	0x0a, 0x1a, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x41, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x70, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x49, 0x0a, 0x22, 0x61, 0x61, 0x5f, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1d, 0x61, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x68, 0x74, 0x6d, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x74, 0x6d, 0x6c, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x1a, 0x40, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x22, 0xbc, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x12, 0x25, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x21, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x22, 0xdb, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x1c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x72, 0x70,
	0x6f, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x21, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x1d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5f,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x12, 0x3f, 0x0a, 0x1c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54,
	0x65, 0x78, 0x74, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x4e, 0x0a, 0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x22, 0x5c, 0x0a, 0x09, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x3d,
	0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x58, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xb2, 0x01, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f,
	0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22,
	0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x75,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xee, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0d, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x6c, 0x0a, 0x1a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x17, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x75, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x70, 0x75,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x41, 0x61,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x50, 0x6f,
	0x70, 0x75, 0x70, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12,
	0x46, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb4, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x12, 0x59, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xcd, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x2d,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x8c, 0x01,
	0x0a, 0x26, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xff, 0x02, 0x0a,
	0x27, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a,
	0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x4d, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x12, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x22,
	0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x88,
	0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xb1, 0x02, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x46, 0x6f, 0x72, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4d,
	0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x22, 0x1e, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x71, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x84, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x46, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05,
	0x12, 0x17, 0x0a, 0x13, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x4f,
	0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x66, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x6f, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x03, 0x76, 0x75, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x76, 0x75, 0x61, 0x22,
	0xae, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x6f, 0x6f, 0x62, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x13,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x6c, 0x64, 0x5f, 0x76,
	0x75, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x6c, 0x64, 0x56, 0x75, 0x61,
	0x22, 0xc6, 0x01, 0x0a, 0x13, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x6f, 0x6f, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x03, 0x76, 0x75, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x76, 0x75, 0x61, 0x12, 0x5d, 0x0a, 0x13, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3b, 0x0a, 0x14, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x52, 0x65, 0x6f, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xed, 0x01, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x66,
	0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x70,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x66,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c,
	0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a,
	0x0a, 0x11, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x73, 0x6b, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x69, 0x70,
	0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x69, 0x70, 0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x66,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66,
	0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x55, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x88, 0x01,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69,
	0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x47, 0x0a, 0x0d, 0x66, 0x69, 0x70, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x66, 0x69, 0x70,
	0x4d, 0x65, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x43,
	0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x75, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x19, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa8, 0x02, 0x0a,
	0x29, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6f, 0x6c,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x38, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x10, 0x1e, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0xec, 0x02, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x72, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x54, 0x0a, 0x10, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x37, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x30, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6d, 0x61, 0x78, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd4, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1e,
	0x0a, 0x0b, 0x66, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b,
	0x0a, 0x0e, 0x61, 0x61, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x61,
	0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x63,
	0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x93, 0x0b,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x47, 0x0a, 0x0d, 0x66, 0x69, 0x70, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x66, 0x69, 0x70,
	0x4d, 0x65, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x1c, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x45, 0x0a, 0x1f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x58, 0x0a, 0x27, 0x66, 0x69, 0x6e, 0x76, 0x75, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x23, 0x66, 0x69, 0x6e, 0x76, 0x75, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x5e, 0x0a, 0x2a, 0x6f, 0x6e,
	0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x26, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x76, 0x32,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x56, 0x32, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x76, 0x32,
	0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3d, 0x0a, 0x19, 0x75, 0x73,
	0x65, 0x5f, 0x66, 0x69, 0x6e, 0x76, 0x75, 0x5f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x16, 0x75, 0x73, 0x65, 0x46, 0x69, 0x6e, 0x76, 0x75, 0x41, 0x73, 0x79, 0x6e, 0x63,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x12, 0x5e, 0x0a, 0x13, 0x61, 0x61, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x10, 0x61, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x4d, 0x65, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x86, 0x01, 0x0a, 0x22, 0x6e, 0x6f,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x1e, 0x6e, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x9c, 0x01, 0x0a, 0x1e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x1c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x53, 0x0a, 0x0f, 0x73, 0x64, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x44, 0x4b, 0x55, 0x69, 0x46, 0x6c, 0x6f,
	0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0d, 0x73, 0x64, 0x6b, 0x46, 0x6c, 0x6f, 0x77,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x78, 0x0a, 0x1e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x65,
	0x64, 0x5f, 0x66, 0x69, 0x70, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x65, 0x64, 0x46, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x19, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x65, 0x64, 0x46, 0x69,
	0x70, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x56, 0x0a, 0x29, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x70, 0x73, 0x5f, 0x6f, 0x74, 0x70, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x23, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x65,
	0x64, 0x46, 0x69, 0x70, 0x73, 0x4f, 0x74, 0x70, 0x1a, 0x88, 0x01, 0x0a, 0x21, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x4d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x8a, 0x02, 0x0a, 0x1b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x12, 0x8a, 0x01, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73,
	0x2e, 0x41, 0x63, 0x63, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x17, 0x61, 0x63, 0x63, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73,
	0x1a, 0x5e, 0x0a, 0x16, 0x41, 0x63, 0x63, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0xe9, 0x01, 0x0a, 0x1e, 0x4e, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65,
	0x78, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x70, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x70, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x16, 0x66, 0x69, 0x70, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x66, 0x69, 0x70, 0x4e, 0x6f, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x54, 0x65, 0x78, 0x74, 0x22, 0x8e, 0x03, 0x0a,
	0x0c, 0x56, 0x32, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3f, 0x0a,
	0x1c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x79, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x19, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x45,
	0x0a, 0x1f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x79, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x74, 0x61, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x58, 0x0a, 0x29, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x25, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x12, 0x43, 0x0a, 0x1e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x3c, 0x0a, 0x1b, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x65, 0x5f, 0x79, 0x6f, 0x75, 0x72,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x63, 0x61, 0x6e, 0x74, 0x53, 0x65, 0x65, 0x59, 0x6f, 0x75,
	0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x54, 0x65, 0x78, 0x74, 0x22, 0xb8, 0x05,
	0x0a, 0x0f, 0x53, 0x44, 0x4b, 0x55, 0x69, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62,
	0x61, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x11, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x42, 0x61, 0x72, 0x12, 0x47, 0x0a, 0x20, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x12, 0x4e, 0x0a,
	0x24, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x63, 0x74, 0x61,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x20, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x65, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x43, 0x74, 0x61, 0x54, 0x65, 0x78, 0x74, 0x12, 0x24, 0x0a,
	0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x74, 0x72, 0x79, 0x43, 0x74, 0x61, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x40, 0x0a, 0x1d, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x63, 0x74, 0x61, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x6b, 0x69, 0x70,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x43, 0x74,
	0x61, 0x54, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x6e, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6e, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x1b,
	0x6e, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x66, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x17, 0x6e, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x75,
	0x6e, 0x64, 0x53, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x19, 0x6e, 0x6f,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x74, 0x61, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e,
	0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x74, 0x61,
	0x54, 0x65, 0x78, 0x74, 0x12, 0x3a, 0x0a, 0x1a, 0x6e, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6e, 0x6f, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c,
	0x12, 0x36, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x22, 0x9b, 0x01, 0x0a, 0x19, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x65, 0x64, 0x46, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x66, 0x69, 0x70, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x07, 0x66,
	0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6d,
	0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xaa, 0x04, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x48, 0x6f, 0x6d, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x12, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x11, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x60, 0x0a, 0x16, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x48, 0x6f,
	0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x52, 0x13, 0x68,
	0x6f, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x59, 0x0a, 0x05, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x42, 0x0a,
	0x0b, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x43, 0x74,
	0x61, 0x12, 0x75, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6e,
	0x65, 0x77, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x41, 0x61, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6e, 0x65,
	0x77, 0x61, 0x6c, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x22, 0x9c, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x63, 0x61, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x61, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0xe0, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3e, 0x0a, 0x0c, 0x6e, 0x65,
	0x77, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0b, 0x6e,
	0x65, 0x77, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x9a, 0x01, 0x0a, 0x15, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x41, 0x0a, 0x09, 0x61, 0x61, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x97, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x3a, 0x02, 0x18,
	0x01, 0x22, 0x63, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xbc, 0x03, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x7d, 0x0a, 0x0f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x4d,
	0x61, 0x70, 0x12, 0x60, 0x0a, 0x1c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x19, 0x69, 0x73, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x1a, 0x6f, 0x0a, 0x12, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x41, 0x0a, 0x09, 0x61, 0x61, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x08, 0x61, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xa6, 0x01, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x36, 0x0a,
	0x17, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x69,
	0x6e, 0x75, 0x74, 0x65, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x4c, 0x0a, 0x11, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x6f, 0x6d, 0x65, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x6d, 0x0a,
	0x1c, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x6f, 0x6d, 0x65, 0x2e, 0x5a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e,
	0x74, 0x52, 0x19, 0x7a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x22, 0xb3, 0x01, 0x0a,
	0x2a, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48,
	0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a,
	0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x6f, 0x6d, 0x65, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xf6, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x45, 0x78, 0x69,
	0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x50, 0x0a, 0x0e,
	0x65, 0x78, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x45, 0x78, 0x69, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0d, 0x65, 0x78, 0x69, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47,
	0x0a, 0x0b, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x45, 0x78, 0x69, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0a, 0x65, 0x78, 0x69,
	0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x61, 0x61, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x0a,
	0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x45, 0x78, 0x69, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x14,
	0x65, 0x78, 0x69, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x12, 0x65, 0x78, 0x69, 0x74, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xee, 0x03, 0x0a, 0x23,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x70, 0x6f, 0x70, 0x75,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x50, 0x6f, 0x70,
	0x75, 0x70, 0x12, 0x41, 0x0a, 0x0f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x74, 0x61,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x43, 0x74, 0x61, 0x52, 0x07, 0x63, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4f, 0x0a, 0x16, 0x64, 0x69, 0x73, 0x6d, 0x69,
	0x73, 0x73, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x14, 0x64, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x50, 0x6f, 0x70, 0x75, 0x70,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x7d, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46, 0x6f,
	0x72, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0b, 0x66,
	0x69, 0x70, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46, 0x6f,
	0x72, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x46, 0x0a, 0x10, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0f, 0x72, 0x65, 0x6e, 0x65, 0x77,
	0x61, 0x6c, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xe7, 0x01, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2e, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c,
	0x6f, 0x77, 0x49, 0x64, 0x22, 0xfe, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6a, 0x0a, 0x15, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x13, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x12, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x6f,
	0x6c, 0x6c, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37,
	0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5a, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x22, 0xfe, 0x03, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6e, 0x0a, 0x19, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x17, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x10, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x4c, 0x0a,
	0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x17, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61,
	0x63, 0x63, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x43, 0x74, 0x61, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x43, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0d, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x22, 0x8c, 0x02, 0x0a, 0x16, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x60, 0x0a, 0x16, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x48,
	0x6f, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x52, 0x13,
	0x68, 0x6f, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x69, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x69, 0x6e, 0x66, 0x6f, 0x43, 0x74,
	0x61, 0x22, 0x95, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x63,
	0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0xa7, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x2e, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x06, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x46, 0x6c, 0x6f,
	0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x63, 0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x46, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x22, 0x83, 0x0a, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x17, 0x70, 0x6f,
	0x70, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x14, 0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x42, 0x61,
	0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x61, 0x0a, 0x11, 0x70,
	0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x70,
	0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47,
	0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x6e, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x59, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x6e, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x57, 0x0a, 0x09, 0x66, 0x69, 0x70, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x46, 0x69, 0x70, 0x49, 0x73, 0x73, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x66, 0x69, 0x70, 0x49, 0x73, 0x73, 0x75, 0x65, 0x12, 0x6d, 0x0a, 0x15, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x13, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x73,
	0x73, 0x75, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x66, 0x69,
	0x6e, 0x76, 0x75, 0x5f, 0x74, 0x6e, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x62, 0x6f, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x76, 0x75,
	0x54, 0x6e, 0x63, 0x12, 0x4d, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f,
	0x74, 0x6e, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x62, 0x6f,
	0x78, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x54,
	0x6e, 0x63, 0x1a, 0x76, 0x0a, 0x0d, 0x46, 0x69, 0x70, 0x49, 0x73, 0x73, 0x75, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xe6, 0x01, 0x0a, 0x08, 0x42,
	0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x69, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x70, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x6c,
	0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x73,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x73, 0x75, 0x65, 0x12,
	0x32, 0x0a, 0x09, 0x61, 0x61, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x41, 0x61, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x61, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x1a, 0x93, 0x01, 0x0a, 0x0c, 0x49, 0x73, 0x73, 0x75, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x59, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x28, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x43, 0x74, 0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x22, 0xaa, 0x01, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x53, 0x64, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x0c, 0x63, 0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x63, 0x61, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x66, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x66, 0x69, 0x70, 0x49, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x64,
	0x6b, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2a, 0xb9, 0x01, 0x0a,
	0x15, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x55, 0x52,
	0x50, 0x4f, 0x53, 0x45, 0x5f, 0x41, 0x41, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x52,
	0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x10, 0x03, 0x2a, 0x93, 0x01, 0x0a, 0x0d, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x48, 0x4f, 0x55,
	0x52, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59,
	0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x46,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x4d, 0x4f,
	0x4e, 0x54, 0x48, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e,
	0x43, 0x59, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x10, 0x04, 0x2a, 0x92,
	0x01, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46,
	0x55, 0x4c, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50,
	0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x2a, 0xa8, 0x01, 0x0a, 0x0d, 0x45, 0x78, 0x69, 0x74, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x4f, 0x54,
	0x50, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x56,
	0x45, 0x52, 0x59, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x4c, 0x49, 0x4e, 0x4b,
	0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x2a, 0xee,
	0x01, 0x0a, 0x0a, 0x45, 0x78, 0x69, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1b, 0x0a,
	0x17, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x58,
	0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58,
	0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x43,
	0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x27, 0x0a,
	0x23, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x06, 0x32,
	0xfb, 0x25, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x0f, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x94, 0x01, 0x0a, 0x12, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88,
	0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x90, 0x01, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64,
	0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x08, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x02, 0x01, 0x12, 0x91, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x12, 0x9a, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xbb, 0x01,
	0x0a, 0x1f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x12, 0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x46, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x0d,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x6f,
	0x6f, 0x62, 0x65, 0x12, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x6f, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x6f, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x12, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x02, 0x01, 0x12, 0x82, 0x01, 0x0a, 0x0c, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x52, 0x65, 0x6f, 0x6f, 0x62, 0x65, 0x12, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x6f,
	0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65,
	0x6f, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8e, 0x01,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69,
	0x70, 0x73, 0x12, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x70, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x46, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9a,
	0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x91, 0x01, 0x0a, 0x11,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x79, 0x6e,
	0x63, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x79, 0x6e, 0x63,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0xc4, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x45, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7,
	0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x48,
	0x6f, 0x6d, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x31, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6d, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x6f,
	0x6d, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0xaf, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x12, 0x3e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x12, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x6e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x0e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x12, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x02, 0x01, 0x12, 0xb8, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x41, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e,
	0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xc4, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x12,
	0x45, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46,
	0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0x94, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x45, 0x78, 0x69, 0x74, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x45, 0x78, 0x69, 0x74, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x64,
	0x6b, 0x45, 0x78, 0x69, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa6, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x6e, 0x65,
	0x77, 0x61, 0x6c, 0x12, 0x3b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x52,
	0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0xc1, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6e, 0x67, 0x46,
	0x69, 0x54, 0x6f, 0x46, 0x69, 0x12, 0x44, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67,
	0x65, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x69,
	0x54, 0x6f, 0x46, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x54, 0x6f, 0x46, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e,
	0xd7, 0x0a, 0x01, 0x12, 0xa0, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x9d, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x12, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x46, 0x69, 0x54, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12,
	0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x69, 0x54, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x69,
	0x54, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88,
	0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x33,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x76, 0x0a, 0x08, 0x47, 0x65,
	0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x12, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x64, 0x6b, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x6e, 0x0a,
	0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_connected_account_service_proto_rawDescOnce sync.Once
	file_api_frontend_connected_account_service_proto_rawDescData = file_api_frontend_connected_account_service_proto_rawDesc
)

func file_api_frontend_connected_account_service_proto_rawDescGZIP() []byte {
	file_api_frontend_connected_account_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_connected_account_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_connected_account_service_proto_rawDescData)
	})
	return file_api_frontend_connected_account_service_proto_rawDescData
}

var file_api_frontend_connected_account_service_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_api_frontend_connected_account_service_proto_msgTypes = make([]protoimpl.MessageInfo, 80)
var file_api_frontend_connected_account_service_proto_goTypes = []interface{}{
	(ConsentRequestPurpose)(0),                                 // 0: frontend.connected_account.ConsentRequestPurpose
	(FrequencyUnit)(0),                                         // 1: frontend.connected_account.FrequencyUnit
	(DataPullStatus)(0),                                        // 2: frontend.connected_account.DataPullStatus
	(ExitOperation)(0),                                         // 3: frontend.connected_account.ExitOperation
	(ExitReason)(0),                                            // 4: frontend.connected_account.ExitReason
	(RegisterFiTncConsentResponse_Status)(0),                   // 5: frontend.connected_account.RegisterFiTncConsentResponse.Status
	(GetBenefitsScreenParamsResponse_Status)(0),                // 6: frontend.connected_account.GetBenefitsScreenParamsResponse.Status
	(GetBenefitsScreenParamsResponse_BenefitsScreenType)(0),    // 7: frontend.connected_account.GetBenefitsScreenParamsResponse.BenefitsScreenType
	(InitiateConsentResponse_Status)(0),                        // 8: frontend.connected_account.InitiateConsentResponse.Status
	(FetchConsentParamsResponse_Status)(0),                     // 9: frontend.connected_account.FetchConsentParamsResponse.Status
	(GetLinkedAaAccountsResponse_Status)(0),                    // 10: frontend.connected_account.GetLinkedAaAccountsResponse.Status
	(GetAccountDetailsResponse_Status)(0),                      // 11: frontend.connected_account.GetAccountDetailsResponse.Status
	(GetConnectedAccountsResponse_Status)(0),                   // 12: frontend.connected_account.GetConnectedAccountsResponse.Status
	(GetRelatedAccountsForDisconnectResponse_Status)(0),        // 13: frontend.connected_account.GetRelatedAccountsForDisconnectResponse.Status
	(GetRelatedAccountsForDeleteResponse_Status)(0),            // 14: frontend.connected_account.GetRelatedAccountsForDeleteResponse.Status
	(DeleteAccountResponse_Status)(0),                          // 15: frontend.connected_account.DeleteAccountResponse.Status
	(*RegisterFiTncConsentRequest)(nil),                        // 16: frontend.connected_account.RegisterFiTncConsentRequest
	(*RegisterFiTncConsentResponse)(nil),                       // 17: frontend.connected_account.RegisterFiTncConsentResponse
	(*GetBenefitsScreenParamsRequest)(nil),                     // 18: frontend.connected_account.GetBenefitsScreenParamsRequest
	(*GetBenefitsScreenParamsResponse)(nil),                    // 19: frontend.connected_account.GetBenefitsScreenParamsResponse
	(*GetLandingPageForConnectingFiToFiRequest)(nil),           // 20: frontend.connected_account.GetLandingPageForConnectingFiToFiRequest
	(*GetLandingPageForConnectingFiToFiResponse)(nil),          // 21: frontend.connected_account.GetLandingPageForConnectingFiToFiResponse
	(*InitiateConsentRequest)(nil),                             // 22: frontend.connected_account.InitiateConsentRequest
	(*InitiateConsentResponse)(nil),                            // 23: frontend.connected_account.InitiateConsentResponse
	(*FetchConsentParamsRequest)(nil),                          // 24: frontend.connected_account.FetchConsentParamsRequest
	(*FetchConsentParamsResponse)(nil),                         // 25: frontend.connected_account.FetchConsentParamsResponse
	(*Consent)(nil),                                            // 26: frontend.connected_account.Consent
	(*ConsentMeta)(nil),                                        // 27: frontend.connected_account.ConsentMeta
	(*Frequency)(nil),                                          // 28: frontend.connected_account.Frequency
	(*GetLinkedAaAccountsRequest)(nil),                         // 29: frontend.connected_account.GetLinkedAaAccountsRequest
	(*GetLinkedAaAccountsResponse)(nil),                        // 30: frontend.connected_account.GetLinkedAaAccountsResponse
	(*GetAccountDetailsRequest)(nil),                           // 31: frontend.connected_account.GetAccountDetailsRequest
	(*GetAccountDetailsResponse)(nil),                          // 32: frontend.connected_account.GetAccountDetailsResponse
	(*GetConnectedAccountsRequest)(nil),                        // 33: frontend.connected_account.GetConnectedAccountsRequest
	(*GetConnectedAccountsResponse)(nil),                       // 34: frontend.connected_account.GetConnectedAccountsResponse
	(*GetRelatedAccountsForDisconnectRequest)(nil),             // 35: frontend.connected_account.GetRelatedAccountsForDisconnectRequest
	(*GetRelatedAccountsForDisconnectResponse)(nil),            // 36: frontend.connected_account.GetRelatedAccountsForDisconnectResponse
	(*GetRelatedAccountsForDeleteRequest)(nil),                 // 37: frontend.connected_account.GetRelatedAccountsForDeleteRequest
	(*GetRelatedAccountsForDeleteResponse)(nil),                // 38: frontend.connected_account.GetRelatedAccountsForDeleteResponse
	(*DeleteAccountRequest)(nil),                               // 39: frontend.connected_account.DeleteAccountRequest
	(*DeleteAccountResponse)(nil),                              // 40: frontend.connected_account.DeleteAccountResponse
	(*CheckReoobeRequest)(nil),                                 // 41: frontend.connected_account.CheckReoobeRequest
	(*CheckReoobeResponse)(nil),                                // 42: frontend.connected_account.CheckReoobeResponse
	(*HandleReoobeRequest)(nil),                                // 43: frontend.connected_account.HandleReoobeRequest
	(*HandleReoobeResponse)(nil),                               // 44: frontend.connected_account.HandleReoobeResponse
	(*Account)(nil),                                            // 45: frontend.connected_account.Account
	(*GetAvailableFipsRequest)(nil),                            // 46: frontend.connected_account.GetAvailableFipsRequest
	(*GetAvailableFipsResponse)(nil),                           // 47: frontend.connected_account.GetAvailableFipsResponse
	(*CreateBankPreferenceRequest)(nil),                        // 48: frontend.connected_account.CreateBankPreferenceRequest
	(*CreateBankPreferenceResponse)(nil),                       // 49: frontend.connected_account.CreateBankPreferenceResponse
	(*ResumeAccountSyncRequest)(nil),                           // 50: frontend.connected_account.ResumeAccountSyncRequest
	(*ResumeAccountSyncResponse)(nil),                          // 51: frontend.connected_account.ResumeAccountSyncResponse
	(*GetDataPullStatusFromConsentHandleRequest)(nil),          // 52: frontend.connected_account.GetDataPullStatusFromConsentHandleRequest
	(*GetDataPullStatusFromConsentHandleResponse)(nil),         // 53: frontend.connected_account.GetDataPullStatusFromConsentHandleResponse
	(*GetAllowedConfigRequest)(nil),                            // 54: frontend.connected_account.GetAllowedConfigRequest
	(*GetAllowedConfigResponse)(nil),                           // 55: frontend.connected_account.GetAllowedConfigResponse
	(*AccountDiscoveryIdentifiers)(nil),                        // 56: frontend.connected_account.AccountDiscoveryIdentifiers
	(*NoAccountsDiscoveredTextParams)(nil),                     // 57: frontend.connected_account.NoAccountsDiscoveredTextParams
	(*V2FlowParams)(nil),                                       // 58: frontend.connected_account.V2FlowParams
	(*SDKUiFlowParams)(nil),                                    // 59: frontend.connected_account.SDKUiFlowParams
	(*GroupedFipMetaDataByTypes)(nil),                          // 60: frontend.connected_account.GroupedFipMetaDataByTypes
	(*GetHomeSummaryRequest)(nil),                              // 61: frontend.connected_account.GetHomeSummaryRequest
	(*GetHomeSummaryResponse)(nil),                             // 62: frontend.connected_account.GetHomeSummaryResponse
	(*GetLandingPageOnConnectRequest)(nil),                     // 63: frontend.connected_account.GetLandingPageOnConnectRequest
	(*GetLandingPageOnConnectResponse)(nil),                    // 64: frontend.connected_account.GetLandingPageOnConnectResponse
	(*ConnectAccountRequest)(nil),                              // 65: frontend.connected_account.ConnectAccountRequest
	(*ConnectAccountResponse)(nil),                             // 66: frontend.connected_account.ConnectAccountResponse
	(*GetConnectedAccountEntryPointsRequest)(nil),              // 67: frontend.connected_account.GetConnectedAccountEntryPointsRequest
	(*GetConnectedAccountEntryPointsResponse)(nil),             // 68: frontend.connected_account.GetConnectedAccountEntryPointsResponse
	(*GetAuthTokenRequest)(nil),                                // 69: frontend.connected_account.GetAuthTokenRequest
	(*GetAuthTokenResponse)(nil),                               // 70: frontend.connected_account.GetAuthTokenResponse
	(*GetConnectedAccountsSummaryForHomeRequest)(nil),          // 71: frontend.connected_account.GetConnectedAccountsSummaryForHomeRequest
	(*GetConnectedAccountsSummaryForHomeResponse)(nil),         // 72: frontend.connected_account.GetConnectedAccountsSummaryForHomeResponse
	(*GetSdkExitDeeplinkRequest)(nil),                          // 73: frontend.connected_account.GetSdkExitDeeplinkRequest
	(*GetSdkExitDeeplinkResponse)(nil),                         // 74: frontend.connected_account.GetSdkExitDeeplinkResponse
	(*BalanceDashboardRenewalPopupOptions)(nil),                // 75: frontend.connected_account.BalanceDashboardRenewalPopupOptions
	(*GetLandingPageForRenewalRequest)(nil),                    // 76: frontend.connected_account.GetLandingPageForRenewalRequest
	(*GetLandingPageForRenewalResponse)(nil),                   // 77: frontend.connected_account.GetLandingPageForRenewalResponse
	(*GetConsentHandleStatusRequest)(nil),                      // 78: frontend.connected_account.GetConsentHandleStatusRequest
	(*GetConsentHandleStatusResponse)(nil),                     // 79: frontend.connected_account.GetConsentHandleStatusResponse
	(*GetAllDepositAccountsRequest)(nil),                       // 80: frontend.connected_account.GetAllDepositAccountsRequest
	(*GetAllDepositAccountsResponse)(nil),                      // 81: frontend.connected_account.GetAllDepositAccountsResponse
	(*DepositAccountsSection)(nil),                             // 82: frontend.connected_account.DepositAccountsSection
	(*GetBenefitScreenRequest)(nil),                            // 83: frontend.connected_account.GetBenefitScreenRequest
	(*GetBenefitScreenResponse)(nil),                           // 84: frontend.connected_account.GetBenefitScreenResponse
	(*GetBanksRequest)(nil),                                    // 85: frontend.connected_account.GetBanksRequest
	(*GetBanksResponse)(nil),                                   // 86: frontend.connected_account.GetBanksResponse
	(*GetSdkDeeplinkRequest)(nil),                              // 87: frontend.connected_account.GetSdkDeeplinkRequest
	(*GetSdkDeeplinkResponse)(nil),                             // 88: frontend.connected_account.GetSdkDeeplinkResponse
	nil,                                                        // 89: frontend.connected_account.FetchConsentParamsResponse.ConsentParamsEntry
	nil,                                                        // 90: frontend.connected_account.GetAllowedConfigResponse.AccountsDiscoveryIdentifiersEntry
	(*AccountDiscoveryIdentifiers_AccDiscoveryIdentifier)(nil), // 91: frontend.connected_account.AccountDiscoveryIdentifiers.AccDiscoveryIdentifier
	nil,                                   // 92: frontend.connected_account.GetConnectedAccountEntryPointsResponse.EntryPointMapEntry
	nil,                                   // 93: frontend.connected_account.GetBanksResponse.FipIssueEntry
	(*GetBanksResponse_BankInfo)(nil),     // 94: frontend.connected_account.GetBanksResponse.BankInfo
	(*GetBanksResponse_IssueMessage)(nil), // 95: frontend.connected_account.GetBanksResponse.IssueMessage
	(*header.RequestHeader)(nil),          // 96: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil),         // 97: frontend.header.ResponseHeader
	(*analyser.ConsentScreen)(nil),        // 98: api.typesv2.analyser.ConsentScreen
	(*deeplink.Deeplink)(nil),             // 99: frontend.deeplink.Deeplink
	(typesv2.AaEntity)(0),                 // 100: api.typesv2.AaEntity
	(AaEntity)(0),                         // 101: frontend.connected_account.AaEntity
	(*rpc.Status)(nil),                    // 102: rpc.Status
	(*typesv2.Date)(nil),                  // 103: api.typesv2.Date
	(*AccountDetail)(nil),                 // 104: frontend.connected_account.AccountDetail
	(*AccountActionOption)(nil),           // 105: frontend.connected_account.AccountActionOption
	(*features.AaConsentRenewalPopupOptions)(nil),    // 106: frontend.connected_account.features.AaConsentRenewalPopupOptions
	(*ui.IconTextComponent)(nil),                     // 107: api.typesv2.ui.IconTextComponent
	(AccountStatus)(0),                               // 108: frontend.connected_account.AccountStatus
	(*FipMeta)(nil),                                  // 109: frontend.connected_account.FipMeta
	(typesv2.Bank)(0),                                // 110: api.typesv2.Bank
	(*common.AaEntityMeta)(nil),                      // 111: frontend.connected_account.common.AaEntityMeta
	(*BalanceMeta)(nil),                              // 112: frontend.connected_account.BalanceMeta
	(*HomeAccountTile)(nil),                          // 113: frontend.connected_account.HomeAccountTile
	(common1.BooleanEnum)(0),                         // 114: api.typesv2.common.BooleanEnum
	(home.DashboardVersion)(0),                       // 115: frontend.home.DashboardVersion
	(home.ZeroStateDashboardCardVariant)(0),          // 116: frontend.home.ZeroStateDashboardCardVariant
	(*home.HomeDashboard)(nil),                       // 117: frontend.home.HomeDashboard
	(*common1.Image)(nil),                            // 118: api.typesv2.common.Image
	(*common1.Text)(nil),                             // 119: api.typesv2.common.Text
	(*deeplink.Cta)(nil),                             // 120: frontend.deeplink.Cta
	(*widget.BackgroundColour)(nil),                  // 121: api.typesv2.common.ui.widget.BackgroundColour
	(*durationpb.Duration)(nil),                      // 122: google.protobuf.Duration
	(common.ConsentHandleStatus)(0),                  // 123: frontend.connected_account.common.ConsentHandleStatus
	(accounts.Type)(0),                               // 124: accounts.Type
	(*screens.BenefitScreen)(nil),                    // 125: frontend.connected_account.screens.BenefitScreen
	(*widget.CheckboxItem)(nil),                      // 126: api.typesv2.common.ui.widget.CheckboxItem
	(*EntryPointOptions)(nil),                        // 127: frontend.connected_account.EntryPointOptions
	(*common1.VisualElement)(nil),                    // 128: api.typesv2.common.VisualElement
	(*widget.VisualElementTitleSubtitleElement)(nil), // 129: api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement
}
var file_api_frontend_connected_account_service_proto_depIdxs = []int32{
	96,  // 0: frontend.connected_account.RegisterFiTncConsentRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 1: frontend.connected_account.RegisterFiTncConsentResponse.resp_header:type_name -> frontend.header.ResponseHeader
	96,  // 2: frontend.connected_account.GetBenefitsScreenParamsRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 3: frontend.connected_account.GetBenefitsScreenParamsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	7,   // 4: frontend.connected_account.GetBenefitsScreenParamsResponse.benefits_screen_type:type_name -> frontend.connected_account.GetBenefitsScreenParamsResponse.BenefitsScreenType
	98,  // 5: frontend.connected_account.GetBenefitsScreenParamsResponse.analyser_consent:type_name -> api.typesv2.analyser.ConsentScreen
	99,  // 6: frontend.connected_account.GetBenefitsScreenParamsResponse.redirect_deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 7: frontend.connected_account.GetLandingPageForConnectingFiToFiRequest.req:type_name -> frontend.header.RequestHeader
	100, // 8: frontend.connected_account.GetLandingPageForConnectingFiToFiRequest.aa_entity:type_name -> api.typesv2.AaEntity
	97,  // 9: frontend.connected_account.GetLandingPageForConnectingFiToFiResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 10: frontend.connected_account.GetLandingPageForConnectingFiToFiResponse.init_fi_to_fi_flow_deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 11: frontend.connected_account.InitiateConsentRequest.req:type_name -> frontend.header.RequestHeader
	101, // 12: frontend.connected_account.InitiateConsentRequest.aa_entity:type_name -> frontend.connected_account.AaEntity
	0,   // 13: frontend.connected_account.InitiateConsentRequest.consent_request_purpose:type_name -> frontend.connected_account.ConsentRequestPurpose
	102, // 14: frontend.connected_account.InitiateConsentResponse.status:type_name -> rpc.Status
	97,  // 15: frontend.connected_account.InitiateConsentResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 16: frontend.connected_account.InitiateConsentResponse.next_action:type_name -> frontend.deeplink.Deeplink
	96,  // 17: frontend.connected_account.FetchConsentParamsRequest.req:type_name -> frontend.header.RequestHeader
	100, // 18: frontend.connected_account.FetchConsentParamsRequest.aa_entity:type_name -> api.typesv2.AaEntity
	102, // 19: frontend.connected_account.FetchConsentParamsResponse.status:type_name -> rpc.Status
	26,  // 20: frontend.connected_account.FetchConsentParamsResponse.consent:type_name -> frontend.connected_account.Consent
	27,  // 21: frontend.connected_account.FetchConsentParamsResponse.consent_meta:type_name -> frontend.connected_account.ConsentMeta
	89,  // 22: frontend.connected_account.FetchConsentParamsResponse.consent_params:type_name -> frontend.connected_account.FetchConsentParamsResponse.ConsentParamsEntry
	97,  // 23: frontend.connected_account.FetchConsentParamsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	103, // 24: frontend.connected_account.Consent.from:type_name -> api.typesv2.Date
	103, // 25: frontend.connected_account.Consent.to:type_name -> api.typesv2.Date
	28,  // 26: frontend.connected_account.ConsentMeta.fetch_frequency:type_name -> frontend.connected_account.Frequency
	1,   // 27: frontend.connected_account.Frequency.unit:type_name -> frontend.connected_account.FrequencyUnit
	96,  // 28: frontend.connected_account.GetLinkedAaAccountsRequest.req:type_name -> frontend.header.RequestHeader
	102, // 29: frontend.connected_account.GetLinkedAaAccountsResponse.status:type_name -> rpc.Status
	45,  // 30: frontend.connected_account.GetLinkedAaAccountsResponse.accounts:type_name -> frontend.connected_account.Account
	96,  // 31: frontend.connected_account.GetAccountDetailsRequest.req:type_name -> frontend.header.RequestHeader
	102, // 32: frontend.connected_account.GetAccountDetailsResponse.status:type_name -> rpc.Status
	104, // 33: frontend.connected_account.GetAccountDetailsResponse.account_detail:type_name -> frontend.connected_account.AccountDetail
	105, // 34: frontend.connected_account.GetAccountDetailsResponse.account_action_option_list:type_name -> frontend.connected_account.AccountActionOption
	106, // 35: frontend.connected_account.GetAccountDetailsResponse.consent_renewal_popup:type_name -> frontend.connected_account.features.AaConsentRenewalPopupOptions
	107, // 36: frontend.connected_account.GetAccountDetailsResponse.action_banner:type_name -> api.typesv2.ui.IconTextComponent
	96,  // 37: frontend.connected_account.GetConnectedAccountsRequest.req:type_name -> frontend.header.RequestHeader
	108, // 38: frontend.connected_account.GetConnectedAccountsRequest.account_status_list:type_name -> frontend.connected_account.AccountStatus
	102, // 39: frontend.connected_account.GetConnectedAccountsResponse.status:type_name -> rpc.Status
	104, // 40: frontend.connected_account.GetConnectedAccountsResponse.account_detail_list:type_name -> frontend.connected_account.AccountDetail
	96,  // 41: frontend.connected_account.GetRelatedAccountsForDisconnectRequest.req:type_name -> frontend.header.RequestHeader
	102, // 42: frontend.connected_account.GetRelatedAccountsForDisconnectResponse.status:type_name -> rpc.Status
	104, // 43: frontend.connected_account.GetRelatedAccountsForDisconnectResponse.account_detail_list:type_name -> frontend.connected_account.AccountDetail
	99,  // 44: frontend.connected_account.GetRelatedAccountsForDisconnectResponse.confirm_bottom_sheet:type_name -> frontend.deeplink.Deeplink
	96,  // 45: frontend.connected_account.GetRelatedAccountsForDeleteRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 46: frontend.connected_account.GetRelatedAccountsForDeleteResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 47: frontend.connected_account.GetRelatedAccountsForDeleteResponse.account_detail_list:type_name -> frontend.connected_account.AccountDetail
	99,  // 48: frontend.connected_account.GetRelatedAccountsForDeleteResponse.confirm_bottom_sheet:type_name -> frontend.deeplink.Deeplink
	96,  // 49: frontend.connected_account.DeleteAccountRequest.req:type_name -> frontend.header.RequestHeader
	102, // 50: frontend.connected_account.DeleteAccountResponse.status:type_name -> rpc.Status
	96,  // 51: frontend.connected_account.CheckReoobeRequest.req:type_name -> frontend.header.RequestHeader
	102, // 52: frontend.connected_account.CheckReoobeResponse.status:type_name -> rpc.Status
	104, // 53: frontend.connected_account.CheckReoobeResponse.account_detail_list:type_name -> frontend.connected_account.AccountDetail
	96,  // 54: frontend.connected_account.HandleReoobeRequest.req:type_name -> frontend.header.RequestHeader
	104, // 55: frontend.connected_account.HandleReoobeRequest.account_detail_list:type_name -> frontend.connected_account.AccountDetail
	102, // 56: frontend.connected_account.HandleReoobeResponse.status:type_name -> rpc.Status
	96,  // 57: frontend.connected_account.GetAvailableFipsRequest.req:type_name -> frontend.header.RequestHeader
	102, // 58: frontend.connected_account.GetAvailableFipsResponse.status:type_name -> rpc.Status
	109, // 59: frontend.connected_account.GetAvailableFipsResponse.fip_meta_list:type_name -> frontend.connected_account.FipMeta
	96,  // 60: frontend.connected_account.CreateBankPreferenceRequest.req:type_name -> frontend.header.RequestHeader
	110, // 61: frontend.connected_account.CreateBankPreferenceRequest.bank_list:type_name -> api.typesv2.Bank
	102, // 62: frontend.connected_account.CreateBankPreferenceResponse.status:type_name -> rpc.Status
	96,  // 63: frontend.connected_account.ResumeAccountSyncRequest.req:type_name -> frontend.header.RequestHeader
	102, // 64: frontend.connected_account.ResumeAccountSyncResponse.status:type_name -> rpc.Status
	96,  // 65: frontend.connected_account.GetDataPullStatusFromConsentHandleRequest.req:type_name -> frontend.header.RequestHeader
	102, // 66: frontend.connected_account.GetDataPullStatusFromConsentHandleResponse.status:type_name -> rpc.Status
	2,   // 67: frontend.connected_account.GetDataPullStatusFromConsentHandleResponse.data_pull_status:type_name -> frontend.connected_account.DataPullStatus
	99,  // 68: frontend.connected_account.GetDataPullStatusFromConsentHandleResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 69: frontend.connected_account.GetAllowedConfigRequest.req:type_name -> frontend.header.RequestHeader
	100, // 70: frontend.connected_account.GetAllowedConfigRequest.aa_entity_list:type_name -> api.typesv2.AaEntity
	102, // 71: frontend.connected_account.GetAllowedConfigResponse.status:type_name -> rpc.Status
	109, // 72: frontend.connected_account.GetAllowedConfigResponse.fip_meta_list:type_name -> frontend.connected_account.FipMeta
	58,  // 73: frontend.connected_account.GetAllowedConfigResponse.v2_flow_params:type_name -> frontend.connected_account.V2FlowParams
	111, // 74: frontend.connected_account.GetAllowedConfigResponse.aa_entity_meta_list:type_name -> frontend.connected_account.common.AaEntityMeta
	57,  // 75: frontend.connected_account.GetAllowedConfigResponse.no_accounts_discovered_text_params:type_name -> frontend.connected_account.NoAccountsDiscoveredTextParams
	90,  // 76: frontend.connected_account.GetAllowedConfigResponse.accounts_discovery_identifiers:type_name -> frontend.connected_account.GetAllowedConfigResponse.AccountsDiscoveryIdentifiersEntry
	59,  // 77: frontend.connected_account.GetAllowedConfigResponse.sdk_flow_params:type_name -> frontend.connected_account.SDKUiFlowParams
	60,  // 78: frontend.connected_account.GetAllowedConfigResponse.grouped_fip_meta_data_by_types:type_name -> frontend.connected_account.GroupedFipMetaDataByTypes
	91,  // 79: frontend.connected_account.AccountDiscoveryIdentifiers.acc_discovery_identifiers:type_name -> frontend.connected_account.AccountDiscoveryIdentifiers.AccDiscoveryIdentifier
	109, // 80: frontend.connected_account.GroupedFipMetaDataByTypes.fip_meta:type_name -> frontend.connected_account.FipMeta
	96,  // 81: frontend.connected_account.GetHomeSummaryRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 82: frontend.connected_account.GetHomeSummaryResponse.resp_header:type_name -> frontend.header.ResponseHeader
	112, // 83: frontend.connected_account.GetHomeSummaryResponse.collective_balance:type_name -> frontend.connected_account.BalanceMeta
	113, // 84: frontend.connected_account.GetHomeSummaryResponse.home_account_tile_list:type_name -> frontend.connected_account.HomeAccountTile
	75,  // 85: frontend.connected_account.GetHomeSummaryResponse.popup:type_name -> frontend.connected_account.BalanceDashboardRenewalPopupOptions
	107, // 86: frontend.connected_account.GetHomeSummaryResponse.renewal_cta:type_name -> api.typesv2.ui.IconTextComponent
	106, // 87: frontend.connected_account.GetHomeSummaryResponse.consent_renewal_popup:type_name -> frontend.connected_account.features.AaConsentRenewalPopupOptions
	96,  // 88: frontend.connected_account.GetLandingPageOnConnectRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 89: frontend.connected_account.GetLandingPageOnConnectResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 90: frontend.connected_account.GetLandingPageOnConnectResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	99,  // 91: frontend.connected_account.GetLandingPageOnConnectResponse.new_deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 92: frontend.connected_account.ConnectAccountRequest.req:type_name -> frontend.header.RequestHeader
	101, // 93: frontend.connected_account.ConnectAccountRequest.aa_entity:type_name -> frontend.connected_account.AaEntity
	97,  // 94: frontend.connected_account.ConnectAccountResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 95: frontend.connected_account.ConnectAccountResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 96: frontend.connected_account.GetConnectedAccountEntryPointsRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 97: frontend.connected_account.GetConnectedAccountEntryPointsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	92,  // 98: frontend.connected_account.GetConnectedAccountEntryPointsResponse.entry_point_map:type_name -> frontend.connected_account.GetConnectedAccountEntryPointsResponse.EntryPointMapEntry
	114, // 99: frontend.connected_account.GetConnectedAccountEntryPointsResponse.is_connected_account_enabled:type_name -> api.typesv2.common.BooleanEnum
	96,  // 100: frontend.connected_account.GetAuthTokenRequest.req:type_name -> frontend.header.RequestHeader
	101, // 101: frontend.connected_account.GetAuthTokenRequest.aa_entity:type_name -> frontend.connected_account.AaEntity
	97,  // 102: frontend.connected_account.GetAuthTokenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	96,  // 103: frontend.connected_account.GetConnectedAccountsSummaryForHomeRequest.req:type_name -> frontend.header.RequestHeader
	115, // 104: frontend.connected_account.GetConnectedAccountsSummaryForHomeRequest.dashboard_version:type_name -> frontend.home.DashboardVersion
	116, // 105: frontend.connected_account.GetConnectedAccountsSummaryForHomeRequest.zero_state_dashboard_variant:type_name -> frontend.home.ZeroStateDashboardCardVariant
	97,  // 106: frontend.connected_account.GetConnectedAccountsSummaryForHomeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	117, // 107: frontend.connected_account.GetConnectedAccountsSummaryForHomeResponse.dashboard_info:type_name -> frontend.home.HomeDashboard
	96,  // 108: frontend.connected_account.GetSdkExitDeeplinkRequest.req:type_name -> frontend.header.RequestHeader
	3,   // 109: frontend.connected_account.GetSdkExitDeeplinkRequest.exit_operation:type_name -> frontend.connected_account.ExitOperation
	4,   // 110: frontend.connected_account.GetSdkExitDeeplinkRequest.exit_reason:type_name -> frontend.connected_account.ExitReason
	100, // 111: frontend.connected_account.GetSdkExitDeeplinkRequest.aa_entity:type_name -> api.typesv2.AaEntity
	97,  // 112: frontend.connected_account.GetSdkExitDeeplinkResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 113: frontend.connected_account.GetSdkExitDeeplinkResponse.exit_screen_deeplink:type_name -> frontend.deeplink.Deeplink
	118, // 114: frontend.connected_account.BalanceDashboardRenewalPopupOptions.header_icon_url:type_name -> api.typesv2.common.Image
	119, // 115: frontend.connected_account.BalanceDashboardRenewalPopupOptions.title:type_name -> api.typesv2.common.Text
	119, // 116: frontend.connected_account.BalanceDashboardRenewalPopupOptions.subtitle:type_name -> api.typesv2.common.Text
	119, // 117: frontend.connected_account.BalanceDashboardRenewalPopupOptions.body:type_name -> api.typesv2.common.Text
	120, // 118: frontend.connected_account.BalanceDashboardRenewalPopupOptions.cta_list:type_name -> frontend.deeplink.Cta
	121, // 119: frontend.connected_account.BalanceDashboardRenewalPopupOptions.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	122, // 120: frontend.connected_account.BalanceDashboardRenewalPopupOptions.dismiss_popup_duration:type_name -> google.protobuf.Duration
	96,  // 121: frontend.connected_account.GetLandingPageForRenewalRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 122: frontend.connected_account.GetLandingPageForRenewalResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 123: frontend.connected_account.GetLandingPageForRenewalResponse.renewal_deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 124: frontend.connected_account.GetConsentHandleStatusRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 125: frontend.connected_account.GetConsentHandleStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	123, // 126: frontend.connected_account.GetConsentHandleStatusResponse.consent_handle_status:type_name -> frontend.connected_account.common.ConsentHandleStatus
	122, // 127: frontend.connected_account.GetConsentHandleStatusResponse.next_poll_duration:type_name -> google.protobuf.Duration
	99,  // 128: frontend.connected_account.GetConsentHandleStatusResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	96,  // 129: frontend.connected_account.GetAllDepositAccountsRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 130: frontend.connected_account.GetAllDepositAccountsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	82,  // 131: frontend.connected_account.GetAllDepositAccountsResponse.deposit_accounts_sections:type_name -> frontend.connected_account.DepositAccountsSection
	119, // 132: frontend.connected_account.GetAllDepositAccountsResponse.total_balance_text:type_name -> api.typesv2.common.Text
	112, // 133: frontend.connected_account.GetAllDepositAccountsResponse.total_balance:type_name -> frontend.connected_account.BalanceMeta
	120, // 134: frontend.connected_account.GetAllDepositAccountsResponse.connect_deposit_acc_cta:type_name -> frontend.deeplink.Cta
	107, // 135: frontend.connected_account.GetAllDepositAccountsResponse.bottom_banner:type_name -> api.typesv2.ui.IconTextComponent
	119, // 136: frontend.connected_account.DepositAccountsSection.title:type_name -> api.typesv2.common.Text
	124, // 137: frontend.connected_account.DepositAccountsSection.type:type_name -> accounts.Type
	113, // 138: frontend.connected_account.DepositAccountsSection.home_account_tile_list:type_name -> frontend.connected_account.HomeAccountTile
	107, // 139: frontend.connected_account.DepositAccountsSection.info_cta:type_name -> api.typesv2.ui.IconTextComponent
	96,  // 140: frontend.connected_account.GetBenefitScreenRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 141: frontend.connected_account.GetBenefitScreenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	125, // 142: frontend.connected_account.GetBenefitScreenResponse.screen:type_name -> frontend.connected_account.screens.BenefitScreen
	96,  // 143: frontend.connected_account.GetBanksRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 144: frontend.connected_account.GetBanksResponse.resp_header:type_name -> frontend.header.ResponseHeader
	119, // 145: frontend.connected_account.GetBanksResponse.popular_bank_list_title:type_name -> api.typesv2.common.Text
	94,  // 146: frontend.connected_account.GetBanksResponse.popular_bank_list:type_name -> frontend.connected_account.GetBanksResponse.BankInfo
	119, // 147: frontend.connected_account.GetBanksResponse.all_bank_list_title:type_name -> api.typesv2.common.Text
	94,  // 148: frontend.connected_account.GetBanksResponse.all_bank_list:type_name -> frontend.connected_account.GetBanksResponse.BankInfo
	93,  // 149: frontend.connected_account.GetBanksResponse.fip_issue:type_name -> frontend.connected_account.GetBanksResponse.FipIssueEntry
	95,  // 150: frontend.connected_account.GetBanksResponse.default_issue_message:type_name -> frontend.connected_account.GetBanksResponse.IssueMessage
	126, // 151: frontend.connected_account.GetBanksResponse.finvu_tnc:type_name -> api.typesv2.common.ui.widget.CheckboxItem
	126, // 152: frontend.connected_account.GetBanksResponse.onemoney_tnc:type_name -> api.typesv2.common.ui.widget.CheckboxItem
	96,  // 153: frontend.connected_account.GetSdkDeeplinkRequest.req:type_name -> frontend.header.RequestHeader
	97,  // 154: frontend.connected_account.GetSdkDeeplinkResponse.resp_header:type_name -> frontend.header.ResponseHeader
	99,  // 155: frontend.connected_account.GetSdkDeeplinkResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	56,  // 156: frontend.connected_account.GetAllowedConfigResponse.AccountsDiscoveryIdentifiersEntry.value:type_name -> frontend.connected_account.AccountDiscoveryIdentifiers
	127, // 157: frontend.connected_account.GetConnectedAccountEntryPointsResponse.EntryPointMapEntry.value:type_name -> frontend.connected_account.EntryPointOptions
	95,  // 158: frontend.connected_account.GetBanksResponse.FipIssueEntry.value:type_name -> frontend.connected_account.GetBanksResponse.IssueMessage
	119, // 159: frontend.connected_account.GetBanksResponse.BankInfo.display_name:type_name -> api.typesv2.common.Text
	128, // 160: frontend.connected_account.GetBanksResponse.BankInfo.logo_url:type_name -> api.typesv2.common.VisualElement
	100, // 161: frontend.connected_account.GetBanksResponse.BankInfo.aa_entity:type_name -> api.typesv2.AaEntity
	129, // 162: frontend.connected_account.GetBanksResponse.IssueMessage.message:type_name -> api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement
	120, // 163: frontend.connected_account.GetBanksResponse.IssueMessage.cta:type_name -> frontend.deeplink.Cta
	22,  // 164: frontend.connected_account.ConnectedAccount.InitiateConsent:input_type -> frontend.connected_account.InitiateConsentRequest
	24,  // 165: frontend.connected_account.ConnectedAccount.FetchConsentParams:input_type -> frontend.connected_account.FetchConsentParamsRequest
	29,  // 166: frontend.connected_account.ConnectedAccount.GetLinkedAaAccounts:input_type -> frontend.connected_account.GetLinkedAaAccountsRequest
	31,  // 167: frontend.connected_account.ConnectedAccount.GetAccountDetails:input_type -> frontend.connected_account.GetAccountDetailsRequest
	33,  // 168: frontend.connected_account.ConnectedAccount.GetConnectedAccounts:input_type -> frontend.connected_account.GetConnectedAccountsRequest
	35,  // 169: frontend.connected_account.ConnectedAccount.GetRelatedAccountsForDisconnect:input_type -> frontend.connected_account.GetRelatedAccountsForDisconnectRequest
	39,  // 170: frontend.connected_account.ConnectedAccount.DeleteAccount:input_type -> frontend.connected_account.DeleteAccountRequest
	41,  // 171: frontend.connected_account.ConnectedAccount.CheckReoobe:input_type -> frontend.connected_account.CheckReoobeRequest
	43,  // 172: frontend.connected_account.ConnectedAccount.HandleReoobe:input_type -> frontend.connected_account.HandleReoobeRequest
	46,  // 173: frontend.connected_account.ConnectedAccount.GetAvailableFips:input_type -> frontend.connected_account.GetAvailableFipsRequest
	48,  // 174: frontend.connected_account.ConnectedAccount.CreateBankPreference:input_type -> frontend.connected_account.CreateBankPreferenceRequest
	50,  // 175: frontend.connected_account.ConnectedAccount.ResumeAccountSync:input_type -> frontend.connected_account.ResumeAccountSyncRequest
	52,  // 176: frontend.connected_account.ConnectedAccount.GetDataPullStatusFromConsentHandle:input_type -> frontend.connected_account.GetDataPullStatusFromConsentHandleRequest
	54,  // 177: frontend.connected_account.ConnectedAccount.GetAllowedConfig:input_type -> frontend.connected_account.GetAllowedConfigRequest
	61,  // 178: frontend.connected_account.ConnectedAccount.GetHomeSummary:input_type -> frontend.connected_account.GetHomeSummaryRequest
	37,  // 179: frontend.connected_account.ConnectedAccount.GetRelatedAccountsForDelete:input_type -> frontend.connected_account.GetRelatedAccountsForDeleteRequest
	63,  // 180: frontend.connected_account.ConnectedAccount.GetLandingPageOnConnect:input_type -> frontend.connected_account.GetLandingPageOnConnectRequest
	65,  // 181: frontend.connected_account.ConnectedAccount.ConnectAccount:input_type -> frontend.connected_account.ConnectAccountRequest
	67,  // 182: frontend.connected_account.ConnectedAccount.GetConnectedAccountEntryPoints:input_type -> frontend.connected_account.GetConnectedAccountEntryPointsRequest
	69,  // 183: frontend.connected_account.ConnectedAccount.GetAuthToken:input_type -> frontend.connected_account.GetAuthTokenRequest
	71,  // 184: frontend.connected_account.ConnectedAccount.GetConnectedAccountsSummaryForHome:input_type -> frontend.connected_account.GetConnectedAccountsSummaryForHomeRequest
	73,  // 185: frontend.connected_account.ConnectedAccount.GetSdkExitDeeplink:input_type -> frontend.connected_account.GetSdkExitDeeplinkRequest
	76,  // 186: frontend.connected_account.ConnectedAccount.GetLandingPageForRenewal:input_type -> frontend.connected_account.GetLandingPageForRenewalRequest
	20,  // 187: frontend.connected_account.ConnectedAccount.GetLandingPageForConnectingFiToFi:input_type -> frontend.connected_account.GetLandingPageForConnectingFiToFiRequest
	78,  // 188: frontend.connected_account.ConnectedAccount.GetConsentHandleStatus:input_type -> frontend.connected_account.GetConsentHandleStatusRequest
	80,  // 189: frontend.connected_account.ConnectedAccount.GetAllDepositAccounts:input_type -> frontend.connected_account.GetAllDepositAccountsRequest
	16,  // 190: frontend.connected_account.ConnectedAccount.RegisterFiTncConsent:input_type -> frontend.connected_account.RegisterFiTncConsentRequest
	18,  // 191: frontend.connected_account.ConnectedAccount.GetBenefitsScreenParams:input_type -> frontend.connected_account.GetBenefitsScreenParamsRequest
	83,  // 192: frontend.connected_account.ConnectedAccount.GetBenefitScreen:input_type -> frontend.connected_account.GetBenefitScreenRequest
	85,  // 193: frontend.connected_account.ConnectedAccount.GetBanks:input_type -> frontend.connected_account.GetBanksRequest
	87,  // 194: frontend.connected_account.ConnectedAccount.GetSdkDeeplink:input_type -> frontend.connected_account.GetSdkDeeplinkRequest
	23,  // 195: frontend.connected_account.ConnectedAccount.InitiateConsent:output_type -> frontend.connected_account.InitiateConsentResponse
	25,  // 196: frontend.connected_account.ConnectedAccount.FetchConsentParams:output_type -> frontend.connected_account.FetchConsentParamsResponse
	30,  // 197: frontend.connected_account.ConnectedAccount.GetLinkedAaAccounts:output_type -> frontend.connected_account.GetLinkedAaAccountsResponse
	32,  // 198: frontend.connected_account.ConnectedAccount.GetAccountDetails:output_type -> frontend.connected_account.GetAccountDetailsResponse
	34,  // 199: frontend.connected_account.ConnectedAccount.GetConnectedAccounts:output_type -> frontend.connected_account.GetConnectedAccountsResponse
	36,  // 200: frontend.connected_account.ConnectedAccount.GetRelatedAccountsForDisconnect:output_type -> frontend.connected_account.GetRelatedAccountsForDisconnectResponse
	40,  // 201: frontend.connected_account.ConnectedAccount.DeleteAccount:output_type -> frontend.connected_account.DeleteAccountResponse
	42,  // 202: frontend.connected_account.ConnectedAccount.CheckReoobe:output_type -> frontend.connected_account.CheckReoobeResponse
	44,  // 203: frontend.connected_account.ConnectedAccount.HandleReoobe:output_type -> frontend.connected_account.HandleReoobeResponse
	47,  // 204: frontend.connected_account.ConnectedAccount.GetAvailableFips:output_type -> frontend.connected_account.GetAvailableFipsResponse
	49,  // 205: frontend.connected_account.ConnectedAccount.CreateBankPreference:output_type -> frontend.connected_account.CreateBankPreferenceResponse
	51,  // 206: frontend.connected_account.ConnectedAccount.ResumeAccountSync:output_type -> frontend.connected_account.ResumeAccountSyncResponse
	53,  // 207: frontend.connected_account.ConnectedAccount.GetDataPullStatusFromConsentHandle:output_type -> frontend.connected_account.GetDataPullStatusFromConsentHandleResponse
	55,  // 208: frontend.connected_account.ConnectedAccount.GetAllowedConfig:output_type -> frontend.connected_account.GetAllowedConfigResponse
	62,  // 209: frontend.connected_account.ConnectedAccount.GetHomeSummary:output_type -> frontend.connected_account.GetHomeSummaryResponse
	38,  // 210: frontend.connected_account.ConnectedAccount.GetRelatedAccountsForDelete:output_type -> frontend.connected_account.GetRelatedAccountsForDeleteResponse
	64,  // 211: frontend.connected_account.ConnectedAccount.GetLandingPageOnConnect:output_type -> frontend.connected_account.GetLandingPageOnConnectResponse
	66,  // 212: frontend.connected_account.ConnectedAccount.ConnectAccount:output_type -> frontend.connected_account.ConnectAccountResponse
	68,  // 213: frontend.connected_account.ConnectedAccount.GetConnectedAccountEntryPoints:output_type -> frontend.connected_account.GetConnectedAccountEntryPointsResponse
	70,  // 214: frontend.connected_account.ConnectedAccount.GetAuthToken:output_type -> frontend.connected_account.GetAuthTokenResponse
	72,  // 215: frontend.connected_account.ConnectedAccount.GetConnectedAccountsSummaryForHome:output_type -> frontend.connected_account.GetConnectedAccountsSummaryForHomeResponse
	74,  // 216: frontend.connected_account.ConnectedAccount.GetSdkExitDeeplink:output_type -> frontend.connected_account.GetSdkExitDeeplinkResponse
	77,  // 217: frontend.connected_account.ConnectedAccount.GetLandingPageForRenewal:output_type -> frontend.connected_account.GetLandingPageForRenewalResponse
	21,  // 218: frontend.connected_account.ConnectedAccount.GetLandingPageForConnectingFiToFi:output_type -> frontend.connected_account.GetLandingPageForConnectingFiToFiResponse
	79,  // 219: frontend.connected_account.ConnectedAccount.GetConsentHandleStatus:output_type -> frontend.connected_account.GetConsentHandleStatusResponse
	81,  // 220: frontend.connected_account.ConnectedAccount.GetAllDepositAccounts:output_type -> frontend.connected_account.GetAllDepositAccountsResponse
	17,  // 221: frontend.connected_account.ConnectedAccount.RegisterFiTncConsent:output_type -> frontend.connected_account.RegisterFiTncConsentResponse
	19,  // 222: frontend.connected_account.ConnectedAccount.GetBenefitsScreenParams:output_type -> frontend.connected_account.GetBenefitsScreenParamsResponse
	84,  // 223: frontend.connected_account.ConnectedAccount.GetBenefitScreen:output_type -> frontend.connected_account.GetBenefitScreenResponse
	86,  // 224: frontend.connected_account.ConnectedAccount.GetBanks:output_type -> frontend.connected_account.GetBanksResponse
	88,  // 225: frontend.connected_account.ConnectedAccount.GetSdkDeeplink:output_type -> frontend.connected_account.GetSdkDeeplinkResponse
	195, // [195:226] is the sub-list for method output_type
	164, // [164:195] is the sub-list for method input_type
	164, // [164:164] is the sub-list for extension type_name
	164, // [164:164] is the sub-list for extension extendee
	0,   // [0:164] is the sub-list for field type_name
}

func init() { file_api_frontend_connected_account_service_proto_init() }
func file_api_frontend_connected_account_service_proto_init() {
	if File_api_frontend_connected_account_service_proto != nil {
		return
	}
	file_api_frontend_connected_account_account_proto_init()
	file_api_frontend_connected_account_entry_point_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_connected_account_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterFiTncConsentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterFiTncConsentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBenefitsScreenParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBenefitsScreenParamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingPageForConnectingFiToFiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingPageForConnectingFiToFiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateConsentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateConsentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchConsentParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchConsentParamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Consent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Frequency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkedAaAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkedAaAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectedAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectedAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelatedAccountsForDisconnectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelatedAccountsForDisconnectResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelatedAccountsForDeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelatedAccountsForDeleteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckReoobeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckReoobeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleReoobeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleReoobeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableFipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableFipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBankPreferenceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBankPreferenceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeAccountSyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeAccountSyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataPullStatusFromConsentHandleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataPullStatusFromConsentHandleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllowedConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllowedConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDiscoveryIdentifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoAccountsDiscoveredTextParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*V2FlowParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDKUiFlowParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupedFipMetaDataByTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHomeSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHomeSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingPageOnConnectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingPageOnConnectResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectedAccountEntryPointsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectedAccountEntryPointsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectedAccountsSummaryForHomeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectedAccountsSummaryForHomeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSdkExitDeeplinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSdkExitDeeplinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceDashboardRenewalPopupOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingPageForRenewalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLandingPageForRenewalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConsentHandleStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConsentHandleStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllDepositAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllDepositAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositAccountsSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBenefitScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBenefitScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBanksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBanksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSdkDeeplinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSdkDeeplinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDiscoveryIdentifiers_AccDiscoveryIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBanksResponse_BankInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_connected_account_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBanksResponse_IssueMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_connected_account_service_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*GetBenefitsScreenParamsResponse_AnalyserConsent)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_connected_account_service_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   80,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_connected_account_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_connected_account_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_connected_account_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_connected_account_service_proto_msgTypes,
	}.Build()
	File_api_frontend_connected_account_service_proto = out.File
	file_api_frontend_connected_account_service_proto_rawDesc = nil
	file_api_frontend_connected_account_service_proto_goTypes = nil
	file_api_frontend_connected_account_service_proto_depIdxs = nil
}
