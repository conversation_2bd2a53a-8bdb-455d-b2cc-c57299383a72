// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/connected_account/service.proto

package connected_account

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	common "github.com/epifi/be-common/api/typesv2/common"

	common1 "github.com/epifi/gamma/api/frontend/connected_account/common"

	home "github.com/epifi/gamma/api/frontend/home"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = common.BooleanEnum(0)

	_ = common1.ConsentHandleStatus(0)

	_ = home.DashboardVersion(0)

	_ = typesv2.AaEntity(0)
)

// Validate checks the field values on RegisterFiTncConsentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterFiTncConsentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterFiTncConsentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterFiTncConsentRequestMultiError, or nil if none found.
func (m *RegisterFiTncConsentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterFiTncConsentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := RegisterFiTncConsentRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterFiTncConsentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterFiTncConsentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterFiTncConsentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetConsentIds()) < 1 {
		err := RegisterFiTncConsentRequestValidationError{
			field:  "ConsentIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RegisterFiTncConsentRequestMultiError(errors)
	}

	return nil
}

// RegisterFiTncConsentRequestMultiError is an error wrapping multiple
// validation errors returned by RegisterFiTncConsentRequest.ValidateAll() if
// the designated constraints aren't met.
type RegisterFiTncConsentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterFiTncConsentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterFiTncConsentRequestMultiError) AllErrors() []error { return m }

// RegisterFiTncConsentRequestValidationError is the validation error returned
// by RegisterFiTncConsentRequest.Validate if the designated constraints
// aren't met.
type RegisterFiTncConsentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterFiTncConsentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterFiTncConsentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterFiTncConsentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterFiTncConsentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterFiTncConsentRequestValidationError) ErrorName() string {
	return "RegisterFiTncConsentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterFiTncConsentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterFiTncConsentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterFiTncConsentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterFiTncConsentRequestValidationError{}

// Validate checks the field values on RegisterFiTncConsentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterFiTncConsentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterFiTncConsentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterFiTncConsentResponseMultiError, or nil if none found.
func (m *RegisterFiTncConsentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterFiTncConsentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterFiTncConsentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterFiTncConsentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterFiTncConsentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegisterFiTncConsentResponseMultiError(errors)
	}

	return nil
}

// RegisterFiTncConsentResponseMultiError is an error wrapping multiple
// validation errors returned by RegisterFiTncConsentResponse.ValidateAll() if
// the designated constraints aren't met.
type RegisterFiTncConsentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterFiTncConsentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterFiTncConsentResponseMultiError) AllErrors() []error { return m }

// RegisterFiTncConsentResponseValidationError is the validation error returned
// by RegisterFiTncConsentResponse.Validate if the designated constraints
// aren't met.
type RegisterFiTncConsentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterFiTncConsentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterFiTncConsentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterFiTncConsentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterFiTncConsentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterFiTncConsentResponseValidationError) ErrorName() string {
	return "RegisterFiTncConsentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterFiTncConsentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterFiTncConsentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterFiTncConsentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterFiTncConsentResponseValidationError{}

// Validate checks the field values on GetBenefitsScreenParamsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBenefitsScreenParamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBenefitsScreenParamsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBenefitsScreenParamsRequestMultiError, or nil if none found.
func (m *GetBenefitsScreenParamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBenefitsScreenParamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetBenefitsScreenParamsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBenefitsScreenParamsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBenefitsScreenParamsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBenefitsScreenParamsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaFlowName

	// no validation rules for CaFlowId

	if len(errors) > 0 {
		return GetBenefitsScreenParamsRequestMultiError(errors)
	}

	return nil
}

// GetBenefitsScreenParamsRequestMultiError is an error wrapping multiple
// validation errors returned by GetBenefitsScreenParamsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetBenefitsScreenParamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBenefitsScreenParamsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBenefitsScreenParamsRequestMultiError) AllErrors() []error { return m }

// GetBenefitsScreenParamsRequestValidationError is the validation error
// returned by GetBenefitsScreenParamsRequest.Validate if the designated
// constraints aren't met.
type GetBenefitsScreenParamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBenefitsScreenParamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBenefitsScreenParamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBenefitsScreenParamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBenefitsScreenParamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBenefitsScreenParamsRequestValidationError) ErrorName() string {
	return "GetBenefitsScreenParamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBenefitsScreenParamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBenefitsScreenParamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBenefitsScreenParamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBenefitsScreenParamsRequestValidationError{}

// Validate checks the field values on GetBenefitsScreenParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBenefitsScreenParamsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBenefitsScreenParamsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBenefitsScreenParamsResponseMultiError, or nil if none found.
func (m *GetBenefitsScreenParamsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBenefitsScreenParamsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBenefitsScreenParamsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBenefitsScreenParamsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBenefitsScreenParamsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BenefitsScreenType

	if all {
		switch v := interface{}(m.GetRedirectDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBenefitsScreenParamsResponseValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBenefitsScreenParamsResponseValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBenefitsScreenParamsResponseValidationError{
				field:  "RedirectDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Params.(type) {
	case *GetBenefitsScreenParamsResponse_AnalyserConsent:
		if v == nil {
			err := GetBenefitsScreenParamsResponseValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAnalyserConsent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBenefitsScreenParamsResponseValidationError{
						field:  "AnalyserConsent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBenefitsScreenParamsResponseValidationError{
						field:  "AnalyserConsent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAnalyserConsent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBenefitsScreenParamsResponseValidationError{
					field:  "AnalyserConsent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetBenefitsScreenParamsResponseMultiError(errors)
	}

	return nil
}

// GetBenefitsScreenParamsResponseMultiError is an error wrapping multiple
// validation errors returned by GetBenefitsScreenParamsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetBenefitsScreenParamsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBenefitsScreenParamsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBenefitsScreenParamsResponseMultiError) AllErrors() []error { return m }

// GetBenefitsScreenParamsResponseValidationError is the validation error
// returned by GetBenefitsScreenParamsResponse.Validate if the designated
// constraints aren't met.
type GetBenefitsScreenParamsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBenefitsScreenParamsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBenefitsScreenParamsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBenefitsScreenParamsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBenefitsScreenParamsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBenefitsScreenParamsResponseValidationError) ErrorName() string {
	return "GetBenefitsScreenParamsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBenefitsScreenParamsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBenefitsScreenParamsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBenefitsScreenParamsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBenefitsScreenParamsResponseValidationError{}

// Validate checks the field values on GetLandingPageForConnectingFiToFiRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLandingPageForConnectingFiToFiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLandingPageForConnectingFiToFiRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLandingPageForConnectingFiToFiRequestMultiError, or nil if none found.
func (m *GetLandingPageForConnectingFiToFiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingPageForConnectingFiToFiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetLandingPageForConnectingFiToFiRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageForConnectingFiToFiRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageForConnectingFiToFiRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageForConnectingFiToFiRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _GetLandingPageForConnectingFiToFiRequest_AaEntity_NotInLookup[m.GetAaEntity()]; ok {
		err := GetLandingPageForConnectingFiToFiRequestValidationError{
			field:  "AaEntity",
			reason: "value must not be in list [AA_ENTITY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLandingPageForConnectingFiToFiRequestMultiError(errors)
	}

	return nil
}

// GetLandingPageForConnectingFiToFiRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetLandingPageForConnectingFiToFiRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLandingPageForConnectingFiToFiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingPageForConnectingFiToFiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingPageForConnectingFiToFiRequestMultiError) AllErrors() []error { return m }

// GetLandingPageForConnectingFiToFiRequestValidationError is the validation
// error returned by GetLandingPageForConnectingFiToFiRequest.Validate if the
// designated constraints aren't met.
type GetLandingPageForConnectingFiToFiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingPageForConnectingFiToFiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingPageForConnectingFiToFiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingPageForConnectingFiToFiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingPageForConnectingFiToFiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingPageForConnectingFiToFiRequestValidationError) ErrorName() string {
	return "GetLandingPageForConnectingFiToFiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingPageForConnectingFiToFiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingPageForConnectingFiToFiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingPageForConnectingFiToFiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingPageForConnectingFiToFiRequestValidationError{}

var _GetLandingPageForConnectingFiToFiRequest_AaEntity_NotInLookup = map[typesv2.AaEntity]struct{}{
	0: {},
}

// Validate checks the field values on
// GetLandingPageForConnectingFiToFiResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLandingPageForConnectingFiToFiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLandingPageForConnectingFiToFiResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLandingPageForConnectingFiToFiResponseMultiError, or nil if none found.
func (m *GetLandingPageForConnectingFiToFiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingPageForConnectingFiToFiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageForConnectingFiToFiResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageForConnectingFiToFiResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageForConnectingFiToFiResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInitFiToFiFlowDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageForConnectingFiToFiResponseValidationError{
					field:  "InitFiToFiFlowDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageForConnectingFiToFiResponseValidationError{
					field:  "InitFiToFiFlowDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitFiToFiFlowDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageForConnectingFiToFiResponseValidationError{
				field:  "InitFiToFiFlowDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingPageForConnectingFiToFiResponseMultiError(errors)
	}

	return nil
}

// GetLandingPageForConnectingFiToFiResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetLandingPageForConnectingFiToFiResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLandingPageForConnectingFiToFiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingPageForConnectingFiToFiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingPageForConnectingFiToFiResponseMultiError) AllErrors() []error { return m }

// GetLandingPageForConnectingFiToFiResponseValidationError is the validation
// error returned by GetLandingPageForConnectingFiToFiResponse.Validate if the
// designated constraints aren't met.
type GetLandingPageForConnectingFiToFiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingPageForConnectingFiToFiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingPageForConnectingFiToFiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingPageForConnectingFiToFiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingPageForConnectingFiToFiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingPageForConnectingFiToFiResponseValidationError) ErrorName() string {
	return "GetLandingPageForConnectingFiToFiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingPageForConnectingFiToFiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingPageForConnectingFiToFiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingPageForConnectingFiToFiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingPageForConnectingFiToFiResponseValidationError{}

// Validate checks the field values on InitiateConsentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateConsentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateConsentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateConsentRequestMultiError, or nil if none found.
func (m *InitiateConsentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateConsentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := InitiateConsentRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateConsentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateConsentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateConsentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vua

	// no validation rules for AaEntity

	// no validation rules for ConsentRequestPurpose

	// no validation rules for CaFlowName

	// no validation rules for NumOfConsentHandlesToGenerate

	// no validation rules for ConnectionFlowId

	if len(errors) > 0 {
		return InitiateConsentRequestMultiError(errors)
	}

	return nil
}

// InitiateConsentRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateConsentRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateConsentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateConsentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateConsentRequestMultiError) AllErrors() []error { return m }

// InitiateConsentRequestValidationError is the validation error returned by
// InitiateConsentRequest.Validate if the designated constraints aren't met.
type InitiateConsentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateConsentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateConsentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateConsentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateConsentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateConsentRequestValidationError) ErrorName() string {
	return "InitiateConsentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateConsentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateConsentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateConsentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateConsentRequestValidationError{}

// Validate checks the field values on InitiateConsentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateConsentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateConsentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateConsentResponseMultiError, or nil if none found.
func (m *InitiateConsentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateConsentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateConsentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateConsentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentHandle

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateConsentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateConsentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateConsentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateConsentResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateConsentResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateConsentResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateConsentResponseMultiError(errors)
	}

	return nil
}

// InitiateConsentResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateConsentResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateConsentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateConsentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateConsentResponseMultiError) AllErrors() []error { return m }

// InitiateConsentResponseValidationError is the validation error returned by
// InitiateConsentResponse.Validate if the designated constraints aren't met.
type InitiateConsentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateConsentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateConsentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateConsentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateConsentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateConsentResponseValidationError) ErrorName() string {
	return "InitiateConsentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateConsentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateConsentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateConsentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateConsentResponseValidationError{}

// Validate checks the field values on FetchConsentParamsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchConsentParamsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchConsentParamsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchConsentParamsRequestMultiError, or nil if none found.
func (m *FetchConsentParamsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchConsentParamsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := FetchConsentParamsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchConsentParamsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchConsentParamsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchConsentParamsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AaEntity

	if len(errors) > 0 {
		return FetchConsentParamsRequestMultiError(errors)
	}

	return nil
}

// FetchConsentParamsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchConsentParamsRequest.ValidateAll() if the
// designated constraints aren't met.
type FetchConsentParamsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchConsentParamsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchConsentParamsRequestMultiError) AllErrors() []error { return m }

// FetchConsentParamsRequestValidationError is the validation error returned by
// FetchConsentParamsRequest.Validate if the designated constraints aren't met.
type FetchConsentParamsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchConsentParamsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchConsentParamsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchConsentParamsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchConsentParamsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchConsentParamsRequestValidationError) ErrorName() string {
	return "FetchConsentParamsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchConsentParamsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchConsentParamsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchConsentParamsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchConsentParamsRequestValidationError{}

// Validate checks the field values on FetchConsentParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchConsentParamsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchConsentParamsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchConsentParamsResponseMultiError, or nil if none found.
func (m *FetchConsentParamsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchConsentParamsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchConsentParamsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "Consent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "Consent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchConsentParamsResponseValidationError{
				field:  "Consent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "ConsentMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "ConsentMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchConsentParamsResponseValidationError{
				field:  "ConsentMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentParams

	// no validation rules for AaEntityConsentBottomInfoText

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchConsentParamsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchConsentParamsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentDetailsHtmlString

	if len(errors) > 0 {
		return FetchConsentParamsResponseMultiError(errors)
	}

	return nil
}

// FetchConsentParamsResponseMultiError is an error wrapping multiple
// validation errors returned by FetchConsentParamsResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchConsentParamsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchConsentParamsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchConsentParamsResponseMultiError) AllErrors() []error { return m }

// FetchConsentParamsResponseValidationError is the validation error returned
// by FetchConsentParamsResponse.Validate if the designated constraints aren't met.
type FetchConsentParamsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchConsentParamsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchConsentParamsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchConsentParamsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchConsentParamsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchConsentParamsResponseValidationError) ErrorName() string {
	return "FetchConsentParamsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchConsentParamsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchConsentParamsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchConsentParamsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchConsentParamsResponseValidationError{}

// Validate checks the field values on Consent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Consent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Consent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ConsentMultiError, or nil if none found.
func (m *Consent) ValidateAll() error {
	return m.validate(true)
}

func (m *Consent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayTextRange

	// no validation rules for DisplayTextValidPeriod

	if len(errors) > 0 {
		return ConsentMultiError(errors)
	}

	return nil
}

// ConsentMultiError is an error wrapping multiple validation errors returned
// by Consent.ValidateAll() if the designated constraints aren't met.
type ConsentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentMultiError) AllErrors() []error { return m }

// ConsentValidationError is the validation error returned by Consent.Validate
// if the designated constraints aren't met.
type ConsentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentValidationError) ErrorName() string { return "ConsentValidationError" }

// Error satisfies the builtin error interface
func (e ConsentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentValidationError{}

// Validate checks the field values on ConsentMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConsentMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsentMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConsentMetaMultiError, or
// nil if none found.
func (m *ConsentMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsentMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayTextConsentPurpose

	// no validation rules for DisplayTextConsentTypesShared

	// no validation rules for DisplayTextFetchFrequency

	if all {
		switch v := interface{}(m.GetFetchFrequency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentMetaValidationError{
					field:  "FetchFrequency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentMetaValidationError{
					field:  "FetchFrequency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFetchFrequency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentMetaValidationError{
				field:  "FetchFrequency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsentMetaMultiError(errors)
	}

	return nil
}

// ConsentMetaMultiError is an error wrapping multiple validation errors
// returned by ConsentMeta.ValidateAll() if the designated constraints aren't met.
type ConsentMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentMetaMultiError) AllErrors() []error { return m }

// ConsentMetaValidationError is the validation error returned by
// ConsentMeta.Validate if the designated constraints aren't met.
type ConsentMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentMetaValidationError) ErrorName() string { return "ConsentMetaValidationError" }

// Error satisfies the builtin error interface
func (e ConsentMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsentMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentMetaValidationError{}

// Validate checks the field values on Frequency with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Frequency) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Frequency with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FrequencyMultiError, or nil
// if none found.
func (m *Frequency) ValidateAll() error {
	return m.validate(true)
}

func (m *Frequency) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Unit

	// no validation rules for Val

	if len(errors) > 0 {
		return FrequencyMultiError(errors)
	}

	return nil
}

// FrequencyMultiError is an error wrapping multiple validation errors returned
// by Frequency.ValidateAll() if the designated constraints aren't met.
type FrequencyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FrequencyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FrequencyMultiError) AllErrors() []error { return m }

// FrequencyValidationError is the validation error returned by
// Frequency.Validate if the designated constraints aren't met.
type FrequencyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FrequencyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FrequencyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FrequencyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FrequencyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FrequencyValidationError) ErrorName() string { return "FrequencyValidationError" }

// Error satisfies the builtin error interface
func (e FrequencyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFrequency.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FrequencyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FrequencyValidationError{}

// Validate checks the field values on GetLinkedAaAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLinkedAaAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLinkedAaAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLinkedAaAccountsRequestMultiError, or nil if none found.
func (m *GetLinkedAaAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLinkedAaAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetLinkedAaAccountsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLinkedAaAccountsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLinkedAaAccountsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLinkedAaAccountsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLinkedAaAccountsRequestMultiError(errors)
	}

	return nil
}

// GetLinkedAaAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by GetLinkedAaAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLinkedAaAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLinkedAaAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLinkedAaAccountsRequestMultiError) AllErrors() []error { return m }

// GetLinkedAaAccountsRequestValidationError is the validation error returned
// by GetLinkedAaAccountsRequest.Validate if the designated constraints aren't met.
type GetLinkedAaAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLinkedAaAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLinkedAaAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLinkedAaAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLinkedAaAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLinkedAaAccountsRequestValidationError) ErrorName() string {
	return "GetLinkedAaAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLinkedAaAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLinkedAaAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLinkedAaAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLinkedAaAccountsRequestValidationError{}

// Validate checks the field values on GetLinkedAaAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLinkedAaAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLinkedAaAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLinkedAaAccountsResponseMultiError, or nil if none found.
func (m *GetLinkedAaAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLinkedAaAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLinkedAaAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLinkedAaAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLinkedAaAccountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLinkedAaAccountsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLinkedAaAccountsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLinkedAaAccountsResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLinkedAaAccountsResponseMultiError(errors)
	}

	return nil
}

// GetLinkedAaAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by GetLinkedAaAccountsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLinkedAaAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLinkedAaAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLinkedAaAccountsResponseMultiError) AllErrors() []error { return m }

// GetLinkedAaAccountsResponseValidationError is the validation error returned
// by GetLinkedAaAccountsResponse.Validate if the designated constraints
// aren't met.
type GetLinkedAaAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLinkedAaAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLinkedAaAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLinkedAaAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLinkedAaAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLinkedAaAccountsResponseValidationError) ErrorName() string {
	return "GetLinkedAaAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLinkedAaAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLinkedAaAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLinkedAaAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLinkedAaAccountsResponseValidationError{}

// Validate checks the field values on GetAccountDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountDetailsRequestMultiError, or nil if none found.
func (m *GetAccountDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetAccountDetailsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	if len(errors) > 0 {
		return GetAccountDetailsRequestMultiError(errors)
	}

	return nil
}

// GetAccountDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccountDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountDetailsRequestMultiError) AllErrors() []error { return m }

// GetAccountDetailsRequestValidationError is the validation error returned by
// GetAccountDetailsRequest.Validate if the designated constraints aren't met.
type GetAccountDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountDetailsRequestValidationError) ErrorName() string {
	return "GetAccountDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountDetailsRequestValidationError{}

// Validate checks the field values on GetAccountDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountDetailsResponseMultiError, or nil if none found.
func (m *GetAccountDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "AccountDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "AccountDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsResponseValidationError{
				field:  "AccountDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountActionOptionList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountDetailsResponseValidationError{
						field:  fmt.Sprintf("AccountActionOptionList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountDetailsResponseValidationError{
						field:  fmt.Sprintf("AccountActionOptionList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountDetailsResponseValidationError{
					field:  fmt.Sprintf("AccountActionOptionList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConsentRenewalPopup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "ConsentRenewalPopup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "ConsentRenewalPopup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentRenewalPopup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsResponseValidationError{
				field:  "ConsentRenewalPopup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "ActionBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "ActionBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsResponseValidationError{
				field:  "ActionBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountDetailsResponseMultiError(errors)
	}

	return nil
}

// GetAccountDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAccountDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountDetailsResponseMultiError) AllErrors() []error { return m }

// GetAccountDetailsResponseValidationError is the validation error returned by
// GetAccountDetailsResponse.Validate if the designated constraints aren't met.
type GetAccountDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountDetailsResponseValidationError) ErrorName() string {
	return "GetAccountDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountDetailsResponseValidationError{}

// Validate checks the field values on GetConnectedAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConnectedAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectedAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetConnectedAccountsRequestMultiError, or nil if none found.
func (m *GetConnectedAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectedAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetConnectedAccountsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConnectedAccountsRequestMultiError(errors)
	}

	return nil
}

// GetConnectedAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by GetConnectedAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetConnectedAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectedAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectedAccountsRequestMultiError) AllErrors() []error { return m }

// GetConnectedAccountsRequestValidationError is the validation error returned
// by GetConnectedAccountsRequest.Validate if the designated constraints
// aren't met.
type GetConnectedAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectedAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectedAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectedAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectedAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectedAccountsRequestValidationError) ErrorName() string {
	return "GetConnectedAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectedAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectedAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectedAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectedAccountsRequestValidationError{}

// Validate checks the field values on GetConnectedAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConnectedAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectedAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetConnectedAccountsResponseMultiError, or nil if none found.
func (m *GetConnectedAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectedAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountDetailList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetConnectedAccountsResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetConnectedAccountsResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetConnectedAccountsResponseValidationError{
					field:  fmt.Sprintf("AccountDetailList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetConnectedAccountsResponseMultiError(errors)
	}

	return nil
}

// GetConnectedAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by GetConnectedAccountsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetConnectedAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectedAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectedAccountsResponseMultiError) AllErrors() []error { return m }

// GetConnectedAccountsResponseValidationError is the validation error returned
// by GetConnectedAccountsResponse.Validate if the designated constraints
// aren't met.
type GetConnectedAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectedAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectedAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectedAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectedAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectedAccountsResponseValidationError) ErrorName() string {
	return "GetConnectedAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectedAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectedAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectedAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectedAccountsResponseValidationError{}

// Validate checks the field values on GetRelatedAccountsForDisconnectRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRelatedAccountsForDisconnectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRelatedAccountsForDisconnectRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRelatedAccountsForDisconnectRequestMultiError, or nil if none found.
func (m *GetRelatedAccountsForDisconnectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedAccountsForDisconnectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetRelatedAccountsForDisconnectRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedAccountsForDisconnectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedAccountsForDisconnectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedAccountsForDisconnectRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := GetRelatedAccountsForDisconnectRequestValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetRelatedAccountsForDisconnectRequestMultiError(errors)
	}

	return nil
}

// GetRelatedAccountsForDisconnectRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetRelatedAccountsForDisconnectRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedAccountsForDisconnectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedAccountsForDisconnectRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedAccountsForDisconnectRequestMultiError) AllErrors() []error { return m }

// GetRelatedAccountsForDisconnectRequestValidationError is the validation
// error returned by GetRelatedAccountsForDisconnectRequest.Validate if the
// designated constraints aren't met.
type GetRelatedAccountsForDisconnectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedAccountsForDisconnectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedAccountsForDisconnectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedAccountsForDisconnectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedAccountsForDisconnectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedAccountsForDisconnectRequestValidationError) ErrorName() string {
	return "GetRelatedAccountsForDisconnectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedAccountsForDisconnectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedAccountsForDisconnectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedAccountsForDisconnectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedAccountsForDisconnectRequestValidationError{}

// Validate checks the field values on GetRelatedAccountsForDisconnectResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRelatedAccountsForDisconnectResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRelatedAccountsForDisconnectResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRelatedAccountsForDisconnectResponseMultiError, or nil if none found.
func (m *GetRelatedAccountsForDisconnectResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedAccountsForDisconnectResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedAccountsForDisconnectResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedAccountsForDisconnectResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedAccountsForDisconnectResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountDetailList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRelatedAccountsForDisconnectResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRelatedAccountsForDisconnectResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRelatedAccountsForDisconnectResponseValidationError{
					field:  fmt.Sprintf("AccountDetailList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConfirmBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedAccountsForDisconnectResponseValidationError{
					field:  "ConfirmBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedAccountsForDisconnectResponseValidationError{
					field:  "ConfirmBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedAccountsForDisconnectResponseValidationError{
				field:  "ConfirmBottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRelatedAccountsForDisconnectResponseMultiError(errors)
	}

	return nil
}

// GetRelatedAccountsForDisconnectResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetRelatedAccountsForDisconnectResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedAccountsForDisconnectResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedAccountsForDisconnectResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedAccountsForDisconnectResponseMultiError) AllErrors() []error { return m }

// GetRelatedAccountsForDisconnectResponseValidationError is the validation
// error returned by GetRelatedAccountsForDisconnectResponse.Validate if the
// designated constraints aren't met.
type GetRelatedAccountsForDisconnectResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedAccountsForDisconnectResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedAccountsForDisconnectResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedAccountsForDisconnectResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedAccountsForDisconnectResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedAccountsForDisconnectResponseValidationError) ErrorName() string {
	return "GetRelatedAccountsForDisconnectResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedAccountsForDisconnectResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedAccountsForDisconnectResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedAccountsForDisconnectResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedAccountsForDisconnectResponseValidationError{}

// Validate checks the field values on GetRelatedAccountsForDeleteRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRelatedAccountsForDeleteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelatedAccountsForDeleteRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRelatedAccountsForDeleteRequestMultiError, or nil if none found.
func (m *GetRelatedAccountsForDeleteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedAccountsForDeleteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetRelatedAccountsForDeleteRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedAccountsForDeleteRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedAccountsForDeleteRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedAccountsForDeleteRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := GetRelatedAccountsForDeleteRequestValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetRelatedAccountsForDeleteRequestMultiError(errors)
	}

	return nil
}

// GetRelatedAccountsForDeleteRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetRelatedAccountsForDeleteRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedAccountsForDeleteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedAccountsForDeleteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedAccountsForDeleteRequestMultiError) AllErrors() []error { return m }

// GetRelatedAccountsForDeleteRequestValidationError is the validation error
// returned by GetRelatedAccountsForDeleteRequest.Validate if the designated
// constraints aren't met.
type GetRelatedAccountsForDeleteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedAccountsForDeleteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedAccountsForDeleteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedAccountsForDeleteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedAccountsForDeleteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedAccountsForDeleteRequestValidationError) ErrorName() string {
	return "GetRelatedAccountsForDeleteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedAccountsForDeleteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedAccountsForDeleteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedAccountsForDeleteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedAccountsForDeleteRequestValidationError{}

// Validate checks the field values on GetRelatedAccountsForDeleteResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRelatedAccountsForDeleteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelatedAccountsForDeleteResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRelatedAccountsForDeleteResponseMultiError, or nil if none found.
func (m *GetRelatedAccountsForDeleteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedAccountsForDeleteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedAccountsForDeleteResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedAccountsForDeleteResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedAccountsForDeleteResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountDetailList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRelatedAccountsForDeleteResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRelatedAccountsForDeleteResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRelatedAccountsForDeleteResponseValidationError{
					field:  fmt.Sprintf("AccountDetailList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConfirmBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedAccountsForDeleteResponseValidationError{
					field:  "ConfirmBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedAccountsForDeleteResponseValidationError{
					field:  "ConfirmBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedAccountsForDeleteResponseValidationError{
				field:  "ConfirmBottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRelatedAccountsForDeleteResponseMultiError(errors)
	}

	return nil
}

// GetRelatedAccountsForDeleteResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRelatedAccountsForDeleteResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedAccountsForDeleteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedAccountsForDeleteResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedAccountsForDeleteResponseMultiError) AllErrors() []error { return m }

// GetRelatedAccountsForDeleteResponseValidationError is the validation error
// returned by GetRelatedAccountsForDeleteResponse.Validate if the designated
// constraints aren't met.
type GetRelatedAccountsForDeleteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedAccountsForDeleteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedAccountsForDeleteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedAccountsForDeleteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedAccountsForDeleteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedAccountsForDeleteResponseValidationError) ErrorName() string {
	return "GetRelatedAccountsForDeleteResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedAccountsForDeleteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedAccountsForDeleteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedAccountsForDeleteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedAccountsForDeleteResponseValidationError{}

// Validate checks the field values on DeleteAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAccountRequestMultiError, or nil if none found.
func (m *DeleteAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := DeleteAccountRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteAccountRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	if len(errors) > 0 {
		return DeleteAccountRequestMultiError(errors)
	}

	return nil
}

// DeleteAccountRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAccountRequestMultiError) AllErrors() []error { return m }

// DeleteAccountRequestValidationError is the validation error returned by
// DeleteAccountRequest.Validate if the designated constraints aren't met.
type DeleteAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAccountRequestValidationError) ErrorName() string {
	return "DeleteAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAccountRequestValidationError{}

// Validate checks the field values on DeleteAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAccountResponseMultiError, or nil if none found.
func (m *DeleteAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteAccountResponseMultiError(errors)
	}

	return nil
}

// DeleteAccountResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAccountResponseMultiError) AllErrors() []error { return m }

// DeleteAccountResponseValidationError is the validation error returned by
// DeleteAccountResponse.Validate if the designated constraints aren't met.
type DeleteAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAccountResponseValidationError) ErrorName() string {
	return "DeleteAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAccountResponseValidationError{}

// Validate checks the field values on CheckReoobeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckReoobeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckReoobeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckReoobeRequestMultiError, or nil if none found.
func (m *CheckReoobeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckReoobeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := CheckReoobeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckReoobeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckReoobeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckReoobeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vua

	if len(errors) > 0 {
		return CheckReoobeRequestMultiError(errors)
	}

	return nil
}

// CheckReoobeRequestMultiError is an error wrapping multiple validation errors
// returned by CheckReoobeRequest.ValidateAll() if the designated constraints
// aren't met.
type CheckReoobeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckReoobeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckReoobeRequestMultiError) AllErrors() []error { return m }

// CheckReoobeRequestValidationError is the validation error returned by
// CheckReoobeRequest.Validate if the designated constraints aren't met.
type CheckReoobeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckReoobeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckReoobeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckReoobeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckReoobeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckReoobeRequestValidationError) ErrorName() string {
	return "CheckReoobeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckReoobeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckReoobeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckReoobeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckReoobeRequestValidationError{}

// Validate checks the field values on CheckReoobeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckReoobeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckReoobeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckReoobeResponseMultiError, or nil if none found.
func (m *CheckReoobeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckReoobeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckReoobeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckReoobeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckReoobeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountDetailList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckReoobeResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckReoobeResponseValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckReoobeResponseValidationError{
					field:  fmt.Sprintf("AccountDetailList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for OldVua

	if len(errors) > 0 {
		return CheckReoobeResponseMultiError(errors)
	}

	return nil
}

// CheckReoobeResponseMultiError is an error wrapping multiple validation
// errors returned by CheckReoobeResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckReoobeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckReoobeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckReoobeResponseMultiError) AllErrors() []error { return m }

// CheckReoobeResponseValidationError is the validation error returned by
// CheckReoobeResponse.Validate if the designated constraints aren't met.
type CheckReoobeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckReoobeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckReoobeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckReoobeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckReoobeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckReoobeResponseValidationError) ErrorName() string {
	return "CheckReoobeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckReoobeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckReoobeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckReoobeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckReoobeResponseValidationError{}

// Validate checks the field values on HandleReoobeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleReoobeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleReoobeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleReoobeRequestMultiError, or nil if none found.
func (m *HandleReoobeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleReoobeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := HandleReoobeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleReoobeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleReoobeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleReoobeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vua

	for idx, item := range m.GetAccountDetailList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HandleReoobeRequestValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HandleReoobeRequestValidationError{
						field:  fmt.Sprintf("AccountDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HandleReoobeRequestValidationError{
					field:  fmt.Sprintf("AccountDetailList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HandleReoobeRequestMultiError(errors)
	}

	return nil
}

// HandleReoobeRequestMultiError is an error wrapping multiple validation
// errors returned by HandleReoobeRequest.ValidateAll() if the designated
// constraints aren't met.
type HandleReoobeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleReoobeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleReoobeRequestMultiError) AllErrors() []error { return m }

// HandleReoobeRequestValidationError is the validation error returned by
// HandleReoobeRequest.Validate if the designated constraints aren't met.
type HandleReoobeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleReoobeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleReoobeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleReoobeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleReoobeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleReoobeRequestValidationError) ErrorName() string {
	return "HandleReoobeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleReoobeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleReoobeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleReoobeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleReoobeRequestValidationError{}

// Validate checks the field values on HandleReoobeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleReoobeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleReoobeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleReoobeResponseMultiError, or nil if none found.
func (m *HandleReoobeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleReoobeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleReoobeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleReoobeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleReoobeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HandleReoobeResponseMultiError(errors)
	}

	return nil
}

// HandleReoobeResponseMultiError is an error wrapping multiple validation
// errors returned by HandleReoobeResponse.ValidateAll() if the designated
// constraints aren't met.
type HandleReoobeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleReoobeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleReoobeResponseMultiError) AllErrors() []error { return m }

// HandleReoobeResponseValidationError is the validation error returned by
// HandleReoobeResponse.Validate if the designated constraints aren't met.
type HandleReoobeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleReoobeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleReoobeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleReoobeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleReoobeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleReoobeResponseValidationError) ErrorName() string {
	return "HandleReoobeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleReoobeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleReoobeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleReoobeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleReoobeResponseValidationError{}

// Validate checks the field values on Account with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Account with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AccountMultiError, or nil if none found.
func (m *Account) ValidateAll() error {
	return m.validate(true)
}

func (m *Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FiType

	// no validation rules for FipId

	// no validation rules for LinkedRefNumber

	// no validation rules for MaskedAccNumber

	// no validation rules for AccountId

	// no validation rules for FipLogoUrl

	// no validation rules for FipName

	if len(errors) > 0 {
		return AccountMultiError(errors)
	}

	return nil
}

// AccountMultiError is an error wrapping multiple validation errors returned
// by Account.ValidateAll() if the designated constraints aren't met.
type AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountMultiError) AllErrors() []error { return m }

// AccountValidationError is the validation error returned by Account.Validate
// if the designated constraints aren't met.
type AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountValidationError) ErrorName() string { return "AccountValidationError" }

// Error satisfies the builtin error interface
func (e AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountValidationError{}

// Validate checks the field values on GetAvailableFipsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAvailableFipsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAvailableFipsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAvailableFipsRequestMultiError, or nil if none found.
func (m *GetAvailableFipsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAvailableFipsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetAvailableFipsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAvailableFipsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAvailableFipsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAvailableFipsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAvailableFipsRequestMultiError(errors)
	}

	return nil
}

// GetAvailableFipsRequestMultiError is an error wrapping multiple validation
// errors returned by GetAvailableFipsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAvailableFipsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAvailableFipsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAvailableFipsRequestMultiError) AllErrors() []error { return m }

// GetAvailableFipsRequestValidationError is the validation error returned by
// GetAvailableFipsRequest.Validate if the designated constraints aren't met.
type GetAvailableFipsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAvailableFipsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAvailableFipsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAvailableFipsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAvailableFipsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAvailableFipsRequestValidationError) ErrorName() string {
	return "GetAvailableFipsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAvailableFipsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAvailableFipsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAvailableFipsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAvailableFipsRequestValidationError{}

// Validate checks the field values on GetAvailableFipsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAvailableFipsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAvailableFipsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAvailableFipsResponseMultiError, or nil if none found.
func (m *GetAvailableFipsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAvailableFipsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAvailableFipsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAvailableFipsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAvailableFipsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFipMetaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAvailableFipsResponseValidationError{
						field:  fmt.Sprintf("FipMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAvailableFipsResponseValidationError{
						field:  fmt.Sprintf("FipMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAvailableFipsResponseValidationError{
					field:  fmt.Sprintf("FipMetaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAvailableFipsResponseMultiError(errors)
	}

	return nil
}

// GetAvailableFipsResponseMultiError is an error wrapping multiple validation
// errors returned by GetAvailableFipsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAvailableFipsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAvailableFipsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAvailableFipsResponseMultiError) AllErrors() []error { return m }

// GetAvailableFipsResponseValidationError is the validation error returned by
// GetAvailableFipsResponse.Validate if the designated constraints aren't met.
type GetAvailableFipsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAvailableFipsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAvailableFipsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAvailableFipsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAvailableFipsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAvailableFipsResponseValidationError) ErrorName() string {
	return "GetAvailableFipsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAvailableFipsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAvailableFipsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAvailableFipsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAvailableFipsResponseValidationError{}

// Validate checks the field values on CreateBankPreferenceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBankPreferenceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBankPreferenceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBankPreferenceRequestMultiError, or nil if none found.
func (m *CreateBankPreferenceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBankPreferenceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := CreateBankPreferenceRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBankPreferenceRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBankPreferenceRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBankPreferenceRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetBankList()) < 1 {
		err := CreateBankPreferenceRequestValidationError{
			field:  "BankList",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateBankPreferenceRequestMultiError(errors)
	}

	return nil
}

// CreateBankPreferenceRequestMultiError is an error wrapping multiple
// validation errors returned by CreateBankPreferenceRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateBankPreferenceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBankPreferenceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBankPreferenceRequestMultiError) AllErrors() []error { return m }

// CreateBankPreferenceRequestValidationError is the validation error returned
// by CreateBankPreferenceRequest.Validate if the designated constraints
// aren't met.
type CreateBankPreferenceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBankPreferenceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBankPreferenceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBankPreferenceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBankPreferenceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBankPreferenceRequestValidationError) ErrorName() string {
	return "CreateBankPreferenceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBankPreferenceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBankPreferenceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBankPreferenceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBankPreferenceRequestValidationError{}

// Validate checks the field values on CreateBankPreferenceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBankPreferenceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBankPreferenceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBankPreferenceResponseMultiError, or nil if none found.
func (m *CreateBankPreferenceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBankPreferenceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBankPreferenceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBankPreferenceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBankPreferenceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateBankPreferenceResponseMultiError(errors)
	}

	return nil
}

// CreateBankPreferenceResponseMultiError is an error wrapping multiple
// validation errors returned by CreateBankPreferenceResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateBankPreferenceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBankPreferenceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBankPreferenceResponseMultiError) AllErrors() []error { return m }

// CreateBankPreferenceResponseValidationError is the validation error returned
// by CreateBankPreferenceResponse.Validate if the designated constraints
// aren't met.
type CreateBankPreferenceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBankPreferenceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBankPreferenceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBankPreferenceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBankPreferenceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBankPreferenceResponseValidationError) ErrorName() string {
	return "CreateBankPreferenceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBankPreferenceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBankPreferenceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBankPreferenceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBankPreferenceResponseValidationError{}

// Validate checks the field values on ResumeAccountSyncRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResumeAccountSyncRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResumeAccountSyncRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResumeAccountSyncRequestMultiError, or nil if none found.
func (m *ResumeAccountSyncRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResumeAccountSyncRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := ResumeAccountSyncRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResumeAccountSyncRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResumeAccountSyncRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResumeAccountSyncRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	if len(errors) > 0 {
		return ResumeAccountSyncRequestMultiError(errors)
	}

	return nil
}

// ResumeAccountSyncRequestMultiError is an error wrapping multiple validation
// errors returned by ResumeAccountSyncRequest.ValidateAll() if the designated
// constraints aren't met.
type ResumeAccountSyncRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResumeAccountSyncRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResumeAccountSyncRequestMultiError) AllErrors() []error { return m }

// ResumeAccountSyncRequestValidationError is the validation error returned by
// ResumeAccountSyncRequest.Validate if the designated constraints aren't met.
type ResumeAccountSyncRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResumeAccountSyncRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResumeAccountSyncRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResumeAccountSyncRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResumeAccountSyncRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResumeAccountSyncRequestValidationError) ErrorName() string {
	return "ResumeAccountSyncRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResumeAccountSyncRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResumeAccountSyncRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResumeAccountSyncRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResumeAccountSyncRequestValidationError{}

// Validate checks the field values on ResumeAccountSyncResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResumeAccountSyncResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResumeAccountSyncResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResumeAccountSyncResponseMultiError, or nil if none found.
func (m *ResumeAccountSyncResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResumeAccountSyncResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResumeAccountSyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResumeAccountSyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResumeAccountSyncResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResumeAccountSyncResponseMultiError(errors)
	}

	return nil
}

// ResumeAccountSyncResponseMultiError is an error wrapping multiple validation
// errors returned by ResumeAccountSyncResponse.ValidateAll() if the
// designated constraints aren't met.
type ResumeAccountSyncResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResumeAccountSyncResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResumeAccountSyncResponseMultiError) AllErrors() []error { return m }

// ResumeAccountSyncResponseValidationError is the validation error returned by
// ResumeAccountSyncResponse.Validate if the designated constraints aren't met.
type ResumeAccountSyncResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResumeAccountSyncResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResumeAccountSyncResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResumeAccountSyncResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResumeAccountSyncResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResumeAccountSyncResponseValidationError) ErrorName() string {
	return "ResumeAccountSyncResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResumeAccountSyncResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResumeAccountSyncResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResumeAccountSyncResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResumeAccountSyncResponseValidationError{}

// Validate checks the field values on
// GetDataPullStatusFromConsentHandleRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDataPullStatusFromConsentHandleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDataPullStatusFromConsentHandleRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDataPullStatusFromConsentHandleRequestMultiError, or nil if none found.
func (m *GetDataPullStatusFromConsentHandleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataPullStatusFromConsentHandleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetDataPullStatusFromConsentHandleRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataPullStatusFromConsentHandleRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataPullStatusFromConsentHandleRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataPullStatusFromConsentHandleRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentHandle

	// no validation rules for CurrentPollCount

	if len(m.GetConsentHandleList()) > 30 {
		err := GetDataPullStatusFromConsentHandleRequestValidationError{
			field:  "ConsentHandleList",
			reason: "value must contain no more than 30 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ConnectionFlowId

	if len(errors) > 0 {
		return GetDataPullStatusFromConsentHandleRequestMultiError(errors)
	}

	return nil
}

// GetDataPullStatusFromConsentHandleRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetDataPullStatusFromConsentHandleRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDataPullStatusFromConsentHandleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataPullStatusFromConsentHandleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataPullStatusFromConsentHandleRequestMultiError) AllErrors() []error { return m }

// GetDataPullStatusFromConsentHandleRequestValidationError is the validation
// error returned by GetDataPullStatusFromConsentHandleRequest.Validate if the
// designated constraints aren't met.
type GetDataPullStatusFromConsentHandleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataPullStatusFromConsentHandleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataPullStatusFromConsentHandleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataPullStatusFromConsentHandleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataPullStatusFromConsentHandleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataPullStatusFromConsentHandleRequestValidationError) ErrorName() string {
	return "GetDataPullStatusFromConsentHandleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataPullStatusFromConsentHandleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataPullStatusFromConsentHandleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataPullStatusFromConsentHandleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataPullStatusFromConsentHandleRequestValidationError{}

// Validate checks the field values on
// GetDataPullStatusFromConsentHandleResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDataPullStatusFromConsentHandleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDataPullStatusFromConsentHandleResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDataPullStatusFromConsentHandleResponseMultiError, or nil if none found.
func (m *GetDataPullStatusFromConsentHandleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataPullStatusFromConsentHandleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataPullStatusFromConsentHandleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataPullStatusFromConsentHandleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataPullStatusFromConsentHandleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DataPullStatus

	// no validation rules for NextPollSeconds

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataPullStatusFromConsentHandleResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataPullStatusFromConsentHandleResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataPullStatusFromConsentHandleResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxAllowedAttempts

	// no validation rules for CurrentPollCount

	if len(errors) > 0 {
		return GetDataPullStatusFromConsentHandleResponseMultiError(errors)
	}

	return nil
}

// GetDataPullStatusFromConsentHandleResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetDataPullStatusFromConsentHandleResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDataPullStatusFromConsentHandleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataPullStatusFromConsentHandleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataPullStatusFromConsentHandleResponseMultiError) AllErrors() []error { return m }

// GetDataPullStatusFromConsentHandleResponseValidationError is the validation
// error returned by GetDataPullStatusFromConsentHandleResponse.Validate if
// the designated constraints aren't met.
type GetDataPullStatusFromConsentHandleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataPullStatusFromConsentHandleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataPullStatusFromConsentHandleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataPullStatusFromConsentHandleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataPullStatusFromConsentHandleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataPullStatusFromConsentHandleResponseValidationError) ErrorName() string {
	return "GetDataPullStatusFromConsentHandleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataPullStatusFromConsentHandleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataPullStatusFromConsentHandleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataPullStatusFromConsentHandleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataPullStatusFromConsentHandleResponseValidationError{}

// Validate checks the field values on GetAllowedConfigRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllowedConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllowedConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllowedConfigRequestMultiError, or nil if none found.
func (m *GetAllowedConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllowedConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetAllowedConfigRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedConfigRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedConfigRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedConfigRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaFlowName

	if len(errors) > 0 {
		return GetAllowedConfigRequestMultiError(errors)
	}

	return nil
}

// GetAllowedConfigRequestMultiError is an error wrapping multiple validation
// errors returned by GetAllowedConfigRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAllowedConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllowedConfigRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllowedConfigRequestMultiError) AllErrors() []error { return m }

// GetAllowedConfigRequestValidationError is the validation error returned by
// GetAllowedConfigRequest.Validate if the designated constraints aren't met.
type GetAllowedConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllowedConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllowedConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllowedConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllowedConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllowedConfigRequestValidationError) ErrorName() string {
	return "GetAllowedConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllowedConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllowedConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllowedConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllowedConfigRequestValidationError{}

// Validate checks the field values on GetAllowedConfigResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllowedConfigResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllowedConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllowedConfigResponseMultiError, or nil if none found.
func (m *GetAllowedConfigResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllowedConfigResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedConfigResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFipMetaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("FipMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("FipMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllowedConfigResponseValidationError{
					field:  fmt.Sprintf("FipMetaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AccountDiscoveryTitleText

	// no validation rules for AccountDiscoverySubtitleText

	// no validation rules for FinvuAccountDiscoveryTimeoutSeconds

	// no validation rules for OnemoneyAccountDiscoveryTimeoutSeconds

	if all {
		switch v := interface{}(m.GetV2FlowParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "V2FlowParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "V2FlowParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetV2FlowParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedConfigResponseValidationError{
				field:  "V2FlowParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UseFinvuAsyncDiscovery

	for idx, item := range m.GetAaEntityMetaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("AaEntityMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("AaEntityMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllowedConfigResponseValidationError{
					field:  fmt.Sprintf("AaEntityMetaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetNoAccountsDiscoveredTextParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "NoAccountsDiscoveredTextParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "NoAccountsDiscoveredTextParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNoAccountsDiscoveredTextParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedConfigResponseValidationError{
				field:  "NoAccountsDiscoveredTextParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetAccountsDiscoveryIdentifiers()))
		i := 0
		for key := range m.GetAccountsDiscoveryIdentifiers() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAccountsDiscoveryIdentifiers()[key]
			_ = val

			// no validation rules for AccountsDiscoveryIdentifiers[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetAllowedConfigResponseValidationError{
							field:  fmt.Sprintf("AccountsDiscoveryIdentifiers[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetAllowedConfigResponseValidationError{
							field:  fmt.Sprintf("AccountsDiscoveryIdentifiers[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("AccountsDiscoveryIdentifiers[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetSdkFlowParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "SdkFlowParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllowedConfigResponseValidationError{
					field:  "SdkFlowParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSdkFlowParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllowedConfigResponseValidationError{
				field:  "SdkFlowParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetGroupedFipMetaDataByTypes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("GroupedFipMetaDataByTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllowedConfigResponseValidationError{
						field:  fmt.Sprintf("GroupedFipMetaDataByTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllowedConfigResponseValidationError{
					field:  fmt.Sprintf("GroupedFipMetaDataByTypes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AutoReadTimeoutAllDiscoveredFipsOtp

	if len(errors) > 0 {
		return GetAllowedConfigResponseMultiError(errors)
	}

	return nil
}

// GetAllowedConfigResponseMultiError is an error wrapping multiple validation
// errors returned by GetAllowedConfigResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAllowedConfigResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllowedConfigResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllowedConfigResponseMultiError) AllErrors() []error { return m }

// GetAllowedConfigResponseValidationError is the validation error returned by
// GetAllowedConfigResponse.Validate if the designated constraints aren't met.
type GetAllowedConfigResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllowedConfigResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllowedConfigResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllowedConfigResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllowedConfigResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllowedConfigResponseValidationError) ErrorName() string {
	return "GetAllowedConfigResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllowedConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllowedConfigResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllowedConfigResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllowedConfigResponseValidationError{}

// Validate checks the field values on AccountDiscoveryIdentifiers with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountDiscoveryIdentifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountDiscoveryIdentifiers with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountDiscoveryIdentifiersMultiError, or nil if none found.
func (m *AccountDiscoveryIdentifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountDiscoveryIdentifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccDiscoveryIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountDiscoveryIdentifiersValidationError{
						field:  fmt.Sprintf("AccDiscoveryIdentifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountDiscoveryIdentifiersValidationError{
						field:  fmt.Sprintf("AccDiscoveryIdentifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountDiscoveryIdentifiersValidationError{
					field:  fmt.Sprintf("AccDiscoveryIdentifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountDiscoveryIdentifiersMultiError(errors)
	}

	return nil
}

// AccountDiscoveryIdentifiersMultiError is an error wrapping multiple
// validation errors returned by AccountDiscoveryIdentifiers.ValidateAll() if
// the designated constraints aren't met.
type AccountDiscoveryIdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDiscoveryIdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDiscoveryIdentifiersMultiError) AllErrors() []error { return m }

// AccountDiscoveryIdentifiersValidationError is the validation error returned
// by AccountDiscoveryIdentifiers.Validate if the designated constraints
// aren't met.
type AccountDiscoveryIdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDiscoveryIdentifiersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountDiscoveryIdentifiersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountDiscoveryIdentifiersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountDiscoveryIdentifiersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDiscoveryIdentifiersValidationError) ErrorName() string {
	return "AccountDiscoveryIdentifiersValidationError"
}

// Error satisfies the builtin error interface
func (e AccountDiscoveryIdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountDiscoveryIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDiscoveryIdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDiscoveryIdentifiersValidationError{}

// Validate checks the field values on NoAccountsDiscoveredTextParams with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoAccountsDiscoveredTextParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoAccountsDiscoveredTextParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NoAccountsDiscoveredTextParamsMultiError, or nil if none found.
func (m *NoAccountsDiscoveredTextParams) ValidateAll() error {
	return m.validate(true)
}

func (m *NoAccountsDiscoveredTextParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TitleText

	// no validation rules for SubTitleText

	// no validation rules for FipIcon

	// no validation rules for FipNotDiscoveryText

	// no validation rules for ProceedDeeplinkText

	if len(errors) > 0 {
		return NoAccountsDiscoveredTextParamsMultiError(errors)
	}

	return nil
}

// NoAccountsDiscoveredTextParamsMultiError is an error wrapping multiple
// validation errors returned by NoAccountsDiscoveredTextParams.ValidateAll()
// if the designated constraints aren't met.
type NoAccountsDiscoveredTextParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoAccountsDiscoveredTextParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoAccountsDiscoveredTextParamsMultiError) AllErrors() []error { return m }

// NoAccountsDiscoveredTextParamsValidationError is the validation error
// returned by NoAccountsDiscoveredTextParams.Validate if the designated
// constraints aren't met.
type NoAccountsDiscoveredTextParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoAccountsDiscoveredTextParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoAccountsDiscoveredTextParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoAccountsDiscoveredTextParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoAccountsDiscoveredTextParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoAccountsDiscoveredTextParamsValidationError) ErrorName() string {
	return "NoAccountsDiscoveredTextParamsValidationError"
}

// Error satisfies the builtin error interface
func (e NoAccountsDiscoveredTextParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoAccountsDiscoveredTextParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoAccountsDiscoveredTextParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoAccountsDiscoveredTextParamsValidationError{}

// Validate checks the field values on V2FlowParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *V2FlowParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on V2FlowParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in V2FlowParamsMultiError, or
// nil if none found.
func (m *V2FlowParams) ValidateAll() error {
	return m.validate(true)
}

func (m *V2FlowParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountDiscoveryTitleText

	// no validation rules for AccountDiscoverySubtitleText

	// no validation rules for CtaText

	// no validation rules for AccountDiscoverySubtitleSearchingText

	// no validation rules for AccountDiscoveryLoadingText

	// no validation rules for CantSeeYourAccountsText

	if len(errors) > 0 {
		return V2FlowParamsMultiError(errors)
	}

	return nil
}

// V2FlowParamsMultiError is an error wrapping multiple validation errors
// returned by V2FlowParams.ValidateAll() if the designated constraints aren't met.
type V2FlowParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m V2FlowParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m V2FlowParamsMultiError) AllErrors() []error { return m }

// V2FlowParamsValidationError is the validation error returned by
// V2FlowParams.Validate if the designated constraints aren't met.
type V2FlowParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e V2FlowParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e V2FlowParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e V2FlowParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e V2FlowParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e V2FlowParamsValidationError) ErrorName() string { return "V2FlowParamsValidationError" }

// Error satisfies the builtin error interface
func (e V2FlowParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sV2FlowParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = V2FlowParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = V2FlowParamsValidationError{}

// Validate checks the field values on SDKUiFlowParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SDKUiFlowParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SDKUiFlowParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SDKUiFlowParamsMultiError, or nil if none found.
func (m *SDKUiFlowParams) ValidateAll() error {
	return m.validate(true)
}

func (m *SDKUiFlowParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FlowHeader

	// no validation rules for ProgressBarStatus

	// no validation rules for DisplayProgressBar

	// no validation rules for AccountDiscoverySearchingText

	// no validation rules for ConnectDiscoveredAccountsCtaText

	// no validation rules for RetryCtaText

	// no validation rules for SkipFailedAccountsCtaText

	// no validation rules for NoAccountsFoundTitle

	// no validation rules for NoAccountsFoundSubTitle

	// no validation rules for NoAccountFoundCtaText

	// no validation rules for NoAccountsFoundIconUrl

	// no validation rules for DisconnectAccountText

	if len(errors) > 0 {
		return SDKUiFlowParamsMultiError(errors)
	}

	return nil
}

// SDKUiFlowParamsMultiError is an error wrapping multiple validation errors
// returned by SDKUiFlowParams.ValidateAll() if the designated constraints
// aren't met.
type SDKUiFlowParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SDKUiFlowParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SDKUiFlowParamsMultiError) AllErrors() []error { return m }

// SDKUiFlowParamsValidationError is the validation error returned by
// SDKUiFlowParams.Validate if the designated constraints aren't met.
type SDKUiFlowParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SDKUiFlowParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SDKUiFlowParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SDKUiFlowParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SDKUiFlowParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SDKUiFlowParamsValidationError) ErrorName() string { return "SDKUiFlowParamsValidationError" }

// Error satisfies the builtin error interface
func (e SDKUiFlowParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSDKUiFlowParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SDKUiFlowParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SDKUiFlowParamsValidationError{}

// Validate checks the field values on GroupedFipMetaDataByTypes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GroupedFipMetaDataByTypes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GroupedFipMetaDataByTypes with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GroupedFipMetaDataByTypesMultiError, or nil if none found.
func (m *GroupedFipMetaDataByTypes) ValidateAll() error {
	return m.validate(true)
}

func (m *GroupedFipMetaDataByTypes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupName

	// no validation rules for EntityType

	for idx, item := range m.GetFipMeta() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GroupedFipMetaDataByTypesValidationError{
						field:  fmt.Sprintf("FipMeta[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GroupedFipMetaDataByTypesValidationError{
						field:  fmt.Sprintf("FipMeta[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GroupedFipMetaDataByTypesValidationError{
					field:  fmt.Sprintf("FipMeta[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GroupedFipMetaDataByTypesMultiError(errors)
	}

	return nil
}

// GroupedFipMetaDataByTypesMultiError is an error wrapping multiple validation
// errors returned by GroupedFipMetaDataByTypes.ValidateAll() if the
// designated constraints aren't met.
type GroupedFipMetaDataByTypesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GroupedFipMetaDataByTypesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GroupedFipMetaDataByTypesMultiError) AllErrors() []error { return m }

// GroupedFipMetaDataByTypesValidationError is the validation error returned by
// GroupedFipMetaDataByTypes.Validate if the designated constraints aren't met.
type GroupedFipMetaDataByTypesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GroupedFipMetaDataByTypesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GroupedFipMetaDataByTypesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GroupedFipMetaDataByTypesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GroupedFipMetaDataByTypesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GroupedFipMetaDataByTypesValidationError) ErrorName() string {
	return "GroupedFipMetaDataByTypesValidationError"
}

// Error satisfies the builtin error interface
func (e GroupedFipMetaDataByTypesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGroupedFipMetaDataByTypes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GroupedFipMetaDataByTypesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GroupedFipMetaDataByTypesValidationError{}

// Validate checks the field values on GetHomeSummaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHomeSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHomeSummaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHomeSummaryRequestMultiError, or nil if none found.
func (m *GetHomeSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHomeSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetHomeSummaryRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeSummaryRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeSummaryRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeSummaryRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHomeSummaryRequestMultiError(errors)
	}

	return nil
}

// GetHomeSummaryRequestMultiError is an error wrapping multiple validation
// errors returned by GetHomeSummaryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetHomeSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHomeSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHomeSummaryRequestMultiError) AllErrors() []error { return m }

// GetHomeSummaryRequestValidationError is the validation error returned by
// GetHomeSummaryRequest.Validate if the designated constraints aren't met.
type GetHomeSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHomeSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHomeSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHomeSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHomeSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHomeSummaryRequestValidationError) ErrorName() string {
	return "GetHomeSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetHomeSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHomeSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHomeSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHomeSummaryRequestValidationError{}

// Validate checks the field values on GetHomeSummaryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHomeSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHomeSummaryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHomeSummaryResponseMultiError, or nil if none found.
func (m *GetHomeSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHomeSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeSummaryResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCollectiveBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "CollectiveBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "CollectiveBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollectiveBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeSummaryResponseValidationError{
				field:  "CollectiveBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHomeAccountTileList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetHomeSummaryResponseValidationError{
						field:  fmt.Sprintf("HomeAccountTileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetHomeSummaryResponseValidationError{
						field:  fmt.Sprintf("HomeAccountTileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetHomeSummaryResponseValidationError{
					field:  fmt.Sprintf("HomeAccountTileList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPopup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "Popup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "Popup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPopup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeSummaryResponseValidationError{
				field:  "Popup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRenewalCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "RenewalCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "RenewalCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRenewalCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeSummaryResponseValidationError{
				field:  "RenewalCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentRenewalPopup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "ConsentRenewalPopup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHomeSummaryResponseValidationError{
					field:  "ConsentRenewalPopup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentRenewalPopup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHomeSummaryResponseValidationError{
				field:  "ConsentRenewalPopup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHomeSummaryResponseMultiError(errors)
	}

	return nil
}

// GetHomeSummaryResponseMultiError is an error wrapping multiple validation
// errors returned by GetHomeSummaryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetHomeSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHomeSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHomeSummaryResponseMultiError) AllErrors() []error { return m }

// GetHomeSummaryResponseValidationError is the validation error returned by
// GetHomeSummaryResponse.Validate if the designated constraints aren't met.
type GetHomeSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHomeSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHomeSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHomeSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHomeSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHomeSummaryResponseValidationError) ErrorName() string {
	return "GetHomeSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetHomeSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHomeSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHomeSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHomeSummaryResponseValidationError{}

// Validate checks the field values on GetLandingPageOnConnectRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingPageOnConnectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingPageOnConnectRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLandingPageOnConnectRequestMultiError, or nil if none found.
func (m *GetLandingPageOnConnectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingPageOnConnectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetLandingPageOnConnectRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageOnConnectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageOnConnectRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageOnConnectRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaFlowName

	// no validation rules for CaFlowId

	if len(errors) > 0 {
		return GetLandingPageOnConnectRequestMultiError(errors)
	}

	return nil
}

// GetLandingPageOnConnectRequestMultiError is an error wrapping multiple
// validation errors returned by GetLandingPageOnConnectRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLandingPageOnConnectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingPageOnConnectRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingPageOnConnectRequestMultiError) AllErrors() []error { return m }

// GetLandingPageOnConnectRequestValidationError is the validation error
// returned by GetLandingPageOnConnectRequest.Validate if the designated
// constraints aren't met.
type GetLandingPageOnConnectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingPageOnConnectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingPageOnConnectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingPageOnConnectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingPageOnConnectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingPageOnConnectRequestValidationError) ErrorName() string {
	return "GetLandingPageOnConnectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingPageOnConnectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingPageOnConnectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingPageOnConnectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingPageOnConnectRequestValidationError{}

// Validate checks the field values on GetLandingPageOnConnectResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingPageOnConnectResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingPageOnConnectResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLandingPageOnConnectResponseMultiError, or nil if none found.
func (m *GetLandingPageOnConnectResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingPageOnConnectResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageOnConnectResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageOnConnectResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageOnConnectResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageOnConnectResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageOnConnectResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageOnConnectResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNewDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageOnConnectResponseValidationError{
					field:  "NewDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageOnConnectResponseValidationError{
					field:  "NewDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageOnConnectResponseValidationError{
				field:  "NewDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingPageOnConnectResponseMultiError(errors)
	}

	return nil
}

// GetLandingPageOnConnectResponseMultiError is an error wrapping multiple
// validation errors returned by GetLandingPageOnConnectResponse.ValidateAll()
// if the designated constraints aren't met.
type GetLandingPageOnConnectResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingPageOnConnectResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingPageOnConnectResponseMultiError) AllErrors() []error { return m }

// GetLandingPageOnConnectResponseValidationError is the validation error
// returned by GetLandingPageOnConnectResponse.Validate if the designated
// constraints aren't met.
type GetLandingPageOnConnectResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingPageOnConnectResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingPageOnConnectResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingPageOnConnectResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingPageOnConnectResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingPageOnConnectResponseValidationError) ErrorName() string {
	return "GetLandingPageOnConnectResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingPageOnConnectResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingPageOnConnectResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingPageOnConnectResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingPageOnConnectResponseValidationError{}

// Validate checks the field values on ConnectAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConnectAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnectAccountRequestMultiError, or nil if none found.
func (m *ConnectAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := ConnectAccountRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectAccountRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AaEntity

	if len(errors) > 0 {
		return ConnectAccountRequestMultiError(errors)
	}

	return nil
}

// ConnectAccountRequestMultiError is an error wrapping multiple validation
// errors returned by ConnectAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type ConnectAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectAccountRequestMultiError) AllErrors() []error { return m }

// ConnectAccountRequestValidationError is the validation error returned by
// ConnectAccountRequest.Validate if the designated constraints aren't met.
type ConnectAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectAccountRequestValidationError) ErrorName() string {
	return "ConnectAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectAccountRequestValidationError{}

// Validate checks the field values on ConnectAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConnectAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnectAccountResponseMultiError, or nil if none found.
func (m *ConnectAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectAccountResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectAccountResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectAccountResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectAccountResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectAccountResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectAccountResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConnectAccountResponseMultiError(errors)
	}

	return nil
}

// ConnectAccountResponseMultiError is an error wrapping multiple validation
// errors returned by ConnectAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type ConnectAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectAccountResponseMultiError) AllErrors() []error { return m }

// ConnectAccountResponseValidationError is the validation error returned by
// ConnectAccountResponse.Validate if the designated constraints aren't met.
type ConnectAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectAccountResponseValidationError) ErrorName() string {
	return "ConnectAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectAccountResponseValidationError{}

// Validate checks the field values on GetConnectedAccountEntryPointsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetConnectedAccountEntryPointsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectedAccountEntryPointsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetConnectedAccountEntryPointsRequestMultiError, or nil if none found.
func (m *GetConnectedAccountEntryPointsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectedAccountEntryPointsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetConnectedAccountEntryPointsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountEntryPointsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountEntryPointsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountEntryPointsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConnectedAccountEntryPointsRequestMultiError(errors)
	}

	return nil
}

// GetConnectedAccountEntryPointsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetConnectedAccountEntryPointsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetConnectedAccountEntryPointsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectedAccountEntryPointsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectedAccountEntryPointsRequestMultiError) AllErrors() []error { return m }

// GetConnectedAccountEntryPointsRequestValidationError is the validation error
// returned by GetConnectedAccountEntryPointsRequest.Validate if the
// designated constraints aren't met.
type GetConnectedAccountEntryPointsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectedAccountEntryPointsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectedAccountEntryPointsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectedAccountEntryPointsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectedAccountEntryPointsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectedAccountEntryPointsRequestValidationError) ErrorName() string {
	return "GetConnectedAccountEntryPointsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectedAccountEntryPointsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectedAccountEntryPointsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectedAccountEntryPointsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectedAccountEntryPointsRequestValidationError{}

// Validate checks the field values on GetConnectedAccountEntryPointsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetConnectedAccountEntryPointsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetConnectedAccountEntryPointsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetConnectedAccountEntryPointsResponseMultiError, or nil if none found.
func (m *GetConnectedAccountEntryPointsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectedAccountEntryPointsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountEntryPointsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountEntryPointsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountEntryPointsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetEntryPointMap()))
		i := 0
		for key := range m.GetEntryPointMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetEntryPointMap()[key]
			_ = val

			// no validation rules for EntryPointMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetConnectedAccountEntryPointsResponseValidationError{
							field:  fmt.Sprintf("EntryPointMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetConnectedAccountEntryPointsResponseValidationError{
							field:  fmt.Sprintf("EntryPointMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetConnectedAccountEntryPointsResponseValidationError{
						field:  fmt.Sprintf("EntryPointMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for IsConnectedAccountEnabled

	if len(errors) > 0 {
		return GetConnectedAccountEntryPointsResponseMultiError(errors)
	}

	return nil
}

// GetConnectedAccountEntryPointsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetConnectedAccountEntryPointsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetConnectedAccountEntryPointsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectedAccountEntryPointsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectedAccountEntryPointsResponseMultiError) AllErrors() []error { return m }

// GetConnectedAccountEntryPointsResponseValidationError is the validation
// error returned by GetConnectedAccountEntryPointsResponse.Validate if the
// designated constraints aren't met.
type GetConnectedAccountEntryPointsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectedAccountEntryPointsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectedAccountEntryPointsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectedAccountEntryPointsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectedAccountEntryPointsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectedAccountEntryPointsResponseValidationError) ErrorName() string {
	return "GetConnectedAccountEntryPointsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectedAccountEntryPointsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectedAccountEntryPointsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectedAccountEntryPointsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectedAccountEntryPointsResponseValidationError{}

// Validate checks the field values on GetAuthTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthTokenRequestMultiError, or nil if none found.
func (m *GetAuthTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetAuthTokenRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthTokenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthTokenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthTokenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AaEntity

	if len(errors) > 0 {
		return GetAuthTokenRequestMultiError(errors)
	}

	return nil
}

// GetAuthTokenRequestMultiError is an error wrapping multiple validation
// errors returned by GetAuthTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAuthTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthTokenRequestMultiError) AllErrors() []error { return m }

// GetAuthTokenRequestValidationError is the validation error returned by
// GetAuthTokenRequest.Validate if the designated constraints aren't met.
type GetAuthTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthTokenRequestValidationError) ErrorName() string {
	return "GetAuthTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthTokenRequestValidationError{}

// Validate checks the field values on GetAuthTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthTokenResponseMultiError, or nil if none found.
func (m *GetAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthTokenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthTokenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthTokenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for ExpiryDurationMinutes

	if len(errors) > 0 {
		return GetAuthTokenResponseMultiError(errors)
	}

	return nil
}

// GetAuthTokenResponseMultiError is an error wrapping multiple validation
// errors returned by GetAuthTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthTokenResponseMultiError) AllErrors() []error { return m }

// GetAuthTokenResponseValidationError is the validation error returned by
// GetAuthTokenResponse.Validate if the designated constraints aren't met.
type GetAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthTokenResponseValidationError) ErrorName() string {
	return "GetAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthTokenResponseValidationError{}

// Validate checks the field values on
// GetConnectedAccountsSummaryForHomeRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetConnectedAccountsSummaryForHomeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetConnectedAccountsSummaryForHomeRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetConnectedAccountsSummaryForHomeRequestMultiError, or nil if none found.
func (m *GetConnectedAccountsSummaryForHomeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectedAccountsSummaryForHomeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetConnectedAccountsSummaryForHomeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountsSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountsSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountsSummaryForHomeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DashboardVersion

	// no validation rules for ZeroStateDashboardVariant

	if len(errors) > 0 {
		return GetConnectedAccountsSummaryForHomeRequestMultiError(errors)
	}

	return nil
}

// GetConnectedAccountsSummaryForHomeRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetConnectedAccountsSummaryForHomeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetConnectedAccountsSummaryForHomeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectedAccountsSummaryForHomeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectedAccountsSummaryForHomeRequestMultiError) AllErrors() []error { return m }

// GetConnectedAccountsSummaryForHomeRequestValidationError is the validation
// error returned by GetConnectedAccountsSummaryForHomeRequest.Validate if the
// designated constraints aren't met.
type GetConnectedAccountsSummaryForHomeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectedAccountsSummaryForHomeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectedAccountsSummaryForHomeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectedAccountsSummaryForHomeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectedAccountsSummaryForHomeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectedAccountsSummaryForHomeRequestValidationError) ErrorName() string {
	return "GetConnectedAccountsSummaryForHomeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectedAccountsSummaryForHomeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectedAccountsSummaryForHomeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectedAccountsSummaryForHomeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectedAccountsSummaryForHomeRequestValidationError{}

// Validate checks the field values on
// GetConnectedAccountsSummaryForHomeResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetConnectedAccountsSummaryForHomeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetConnectedAccountsSummaryForHomeResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetConnectedAccountsSummaryForHomeResponseMultiError, or nil if none found.
func (m *GetConnectedAccountsSummaryForHomeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectedAccountsSummaryForHomeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountsSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountsSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountsSummaryForHomeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectedAccountsSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectedAccountsSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectedAccountsSummaryForHomeResponseValidationError{
				field:  "DashboardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConnectedAccountsSummaryForHomeResponseMultiError(errors)
	}

	return nil
}

// GetConnectedAccountsSummaryForHomeResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetConnectedAccountsSummaryForHomeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetConnectedAccountsSummaryForHomeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectedAccountsSummaryForHomeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectedAccountsSummaryForHomeResponseMultiError) AllErrors() []error { return m }

// GetConnectedAccountsSummaryForHomeResponseValidationError is the validation
// error returned by GetConnectedAccountsSummaryForHomeResponse.Validate if
// the designated constraints aren't met.
type GetConnectedAccountsSummaryForHomeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectedAccountsSummaryForHomeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectedAccountsSummaryForHomeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectedAccountsSummaryForHomeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectedAccountsSummaryForHomeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectedAccountsSummaryForHomeResponseValidationError) ErrorName() string {
	return "GetConnectedAccountsSummaryForHomeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectedAccountsSummaryForHomeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectedAccountsSummaryForHomeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectedAccountsSummaryForHomeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectedAccountsSummaryForHomeResponseValidationError{}

// Validate checks the field values on GetSdkExitDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSdkExitDeeplinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSdkExitDeeplinkRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSdkExitDeeplinkRequestMultiError, or nil if none found.
func (m *GetSdkExitDeeplinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSdkExitDeeplinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetSdkExitDeeplinkRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSdkExitDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSdkExitDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSdkExitDeeplinkRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExitOperation

	// no validation rules for ExitReason

	// no validation rules for CaFlowName

	// no validation rules for AaEntity

	// no validation rules for ConnectionFlowId

	if len(errors) > 0 {
		return GetSdkExitDeeplinkRequestMultiError(errors)
	}

	return nil
}

// GetSdkExitDeeplinkRequestMultiError is an error wrapping multiple validation
// errors returned by GetSdkExitDeeplinkRequest.ValidateAll() if the
// designated constraints aren't met.
type GetSdkExitDeeplinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSdkExitDeeplinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSdkExitDeeplinkRequestMultiError) AllErrors() []error { return m }

// GetSdkExitDeeplinkRequestValidationError is the validation error returned by
// GetSdkExitDeeplinkRequest.Validate if the designated constraints aren't met.
type GetSdkExitDeeplinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSdkExitDeeplinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSdkExitDeeplinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSdkExitDeeplinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSdkExitDeeplinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSdkExitDeeplinkRequestValidationError) ErrorName() string {
	return "GetSdkExitDeeplinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSdkExitDeeplinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSdkExitDeeplinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSdkExitDeeplinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSdkExitDeeplinkRequestValidationError{}

// Validate checks the field values on GetSdkExitDeeplinkResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSdkExitDeeplinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSdkExitDeeplinkResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSdkExitDeeplinkResponseMultiError, or nil if none found.
func (m *GetSdkExitDeeplinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSdkExitDeeplinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSdkExitDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSdkExitDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSdkExitDeeplinkResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExitScreenDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSdkExitDeeplinkResponseValidationError{
					field:  "ExitScreenDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSdkExitDeeplinkResponseValidationError{
					field:  "ExitScreenDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitScreenDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSdkExitDeeplinkResponseValidationError{
				field:  "ExitScreenDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSdkExitDeeplinkResponseMultiError(errors)
	}

	return nil
}

// GetSdkExitDeeplinkResponseMultiError is an error wrapping multiple
// validation errors returned by GetSdkExitDeeplinkResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSdkExitDeeplinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSdkExitDeeplinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSdkExitDeeplinkResponseMultiError) AllErrors() []error { return m }

// GetSdkExitDeeplinkResponseValidationError is the validation error returned
// by GetSdkExitDeeplinkResponse.Validate if the designated constraints aren't met.
type GetSdkExitDeeplinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSdkExitDeeplinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSdkExitDeeplinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSdkExitDeeplinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSdkExitDeeplinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSdkExitDeeplinkResponseValidationError) ErrorName() string {
	return "GetSdkExitDeeplinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSdkExitDeeplinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSdkExitDeeplinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSdkExitDeeplinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSdkExitDeeplinkResponseValidationError{}

// Validate checks the field values on BalanceDashboardRenewalPopupOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BalanceDashboardRenewalPopupOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BalanceDashboardRenewalPopupOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BalanceDashboardRenewalPopupOptionsMultiError, or nil if none found.
func (m *BalanceDashboardRenewalPopupOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *BalanceDashboardRenewalPopupOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShowPopup

	if all {
		switch v := interface{}(m.GetHeaderIconUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "HeaderIconUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "HeaderIconUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderIconUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDashboardRenewalPopupOptionsValidationError{
				field:  "HeaderIconUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDashboardRenewalPopupOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDashboardRenewalPopupOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDashboardRenewalPopupOptionsValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
						field:  fmt.Sprintf("CtaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
						field:  fmt.Sprintf("CtaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BalanceDashboardRenewalPopupOptionsValidationError{
					field:  fmt.Sprintf("CtaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDashboardRenewalPopupOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDismissPopupDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "DismissPopupDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDashboardRenewalPopupOptionsValidationError{
					field:  "DismissPopupDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDismissPopupDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDashboardRenewalPopupOptionsValidationError{
				field:  "DismissPopupDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BalanceDashboardRenewalPopupOptionsMultiError(errors)
	}

	return nil
}

// BalanceDashboardRenewalPopupOptionsMultiError is an error wrapping multiple
// validation errors returned by
// BalanceDashboardRenewalPopupOptions.ValidateAll() if the designated
// constraints aren't met.
type BalanceDashboardRenewalPopupOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BalanceDashboardRenewalPopupOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BalanceDashboardRenewalPopupOptionsMultiError) AllErrors() []error { return m }

// BalanceDashboardRenewalPopupOptionsValidationError is the validation error
// returned by BalanceDashboardRenewalPopupOptions.Validate if the designated
// constraints aren't met.
type BalanceDashboardRenewalPopupOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BalanceDashboardRenewalPopupOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BalanceDashboardRenewalPopupOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BalanceDashboardRenewalPopupOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BalanceDashboardRenewalPopupOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BalanceDashboardRenewalPopupOptionsValidationError) ErrorName() string {
	return "BalanceDashboardRenewalPopupOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e BalanceDashboardRenewalPopupOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBalanceDashboardRenewalPopupOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BalanceDashboardRenewalPopupOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BalanceDashboardRenewalPopupOptionsValidationError{}

// Validate checks the field values on GetLandingPageForRenewalRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingPageForRenewalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingPageForRenewalRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLandingPageForRenewalRequestMultiError, or nil if none found.
func (m *GetLandingPageForRenewalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingPageForRenewalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetLandingPageForRenewalRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageForRenewalRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageForRenewalRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageForRenewalRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingPageForRenewalRequestMultiError(errors)
	}

	return nil
}

// GetLandingPageForRenewalRequestMultiError is an error wrapping multiple
// validation errors returned by GetLandingPageForRenewalRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLandingPageForRenewalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingPageForRenewalRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingPageForRenewalRequestMultiError) AllErrors() []error { return m }

// GetLandingPageForRenewalRequestValidationError is the validation error
// returned by GetLandingPageForRenewalRequest.Validate if the designated
// constraints aren't met.
type GetLandingPageForRenewalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingPageForRenewalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingPageForRenewalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingPageForRenewalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingPageForRenewalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingPageForRenewalRequestValidationError) ErrorName() string {
	return "GetLandingPageForRenewalRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingPageForRenewalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingPageForRenewalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingPageForRenewalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingPageForRenewalRequestValidationError{}

// Validate checks the field values on GetLandingPageForRenewalResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLandingPageForRenewalResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingPageForRenewalResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLandingPageForRenewalResponseMultiError, or nil if none found.
func (m *GetLandingPageForRenewalResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingPageForRenewalResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageForRenewalResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageForRenewalResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageForRenewalResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRenewalDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingPageForRenewalResponseValidationError{
					field:  "RenewalDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingPageForRenewalResponseValidationError{
					field:  "RenewalDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRenewalDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingPageForRenewalResponseValidationError{
				field:  "RenewalDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingPageForRenewalResponseMultiError(errors)
	}

	return nil
}

// GetLandingPageForRenewalResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLandingPageForRenewalResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLandingPageForRenewalResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingPageForRenewalResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingPageForRenewalResponseMultiError) AllErrors() []error { return m }

// GetLandingPageForRenewalResponseValidationError is the validation error
// returned by GetLandingPageForRenewalResponse.Validate if the designated
// constraints aren't met.
type GetLandingPageForRenewalResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingPageForRenewalResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingPageForRenewalResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingPageForRenewalResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingPageForRenewalResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingPageForRenewalResponseValidationError) ErrorName() string {
	return "GetLandingPageForRenewalResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingPageForRenewalResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingPageForRenewalResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingPageForRenewalResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingPageForRenewalResponseValidationError{}

// Validate checks the field values on GetConsentHandleStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConsentHandleStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConsentHandleStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetConsentHandleStatusRequestMultiError, or nil if none found.
func (m *GetConsentHandleStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConsentHandleStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetConsentHandleStatusRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentHandleStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentHandleStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentHandleStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetConsentHandle()) < 1 {
		err := GetConsentHandleStatusRequestValidationError{
			field:  "ConsentHandle",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CurrentPollCount

	// no validation rules for ConnectionFlowId

	if len(errors) > 0 {
		return GetConsentHandleStatusRequestMultiError(errors)
	}

	return nil
}

// GetConsentHandleStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetConsentHandleStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetConsentHandleStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConsentHandleStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConsentHandleStatusRequestMultiError) AllErrors() []error { return m }

// GetConsentHandleStatusRequestValidationError is the validation error
// returned by GetConsentHandleStatusRequest.Validate if the designated
// constraints aren't met.
type GetConsentHandleStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConsentHandleStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConsentHandleStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConsentHandleStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConsentHandleStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConsentHandleStatusRequestValidationError) ErrorName() string {
	return "GetConsentHandleStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConsentHandleStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConsentHandleStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConsentHandleStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConsentHandleStatusRequestValidationError{}

// Validate checks the field values on GetConsentHandleStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConsentHandleStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConsentHandleStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetConsentHandleStatusResponseMultiError, or nil if none found.
func (m *GetConsentHandleStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConsentHandleStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentHandleStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentHandleStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentHandleStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentHandleStatus

	if all {
		switch v := interface{}(m.GetNextPollDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentHandleStatusResponseValidationError{
					field:  "NextPollDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentHandleStatusResponseValidationError{
					field:  "NextPollDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextPollDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentHandleStatusResponseValidationError{
				field:  "NextPollDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConsentHandleStatusResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConsentHandleStatusResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConsentHandleStatusResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentPollCount

	if len(errors) > 0 {
		return GetConsentHandleStatusResponseMultiError(errors)
	}

	return nil
}

// GetConsentHandleStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetConsentHandleStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetConsentHandleStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConsentHandleStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConsentHandleStatusResponseMultiError) AllErrors() []error { return m }

// GetConsentHandleStatusResponseValidationError is the validation error
// returned by GetConsentHandleStatusResponse.Validate if the designated
// constraints aren't met.
type GetConsentHandleStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConsentHandleStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConsentHandleStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConsentHandleStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConsentHandleStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConsentHandleStatusResponseValidationError) ErrorName() string {
	return "GetConsentHandleStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConsentHandleStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConsentHandleStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConsentHandleStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConsentHandleStatusResponseValidationError{}

// Validate checks the field values on GetAllDepositAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllDepositAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllDepositAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllDepositAccountsRequestMultiError, or nil if none found.
func (m *GetAllDepositAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllDepositAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetAllDepositAccountsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDepositAccountsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDepositAccountsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDepositAccountsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllDepositAccountsRequestMultiError(errors)
	}

	return nil
}

// GetAllDepositAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllDepositAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAllDepositAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllDepositAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllDepositAccountsRequestMultiError) AllErrors() []error { return m }

// GetAllDepositAccountsRequestValidationError is the validation error returned
// by GetAllDepositAccountsRequest.Validate if the designated constraints
// aren't met.
type GetAllDepositAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllDepositAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllDepositAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllDepositAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllDepositAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllDepositAccountsRequestValidationError) ErrorName() string {
	return "GetAllDepositAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllDepositAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllDepositAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllDepositAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllDepositAccountsRequestValidationError{}

// Validate checks the field values on GetAllDepositAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllDepositAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllDepositAccountsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllDepositAccountsResponseMultiError, or nil if none found.
func (m *GetAllDepositAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllDepositAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDepositAccountsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDepositAccountsSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllDepositAccountsResponseValidationError{
						field:  fmt.Sprintf("DepositAccountsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllDepositAccountsResponseValidationError{
						field:  fmt.Sprintf("DepositAccountsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllDepositAccountsResponseValidationError{
					field:  fmt.Sprintf("DepositAccountsSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTotalBalanceText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "TotalBalanceText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "TotalBalanceText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBalanceText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDepositAccountsResponseValidationError{
				field:  "TotalBalanceText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "TotalBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "TotalBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDepositAccountsResponseValidationError{
				field:  "TotalBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConnectDepositAccCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "ConnectDepositAccCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "ConnectDepositAccCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectDepositAccCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDepositAccountsResponseValidationError{
				field:  "ConnectDepositAccCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "BottomBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDepositAccountsResponseValidationError{
					field:  "BottomBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDepositAccountsResponseValidationError{
				field:  "BottomBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllDepositAccountsResponseMultiError(errors)
	}

	return nil
}

// GetAllDepositAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllDepositAccountsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAllDepositAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllDepositAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllDepositAccountsResponseMultiError) AllErrors() []error { return m }

// GetAllDepositAccountsResponseValidationError is the validation error
// returned by GetAllDepositAccountsResponse.Validate if the designated
// constraints aren't met.
type GetAllDepositAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllDepositAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllDepositAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllDepositAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllDepositAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllDepositAccountsResponseValidationError) ErrorName() string {
	return "GetAllDepositAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllDepositAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllDepositAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllDepositAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllDepositAccountsResponseValidationError{}

// Validate checks the field values on DepositAccountsSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DepositAccountsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositAccountsSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DepositAccountsSectionMultiError, or nil if none found.
func (m *DepositAccountsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositAccountsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositAccountsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositAccountsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositAccountsSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	for idx, item := range m.GetHomeAccountTileList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DepositAccountsSectionValidationError{
						field:  fmt.Sprintf("HomeAccountTileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DepositAccountsSectionValidationError{
						field:  fmt.Sprintf("HomeAccountTileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DepositAccountsSectionValidationError{
					field:  fmt.Sprintf("HomeAccountTileList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInfoCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositAccountsSectionValidationError{
					field:  "InfoCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositAccountsSectionValidationError{
					field:  "InfoCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositAccountsSectionValidationError{
				field:  "InfoCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DepositAccountsSectionMultiError(errors)
	}

	return nil
}

// DepositAccountsSectionMultiError is an error wrapping multiple validation
// errors returned by DepositAccountsSection.ValidateAll() if the designated
// constraints aren't met.
type DepositAccountsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositAccountsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositAccountsSectionMultiError) AllErrors() []error { return m }

// DepositAccountsSectionValidationError is the validation error returned by
// DepositAccountsSection.Validate if the designated constraints aren't met.
type DepositAccountsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositAccountsSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositAccountsSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositAccountsSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositAccountsSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositAccountsSectionValidationError) ErrorName() string {
	return "DepositAccountsSectionValidationError"
}

// Error satisfies the builtin error interface
func (e DepositAccountsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositAccountsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositAccountsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositAccountsSectionValidationError{}

// Validate checks the field values on GetBenefitScreenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBenefitScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBenefitScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBenefitScreenRequestMultiError, or nil if none found.
func (m *GetBenefitScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBenefitScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetBenefitScreenRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBenefitScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBenefitScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBenefitScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaFlowName

	// no validation rules for CaFlowId

	if len(errors) > 0 {
		return GetBenefitScreenRequestMultiError(errors)
	}

	return nil
}

// GetBenefitScreenRequestMultiError is an error wrapping multiple validation
// errors returned by GetBenefitScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBenefitScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBenefitScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBenefitScreenRequestMultiError) AllErrors() []error { return m }

// GetBenefitScreenRequestValidationError is the validation error returned by
// GetBenefitScreenRequest.Validate if the designated constraints aren't met.
type GetBenefitScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBenefitScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBenefitScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBenefitScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBenefitScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBenefitScreenRequestValidationError) ErrorName() string {
	return "GetBenefitScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBenefitScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBenefitScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBenefitScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBenefitScreenRequestValidationError{}

// Validate checks the field values on GetBenefitScreenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBenefitScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBenefitScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBenefitScreenResponseMultiError, or nil if none found.
func (m *GetBenefitScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBenefitScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBenefitScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBenefitScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBenefitScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBenefitScreenResponseValidationError{
					field:  "Screen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBenefitScreenResponseValidationError{
					field:  "Screen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBenefitScreenResponseValidationError{
				field:  "Screen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBenefitScreenResponseMultiError(errors)
	}

	return nil
}

// GetBenefitScreenResponseMultiError is an error wrapping multiple validation
// errors returned by GetBenefitScreenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBenefitScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBenefitScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBenefitScreenResponseMultiError) AllErrors() []error { return m }

// GetBenefitScreenResponseValidationError is the validation error returned by
// GetBenefitScreenResponse.Validate if the designated constraints aren't met.
type GetBenefitScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBenefitScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBenefitScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBenefitScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBenefitScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBenefitScreenResponseValidationError) ErrorName() string {
	return "GetBenefitScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBenefitScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBenefitScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBenefitScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBenefitScreenResponseValidationError{}

// Validate checks the field values on GetBanksRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetBanksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBanksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBanksRequestMultiError, or nil if none found.
func (m *GetBanksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBanksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetBanksRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaFlowName

	// no validation rules for CaFlowId

	if len(errors) > 0 {
		return GetBanksRequestMultiError(errors)
	}

	return nil
}

// GetBanksRequestMultiError is an error wrapping multiple validation errors
// returned by GetBanksRequest.ValidateAll() if the designated constraints
// aren't met.
type GetBanksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBanksRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBanksRequestMultiError) AllErrors() []error { return m }

// GetBanksRequestValidationError is the validation error returned by
// GetBanksRequest.Validate if the designated constraints aren't met.
type GetBanksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBanksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBanksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBanksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBanksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBanksRequestValidationError) ErrorName() string { return "GetBanksRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetBanksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBanksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBanksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBanksRequestValidationError{}

// Validate checks the field values on GetBanksResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetBanksResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBanksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBanksResponseMultiError, or nil if none found.
func (m *GetBanksResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBanksResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPopularBankListTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "PopularBankListTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "PopularBankListTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPopularBankListTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponseValidationError{
				field:  "PopularBankListTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPopularBankList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBanksResponseValidationError{
						field:  fmt.Sprintf("PopularBankList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBanksResponseValidationError{
						field:  fmt.Sprintf("PopularBankList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBanksResponseValidationError{
					field:  fmt.Sprintf("PopularBankList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAllBankListTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "AllBankListTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "AllBankListTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllBankListTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponseValidationError{
				field:  "AllBankListTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAllBankList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBanksResponseValidationError{
						field:  fmt.Sprintf("AllBankList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBanksResponseValidationError{
						field:  fmt.Sprintf("AllBankList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBanksResponseValidationError{
					field:  fmt.Sprintf("AllBankList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetFipIssue()))
		i := 0
		for key := range m.GetFipIssue() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFipIssue()[key]
			_ = val

			// no validation rules for FipIssue[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetBanksResponseValidationError{
							field:  fmt.Sprintf("FipIssue[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetBanksResponseValidationError{
							field:  fmt.Sprintf("FipIssue[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetBanksResponseValidationError{
						field:  fmt.Sprintf("FipIssue[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetDefaultIssueMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "DefaultIssueMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "DefaultIssueMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultIssueMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponseValidationError{
				field:  "DefaultIssueMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinvuTnc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "FinvuTnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "FinvuTnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinvuTnc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponseValidationError{
				field:  "FinvuTnc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOnemoneyTnc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "OnemoneyTnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponseValidationError{
					field:  "OnemoneyTnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnemoneyTnc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponseValidationError{
				field:  "OnemoneyTnc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBanksResponseMultiError(errors)
	}

	return nil
}

// GetBanksResponseMultiError is an error wrapping multiple validation errors
// returned by GetBanksResponse.ValidateAll() if the designated constraints
// aren't met.
type GetBanksResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBanksResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBanksResponseMultiError) AllErrors() []error { return m }

// GetBanksResponseValidationError is the validation error returned by
// GetBanksResponse.Validate if the designated constraints aren't met.
type GetBanksResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBanksResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBanksResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBanksResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBanksResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBanksResponseValidationError) ErrorName() string { return "GetBanksResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetBanksResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBanksResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBanksResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBanksResponseValidationError{}

// Validate checks the field values on GetSdkDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSdkDeeplinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSdkDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSdkDeeplinkRequestMultiError, or nil if none found.
func (m *GetSdkDeeplinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSdkDeeplinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetSdkDeeplinkRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSdkDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSdkDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSdkDeeplinkRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CaFlowName

	// no validation rules for CaFlowId

	// no validation rules for FipId

	if len(errors) > 0 {
		return GetSdkDeeplinkRequestMultiError(errors)
	}

	return nil
}

// GetSdkDeeplinkRequestMultiError is an error wrapping multiple validation
// errors returned by GetSdkDeeplinkRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSdkDeeplinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSdkDeeplinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSdkDeeplinkRequestMultiError) AllErrors() []error { return m }

// GetSdkDeeplinkRequestValidationError is the validation error returned by
// GetSdkDeeplinkRequest.Validate if the designated constraints aren't met.
type GetSdkDeeplinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSdkDeeplinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSdkDeeplinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSdkDeeplinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSdkDeeplinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSdkDeeplinkRequestValidationError) ErrorName() string {
	return "GetSdkDeeplinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSdkDeeplinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSdkDeeplinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSdkDeeplinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSdkDeeplinkRequestValidationError{}

// Validate checks the field values on GetSdkDeeplinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSdkDeeplinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSdkDeeplinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSdkDeeplinkResponseMultiError, or nil if none found.
func (m *GetSdkDeeplinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSdkDeeplinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSdkDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSdkDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSdkDeeplinkResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSdkDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSdkDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSdkDeeplinkResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSdkDeeplinkResponseMultiError(errors)
	}

	return nil
}

// GetSdkDeeplinkResponseMultiError is an error wrapping multiple validation
// errors returned by GetSdkDeeplinkResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSdkDeeplinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSdkDeeplinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSdkDeeplinkResponseMultiError) AllErrors() []error { return m }

// GetSdkDeeplinkResponseValidationError is the validation error returned by
// GetSdkDeeplinkResponse.Validate if the designated constraints aren't met.
type GetSdkDeeplinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSdkDeeplinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSdkDeeplinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSdkDeeplinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSdkDeeplinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSdkDeeplinkResponseValidationError) ErrorName() string {
	return "GetSdkDeeplinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSdkDeeplinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSdkDeeplinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSdkDeeplinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSdkDeeplinkResponseValidationError{}

// Validate checks the field values on
// AccountDiscoveryIdentifiers_AccDiscoveryIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AccountDiscoveryIdentifiers_AccDiscoveryIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// AccountDiscoveryIdentifiers_AccDiscoveryIdentifierMultiError, or nil if
// none found.
func (m *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountDiscoveryIdentifiers_AccDiscoveryIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Category

	// no validation rules for Value

	// no validation rules for Type

	if len(errors) > 0 {
		return AccountDiscoveryIdentifiers_AccDiscoveryIdentifierMultiError(errors)
	}

	return nil
}

// AccountDiscoveryIdentifiers_AccDiscoveryIdentifierMultiError is an error
// wrapping multiple validation errors returned by
// AccountDiscoveryIdentifiers_AccDiscoveryIdentifier.ValidateAll() if the
// designated constraints aren't met.
type AccountDiscoveryIdentifiers_AccDiscoveryIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDiscoveryIdentifiers_AccDiscoveryIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDiscoveryIdentifiers_AccDiscoveryIdentifierMultiError) AllErrors() []error { return m }

// AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError is the
// validation error returned by
// AccountDiscoveryIdentifiers_AccDiscoveryIdentifier.Validate if the
// designated constraints aren't met.
type AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError) ErrorName() string {
	return "AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountDiscoveryIdentifiers_AccDiscoveryIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDiscoveryIdentifiers_AccDiscoveryIdentifierValidationError{}

// Validate checks the field values on GetBanksResponse_BankInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBanksResponse_BankInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBanksResponse_BankInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBanksResponse_BankInfoMultiError, or nil if none found.
func (m *GetBanksResponse_BankInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBanksResponse_BankInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FipId

	if all {
		switch v := interface{}(m.GetDisplayName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponse_BankInfoValidationError{
					field:  "DisplayName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponse_BankInfoValidationError{
					field:  "DisplayName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponse_BankInfoValidationError{
				field:  "DisplayName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLogoUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponse_BankInfoValidationError{
					field:  "LogoUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponse_BankInfoValidationError{
					field:  "LogoUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLogoUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponse_BankInfoValidationError{
				field:  "LogoUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Issue

	// no validation rules for AaEntity

	if len(errors) > 0 {
		return GetBanksResponse_BankInfoMultiError(errors)
	}

	return nil
}

// GetBanksResponse_BankInfoMultiError is an error wrapping multiple validation
// errors returned by GetBanksResponse_BankInfo.ValidateAll() if the
// designated constraints aren't met.
type GetBanksResponse_BankInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBanksResponse_BankInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBanksResponse_BankInfoMultiError) AllErrors() []error { return m }

// GetBanksResponse_BankInfoValidationError is the validation error returned by
// GetBanksResponse_BankInfo.Validate if the designated constraints aren't met.
type GetBanksResponse_BankInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBanksResponse_BankInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBanksResponse_BankInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBanksResponse_BankInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBanksResponse_BankInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBanksResponse_BankInfoValidationError) ErrorName() string {
	return "GetBanksResponse_BankInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetBanksResponse_BankInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBanksResponse_BankInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBanksResponse_BankInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBanksResponse_BankInfoValidationError{}

// Validate checks the field values on GetBanksResponse_IssueMessage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBanksResponse_IssueMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBanksResponse_IssueMessage with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBanksResponse_IssueMessageMultiError, or nil if none found.
func (m *GetBanksResponse_IssueMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBanksResponse_IssueMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponse_IssueMessageValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponse_IssueMessageValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponse_IssueMessageValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBanksResponse_IssueMessageValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBanksResponse_IssueMessageValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBanksResponse_IssueMessageValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBanksResponse_IssueMessageMultiError(errors)
	}

	return nil
}

// GetBanksResponse_IssueMessageMultiError is an error wrapping multiple
// validation errors returned by GetBanksResponse_IssueMessage.ValidateAll()
// if the designated constraints aren't met.
type GetBanksResponse_IssueMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBanksResponse_IssueMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBanksResponse_IssueMessageMultiError) AllErrors() []error { return m }

// GetBanksResponse_IssueMessageValidationError is the validation error
// returned by GetBanksResponse_IssueMessage.Validate if the designated
// constraints aren't met.
type GetBanksResponse_IssueMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBanksResponse_IssueMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBanksResponse_IssueMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBanksResponse_IssueMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBanksResponse_IssueMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBanksResponse_IssueMessageValidationError) ErrorName() string {
	return "GetBanksResponse_IssueMessageValidationError"
}

// Error satisfies the builtin error interface
func (e GetBanksResponse_IssueMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBanksResponse_IssueMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBanksResponse_IssueMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBanksResponse_IssueMessageValidationError{}
