syntax = "proto3";

package frontend.insights.networth;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/home/<USER>";
import "api/frontend/home/<USER>/components.proto";
import "api/frontend/insights/networth/asset_dashboard.proto";
import "api/frontend/insights/networth/magic_import.proto";
import "api/frontend/insights/networth/manual_form.proto";
import "api/frontend/insights/networth/ui/ui.proto";
import "api/frontend/insights/secrets/secret_summary.proto";
import "api/frontend/investment/ui/landing_page.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/assetandanalysis/screen_options.proto";
import "api/typesv2/deeplink_screen_option/insights/networth_refresh/net_worth_refresh.proto";
import "api/typesv2/deeplink_screen_option/magicimport/networth_magic_import_screen_options.proto";
import "api/typesv2/file/file.proto";
import "api/typesv2/manual_asset_form_idenitifer.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/networth";
option java_package = "com.github.epifi.gamma.api.frontend.insights.networth";

service NetWorth {
  // GetNetWorthDashboard rpc will get all the details for net worth dashboard.
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5591&mode=design&t=AwPidyMULqEY9jbe-0
  rpc GetNetWorthDashboard (GetNetWorthDashboardRequest) returns (GetNetWorthDashboardResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Use to manually record a Deposit created by user on some other platform.
  // One use case for this is to calculate the net worth of the user.
  // https://drive.google.com/file/d/1KQI6qM3FMttsoIrHpPsRF49F7CqTw8I0/view?usp=drive_link
  rpc DepositDeclaration (DepositDeclarationRequest) returns (DepositDeclarationResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetManualFormConfig (GetManualFormConfigRequest) returns (GetManualFormConfigResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc SubmitManualForm (SubmitManualFormRequest) returns (SubmitManualFormResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetManualAssetDashboard gets dashboard for a particular asset based on type
  rpc GetManualAssetDashboard (GetManualAssetDashboardRequest) returns (GetManualAssetDashboardResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc DeleteManualAsset (DeleteManualAssetRequest) returns (DeleteManualAssetResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC provides Net Worth(Savings Account, Assets, Loans & Liabilities) of all connected accounts
  // Used in Home screen for rendering the Net Worth card
  // Figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50184&mode=design&t=3DhsGDfnbVVKGstY-0
  // OK : success
  // INTERNAL : internal error
  rpc GetNetWorthSummaryForHome (GetNetWorthSummaryForHomeRequest) returns (GetNetWorthSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC used for orchestrating Net worth refresh flows.
  // This rpc is to be called by the client on receiving the deeplink 'NET_WORTH_REFRESH_GET_NEXT_ACTION' and then redirect
  // to the deeplink provided in the response
  rpc GetNextNetWorthRefreshAction (GetNextNetWorthRefreshActionRequest) returns (GetNextNetWorthRefreshActionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC used for updating all manual assets provided by the user
  // This rpc is to be called by the client on receiving the deeplink 'NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS' and then redirect
  // to the deeplink provided in the response
  rpc UpdateManualAssets (UpdateManualAssetsRequest) returns (UpdateManualAssetsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SearchAssetFieldOptions returns a list of relevant options for a search-text and a field name in the form to add a manual asset,
  // e.g., for a list of SEBI-registered portfolio managers, i.e., companies for the field "AMC Name" in PMS.
  rpc SearchAssetFormFieldOptions (SearchAssetFormFieldOptionsRequest) returns (SearchAssetFormFieldOptionsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  //Rpc to provide credit score details in dashboard card
  //https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
  rpc GetCreditScoreSummaryForHome (GetCreditScoreSummaryForHomeRequest) returns (GetCreditScoreSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  //Rpc to provide EPF details in dashboard card
  //https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
  rpc GetEpfSummaryForHome (GetEpfSummaryForHomeRequest) returns (GetEpfSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  //Rpc to provide MF details in dashboard card
  //https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4963-41902&t=K6m5KdnunF2JI7fB-1
  rpc GetMfSummaryForHome (GetMfSummaryForHomeRequest) returns (GetMfSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Rpc to provide the wealth builder landing page components like dashboard and analysis card
  rpc GetWealthBuilderLandingPage (GetWealthBuilderLandingPageRequest) returns (GetWealthBuilderLandingPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSecretImportFlowStatus returns the polling status for secret import flow
  rpc GetAssetImportFlowStatus (GetAssetImportFlowStatusRequest) returns (GetAssetImportFlowStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetConnectMoreAssetsScreen to get the screen for connecting more assets and liabilities
  // This is only used for widget generation for non-connected assets and liabilities
  // generates widgets for assets and liabilities in request of api
  // figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7351-45880&t=pxg8lOjhsfTmCMR2-4
  // revamped figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=15731-22183&t=7kO8lb4vTxCWsGma-4
  rpc GetConnectMoreAssetsScreen (GetConnectMoreAssetsScreenRequest) returns (GetConnectMoreAssetsScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetWealthBuilderDashboardComponent to provide all details for wealth builder landing component
  // figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46845&t=pxg8lOjhsfTmCMR2-4
  rpc GetWealthBuilderDashboardComponent (GetWealthBuilderDashboardComponentRequest) returns (GetWealthBuilderDashboardComponentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc MagicImportFiles (MagicImportFilesRequest) returns (MagicImportFilesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc SubmitManualForms (SubmitManualFormsRequest) returns (SubmitManualFormsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetMagicImportIdeas returns a SDUI section which has ideas on user can use Magic import feature
  // e.g: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=qmTzSq6nX4YOgW1k-0
  rpc GetMagicImportIdeas (GetMagicImportIdeasRequest) returns (GetMagicImportIdeasResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
  // mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history.
  // Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
  rpc GetNetworthDataFile (GetNetworthDataFileRequest) returns (GetNetworthDataFileResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetMagicImportIdeasRequest {
  frontend.header.RequestHeader req = 1;
}

message GetMagicImportIdeasResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // The widget content, rendered by Server driven UI
  api.typesv2.ui.sdui.sections.Section section = 2;
}

message MagicImportFilesRequest {
  frontend.header.RequestHeader req = 1;
  repeated api.typesv2.common.file.File files = 2;
}

message MagicImportFilesResponse {
  enum Status {
    OK = 0;
    // MALFORMED_LLM_RESPONSE indicates that the response from LLM is not in expected format
    MALFORMED_LLM_RESPONSE = 101;
    // NO_ASSETS_FOUND indicates that no assets were found in the uploaded files
    NO_ASSETS_FOUND = 102;
  }
  frontend.header.ResponseHeader resp_header = 1;
  MagicImportedAssetsListScreen imported_assets_list_screen = 2;
  // This is to handle cases where we want to share an error screen after scanning images.
  // For example, in case of 0 assets found, we want to show an error screen with a retry option
  api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen error_state_screen = 3;
}

message DeleteManualAssetRequest {
  frontend.header.RequestHeader req = 1;
  // external_id of asset to be deleted for actor
  string external_id = 2 [(validate.rules).string.min_len = 1];
}

message DeleteManualAssetResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetManualAssetDashboardRequest {
  frontend.header.RequestHeader req = 1;
  // This is referred from api/insights/networth/service.proto AssetType (converted to string)
  string asset_type = 2 [(validate.rules).string.min_len = 1];
}

message GetManualAssetDashboardResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.insights.networth.NetWorthAssetDashboard dashboard = 2;
}

message SubmitManualFormRequest {
  // 4 maps to consent_ids
  reserved 4;
  frontend.header.RequestHeader req = 1;
  api.typesv2.ManualAssetFormIdentifier form_identifier = 2;
  repeated frontend.insights.networth.NetWorthManualInputData form_data = 3;
}

message SubmitManualFormResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetManualFormConfigRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.ManualAssetFormIdentifier form_identifier = 2;
}

message GetManualFormConfigResponse {
  // 3 maps to consents
  reserved 3;
  frontend.header.ResponseHeader resp_header = 1;
  frontend.insights.networth.NetWorthManualForm form_response = 2;
}

message DepositDeclarationRequest {
  frontend.header.RequestHeader req = 1;
  // Amount invested in the deposit.
  api.typesv2.Money amount = 2 [(validate.rules).message.required = true];
  // Interest rate for the deposit.
  double interest_rate = 3 [(validate.rules).double = {gt: 0, lt: 100}];
  // Creation date timestamp for the deposit.
  google.protobuf.Timestamp start_date = 4 [(validate.rules).timestamp.required = true];
  // Closure date timestamp for the deposit.
  google.protobuf.Timestamp end_date = 5 [(validate.rules).timestamp.required = true];
  // Type of Deposit.
  ManualAddDepositType deposit_type = 6 [(validate.rules).enum = {not_in: [0]}];
  // name of deposit
  string deposit_name = 7 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

// Identifier for type of Deposit while manually adding a deposit.
enum ManualAddDepositType {
  DEPOSIT_TYPE_UNSPECIFIED = 0;
  // Fixed Deposit.
  FD = 1;
  // Recurring deposits.
  RECURRING = 2;
}

message DepositDeclarationResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetNetWorthDashboardRequest {
  frontend.header.RequestHeader req = 1;
  // dashboard_type is sent by backend through screen options
  string dashboard_type = 2;
}

message GetNetWorthDashboardResponse {
  enum Status {
    Ok = 0;
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // Page title
  string title = 5;
  // banner to show in case net worth assets need to refreshed
  // location for this is fixed to be above the 'sections' field
  // Ref: https://drive.google.com/file/d/1bBfd_cXfNFUlMHwF2OoX95HCKUW9nRhw/view?usp=drive_link
  api.typesv2.ui.IconTextComponent refresh_banner = 6;
  // Visualisation will be used to give the total net worth of the user
  NetWorthVisualisation visualisation = 2;
  // Sections will have a list of widgets giving details about the assets, liabilities etc.
  repeated Section sections = 3 [deprecated = true];
  // footer to show at the bottom of the page
  api.typesv2.common.Text footer = 4;
  // list of components
  repeated Component components = 7;
  // this cta will always be placed above the refresh_banner
  // this cta will only be shown for dash_board_type assets
  // Ref: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39671&t=xBx5ulnmaQR9chb6-4
  api.typesv2.ui.IconTextComponent connect_assets_cta = 8;
}

// Component represent a ui component spanning the width of screen in the net worth dashboard
message Component {
  oneof component {
    Section section = 1;
    SecretsSection secrets_section = 2;
  }
}

// SecretsSection displays the secrets available for user in networth screen
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20786-38262&t=eH5MzOzTx9gt6HYB-4
message SecretsSection {
  api.typesv2.ui.IconTextComponent title = 1;
  api.typesv2.ui.IconTextComponent cta = 2;
  repeated frontend.insights.secrets.SecretSummary secret_summaries = 3;
  api.typesv2.ui.IconTextComponent subtitle = 4;
}

message NetWorthVisualisation {
  VisualisationType visualisation_type = 1;
  oneof params {
    IslandParams island_params = 2;
  }
}

enum VisualisationType {
  VISUALISATION_TYPE_UNSPECIFIED = 0;
  VISUALISATION_TYPE_ISLAND = 1;
}

message IslandParams {
  // Asset's or Liabilities icons
  repeated CategoryVisualisationItem categories = 1;
  // The background image over which the CategoryVisualisationItems will be plotted
  Image background_image = 2;
  // Summary of users total assets and liabilities
  VisualisationSummary summary = 3;
  // The message informing user on assets or liabilities status
  api.typesv2.common.Text message = 4;
}

message Image {
  string image_url = 1;
  // This field will give the value of image width to screen width. Value will be between 0 and 1 as the image width should always be less than screen width.
  float image_to_screen_width_ratio = 2;
  // This field will give the ratio of image width to image_height. This will be used to calculate the height of the image.
  float image_aspect_ratio = 3;
}

// The summary shown below the visualisation
message VisualisationSummary {
  // title to show in the net worth visualisation summary
  api.typesv2.ui.IconTextComponent title = 1;
  // Total net worth value. The value should be overridden by client and formatting should be used as it is.
  api.typesv2.ui.IconTextComponent value = 2;
  // Number of categories added by user. If client uses cache for some widget they will have to calculate this value.
  int32 categories_added = 3;
  // Total number of categories
  int32 total_categories = 4;
  // Border Color for the summary card
  string borderColor = 5;
  // Shadow color for the summary card
  string shadowColor = 6;
}

// The icon for every category
message CategoryVisualisationItem {
  api.typesv2.common.VisualElement category_icon = 1;
  // X position of the top left of the item. This will be from the top left point and relative to the width of the island image. It's value will vary from 0 to 1.
  float x = 2;
  // Y position of the top left of the item. This will be from the top left point and relative to the height of the island image. It's value will vary from 0 to 1.
  float y = 3;
  // relative width of the image. It will vary from 0 to 1.
  float width = 4;
  // relative height of the image. It will vary from 0 to 1.
  float height = 5;
}

message Section {
  // This will be the unique identifier for the section. Each widget will be specific to a section. Caching of a widget should be done with the section.
  // If section id is updated then all the widgets that are cached in the section should be invalidated.
  string id = 1;
  // Summary to show for the section. It will have title and value. Value will be empty string. It should be overwritten by client.
  api.typesv2.ui.VerticalKeyValuePair summary = 2 [deprecated = true];
  // It will give text for add more button.
  api.typesv2.ui.IconTextComponent add_more_button = 3 [deprecated = true];
  // The details required for add more widgets page such as list of widgets, search hints etc.
  AddMoreWidgets add_more_widgets = 4;
  // List of widgets that will be displayed in the section. If cache is used then the ordering should be done by client. Else the ordering should be same as what is sent by backend.
  repeated Widget widgets = 5;
  // Limit on the number of categories to show in section. Other widgets will be shown when user clicks on show more widget.
  int32 limit = 6;

  // This will be used for the dash_board_type networth. For assets this will be nil
  SectionHeader section_header = 7;

  message SectionHeader {
    // Summary to show for the section. It will have title and value. Value will be empty string. It should be overwritten by client.
    api.typesv2.ui.VerticalKeyValuePair summary = 1;
    // It will give text for add more button.
    api.typesv2.ui.IconTextComponent add_more_button = 2;
  }
}

message AddMoreWidgets {
  // title of add more page
  string title = 2;
  // The list of widgets to be shown when use clicks on add more button. These widgets will be the ones which user has not added yet to the net worth.
  // The ordering of the widgets should be maintained by backend.
  repeated Widget widgets = 1;
}

// Deprecated: Use WidgetV2 instead from api/frontend/insights/networth/ui/ui.proto
message Widget {
  // unique identifier for the widget. This is to be used by client for caching.
  string id = 1;
  // type of widget that should be rendered.
  WidgetType type = 2;
  oneof params {
    // Params for Card type widget
    CardWidgetParams card_widget_params = 3;
  }
  // This enum will tell if client should use the widget from cache or not.
  CacheControl cache_control = 5;
  // State of widget i.e if it is uninitialized, initialized etc.
  WidgetState state = 6;
  // payload to be sent in analytics event from client
  // if cache_control is set to use cache, use the payload as well from cache
  string widget_analytics_payload = 7;
}

enum WidgetType {
  WIDGET_TYPE_UNSPECIFIED = 0;
  WIDGET_TYPE_CARD = 1;
}

message CardWidgetParams {
  // Icon to show at the top of card
  api.typesv2.common.VisualElement icon = 1;
  // title to show below the icon
  api.typesv2.common.Text title = 2;
  // Primary text for the widget to show below title
  api.typesv2.ui.IconTextComponent primary_text = 3;
  // Value to be used for calculating the total section value and net worth. This can be negative in case of liabilities.
  api.typesv2.Money value = 4;
  // deeplink to open when user clicks on the widget
  deeplink.Deeplink deeplink = 5;
  // background color of the card
  string background_color = 6;
  // tag to show at the bottom of the widget
  api.typesv2.ui.IconTextComponent tag = 7;
}

enum CacheControl {
  // If cache control is unspecified then client should not use cache
  CACHE_CONTROL_UNSPECIFIED = 0;
  // Client is supposed to read from cache for overall computation.
  CACHE_CONTROL_USE_CACHE = 1;
}

enum WidgetState {
  WIDGET_STATE_UNSPECIFIED = 0;
  // This refers to the state where user had not successfully completed the category process.
  WIDGET_STATE_UNINITIALIZED = 1;
  // This refers to the state where the user has completed the category process successfully. The data can be zero or contain some value.
  // If a widget is in initialised state and client has to use cache, then it should count this widget in category added.
  WIDGET_STATE_INITIALIZED = 2;
}

message GetNetWorthSummaryForHomeRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetNetWorthSummaryForHomeResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // dashboard info in new home summary format
  home.HomeDashboard dashboard_info = 2;
}

message GetNextNetWorthRefreshActionRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  api.typesv2.deeplink_screen_option.insights.networth_refresh.NetWorthGetNextActionRequestParams request_params = 2;
}

message GetNextNetWorthRefreshActionResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for next action
  deeplink.Deeplink next_action = 2;
  // header for persisting throughout the refresh session
  api.typesv2.deeplink_screen_option.insights.networth_refresh.NetWorthRefreshHeader refresh_header = 3;
}

message UpdateManualAssetsRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  reserved 2;
  // Map of 'asset_id -> InputOptionValue'
  // Asset id would correspond to 'ManualAssetsCurrentValueRefreshDetails -> asset_id'
  // current value would be whatever user inputs
  map<string, InputOptionValue> updated_asset_current_values_v2 = 3;
}

message UpdateManualAssetsResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for next action
  // to be shown in case of success status
  deeplink.Deeplink next_screen = 2;
}

message SearchAssetFormFieldOptionsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];

  // The identifier to use when searching for relevant choices based on user's search-text for the field.
  // Distinguishes one form field from another when both may have the same name.
  // This is a string form of the AssetFormFieldSearchIdentifier enum
  // kept client-independent for backward compatibility.
  string search_identifier = 2;

  string search_text = 3;
}

message SearchAssetFormFieldOptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // A list of choices relevant to a form field and search-text
  // This list is expected to change based on user's search text, and thus can be empty if there are no relevant choices.
  // Note: The actual list of choices for an input can be long. Hence, only a few relevant options
  // are returned (thus, avoiding requirement of pagination).
   // deprecated in favour of search_response
  repeated PresetChoice choices = 2 [deprecated = true];

  oneof search_response {
    PresetChoicesList preset_choices_list = 3;
    // Error screen to be shown in case of no data found for the requested search text
    ErrorView error_view = 4;
  }

  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4999-42637&t=a9T3DDR2aPDrylRC-4
  message ErrorView {
    api.typesv2.common.VisualElement image = 1;
    api.typesv2.ui.VerticalKeyValuePair key_value_pair = 2;
    // redirection to bottom sheet explaining about the error occurred
    // e.g. for employer not found error screen
    api.typesv2.ui.IconTextComponent info_redirection = 3;
  }
}

message PresetChoicesList {
  // A list of choices relevant to a form field and search-text
  // This list is expected to change based on user's search text, and thus can be empty if there are no relevant choices.
  // Note: The actual list of choices for an input can be long. Hence, only a few relevant options
  // are returned (thus, avoiding requirement of pagination).
  repeated PresetChoice preset_choices = 1;
}

message GetCreditScoreSummaryForHomeRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}
message GetCreditScoreSummaryForHomeResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // dashboard info in new home summary format
  home.HomeDashboard dashboard_info = 2;
}

message GetEpfSummaryForHomeRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}
message GetEpfSummaryForHomeResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // dashboard info in new home summary format
  home.HomeDashboard dashboard_info = 2;
}

message GetMfSummaryForHomeRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}
message GetMfSummaryForHomeResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // dashboard info in new home summary format
  home.HomeDashboard dashboard_info = 2;
}

message GetWealthBuilderLandingPageRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetWealthBuilderLandingPageResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // list of scrollable components
  // This will comprise of widgets that can be added in Home screen also
  repeated home.orchestrator.ScrollableComponent scrollable_components = 2;
}


message GetAssetImportFlowStatusRequest {
  frontend.header.RequestHeader req = 1;
  string asset_type = 2;
  string flow_id = 3;
  string flow_type = 4;
  bytes payload = 5;
}

message GetAssetImportFlowStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  oneof status_update {
    // while polling is in progress this property will return details which need to be displayed.
    // this would primarily be used if we wish to update footer details or title or lottie frames
    api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails status = 2;
    // will return the results based on status of fetch
    AssetImportResultsDetails results = 3;
  }
}

message AssetImportResultsDetails {
  // Title stating the current status of data fetch
  api.typesv2.common.Text title = 1;

  oneof results {
    api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails import_in_progress_details = 2;
    AssetImportSuccessDetails import_success_details = 3;
    api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails import_failure_details = 4;
  }
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=134-6218&t=66Wt92ulqSVdD9D2-4
// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=137-8345&t=66Wt92ulqSVdD9D2-4
message AssetImportSuccessDetails {
  // text to be shown above the animation eg Net Worth
  api.typesv2.common.Text title = 1;
  // the rolling animation should only be rendered if rolling_animation_details are not nil
  NetworthRollingAnimationDetails rolling_animation_details = 2;
  // used to update how many funds are connected
  api.typesv2.ui.IconTextComponent assets_update_label = 3;
  ConnectMoreAssetsComponent connect_more_assets_component = 4;
  frontend.deeplink.Cta exit_cta = 5;
  // based on the status this will pass frames to be loaded on the lottie
  api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails lottie_details = 6;
  // client will use this to show share cta in nav bar top right corner
  api.typesv2.ui.IconTextComponent share_cta = 7;
}

message NetworthRollingAnimationDetails {
  // value to be used at the end of rolling animation.
  // updated networth value contains value of asset connected
  api.typesv2.Money updated_networth = 1;
  api.typesv2.common.Text currency_symbol = 2;
  api.typesv2.ui.IconTextComponent updated_networth_text = 3;
}

message ConnectMoreAssetsComponent {
  api.typesv2.ui.IconTextComponent title = 1;
  frontend.deeplink.Cta connect_more_cta = 2 [deprecated = true];
  // connect more
  api.typesv2.ui.IconTextComponent connect_more = 3;
  // background color for the component
  string background_color = 4;
  int32 corner_radius = 5;
  // progress bar details
  ProgressBarDetails progress_bar_details = 6;
  message ProgressBarDetails {
    int32 completed_percentage = 1;
    string foreground_color = 2;
    string background_color = 3;
  }
}

message GetWealthBuilderDashboardComponentRequest {
  frontend.header.RequestHeader req = 1;
}

message GetWealthBuilderDashboardComponentResponse {
  frontend.header.ResponseHeader resp_header = 1;
  ui.WealthBuilderLandingComponent wealth_builder_landing_component = 2;
}

message GetConnectMoreAssetsScreenRequest {
  frontend.header.RequestHeader req = 1;
  ConnectMoreAssetsScreenRequestParams request_params = 2;
}

message GetConnectMoreAssetsScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // title to show on the screen(for older users)
  api.typesv2.common.Text title = 2;
  // card image to show at the bottom of the screen(on top for older users)
  api.typesv2.common.VisualElement card_image = 3;
  // list of assets and liabilities that can be connected
  repeated ui.WealthBuilderLandingSection wealth_builder_landing_sections = 4;
  // dashboard header,
  // contains either zero state when no assets are connected
  // or contains section title, money value and other details when any single asset is connected
  ui.DashboardHeader dashboard_header = 5;
  // toggle component to switch between assets and liabilities
  // 0th index is for assets and 1st index is for liabilities
  // toggle_value_1 -> assets and toggle_value_2 -> liabilities
  investment.ui.AssetLandingToggleComponent toggle_component = 6;
  // info cta to show like 'liabilities are also tracked'
  api.typesv2.ui.IconTextComponent liability_tracking_cta = 7;
}

message ConnectMoreAssetsScreenRequestParams {
  // asset types which are not connected
  repeated string asset_types = 2;
  // liability types which are not connected
  repeated string liability_types = 3;
}

message SubmitManualFormsRequest {
  frontend.header.RequestHeader req = 1;

  message FormSubmissionData {
    api.typesv2.ManualAssetFormIdentifier form_identifier = 1;
    repeated frontend.insights.networth.NetWorthManualInputData form_data = 2;
  }

  repeated FormSubmissionData form_submission_data = 2;
}

message SubmitManualFormsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink exit_deeplink = 2;
}

message GetNetworthDataFileRequest {
  frontend.header.RequestHeader req = 1;
  string req_payload = 2;
}

message GetNetworthDataFileResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // file containing comprehensive networth data in base64-encoded JSON format.
  api.typesv2.common.file.File networth_file = 2;
}
