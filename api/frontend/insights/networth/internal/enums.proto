syntax = "proto3";

package frontend.insights.networth;

option go_package = "github.com/epifi/gamma/api/frontend/insights/networth";
option java_package = "com.github.epifi.gamma.api.frontend.insights.networth";

enum NetworthCategory {
  NETWORTH_CATEGORY_UNSPECIFIED = 0;
  NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS = 1;
  NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS = 2;
  NETWORTH_CATEGORY_ASSET_EPF = 3;
  NETWORTH_CATEGORY_ASSET_DEPOSITS = 4;
  NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES = 5;
  NETWORTH_CATEGORY_ASSET_US_SECURITIES = 6;
  NETWORTH_CATEGORY_ASSET_JUMP = 7;
  NETWORTH_CATEGORY_ASSET_FI_SD = 8;
  NETWORTH_CATEGORY_ASSET_FI_FD = 9;

  NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING = 10;
  NETWORTH_CATEGORY_LIABILITY_HOME_LOAN = 11;
  NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN = 12;
  NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN = 13;
  NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN = 14;
  NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS = 15;

  NETWORTH_CATEGORY_ASSET_NPS = 16;

  NETWORTH_CATEGORY_AIF = 17;
  NETWORTH_CATEGORY_ART_ARTEFACTS = 18;
  NETWORTH_CATEGORY_BONDS = 19;
  NETWORTH_CATEGORY_CASH = 20;
  NETWORTH_CATEGORY_DIGITAL_GOLD = 21;
  NETWORTH_CATEGORY_DIGITAL_SILVER = 22;
  NETWORTH_CATEGORY_PRIVATE_EQUITY = 23;
  NETWORTH_CATEGORY_REAL_ESTATE = 24;
  NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE = 25;
  NETWORTH_CATEGORY_PUBLIC_PROVIDENT_FUND = 26;
  NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION = 27;
  NETWORTH_CATEGORY_GADGETS = 28;
  NETWORTH_CATEGORY_VEHICLE = 29;
  NETWORTH_CATEGORY_CRYPTO = 30;
  NETWORTH_CATEGORY_FURNITURE = 31;
  NETWORTH_CATEGORY_COLLECTIBLES = 32;
  NETWORTH_CATEGORY_JEWELLERY = 33;
  NETWORTH_CATEGORY_OTHERS = 34;
}

enum NetworthSectionType {
  NETWORTH_SECTION_TYPE_UNSPECIFIED = 0;
  NETWORTH_SECTION_TYPE_ASSETS = 1;
  NETWORTH_SECTION_TYPE_LIABILITIES = 2;
}

enum NetworthCategoryStatus {
  NETWORTH_CATEGORY_STATUS_UNSPECIFIED = 0;
  // This refers to the state when a user has not successfully completed the initialisation of category.
  // eg. A user has not completed the credit report import flow successfully.
  NETWORTH_CATEGORY_STATUS_UNINITIALIZED = 1;
  // This refers to the state when a user has successfully completed the initialisation of category, but the category value does not exist for them.
  // eg. User has fetched credit report but has not taken a loan, so the loan category is not applicable for the user.
  NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE = 2;
  // This refers to the state when a user has successfully completed the initialisation of category and the category value also exists for them.
  NETWORTH_CATEGORY_STATUS_INITIALIZED = 3;
  // This refers to the state when the category value fetch failed for a given request.
  NETWORTH_CATEGORY_STATUS_VALUE_FETCH_FAILED = 4;
  // This refers to the state when the computation of category value is in process
  NETWORTH_CATEGORY_STATUS_IN_PROCESS = 5;
}

enum NetworthWidgetState {
  NETWORTH_WIDGET_STATE_UNSPECIFIED = 0;
  // Widget will have interactions enabled
  NETWORTH_WIDGET_STATE_ACTIVE = 1;
  // Widget is not supported yet. It will come in future.
  NETWORTH_WIDGET_STATE_COMING_SOON = 2;
}


// NetworthManualFormFieldName denotes different field names
// in networth manual forms
enum NetworthManualFormFieldName {
  NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED = 0;
  NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME = 1;
  NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE = 2;
  NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE = 3;
  NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE = 4;
  NETWORTH_MANUAL_FORM_FIELD_EVALUATION_DATE = 5;
  NETWORTH_MANUAL_FORM_FIELD_AMC_NAME = 6;
  NETWORTH_MANUAL_FORM_FIELD_CATEGORY = 7;
  NETWORTH_MANUAL_FORM_FIELD_FOLIO_ID = 8;
  NETWORTH_MANUAL_FORM_FIELD_BROKER_CODE = 9;
  NETWORTH_MANUAL_FORM_FIELD_REMARKS = 10;
  NETWORTH_MANUAL_FORM_FIELD_MATURITY_DATE = 11;
  NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS = 12;
  NETWORTH_MANUAL_FORM_FIELD_QUANTITY_GRAMS = 13;
  NETWORTH_MANUAL_FORM_FIELD_GOLD_TYPE = 14;
  NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_FREQUENCY = 15;
  NETWORTH_MANUAL_FORM_FIELD_CURRENCY_TYPE = 16;
  NETWORTH_MANUAL_FORM_FIELD_VESTING_SCHEDULE = 17;
  NETWORTH_MANUAL_FORM_FIELD_INTEREST_RATE = 18;
  NETWORTH_MANUAL_FORM_FIELD_NAME_DEPOSIT_TYPE = 19;
  NETWORTH_MANUAL_FORM_FIELD_NAME_CONDITION = 20;
  NETWORTH_MANUAL_FORM_FIELD_NAME_DEVICE_TYPE = 21;
  NETWORTH_MANUAL_FORM_FIELD_NAME_VEHICLE_TYPE = 22;
  NETWORTH_MANUAL_FORM_FIELD_NAME_DEPRECIATION_RATE = 23;
  NETWORTH_MANUAL_FORM_FIELD_NAME_APPRECIATION_RATE = 24;
  NETWORTH_MANUAL_FORM_FIELD_NAME_CURRENT_VALUE_PER_UNIT = 25;
  NETWORTH_MANUAL_FORM_FIELD_NAME_FURNITURE_TYPE = 26;
  NETWORTH_MANUAL_FORM_FIELD_NAME_ITEM_TYPE = 27;
  NETWORTH_MANUAL_FORM_FIELD_NAME_ITEM_BRAND = 28;
  NETWORTH_MANUAL_FORM_FIELD_NAME_ITEM_MODEL = 29;
  NETWORTH_MANUAL_FORM_FIELD_NAME_BASE_METAL = 30;
  NETWORTH_MANUAL_FORM_FIELD_NAME_PURITY = 31;
  NETWORTH_MANUAL_FORM_FIELD_NAME_INVESTMENT_TYPE = 32;
}

enum NetworthAggregateType {
  NETWORTH_AGGREGATE_TYPE_UNSPECIFIED = 0;
  NETWORTH_AGGREGATE_TYPE_SAVINGS = 1;
  NETWORTH_AGGREGATE_TYPE_ASSETS = 2;
  NETWORTH_AGGREGATE_TYPE_LIABILITIES = 3;
  NETWORTH_AGGREGATE_TYPE_INVESTMENTS = 4;
}

// Defines the purpose for which the networth data file is being requested
enum NetworthDataFilePurpose {
  NETWORTH_DATA_FILE_PURPOSE_UNSPECIFIED = 0;
  // File is being requested for download to user's device
  NETWORTH_DATA_FILE_PURPOSE_DOWNLOAD = 1;
  // File is being requested for export to external AI/analytics applications
  NETWORTH_DATA_FILE_PURPOSE_EXPORT = 2;
}

// Defines the type of data included in the networth data file
enum NetworthDataFileType {
  NETWORTH_DATA_FILE_TYPE_UNSPECIFIED = 0;
  // File contains comprehensive networth data including assets, liabilities, and analysis
  NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA = 1;
  // File contains bank account transactions data
  NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA = 2;
}
