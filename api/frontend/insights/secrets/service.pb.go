// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/secrets/service.proto

package secrets

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	analyser "github.com/epifi/gamma/api/frontend/analyser"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	secrets "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	portfoliotracker "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets/portfoliotracker"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	wealthanalyser "github.com/epifi/gamma/api/typesv2/ui/insights/wealthanalyser"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSecretAnalyserResponse_Status int32

const (
	GetSecretAnalyserResponse_OK GetSecretAnalyserResponse_Status = 0
	// The client specified an invalid argument such as incorrect secret id
	GetSecretAnalyserResponse_INVALID_ARGUMENT GetSecretAnalyserResponse_Status = 3
	// Secret could not be created due to unavailability of data
	GetSecretAnalyserResponse_NOT_FOUND       GetSecretAnalyserResponse_Status = 5
	GetSecretAnalyserResponse_INTERNAL        GetSecretAnalyserResponse_Status = 13
	GetSecretAnalyserResponse_UNAUTHENTICATED GetSecretAnalyserResponse_Status = 16
)

// Enum value maps for GetSecretAnalyserResponse_Status.
var (
	GetSecretAnalyserResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	GetSecretAnalyserResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
		"UNAUTHENTICATED":  16,
	}
)

func (x GetSecretAnalyserResponse_Status) Enum() *GetSecretAnalyserResponse_Status {
	p := new(GetSecretAnalyserResponse_Status)
	*p = x
	return p
}

func (x GetSecretAnalyserResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetSecretAnalyserResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_secrets_service_proto_enumTypes[0].Descriptor()
}

func (GetSecretAnalyserResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_secrets_service_proto_enumTypes[0]
}

func (x GetSecretAnalyserResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetSecretAnalyserResponse_Status.Descriptor instead.
func (GetSecretAnalyserResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetSecretSummariesResponse_Status int32

const (
	GetSecretSummariesResponse_OK GetSecretSummariesResponse_Status = 0
	// The client specified an invalid argument such as incorrect secret id
	GetSecretSummariesResponse_INVALID_ARGUMENT GetSecretSummariesResponse_Status = 3
	// Secret could not be created due to unavailability of data
	GetSecretSummariesResponse_NOT_FOUND       GetSecretSummariesResponse_Status = 5
	GetSecretSummariesResponse_INTERNAL        GetSecretSummariesResponse_Status = 13
	GetSecretSummariesResponse_UNAUTHENTICATED GetSecretSummariesResponse_Status = 16
)

// Enum value maps for GetSecretSummariesResponse_Status.
var (
	GetSecretSummariesResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	GetSecretSummariesResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
		"UNAUTHENTICATED":  16,
	}
)

func (x GetSecretSummariesResponse_Status) Enum() *GetSecretSummariesResponse_Status {
	p := new(GetSecretSummariesResponse_Status)
	*p = x
	return p
}

func (x GetSecretSummariesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetSecretSummariesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_secrets_service_proto_enumTypes[1].Descriptor()
}

func (GetSecretSummariesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_secrets_service_proto_enumTypes[1]
}

func (x GetSecretSummariesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetSecretSummariesResponse_Status.Descriptor instead.
func (GetSecretSummariesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{6, 0}
}

type GetSecretAnalyserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req                  *header.RequestHeader           `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	SecretName           string                          `protobuf:"bytes,2,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
	Filters              []*analyser.SelectedFilterValue `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	SecretCollectionName string                          `protobuf:"bytes,4,opt,name=secret_collection_name,json=secretCollectionName,proto3" json:"secret_collection_name,omitempty"`
	// provenance is used to track the flow of the request
	Provenance secrets.Provenance `protobuf:"varint,5,opt,name=provenance,proto3,enum=api.typesv2.deeplink_screen_option.insights.secrets.Provenance" json:"provenance,omitempty"`
}

func (x *GetSecretAnalyserRequest) Reset() {
	*x = GetSecretAnalyserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretAnalyserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretAnalyserRequest) ProtoMessage() {}

func (x *GetSecretAnalyserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretAnalyserRequest.ProtoReflect.Descriptor instead.
func (*GetSecretAnalyserRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSecretAnalyserRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSecretAnalyserRequest) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

func (x *GetSecretAnalyserRequest) GetFilters() []*analyser.SelectedFilterValue {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetSecretAnalyserRequest) GetSecretCollectionName() string {
	if x != nil {
		return x.SecretCollectionName
	}
	return ""
}

func (x *GetSecretAnalyserRequest) GetProvenance() secrets.Provenance {
	if x != nil {
		return x.Provenance
	}
	return secrets.Provenance(0)
}

type GetSecretAnalyserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader             *header.ResponseHeader  `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	SecretAnalyserResponse *SecretAnalyserResponse `protobuf:"bytes,2,opt,name=secret_analyser_response,json=secretAnalyserResponse,proto3" json:"secret_analyser_response,omitempty"`
}

func (x *GetSecretAnalyserResponse) Reset() {
	*x = GetSecretAnalyserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretAnalyserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretAnalyserResponse) ProtoMessage() {}

func (x *GetSecretAnalyserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretAnalyserResponse.ProtoReflect.Descriptor instead.
func (*GetSecretAnalyserResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSecretAnalyserResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSecretAnalyserResponse) GetSecretAnalyserResponse() *SecretAnalyserResponse {
	if x != nil {
		return x.SecretAnalyserResponse
	}
	return nil
}

// SecretAnalyserResponse is wrapper time to contain the types of responses expected in GetSecretAnalyserResponse
type SecretAnalyserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Response:
	//
	//	*SecretAnalyserResponse_SecretAnalyser
	//	*SecretAnalyserResponse_RedirectDeeplink
	Response isSecretAnalyserResponse_Response `protobuf_oneof:"response"`
	// send the event properties required for client events
	// this will have secret_name, secret_id, flow_name, state (i.e hidden or visible) and data_available is for if account is connected or not
	EventProperties map[string]string `protobuf:"bytes,4,rep,name=event_properties,json=eventProperties,proto3" json:"event_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SecretAnalyserResponse) Reset() {
	*x = SecretAnalyserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretAnalyserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretAnalyserResponse) ProtoMessage() {}

func (x *SecretAnalyserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretAnalyserResponse.ProtoReflect.Descriptor instead.
func (*SecretAnalyserResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{2}
}

func (m *SecretAnalyserResponse) GetResponse() isSecretAnalyserResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *SecretAnalyserResponse) GetSecretAnalyser() *SecretAnalyser {
	if x, ok := x.GetResponse().(*SecretAnalyserResponse_SecretAnalyser); ok {
		return x.SecretAnalyser
	}
	return nil
}

func (x *SecretAnalyserResponse) GetRedirectDeeplink() *deeplink.Deeplink {
	if x, ok := x.GetResponse().(*SecretAnalyserResponse_RedirectDeeplink); ok {
		return x.RedirectDeeplink
	}
	return nil
}

func (x *SecretAnalyserResponse) GetEventProperties() map[string]string {
	if x != nil {
		return x.EventProperties
	}
	return nil
}

type isSecretAnalyserResponse_Response interface {
	isSecretAnalyserResponse_Response()
}

type SecretAnalyserResponse_SecretAnalyser struct {
	SecretAnalyser *SecretAnalyser `protobuf:"bytes,2,opt,name=secret_analyser,json=secretAnalyser,proto3,oneof"`
}

type SecretAnalyserResponse_RedirectDeeplink struct {
	// Redirect deeplink in case no data is found to build the secret
	// deeplink will be sent with either OK or NOT_FOUND status codes
	RedirectDeeplink *deeplink.Deeplink `protobuf:"bytes,3,opt,name=redirect_deeplink,json=redirectDeeplink,proto3,oneof"`
}

func (*SecretAnalyserResponse_SecretAnalyser) isSecretAnalyserResponse_Response() {}

func (*SecretAnalyserResponse_RedirectDeeplink) isSecretAnalyserResponse_Response() {}

type AddSecretToFavouritesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req           *header.RequestHeader               `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	RequestParams *AddSecretToFavouritesRequestParams `protobuf:"bytes,2,opt,name=request_params,json=requestParams,proto3" json:"request_params,omitempty"`
}

func (x *AddSecretToFavouritesRequest) Reset() {
	*x = AddSecretToFavouritesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSecretToFavouritesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSecretToFavouritesRequest) ProtoMessage() {}

func (x *AddSecretToFavouritesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSecretToFavouritesRequest.ProtoReflect.Descriptor instead.
func (*AddSecretToFavouritesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{3}
}

func (x *AddSecretToFavouritesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *AddSecretToFavouritesRequest) GetRequestParams() *AddSecretToFavouritesRequestParams {
	if x != nil {
		return x.RequestParams
	}
	return nil
}

type AddSecretToFavouritesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// In case of failure, clients should show a toast and not update use the 'favourite_icon
	RespHeader    *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	FavouriteIcon *common.VisualElement  `protobuf:"bytes,2,opt,name=favourite_icon,json=favouriteIcon,proto3" json:"favourite_icon,omitempty"`
}

func (x *AddSecretToFavouritesResponse) Reset() {
	*x = AddSecretToFavouritesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddSecretToFavouritesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSecretToFavouritesResponse) ProtoMessage() {}

func (x *AddSecretToFavouritesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSecretToFavouritesResponse.ProtoReflect.Descriptor instead.
func (*AddSecretToFavouritesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{4}
}

func (x *AddSecretToFavouritesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *AddSecretToFavouritesResponse) GetFavouriteIcon() *common.VisualElement {
	if x != nil {
		return x.FavouriteIcon
	}
	return nil
}

type GetSecretSummariesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// stringified entrypoint at which secret summaries will be shown
	// entrypoints are defined in api/insights/secrets/frontend/entrypoint.proto
	Entrypoint string `protobuf:"bytes,2,opt,name=entrypoint,proto3" json:"entrypoint,omitempty"`
}

func (x *GetSecretSummariesRequest) Reset() {
	*x = GetSecretSummariesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretSummariesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretSummariesRequest) ProtoMessage() {}

func (x *GetSecretSummariesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretSummariesRequest.ProtoReflect.Descriptor instead.
func (*GetSecretSummariesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetSecretSummariesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSecretSummariesRequest) GetEntrypoint() string {
	if x != nil {
		return x.Entrypoint
	}
	return ""
}

type GetSecretSummariesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader      *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	SecretSummaries []*SecretSummary       `protobuf:"bytes,2,rep,name=secret_summaries,json=secretSummaries,proto3" json:"secret_summaries,omitempty"`
	// title for the summaries collection
	// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16935-8404&t=xwZgiWjJQosW4n0e-1
	Title *ui.IconTextComponent `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// Unlock all Money Secrets
	// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16935-8404&t=xwZgiWjJQosW4n0e-1
	Cta *ui.IconTextComponent `protobuf:"bytes,4,opt,name=cta,proto3" json:"cta,omitempty"`
	// Show/Hide values button on Money Secrets Section
	// This will only be used for UI and visibility of the button. The state will not be saved on BE and will be completely controlled by client
	// Absence of this field indicate that the show/hide feature is disabled and summary cards should show their values
	// figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=19329-20961&t=JBPAFgn1bNeTSeRX-4
	ShowHideButton *GetSecretSummariesResponse_ShowHideComponent `protobuf:"bytes,5,opt,name=show_hide_button,json=showHideButton,proto3" json:"show_hide_button,omitempty"`
	Subtitle       *ui.IconTextComponent                         `protobuf:"bytes,6,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
}

func (x *GetSecretSummariesResponse) Reset() {
	*x = GetSecretSummariesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretSummariesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretSummariesResponse) ProtoMessage() {}

func (x *GetSecretSummariesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretSummariesResponse.ProtoReflect.Descriptor instead.
func (*GetSecretSummariesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetSecretSummariesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSecretSummariesResponse) GetSecretSummaries() []*SecretSummary {
	if x != nil {
		return x.SecretSummaries
	}
	return nil
}

func (x *GetSecretSummariesResponse) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GetSecretSummariesResponse) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *GetSecretSummariesResponse) GetShowHideButton() *GetSecretSummariesResponse_ShowHideComponent {
	if x != nil {
		return x.ShowHideButton
	}
	return nil
}

func (x *GetSecretSummariesResponse) GetSubtitle() *ui.IconTextComponent {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

type GetSecretLibraryPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetSecretLibraryPageRequest) Reset() {
	*x = GetSecretLibraryPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretLibraryPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretLibraryPageRequest) ProtoMessage() {}

func (x *GetSecretLibraryPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretLibraryPageRequest.ProtoReflect.Descriptor instead.
func (*GetSecretLibraryPageRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetSecretLibraryPageRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetSecretLibraryPageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader        *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	SecretLandingPage *SecretLibraryPage     `protobuf:"bytes,2,opt,name=secret_landing_page,json=secretLandingPage,proto3" json:"secret_landing_page,omitempty"`
}

func (x *GetSecretLibraryPageResponse) Reset() {
	*x = GetSecretLibraryPageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretLibraryPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretLibraryPageResponse) ProtoMessage() {}

func (x *GetSecretLibraryPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretLibraryPageResponse.ProtoReflect.Descriptor instead.
func (*GetSecretLibraryPageResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetSecretLibraryPageResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSecretLibraryPageResponse) GetSecretLandingPage() *SecretLibraryPage {
	if x != nil {
		return x.SecretLandingPage
	}
	return nil
}

type WealthAnalyserReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Corresponds to 'WEALTH_ANALYSER_REPORT_TYPE' enum in backend
	ReportType string `protobuf:"bytes,2,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
}

func (x *WealthAnalyserReportRequest) Reset() {
	*x = WealthAnalyserReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthAnalyserReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthAnalyserReportRequest) ProtoMessage() {}

func (x *WealthAnalyserReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthAnalyserReportRequest.ProtoReflect.Descriptor instead.
func (*WealthAnalyserReportRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{9}
}

func (x *WealthAnalyserReportRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *WealthAnalyserReportRequest) GetReportType() string {
	if x != nil {
		return x.ReportType
	}
	return ""
}

type WealthAnalyserReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// [Deprecated] Use the oneof response field instead because we need redirection deeplink as well
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/secrets/service.proto.
	Report *WealthAnalyserReport `protobuf:"bytes,2,opt,name=report,proto3" json:"report,omitempty"`
	// Types that are assignable to Response:
	//
	//	*WealthAnalyserReportResponse_WealthAnalyserReport
	//	*WealthAnalyserReportResponse_RedirectDeeplink
	Response isWealthAnalyserReportResponse_Response `protobuf_oneof:"response"`
}

func (x *WealthAnalyserReportResponse) Reset() {
	*x = WealthAnalyserReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthAnalyserReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthAnalyserReportResponse) ProtoMessage() {}

func (x *WealthAnalyserReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthAnalyserReportResponse.ProtoReflect.Descriptor instead.
func (*WealthAnalyserReportResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{10}
}

func (x *WealthAnalyserReportResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/secrets/service.proto.
func (x *WealthAnalyserReportResponse) GetReport() *WealthAnalyserReport {
	if x != nil {
		return x.Report
	}
	return nil
}

func (m *WealthAnalyserReportResponse) GetResponse() isWealthAnalyserReportResponse_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *WealthAnalyserReportResponse) GetWealthAnalyserReport() *WealthAnalyserReport {
	if x, ok := x.GetResponse().(*WealthAnalyserReportResponse_WealthAnalyserReport); ok {
		return x.WealthAnalyserReport
	}
	return nil
}

func (x *WealthAnalyserReportResponse) GetRedirectDeeplink() *deeplink.Deeplink {
	if x, ok := x.GetResponse().(*WealthAnalyserReportResponse_RedirectDeeplink); ok {
		return x.RedirectDeeplink
	}
	return nil
}

type isWealthAnalyserReportResponse_Response interface {
	isWealthAnalyserReportResponse_Response()
}

type WealthAnalyserReportResponse_WealthAnalyserReport struct {
	WealthAnalyserReport *WealthAnalyserReport `protobuf:"bytes,3,opt,name=wealth_analyser_report,json=wealthAnalyserReport,proto3,oneof"`
}

type WealthAnalyserReportResponse_RedirectDeeplink struct {
	RedirectDeeplink *deeplink.Deeplink `protobuf:"bytes,4,opt,name=redirect_deeplink,json=redirectDeeplink,proto3,oneof"`
}

func (*WealthAnalyserReportResponse_WealthAnalyserReport) isWealthAnalyserReportResponse_Response() {}

func (*WealthAnalyserReportResponse_RedirectDeeplink) isWealthAnalyserReportResponse_Response() {}

// Request message for the GetWealthAnalyserWidget RPC method
type GetWealthAnalyserWidgetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// String value of WealthAnalyserWidgetEntrypoint, defined in "api/frontend/insights/enums.proto"
	Entrypoint string `protobuf:"bytes,2,opt,name=entrypoint,proto3" json:"entrypoint,omitempty"`
}

func (x *GetWealthAnalyserWidgetRequest) Reset() {
	*x = GetWealthAnalyserWidgetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWealthAnalyserWidgetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWealthAnalyserWidgetRequest) ProtoMessage() {}

func (x *GetWealthAnalyserWidgetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWealthAnalyserWidgetRequest.ProtoReflect.Descriptor instead.
func (*GetWealthAnalyserWidgetRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetWealthAnalyserWidgetRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetWealthAnalyserWidgetRequest) GetEntrypoint() string {
	if x != nil {
		return x.Entrypoint
	}
	return ""
}

// Response message for the GetWealthAnalyserWidget RPC method
type GetWealthAnalyserWidgetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Example: title - "Know your money better", subtitle - "With expert curated analysis!"
	Header *ui.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	// List of asset analysis cards
	Cards []*wealthanalyser.AssetsAnalysisCard `protobuf:"bytes,3,rep,name=cards,proto3" json:"cards,omitempty"`
	// Linear gradient background for the widget
	BgLinearGradient *widget.LinearGradient `protobuf:"bytes,4,opt,name=bg_linear_gradient,json=bgLinearGradient,proto3" json:"bg_linear_gradient,omitempty"`
}

func (x *GetWealthAnalyserWidgetResponse) Reset() {
	*x = GetWealthAnalyserWidgetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWealthAnalyserWidgetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWealthAnalyserWidgetResponse) ProtoMessage() {}

func (x *GetWealthAnalyserWidgetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWealthAnalyserWidgetResponse.ProtoReflect.Descriptor instead.
func (*GetWealthAnalyserWidgetResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetWealthAnalyserWidgetResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetWealthAnalyserWidgetResponse) GetHeader() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetWealthAnalyserWidgetResponse) GetCards() []*wealthanalyser.AssetsAnalysisCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

func (x *GetWealthAnalyserWidgetResponse) GetBgLinearGradient() *widget.LinearGradient {
	if x != nil {
		return x.BgLinearGradient
	}
	return nil
}

type GetSecretsFooterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// identifies the collection to be navigated in the footer
	SecretCollectionName string `protobuf:"bytes,2,opt,name=secret_collection_name,json=secretCollectionName,proto3" json:"secret_collection_name,omitempty"`
}

func (x *GetSecretsFooterRequest) Reset() {
	*x = GetSecretsFooterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretsFooterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretsFooterRequest) ProtoMessage() {}

func (x *GetSecretsFooterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretsFooterRequest.ProtoReflect.Descriptor instead.
func (*GetSecretsFooterRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetSecretsFooterRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSecretsFooterRequest) GetSecretCollectionName() string {
	if x != nil {
		return x.SecretCollectionName
	}
	return ""
}

type GetSecretsFooterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// array of secrets to be navigated in the footer
	// used to call 'GetSecretAnalyser' on next/back navigation
	// secret_names is deprecated, use secret_deeplinks instead
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/secrets/service.proto.
	SecretNames []string `protobuf:"bytes,2,rep,name=secret_names,json=secretNames,proto3" json:"secret_names,omitempty"`
	// back button, hidden on first index
	BackButton *ui.IconTextComponent `protobuf:"bytes,3,opt,name=back_button,json=backButton,proto3" json:"back_button,omitempty"`
	// next button, hidden on last index
	NextButton *ui.IconTextComponent `protobuf:"bytes,4,opt,name=next_button,json=nextButton,proto3" json:"next_button,omitempty"`
	// background color
	BgColor *ui.BackgroundColour `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// used for font properties for page index label, text string will be set on client
	IndexLabel *common.Text `protobuf:"bytes,6,opt,name=index_label,json=indexLabel,proto3" json:"index_label,omitempty"`
	// array of secret deeplinks to be used to navigate in the footer
	// deeplinks for GetSecretAnalyser on next/back navigation
	SecretDeeplinks []*deeplink.Deeplink `protobuf:"bytes,7,rep,name=secret_deeplinks,json=secretDeeplinks,proto3" json:"secret_deeplinks,omitempty"`
	// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=5580-46284&t=ZEWUctFYgsnUYce5-4
	// deeplink to use in case of next press by user on last secret of collection
	FlowCompletionScreen *deeplink.Deeplink `protobuf:"bytes,8,opt,name=flow_completion_screen,json=flowCompletionScreen,proto3" json:"flow_completion_screen,omitempty"`
}

func (x *GetSecretsFooterResponse) Reset() {
	*x = GetSecretsFooterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretsFooterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretsFooterResponse) ProtoMessage() {}

func (x *GetSecretsFooterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretsFooterResponse.ProtoReflect.Descriptor instead.
func (*GetSecretsFooterResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetSecretsFooterResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/secrets/service.proto.
func (x *GetSecretsFooterResponse) GetSecretNames() []string {
	if x != nil {
		return x.SecretNames
	}
	return nil
}

func (x *GetSecretsFooterResponse) GetBackButton() *ui.IconTextComponent {
	if x != nil {
		return x.BackButton
	}
	return nil
}

func (x *GetSecretsFooterResponse) GetNextButton() *ui.IconTextComponent {
	if x != nil {
		return x.NextButton
	}
	return nil
}

func (x *GetSecretsFooterResponse) GetBgColor() *ui.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *GetSecretsFooterResponse) GetIndexLabel() *common.Text {
	if x != nil {
		return x.IndexLabel
	}
	return nil
}

func (x *GetSecretsFooterResponse) GetSecretDeeplinks() []*deeplink.Deeplink {
	if x != nil {
		return x.SecretDeeplinks
	}
	return nil
}

func (x *GetSecretsFooterResponse) GetFlowCompletionScreen() *deeplink.Deeplink {
	if x != nil {
		return x.FlowCompletionScreen
	}
	return nil
}

type GetPortfolioTrackerLandingPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetPortfolioTrackerLandingPageRequest) Reset() {
	*x = GetPortfolioTrackerLandingPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPortfolioTrackerLandingPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortfolioTrackerLandingPageRequest) ProtoMessage() {}

func (x *GetPortfolioTrackerLandingPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortfolioTrackerLandingPageRequest.ProtoReflect.Descriptor instead.
func (*GetPortfolioTrackerLandingPageRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetPortfolioTrackerLandingPageRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=FKyA38eSynsGDVHi-4
type GetPortfolioTrackerLandingPageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1816&t=FKyA38eSynsGDVHi-4
	Header *PortFolioTrackerLandingPageHeader `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	// 'fixed_components' would be fixed at the top of the screen and user can scroll the scrollable components
	// Only the last component of the 'fixed_components' list would be stuck once user starts scrolling.
	// Currently only 'PortfolioTrackerTitleComponent' and 'NavigationToggleList' would be sent in this
	FixedComponents      []*PortfolioTrackerComponent `protobuf:"bytes,3,rep,name=fixed_components,json=fixedComponents,proto3" json:"fixed_components,omitempty"`
	ScrollableComponents []*PortfolioTrackerComponent `protobuf:"bytes,4,rep,name=scrollable_components,json=scrollableComponents,proto3" json:"scrollable_components,omitempty"`
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1799&t=KEfg8KQf8X3VlIa2-4
	// this is to show powered by epifiwealth on the end of the page
	// [Deprecated] in favour of repeated footer_components
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/secrets/service.proto.
	Footer *ui.IconTextComponent `protobuf:"bytes,5,opt,name=footer,proto3" json:"footer,omitempty"`
	// this is show disclaimer as well as survey component in footer
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=10737-8171&t=HajDK5vYkyuK6nJD-4
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=9579-14176&t=HajDK5vYkyuK6nJD-4
	FooterComponents []*ui.IconTextComponent `protobuf:"bytes,6,rep,name=footer_components,json=footerComponents,proto3" json:"footer_components,omitempty"`
}

func (x *GetPortfolioTrackerLandingPageResponse) Reset() {
	*x = GetPortfolioTrackerLandingPageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPortfolioTrackerLandingPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortfolioTrackerLandingPageResponse) ProtoMessage() {}

func (x *GetPortfolioTrackerLandingPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortfolioTrackerLandingPageResponse.ProtoReflect.Descriptor instead.
func (*GetPortfolioTrackerLandingPageResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetPortfolioTrackerLandingPageResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetPortfolioTrackerLandingPageResponse) GetHeader() *PortFolioTrackerLandingPageHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetPortfolioTrackerLandingPageResponse) GetFixedComponents() []*PortfolioTrackerComponent {
	if x != nil {
		return x.FixedComponents
	}
	return nil
}

func (x *GetPortfolioTrackerLandingPageResponse) GetScrollableComponents() []*PortfolioTrackerComponent {
	if x != nil {
		return x.ScrollableComponents
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/secrets/service.proto.
func (x *GetPortfolioTrackerLandingPageResponse) GetFooter() *ui.IconTextComponent {
	if x != nil {
		return x.Footer
	}
	return nil
}

func (x *GetPortfolioTrackerLandingPageResponse) GetFooterComponents() []*ui.IconTextComponent {
	if x != nil {
		return x.FooterComponents
	}
	return nil
}

type GetPortfolioTrackerAssetDetailsPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// mandatory field
	// Asset for which we need the AssetDistribution page
	RequestParams *portfoliotracker.AssetDetailsPageRequestParams `protobuf:"bytes,2,opt,name=request_params,json=requestParams,proto3" json:"request_params,omitempty"`
}

func (x *GetPortfolioTrackerAssetDetailsPageRequest) Reset() {
	*x = GetPortfolioTrackerAssetDetailsPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPortfolioTrackerAssetDetailsPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortfolioTrackerAssetDetailsPageRequest) ProtoMessage() {}

func (x *GetPortfolioTrackerAssetDetailsPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortfolioTrackerAssetDetailsPageRequest.ProtoReflect.Descriptor instead.
func (*GetPortfolioTrackerAssetDetailsPageRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetPortfolioTrackerAssetDetailsPageRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetPortfolioTrackerAssetDetailsPageRequest) GetRequestParams() *portfoliotracker.AssetDetailsPageRequestParams {
	if x != nil {
		return x.RequestParams
	}
	return nil
}

type GetPortfolioTrackerAssetDetailsPageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20157&t=Q9eQjZjJg10nxoIA-4
	AssetDetails *PortfolioTrackerAssetDetails `protobuf:"bytes,2,opt,name=asset_details,json=assetDetails,proto3" json:"asset_details,omitempty"`
}

func (x *GetPortfolioTrackerAssetDetailsPageResponse) Reset() {
	*x = GetPortfolioTrackerAssetDetailsPageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPortfolioTrackerAssetDetailsPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortfolioTrackerAssetDetailsPageResponse) ProtoMessage() {}

func (x *GetPortfolioTrackerAssetDetailsPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortfolioTrackerAssetDetailsPageResponse.ProtoReflect.Descriptor instead.
func (*GetPortfolioTrackerAssetDetailsPageResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetPortfolioTrackerAssetDetailsPageResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetPortfolioTrackerAssetDetailsPageResponse) GetAssetDetails() *PortfolioTrackerAssetDetails {
	if x != nil {
		return x.AssetDetails
	}
	return nil
}

type GetSecretSummariesResponse_ShowHideComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShowButton *ui.IconTextComponent `protobuf:"bytes,1,opt,name=show_button,json=showButton,proto3" json:"show_button,omitempty"`
	HideButton *ui.IconTextComponent `protobuf:"bytes,2,opt,name=hide_button,json=hideButton,proto3" json:"hide_button,omitempty"`
}

func (x *GetSecretSummariesResponse_ShowHideComponent) Reset() {
	*x = GetSecretSummariesResponse_ShowHideComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSecretSummariesResponse_ShowHideComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretSummariesResponse_ShowHideComponent) ProtoMessage() {}

func (x *GetSecretSummariesResponse_ShowHideComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretSummariesResponse_ShowHideComponent.ProtoReflect.Descriptor instead.
func (*GetSecretSummariesResponse_ShowHideComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *GetSecretSummariesResponse_ShowHideComponent) GetShowButton() *ui.IconTextComponent {
	if x != nil {
		return x.ShowButton
	}
	return nil
}

func (x *GetSecretSummariesResponse_ShowHideComponent) GetHideButton() *ui.IconTextComponent {
	if x != nil {
		return x.HideButton
	}
	return nil
}

var File_api_frontend_insights_secrets_service_proto protoreflect.FileDescriptor

var file_api_frontend_insights_secrets_service_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x44, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x59, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2f, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f,
	0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xc6, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x40, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xa4, 0x02, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6b, 0x0a, 0x18, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x16, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x58, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f,
	0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x10, 0x22, 0xfd, 0x02, 0x0a, 0x16, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x48, 0x00, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x12, 0x4a, 0x0a, 0x11, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x10, 0x72, 0x65,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x71,
	0x0a, 0x10, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x1a, 0x42, 0x0a, 0x14, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xb6, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x54,
	0x6f, 0x46, 0x61, 0x76, 0x6f, 0x75, 0x72, 0x69, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x64, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x54, 0x6f, 0x46, 0x61, 0x76, 0x6f, 0x75, 0x72, 0x69, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xab, 0x01, 0x0a, 0x1d, 0x41,
	0x64, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x54, 0x6f, 0x46, 0x61, 0x76, 0x6f, 0x75, 0x72,
	0x69, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x48,
	0x0a, 0x0e, 0x66, 0x61, 0x76, 0x6f, 0x75, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x66, 0x61, 0x76, 0x6f, 0x75,
	0x72, 0x69, 0x74, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x6d, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xcb, 0x05, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x37, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x71, 0x0a, 0x10, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x68, 0x6f,
	0x77, 0x48, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e,
	0x73, 0x68, 0x6f, 0x77, 0x48, 0x69, 0x64, 0x65, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x3d,
	0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x1a, 0x9b, 0x01,
	0x0a, 0x11, 0x53, 0x68, 0x6f, 0x77, 0x48, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0b, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x62, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x73, 0x68, 0x6f,
	0x77, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0b, 0x68, 0x69, 0x64, 0x65, 0x5f,
	0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x0a, 0x68, 0x69, 0x64, 0x65, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x22, 0x58, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x10, 0x22, 0x4f, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xbe, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5c, 0x0a, 0x13, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x11, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x22, 0x70, 0x0a, 0x1b, 0x57, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xee, 0x02, 0x0a, 0x1c, 0x57, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x06,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x67, 0x0a, 0x16, 0x77, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52, 0x14, 0x77, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x4a, 0x0a, 0x11, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x10, 0x72, 0x65,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x0a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x72, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xcf,
	0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x50, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x77, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x5a, 0x0a, 0x12, 0x62, 0x67, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x61,
	0x72, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x47, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x10,
	0x62, 0x67, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x47, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74,
	0x22, 0x81, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x46,
	0x6f, 0x6f, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x34,
	0x0a, 0x16, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9e, 0x04, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x62, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x42,
	0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x39, 0x0a, 0x0b, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0a,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x46, 0x0a, 0x10, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x73, 0x12, 0x51, 0x0a, 0x16, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x14, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x22, 0x63, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x9b, 0x04, 0x0a, 0x26, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5f, 0x0a,
	0x10, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x69,
	0x0a, 0x15, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x14, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x06, 0x66, 0x6f, 0x6f,
	0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x11, 0x66, 0x6f, 0x6f, 0x74,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xf5, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74,
	0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x8a, 0x01, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x63, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0xcd, 0x01, 0x0a, 0x2b, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x5c, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x32, 0xed, 0x0b, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x12, 0x99, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x12, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0xf0, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0xa0, 0x01, 0x0a, 0x15, 0x41, 0x64, 0x64,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x54, 0x6f, 0x46, 0x61, 0x76, 0x6f, 0x75, 0x72, 0x69, 0x74,
	0x65, 0x73, 0x12, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41,
	0x64, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x54, 0x6f, 0x46, 0x61, 0x76, 0x6f, 0x75, 0x72,
	0x69, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x54, 0x6f, 0x46, 0x61, 0x76, 0x6f, 0x75, 0x72, 0x69, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x97, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9d, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x12, 0x36,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa6, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x12, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88,
	0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa0,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x36, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7,
	0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x91, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x12, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x6f,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xbb, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72,
	0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0xca, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x67, 0x65, 0x12, 0x45, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x46, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_insights_secrets_service_proto_rawDescOnce sync.Once
	file_api_frontend_insights_secrets_service_proto_rawDescData = file_api_frontend_insights_secrets_service_proto_rawDesc
)

func file_api_frontend_insights_secrets_service_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_secrets_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_secrets_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_secrets_service_proto_rawDescData)
	})
	return file_api_frontend_insights_secrets_service_proto_rawDescData
}

var file_api_frontend_insights_secrets_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_frontend_insights_secrets_service_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_api_frontend_insights_secrets_service_proto_goTypes = []interface{}{
	(GetSecretAnalyserResponse_Status)(0),               // 0: frontend.insights.secrets.GetSecretAnalyserResponse.Status
	(GetSecretSummariesResponse_Status)(0),              // 1: frontend.insights.secrets.GetSecretSummariesResponse.Status
	(*GetSecretAnalyserRequest)(nil),                    // 2: frontend.insights.secrets.GetSecretAnalyserRequest
	(*GetSecretAnalyserResponse)(nil),                   // 3: frontend.insights.secrets.GetSecretAnalyserResponse
	(*SecretAnalyserResponse)(nil),                      // 4: frontend.insights.secrets.SecretAnalyserResponse
	(*AddSecretToFavouritesRequest)(nil),                // 5: frontend.insights.secrets.AddSecretToFavouritesRequest
	(*AddSecretToFavouritesResponse)(nil),               // 6: frontend.insights.secrets.AddSecretToFavouritesResponse
	(*GetSecretSummariesRequest)(nil),                   // 7: frontend.insights.secrets.GetSecretSummariesRequest
	(*GetSecretSummariesResponse)(nil),                  // 8: frontend.insights.secrets.GetSecretSummariesResponse
	(*GetSecretLibraryPageRequest)(nil),                 // 9: frontend.insights.secrets.GetSecretLibraryPageRequest
	(*GetSecretLibraryPageResponse)(nil),                // 10: frontend.insights.secrets.GetSecretLibraryPageResponse
	(*WealthAnalyserReportRequest)(nil),                 // 11: frontend.insights.secrets.WealthAnalyserReportRequest
	(*WealthAnalyserReportResponse)(nil),                // 12: frontend.insights.secrets.WealthAnalyserReportResponse
	(*GetWealthAnalyserWidgetRequest)(nil),              // 13: frontend.insights.secrets.GetWealthAnalyserWidgetRequest
	(*GetWealthAnalyserWidgetResponse)(nil),             // 14: frontend.insights.secrets.GetWealthAnalyserWidgetResponse
	(*GetSecretsFooterRequest)(nil),                     // 15: frontend.insights.secrets.GetSecretsFooterRequest
	(*GetSecretsFooterResponse)(nil),                    // 16: frontend.insights.secrets.GetSecretsFooterResponse
	(*GetPortfolioTrackerLandingPageRequest)(nil),       // 17: frontend.insights.secrets.GetPortfolioTrackerLandingPageRequest
	(*GetPortfolioTrackerLandingPageResponse)(nil),      // 18: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse
	(*GetPortfolioTrackerAssetDetailsPageRequest)(nil),  // 19: frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageRequest
	(*GetPortfolioTrackerAssetDetailsPageResponse)(nil), // 20: frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageResponse
	nil, // 21: frontend.insights.secrets.SecretAnalyserResponse.EventPropertiesEntry
	(*GetSecretSummariesResponse_ShowHideComponent)(nil),   // 22: frontend.insights.secrets.GetSecretSummariesResponse.ShowHideComponent
	(*header.RequestHeader)(nil),                           // 23: frontend.header.RequestHeader
	(*analyser.SelectedFilterValue)(nil),                   // 24: frontend.analyser.SelectedFilterValue
	(secrets.Provenance)(0),                                // 25: api.typesv2.deeplink_screen_option.insights.secrets.Provenance
	(*header.ResponseHeader)(nil),                          // 26: frontend.header.ResponseHeader
	(*SecretAnalyser)(nil),                                 // 27: frontend.insights.secrets.SecretAnalyser
	(*deeplink.Deeplink)(nil),                              // 28: frontend.deeplink.Deeplink
	(*AddSecretToFavouritesRequestParams)(nil),             // 29: frontend.insights.secrets.AddSecretToFavouritesRequestParams
	(*common.VisualElement)(nil),                           // 30: api.typesv2.common.VisualElement
	(*SecretSummary)(nil),                                  // 31: frontend.insights.secrets.SecretSummary
	(*ui.IconTextComponent)(nil),                           // 32: api.typesv2.ui.IconTextComponent
	(*SecretLibraryPage)(nil),                              // 33: frontend.insights.secrets.SecretLibraryPage
	(*WealthAnalyserReport)(nil),                           // 34: frontend.insights.secrets.WealthAnalyserReport
	(*ui.VerticalKeyValuePair)(nil),                        // 35: api.typesv2.ui.VerticalKeyValuePair
	(*wealthanalyser.AssetsAnalysisCard)(nil),              // 36: api.typesv2.ui.insights.wealthanalyser.AssetsAnalysisCard
	(*widget.LinearGradient)(nil),                          // 37: api.typesv2.common.ui.widget.LinearGradient
	(*ui.BackgroundColour)(nil),                            // 38: api.typesv2.ui.BackgroundColour
	(*common.Text)(nil),                                    // 39: api.typesv2.common.Text
	(*PortFolioTrackerLandingPageHeader)(nil),              // 40: frontend.insights.secrets.PortFolioTrackerLandingPageHeader
	(*PortfolioTrackerComponent)(nil),                      // 41: frontend.insights.secrets.PortfolioTrackerComponent
	(*portfoliotracker.AssetDetailsPageRequestParams)(nil), // 42: api.typesv2.deeplink_screen_option.insights.secrets.portfoliotracker.AssetDetailsPageRequestParams
	(*PortfolioTrackerAssetDetails)(nil),                   // 43: frontend.insights.secrets.PortfolioTrackerAssetDetails
}
var file_api_frontend_insights_secrets_service_proto_depIdxs = []int32{
	23, // 0: frontend.insights.secrets.GetSecretAnalyserRequest.req:type_name -> frontend.header.RequestHeader
	24, // 1: frontend.insights.secrets.GetSecretAnalyserRequest.filters:type_name -> frontend.analyser.SelectedFilterValue
	25, // 2: frontend.insights.secrets.GetSecretAnalyserRequest.provenance:type_name -> api.typesv2.deeplink_screen_option.insights.secrets.Provenance
	26, // 3: frontend.insights.secrets.GetSecretAnalyserResponse.resp_header:type_name -> frontend.header.ResponseHeader
	4,  // 4: frontend.insights.secrets.GetSecretAnalyserResponse.secret_analyser_response:type_name -> frontend.insights.secrets.SecretAnalyserResponse
	27, // 5: frontend.insights.secrets.SecretAnalyserResponse.secret_analyser:type_name -> frontend.insights.secrets.SecretAnalyser
	28, // 6: frontend.insights.secrets.SecretAnalyserResponse.redirect_deeplink:type_name -> frontend.deeplink.Deeplink
	21, // 7: frontend.insights.secrets.SecretAnalyserResponse.event_properties:type_name -> frontend.insights.secrets.SecretAnalyserResponse.EventPropertiesEntry
	23, // 8: frontend.insights.secrets.AddSecretToFavouritesRequest.req:type_name -> frontend.header.RequestHeader
	29, // 9: frontend.insights.secrets.AddSecretToFavouritesRequest.request_params:type_name -> frontend.insights.secrets.AddSecretToFavouritesRequestParams
	26, // 10: frontend.insights.secrets.AddSecretToFavouritesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	30, // 11: frontend.insights.secrets.AddSecretToFavouritesResponse.favourite_icon:type_name -> api.typesv2.common.VisualElement
	23, // 12: frontend.insights.secrets.GetSecretSummariesRequest.req:type_name -> frontend.header.RequestHeader
	26, // 13: frontend.insights.secrets.GetSecretSummariesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	31, // 14: frontend.insights.secrets.GetSecretSummariesResponse.secret_summaries:type_name -> frontend.insights.secrets.SecretSummary
	32, // 15: frontend.insights.secrets.GetSecretSummariesResponse.title:type_name -> api.typesv2.ui.IconTextComponent
	32, // 16: frontend.insights.secrets.GetSecretSummariesResponse.cta:type_name -> api.typesv2.ui.IconTextComponent
	22, // 17: frontend.insights.secrets.GetSecretSummariesResponse.show_hide_button:type_name -> frontend.insights.secrets.GetSecretSummariesResponse.ShowHideComponent
	32, // 18: frontend.insights.secrets.GetSecretSummariesResponse.subtitle:type_name -> api.typesv2.ui.IconTextComponent
	23, // 19: frontend.insights.secrets.GetSecretLibraryPageRequest.req:type_name -> frontend.header.RequestHeader
	26, // 20: frontend.insights.secrets.GetSecretLibraryPageResponse.resp_header:type_name -> frontend.header.ResponseHeader
	33, // 21: frontend.insights.secrets.GetSecretLibraryPageResponse.secret_landing_page:type_name -> frontend.insights.secrets.SecretLibraryPage
	23, // 22: frontend.insights.secrets.WealthAnalyserReportRequest.req:type_name -> frontend.header.RequestHeader
	26, // 23: frontend.insights.secrets.WealthAnalyserReportResponse.resp_header:type_name -> frontend.header.ResponseHeader
	34, // 24: frontend.insights.secrets.WealthAnalyserReportResponse.report:type_name -> frontend.insights.secrets.WealthAnalyserReport
	34, // 25: frontend.insights.secrets.WealthAnalyserReportResponse.wealth_analyser_report:type_name -> frontend.insights.secrets.WealthAnalyserReport
	28, // 26: frontend.insights.secrets.WealthAnalyserReportResponse.redirect_deeplink:type_name -> frontend.deeplink.Deeplink
	23, // 27: frontend.insights.secrets.GetWealthAnalyserWidgetRequest.req:type_name -> frontend.header.RequestHeader
	26, // 28: frontend.insights.secrets.GetWealthAnalyserWidgetResponse.resp_header:type_name -> frontend.header.ResponseHeader
	35, // 29: frontend.insights.secrets.GetWealthAnalyserWidgetResponse.header:type_name -> api.typesv2.ui.VerticalKeyValuePair
	36, // 30: frontend.insights.secrets.GetWealthAnalyserWidgetResponse.cards:type_name -> api.typesv2.ui.insights.wealthanalyser.AssetsAnalysisCard
	37, // 31: frontend.insights.secrets.GetWealthAnalyserWidgetResponse.bg_linear_gradient:type_name -> api.typesv2.common.ui.widget.LinearGradient
	23, // 32: frontend.insights.secrets.GetSecretsFooterRequest.req:type_name -> frontend.header.RequestHeader
	26, // 33: frontend.insights.secrets.GetSecretsFooterResponse.resp_header:type_name -> frontend.header.ResponseHeader
	32, // 34: frontend.insights.secrets.GetSecretsFooterResponse.back_button:type_name -> api.typesv2.ui.IconTextComponent
	32, // 35: frontend.insights.secrets.GetSecretsFooterResponse.next_button:type_name -> api.typesv2.ui.IconTextComponent
	38, // 36: frontend.insights.secrets.GetSecretsFooterResponse.bg_color:type_name -> api.typesv2.ui.BackgroundColour
	39, // 37: frontend.insights.secrets.GetSecretsFooterResponse.index_label:type_name -> api.typesv2.common.Text
	28, // 38: frontend.insights.secrets.GetSecretsFooterResponse.secret_deeplinks:type_name -> frontend.deeplink.Deeplink
	28, // 39: frontend.insights.secrets.GetSecretsFooterResponse.flow_completion_screen:type_name -> frontend.deeplink.Deeplink
	23, // 40: frontend.insights.secrets.GetPortfolioTrackerLandingPageRequest.req:type_name -> frontend.header.RequestHeader
	26, // 41: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse.resp_header:type_name -> frontend.header.ResponseHeader
	40, // 42: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse.header:type_name -> frontend.insights.secrets.PortFolioTrackerLandingPageHeader
	41, // 43: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse.fixed_components:type_name -> frontend.insights.secrets.PortfolioTrackerComponent
	41, // 44: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse.scrollable_components:type_name -> frontend.insights.secrets.PortfolioTrackerComponent
	32, // 45: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse.footer:type_name -> api.typesv2.ui.IconTextComponent
	32, // 46: frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse.footer_components:type_name -> api.typesv2.ui.IconTextComponent
	23, // 47: frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageRequest.req:type_name -> frontend.header.RequestHeader
	42, // 48: frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageRequest.request_params:type_name -> api.typesv2.deeplink_screen_option.insights.secrets.portfoliotracker.AssetDetailsPageRequestParams
	26, // 49: frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageResponse.resp_header:type_name -> frontend.header.ResponseHeader
	43, // 50: frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageResponse.asset_details:type_name -> frontend.insights.secrets.PortfolioTrackerAssetDetails
	32, // 51: frontend.insights.secrets.GetSecretSummariesResponse.ShowHideComponent.show_button:type_name -> api.typesv2.ui.IconTextComponent
	32, // 52: frontend.insights.secrets.GetSecretSummariesResponse.ShowHideComponent.hide_button:type_name -> api.typesv2.ui.IconTextComponent
	2,  // 53: frontend.insights.secrets.Secrets.GetSecretAnalyser:input_type -> frontend.insights.secrets.GetSecretAnalyserRequest
	5,  // 54: frontend.insights.secrets.Secrets.AddSecretToFavourites:input_type -> frontend.insights.secrets.AddSecretToFavouritesRequest
	7,  // 55: frontend.insights.secrets.Secrets.GetSecretSummaries:input_type -> frontend.insights.secrets.GetSecretSummariesRequest
	9,  // 56: frontend.insights.secrets.Secrets.GetSecretLibraryPage:input_type -> frontend.insights.secrets.GetSecretLibraryPageRequest
	13, // 57: frontend.insights.secrets.Secrets.GetWealthAnalyserWidget:input_type -> frontend.insights.secrets.GetWealthAnalyserWidgetRequest
	11, // 58: frontend.insights.secrets.Secrets.GetWealthAnalyserReport:input_type -> frontend.insights.secrets.WealthAnalyserReportRequest
	15, // 59: frontend.insights.secrets.Secrets.GetSecretsFooter:input_type -> frontend.insights.secrets.GetSecretsFooterRequest
	17, // 60: frontend.insights.secrets.Secrets.GetPortfolioTrackerLandingPage:input_type -> frontend.insights.secrets.GetPortfolioTrackerLandingPageRequest
	19, // 61: frontend.insights.secrets.Secrets.GetPortfolioTrackerAssetDetailsPage:input_type -> frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageRequest
	3,  // 62: frontend.insights.secrets.Secrets.GetSecretAnalyser:output_type -> frontend.insights.secrets.GetSecretAnalyserResponse
	6,  // 63: frontend.insights.secrets.Secrets.AddSecretToFavourites:output_type -> frontend.insights.secrets.AddSecretToFavouritesResponse
	8,  // 64: frontend.insights.secrets.Secrets.GetSecretSummaries:output_type -> frontend.insights.secrets.GetSecretSummariesResponse
	10, // 65: frontend.insights.secrets.Secrets.GetSecretLibraryPage:output_type -> frontend.insights.secrets.GetSecretLibraryPageResponse
	14, // 66: frontend.insights.secrets.Secrets.GetWealthAnalyserWidget:output_type -> frontend.insights.secrets.GetWealthAnalyserWidgetResponse
	12, // 67: frontend.insights.secrets.Secrets.GetWealthAnalyserReport:output_type -> frontend.insights.secrets.WealthAnalyserReportResponse
	16, // 68: frontend.insights.secrets.Secrets.GetSecretsFooter:output_type -> frontend.insights.secrets.GetSecretsFooterResponse
	18, // 69: frontend.insights.secrets.Secrets.GetPortfolioTrackerLandingPage:output_type -> frontend.insights.secrets.GetPortfolioTrackerLandingPageResponse
	20, // 70: frontend.insights.secrets.Secrets.GetPortfolioTrackerAssetDetailsPage:output_type -> frontend.insights.secrets.GetPortfolioTrackerAssetDetailsPageResponse
	62, // [62:71] is the sub-list for method output_type
	53, // [53:62] is the sub-list for method input_type
	53, // [53:53] is the sub-list for extension type_name
	53, // [53:53] is the sub-list for extension extendee
	0,  // [0:53] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_secrets_service_proto_init() }
func file_api_frontend_insights_secrets_service_proto_init() {
	if File_api_frontend_insights_secrets_service_proto != nil {
		return
	}
	file_api_frontend_insights_secrets_portfolio_tracker_proto_init()
	file_api_frontend_insights_secrets_portfolio_tracker_assets_details_proto_init()
	file_api_frontend_insights_secrets_secret_analyser_proto_init()
	file_api_frontend_insights_secrets_secret_landing_page_proto_init()
	file_api_frontend_insights_secrets_secret_summary_proto_init()
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_insights_secrets_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretAnalyserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretAnalyserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretAnalyserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddSecretToFavouritesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddSecretToFavouritesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretSummariesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretSummariesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretLibraryPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretLibraryPageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthAnalyserReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthAnalyserReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWealthAnalyserWidgetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWealthAnalyserWidgetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretsFooterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretsFooterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPortfolioTrackerLandingPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPortfolioTrackerLandingPageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPortfolioTrackerAssetDetailsPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPortfolioTrackerAssetDetailsPageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSecretSummariesResponse_ShowHideComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_insights_secrets_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*SecretAnalyserResponse_SecretAnalyser)(nil),
		(*SecretAnalyserResponse_RedirectDeeplink)(nil),
	}
	file_api_frontend_insights_secrets_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*WealthAnalyserReportResponse_WealthAnalyserReport)(nil),
		(*WealthAnalyserReportResponse_RedirectDeeplink)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_secrets_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_insights_secrets_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_secrets_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_insights_secrets_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_insights_secrets_service_proto_msgTypes,
	}.Build()
	File_api_frontend_insights_secrets_service_proto = out.File
	file_api_frontend_insights_secrets_service_proto_rawDesc = nil
	file_api_frontend_insights_secrets_service_proto_goTypes = nil
	file_api_frontend_insights_secrets_service_proto_depIdxs = nil
}
