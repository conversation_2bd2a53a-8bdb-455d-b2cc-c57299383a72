// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/secrets/wealth_analyser_report.proto

package secrets

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on WealthAnalyserReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthAnalyserReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthAnalyserReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthAnalyserReportMultiError, or nil if none found.
func (m *WealthAnalyserReport) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthAnalyserReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNavigationItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthAnalyserReportValidationError{
						field:  fmt.Sprintf("NavigationItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthAnalyserReportValidationError{
						field:  fmt.Sprintf("NavigationItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthAnalyserReportValidationError{
					field:  fmt.Sprintf("NavigationItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDashboard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportValidationError{
					field:  "Dashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportValidationError{
					field:  "Dashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportValidationError{
				field:  "Dashboard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecretsCollection() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthAnalyserReportValidationError{
						field:  fmt.Sprintf("SecretsCollection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthAnalyserReportValidationError{
						field:  fmt.Sprintf("SecretsCollection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthAnalyserReportValidationError{
					field:  fmt.Sprintf("SecretsCollection[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRefreshBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportValidationError{
					field:  "RefreshBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportValidationError{
					field:  "RefreshBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRefreshBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportValidationError{
				field:  "RefreshBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventProperties

	if len(errors) > 0 {
		return WealthAnalyserReportMultiError(errors)
	}

	return nil
}

// WealthAnalyserReportMultiError is an error wrapping multiple validation
// errors returned by WealthAnalyserReport.ValidateAll() if the designated
// constraints aren't met.
type WealthAnalyserReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthAnalyserReportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthAnalyserReportMultiError) AllErrors() []error { return m }

// WealthAnalyserReportValidationError is the validation error returned by
// WealthAnalyserReport.Validate if the designated constraints aren't met.
type WealthAnalyserReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthAnalyserReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthAnalyserReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthAnalyserReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthAnalyserReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthAnalyserReportValidationError) ErrorName() string {
	return "WealthAnalyserReportValidationError"
}

// Error satisfies the builtin error interface
func (e WealthAnalyserReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthAnalyserReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthAnalyserReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthAnalyserReportValidationError{}

// Validate checks the field values on WealthAnalyserNavigationItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthAnalyserNavigationItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthAnalyserNavigationItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthAnalyserNavigationItemMultiError, or nil if none found.
func (m *WealthAnalyserNavigationItem) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthAnalyserNavigationItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.NavigationItem.(type) {
	case *WealthAnalyserNavigationItem_LogoNavigation:
		if v == nil {
			err := WealthAnalyserNavigationItemValidationError{
				field:  "NavigationItem",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLogoNavigation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthAnalyserNavigationItemValidationError{
						field:  "LogoNavigation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthAnalyserNavigationItemValidationError{
						field:  "LogoNavigation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLogoNavigation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthAnalyserNavigationItemValidationError{
					field:  "LogoNavigation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WealthAnalyserNavigationItem_Cta:
		if v == nil {
			err := WealthAnalyserNavigationItemValidationError{
				field:  "NavigationItem",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCta()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthAnalyserNavigationItemValidationError{
						field:  "Cta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthAnalyserNavigationItemValidationError{
						field:  "Cta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthAnalyserNavigationItemValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WealthAnalyserNavigationItemMultiError(errors)
	}

	return nil
}

// WealthAnalyserNavigationItemMultiError is an error wrapping multiple
// validation errors returned by WealthAnalyserNavigationItem.ValidateAll() if
// the designated constraints aren't met.
type WealthAnalyserNavigationItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthAnalyserNavigationItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthAnalyserNavigationItemMultiError) AllErrors() []error { return m }

// WealthAnalyserNavigationItemValidationError is the validation error returned
// by WealthAnalyserNavigationItem.Validate if the designated constraints
// aren't met.
type WealthAnalyserNavigationItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthAnalyserNavigationItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthAnalyserNavigationItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthAnalyserNavigationItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthAnalyserNavigationItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthAnalyserNavigationItemValidationError) ErrorName() string {
	return "WealthAnalyserNavigationItemValidationError"
}

// Error satisfies the builtin error interface
func (e WealthAnalyserNavigationItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthAnalyserNavigationItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthAnalyserNavigationItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthAnalyserNavigationItemValidationError{}

// Validate checks the field values on ConnectedAccountsNavigationComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ConnectedAccountsNavigationComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectedAccountsNavigationComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConnectedAccountsNavigationComponentMultiError, or nil if none found.
func (m *ConnectedAccountsNavigationComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectedAccountsNavigationComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLogos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConnectedAccountsNavigationComponentValidationError{
						field:  fmt.Sprintf("Logos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConnectedAccountsNavigationComponentValidationError{
						field:  fmt.Sprintf("Logos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConnectedAccountsNavigationComponentValidationError{
					field:  fmt.Sprintf("Logos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMoreText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectedAccountsNavigationComponentValidationError{
					field:  "MoreText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectedAccountsNavigationComponentValidationError{
					field:  "MoreText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoreText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectedAccountsNavigationComponentValidationError{
				field:  "MoreText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConnectedAccountsNavigationComponentMultiError(errors)
	}

	return nil
}

// ConnectedAccountsNavigationComponentMultiError is an error wrapping multiple
// validation errors returned by
// ConnectedAccountsNavigationComponent.ValidateAll() if the designated
// constraints aren't met.
type ConnectedAccountsNavigationComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectedAccountsNavigationComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectedAccountsNavigationComponentMultiError) AllErrors() []error { return m }

// ConnectedAccountsNavigationComponentValidationError is the validation error
// returned by ConnectedAccountsNavigationComponent.Validate if the designated
// constraints aren't met.
type ConnectedAccountsNavigationComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectedAccountsNavigationComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectedAccountsNavigationComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectedAccountsNavigationComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectedAccountsNavigationComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectedAccountsNavigationComponentValidationError) ErrorName() string {
	return "ConnectedAccountsNavigationComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectedAccountsNavigationComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectedAccountsNavigationComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectedAccountsNavigationComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectedAccountsNavigationComponentValidationError{}

// Validate checks the field values on WealthAnalyserReportDashboard with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthAnalyserReportDashboard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthAnalyserReportDashboard with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WealthAnalyserReportDashboardMultiError, or nil if none found.
func (m *WealthAnalyserReportDashboard) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthAnalyserReportDashboard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopCenterValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "TopCenterValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "TopCenterValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopCenterValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "TopCenterValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftBottomValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "LeftBottomValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "LeftBottomValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftBottomValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "LeftBottomValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCentreBottomValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "CentreBottomValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "CentreBottomValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCentreBottomValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "CentreBottomValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightBottomValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "RightBottomValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "RightBottomValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightBottomValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "RightBottomValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DividerColor

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetSeeAllCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "SeeAllCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "SeeAllCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSeeAllCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "SeeAllCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastSync()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "LastSync",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "LastSync",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastSync()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "LastSync",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventProperties

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportDashboardValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportDashboardValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WealthAnalyserReportDashboardMultiError(errors)
	}

	return nil
}

// WealthAnalyserReportDashboardMultiError is an error wrapping multiple
// validation errors returned by WealthAnalyserReportDashboard.ValidateAll()
// if the designated constraints aren't met.
type WealthAnalyserReportDashboardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthAnalyserReportDashboardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthAnalyserReportDashboardMultiError) AllErrors() []error { return m }

// WealthAnalyserReportDashboardValidationError is the validation error
// returned by WealthAnalyserReportDashboard.Validate if the designated
// constraints aren't met.
type WealthAnalyserReportDashboardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthAnalyserReportDashboardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthAnalyserReportDashboardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthAnalyserReportDashboardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthAnalyserReportDashboardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthAnalyserReportDashboardValidationError) ErrorName() string {
	return "WealthAnalyserReportDashboardValidationError"
}

// Error satisfies the builtin error interface
func (e WealthAnalyserReportDashboardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthAnalyserReportDashboard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthAnalyserReportDashboardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthAnalyserReportDashboardValidationError{}
