// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/secrets/wealth_analyser_report.proto

package secrets

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39739&t=v83a5Lp6TOWOy1bL-1
type WealthAnalyserReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Items to be shown on right side of the navigation bar
	NavigationItems []*WealthAnalyserNavigationItem `protobuf:"bytes,1,rep,name=navigation_items,json=navigationItems,proto3" json:"navigation_items,omitempty"`
	// Represents the dashboard summary of the asset report
	Dashboard *WealthAnalyserReportDashboard `protobuf:"bytes,2,opt,name=dashboard,proto3" json:"dashboard,omitempty"`
	// This represents multiple analysis collections which made the report
	SecretsCollection []*SecretLibraryCollection `protobuf:"bytes,3,rep,name=secrets_collection,json=secretsCollection,proto3" json:"secrets_collection,omitempty"`
	// this is the footer on the asset report for compliance info etc
	Footer *ui.IconTextComponent `protobuf:"bytes,4,opt,name=footer,proto3" json:"footer,omitempty"`
	// this is the refresh banner for the wealth analyser report to let the user refresh the asset
	RefreshBanner *ActionableBanner `protobuf:"bytes,5,opt,name=refresh_banner,json=refreshBanner,proto3" json:"refresh_banner,omitempty"`
	// these are the properties we need from backend to send for report page like report_type, connection_state
	// events sheet https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit?gid=*********#gid=*********
	EventProperties map[string]string `protobuf:"bytes,8,rep,name=event_properties,json=eventProperties,proto3" json:"event_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WealthAnalyserReport) Reset() {
	*x = WealthAnalyserReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthAnalyserReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthAnalyserReport) ProtoMessage() {}

func (x *WealthAnalyserReport) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthAnalyserReport.ProtoReflect.Descriptor instead.
func (*WealthAnalyserReport) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescGZIP(), []int{0}
}

func (x *WealthAnalyserReport) GetNavigationItems() []*WealthAnalyserNavigationItem {
	if x != nil {
		return x.NavigationItems
	}
	return nil
}

func (x *WealthAnalyserReport) GetDashboard() *WealthAnalyserReportDashboard {
	if x != nil {
		return x.Dashboard
	}
	return nil
}

func (x *WealthAnalyserReport) GetSecretsCollection() []*SecretLibraryCollection {
	if x != nil {
		return x.SecretsCollection
	}
	return nil
}

func (x *WealthAnalyserReport) GetFooter() *ui.IconTextComponent {
	if x != nil {
		return x.Footer
	}
	return nil
}

func (x *WealthAnalyserReport) GetRefreshBanner() *ActionableBanner {
	if x != nil {
		return x.RefreshBanner
	}
	return nil
}

func (x *WealthAnalyserReport) GetEventProperties() map[string]string {
	if x != nil {
		return x.EventProperties
	}
	return nil
}

type WealthAnalyserNavigationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to NavigationItem:
	//	*WealthAnalyserNavigationItem_LogoNavigation
	//	*WealthAnalyserNavigationItem_Cta
	NavigationItem isWealthAnalyserNavigationItem_NavigationItem `protobuf_oneof:"navigation_item"`
}

func (x *WealthAnalyserNavigationItem) Reset() {
	*x = WealthAnalyserNavigationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthAnalyserNavigationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthAnalyserNavigationItem) ProtoMessage() {}

func (x *WealthAnalyserNavigationItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthAnalyserNavigationItem.ProtoReflect.Descriptor instead.
func (*WealthAnalyserNavigationItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescGZIP(), []int{1}
}

func (m *WealthAnalyserNavigationItem) GetNavigationItem() isWealthAnalyserNavigationItem_NavigationItem {
	if m != nil {
		return m.NavigationItem
	}
	return nil
}

func (x *WealthAnalyserNavigationItem) GetLogoNavigation() *ConnectedAccountsNavigationComponent {
	if x, ok := x.GetNavigationItem().(*WealthAnalyserNavigationItem_LogoNavigation); ok {
		return x.LogoNavigation
	}
	return nil
}

func (x *WealthAnalyserNavigationItem) GetCta() *ui.IconTextComponent {
	if x, ok := x.GetNavigationItem().(*WealthAnalyserNavigationItem_Cta); ok {
		return x.Cta
	}
	return nil
}

type isWealthAnalyserNavigationItem_NavigationItem interface {
	isWealthAnalyserNavigationItem_NavigationItem()
}

type WealthAnalyserNavigationItem_LogoNavigation struct {
	LogoNavigation *ConnectedAccountsNavigationComponent `protobuf:"bytes,1,opt,name=logo_navigation,json=logoNavigation,proto3,oneof"`
}

type WealthAnalyserNavigationItem_Cta struct {
	// this can be the more icon with three vertical dots or + Add More Assets etc
	Cta *ui.IconTextComponent `protobuf:"bytes,2,opt,name=cta,proto3,oneof"`
}

func (*WealthAnalyserNavigationItem_LogoNavigation) isWealthAnalyserNavigationItem_NavigationItem() {}

func (*WealthAnalyserNavigationItem_Cta) isWealthAnalyserNavigationItem_NavigationItem() {}

//figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39739&t=v83a5Lp6TOWOy1bL-1
type ConnectedAccountsNavigationComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this will be the logos of different accounts connected
	Logos []*ui.IconTextComponent `protobuf:"bytes,1,rep,name=logos,proto3" json:"logos,omitempty"`
	// if logos are more than tw0 count of remaining logos text as '+n' where n is the count of remaining logos
	MoreText *common.Text `protobuf:"bytes,2,opt,name=more_text,json=moreText,proto3" json:"more_text,omitempty"`
}

func (x *ConnectedAccountsNavigationComponent) Reset() {
	*x = ConnectedAccountsNavigationComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectedAccountsNavigationComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectedAccountsNavigationComponent) ProtoMessage() {}

func (x *ConnectedAccountsNavigationComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectedAccountsNavigationComponent.ProtoReflect.Descriptor instead.
func (*ConnectedAccountsNavigationComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescGZIP(), []int{2}
}

func (x *ConnectedAccountsNavigationComponent) GetLogos() []*ui.IconTextComponent {
	if x != nil {
		return x.Logos
	}
	return nil
}

func (x *ConnectedAccountsNavigationComponent) GetMoreText() *common.Text {
	if x != nil {
		return x.MoreText
	}
	return nil
}

//figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39739&t=v83a5Lp6TOWOy1bL-1
type WealthAnalyserReportDashboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Client should handle all following cases and corresponding separators according to the figma
	// - All 4 values
	// - Three values include 'top_centre' value
	// - 'top_centre' value + either left/right bottom value
	// - Only 'top_centre' value
	// Rest of cases should not occur and we would be adding validations for the same
	TopCenterValue    *ui.VerticalKeyValuePair `protobuf:"bytes,1,opt,name=top_center_value,json=topCenterValue,proto3" json:"top_center_value,omitempty"`
	LeftBottomValue   *ui.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=left_bottom_value,json=leftBottomValue,proto3" json:"left_bottom_value,omitempty"`
	CentreBottomValue *ui.VerticalKeyValuePair `protobuf:"bytes,3,opt,name=centre_bottom_value,json=centreBottomValue,proto3" json:"centre_bottom_value,omitempty"`
	RightBottomValue  *ui.VerticalKeyValuePair `protobuf:"bytes,4,opt,name=right_bottom_value,json=rightBottomValue,proto3" json:"right_bottom_value,omitempty"`
	// Color of the divider line
	// this will be the divider lines between multiple values as shown in figma
	DividerColor string `protobuf:"bytes,5,opt,name=divider_color,json=dividerColor,proto3" json:"divider_color,omitempty"`
	// bg color of the dashboard
	BgColor string `protobuf:"bytes,6,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// This is the cta for see_all to see more info about summary e.g see all funds, see all accounts etc based on asset type
	SeeAllCta *ui.IconTextComponent `protobuf:"bytes,7,opt,name=see_all_cta,json=seeAllCta,proto3" json:"see_all_cta,omitempty"`
	// This is to show last sync time of the report
	LastSync *ui.IconTextComponent `protobuf:"bytes,8,opt,name=last_sync,json=lastSync,proto3" json:"last_sync,omitempty"`
	// these are the properties of dashboard we need to send for analytics
	// events sheet https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit?gid=*********#gid=*********
	EventProperties map[string]string `protobuf:"bytes,9,rep,name=event_properties,json=eventProperties,proto3" json:"event_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// this is the footer to be shown below the dashboard summary to show powered by epifi wealth
	Footer *ui.IconTextComponent `protobuf:"bytes,10,opt,name=footer,proto3" json:"footer,omitempty"`
}

func (x *WealthAnalyserReportDashboard) Reset() {
	*x = WealthAnalyserReportDashboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthAnalyserReportDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthAnalyserReportDashboard) ProtoMessage() {}

func (x *WealthAnalyserReportDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthAnalyserReportDashboard.ProtoReflect.Descriptor instead.
func (*WealthAnalyserReportDashboard) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescGZIP(), []int{3}
}

func (x *WealthAnalyserReportDashboard) GetTopCenterValue() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.TopCenterValue
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetLeftBottomValue() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.LeftBottomValue
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetCentreBottomValue() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.CentreBottomValue
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetRightBottomValue() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.RightBottomValue
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetDividerColor() string {
	if x != nil {
		return x.DividerColor
	}
	return ""
}

func (x *WealthAnalyserReportDashboard) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *WealthAnalyserReportDashboard) GetSeeAllCta() *ui.IconTextComponent {
	if x != nil {
		return x.SeeAllCta
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetLastSync() *ui.IconTextComponent {
	if x != nil {
		return x.LastSync
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetEventProperties() map[string]string {
	if x != nil {
		return x.EventProperties
	}
	return nil
}

func (x *WealthAnalyserReportDashboard) GetFooter() *ui.IconTextComponent {
	if x != nil {
		return x.Footer
	}
	return nil
}

var File_api_frontend_insights_secrets_wealth_analyser_report_proto protoreflect.FileDescriptor

var file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf9, 0x04, 0x0a,
	0x14, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x62, 0x0a, 0x10, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x56, 0x0a, 0x09, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0x61, 0x0a, 0x12, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x11, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x12,
	0x52, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x12, 0x6f, 0x0a, 0x10, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x1a, 0x42, 0x0a, 0x14, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd4, 0x01, 0x0a, 0x1c, 0x57, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x6a, 0x0a, 0x0f, 0x6c, 0x6f, 0x67,
	0x6f, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x6f, 0x67, 0x6f, 0x4e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x03, 0x63, 0x74, 0x61, 0x42, 0x11, 0x0a, 0x0f,
	0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x22,
	0x96, 0x01, 0x0a, 0x24, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x6f,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x6f,
	0x73, 0x12, 0x35, 0x0a, 0x09, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08,
	0x6d, 0x6f, 0x72, 0x65, 0x54, 0x65, 0x78, 0x74, 0x22, 0xa7, 0x06, 0x0a, 0x1d, 0x57, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x4e, 0x0a, 0x10, 0x74, 0x6f,
	0x70, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0e, 0x74, 0x6f, 0x70, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x50, 0x0a, 0x11, 0x6c, 0x65,
	0x66, 0x74, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b,
	0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0f, 0x6c, 0x65, 0x66,
	0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x54, 0x0a, 0x13,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x65, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69,
	0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52,
	0x11, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x65, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x62, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x52, 0x10, 0x72, 0x69, 0x67, 0x68, 0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f,
	0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64,
	0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x0b, 0x73, 0x65, 0x65, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09,
	0x73, 0x65, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x09, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x78, 0x0a, 0x10, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x1a, 0x42,
	0x0a, 0x14, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescOnce sync.Once
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescData = file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDesc
)

func file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescData)
	})
	return file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDescData
}

var file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_frontend_insights_secrets_wealth_analyser_report_proto_goTypes = []interface{}{
	(*WealthAnalyserReport)(nil),                 // 0: frontend.insights.secrets.WealthAnalyserReport
	(*WealthAnalyserNavigationItem)(nil),         // 1: frontend.insights.secrets.WealthAnalyserNavigationItem
	(*ConnectedAccountsNavigationComponent)(nil), // 2: frontend.insights.secrets.ConnectedAccountsNavigationComponent
	(*WealthAnalyserReportDashboard)(nil),        // 3: frontend.insights.secrets.WealthAnalyserReportDashboard
	nil,                                          // 4: frontend.insights.secrets.WealthAnalyserReport.EventPropertiesEntry
	nil,                                          // 5: frontend.insights.secrets.WealthAnalyserReportDashboard.EventPropertiesEntry
	(*SecretLibraryCollection)(nil),              // 6: frontend.insights.secrets.SecretLibraryCollection
	(*ui.IconTextComponent)(nil),                 // 7: api.typesv2.ui.IconTextComponent
	(*ActionableBanner)(nil),                     // 8: frontend.insights.secrets.ActionableBanner
	(*common.Text)(nil),                          // 9: api.typesv2.common.Text
	(*ui.VerticalKeyValuePair)(nil),              // 10: api.typesv2.ui.VerticalKeyValuePair
}
var file_api_frontend_insights_secrets_wealth_analyser_report_proto_depIdxs = []int32{
	1,  // 0: frontend.insights.secrets.WealthAnalyserReport.navigation_items:type_name -> frontend.insights.secrets.WealthAnalyserNavigationItem
	3,  // 1: frontend.insights.secrets.WealthAnalyserReport.dashboard:type_name -> frontend.insights.secrets.WealthAnalyserReportDashboard
	6,  // 2: frontend.insights.secrets.WealthAnalyserReport.secrets_collection:type_name -> frontend.insights.secrets.SecretLibraryCollection
	7,  // 3: frontend.insights.secrets.WealthAnalyserReport.footer:type_name -> api.typesv2.ui.IconTextComponent
	8,  // 4: frontend.insights.secrets.WealthAnalyserReport.refresh_banner:type_name -> frontend.insights.secrets.ActionableBanner
	4,  // 5: frontend.insights.secrets.WealthAnalyserReport.event_properties:type_name -> frontend.insights.secrets.WealthAnalyserReport.EventPropertiesEntry
	2,  // 6: frontend.insights.secrets.WealthAnalyserNavigationItem.logo_navigation:type_name -> frontend.insights.secrets.ConnectedAccountsNavigationComponent
	7,  // 7: frontend.insights.secrets.WealthAnalyserNavigationItem.cta:type_name -> api.typesv2.ui.IconTextComponent
	7,  // 8: frontend.insights.secrets.ConnectedAccountsNavigationComponent.logos:type_name -> api.typesv2.ui.IconTextComponent
	9,  // 9: frontend.insights.secrets.ConnectedAccountsNavigationComponent.more_text:type_name -> api.typesv2.common.Text
	10, // 10: frontend.insights.secrets.WealthAnalyserReportDashboard.top_center_value:type_name -> api.typesv2.ui.VerticalKeyValuePair
	10, // 11: frontend.insights.secrets.WealthAnalyserReportDashboard.left_bottom_value:type_name -> api.typesv2.ui.VerticalKeyValuePair
	10, // 12: frontend.insights.secrets.WealthAnalyserReportDashboard.centre_bottom_value:type_name -> api.typesv2.ui.VerticalKeyValuePair
	10, // 13: frontend.insights.secrets.WealthAnalyserReportDashboard.right_bottom_value:type_name -> api.typesv2.ui.VerticalKeyValuePair
	7,  // 14: frontend.insights.secrets.WealthAnalyserReportDashboard.see_all_cta:type_name -> api.typesv2.ui.IconTextComponent
	7,  // 15: frontend.insights.secrets.WealthAnalyserReportDashboard.last_sync:type_name -> api.typesv2.ui.IconTextComponent
	5,  // 16: frontend.insights.secrets.WealthAnalyserReportDashboard.event_properties:type_name -> frontend.insights.secrets.WealthAnalyserReportDashboard.EventPropertiesEntry
	7,  // 17: frontend.insights.secrets.WealthAnalyserReportDashboard.footer:type_name -> api.typesv2.ui.IconTextComponent
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_secrets_wealth_analyser_report_proto_init() }
func file_api_frontend_insights_secrets_wealth_analyser_report_proto_init() {
	if File_api_frontend_insights_secrets_wealth_analyser_report_proto != nil {
		return
	}
	file_api_frontend_insights_secrets_secret_analyser_proto_init()
	file_api_frontend_insights_secrets_secret_landing_page_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthAnalyserReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthAnalyserNavigationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectedAccountsNavigationComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthAnalyserReportDashboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*WealthAnalyserNavigationItem_LogoNavigation)(nil),
		(*WealthAnalyserNavigationItem_Cta)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_insights_secrets_wealth_analyser_report_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_secrets_wealth_analyser_report_proto_depIdxs,
		MessageInfos:      file_api_frontend_insights_secrets_wealth_analyser_report_proto_msgTypes,
	}.Build()
	File_api_frontend_insights_secrets_wealth_analyser_report_proto = out.File
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_rawDesc = nil
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_goTypes = nil
	file_api_frontend_insights_secrets_wealth_analyser_report_proto_depIdxs = nil
}
