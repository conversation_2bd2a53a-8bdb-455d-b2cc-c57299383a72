// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/secrets/portfolio_tracker.proto

package secrets

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui1 "github.com/epifi/gamma/api/frontend/investment/ui"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	charts "github.com/epifi/gamma/api/typesv2/ui/charts"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PortFolioTrackerTopBarIcon_IconType int32

const (
	// Client to use the deeplink provided in ITC in case of this
	PortFolioTrackerTopBarIcon_ICON_TYPE_UNSPECIFIED PortFolioTrackerTopBarIcon_IconType = 0
	PortFolioTrackerTopBarIcon_ICON_TYPE_SHARE       PortFolioTrackerTopBarIcon_IconType = 1
	PortFolioTrackerTopBarIcon_ICON_TYPE_DOWNLOAD    PortFolioTrackerTopBarIcon_IconType = 2
)

// Enum value maps for PortFolioTrackerTopBarIcon_IconType.
var (
	PortFolioTrackerTopBarIcon_IconType_name = map[int32]string{
		0: "ICON_TYPE_UNSPECIFIED",
		1: "ICON_TYPE_SHARE",
		2: "ICON_TYPE_DOWNLOAD",
	}
	PortFolioTrackerTopBarIcon_IconType_value = map[string]int32{
		"ICON_TYPE_UNSPECIFIED": 0,
		"ICON_TYPE_SHARE":       1,
		"ICON_TYPE_DOWNLOAD":    2,
	}
)

func (x PortFolioTrackerTopBarIcon_IconType) Enum() *PortFolioTrackerTopBarIcon_IconType {
	p := new(PortFolioTrackerTopBarIcon_IconType)
	*p = x
	return p
}

func (x PortFolioTrackerTopBarIcon_IconType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PortFolioTrackerTopBarIcon_IconType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_enumTypes[0].Descriptor()
}

func (PortFolioTrackerTopBarIcon_IconType) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_secrets_portfolio_tracker_proto_enumTypes[0]
}

func (x PortFolioTrackerTopBarIcon_IconType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PortFolioTrackerTopBarIcon_IconType.Descriptor instead.
func (PortFolioTrackerTopBarIcon_IconType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{1, 0}
}

type PortFolioTrackerLandingPageHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1816&t=FKyA38eSynsGDVHi-4
	// These icons would be shown at the top app bar. Handling for these icons would be hardcoded
	RightAlignedIcons []*PortFolioTrackerTopBarIcon `protobuf:"bytes,1,rep,name=right_aligned_icons,json=rightAlignedIcons,proto3" json:"right_aligned_icons,omitempty"`
}

func (x *PortFolioTrackerLandingPageHeader) Reset() {
	*x = PortFolioTrackerLandingPageHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortFolioTrackerLandingPageHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortFolioTrackerLandingPageHeader) ProtoMessage() {}

func (x *PortFolioTrackerLandingPageHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortFolioTrackerLandingPageHeader.ProtoReflect.Descriptor instead.
func (*PortFolioTrackerLandingPageHeader) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{0}
}

func (x *PortFolioTrackerLandingPageHeader) GetRightAlignedIcons() []*PortFolioTrackerTopBarIcon {
	if x != nil {
		return x.RightAlignedIcons
	}
	return nil
}

type PortFolioTrackerTopBarIcon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IconType PortFolioTrackerTopBarIcon_IconType `protobuf:"varint,1,opt,name=icon_type,json=iconType,proto3,enum=frontend.insights.secrets.PortFolioTrackerTopBarIcon_IconType" json:"icon_type,omitempty"`
	Icon     *common.VisualElement               `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	// action on tapping icon
	Deeplink *deeplink.Deeplink `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *PortFolioTrackerTopBarIcon) Reset() {
	*x = PortFolioTrackerTopBarIcon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortFolioTrackerTopBarIcon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortFolioTrackerTopBarIcon) ProtoMessage() {}

func (x *PortFolioTrackerTopBarIcon) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortFolioTrackerTopBarIcon.ProtoReflect.Descriptor instead.
func (*PortFolioTrackerTopBarIcon) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{1}
}

func (x *PortFolioTrackerTopBarIcon) GetIconType() PortFolioTrackerTopBarIcon_IconType {
	if x != nil {
		return x.IconType
	}
	return PortFolioTrackerTopBarIcon_ICON_TYPE_UNSPECIFIED
}

func (x *PortFolioTrackerTopBarIcon) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *PortFolioTrackerTopBarIcon) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// Each component here corresponds to specific UI components shown on the screen
// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=FKyA38eSynsGDVHi-4
// Instructions/Data for rendering individual components is provided with each component.
type PortfolioTrackerComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Component:
	//
	//	*PortfolioTrackerComponent_TitleComponent
	//	*PortfolioTrackerComponent_NavigationToggles
	//	*PortfolioTrackerComponent_PortfolioSummaryComponent
	//	*PortfolioTrackerComponent_TopMoversComponent
	//	*PortfolioTrackerComponent_AssetDetailsComponent
	//	*PortfolioTrackerComponent_MarketComparisonAndNewsComponent
	//	*PortfolioTrackerComponent_PortfolioTipsComponent
	//	*PortfolioTrackerComponent_InvestmentStageStickersComponent
	//	*PortfolioTrackerComponent_MarketInsightComponent
	//	*PortfolioTrackerComponent_IconTextComponent
	Component isPortfolioTrackerComponent_Component `protobuf_oneof:"component"`
}

func (x *PortfolioTrackerComponent) Reset() {
	*x = PortfolioTrackerComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioTrackerComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioTrackerComponent) ProtoMessage() {}

func (x *PortfolioTrackerComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioTrackerComponent.ProtoReflect.Descriptor instead.
func (*PortfolioTrackerComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{2}
}

func (m *PortfolioTrackerComponent) GetComponent() isPortfolioTrackerComponent_Component {
	if m != nil {
		return m.Component
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetTitleComponent() *PortfolioTrackerTitleComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_TitleComponent); ok {
		return x.TitleComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetNavigationToggles() *NavigationToggleList {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_NavigationToggles); ok {
		return x.NavigationToggles
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetPortfolioSummaryComponent() *PortfolioSummaryComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_PortfolioSummaryComponent); ok {
		return x.PortfolioSummaryComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetTopMoversComponent() *TopMoversComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_TopMoversComponent); ok {
		return x.TopMoversComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetAssetDetailsComponent() *AssetDetailsComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_AssetDetailsComponent); ok {
		return x.AssetDetailsComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetMarketComparisonAndNewsComponent() *MarketComparisonAndNewsComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_MarketComparisonAndNewsComponent); ok {
		return x.MarketComparisonAndNewsComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetPortfolioTipsComponent() *PortfolioNextStepsSuggestionsComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_PortfolioTipsComponent); ok {
		return x.PortfolioTipsComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetInvestmentStageStickersComponent() *InvestmentBehaviourStickersComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_InvestmentStageStickersComponent); ok {
		return x.InvestmentStageStickersComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetMarketInsightComponent() *MarketInsightComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_MarketInsightComponent); ok {
		return x.MarketInsightComponent
	}
	return nil
}

func (x *PortfolioTrackerComponent) GetIconTextComponent() *ui.IconTextComponent {
	if x, ok := x.GetComponent().(*PortfolioTrackerComponent_IconTextComponent); ok {
		return x.IconTextComponent
	}
	return nil
}

type isPortfolioTrackerComponent_Component interface {
	isPortfolioTrackerComponent_Component()
}

type PortfolioTrackerComponent_TitleComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1800&t=SysGDRVMp916Ss7Y-4
	TitleComponent *PortfolioTrackerTitleComponent `protobuf:"bytes,1,opt,name=title_component,json=titleComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_NavigationToggles struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1819&t=FKyA38eSynsGDVHi-4
	NavigationToggles *NavigationToggleList `protobuf:"bytes,2,opt,name=navigation_toggles,json=navigationToggles,proto3,oneof"`
}

type PortfolioTrackerComponent_PortfolioSummaryComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1595&t=FKyA38eSynsGDVHi-4
	PortfolioSummaryComponent *PortfolioSummaryComponent `protobuf:"bytes,3,opt,name=portfolio_summary_component,json=portfolioSummaryComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_TopMoversComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20146&t=SysGDRVMp916Ss7Y-4
	TopMoversComponent *TopMoversComponent `protobuf:"bytes,4,opt,name=top_movers_component,json=topMoversComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_AssetDetailsComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3586-2093&t=SysGDRVMp916Ss7Y-4
	AssetDetailsComponent *AssetDetailsComponent `protobuf:"bytes,5,opt,name=asset_details_component,json=assetDetailsComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_MarketComparisonAndNewsComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1673&t=WEJyRjMjSpc2iBLS-4
	MarketComparisonAndNewsComponent *MarketComparisonAndNewsComponent `protobuf:"bytes,6,opt,name=market_comparison_and_news_component,json=marketComparisonAndNewsComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_PortfolioTipsComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1729&t=WEJyRjMjSpc2iBLS-4
	PortfolioTipsComponent *PortfolioNextStepsSuggestionsComponent `protobuf:"bytes,7,opt,name=portfolio_tips_component,json=portfolioTipsComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_InvestmentStageStickersComponent struct {
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3939-2995&t=wRbNeLll5Ir2JJ2H-4
	InvestmentStageStickersComponent *InvestmentBehaviourStickersComponent `protobuf:"bytes,8,opt,name=investment_stage_stickers_component,json=investmentStageStickersComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_MarketInsightComponent struct {
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Wealth-builder?node-id=9579-14423
	MarketInsightComponent *MarketInsightComponent `protobuf:"bytes,9,opt,name=market_insight_component,json=marketInsightComponent,proto3,oneof"`
}

type PortfolioTrackerComponent_IconTextComponent struct {
	// this is to add any itc component in the portfolio tracker
	// e.g survey itc
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=10737-8171&t=LZ6GB8AwYOZZyJJ3-4
	IconTextComponent *ui.IconTextComponent `protobuf:"bytes,10,opt,name=icon_text_component,json=iconTextComponent,proto3,oneof"`
}

func (*PortfolioTrackerComponent_TitleComponent) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_NavigationToggles) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_PortfolioSummaryComponent) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_TopMoversComponent) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_AssetDetailsComponent) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_MarketComparisonAndNewsComponent) isPortfolioTrackerComponent_Component() {
}

func (*PortfolioTrackerComponent_PortfolioTipsComponent) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_InvestmentStageStickersComponent) isPortfolioTrackerComponent_Component() {
}

func (*PortfolioTrackerComponent_MarketInsightComponent) isPortfolioTrackerComponent_Component() {}

func (*PortfolioTrackerComponent_IconTextComponent) isPortfolioTrackerComponent_Component() {}

type PortfolioTrackerTitleComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daily Portfolio Tracker
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Know what changed in a day, everyday
	SubTitle *common.Text `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// Powered by epifi wealth
	TAndCComponent  *ui.IconTextComponent   `protobuf:"bytes,3,opt,name=t_and_c_component,json=tAndCComponent,proto3" json:"t_and_c_component,omitempty"`
	Ctas            []*ui.IconTextComponent `protobuf:"bytes,4,rep,name=ctas,proto3" json:"ctas,omitempty"`
	BackgroundImage *common.VisualElement   `protobuf:"bytes,5,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
}

func (x *PortfolioTrackerTitleComponent) Reset() {
	*x = PortfolioTrackerTitleComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioTrackerTitleComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioTrackerTitleComponent) ProtoMessage() {}

func (x *PortfolioTrackerTitleComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioTrackerTitleComponent.ProtoReflect.Descriptor instead.
func (*PortfolioTrackerTitleComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{3}
}

func (x *PortfolioTrackerTitleComponent) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *PortfolioTrackerTitleComponent) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *PortfolioTrackerTitleComponent) GetTAndCComponent() *ui.IconTextComponent {
	if x != nil {
		return x.TAndCComponent
	}
	return nil
}

func (x *PortfolioTrackerTitleComponent) GetCtas() []*ui.IconTextComponent {
	if x != nil {
		return x.Ctas
	}
	return nil
}

func (x *PortfolioTrackerTitleComponent) GetBackgroundImage() *common.VisualElement {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

type NavigationToggleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NavigationToggles []*NavigationToggle `protobuf:"bytes,1,rep,name=navigation_toggles,json=navigationToggles,proto3" json:"navigation_toggles,omitempty"`
}

func (x *NavigationToggleList) Reset() {
	*x = NavigationToggleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NavigationToggleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NavigationToggleList) ProtoMessage() {}

func (x *NavigationToggleList) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NavigationToggleList.ProtoReflect.Descriptor instead.
func (*NavigationToggleList) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{4}
}

func (x *NavigationToggleList) GetNavigationToggles() []*NavigationToggle {
	if x != nil {
		return x.NavigationToggles
	}
	return nil
}

type NavigationToggle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType        string                `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	SelectedToggleIcon   *ui.IconTextComponent `protobuf:"bytes,2,opt,name=selected_toggle_icon,json=selectedToggleIcon,proto3" json:"selected_toggle_icon,omitempty"`
	UnselectedToggleIcon *ui.IconTextComponent `protobuf:"bytes,3,opt,name=unselected_toggle_icon,json=unselectedToggleIcon,proto3" json:"unselected_toggle_icon,omitempty"`
	IsSelected           bool                  `protobuf:"varint,4,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
}

func (x *NavigationToggle) Reset() {
	*x = NavigationToggle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NavigationToggle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NavigationToggle) ProtoMessage() {}

func (x *NavigationToggle) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NavigationToggle.ProtoReflect.Descriptor instead.
func (*NavigationToggle) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{5}
}

func (x *NavigationToggle) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *NavigationToggle) GetSelectedToggleIcon() *ui.IconTextComponent {
	if x != nil {
		return x.SelectedToggleIcon
	}
	return nil
}

func (x *NavigationToggle) GetUnselectedToggleIcon() *ui.IconTextComponent {
	if x != nil {
		return x.UnselectedToggleIcon
	}
	return nil
}

func (x *NavigationToggle) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

type PortfolioSummaryComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType string `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	// Summary 'i'
	Title       *ui.IconTextComponent                  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SummaryCard *PortfolioSummaryComponent_SummaryCard `protobuf:"bytes,3,opt,name=summary_card,json=summaryCard,proto3" json:"summary_card,omitempty"`
}

func (x *PortfolioSummaryComponent) Reset() {
	*x = PortfolioSummaryComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioSummaryComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioSummaryComponent) ProtoMessage() {}

func (x *PortfolioSummaryComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioSummaryComponent.ProtoReflect.Descriptor instead.
func (*PortfolioSummaryComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{6}
}

func (x *PortfolioSummaryComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *PortfolioSummaryComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *PortfolioSummaryComponent) GetSummaryCard() *PortfolioSummaryComponent_SummaryCard {
	if x != nil {
		return x.SummaryCard
	}
	return nil
}

// Similar to 'frontend.insights.networth.NetworthRollingAnimationDetails'
// Have created a duplicate as handling of this would be different and importing 'NetworthRollingAnimationDetails'
// leads to cyclic imports.
// If this indeed is something common that might be needed in multiple places, we should
// create this in a common place like types or a separate common package with a better naming convention
type PortfolioTrackerRollingAnimationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value to be used at the end of rolling animation.
	// updated networth value contains value of asset connected
	UpdatedPortfolio     *typesv2.Money        `protobuf:"bytes,1,opt,name=updated_portfolio,json=updatedPortfolio,proto3" json:"updated_portfolio,omitempty"`
	CurrencySymbol       *common.Text          `protobuf:"bytes,2,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	UpdatedPortfolioText *ui.IconTextComponent `protobuf:"bytes,3,opt,name=updated_portfolio_text,json=updatedPortfolioText,proto3" json:"updated_portfolio_text,omitempty"`
}

func (x *PortfolioTrackerRollingAnimationDetails) Reset() {
	*x = PortfolioTrackerRollingAnimationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioTrackerRollingAnimationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioTrackerRollingAnimationDetails) ProtoMessage() {}

func (x *PortfolioTrackerRollingAnimationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioTrackerRollingAnimationDetails.ProtoReflect.Descriptor instead.
func (*PortfolioTrackerRollingAnimationDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{7}
}

func (x *PortfolioTrackerRollingAnimationDetails) GetUpdatedPortfolio() *typesv2.Money {
	if x != nil {
		return x.UpdatedPortfolio
	}
	return nil
}

func (x *PortfolioTrackerRollingAnimationDetails) GetCurrencySymbol() *common.Text {
	if x != nil {
		return x.CurrencySymbol
	}
	return nil
}

func (x *PortfolioTrackerRollingAnimationDetails) GetUpdatedPortfolioText() *ui.IconTextComponent {
	if x != nil {
		return x.UpdatedPortfolioText
	}
	return nil
}

type TopMoversComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType string `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	// Top Movers
	Title *ui.IconTextComponent `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Your investments are doing great! Check out your top movers.
	SubTitle *common.Text `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// Section with Top gainers and Top losers Assets
	TopMoversSection *TopMoversSection `protobuf:"bytes,4,opt,name=top_movers_section,json=topMoversSection,proto3" json:"top_movers_section,omitempty"`
}

func (x *TopMoversComponent) Reset() {
	*x = TopMoversComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopMoversComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopMoversComponent) ProtoMessage() {}

func (x *TopMoversComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopMoversComponent.ProtoReflect.Descriptor instead.
func (*TopMoversComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{8}
}

func (x *TopMoversComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *TopMoversComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *TopMoversComponent) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *TopMoversComponent) GetTopMoversSection() *TopMoversSection {
	if x != nil {
		return x.TopMoversSection
	}
	return nil
}

// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3566-20285&t=uwqYYJjE2eyNndJ4-4
// This can be moved to SummaryCard if we wish to combine Summary and Top Movers section in the future
type TopMoversSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Top Gainers
	// Attached with 'top_gainer_tiles'
	TopGainersText *common.Text `protobuf:"bytes,1,opt,name=top_gainers_text,json=topGainersText,proto3" json:"top_gainers_text,omitempty"`
	// Client should support atleast '3' tiles. Discard further tiles if present
	// This would be sorted in decreasing order gain value/percent and client is supposed
	// to reduce size of each tile as they render
	TopGainerTiles []*TopMoversSection_AssetMoverTile `protobuf:"bytes,2,rep,name=top_gainer_tiles,json=topGainerTiles,proto3" json:"top_gainer_tiles,omitempty"`
	// Top Losers
	// Attached with 'top_loser_tiles'
	TopLosersText *common.Text `protobuf:"bytes,3,opt,name=top_losers_text,json=topLosersText,proto3" json:"top_losers_text,omitempty"`
	// Client should support atleast '3' tiles. Discard further tiles if present
	// This would be sorted in increasing order of loss value/percent and client is supposed
	// to increase size of each tile as they render
	TopLoserTiles []*TopMoversSection_AssetMoverTile `protobuf:"bytes,4,rep,name=top_loser_tiles,json=topLoserTiles,proto3" json:"top_loser_tiles,omitempty"`
}

func (x *TopMoversSection) Reset() {
	*x = TopMoversSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopMoversSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopMoversSection) ProtoMessage() {}

func (x *TopMoversSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopMoversSection.ProtoReflect.Descriptor instead.
func (*TopMoversSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{9}
}

func (x *TopMoversSection) GetTopGainersText() *common.Text {
	if x != nil {
		return x.TopGainersText
	}
	return nil
}

func (x *TopMoversSection) GetTopGainerTiles() []*TopMoversSection_AssetMoverTile {
	if x != nil {
		return x.TopGainerTiles
	}
	return nil
}

func (x *TopMoversSection) GetTopLosersText() *common.Text {
	if x != nil {
		return x.TopLosersText
	}
	return nil
}

func (x *TopMoversSection) GetTopLoserTiles() []*TopMoversSection_AssetMoverTile {
	if x != nil {
		return x.TopLoserTiles
	}
	return nil
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=Q9eQjZjJg10nxoIA-4
type AssetDetailsComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType string `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	// Asset-wise distribution 'i'
	Title *ui.IconTextComponent `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// List of filter tags
	// Figure out the selected tag using the 'is_selected' field and show corresponding
	FilterTags []*AssetDetailsComponent_AssetFilterTag `protobuf:"bytes,4,rep,name=filter_tags,json=filterTags,proto3" json:"filter_tags,omitempty"`
	// Asset Details card corresponding to asset type selected in the filter tags
	// If the value is not found, client can call 'GetPortfolioTrackerAssetDetailsPage' with the asset name to fetch the details
	DetailCards map[string]*PortfolioTrackerAssetDetailsCard `protobuf:"bytes,5,rep,name=detail_cards,json=detailCards,proto3" json:"detail_cards,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// See how each asset type in your portfolio performed
	SubTitle *common.Text `protobuf:"bytes,6,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
}

func (x *AssetDetailsComponent) Reset() {
	*x = AssetDetailsComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetDetailsComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetDetailsComponent) ProtoMessage() {}

func (x *AssetDetailsComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetDetailsComponent.ProtoReflect.Descriptor instead.
func (*AssetDetailsComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{10}
}

func (x *AssetDetailsComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *AssetDetailsComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AssetDetailsComponent) GetFilterTags() []*AssetDetailsComponent_AssetFilterTag {
	if x != nil {
		return x.FilterTags
	}
	return nil
}

func (x *AssetDetailsComponent) GetDetailCards() map[string]*PortfolioTrackerAssetDetailsCard {
	if x != nil {
		return x.DetailCards
	}
	return nil
}

func (x *AssetDetailsComponent) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

// Details card for individual asset
type PortfolioTrackerAssetDetailsCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// For identifying which asset this card belongs to
	// maps to 'insights.networth.AssetType'
	AssetType string `protobuf:"bytes,1,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	// 13 mutual funds
	AssetCountText *common.Text `protobuf:"bytes,2,opt,name=asset_count_text,json=assetCountText,proto3" json:"asset_count_text,omitempty"`
	// list of possible toggle value
	// Eg: ₹43.2K +1.25% Day change (%), ₹43.2K +1.25%  Returns (%)
	ToggleList []*PortfolioTrackerAssetDetailsCard_ToggleValue `protobuf:"bytes,3,rep,name=toggle_list,json=toggleList,proto3" json:"toggle_list,omitempty"`
	// default display_value_type for this card.
	// Would be used for the first time rendering
	DefaultDisplayValueType string `protobuf:"bytes,4,opt,name=default_display_value_type,json=defaultDisplayValueType,proto3" json:"default_display_value_type,omitempty"`
	// Individual line items for the distribution
	LineItems []*PortfolioTrackerLineItem `protobuf:"bytes,5,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// should redirect to 'AssetDistributionPage' for all lineitems
	ViewMoreCta *ui.IconTextComponent `protobuf:"bytes,6,opt,name=view_more_cta,json=viewMoreCta,proto3" json:"view_more_cta,omitempty"`
	// background for list
	BgColour *widget.BackgroundColour `protobuf:"bytes,7,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *PortfolioTrackerAssetDetailsCard) Reset() {
	*x = PortfolioTrackerAssetDetailsCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioTrackerAssetDetailsCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioTrackerAssetDetailsCard) ProtoMessage() {}

func (x *PortfolioTrackerAssetDetailsCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioTrackerAssetDetailsCard.ProtoReflect.Descriptor instead.
func (*PortfolioTrackerAssetDetailsCard) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{11}
}

func (x *PortfolioTrackerAssetDetailsCard) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *PortfolioTrackerAssetDetailsCard) GetAssetCountText() *common.Text {
	if x != nil {
		return x.AssetCountText
	}
	return nil
}

func (x *PortfolioTrackerAssetDetailsCard) GetToggleList() []*PortfolioTrackerAssetDetailsCard_ToggleValue {
	if x != nil {
		return x.ToggleList
	}
	return nil
}

func (x *PortfolioTrackerAssetDetailsCard) GetDefaultDisplayValueType() string {
	if x != nil {
		return x.DefaultDisplayValueType
	}
	return ""
}

func (x *PortfolioTrackerAssetDetailsCard) GetLineItems() []*PortfolioTrackerLineItem {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *PortfolioTrackerAssetDetailsCard) GetViewMoreCta() *ui.IconTextComponent {
	if x != nil {
		return x.ViewMoreCta
	}
	return nil
}

func (x *PortfolioTrackerAssetDetailsCard) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

// Wrapper over 'investment.ui.LineItem' that uses a map of
// 'PortfolioTrackerToggleValueType' -> Heading+tag to determine
// which value to show based on what toggle value user has selected
type PortfolioTrackerLineItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Individual line items for the distribution
	LineItem *ui1.LineItem `protobuf:"bytes,1,opt,name=line_item,json=lineItem,proto3" json:"line_item,omitempty"`
	// 'PortfolioTrackerToggleValueType' -> 'Lineitem's 'right_heading'+'right_tags'
	// Value should override each lineitem's 'right_heading'+'right_tags'
	LineItemDisplayValue map[string]*ui1.LineItemHeadingTag `protobuf:"bytes,7,rep,name=line_item_display_value,json=lineItemDisplayValue,proto3" json:"line_item_display_value,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PortfolioTrackerLineItem) Reset() {
	*x = PortfolioTrackerLineItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioTrackerLineItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioTrackerLineItem) ProtoMessage() {}

func (x *PortfolioTrackerLineItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioTrackerLineItem.ProtoReflect.Descriptor instead.
func (*PortfolioTrackerLineItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{12}
}

func (x *PortfolioTrackerLineItem) GetLineItem() *ui1.LineItem {
	if x != nil {
		return x.LineItem
	}
	return nil
}

func (x *PortfolioTrackerLineItem) GetLineItemDisplayValue() map[string]*ui1.LineItemHeadingTag {
	if x != nil {
		return x.LineItemDisplayValue
	}
	return nil
}

// News Section
// figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3699-2448&t=KEfg8KQf8X3VlIa2-4
type NewsSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// In news
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Divider between title and news content
	Divider *ui.Divider `protobuf:"bytes,2,opt,name=divider,proto3" json:"divider,omitempty"`
	// List of news items to show in carousel
	NewsItems []*ui.IconTextComponent `protobuf:"bytes,3,rep,name=news_items,json=newsItems,proto3" json:"news_items,omitempty"`
	// this is the carousal indicator config
	CarouselIndicatorConfig *NewsSection_NewsCarouselIndicatorConfig `protobuf:"bytes,4,opt,name=carousel_indicator_config,json=carouselIndicatorConfig,proto3" json:"carousel_indicator_config,omitempty"`
}

func (x *NewsSection) Reset() {
	*x = NewsSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsSection) ProtoMessage() {}

func (x *NewsSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsSection.ProtoReflect.Descriptor instead.
func (*NewsSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{13}
}

func (x *NewsSection) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *NewsSection) GetDivider() *ui.Divider {
	if x != nil {
		return x.Divider
	}
	return nil
}

func (x *NewsSection) GetNewsItems() []*ui.IconTextComponent {
	if x != nil {
		return x.NewsItems
	}
	return nil
}

func (x *NewsSection) GetCarouselIndicatorConfig() *NewsSection_NewsCarouselIndicatorConfig {
	if x != nil {
		return x.CarouselIndicatorConfig
	}
	return nil
}

// Market Comparison and News Component shows market comparison and news in a single card
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1673&t=wRbNeLll5Ir2JJ2H-4
type MarketComparisonAndNewsComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType    string                   `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	MarketComparison *MarketComparisonSection `protobuf:"bytes,2,opt,name=market_comparison,json=marketComparison,proto3" json:"market_comparison,omitempty"`
	News             *NewsSection             `protobuf:"bytes,3,opt,name=news,proto3" json:"news,omitempty"`
}

func (x *MarketComparisonAndNewsComponent) Reset() {
	*x = MarketComparisonAndNewsComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketComparisonAndNewsComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketComparisonAndNewsComponent) ProtoMessage() {}

func (x *MarketComparisonAndNewsComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketComparisonAndNewsComponent.ProtoReflect.Descriptor instead.
func (*MarketComparisonAndNewsComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{14}
}

func (x *MarketComparisonAndNewsComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *MarketComparisonAndNewsComponent) GetMarketComparison() *MarketComparisonSection {
	if x != nil {
		return x.MarketComparison
	}
	return nil
}

func (x *MarketComparisonAndNewsComponent) GetNews() *NewsSection {
	if x != nil {
		return x.News
	}
	return nil
}

// Market Comparison Section
// figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1710&t=KEfg8KQf8X3VlIa2-4
type MarketComparisonSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Your Mutual Funds v/s Market
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// See where you stand compared to market trends
	SubTitle        *common.Text     `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	ComparisonChart *ComparisonChart `protobuf:"bytes,3,opt,name=comparison_chart,json=comparisonChart,proto3" json:"comparison_chart,omitempty"`
}

func (x *MarketComparisonSection) Reset() {
	*x = MarketComparisonSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketComparisonSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketComparisonSection) ProtoMessage() {}

func (x *MarketComparisonSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketComparisonSection.ProtoReflect.Descriptor instead.
func (*MarketComparisonSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{15}
}

func (x *MarketComparisonSection) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *MarketComparisonSection) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *MarketComparisonSection) GetComparisonChart() *ComparisonChart {
	if x != nil {
		return x.ComparisonChart
	}
	return nil
}

// figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3699-2448&t=wRbNeLll5Ir2JJ2H-4
type ComparisonChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Background image for the chart
	Bars []*charts.Bar `protobuf:"bytes,1,rep,name=bars,proto3" json:"bars,omitempty"`
	// Yesterday's date label
	DateLabel *common.Text `protobuf:"bytes,2,opt,name=date_label,json=dateLabel,proto3" json:"date_label,omitempty"`
	// User's portfolio performance
	Legends []*ui.IconTextComponent `protobuf:"bytes,3,rep,name=legends,proto3" json:"legends,omitempty"`
	// Background color for the chart
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,4,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Border color for the chart
	BorderColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=border_colour,json=borderColour,proto3" json:"border_colour,omitempty"`
	// this is a reference line of zero in the chart
	ReferenceLineConfig *ComparisonChart_ReferenceLineConfig `protobuf:"bytes,6,opt,name=reference_line_config,json=referenceLineConfig,proto3" json:"reference_line_config,omitempty"`
}

func (x *ComparisonChart) Reset() {
	*x = ComparisonChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComparisonChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComparisonChart) ProtoMessage() {}

func (x *ComparisonChart) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComparisonChart.ProtoReflect.Descriptor instead.
func (*ComparisonChart) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{16}
}

func (x *ComparisonChart) GetBars() []*charts.Bar {
	if x != nil {
		return x.Bars
	}
	return nil
}

func (x *ComparisonChart) GetDateLabel() *common.Text {
	if x != nil {
		return x.DateLabel
	}
	return nil
}

func (x *ComparisonChart) GetLegends() []*ui.IconTextComponent {
	if x != nil {
		return x.Legends
	}
	return nil
}

func (x *ComparisonChart) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *ComparisonChart) GetBorderColour() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColour
	}
	return nil
}

func (x *ComparisonChart) GetReferenceLineConfig() *ComparisonChart_ReferenceLineConfig {
	if x != nil {
		return x.ReferenceLineConfig
	}
	return nil
}

// Portfolio Tips Component shows portfolio analysis and recommendations
// figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1729&t=KEfg8KQf8X3VlIa2-4
type PortfolioNextStepsSuggestionsComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType string `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	// What could be your next steps?
	Title *ui.IconTextComponent `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Description text explaining the recommendation
	Description *common.Text `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Visual representation of portfolio distribution including pie chart and percentages
	DistributionChart *common.VisualElement `protobuf:"bytes,4,opt,name=distribution_chart,json=distributionChart,proto3" json:"distribution_chart,omitempty"`
	// recommended actions to be shown
	// this can be at max 2, will always pick first two on client side
	ActionCards []*ActionCard `protobuf:"bytes,5,rep,name=action_cards,json=actionCards,proto3" json:"action_cards,omitempty"`
}

func (x *PortfolioNextStepsSuggestionsComponent) Reset() {
	*x = PortfolioNextStepsSuggestionsComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioNextStepsSuggestionsComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioNextStepsSuggestionsComponent) ProtoMessage() {}

func (x *PortfolioNextStepsSuggestionsComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioNextStepsSuggestionsComponent.ProtoReflect.Descriptor instead.
func (*PortfolioNextStepsSuggestionsComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{17}
}

func (x *PortfolioNextStepsSuggestionsComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *PortfolioNextStepsSuggestionsComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *PortfolioNextStepsSuggestionsComponent) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *PortfolioNextStepsSuggestionsComponent) GetDistributionChart() *common.VisualElement {
	if x != nil {
		return x.DistributionChart
	}
	return nil
}

func (x *PortfolioNextStepsSuggestionsComponent) GetActionCards() []*ActionCard {
	if x != nil {
		return x.ActionCards
	}
	return nil
}

// Action cards for recommendations
// figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3939-2995&t=KEfg8KQf8X3VlIa2-4
type ActionCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Title of the action (e.g., "Explore US Stocks")
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Description of the action (e.g., "Invest in 3,000+ stocks and ETFs")
	Description *common.Text `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Icon/image for the action
	Icon *common.VisualElement `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	// CTA text (e.g., "Start investing") with bg_color and the padding
	Cta *ui.IconTextComponent `protobuf:"bytes,4,opt,name=cta,proto3" json:"cta,omitempty"`
	// Background color for the card
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,6,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// Corner radius for the action card
	CornerRadius float32                  `protobuf:"fixed32,7,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	BorderColour *widget.BackgroundColour `protobuf:"bytes,8,opt,name=border_colour,json=borderColour,proto3" json:"border_colour,omitempty"`
}

func (x *ActionCard) Reset() {
	*x = ActionCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionCard) ProtoMessage() {}

func (x *ActionCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionCard.ProtoReflect.Descriptor instead.
func (*ActionCard) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{18}
}

func (x *ActionCard) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ActionCard) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *ActionCard) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *ActionCard) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *ActionCard) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *ActionCard) GetCornerRadius() float32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *ActionCard) GetBorderColour() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColour
	}
	return nil
}

// Investment Behavior Component shows INBE insights and analysis
type InvestmentBehaviourStickersComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType string `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	// Title of the INBE section
	Title *ui.IconTextComponent `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Description text explaining the investment behavior
	Description *common.Text `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Main image showing investment behavior visualization
	StickerImage *common.VisualElement `protobuf:"bytes,4,opt,name=sticker_image,json=stickerImage,proto3" json:"sticker_image,omitempty"`
	// Background visual element for styling
	BackgroundImage *common.VisualElement `protobuf:"bytes,5,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	// CTA for more details or actions
	Cta *ui.IconTextComponent `protobuf:"bytes,6,opt,name=cta,proto3" json:"cta,omitempty"`
	// Border color for the component
	BorderColour *widget.BackgroundColour `protobuf:"bytes,7,opt,name=border_colour,json=borderColour,proto3" json:"border_colour,omitempty"`
}

func (x *InvestmentBehaviourStickersComponent) Reset() {
	*x = InvestmentBehaviourStickersComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentBehaviourStickersComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentBehaviourStickersComponent) ProtoMessage() {}

func (x *InvestmentBehaviourStickersComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentBehaviourStickersComponent.ProtoReflect.Descriptor instead.
func (*InvestmentBehaviourStickersComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{19}
}

func (x *InvestmentBehaviourStickersComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *InvestmentBehaviourStickersComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *InvestmentBehaviourStickersComponent) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *InvestmentBehaviourStickersComponent) GetStickerImage() *common.VisualElement {
	if x != nil {
		return x.StickerImage
	}
	return nil
}

func (x *InvestmentBehaviourStickersComponent) GetBackgroundImage() *common.VisualElement {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

func (x *InvestmentBehaviourStickersComponent) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *InvestmentBehaviourStickersComponent) GetBorderColour() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColour
	}
	return nil
}

// Market Insight Component shows educational insights about market performance
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Wealth-builder?node-id=9579-14423
type MarketInsightComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maps to 'insights.secrets.frontend.PortfolioTrackerComponentType'
	ComponentType string `protobuf:"bytes,1,opt,name=component_type,json=componentType,proto3" json:"component_type,omitempty"`
	// Header with sparkle icon and "Insight" text
	Header *ui.IconTextComponent `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	// Divider between header and content
	Divider *ui.Divider `protobuf:"bytes,3,opt,name=divider,proto3" json:"divider,omitempty"`
	// Main content text to describe the graph
	Description *common.Text `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// Graph visualization as a single image
	GraphImage *common.VisualElement `protobuf:"bytes,5,opt,name=graph_image,json=graphImage,proto3" json:"graph_image,omitempty"`
	// Disclaimer text at the bottom
	Disclaimer *common.Text `protobuf:"bytes,6,opt,name=disclaimer,proto3" json:"disclaimer,omitempty"`
	// Visual styling
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,7,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	CornerRadius     float32                  `protobuf:"fixed32,8,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
}

func (x *MarketInsightComponent) Reset() {
	*x = MarketInsightComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketInsightComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketInsightComponent) ProtoMessage() {}

func (x *MarketInsightComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketInsightComponent.ProtoReflect.Descriptor instead.
func (*MarketInsightComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{20}
}

func (x *MarketInsightComponent) GetComponentType() string {
	if x != nil {
		return x.ComponentType
	}
	return ""
}

func (x *MarketInsightComponent) GetHeader() *ui.IconTextComponent {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MarketInsightComponent) GetDivider() *ui.Divider {
	if x != nil {
		return x.Divider
	}
	return nil
}

func (x *MarketInsightComponent) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *MarketInsightComponent) GetGraphImage() *common.VisualElement {
	if x != nil {
		return x.GraphImage
	}
	return nil
}

func (x *MarketInsightComponent) GetDisclaimer() *common.Text {
	if x != nil {
		return x.Disclaimer
	}
	return nil
}

func (x *MarketInsightComponent) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *MarketInsightComponent) GetCornerRadius() float32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

type PortfolioSummaryComponent_SummaryCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// INVESTMENTS 1D RETURNS
	TopTextTags          []*ui.IconTextComponent                  `protobuf:"bytes,1,rep,name=top_text_tags,json=topTextTags,proto3" json:"top_text_tags,omitempty"`
	ChangeValueAnimation *PortfolioTrackerRollingAnimationDetails `protobuf:"bytes,3,opt,name=change_value_animation,json=changeValueAnimation,proto3" json:"change_value_animation,omitempty"`
	ChangePercentTag     *ui.IconTextComponent                    `protobuf:"bytes,4,opt,name=change_percent_tag,json=changePercentTag,proto3" json:"change_percent_tag,omitempty"`
	// CURRENT ₹2.84L INVESTED ₹2.41L
	BottomTextTags []*ui.IconTextComponent `protobuf:"bytes,5,rep,name=bottom_text_tags,json=bottomTextTags,proto3" json:"bottom_text_tags,omitempty"`
	RightImage     *common.VisualElement   `protobuf:"bytes,6,opt,name=right_image,json=rightImage,proto3" json:"right_image,omitempty"`
	// 'i' Next report on 27th Feb, 11:00 AM
	BottomAddendumBar *ui.IconTextComponent    `protobuf:"bytes,7,opt,name=bottom_addendum_bar,json=bottomAddendumBar,proto3" json:"bottom_addendum_bar,omitempty"`
	BackgroundColour  *widget.BackgroundColour `protobuf:"bytes,8,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
}

func (x *PortfolioSummaryComponent_SummaryCard) Reset() {
	*x = PortfolioSummaryComponent_SummaryCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioSummaryComponent_SummaryCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioSummaryComponent_SummaryCard) ProtoMessage() {}

func (x *PortfolioSummaryComponent_SummaryCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioSummaryComponent_SummaryCard.ProtoReflect.Descriptor instead.
func (*PortfolioSummaryComponent_SummaryCard) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{6, 0}
}

func (x *PortfolioSummaryComponent_SummaryCard) GetTopTextTags() []*ui.IconTextComponent {
	if x != nil {
		return x.TopTextTags
	}
	return nil
}

func (x *PortfolioSummaryComponent_SummaryCard) GetChangeValueAnimation() *PortfolioTrackerRollingAnimationDetails {
	if x != nil {
		return x.ChangeValueAnimation
	}
	return nil
}

func (x *PortfolioSummaryComponent_SummaryCard) GetChangePercentTag() *ui.IconTextComponent {
	if x != nil {
		return x.ChangePercentTag
	}
	return nil
}

func (x *PortfolioSummaryComponent_SummaryCard) GetBottomTextTags() []*ui.IconTextComponent {
	if x != nil {
		return x.BottomTextTags
	}
	return nil
}

func (x *PortfolioSummaryComponent_SummaryCard) GetRightImage() *common.VisualElement {
	if x != nil {
		return x.RightImage
	}
	return nil
}

func (x *PortfolioSummaryComponent_SummaryCard) GetBottomAddendumBar() *ui.IconTextComponent {
	if x != nil {
		return x.BottomAddendumBar
	}
	return nil
}

func (x *PortfolioSummaryComponent_SummaryCard) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

// represents single asset in the section
type TopMoversSection_AssetMoverTile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Parag Parikh Mid Cap
	AssetName *common.Text `protobuf:"bytes,1,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	// ₹ + 1.6K 6.5%
	ChangeValueTexts *ui.IconTextComponent `protobuf:"bytes,2,opt,name=change_value_texts,json=changeValueTexts,proto3" json:"change_value_texts,omitempty"`
	// background color of the component
	BgColour *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *TopMoversSection_AssetMoverTile) Reset() {
	*x = TopMoversSection_AssetMoverTile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopMoversSection_AssetMoverTile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopMoversSection_AssetMoverTile) ProtoMessage() {}

func (x *TopMoversSection_AssetMoverTile) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopMoversSection_AssetMoverTile.ProtoReflect.Descriptor instead.
func (*TopMoversSection_AssetMoverTile) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{9, 0}
}

func (x *TopMoversSection_AssetMoverTile) GetAssetName() *common.Text {
	if x != nil {
		return x.AssetName
	}
	return nil
}

func (x *TopMoversSection_AssetMoverTile) GetChangeValueTexts() *ui.IconTextComponent {
	if x != nil {
		return x.ChangeValueTexts
	}
	return nil
}

func (x *TopMoversSection_AssetMoverTile) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

type AssetDetailsComponent_AssetFilterTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this maps to 'insights.networth.AssetType'
	// to be used when showing asset details for a specific asset
	AssetType string `protobuf:"bytes,1,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	// tag to be shown if the filter is selected
	SelectedTag *ui.IconTextComponent `protobuf:"bytes,2,opt,name=selected_tag,json=selectedTag,proto3" json:"selected_tag,omitempty"`
	// tag to be shown when unselected
	UnselectedTag *ui.IconTextComponent `protobuf:"bytes,3,opt,name=unselected_tag,json=unselectedTag,proto3" json:"unselected_tag,omitempty"`
	// For showing default selected tag
	IsSelected bool `protobuf:"varint,4,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
	// For showing tag that are not selectable
	// Eg: 'Gold, deposits & more soon'
	// Only 'unselected_tag' would be present for this
	IsDisabled bool `protobuf:"varint,5,opt,name=is_disabled,json=isDisabled,proto3" json:"is_disabled,omitempty"`
}

func (x *AssetDetailsComponent_AssetFilterTag) Reset() {
	*x = AssetDetailsComponent_AssetFilterTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetDetailsComponent_AssetFilterTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetDetailsComponent_AssetFilterTag) ProtoMessage() {}

func (x *AssetDetailsComponent_AssetFilterTag) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetDetailsComponent_AssetFilterTag.ProtoReflect.Descriptor instead.
func (*AssetDetailsComponent_AssetFilterTag) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{10, 0}
}

func (x *AssetDetailsComponent_AssetFilterTag) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *AssetDetailsComponent_AssetFilterTag) GetSelectedTag() *ui.IconTextComponent {
	if x != nil {
		return x.SelectedTag
	}
	return nil
}

func (x *AssetDetailsComponent_AssetFilterTag) GetUnselectedTag() *ui.IconTextComponent {
	if x != nil {
		return x.UnselectedTag
	}
	return nil
}

func (x *AssetDetailsComponent_AssetFilterTag) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

func (x *AssetDetailsComponent_AssetFilterTag) GetIsDisabled() bool {
	if x != nil {
		return x.IsDisabled
	}
	return false
}

type PortfolioTrackerAssetDetailsCard_ToggleValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// would be one of the value in 'insights.secrets.frontend.PortfolioTrackerToggleValueType'
	ToggleValueType string `protobuf:"bytes,1,opt,name=toggle_value_type,json=toggleValueType,proto3" json:"toggle_value_type,omitempty"`
	// ₹43.2K +1.25%
	AggregatedValue *ui.IconTextComponent `protobuf:"bytes,2,opt,name=aggregated_value,json=aggregatedValue,proto3" json:"aggregated_value,omitempty"`
	// Day Change (%)
	ToggleText *ui.IconTextComponent `protobuf:"bytes,3,opt,name=toggle_text,json=toggleText,proto3" json:"toggle_text,omitempty"`
}

func (x *PortfolioTrackerAssetDetailsCard_ToggleValue) Reset() {
	*x = PortfolioTrackerAssetDetailsCard_ToggleValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioTrackerAssetDetailsCard_ToggleValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioTrackerAssetDetailsCard_ToggleValue) ProtoMessage() {}

func (x *PortfolioTrackerAssetDetailsCard_ToggleValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioTrackerAssetDetailsCard_ToggleValue.ProtoReflect.Descriptor instead.
func (*PortfolioTrackerAssetDetailsCard_ToggleValue) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{11, 0}
}

func (x *PortfolioTrackerAssetDetailsCard_ToggleValue) GetToggleValueType() string {
	if x != nil {
		return x.ToggleValueType
	}
	return ""
}

func (x *PortfolioTrackerAssetDetailsCard_ToggleValue) GetAggregatedValue() *ui.IconTextComponent {
	if x != nil {
		return x.AggregatedValue
	}
	return nil
}

func (x *PortfolioTrackerAssetDetailsCard_ToggleValue) GetToggleText() *ui.IconTextComponent {
	if x != nil {
		return x.ToggleText
	}
	return nil
}

type NewsSection_NewsCarouselIndicatorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Auto scroll interval in seconds
	AutoScrollInterval float32 `protobuf:"fixed32,1,opt,name=auto_scroll_interval,json=autoScrollInterval,proto3" json:"auto_scroll_interval,omitempty"`
	// Color for unselected index dots
	UnselectedIndexIndicatorBgColour *widget.BackgroundColour                                         `protobuf:"bytes,3,opt,name=unselected_index_indicator_bg_colour,json=unselectedIndexIndicatorBgColour,proto3" json:"unselected_index_indicator_bg_colour,omitempty"`
	SelectedIndicatorConfig          *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig `protobuf:"bytes,4,opt,name=selected_indicator_config,json=selectedIndicatorConfig,proto3" json:"selected_indicator_config,omitempty"`
}

func (x *NewsSection_NewsCarouselIndicatorConfig) Reset() {
	*x = NewsSection_NewsCarouselIndicatorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsSection_NewsCarouselIndicatorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsSection_NewsCarouselIndicatorConfig) ProtoMessage() {}

func (x *NewsSection_NewsCarouselIndicatorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsSection_NewsCarouselIndicatorConfig.ProtoReflect.Descriptor instead.
func (*NewsSection_NewsCarouselIndicatorConfig) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{13, 0}
}

func (x *NewsSection_NewsCarouselIndicatorConfig) GetAutoScrollInterval() float32 {
	if x != nil {
		return x.AutoScrollInterval
	}
	return 0
}

func (x *NewsSection_NewsCarouselIndicatorConfig) GetUnselectedIndexIndicatorBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.UnselectedIndexIndicatorBgColour
	}
	return nil
}

func (x *NewsSection_NewsCarouselIndicatorConfig) GetSelectedIndicatorConfig() *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig {
	if x != nil {
		return x.SelectedIndicatorConfig
	}
	return nil
}

type NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BgColor *widget.BackgroundColour `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// the text will be overriden by the client on basis of scrolling
	SelectedIndexText *common.Text `protobuf:"bytes,2,opt,name=selected_index_text,json=selectedIndexText,proto3" json:"selected_index_text,omitempty"`
	// backend will send this text / totalcount, this will not be overriden by client, total count is the total number of news items
	TotalCountText *common.Text `protobuf:"bytes,3,opt,name=total_count_text,json=totalCountText,proto3" json:"total_count_text,omitempty"`
	CornerRadius   float32      `protobuf:"fixed32,4,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) Reset() {
	*x = NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) ProtoMessage() {}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig.ProtoReflect.Descriptor instead.
func (*NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{13, 0, 0}
}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) GetSelectedIndexText() *common.Text {
	if x != nil {
		return x.SelectedIndexText
	}
	return nil
}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) GetTotalCountText() *common.Text {
	if x != nil {
		return x.TotalCountText
	}
	return nil
}

func (x *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) GetCornerRadius() float32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

type ComparisonChart_ReferenceLineConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// YEST. as shown in figma
	LeadingText *common.Text `protobuf:"bytes,1,opt,name=leading_text,json=leadingText,proto3" json:"leading_text,omitempty"`
	LineHeight  float32      `protobuf:"fixed32,2,opt,name=line_height,json=lineHeight,proto3" json:"line_height,omitempty"`
	// Line color for the chart
	LineColour *widget.BackgroundColour `protobuf:"bytes,3,opt,name=line_colour,json=lineColour,proto3" json:"line_colour,omitempty"`
}

func (x *ComparisonChart_ReferenceLineConfig) Reset() {
	*x = ComparisonChart_ReferenceLineConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComparisonChart_ReferenceLineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComparisonChart_ReferenceLineConfig) ProtoMessage() {}

func (x *ComparisonChart_ReferenceLineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComparisonChart_ReferenceLineConfig.ProtoReflect.Descriptor instead.
func (*ComparisonChart_ReferenceLineConfig) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ComparisonChart_ReferenceLineConfig) GetLeadingText() *common.Text {
	if x != nil {
		return x.LeadingText
	}
	return nil
}

func (x *ComparisonChart_ReferenceLineConfig) GetLineHeight() float32 {
	if x != nil {
		return x.LineHeight
	}
	return 0
}

func (x *ComparisonChart_ReferenceLineConfig) GetLineColour() *widget.BackgroundColour {
	if x != nil {
		return x.LineColour
	}
	return nil
}

var File_api_frontend_insights_secrets_portfolio_tracker_proto protoreflect.FileDescriptor

var file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f,
	0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75,
	0x69, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x73, 0x2f, 0x62, 0x61, 0x72, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x8a, 0x01, 0x0a, 0x21, 0x50, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x65, 0x0a, 0x13, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x61, 0x6c, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x50, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x54, 0x6f, 0x70, 0x42, 0x61, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x11, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x41, 0x6c, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x22, 0xbd, 0x02,
	0x0a, 0x1a, 0x50, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x54, 0x6f, 0x70, 0x42, 0x61, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x09,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74,
	0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x42,
	0x61, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x69, 0x63, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x52, 0x0a, 0x08, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x48,
	0x41, 0x52, 0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x02, 0x22, 0x9d, 0x09,
	0x0a, 0x19, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x64, 0x0a, 0x0f, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x60, 0x0a, 0x12, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x11, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x67, 0x67,
	0x6c, 0x65, 0x73, 0x12, 0x76, 0x0a, 0x1b, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f,
	0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x19, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x61, 0x0a, 0x14, 0x74,
	0x6f, 0x70, 0x5f, 0x6d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x54, 0x6f, 0x70, 0x4d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x12, 0x74, 0x6f, 0x70, 0x4d,
	0x6f, 0x76, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x6a,
	0x0a, 0x17, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x48, 0x00, 0x52, 0x15, 0x61, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x24, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x20, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x7d, 0x0a, 0x18, 0x70, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x65, 0x70, 0x73, 0x53, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x16, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x69, 0x70, 0x73,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x23, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x75, 0x72, 0x53, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x20, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x72, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x6d, 0x0a, 0x18,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x48, 0x00, 0x52, 0x16, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x13, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x11, 0x69,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0xda, 0x02,
	0x0a, 0x1e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4c, 0x0a, 0x11, 0x74, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x63, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x74, 0x41, 0x6e, 0x64, 0x43, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x63, 0x74, 0x61, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x63, 0x74, 0x61, 0x73, 0x12, 0x4c, 0x0a, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x72, 0x0a, 0x14, 0x4e, 0x61,
	0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x5a, 0x0a, 0x12, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x52, 0x11, 0x6e, 0x61, 0x76,
	0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x73, 0x22, 0x88,
	0x02, 0x0a, 0x10, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x67,
	0x67, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x14, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x5f, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12,
	0x57, 0x0a, 0x16, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f,
	0x67, 0x67, 0x6c, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x14, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x6f,
	0x67, 0x67, 0x6c, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xc3, 0x06, 0x0a, 0x19, 0x50, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x63, 0x0a, 0x0c, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x0b, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x1a, 0xe0, 0x04, 0x0a,
	0x0b, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x12, 0x45, 0x0a, 0x0d,
	0x74, 0x6f, 0x70, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x74, 0x6f, 0x70, 0x54, 0x65, 0x78, 0x74, 0x54,
	0x61, 0x67, 0x73, 0x12, 0x78, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x52, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a,
	0x12, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x12, 0x4b,
	0x0a, 0x10, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x62, 0x6f, 0x74,
	0x74, 0x6f, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x54, 0x61, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x51, 0x0a, 0x13, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x64, 0x65, 0x6e, 0x64,
	0x75, 0x6d, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x11, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x41, 0x64, 0x64, 0x65, 0x6e, 0x64, 0x75, 0x6d, 0x42,
	0x61, 0x72, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22,
	0x86, 0x02, 0x0a, 0x27, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x11, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x12, 0x41, 0x0a, 0x0f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12,
	0x57, 0x0a, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x22, 0x86, 0x02, 0x0a, 0x12, 0x54, 0x6f, 0x70,
	0x4d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x59, 0x0a, 0x12, 0x74, 0x6f, 0x70, 0x5f, 0x6d, 0x6f,
	0x76, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x54,
	0x6f, 0x70, 0x4d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x10, 0x74, 0x6f, 0x70, 0x4d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xcc, 0x04, 0x0a, 0x10, 0x54, 0x6f, 0x70, 0x4d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x10, 0x74, 0x6f, 0x70, 0x5f, 0x67, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x74, 0x6f, 0x70, 0x47,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x64, 0x0a, 0x10, 0x74, 0x6f,
	0x70, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x54, 0x6f, 0x70, 0x4d, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6c, 0x65,
	0x52, 0x0e, 0x74, 0x6f, 0x70, 0x47, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x40, 0x0a, 0x0f, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x0d, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x73, 0x65, 0x72, 0x73, 0x54, 0x65,
	0x78, 0x74, 0x12, 0x62, 0x0a, 0x0f, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x73, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x54, 0x6f, 0x70, 0x4d, 0x6f, 0x76, 0x65, 0x72,
	0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x6f,
	0x76, 0x65, 0x72, 0x54, 0x69, 0x6c, 0x65, 0x52, 0x0d, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x73, 0x65,
	0x72, 0x54, 0x69, 0x6c, 0x65, 0x73, 0x1a, 0xe7, 0x01, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4d, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x65,
	0x78, 0x74, 0x73, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x22, 0xf7, 0x05, 0x0a, 0x15, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x60, 0x0a, 0x0b, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67,
	0x52, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x12, 0x64, 0x0a, 0x0c,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x1a, 0x81, 0x02, 0x0a, 0x0e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x61,
	0x67, 0x12, 0x48, 0x0a, 0x0e, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x75, 0x6e,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x1a, 0x7b, 0x0a,
	0x10, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x51, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe2, 0x05, 0x0a, 0x20, 0x50,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42,
	0x0a, 0x10, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65,
	0x78, 0x74, 0x12, 0x68, 0x0a, 0x0b, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x43, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0a, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x1a,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0a, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x45, 0x0a,
	0x0d, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x76, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x72,
	0x65, 0x43, 0x74, 0x61, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x1a, 0xcb, 0x01, 0x0a, 0x0b, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x6f,
	0x67, 0x67, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a,
	0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x74,
	0x6f, 0x67, 0x67, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x22,
	0xd5, 0x02, 0x0a, 0x18, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x3d, 0x0a, 0x09,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x84, 0x01, 0x0a, 0x17,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x6c, 0x69,
	0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x1a, 0x73, 0x0a, 0x19, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc0, 0x07, 0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x73,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x31, 0x0a, 0x07, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x44, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52, 0x07, 0x64, 0x69, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x73,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x7e, 0x0a, 0x19, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65,
	0x6c, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17, 0x63, 0x61,
	0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x82, 0x05, 0x0a, 0x1b, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61,
	0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x73, 0x63,
	0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x7e, 0x0a, 0x24, 0x75, 0x6e, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x20, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x42,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x96, 0x01, 0x0a, 0x19, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c,
	0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x1a, 0x97, 0x02, 0x0a, 0x17, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x49, 0x0a, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x11,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x42, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x65, 0x78, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f,
	0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x63, 0x6f,
	0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x22, 0xe6, 0x01, 0x0a, 0x20, 0x4d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x41,
	0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x11, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x04, 0x6e, 0x65, 0x77, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6e,
	0x65, 0x77, 0x73, 0x22, 0xe0, 0x01, 0x0a, 0x17, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x55, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f,
	0x6e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x22, 0xa4, 0x05, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x72, 0x69, 0x73, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x62, 0x61,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x74, 0x73,
	0x2e, 0x42, 0x61, 0x72, 0x52, 0x04, 0x62, 0x61, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x0a, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x3b, 0x0a, 0x07, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x73,
	0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x53, 0x0a,
	0x0d, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x12, 0x72, 0x0a, 0x15, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x13, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xc4, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b,
	0x0a, 0x0c, 0x6c, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x6c, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x4f, 0x0a, 0x0b,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0xe0, 0x02,
	0x0a, 0x26, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x4e, 0x65, 0x78, 0x74, 0x53,
	0x74, 0x65, 0x70, 0x73, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x11, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x22, 0xbb, 0x03, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x63, 0x6f, 0x72,
	0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x53, 0x0a, 0x0d, 0x62, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0xe2,
	0x03, 0x0a, 0x24, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x75, 0x72, 0x53, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0d, 0x73, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x73,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x4c, 0x0a, 0x10, 0x62,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x53,
	0x0a, 0x0d, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x22, 0xe9, 0x03, 0x0a, 0x16, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x31, 0x0a, 0x07, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x44, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52, 0x07, 0x64, 0x69, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x42, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x70, 0x68, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x72, 0x12, 0x5b, 0x0a,
	0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x42,
	0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescOnce sync.Once
	file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescData = file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDesc
)

func file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescData)
	})
	return file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDescData
}

var file_api_frontend_insights_secrets_portfolio_tracker_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_api_frontend_insights_secrets_portfolio_tracker_proto_goTypes = []interface{}{
	(PortFolioTrackerTopBarIcon_IconType)(0),        // 0: frontend.insights.secrets.PortFolioTrackerTopBarIcon.IconType
	(*PortFolioTrackerLandingPageHeader)(nil),       // 1: frontend.insights.secrets.PortFolioTrackerLandingPageHeader
	(*PortFolioTrackerTopBarIcon)(nil),              // 2: frontend.insights.secrets.PortFolioTrackerTopBarIcon
	(*PortfolioTrackerComponent)(nil),               // 3: frontend.insights.secrets.PortfolioTrackerComponent
	(*PortfolioTrackerTitleComponent)(nil),          // 4: frontend.insights.secrets.PortfolioTrackerTitleComponent
	(*NavigationToggleList)(nil),                    // 5: frontend.insights.secrets.NavigationToggleList
	(*NavigationToggle)(nil),                        // 6: frontend.insights.secrets.NavigationToggle
	(*PortfolioSummaryComponent)(nil),               // 7: frontend.insights.secrets.PortfolioSummaryComponent
	(*PortfolioTrackerRollingAnimationDetails)(nil), // 8: frontend.insights.secrets.PortfolioTrackerRollingAnimationDetails
	(*TopMoversComponent)(nil),                      // 9: frontend.insights.secrets.TopMoversComponent
	(*TopMoversSection)(nil),                        // 10: frontend.insights.secrets.TopMoversSection
	(*AssetDetailsComponent)(nil),                   // 11: frontend.insights.secrets.AssetDetailsComponent
	(*PortfolioTrackerAssetDetailsCard)(nil),        // 12: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard
	(*PortfolioTrackerLineItem)(nil),                // 13: frontend.insights.secrets.PortfolioTrackerLineItem
	(*NewsSection)(nil),                             // 14: frontend.insights.secrets.NewsSection
	(*MarketComparisonAndNewsComponent)(nil),        // 15: frontend.insights.secrets.MarketComparisonAndNewsComponent
	(*MarketComparisonSection)(nil),                 // 16: frontend.insights.secrets.MarketComparisonSection
	(*ComparisonChart)(nil),                         // 17: frontend.insights.secrets.ComparisonChart
	(*PortfolioNextStepsSuggestionsComponent)(nil),  // 18: frontend.insights.secrets.PortfolioNextStepsSuggestionsComponent
	(*ActionCard)(nil),                              // 19: frontend.insights.secrets.ActionCard
	(*InvestmentBehaviourStickersComponent)(nil),    // 20: frontend.insights.secrets.InvestmentBehaviourStickersComponent
	(*MarketInsightComponent)(nil),                  // 21: frontend.insights.secrets.MarketInsightComponent
	(*PortfolioSummaryComponent_SummaryCard)(nil),   // 22: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard
	(*TopMoversSection_AssetMoverTile)(nil),         // 23: frontend.insights.secrets.TopMoversSection.AssetMoverTile
	(*AssetDetailsComponent_AssetFilterTag)(nil),    // 24: frontend.insights.secrets.AssetDetailsComponent.AssetFilterTag
	nil, // 25: frontend.insights.secrets.AssetDetailsComponent.DetailCardsEntry
	(*PortfolioTrackerAssetDetailsCard_ToggleValue)(nil), // 26: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.ToggleValue
	nil, // 27: frontend.insights.secrets.PortfolioTrackerLineItem.LineItemDisplayValueEntry
	(*NewsSection_NewsCarouselIndicatorConfig)(nil),                         // 28: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig
	(*NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig)(nil), // 29: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.SelectedIndicatorConfig
	(*ComparisonChart_ReferenceLineConfig)(nil),                             // 30: frontend.insights.secrets.ComparisonChart.ReferenceLineConfig
	(*common.VisualElement)(nil),                                            // 31: api.typesv2.common.VisualElement
	(*deeplink.Deeplink)(nil),                                               // 32: frontend.deeplink.Deeplink
	(*ui.IconTextComponent)(nil),                                            // 33: api.typesv2.ui.IconTextComponent
	(*common.Text)(nil),                                                     // 34: api.typesv2.common.Text
	(*typesv2.Money)(nil),                                                   // 35: api.typesv2.Money
	(*widget.BackgroundColour)(nil),                                         // 36: api.typesv2.common.ui.widget.BackgroundColour
	(*ui1.LineItem)(nil),                                                    // 37: frontend.investment.ui.LineItem
	(*ui.Divider)(nil),                                                      // 38: api.typesv2.ui.Divider
	(*charts.Bar)(nil),                                                      // 39: api.typesv2.ui.charts.Bar
	(*ui1.LineItemHeadingTag)(nil),                                          // 40: frontend.investment.ui.LineItemHeadingTag
}
var file_api_frontend_insights_secrets_portfolio_tracker_proto_depIdxs = []int32{
	2,   // 0: frontend.insights.secrets.PortFolioTrackerLandingPageHeader.right_aligned_icons:type_name -> frontend.insights.secrets.PortFolioTrackerTopBarIcon
	0,   // 1: frontend.insights.secrets.PortFolioTrackerTopBarIcon.icon_type:type_name -> frontend.insights.secrets.PortFolioTrackerTopBarIcon.IconType
	31,  // 2: frontend.insights.secrets.PortFolioTrackerTopBarIcon.icon:type_name -> api.typesv2.common.VisualElement
	32,  // 3: frontend.insights.secrets.PortFolioTrackerTopBarIcon.deeplink:type_name -> frontend.deeplink.Deeplink
	4,   // 4: frontend.insights.secrets.PortfolioTrackerComponent.title_component:type_name -> frontend.insights.secrets.PortfolioTrackerTitleComponent
	5,   // 5: frontend.insights.secrets.PortfolioTrackerComponent.navigation_toggles:type_name -> frontend.insights.secrets.NavigationToggleList
	7,   // 6: frontend.insights.secrets.PortfolioTrackerComponent.portfolio_summary_component:type_name -> frontend.insights.secrets.PortfolioSummaryComponent
	9,   // 7: frontend.insights.secrets.PortfolioTrackerComponent.top_movers_component:type_name -> frontend.insights.secrets.TopMoversComponent
	11,  // 8: frontend.insights.secrets.PortfolioTrackerComponent.asset_details_component:type_name -> frontend.insights.secrets.AssetDetailsComponent
	15,  // 9: frontend.insights.secrets.PortfolioTrackerComponent.market_comparison_and_news_component:type_name -> frontend.insights.secrets.MarketComparisonAndNewsComponent
	18,  // 10: frontend.insights.secrets.PortfolioTrackerComponent.portfolio_tips_component:type_name -> frontend.insights.secrets.PortfolioNextStepsSuggestionsComponent
	20,  // 11: frontend.insights.secrets.PortfolioTrackerComponent.investment_stage_stickers_component:type_name -> frontend.insights.secrets.InvestmentBehaviourStickersComponent
	21,  // 12: frontend.insights.secrets.PortfolioTrackerComponent.market_insight_component:type_name -> frontend.insights.secrets.MarketInsightComponent
	33,  // 13: frontend.insights.secrets.PortfolioTrackerComponent.icon_text_component:type_name -> api.typesv2.ui.IconTextComponent
	34,  // 14: frontend.insights.secrets.PortfolioTrackerTitleComponent.title:type_name -> api.typesv2.common.Text
	34,  // 15: frontend.insights.secrets.PortfolioTrackerTitleComponent.sub_title:type_name -> api.typesv2.common.Text
	33,  // 16: frontend.insights.secrets.PortfolioTrackerTitleComponent.t_and_c_component:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 17: frontend.insights.secrets.PortfolioTrackerTitleComponent.ctas:type_name -> api.typesv2.ui.IconTextComponent
	31,  // 18: frontend.insights.secrets.PortfolioTrackerTitleComponent.background_image:type_name -> api.typesv2.common.VisualElement
	6,   // 19: frontend.insights.secrets.NavigationToggleList.navigation_toggles:type_name -> frontend.insights.secrets.NavigationToggle
	33,  // 20: frontend.insights.secrets.NavigationToggle.selected_toggle_icon:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 21: frontend.insights.secrets.NavigationToggle.unselected_toggle_icon:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 22: frontend.insights.secrets.PortfolioSummaryComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	22,  // 23: frontend.insights.secrets.PortfolioSummaryComponent.summary_card:type_name -> frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard
	35,  // 24: frontend.insights.secrets.PortfolioTrackerRollingAnimationDetails.updated_portfolio:type_name -> api.typesv2.Money
	34,  // 25: frontend.insights.secrets.PortfolioTrackerRollingAnimationDetails.currency_symbol:type_name -> api.typesv2.common.Text
	33,  // 26: frontend.insights.secrets.PortfolioTrackerRollingAnimationDetails.updated_portfolio_text:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 27: frontend.insights.secrets.TopMoversComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	34,  // 28: frontend.insights.secrets.TopMoversComponent.sub_title:type_name -> api.typesv2.common.Text
	10,  // 29: frontend.insights.secrets.TopMoversComponent.top_movers_section:type_name -> frontend.insights.secrets.TopMoversSection
	34,  // 30: frontend.insights.secrets.TopMoversSection.top_gainers_text:type_name -> api.typesv2.common.Text
	23,  // 31: frontend.insights.secrets.TopMoversSection.top_gainer_tiles:type_name -> frontend.insights.secrets.TopMoversSection.AssetMoverTile
	34,  // 32: frontend.insights.secrets.TopMoversSection.top_losers_text:type_name -> api.typesv2.common.Text
	23,  // 33: frontend.insights.secrets.TopMoversSection.top_loser_tiles:type_name -> frontend.insights.secrets.TopMoversSection.AssetMoverTile
	33,  // 34: frontend.insights.secrets.AssetDetailsComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	24,  // 35: frontend.insights.secrets.AssetDetailsComponent.filter_tags:type_name -> frontend.insights.secrets.AssetDetailsComponent.AssetFilterTag
	25,  // 36: frontend.insights.secrets.AssetDetailsComponent.detail_cards:type_name -> frontend.insights.secrets.AssetDetailsComponent.DetailCardsEntry
	34,  // 37: frontend.insights.secrets.AssetDetailsComponent.sub_title:type_name -> api.typesv2.common.Text
	34,  // 38: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.asset_count_text:type_name -> api.typesv2.common.Text
	26,  // 39: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.toggle_list:type_name -> frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.ToggleValue
	13,  // 40: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.line_items:type_name -> frontend.insights.secrets.PortfolioTrackerLineItem
	33,  // 41: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.view_more_cta:type_name -> api.typesv2.ui.IconTextComponent
	36,  // 42: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	37,  // 43: frontend.insights.secrets.PortfolioTrackerLineItem.line_item:type_name -> frontend.investment.ui.LineItem
	27,  // 44: frontend.insights.secrets.PortfolioTrackerLineItem.line_item_display_value:type_name -> frontend.insights.secrets.PortfolioTrackerLineItem.LineItemDisplayValueEntry
	33,  // 45: frontend.insights.secrets.NewsSection.title:type_name -> api.typesv2.ui.IconTextComponent
	38,  // 46: frontend.insights.secrets.NewsSection.divider:type_name -> api.typesv2.ui.Divider
	33,  // 47: frontend.insights.secrets.NewsSection.news_items:type_name -> api.typesv2.ui.IconTextComponent
	28,  // 48: frontend.insights.secrets.NewsSection.carousel_indicator_config:type_name -> frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig
	16,  // 49: frontend.insights.secrets.MarketComparisonAndNewsComponent.market_comparison:type_name -> frontend.insights.secrets.MarketComparisonSection
	14,  // 50: frontend.insights.secrets.MarketComparisonAndNewsComponent.news:type_name -> frontend.insights.secrets.NewsSection
	33,  // 51: frontend.insights.secrets.MarketComparisonSection.title:type_name -> api.typesv2.ui.IconTextComponent
	34,  // 52: frontend.insights.secrets.MarketComparisonSection.sub_title:type_name -> api.typesv2.common.Text
	17,  // 53: frontend.insights.secrets.MarketComparisonSection.comparison_chart:type_name -> frontend.insights.secrets.ComparisonChart
	39,  // 54: frontend.insights.secrets.ComparisonChart.bars:type_name -> api.typesv2.ui.charts.Bar
	34,  // 55: frontend.insights.secrets.ComparisonChart.date_label:type_name -> api.typesv2.common.Text
	33,  // 56: frontend.insights.secrets.ComparisonChart.legends:type_name -> api.typesv2.ui.IconTextComponent
	36,  // 57: frontend.insights.secrets.ComparisonChart.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	36,  // 58: frontend.insights.secrets.ComparisonChart.border_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	30,  // 59: frontend.insights.secrets.ComparisonChart.reference_line_config:type_name -> frontend.insights.secrets.ComparisonChart.ReferenceLineConfig
	33,  // 60: frontend.insights.secrets.PortfolioNextStepsSuggestionsComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	34,  // 61: frontend.insights.secrets.PortfolioNextStepsSuggestionsComponent.description:type_name -> api.typesv2.common.Text
	31,  // 62: frontend.insights.secrets.PortfolioNextStepsSuggestionsComponent.distribution_chart:type_name -> api.typesv2.common.VisualElement
	19,  // 63: frontend.insights.secrets.PortfolioNextStepsSuggestionsComponent.action_cards:type_name -> frontend.insights.secrets.ActionCard
	34,  // 64: frontend.insights.secrets.ActionCard.title:type_name -> api.typesv2.common.Text
	34,  // 65: frontend.insights.secrets.ActionCard.description:type_name -> api.typesv2.common.Text
	31,  // 66: frontend.insights.secrets.ActionCard.icon:type_name -> api.typesv2.common.VisualElement
	33,  // 67: frontend.insights.secrets.ActionCard.cta:type_name -> api.typesv2.ui.IconTextComponent
	36,  // 68: frontend.insights.secrets.ActionCard.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	36,  // 69: frontend.insights.secrets.ActionCard.border_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	33,  // 70: frontend.insights.secrets.InvestmentBehaviourStickersComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	34,  // 71: frontend.insights.secrets.InvestmentBehaviourStickersComponent.description:type_name -> api.typesv2.common.Text
	31,  // 72: frontend.insights.secrets.InvestmentBehaviourStickersComponent.sticker_image:type_name -> api.typesv2.common.VisualElement
	31,  // 73: frontend.insights.secrets.InvestmentBehaviourStickersComponent.background_image:type_name -> api.typesv2.common.VisualElement
	33,  // 74: frontend.insights.secrets.InvestmentBehaviourStickersComponent.cta:type_name -> api.typesv2.ui.IconTextComponent
	36,  // 75: frontend.insights.secrets.InvestmentBehaviourStickersComponent.border_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	33,  // 76: frontend.insights.secrets.MarketInsightComponent.header:type_name -> api.typesv2.ui.IconTextComponent
	38,  // 77: frontend.insights.secrets.MarketInsightComponent.divider:type_name -> api.typesv2.ui.Divider
	34,  // 78: frontend.insights.secrets.MarketInsightComponent.description:type_name -> api.typesv2.common.Text
	31,  // 79: frontend.insights.secrets.MarketInsightComponent.graph_image:type_name -> api.typesv2.common.VisualElement
	34,  // 80: frontend.insights.secrets.MarketInsightComponent.disclaimer:type_name -> api.typesv2.common.Text
	36,  // 81: frontend.insights.secrets.MarketInsightComponent.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	33,  // 82: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.top_text_tags:type_name -> api.typesv2.ui.IconTextComponent
	8,   // 83: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.change_value_animation:type_name -> frontend.insights.secrets.PortfolioTrackerRollingAnimationDetails
	33,  // 84: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.change_percent_tag:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 85: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.bottom_text_tags:type_name -> api.typesv2.ui.IconTextComponent
	31,  // 86: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.right_image:type_name -> api.typesv2.common.VisualElement
	33,  // 87: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.bottom_addendum_bar:type_name -> api.typesv2.ui.IconTextComponent
	36,  // 88: frontend.insights.secrets.PortfolioSummaryComponent.SummaryCard.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	34,  // 89: frontend.insights.secrets.TopMoversSection.AssetMoverTile.asset_name:type_name -> api.typesv2.common.Text
	33,  // 90: frontend.insights.secrets.TopMoversSection.AssetMoverTile.change_value_texts:type_name -> api.typesv2.ui.IconTextComponent
	36,  // 91: frontend.insights.secrets.TopMoversSection.AssetMoverTile.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	33,  // 92: frontend.insights.secrets.AssetDetailsComponent.AssetFilterTag.selected_tag:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 93: frontend.insights.secrets.AssetDetailsComponent.AssetFilterTag.unselected_tag:type_name -> api.typesv2.ui.IconTextComponent
	12,  // 94: frontend.insights.secrets.AssetDetailsComponent.DetailCardsEntry.value:type_name -> frontend.insights.secrets.PortfolioTrackerAssetDetailsCard
	33,  // 95: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.ToggleValue.aggregated_value:type_name -> api.typesv2.ui.IconTextComponent
	33,  // 96: frontend.insights.secrets.PortfolioTrackerAssetDetailsCard.ToggleValue.toggle_text:type_name -> api.typesv2.ui.IconTextComponent
	40,  // 97: frontend.insights.secrets.PortfolioTrackerLineItem.LineItemDisplayValueEntry.value:type_name -> frontend.investment.ui.LineItemHeadingTag
	36,  // 98: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.unselected_index_indicator_bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	29,  // 99: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.selected_indicator_config:type_name -> frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.SelectedIndicatorConfig
	36,  // 100: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.SelectedIndicatorConfig.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	34,  // 101: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.SelectedIndicatorConfig.selected_index_text:type_name -> api.typesv2.common.Text
	34,  // 102: frontend.insights.secrets.NewsSection.NewsCarouselIndicatorConfig.SelectedIndicatorConfig.total_count_text:type_name -> api.typesv2.common.Text
	34,  // 103: frontend.insights.secrets.ComparisonChart.ReferenceLineConfig.leading_text:type_name -> api.typesv2.common.Text
	36,  // 104: frontend.insights.secrets.ComparisonChart.ReferenceLineConfig.line_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	105, // [105:105] is the sub-list for method output_type
	105, // [105:105] is the sub-list for method input_type
	105, // [105:105] is the sub-list for extension type_name
	105, // [105:105] is the sub-list for extension extendee
	0,   // [0:105] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_secrets_portfolio_tracker_proto_init() }
func file_api_frontend_insights_secrets_portfolio_tracker_proto_init() {
	if File_api_frontend_insights_secrets_portfolio_tracker_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortFolioTrackerLandingPageHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortFolioTrackerTopBarIcon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioTrackerComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioTrackerTitleComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NavigationToggleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NavigationToggle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioSummaryComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioTrackerRollingAnimationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopMoversComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopMoversSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetDetailsComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioTrackerAssetDetailsCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioTrackerLineItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketComparisonAndNewsComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketComparisonSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComparisonChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioNextStepsSuggestionsComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentBehaviourStickersComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketInsightComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioSummaryComponent_SummaryCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopMoversSection_AssetMoverTile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetDetailsComponent_AssetFilterTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioTrackerAssetDetailsCard_ToggleValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsSection_NewsCarouselIndicatorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComparisonChart_ReferenceLineConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*PortfolioTrackerComponent_TitleComponent)(nil),
		(*PortfolioTrackerComponent_NavigationToggles)(nil),
		(*PortfolioTrackerComponent_PortfolioSummaryComponent)(nil),
		(*PortfolioTrackerComponent_TopMoversComponent)(nil),
		(*PortfolioTrackerComponent_AssetDetailsComponent)(nil),
		(*PortfolioTrackerComponent_MarketComparisonAndNewsComponent)(nil),
		(*PortfolioTrackerComponent_PortfolioTipsComponent)(nil),
		(*PortfolioTrackerComponent_InvestmentStageStickersComponent)(nil),
		(*PortfolioTrackerComponent_MarketInsightComponent)(nil),
		(*PortfolioTrackerComponent_IconTextComponent)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_insights_secrets_portfolio_tracker_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_secrets_portfolio_tracker_proto_depIdxs,
		EnumInfos:         file_api_frontend_insights_secrets_portfolio_tracker_proto_enumTypes,
		MessageInfos:      file_api_frontend_insights_secrets_portfolio_tracker_proto_msgTypes,
	}.Build()
	File_api_frontend_insights_secrets_portfolio_tracker_proto = out.File
	file_api_frontend_insights_secrets_portfolio_tracker_proto_rawDesc = nil
	file_api_frontend_insights_secrets_portfolio_tracker_proto_goTypes = nil
	file_api_frontend_insights_secrets_portfolio_tracker_proto_depIdxs = nil
}
