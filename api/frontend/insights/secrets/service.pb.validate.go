// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/secrets/service.proto

package secrets

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	secrets "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = secrets.Provenance(0)
)

// Validate checks the field values on GetSecretAnalyserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretAnalyserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretAnalyserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretAnalyserRequestMultiError, or nil if none found.
func (m *GetSecretAnalyserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretAnalyserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretAnalyserRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretAnalyserRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretAnalyserRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SecretName

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecretAnalyserRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecretAnalyserRequestValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecretAnalyserRequestValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SecretCollectionName

	// no validation rules for Provenance

	if len(errors) > 0 {
		return GetSecretAnalyserRequestMultiError(errors)
	}

	return nil
}

// GetSecretAnalyserRequestMultiError is an error wrapping multiple validation
// errors returned by GetSecretAnalyserRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSecretAnalyserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretAnalyserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretAnalyserRequestMultiError) AllErrors() []error { return m }

// GetSecretAnalyserRequestValidationError is the validation error returned by
// GetSecretAnalyserRequest.Validate if the designated constraints aren't met.
type GetSecretAnalyserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretAnalyserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretAnalyserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretAnalyserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretAnalyserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretAnalyserRequestValidationError) ErrorName() string {
	return "GetSecretAnalyserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretAnalyserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretAnalyserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretAnalyserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretAnalyserRequestValidationError{}

// Validate checks the field values on GetSecretAnalyserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretAnalyserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretAnalyserResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretAnalyserResponseMultiError, or nil if none found.
func (m *GetSecretAnalyserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretAnalyserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretAnalyserResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretAnalyserResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretAnalyserResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecretAnalyserResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretAnalyserResponseValidationError{
					field:  "SecretAnalyserResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretAnalyserResponseValidationError{
					field:  "SecretAnalyserResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecretAnalyserResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretAnalyserResponseValidationError{
				field:  "SecretAnalyserResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecretAnalyserResponseMultiError(errors)
	}

	return nil
}

// GetSecretAnalyserResponseMultiError is an error wrapping multiple validation
// errors returned by GetSecretAnalyserResponse.ValidateAll() if the
// designated constraints aren't met.
type GetSecretAnalyserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretAnalyserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretAnalyserResponseMultiError) AllErrors() []error { return m }

// GetSecretAnalyserResponseValidationError is the validation error returned by
// GetSecretAnalyserResponse.Validate if the designated constraints aren't met.
type GetSecretAnalyserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretAnalyserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretAnalyserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretAnalyserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretAnalyserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretAnalyserResponseValidationError) ErrorName() string {
	return "GetSecretAnalyserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretAnalyserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretAnalyserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretAnalyserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretAnalyserResponseValidationError{}

// Validate checks the field values on SecretAnalyserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretAnalyserResponseMultiError, or nil if none found.
func (m *SecretAnalyserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventProperties

	switch v := m.Response.(type) {
	case *SecretAnalyserResponse_SecretAnalyser:
		if v == nil {
			err := SecretAnalyserResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSecretAnalyser()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserResponseValidationError{
						field:  "SecretAnalyser",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserResponseValidationError{
						field:  "SecretAnalyser",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSecretAnalyser()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserResponseValidationError{
					field:  "SecretAnalyser",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserResponse_RedirectDeeplink:
		if v == nil {
			err := SecretAnalyserResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRedirectDeeplink()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserResponseValidationError{
						field:  "RedirectDeeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserResponseValidationError{
						field:  "RedirectDeeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRedirectDeeplink()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserResponseValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretAnalyserResponseMultiError(errors)
	}

	return nil
}

// SecretAnalyserResponseMultiError is an error wrapping multiple validation
// errors returned by SecretAnalyserResponse.ValidateAll() if the designated
// constraints aren't met.
type SecretAnalyserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserResponseMultiError) AllErrors() []error { return m }

// SecretAnalyserResponseValidationError is the validation error returned by
// SecretAnalyserResponse.Validate if the designated constraints aren't met.
type SecretAnalyserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserResponseValidationError) ErrorName() string {
	return "SecretAnalyserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SecretAnalyserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserResponseValidationError{}

// Validate checks the field values on AddSecretToFavouritesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddSecretToFavouritesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddSecretToFavouritesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddSecretToFavouritesRequestMultiError, or nil if none found.
func (m *AddSecretToFavouritesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddSecretToFavouritesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddSecretToFavouritesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddSecretToFavouritesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddSecretToFavouritesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddSecretToFavouritesRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddSecretToFavouritesRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddSecretToFavouritesRequestValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddSecretToFavouritesRequestMultiError(errors)
	}

	return nil
}

// AddSecretToFavouritesRequestMultiError is an error wrapping multiple
// validation errors returned by AddSecretToFavouritesRequest.ValidateAll() if
// the designated constraints aren't met.
type AddSecretToFavouritesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddSecretToFavouritesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddSecretToFavouritesRequestMultiError) AllErrors() []error { return m }

// AddSecretToFavouritesRequestValidationError is the validation error returned
// by AddSecretToFavouritesRequest.Validate if the designated constraints
// aren't met.
type AddSecretToFavouritesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddSecretToFavouritesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddSecretToFavouritesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddSecretToFavouritesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddSecretToFavouritesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddSecretToFavouritesRequestValidationError) ErrorName() string {
	return "AddSecretToFavouritesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddSecretToFavouritesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddSecretToFavouritesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddSecretToFavouritesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddSecretToFavouritesRequestValidationError{}

// Validate checks the field values on AddSecretToFavouritesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddSecretToFavouritesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddSecretToFavouritesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AddSecretToFavouritesResponseMultiError, or nil if none found.
func (m *AddSecretToFavouritesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddSecretToFavouritesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddSecretToFavouritesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddSecretToFavouritesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddSecretToFavouritesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFavouriteIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddSecretToFavouritesResponseValidationError{
					field:  "FavouriteIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddSecretToFavouritesResponseValidationError{
					field:  "FavouriteIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFavouriteIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddSecretToFavouritesResponseValidationError{
				field:  "FavouriteIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddSecretToFavouritesResponseMultiError(errors)
	}

	return nil
}

// AddSecretToFavouritesResponseMultiError is an error wrapping multiple
// validation errors returned by AddSecretToFavouritesResponse.ValidateAll()
// if the designated constraints aren't met.
type AddSecretToFavouritesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddSecretToFavouritesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddSecretToFavouritesResponseMultiError) AllErrors() []error { return m }

// AddSecretToFavouritesResponseValidationError is the validation error
// returned by AddSecretToFavouritesResponse.Validate if the designated
// constraints aren't met.
type AddSecretToFavouritesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddSecretToFavouritesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddSecretToFavouritesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddSecretToFavouritesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddSecretToFavouritesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddSecretToFavouritesResponseValidationError) ErrorName() string {
	return "AddSecretToFavouritesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddSecretToFavouritesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddSecretToFavouritesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddSecretToFavouritesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddSecretToFavouritesResponseValidationError{}

// Validate checks the field values on GetSecretSummariesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretSummariesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretSummariesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretSummariesRequestMultiError, or nil if none found.
func (m *GetSecretSummariesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretSummariesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Entrypoint

	if len(errors) > 0 {
		return GetSecretSummariesRequestMultiError(errors)
	}

	return nil
}

// GetSecretSummariesRequestMultiError is an error wrapping multiple validation
// errors returned by GetSecretSummariesRequest.ValidateAll() if the
// designated constraints aren't met.
type GetSecretSummariesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretSummariesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretSummariesRequestMultiError) AllErrors() []error { return m }

// GetSecretSummariesRequestValidationError is the validation error returned by
// GetSecretSummariesRequest.Validate if the designated constraints aren't met.
type GetSecretSummariesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretSummariesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretSummariesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretSummariesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretSummariesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretSummariesRequestValidationError) ErrorName() string {
	return "GetSecretSummariesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretSummariesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretSummariesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretSummariesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretSummariesRequestValidationError{}

// Validate checks the field values on GetSecretSummariesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretSummariesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretSummariesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretSummariesResponseMultiError, or nil if none found.
func (m *GetSecretSummariesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretSummariesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecretSummaries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecretSummariesResponseValidationError{
						field:  fmt.Sprintf("SecretSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecretSummariesResponseValidationError{
						field:  fmt.Sprintf("SecretSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecretSummariesResponseValidationError{
					field:  fmt.Sprintf("SecretSummaries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponseValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShowHideButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "ShowHideButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "ShowHideButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShowHideButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponseValidationError{
				field:  "ShowHideButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponseValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponseValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecretSummariesResponseMultiError(errors)
	}

	return nil
}

// GetSecretSummariesResponseMultiError is an error wrapping multiple
// validation errors returned by GetSecretSummariesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSecretSummariesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretSummariesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretSummariesResponseMultiError) AllErrors() []error { return m }

// GetSecretSummariesResponseValidationError is the validation error returned
// by GetSecretSummariesResponse.Validate if the designated constraints aren't met.
type GetSecretSummariesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretSummariesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretSummariesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretSummariesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretSummariesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretSummariesResponseValidationError) ErrorName() string {
	return "GetSecretSummariesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretSummariesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretSummariesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretSummariesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretSummariesResponseValidationError{}

// Validate checks the field values on GetSecretLibraryPageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretLibraryPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretLibraryPageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretLibraryPageRequestMultiError, or nil if none found.
func (m *GetSecretLibraryPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretLibraryPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretLibraryPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretLibraryPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretLibraryPageRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecretLibraryPageRequestMultiError(errors)
	}

	return nil
}

// GetSecretLibraryPageRequestMultiError is an error wrapping multiple
// validation errors returned by GetSecretLibraryPageRequest.ValidateAll() if
// the designated constraints aren't met.
type GetSecretLibraryPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretLibraryPageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretLibraryPageRequestMultiError) AllErrors() []error { return m }

// GetSecretLibraryPageRequestValidationError is the validation error returned
// by GetSecretLibraryPageRequest.Validate if the designated constraints
// aren't met.
type GetSecretLibraryPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretLibraryPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretLibraryPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretLibraryPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretLibraryPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretLibraryPageRequestValidationError) ErrorName() string {
	return "GetSecretLibraryPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretLibraryPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretLibraryPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretLibraryPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretLibraryPageRequestValidationError{}

// Validate checks the field values on GetSecretLibraryPageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretLibraryPageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretLibraryPageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretLibraryPageResponseMultiError, or nil if none found.
func (m *GetSecretLibraryPageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretLibraryPageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretLibraryPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretLibraryPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretLibraryPageResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecretLandingPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretLibraryPageResponseValidationError{
					field:  "SecretLandingPage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretLibraryPageResponseValidationError{
					field:  "SecretLandingPage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecretLandingPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretLibraryPageResponseValidationError{
				field:  "SecretLandingPage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecretLibraryPageResponseMultiError(errors)
	}

	return nil
}

// GetSecretLibraryPageResponseMultiError is an error wrapping multiple
// validation errors returned by GetSecretLibraryPageResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSecretLibraryPageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretLibraryPageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretLibraryPageResponseMultiError) AllErrors() []error { return m }

// GetSecretLibraryPageResponseValidationError is the validation error returned
// by GetSecretLibraryPageResponse.Validate if the designated constraints
// aren't met.
type GetSecretLibraryPageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretLibraryPageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretLibraryPageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretLibraryPageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretLibraryPageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretLibraryPageResponseValidationError) ErrorName() string {
	return "GetSecretLibraryPageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretLibraryPageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretLibraryPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretLibraryPageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretLibraryPageResponseValidationError{}

// Validate checks the field values on WealthAnalyserReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthAnalyserReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthAnalyserReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthAnalyserReportRequestMultiError, or nil if none found.
func (m *WealthAnalyserReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthAnalyserReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportType

	if len(errors) > 0 {
		return WealthAnalyserReportRequestMultiError(errors)
	}

	return nil
}

// WealthAnalyserReportRequestMultiError is an error wrapping multiple
// validation errors returned by WealthAnalyserReportRequest.ValidateAll() if
// the designated constraints aren't met.
type WealthAnalyserReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthAnalyserReportRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthAnalyserReportRequestMultiError) AllErrors() []error { return m }

// WealthAnalyserReportRequestValidationError is the validation error returned
// by WealthAnalyserReportRequest.Validate if the designated constraints
// aren't met.
type WealthAnalyserReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthAnalyserReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthAnalyserReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthAnalyserReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthAnalyserReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthAnalyserReportRequestValidationError) ErrorName() string {
	return "WealthAnalyserReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e WealthAnalyserReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthAnalyserReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthAnalyserReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthAnalyserReportRequestValidationError{}

// Validate checks the field values on WealthAnalyserReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WealthAnalyserReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WealthAnalyserReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WealthAnalyserReportResponseMultiError, or nil if none found.
func (m *WealthAnalyserReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *WealthAnalyserReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WealthAnalyserReportResponseValidationError{
					field:  "Report",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WealthAnalyserReportResponseValidationError{
					field:  "Report",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WealthAnalyserReportResponseValidationError{
				field:  "Report",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Response.(type) {
	case *WealthAnalyserReportResponse_WealthAnalyserReport:
		if v == nil {
			err := WealthAnalyserReportResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWealthAnalyserReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthAnalyserReportResponseValidationError{
						field:  "WealthAnalyserReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthAnalyserReportResponseValidationError{
						field:  "WealthAnalyserReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWealthAnalyserReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthAnalyserReportResponseValidationError{
					field:  "WealthAnalyserReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WealthAnalyserReportResponse_RedirectDeeplink:
		if v == nil {
			err := WealthAnalyserReportResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRedirectDeeplink()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WealthAnalyserReportResponseValidationError{
						field:  "RedirectDeeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WealthAnalyserReportResponseValidationError{
						field:  "RedirectDeeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRedirectDeeplink()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WealthAnalyserReportResponseValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WealthAnalyserReportResponseMultiError(errors)
	}

	return nil
}

// WealthAnalyserReportResponseMultiError is an error wrapping multiple
// validation errors returned by WealthAnalyserReportResponse.ValidateAll() if
// the designated constraints aren't met.
type WealthAnalyserReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WealthAnalyserReportResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WealthAnalyserReportResponseMultiError) AllErrors() []error { return m }

// WealthAnalyserReportResponseValidationError is the validation error returned
// by WealthAnalyserReportResponse.Validate if the designated constraints
// aren't met.
type WealthAnalyserReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WealthAnalyserReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WealthAnalyserReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WealthAnalyserReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WealthAnalyserReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WealthAnalyserReportResponseValidationError) ErrorName() string {
	return "WealthAnalyserReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e WealthAnalyserReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWealthAnalyserReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WealthAnalyserReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WealthAnalyserReportResponseValidationError{}

// Validate checks the field values on GetWealthAnalyserWidgetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWealthAnalyserWidgetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWealthAnalyserWidgetRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetWealthAnalyserWidgetRequestMultiError, or nil if none found.
func (m *GetWealthAnalyserWidgetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWealthAnalyserWidgetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthAnalyserWidgetRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Entrypoint

	if len(errors) > 0 {
		return GetWealthAnalyserWidgetRequestMultiError(errors)
	}

	return nil
}

// GetWealthAnalyserWidgetRequestMultiError is an error wrapping multiple
// validation errors returned by GetWealthAnalyserWidgetRequest.ValidateAll()
// if the designated constraints aren't met.
type GetWealthAnalyserWidgetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWealthAnalyserWidgetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWealthAnalyserWidgetRequestMultiError) AllErrors() []error { return m }

// GetWealthAnalyserWidgetRequestValidationError is the validation error
// returned by GetWealthAnalyserWidgetRequest.Validate if the designated
// constraints aren't met.
type GetWealthAnalyserWidgetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWealthAnalyserWidgetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWealthAnalyserWidgetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWealthAnalyserWidgetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWealthAnalyserWidgetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWealthAnalyserWidgetRequestValidationError) ErrorName() string {
	return "GetWealthAnalyserWidgetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWealthAnalyserWidgetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWealthAnalyserWidgetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWealthAnalyserWidgetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWealthAnalyserWidgetRequestValidationError{}

// Validate checks the field values on GetWealthAnalyserWidgetResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWealthAnalyserWidgetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWealthAnalyserWidgetResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetWealthAnalyserWidgetResponseMultiError, or nil if none found.
func (m *GetWealthAnalyserWidgetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWealthAnalyserWidgetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthAnalyserWidgetResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthAnalyserWidgetResponseValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetWealthAnalyserWidgetResponseValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgLinearGradient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
					field:  "BgLinearGradient",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthAnalyserWidgetResponseValidationError{
					field:  "BgLinearGradient",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgLinearGradient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthAnalyserWidgetResponseValidationError{
				field:  "BgLinearGradient",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWealthAnalyserWidgetResponseMultiError(errors)
	}

	return nil
}

// GetWealthAnalyserWidgetResponseMultiError is an error wrapping multiple
// validation errors returned by GetWealthAnalyserWidgetResponse.ValidateAll()
// if the designated constraints aren't met.
type GetWealthAnalyserWidgetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWealthAnalyserWidgetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWealthAnalyserWidgetResponseMultiError) AllErrors() []error { return m }

// GetWealthAnalyserWidgetResponseValidationError is the validation error
// returned by GetWealthAnalyserWidgetResponse.Validate if the designated
// constraints aren't met.
type GetWealthAnalyserWidgetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWealthAnalyserWidgetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWealthAnalyserWidgetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWealthAnalyserWidgetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWealthAnalyserWidgetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWealthAnalyserWidgetResponseValidationError) ErrorName() string {
	return "GetWealthAnalyserWidgetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWealthAnalyserWidgetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWealthAnalyserWidgetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWealthAnalyserWidgetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWealthAnalyserWidgetResponseValidationError{}

// Validate checks the field values on GetSecretsFooterRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretsFooterRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretsFooterRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretsFooterRequestMultiError, or nil if none found.
func (m *GetSecretsFooterRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretsFooterRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SecretCollectionName

	if len(errors) > 0 {
		return GetSecretsFooterRequestMultiError(errors)
	}

	return nil
}

// GetSecretsFooterRequestMultiError is an error wrapping multiple validation
// errors returned by GetSecretsFooterRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSecretsFooterRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretsFooterRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretsFooterRequestMultiError) AllErrors() []error { return m }

// GetSecretsFooterRequestValidationError is the validation error returned by
// GetSecretsFooterRequest.Validate if the designated constraints aren't met.
type GetSecretsFooterRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretsFooterRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretsFooterRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretsFooterRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretsFooterRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretsFooterRequestValidationError) ErrorName() string {
	return "GetSecretsFooterRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretsFooterRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretsFooterRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretsFooterRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretsFooterRequestValidationError{}

// Validate checks the field values on GetSecretsFooterResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSecretsFooterResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSecretsFooterResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSecretsFooterResponseMultiError, or nil if none found.
func (m *GetSecretsFooterResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretsFooterResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "BackButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "BackButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterResponseValidationError{
				field:  "BackButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "NextButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "NextButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterResponseValidationError{
				field:  "NextButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterResponseValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIndexLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "IndexLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "IndexLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIndexLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterResponseValidationError{
				field:  "IndexLabel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecretDeeplinks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSecretsFooterResponseValidationError{
						field:  fmt.Sprintf("SecretDeeplinks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSecretsFooterResponseValidationError{
						field:  fmt.Sprintf("SecretDeeplinks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSecretsFooterResponseValidationError{
					field:  fmt.Sprintf("SecretDeeplinks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFlowCompletionScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "FlowCompletionScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretsFooterResponseValidationError{
					field:  "FlowCompletionScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFlowCompletionScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretsFooterResponseValidationError{
				field:  "FlowCompletionScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecretsFooterResponseMultiError(errors)
	}

	return nil
}

// GetSecretsFooterResponseMultiError is an error wrapping multiple validation
// errors returned by GetSecretsFooterResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSecretsFooterResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretsFooterResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretsFooterResponseMultiError) AllErrors() []error { return m }

// GetSecretsFooterResponseValidationError is the validation error returned by
// GetSecretsFooterResponse.Validate if the designated constraints aren't met.
type GetSecretsFooterResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretsFooterResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretsFooterResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretsFooterResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretsFooterResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretsFooterResponseValidationError) ErrorName() string {
	return "GetSecretsFooterResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretsFooterResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretsFooterResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretsFooterResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretsFooterResponseValidationError{}

// Validate checks the field values on GetPortfolioTrackerLandingPageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPortfolioTrackerLandingPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPortfolioTrackerLandingPageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPortfolioTrackerLandingPageRequestMultiError, or nil if none found.
func (m *GetPortfolioTrackerLandingPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortfolioTrackerLandingPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetPortfolioTrackerLandingPageRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerLandingPageRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPortfolioTrackerLandingPageRequestMultiError(errors)
	}

	return nil
}

// GetPortfolioTrackerLandingPageRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetPortfolioTrackerLandingPageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPortfolioTrackerLandingPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortfolioTrackerLandingPageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortfolioTrackerLandingPageRequestMultiError) AllErrors() []error { return m }

// GetPortfolioTrackerLandingPageRequestValidationError is the validation error
// returned by GetPortfolioTrackerLandingPageRequest.Validate if the
// designated constraints aren't met.
type GetPortfolioTrackerLandingPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortfolioTrackerLandingPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortfolioTrackerLandingPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortfolioTrackerLandingPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortfolioTrackerLandingPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortfolioTrackerLandingPageRequestValidationError) ErrorName() string {
	return "GetPortfolioTrackerLandingPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortfolioTrackerLandingPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortfolioTrackerLandingPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortfolioTrackerLandingPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortfolioTrackerLandingPageRequestValidationError{}

// Validate checks the field values on GetPortfolioTrackerLandingPageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPortfolioTrackerLandingPageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPortfolioTrackerLandingPageResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetPortfolioTrackerLandingPageResponseMultiError, or nil if none found.
func (m *GetPortfolioTrackerLandingPageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortfolioTrackerLandingPageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerLandingPageResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerLandingPageResponseValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFixedComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
						field:  fmt.Sprintf("FixedComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
						field:  fmt.Sprintf("FixedComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPortfolioTrackerLandingPageResponseValidationError{
					field:  fmt.Sprintf("FixedComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetScrollableComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
						field:  fmt.Sprintf("ScrollableComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
						field:  fmt.Sprintf("ScrollableComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPortfolioTrackerLandingPageResponseValidationError{
					field:  fmt.Sprintf("ScrollableComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerLandingPageResponseValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFooterComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
						field:  fmt.Sprintf("FooterComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPortfolioTrackerLandingPageResponseValidationError{
						field:  fmt.Sprintf("FooterComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPortfolioTrackerLandingPageResponseValidationError{
					field:  fmt.Sprintf("FooterComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPortfolioTrackerLandingPageResponseMultiError(errors)
	}

	return nil
}

// GetPortfolioTrackerLandingPageResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPortfolioTrackerLandingPageResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPortfolioTrackerLandingPageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortfolioTrackerLandingPageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortfolioTrackerLandingPageResponseMultiError) AllErrors() []error { return m }

// GetPortfolioTrackerLandingPageResponseValidationError is the validation
// error returned by GetPortfolioTrackerLandingPageResponse.Validate if the
// designated constraints aren't met.
type GetPortfolioTrackerLandingPageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortfolioTrackerLandingPageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortfolioTrackerLandingPageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortfolioTrackerLandingPageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortfolioTrackerLandingPageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortfolioTrackerLandingPageResponseValidationError) ErrorName() string {
	return "GetPortfolioTrackerLandingPageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortfolioTrackerLandingPageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortfolioTrackerLandingPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortfolioTrackerLandingPageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortfolioTrackerLandingPageResponseValidationError{}

// Validate checks the field values on
// GetPortfolioTrackerAssetDetailsPageRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPortfolioTrackerAssetDetailsPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPortfolioTrackerAssetDetailsPageRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPortfolioTrackerAssetDetailsPageRequestMultiError, or nil if none found.
func (m *GetPortfolioTrackerAssetDetailsPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortfolioTrackerAssetDetailsPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetPortfolioTrackerAssetDetailsPageRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerAssetDetailsPageRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerAssetDetailsPageRequestValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPortfolioTrackerAssetDetailsPageRequestMultiError(errors)
	}

	return nil
}

// GetPortfolioTrackerAssetDetailsPageRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetPortfolioTrackerAssetDetailsPageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPortfolioTrackerAssetDetailsPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortfolioTrackerAssetDetailsPageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortfolioTrackerAssetDetailsPageRequestMultiError) AllErrors() []error { return m }

// GetPortfolioTrackerAssetDetailsPageRequestValidationError is the validation
// error returned by GetPortfolioTrackerAssetDetailsPageRequest.Validate if
// the designated constraints aren't met.
type GetPortfolioTrackerAssetDetailsPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortfolioTrackerAssetDetailsPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortfolioTrackerAssetDetailsPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortfolioTrackerAssetDetailsPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortfolioTrackerAssetDetailsPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortfolioTrackerAssetDetailsPageRequestValidationError) ErrorName() string {
	return "GetPortfolioTrackerAssetDetailsPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortfolioTrackerAssetDetailsPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortfolioTrackerAssetDetailsPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortfolioTrackerAssetDetailsPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortfolioTrackerAssetDetailsPageRequestValidationError{}

// Validate checks the field values on
// GetPortfolioTrackerAssetDetailsPageResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPortfolioTrackerAssetDetailsPageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPortfolioTrackerAssetDetailsPageResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPortfolioTrackerAssetDetailsPageResponseMultiError, or nil if none found.
func (m *GetPortfolioTrackerAssetDetailsPageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortfolioTrackerAssetDetailsPageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerAssetDetailsPageResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageResponseValidationError{
					field:  "AssetDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioTrackerAssetDetailsPageResponseValidationError{
					field:  "AssetDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioTrackerAssetDetailsPageResponseValidationError{
				field:  "AssetDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPortfolioTrackerAssetDetailsPageResponseMultiError(errors)
	}

	return nil
}

// GetPortfolioTrackerAssetDetailsPageResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPortfolioTrackerAssetDetailsPageResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPortfolioTrackerAssetDetailsPageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortfolioTrackerAssetDetailsPageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortfolioTrackerAssetDetailsPageResponseMultiError) AllErrors() []error { return m }

// GetPortfolioTrackerAssetDetailsPageResponseValidationError is the validation
// error returned by GetPortfolioTrackerAssetDetailsPageResponse.Validate if
// the designated constraints aren't met.
type GetPortfolioTrackerAssetDetailsPageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortfolioTrackerAssetDetailsPageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortfolioTrackerAssetDetailsPageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortfolioTrackerAssetDetailsPageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortfolioTrackerAssetDetailsPageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortfolioTrackerAssetDetailsPageResponseValidationError) ErrorName() string {
	return "GetPortfolioTrackerAssetDetailsPageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortfolioTrackerAssetDetailsPageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortfolioTrackerAssetDetailsPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortfolioTrackerAssetDetailsPageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortfolioTrackerAssetDetailsPageResponseValidationError{}

// Validate checks the field values on
// GetSecretSummariesResponse_ShowHideComponent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetSecretSummariesResponse_ShowHideComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSecretSummariesResponse_ShowHideComponent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetSecretSummariesResponse_ShowHideComponentMultiError, or nil if none found.
func (m *GetSecretSummariesResponse_ShowHideComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSecretSummariesResponse_ShowHideComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShowButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponse_ShowHideComponentValidationError{
					field:  "ShowButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponse_ShowHideComponentValidationError{
					field:  "ShowButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShowButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponse_ShowHideComponentValidationError{
				field:  "ShowButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHideButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSecretSummariesResponse_ShowHideComponentValidationError{
					field:  "HideButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSecretSummariesResponse_ShowHideComponentValidationError{
					field:  "HideButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHideButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSecretSummariesResponse_ShowHideComponentValidationError{
				field:  "HideButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSecretSummariesResponse_ShowHideComponentMultiError(errors)
	}

	return nil
}

// GetSecretSummariesResponse_ShowHideComponentMultiError is an error wrapping
// multiple validation errors returned by
// GetSecretSummariesResponse_ShowHideComponent.ValidateAll() if the
// designated constraints aren't met.
type GetSecretSummariesResponse_ShowHideComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSecretSummariesResponse_ShowHideComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSecretSummariesResponse_ShowHideComponentMultiError) AllErrors() []error { return m }

// GetSecretSummariesResponse_ShowHideComponentValidationError is the
// validation error returned by
// GetSecretSummariesResponse_ShowHideComponent.Validate if the designated
// constraints aren't met.
type GetSecretSummariesResponse_ShowHideComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSecretSummariesResponse_ShowHideComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSecretSummariesResponse_ShowHideComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSecretSummariesResponse_ShowHideComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSecretSummariesResponse_ShowHideComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSecretSummariesResponse_ShowHideComponentValidationError) ErrorName() string {
	return "GetSecretSummariesResponse_ShowHideComponentValidationError"
}

// Error satisfies the builtin error interface
func (e GetSecretSummariesResponse_ShowHideComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSecretSummariesResponse_ShowHideComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSecretSummariesResponse_ShowHideComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSecretSummariesResponse_ShowHideComponentValidationError{}
