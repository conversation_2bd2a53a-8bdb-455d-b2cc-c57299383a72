// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/secrets/portfolio_tracker.proto

package secrets

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PortFolioTrackerLandingPageHeader with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PortFolioTrackerLandingPageHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortFolioTrackerLandingPageHeader
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PortFolioTrackerLandingPageHeaderMultiError, or nil if none found.
func (m *PortFolioTrackerLandingPageHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *PortFolioTrackerLandingPageHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRightAlignedIcons() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortFolioTrackerLandingPageHeaderValidationError{
						field:  fmt.Sprintf("RightAlignedIcons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortFolioTrackerLandingPageHeaderValidationError{
						field:  fmt.Sprintf("RightAlignedIcons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortFolioTrackerLandingPageHeaderValidationError{
					field:  fmt.Sprintf("RightAlignedIcons[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PortFolioTrackerLandingPageHeaderMultiError(errors)
	}

	return nil
}

// PortFolioTrackerLandingPageHeaderMultiError is an error wrapping multiple
// validation errors returned by
// PortFolioTrackerLandingPageHeader.ValidateAll() if the designated
// constraints aren't met.
type PortFolioTrackerLandingPageHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortFolioTrackerLandingPageHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortFolioTrackerLandingPageHeaderMultiError) AllErrors() []error { return m }

// PortFolioTrackerLandingPageHeaderValidationError is the validation error
// returned by PortFolioTrackerLandingPageHeader.Validate if the designated
// constraints aren't met.
type PortFolioTrackerLandingPageHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortFolioTrackerLandingPageHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortFolioTrackerLandingPageHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortFolioTrackerLandingPageHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortFolioTrackerLandingPageHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortFolioTrackerLandingPageHeaderValidationError) ErrorName() string {
	return "PortFolioTrackerLandingPageHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e PortFolioTrackerLandingPageHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortFolioTrackerLandingPageHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortFolioTrackerLandingPageHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortFolioTrackerLandingPageHeaderValidationError{}

// Validate checks the field values on PortFolioTrackerTopBarIcon with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortFolioTrackerTopBarIcon) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortFolioTrackerTopBarIcon with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortFolioTrackerTopBarIconMultiError, or nil if none found.
func (m *PortFolioTrackerTopBarIcon) ValidateAll() error {
	return m.validate(true)
}

func (m *PortFolioTrackerTopBarIcon) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconType

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortFolioTrackerTopBarIconValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortFolioTrackerTopBarIconValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortFolioTrackerTopBarIconValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortFolioTrackerTopBarIconValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortFolioTrackerTopBarIconValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortFolioTrackerTopBarIconValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortFolioTrackerTopBarIconMultiError(errors)
	}

	return nil
}

// PortFolioTrackerTopBarIconMultiError is an error wrapping multiple
// validation errors returned by PortFolioTrackerTopBarIcon.ValidateAll() if
// the designated constraints aren't met.
type PortFolioTrackerTopBarIconMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortFolioTrackerTopBarIconMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortFolioTrackerTopBarIconMultiError) AllErrors() []error { return m }

// PortFolioTrackerTopBarIconValidationError is the validation error returned
// by PortFolioTrackerTopBarIcon.Validate if the designated constraints aren't met.
type PortFolioTrackerTopBarIconValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortFolioTrackerTopBarIconValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortFolioTrackerTopBarIconValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortFolioTrackerTopBarIconValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortFolioTrackerTopBarIconValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortFolioTrackerTopBarIconValidationError) ErrorName() string {
	return "PortFolioTrackerTopBarIconValidationError"
}

// Error satisfies the builtin error interface
func (e PortFolioTrackerTopBarIconValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortFolioTrackerTopBarIcon.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortFolioTrackerTopBarIconValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortFolioTrackerTopBarIconValidationError{}

// Validate checks the field values on PortfolioTrackerComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortfolioTrackerComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioTrackerComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortfolioTrackerComponentMultiError, or nil if none found.
func (m *PortfolioTrackerComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioTrackerComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Component.(type) {
	case *PortfolioTrackerComponent_TitleComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTitleComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "TitleComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "TitleComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTitleComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "TitleComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_NavigationToggles:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNavigationToggles()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "NavigationToggles",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "NavigationToggles",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNavigationToggles()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "NavigationToggles",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_PortfolioSummaryComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPortfolioSummaryComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "PortfolioSummaryComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "PortfolioSummaryComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPortfolioSummaryComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "PortfolioSummaryComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_TopMoversComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTopMoversComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "TopMoversComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "TopMoversComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTopMoversComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "TopMoversComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_AssetDetailsComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAssetDetailsComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "AssetDetailsComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "AssetDetailsComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAssetDetailsComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "AssetDetailsComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_MarketComparisonAndNewsComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMarketComparisonAndNewsComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "MarketComparisonAndNewsComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "MarketComparisonAndNewsComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMarketComparisonAndNewsComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "MarketComparisonAndNewsComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_PortfolioTipsComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPortfolioTipsComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "PortfolioTipsComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "PortfolioTipsComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPortfolioTipsComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "PortfolioTipsComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_InvestmentStageStickersComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInvestmentStageStickersComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "InvestmentStageStickersComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "InvestmentStageStickersComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInvestmentStageStickersComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "InvestmentStageStickersComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_MarketInsightComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMarketInsightComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "MarketInsightComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "MarketInsightComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMarketInsightComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "MarketInsightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PortfolioTrackerComponent_IconTextComponent:
		if v == nil {
			err := PortfolioTrackerComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIconTextComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "IconTextComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerComponentValidationError{
						field:  "IconTextComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIconTextComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerComponentValidationError{
					field:  "IconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PortfolioTrackerComponentMultiError(errors)
	}

	return nil
}

// PortfolioTrackerComponentMultiError is an error wrapping multiple validation
// errors returned by PortfolioTrackerComponent.ValidateAll() if the
// designated constraints aren't met.
type PortfolioTrackerComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioTrackerComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioTrackerComponentMultiError) AllErrors() []error { return m }

// PortfolioTrackerComponentValidationError is the validation error returned by
// PortfolioTrackerComponent.Validate if the designated constraints aren't met.
type PortfolioTrackerComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioTrackerComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioTrackerComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioTrackerComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioTrackerComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioTrackerComponentValidationError) ErrorName() string {
	return "PortfolioTrackerComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioTrackerComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioTrackerComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioTrackerComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioTrackerComponentValidationError{}

// Validate checks the field values on PortfolioTrackerTitleComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortfolioTrackerTitleComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioTrackerTitleComponent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PortfolioTrackerTitleComponentMultiError, or nil if none found.
func (m *PortfolioTrackerTitleComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioTrackerTitleComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerTitleComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerTitleComponentValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTAndCComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "TAndCComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "TAndCComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTAndCComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerTitleComponentValidationError{
				field:  "TAndCComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerTitleComponentValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerTitleComponentValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerTitleComponentValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerTitleComponentValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerTitleComponentValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortfolioTrackerTitleComponentMultiError(errors)
	}

	return nil
}

// PortfolioTrackerTitleComponentMultiError is an error wrapping multiple
// validation errors returned by PortfolioTrackerTitleComponent.ValidateAll()
// if the designated constraints aren't met.
type PortfolioTrackerTitleComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioTrackerTitleComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioTrackerTitleComponentMultiError) AllErrors() []error { return m }

// PortfolioTrackerTitleComponentValidationError is the validation error
// returned by PortfolioTrackerTitleComponent.Validate if the designated
// constraints aren't met.
type PortfolioTrackerTitleComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioTrackerTitleComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioTrackerTitleComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioTrackerTitleComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioTrackerTitleComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioTrackerTitleComponentValidationError) ErrorName() string {
	return "PortfolioTrackerTitleComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioTrackerTitleComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioTrackerTitleComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioTrackerTitleComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioTrackerTitleComponentValidationError{}

// Validate checks the field values on NavigationToggleList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NavigationToggleList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavigationToggleList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NavigationToggleListMultiError, or nil if none found.
func (m *NavigationToggleList) ValidateAll() error {
	return m.validate(true)
}

func (m *NavigationToggleList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNavigationToggles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NavigationToggleListValidationError{
						field:  fmt.Sprintf("NavigationToggles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NavigationToggleListValidationError{
						field:  fmt.Sprintf("NavigationToggles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NavigationToggleListValidationError{
					field:  fmt.Sprintf("NavigationToggles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NavigationToggleListMultiError(errors)
	}

	return nil
}

// NavigationToggleListMultiError is an error wrapping multiple validation
// errors returned by NavigationToggleList.ValidateAll() if the designated
// constraints aren't met.
type NavigationToggleListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavigationToggleListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavigationToggleListMultiError) AllErrors() []error { return m }

// NavigationToggleListValidationError is the validation error returned by
// NavigationToggleList.Validate if the designated constraints aren't met.
type NavigationToggleListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavigationToggleListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavigationToggleListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavigationToggleListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavigationToggleListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavigationToggleListValidationError) ErrorName() string {
	return "NavigationToggleListValidationError"
}

// Error satisfies the builtin error interface
func (e NavigationToggleListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavigationToggleList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavigationToggleListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavigationToggleListValidationError{}

// Validate checks the field values on NavigationToggle with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NavigationToggle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NavigationToggle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NavigationToggleMultiError, or nil if none found.
func (m *NavigationToggle) ValidateAll() error {
	return m.validate(true)
}

func (m *NavigationToggle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetSelectedToggleIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NavigationToggleValidationError{
					field:  "SelectedToggleIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NavigationToggleValidationError{
					field:  "SelectedToggleIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedToggleIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NavigationToggleValidationError{
				field:  "SelectedToggleIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnselectedToggleIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NavigationToggleValidationError{
					field:  "UnselectedToggleIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NavigationToggleValidationError{
					field:  "UnselectedToggleIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnselectedToggleIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NavigationToggleValidationError{
				field:  "UnselectedToggleIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSelected

	if len(errors) > 0 {
		return NavigationToggleMultiError(errors)
	}

	return nil
}

// NavigationToggleMultiError is an error wrapping multiple validation errors
// returned by NavigationToggle.ValidateAll() if the designated constraints
// aren't met.
type NavigationToggleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NavigationToggleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NavigationToggleMultiError) AllErrors() []error { return m }

// NavigationToggleValidationError is the validation error returned by
// NavigationToggle.Validate if the designated constraints aren't met.
type NavigationToggleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NavigationToggleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NavigationToggleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NavigationToggleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NavigationToggleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NavigationToggleValidationError) ErrorName() string { return "NavigationToggleValidationError" }

// Error satisfies the builtin error interface
func (e NavigationToggleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNavigationToggle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NavigationToggleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NavigationToggleValidationError{}

// Validate checks the field values on PortfolioSummaryComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortfolioSummaryComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioSummaryComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortfolioSummaryComponentMultiError, or nil if none found.
func (m *PortfolioSummaryComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioSummaryComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummaryCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponentValidationError{
					field:  "SummaryCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponentValidationError{
					field:  "SummaryCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummaryCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponentValidationError{
				field:  "SummaryCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortfolioSummaryComponentMultiError(errors)
	}

	return nil
}

// PortfolioSummaryComponentMultiError is an error wrapping multiple validation
// errors returned by PortfolioSummaryComponent.ValidateAll() if the
// designated constraints aren't met.
type PortfolioSummaryComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioSummaryComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioSummaryComponentMultiError) AllErrors() []error { return m }

// PortfolioSummaryComponentValidationError is the validation error returned by
// PortfolioSummaryComponent.Validate if the designated constraints aren't met.
type PortfolioSummaryComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioSummaryComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioSummaryComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioSummaryComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioSummaryComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioSummaryComponentValidationError) ErrorName() string {
	return "PortfolioSummaryComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioSummaryComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioSummaryComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioSummaryComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioSummaryComponentValidationError{}

// Validate checks the field values on PortfolioTrackerRollingAnimationDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PortfolioTrackerRollingAnimationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PortfolioTrackerRollingAnimationDetails with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// PortfolioTrackerRollingAnimationDetailsMultiError, or nil if none found.
func (m *PortfolioTrackerRollingAnimationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioTrackerRollingAnimationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedPortfolio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerRollingAnimationDetailsValidationError{
					field:  "UpdatedPortfolio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerRollingAnimationDetailsValidationError{
					field:  "UpdatedPortfolio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedPortfolio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerRollingAnimationDetailsValidationError{
				field:  "UpdatedPortfolio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrencySymbol()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerRollingAnimationDetailsValidationError{
					field:  "CurrencySymbol",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerRollingAnimationDetailsValidationError{
					field:  "CurrencySymbol",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrencySymbol()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerRollingAnimationDetailsValidationError{
				field:  "CurrencySymbol",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedPortfolioText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerRollingAnimationDetailsValidationError{
					field:  "UpdatedPortfolioText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerRollingAnimationDetailsValidationError{
					field:  "UpdatedPortfolioText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedPortfolioText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerRollingAnimationDetailsValidationError{
				field:  "UpdatedPortfolioText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortfolioTrackerRollingAnimationDetailsMultiError(errors)
	}

	return nil
}

// PortfolioTrackerRollingAnimationDetailsMultiError is an error wrapping
// multiple validation errors returned by
// PortfolioTrackerRollingAnimationDetails.ValidateAll() if the designated
// constraints aren't met.
type PortfolioTrackerRollingAnimationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioTrackerRollingAnimationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioTrackerRollingAnimationDetailsMultiError) AllErrors() []error { return m }

// PortfolioTrackerRollingAnimationDetailsValidationError is the validation
// error returned by PortfolioTrackerRollingAnimationDetails.Validate if the
// designated constraints aren't met.
type PortfolioTrackerRollingAnimationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioTrackerRollingAnimationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioTrackerRollingAnimationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioTrackerRollingAnimationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioTrackerRollingAnimationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioTrackerRollingAnimationDetailsValidationError) ErrorName() string {
	return "PortfolioTrackerRollingAnimationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioTrackerRollingAnimationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioTrackerRollingAnimationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioTrackerRollingAnimationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioTrackerRollingAnimationDetailsValidationError{}

// Validate checks the field values on TopMoversComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopMoversComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopMoversComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopMoversComponentMultiError, or nil if none found.
func (m *TopMoversComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *TopMoversComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversComponentValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversComponentValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversComponentValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopMoversSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversComponentValidationError{
					field:  "TopMoversSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversComponentValidationError{
					field:  "TopMoversSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopMoversSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversComponentValidationError{
				field:  "TopMoversSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TopMoversComponentMultiError(errors)
	}

	return nil
}

// TopMoversComponentMultiError is an error wrapping multiple validation errors
// returned by TopMoversComponent.ValidateAll() if the designated constraints
// aren't met.
type TopMoversComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopMoversComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopMoversComponentMultiError) AllErrors() []error { return m }

// TopMoversComponentValidationError is the validation error returned by
// TopMoversComponent.Validate if the designated constraints aren't met.
type TopMoversComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopMoversComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopMoversComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopMoversComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopMoversComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopMoversComponentValidationError) ErrorName() string {
	return "TopMoversComponentValidationError"
}

// Error satisfies the builtin error interface
func (e TopMoversComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopMoversComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopMoversComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopMoversComponentValidationError{}

// Validate checks the field values on TopMoversSection with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TopMoversSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopMoversSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopMoversSectionMultiError, or nil if none found.
func (m *TopMoversSection) ValidateAll() error {
	return m.validate(true)
}

func (m *TopMoversSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopGainersText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversSectionValidationError{
					field:  "TopGainersText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversSectionValidationError{
					field:  "TopGainersText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopGainersText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversSectionValidationError{
				field:  "TopGainersText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTopGainerTiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TopMoversSectionValidationError{
						field:  fmt.Sprintf("TopGainerTiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TopMoversSectionValidationError{
						field:  fmt.Sprintf("TopGainerTiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TopMoversSectionValidationError{
					field:  fmt.Sprintf("TopGainerTiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTopLosersText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversSectionValidationError{
					field:  "TopLosersText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversSectionValidationError{
					field:  "TopLosersText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopLosersText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversSectionValidationError{
				field:  "TopLosersText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTopLoserTiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TopMoversSectionValidationError{
						field:  fmt.Sprintf("TopLoserTiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TopMoversSectionValidationError{
						field:  fmt.Sprintf("TopLoserTiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TopMoversSectionValidationError{
					field:  fmt.Sprintf("TopLoserTiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TopMoversSectionMultiError(errors)
	}

	return nil
}

// TopMoversSectionMultiError is an error wrapping multiple validation errors
// returned by TopMoversSection.ValidateAll() if the designated constraints
// aren't met.
type TopMoversSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopMoversSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopMoversSectionMultiError) AllErrors() []error { return m }

// TopMoversSectionValidationError is the validation error returned by
// TopMoversSection.Validate if the designated constraints aren't met.
type TopMoversSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopMoversSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopMoversSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopMoversSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopMoversSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopMoversSectionValidationError) ErrorName() string { return "TopMoversSectionValidationError" }

// Error satisfies the builtin error interface
func (e TopMoversSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopMoversSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopMoversSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopMoversSectionValidationError{}

// Validate checks the field values on AssetDetailsComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssetDetailsComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetDetailsComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetDetailsComponentMultiError, or nil if none found.
func (m *AssetDetailsComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetDetailsComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetDetailsComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetDetailsComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetDetailsComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFilterTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssetDetailsComponentValidationError{
						field:  fmt.Sprintf("FilterTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssetDetailsComponentValidationError{
						field:  fmt.Sprintf("FilterTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssetDetailsComponentValidationError{
					field:  fmt.Sprintf("FilterTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetDetailCards()))
		i := 0
		for key := range m.GetDetailCards() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetDetailCards()[key]
			_ = val

			// no validation rules for DetailCards[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, AssetDetailsComponentValidationError{
							field:  fmt.Sprintf("DetailCards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, AssetDetailsComponentValidationError{
							field:  fmt.Sprintf("DetailCards[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return AssetDetailsComponentValidationError{
						field:  fmt.Sprintf("DetailCards[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetDetailsComponentValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetDetailsComponentValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetDetailsComponentValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AssetDetailsComponentMultiError(errors)
	}

	return nil
}

// AssetDetailsComponentMultiError is an error wrapping multiple validation
// errors returned by AssetDetailsComponent.ValidateAll() if the designated
// constraints aren't met.
type AssetDetailsComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetDetailsComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetDetailsComponentMultiError) AllErrors() []error { return m }

// AssetDetailsComponentValidationError is the validation error returned by
// AssetDetailsComponent.Validate if the designated constraints aren't met.
type AssetDetailsComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetDetailsComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetDetailsComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetDetailsComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetDetailsComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetDetailsComponentValidationError) ErrorName() string {
	return "AssetDetailsComponentValidationError"
}

// Error satisfies the builtin error interface
func (e AssetDetailsComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetDetailsComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetDetailsComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetDetailsComponentValidationError{}

// Validate checks the field values on PortfolioTrackerAssetDetailsCard with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PortfolioTrackerAssetDetailsCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioTrackerAssetDetailsCard with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PortfolioTrackerAssetDetailsCardMultiError, or nil if none found.
func (m *PortfolioTrackerAssetDetailsCard) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioTrackerAssetDetailsCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetType

	if all {
		switch v := interface{}(m.GetAssetCountText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
					field:  "AssetCountText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
					field:  "AssetCountText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetCountText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerAssetDetailsCardValidationError{
				field:  "AssetCountText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetToggleList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
						field:  fmt.Sprintf("ToggleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
						field:  fmt.Sprintf("ToggleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerAssetDetailsCardValidationError{
					field:  fmt.Sprintf("ToggleList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DefaultDisplayValueType

	for idx, item := range m.GetLineItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
						field:  fmt.Sprintf("LineItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
						field:  fmt.Sprintf("LineItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioTrackerAssetDetailsCardValidationError{
					field:  fmt.Sprintf("LineItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetViewMoreCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
					field:  "ViewMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
					field:  "ViewMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewMoreCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerAssetDetailsCardValidationError{
				field:  "ViewMoreCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCardValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerAssetDetailsCardValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortfolioTrackerAssetDetailsCardMultiError(errors)
	}

	return nil
}

// PortfolioTrackerAssetDetailsCardMultiError is an error wrapping multiple
// validation errors returned by
// PortfolioTrackerAssetDetailsCard.ValidateAll() if the designated
// constraints aren't met.
type PortfolioTrackerAssetDetailsCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioTrackerAssetDetailsCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioTrackerAssetDetailsCardMultiError) AllErrors() []error { return m }

// PortfolioTrackerAssetDetailsCardValidationError is the validation error
// returned by PortfolioTrackerAssetDetailsCard.Validate if the designated
// constraints aren't met.
type PortfolioTrackerAssetDetailsCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioTrackerAssetDetailsCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioTrackerAssetDetailsCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioTrackerAssetDetailsCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioTrackerAssetDetailsCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioTrackerAssetDetailsCardValidationError) ErrorName() string {
	return "PortfolioTrackerAssetDetailsCardValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioTrackerAssetDetailsCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioTrackerAssetDetailsCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioTrackerAssetDetailsCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioTrackerAssetDetailsCardValidationError{}

// Validate checks the field values on PortfolioTrackerLineItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortfolioTrackerLineItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioTrackerLineItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortfolioTrackerLineItemMultiError, or nil if none found.
func (m *PortfolioTrackerLineItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioTrackerLineItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLineItem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerLineItemValidationError{
					field:  "LineItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerLineItemValidationError{
					field:  "LineItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLineItem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerLineItemValidationError{
				field:  "LineItem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetLineItemDisplayValue()))
		i := 0
		for key := range m.GetLineItemDisplayValue() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLineItemDisplayValue()[key]
			_ = val

			// no validation rules for LineItemDisplayValue[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PortfolioTrackerLineItemValidationError{
							field:  fmt.Sprintf("LineItemDisplayValue[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PortfolioTrackerLineItemValidationError{
							field:  fmt.Sprintf("LineItemDisplayValue[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PortfolioTrackerLineItemValidationError{
						field:  fmt.Sprintf("LineItemDisplayValue[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return PortfolioTrackerLineItemMultiError(errors)
	}

	return nil
}

// PortfolioTrackerLineItemMultiError is an error wrapping multiple validation
// errors returned by PortfolioTrackerLineItem.ValidateAll() if the designated
// constraints aren't met.
type PortfolioTrackerLineItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioTrackerLineItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioTrackerLineItemMultiError) AllErrors() []error { return m }

// PortfolioTrackerLineItemValidationError is the validation error returned by
// PortfolioTrackerLineItem.Validate if the designated constraints aren't met.
type PortfolioTrackerLineItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioTrackerLineItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioTrackerLineItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioTrackerLineItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioTrackerLineItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioTrackerLineItemValidationError) ErrorName() string {
	return "PortfolioTrackerLineItemValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioTrackerLineItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioTrackerLineItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioTrackerLineItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioTrackerLineItemValidationError{}

// Validate checks the field values on NewsSection with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NewsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NewsSection with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NewsSectionMultiError, or
// nil if none found.
func (m *NewsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *NewsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDivider()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSectionValidationError{
					field:  "Divider",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSectionValidationError{
					field:  "Divider",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDivider()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSectionValidationError{
				field:  "Divider",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNewsItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NewsSectionValidationError{
						field:  fmt.Sprintf("NewsItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NewsSectionValidationError{
						field:  fmt.Sprintf("NewsItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NewsSectionValidationError{
					field:  fmt.Sprintf("NewsItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCarouselIndicatorConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSectionValidationError{
					field:  "CarouselIndicatorConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSectionValidationError{
					field:  "CarouselIndicatorConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCarouselIndicatorConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSectionValidationError{
				field:  "CarouselIndicatorConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NewsSectionMultiError(errors)
	}

	return nil
}

// NewsSectionMultiError is an error wrapping multiple validation errors
// returned by NewsSection.ValidateAll() if the designated constraints aren't met.
type NewsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NewsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NewsSectionMultiError) AllErrors() []error { return m }

// NewsSectionValidationError is the validation error returned by
// NewsSection.Validate if the designated constraints aren't met.
type NewsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NewsSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NewsSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NewsSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NewsSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NewsSectionValidationError) ErrorName() string { return "NewsSectionValidationError" }

// Error satisfies the builtin error interface
func (e NewsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNewsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NewsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NewsSectionValidationError{}

// Validate checks the field values on MarketComparisonAndNewsComponent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *MarketComparisonAndNewsComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarketComparisonAndNewsComponent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MarketComparisonAndNewsComponentMultiError, or nil if none found.
func (m *MarketComparisonAndNewsComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *MarketComparisonAndNewsComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetMarketComparison()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketComparisonAndNewsComponentValidationError{
					field:  "MarketComparison",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketComparisonAndNewsComponentValidationError{
					field:  "MarketComparison",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMarketComparison()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketComparisonAndNewsComponentValidationError{
				field:  "MarketComparison",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNews()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketComparisonAndNewsComponentValidationError{
					field:  "News",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketComparisonAndNewsComponentValidationError{
					field:  "News",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNews()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketComparisonAndNewsComponentValidationError{
				field:  "News",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarketComparisonAndNewsComponentMultiError(errors)
	}

	return nil
}

// MarketComparisonAndNewsComponentMultiError is an error wrapping multiple
// validation errors returned by
// MarketComparisonAndNewsComponent.ValidateAll() if the designated
// constraints aren't met.
type MarketComparisonAndNewsComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarketComparisonAndNewsComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarketComparisonAndNewsComponentMultiError) AllErrors() []error { return m }

// MarketComparisonAndNewsComponentValidationError is the validation error
// returned by MarketComparisonAndNewsComponent.Validate if the designated
// constraints aren't met.
type MarketComparisonAndNewsComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarketComparisonAndNewsComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarketComparisonAndNewsComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarketComparisonAndNewsComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarketComparisonAndNewsComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarketComparisonAndNewsComponentValidationError) ErrorName() string {
	return "MarketComparisonAndNewsComponentValidationError"
}

// Error satisfies the builtin error interface
func (e MarketComparisonAndNewsComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarketComparisonAndNewsComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarketComparisonAndNewsComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarketComparisonAndNewsComponentValidationError{}

// Validate checks the field values on MarketComparisonSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarketComparisonSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarketComparisonSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MarketComparisonSectionMultiError, or nil if none found.
func (m *MarketComparisonSection) ValidateAll() error {
	return m.validate(true)
}

func (m *MarketComparisonSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketComparisonSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketComparisonSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketComparisonSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketComparisonSectionValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketComparisonSectionValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketComparisonSectionValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComparisonChart()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketComparisonSectionValidationError{
					field:  "ComparisonChart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketComparisonSectionValidationError{
					field:  "ComparisonChart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComparisonChart()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketComparisonSectionValidationError{
				field:  "ComparisonChart",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarketComparisonSectionMultiError(errors)
	}

	return nil
}

// MarketComparisonSectionMultiError is an error wrapping multiple validation
// errors returned by MarketComparisonSection.ValidateAll() if the designated
// constraints aren't met.
type MarketComparisonSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarketComparisonSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarketComparisonSectionMultiError) AllErrors() []error { return m }

// MarketComparisonSectionValidationError is the validation error returned by
// MarketComparisonSection.Validate if the designated constraints aren't met.
type MarketComparisonSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarketComparisonSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarketComparisonSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarketComparisonSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarketComparisonSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarketComparisonSectionValidationError) ErrorName() string {
	return "MarketComparisonSectionValidationError"
}

// Error satisfies the builtin error interface
func (e MarketComparisonSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarketComparisonSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarketComparisonSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarketComparisonSectionValidationError{}

// Validate checks the field values on ComparisonChart with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ComparisonChart) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComparisonChart with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComparisonChartMultiError, or nil if none found.
func (m *ComparisonChart) ValidateAll() error {
	return m.validate(true)
}

func (m *ComparisonChart) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComparisonChartValidationError{
						field:  fmt.Sprintf("Bars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComparisonChartValidationError{
						field:  fmt.Sprintf("Bars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComparisonChartValidationError{
					field:  fmt.Sprintf("Bars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDateLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "DateLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "DateLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComparisonChartValidationError{
				field:  "DateLabel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLegends() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComparisonChartValidationError{
						field:  fmt.Sprintf("Legends[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComparisonChartValidationError{
						field:  fmt.Sprintf("Legends[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComparisonChartValidationError{
					field:  fmt.Sprintf("Legends[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComparisonChartValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComparisonChartValidationError{
				field:  "BorderColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReferenceLineConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "ReferenceLineConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComparisonChartValidationError{
					field:  "ReferenceLineConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReferenceLineConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComparisonChartValidationError{
				field:  "ReferenceLineConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ComparisonChartMultiError(errors)
	}

	return nil
}

// ComparisonChartMultiError is an error wrapping multiple validation errors
// returned by ComparisonChart.ValidateAll() if the designated constraints
// aren't met.
type ComparisonChartMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComparisonChartMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComparisonChartMultiError) AllErrors() []error { return m }

// ComparisonChartValidationError is the validation error returned by
// ComparisonChart.Validate if the designated constraints aren't met.
type ComparisonChartValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComparisonChartValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComparisonChartValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComparisonChartValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComparisonChartValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComparisonChartValidationError) ErrorName() string { return "ComparisonChartValidationError" }

// Error satisfies the builtin error interface
func (e ComparisonChartValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComparisonChart.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComparisonChartValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComparisonChartValidationError{}

// Validate checks the field values on PortfolioNextStepsSuggestionsComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PortfolioNextStepsSuggestionsComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PortfolioNextStepsSuggestionsComponent with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// PortfolioNextStepsSuggestionsComponentMultiError, or nil if none found.
func (m *PortfolioNextStepsSuggestionsComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioNextStepsSuggestionsComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioNextStepsSuggestionsComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioNextStepsSuggestionsComponentValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDistributionChart()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
					field:  "DistributionChart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
					field:  "DistributionChart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDistributionChart()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioNextStepsSuggestionsComponentValidationError{
				field:  "DistributionChart",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
						field:  fmt.Sprintf("ActionCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioNextStepsSuggestionsComponentValidationError{
						field:  fmt.Sprintf("ActionCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioNextStepsSuggestionsComponentValidationError{
					field:  fmt.Sprintf("ActionCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PortfolioNextStepsSuggestionsComponentMultiError(errors)
	}

	return nil
}

// PortfolioNextStepsSuggestionsComponentMultiError is an error wrapping
// multiple validation errors returned by
// PortfolioNextStepsSuggestionsComponent.ValidateAll() if the designated
// constraints aren't met.
type PortfolioNextStepsSuggestionsComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioNextStepsSuggestionsComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioNextStepsSuggestionsComponentMultiError) AllErrors() []error { return m }

// PortfolioNextStepsSuggestionsComponentValidationError is the validation
// error returned by PortfolioNextStepsSuggestionsComponent.Validate if the
// designated constraints aren't met.
type PortfolioNextStepsSuggestionsComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioNextStepsSuggestionsComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioNextStepsSuggestionsComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioNextStepsSuggestionsComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioNextStepsSuggestionsComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioNextStepsSuggestionsComponentValidationError) ErrorName() string {
	return "PortfolioNextStepsSuggestionsComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioNextStepsSuggestionsComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioNextStepsSuggestionsComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioNextStepsSuggestionsComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioNextStepsSuggestionsComponentValidationError{}

// Validate checks the field values on ActionCard with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActionCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionCard with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActionCardMultiError, or
// nil if none found.
func (m *ActionCard) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionCardValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionCardValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionCardValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionCardValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetBorderColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionCardValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionCardValidationError{
				field:  "BorderColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActionCardMultiError(errors)
	}

	return nil
}

// ActionCardMultiError is an error wrapping multiple validation errors
// returned by ActionCard.ValidateAll() if the designated constraints aren't met.
type ActionCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionCardMultiError) AllErrors() []error { return m }

// ActionCardValidationError is the validation error returned by
// ActionCard.Validate if the designated constraints aren't met.
type ActionCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionCardValidationError) ErrorName() string { return "ActionCardValidationError" }

// Error satisfies the builtin error interface
func (e ActionCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionCardValidationError{}

// Validate checks the field values on InvestmentBehaviourStickersComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InvestmentBehaviourStickersComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentBehaviourStickersComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InvestmentBehaviourStickersComponentMultiError, or nil if none found.
func (m *InvestmentBehaviourStickersComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentBehaviourStickersComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentBehaviourStickersComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentBehaviourStickersComponentValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStickerImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "StickerImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "StickerImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStickerImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentBehaviourStickersComponentValidationError{
				field:  "StickerImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentBehaviourStickersComponentValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentBehaviourStickersComponentValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentBehaviourStickersComponentValidationError{
					field:  "BorderColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentBehaviourStickersComponentValidationError{
				field:  "BorderColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InvestmentBehaviourStickersComponentMultiError(errors)
	}

	return nil
}

// InvestmentBehaviourStickersComponentMultiError is an error wrapping multiple
// validation errors returned by
// InvestmentBehaviourStickersComponent.ValidateAll() if the designated
// constraints aren't met.
type InvestmentBehaviourStickersComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentBehaviourStickersComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentBehaviourStickersComponentMultiError) AllErrors() []error { return m }

// InvestmentBehaviourStickersComponentValidationError is the validation error
// returned by InvestmentBehaviourStickersComponent.Validate if the designated
// constraints aren't met.
type InvestmentBehaviourStickersComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentBehaviourStickersComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentBehaviourStickersComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentBehaviourStickersComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentBehaviourStickersComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentBehaviourStickersComponentValidationError) ErrorName() string {
	return "InvestmentBehaviourStickersComponentValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentBehaviourStickersComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentBehaviourStickersComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentBehaviourStickersComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentBehaviourStickersComponentValidationError{}

// Validate checks the field values on MarketInsightComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarketInsightComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarketInsightComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MarketInsightComponentMultiError, or nil if none found.
func (m *MarketInsightComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *MarketInsightComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentType

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketInsightComponentValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDivider()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Divider",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Divider",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDivider()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketInsightComponentValidationError{
				field:  "Divider",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketInsightComponentValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGraphImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "GraphImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "GraphImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGraphImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketInsightComponentValidationError{
				field:  "GraphImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisclaimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Disclaimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "Disclaimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisclaimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketInsightComponentValidationError{
				field:  "Disclaimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarketInsightComponentValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarketInsightComponentValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if len(errors) > 0 {
		return MarketInsightComponentMultiError(errors)
	}

	return nil
}

// MarketInsightComponentMultiError is an error wrapping multiple validation
// errors returned by MarketInsightComponent.ValidateAll() if the designated
// constraints aren't met.
type MarketInsightComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarketInsightComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarketInsightComponentMultiError) AllErrors() []error { return m }

// MarketInsightComponentValidationError is the validation error returned by
// MarketInsightComponent.Validate if the designated constraints aren't met.
type MarketInsightComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarketInsightComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarketInsightComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarketInsightComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarketInsightComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarketInsightComponentValidationError) ErrorName() string {
	return "MarketInsightComponentValidationError"
}

// Error satisfies the builtin error interface
func (e MarketInsightComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarketInsightComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarketInsightComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarketInsightComponentValidationError{}

// Validate checks the field values on PortfolioSummaryComponent_SummaryCard
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PortfolioSummaryComponent_SummaryCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioSummaryComponent_SummaryCard
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PortfolioSummaryComponent_SummaryCardMultiError, or nil if none found.
func (m *PortfolioSummaryComponent_SummaryCard) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioSummaryComponent_SummaryCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTopTextTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
						field:  fmt.Sprintf("TopTextTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
						field:  fmt.Sprintf("TopTextTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioSummaryComponent_SummaryCardValidationError{
					field:  fmt.Sprintf("TopTextTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetChangeValueAnimation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "ChangeValueAnimation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "ChangeValueAnimation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChangeValueAnimation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponent_SummaryCardValidationError{
				field:  "ChangeValueAnimation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChangePercentTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "ChangePercentTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "ChangePercentTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChangePercentTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponent_SummaryCardValidationError{
				field:  "ChangePercentTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBottomTextTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
						field:  fmt.Sprintf("BottomTextTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
						field:  fmt.Sprintf("BottomTextTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioSummaryComponent_SummaryCardValidationError{
					field:  fmt.Sprintf("BottomTextTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRightImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "RightImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "RightImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponent_SummaryCardValidationError{
				field:  "RightImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomAddendumBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "BottomAddendumBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "BottomAddendumBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomAddendumBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponent_SummaryCardValidationError{
				field:  "BottomAddendumBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioSummaryComponent_SummaryCardValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioSummaryComponent_SummaryCardValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortfolioSummaryComponent_SummaryCardMultiError(errors)
	}

	return nil
}

// PortfolioSummaryComponent_SummaryCardMultiError is an error wrapping
// multiple validation errors returned by
// PortfolioSummaryComponent_SummaryCard.ValidateAll() if the designated
// constraints aren't met.
type PortfolioSummaryComponent_SummaryCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioSummaryComponent_SummaryCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioSummaryComponent_SummaryCardMultiError) AllErrors() []error { return m }

// PortfolioSummaryComponent_SummaryCardValidationError is the validation error
// returned by PortfolioSummaryComponent_SummaryCard.Validate if the
// designated constraints aren't met.
type PortfolioSummaryComponent_SummaryCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioSummaryComponent_SummaryCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioSummaryComponent_SummaryCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioSummaryComponent_SummaryCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioSummaryComponent_SummaryCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioSummaryComponent_SummaryCardValidationError) ErrorName() string {
	return "PortfolioSummaryComponent_SummaryCardValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioSummaryComponent_SummaryCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioSummaryComponent_SummaryCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioSummaryComponent_SummaryCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioSummaryComponent_SummaryCardValidationError{}

// Validate checks the field values on TopMoversSection_AssetMoverTile with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopMoversSection_AssetMoverTile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopMoversSection_AssetMoverTile with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TopMoversSection_AssetMoverTileMultiError, or nil if none found.
func (m *TopMoversSection_AssetMoverTile) ValidateAll() error {
	return m.validate(true)
}

func (m *TopMoversSection_AssetMoverTile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAssetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversSection_AssetMoverTileValidationError{
					field:  "AssetName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversSection_AssetMoverTileValidationError{
					field:  "AssetName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversSection_AssetMoverTileValidationError{
				field:  "AssetName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChangeValueTexts()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversSection_AssetMoverTileValidationError{
					field:  "ChangeValueTexts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversSection_AssetMoverTileValidationError{
					field:  "ChangeValueTexts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChangeValueTexts()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversSection_AssetMoverTileValidationError{
				field:  "ChangeValueTexts",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopMoversSection_AssetMoverTileValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopMoversSection_AssetMoverTileValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopMoversSection_AssetMoverTileValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TopMoversSection_AssetMoverTileMultiError(errors)
	}

	return nil
}

// TopMoversSection_AssetMoverTileMultiError is an error wrapping multiple
// validation errors returned by TopMoversSection_AssetMoverTile.ValidateAll()
// if the designated constraints aren't met.
type TopMoversSection_AssetMoverTileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopMoversSection_AssetMoverTileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopMoversSection_AssetMoverTileMultiError) AllErrors() []error { return m }

// TopMoversSection_AssetMoverTileValidationError is the validation error
// returned by TopMoversSection_AssetMoverTile.Validate if the designated
// constraints aren't met.
type TopMoversSection_AssetMoverTileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopMoversSection_AssetMoverTileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopMoversSection_AssetMoverTileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopMoversSection_AssetMoverTileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopMoversSection_AssetMoverTileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopMoversSection_AssetMoverTileValidationError) ErrorName() string {
	return "TopMoversSection_AssetMoverTileValidationError"
}

// Error satisfies the builtin error interface
func (e TopMoversSection_AssetMoverTileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopMoversSection_AssetMoverTile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopMoversSection_AssetMoverTileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopMoversSection_AssetMoverTileValidationError{}

// Validate checks the field values on AssetDetailsComponent_AssetFilterTag
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AssetDetailsComponent_AssetFilterTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetDetailsComponent_AssetFilterTag
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AssetDetailsComponent_AssetFilterTagMultiError, or nil if none found.
func (m *AssetDetailsComponent_AssetFilterTag) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetDetailsComponent_AssetFilterTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetType

	if all {
		switch v := interface{}(m.GetSelectedTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetDetailsComponent_AssetFilterTagValidationError{
					field:  "SelectedTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetDetailsComponent_AssetFilterTagValidationError{
					field:  "SelectedTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetDetailsComponent_AssetFilterTagValidationError{
				field:  "SelectedTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnselectedTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetDetailsComponent_AssetFilterTagValidationError{
					field:  "UnselectedTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetDetailsComponent_AssetFilterTagValidationError{
					field:  "UnselectedTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnselectedTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetDetailsComponent_AssetFilterTagValidationError{
				field:  "UnselectedTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSelected

	// no validation rules for IsDisabled

	if len(errors) > 0 {
		return AssetDetailsComponent_AssetFilterTagMultiError(errors)
	}

	return nil
}

// AssetDetailsComponent_AssetFilterTagMultiError is an error wrapping multiple
// validation errors returned by
// AssetDetailsComponent_AssetFilterTag.ValidateAll() if the designated
// constraints aren't met.
type AssetDetailsComponent_AssetFilterTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetDetailsComponent_AssetFilterTagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetDetailsComponent_AssetFilterTagMultiError) AllErrors() []error { return m }

// AssetDetailsComponent_AssetFilterTagValidationError is the validation error
// returned by AssetDetailsComponent_AssetFilterTag.Validate if the designated
// constraints aren't met.
type AssetDetailsComponent_AssetFilterTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetDetailsComponent_AssetFilterTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetDetailsComponent_AssetFilterTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetDetailsComponent_AssetFilterTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetDetailsComponent_AssetFilterTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetDetailsComponent_AssetFilterTagValidationError) ErrorName() string {
	return "AssetDetailsComponent_AssetFilterTagValidationError"
}

// Error satisfies the builtin error interface
func (e AssetDetailsComponent_AssetFilterTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetDetailsComponent_AssetFilterTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetDetailsComponent_AssetFilterTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetDetailsComponent_AssetFilterTagValidationError{}

// Validate checks the field values on
// PortfolioTrackerAssetDetailsCard_ToggleValue with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PortfolioTrackerAssetDetailsCard_ToggleValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PortfolioTrackerAssetDetailsCard_ToggleValue with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PortfolioTrackerAssetDetailsCard_ToggleValueMultiError, or nil if none found.
func (m *PortfolioTrackerAssetDetailsCard_ToggleValue) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioTrackerAssetDetailsCard_ToggleValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ToggleValueType

	if all {
		switch v := interface{}(m.GetAggregatedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{
					field:  "AggregatedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{
					field:  "AggregatedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAggregatedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{
				field:  "AggregatedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToggleText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{
					field:  "ToggleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{
					field:  "ToggleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToggleText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{
				field:  "ToggleText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PortfolioTrackerAssetDetailsCard_ToggleValueMultiError(errors)
	}

	return nil
}

// PortfolioTrackerAssetDetailsCard_ToggleValueMultiError is an error wrapping
// multiple validation errors returned by
// PortfolioTrackerAssetDetailsCard_ToggleValue.ValidateAll() if the
// designated constraints aren't met.
type PortfolioTrackerAssetDetailsCard_ToggleValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioTrackerAssetDetailsCard_ToggleValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioTrackerAssetDetailsCard_ToggleValueMultiError) AllErrors() []error { return m }

// PortfolioTrackerAssetDetailsCard_ToggleValueValidationError is the
// validation error returned by
// PortfolioTrackerAssetDetailsCard_ToggleValue.Validate if the designated
// constraints aren't met.
type PortfolioTrackerAssetDetailsCard_ToggleValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioTrackerAssetDetailsCard_ToggleValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioTrackerAssetDetailsCard_ToggleValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioTrackerAssetDetailsCard_ToggleValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioTrackerAssetDetailsCard_ToggleValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioTrackerAssetDetailsCard_ToggleValueValidationError) ErrorName() string {
	return "PortfolioTrackerAssetDetailsCard_ToggleValueValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioTrackerAssetDetailsCard_ToggleValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioTrackerAssetDetailsCard_ToggleValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioTrackerAssetDetailsCard_ToggleValueValidationError{}

// Validate checks the field values on NewsSection_NewsCarouselIndicatorConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *NewsSection_NewsCarouselIndicatorConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// NewsSection_NewsCarouselIndicatorConfig with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// NewsSection_NewsCarouselIndicatorConfigMultiError, or nil if none found.
func (m *NewsSection_NewsCarouselIndicatorConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *NewsSection_NewsCarouselIndicatorConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AutoScrollInterval

	if all {
		switch v := interface{}(m.GetUnselectedIndexIndicatorBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfigValidationError{
					field:  "UnselectedIndexIndicatorBgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfigValidationError{
					field:  "UnselectedIndexIndicatorBgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnselectedIndexIndicatorBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSection_NewsCarouselIndicatorConfigValidationError{
				field:  "UnselectedIndexIndicatorBgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSelectedIndicatorConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfigValidationError{
					field:  "SelectedIndicatorConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfigValidationError{
					field:  "SelectedIndicatorConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedIndicatorConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSection_NewsCarouselIndicatorConfigValidationError{
				field:  "SelectedIndicatorConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NewsSection_NewsCarouselIndicatorConfigMultiError(errors)
	}

	return nil
}

// NewsSection_NewsCarouselIndicatorConfigMultiError is an error wrapping
// multiple validation errors returned by
// NewsSection_NewsCarouselIndicatorConfig.ValidateAll() if the designated
// constraints aren't met.
type NewsSection_NewsCarouselIndicatorConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NewsSection_NewsCarouselIndicatorConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NewsSection_NewsCarouselIndicatorConfigMultiError) AllErrors() []error { return m }

// NewsSection_NewsCarouselIndicatorConfigValidationError is the validation
// error returned by NewsSection_NewsCarouselIndicatorConfig.Validate if the
// designated constraints aren't met.
type NewsSection_NewsCarouselIndicatorConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NewsSection_NewsCarouselIndicatorConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NewsSection_NewsCarouselIndicatorConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NewsSection_NewsCarouselIndicatorConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NewsSection_NewsCarouselIndicatorConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NewsSection_NewsCarouselIndicatorConfigValidationError) ErrorName() string {
	return "NewsSection_NewsCarouselIndicatorConfigValidationError"
}

// Error satisfies the builtin error interface
func (e NewsSection_NewsCarouselIndicatorConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNewsSection_NewsCarouselIndicatorConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NewsSection_NewsCarouselIndicatorConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NewsSection_NewsCarouselIndicatorConfigValidationError{}

// Validate checks the field values on
// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigMultiError,
// or nil if none found.
func (m *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSelectedIndexText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
					field:  "SelectedIndexText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
					field:  "SelectedIndexText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedIndexText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
				field:  "SelectedIndexText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalCountText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
					field:  "TotalCountText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
					field:  "TotalCountText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalCountText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{
				field:  "TotalCountText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if len(errors) > 0 {
		return NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigMultiError(errors)
	}

	return nil
}

// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigMultiError is
// an error wrapping multiple validation errors returned by
// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig.ValidateAll()
// if the designated constraints aren't met.
type NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigMultiError) AllErrors() []error {
	return m
}

// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError
// is the validation error returned by
// NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig.Validate if
// the designated constraints aren't met.
type NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError) ErrorName() string {
	return "NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError"
}

// Error satisfies the builtin error interface
func (e NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NewsSection_NewsCarouselIndicatorConfig_SelectedIndicatorConfigValidationError{}

// Validate checks the field values on ComparisonChart_ReferenceLineConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ComparisonChart_ReferenceLineConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComparisonChart_ReferenceLineConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ComparisonChart_ReferenceLineConfigMultiError, or nil if none found.
func (m *ComparisonChart_ReferenceLineConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ComparisonChart_ReferenceLineConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeadingText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComparisonChart_ReferenceLineConfigValidationError{
					field:  "LeadingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComparisonChart_ReferenceLineConfigValidationError{
					field:  "LeadingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeadingText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComparisonChart_ReferenceLineConfigValidationError{
				field:  "LeadingText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LineHeight

	if all {
		switch v := interface{}(m.GetLineColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComparisonChart_ReferenceLineConfigValidationError{
					field:  "LineColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComparisonChart_ReferenceLineConfigValidationError{
					field:  "LineColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLineColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComparisonChart_ReferenceLineConfigValidationError{
				field:  "LineColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ComparisonChart_ReferenceLineConfigMultiError(errors)
	}

	return nil
}

// ComparisonChart_ReferenceLineConfigMultiError is an error wrapping multiple
// validation errors returned by
// ComparisonChart_ReferenceLineConfig.ValidateAll() if the designated
// constraints aren't met.
type ComparisonChart_ReferenceLineConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComparisonChart_ReferenceLineConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComparisonChart_ReferenceLineConfigMultiError) AllErrors() []error { return m }

// ComparisonChart_ReferenceLineConfigValidationError is the validation error
// returned by ComparisonChart_ReferenceLineConfig.Validate if the designated
// constraints aren't met.
type ComparisonChart_ReferenceLineConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComparisonChart_ReferenceLineConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComparisonChart_ReferenceLineConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComparisonChart_ReferenceLineConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComparisonChart_ReferenceLineConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComparisonChart_ReferenceLineConfigValidationError) ErrorName() string {
	return "ComparisonChart_ReferenceLineConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ComparisonChart_ReferenceLineConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComparisonChart_ReferenceLineConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComparisonChart_ReferenceLineConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComparisonChart_ReferenceLineConfigValidationError{}
