// protolint:disable MAX_LINE_LENGTH

// Frontend RPC for operations involving payment transactions

syntax = "proto3";

package frontend.pay.transaction;

import "api/accounts/account_type.proto";
import "api/frontend/account/account.proto";
import "api/frontend/account/upi/cred_block.proto";
import "api/frontend/chat_head.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/deeplink/timeline/enter_amount_screen_options.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/metadata.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/pay/add_funds_v2/add_funds_v2.proto";
import "api/frontend/pay/add_funds_v2/onboarding/bottom_sheet.proto";
import "api/frontend/pay/add_funds_v2/onboarding_add_funds_v2.proto";
import "api/frontend/pay/beneficiary_management.proto";
import "api/frontend/pay/order_event.proto";
import "api/frontend/pay/pay.proto";
import "api/frontend/pay/payment_protocol.proto";
import "api/frontend/pay/transaction/enums.proto";
import "api/frontend/pay/transaction/payload/payment_details.proto";
import "api/frontend/pay/transaction/payment_options.proto";
import "api/frontend/pay/txn_category.proto";
import "api/frontend/timeline/timeline_action.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/inapphelp_media_uicontext.proto";
import "api/typesv2/money.proto";
import "api/typesv2/pay/user_identifier/user_identifier.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/frontend/pay/transaction";
option java_package = "com.github.epifi.gamma.api.frontend.pay.transaction";

// Frontend RPC service to enable payment services for Epifi users
//
// The Payment Service enables money transfer services from a bank account, that belongs to
// the user, to any other bank account that exists in India.
//
// It facilitates transfer process using below medium a.k.a payment instruments in epiFi terminology:
//   * Account Transfer
//   * UPI
//   * Mobile number
//
// Payment flow is typically a two pronged approach:
// 1. Create order with the user entered parameters
// 2. Initialise the payment once user authorizes payment
// This helps in avoiding scenarios where client & server are not in sync about a transaction
// E.g., timeout scenario where client doesn't know the transaction status and the transaction is processed on server
//
// Note:
// `Transaction` is an agreement between a buyer and a seller to exchange goods, services or financial instruments.
// `Payment` is the transfer of one form of goods, services, or financial assets in exchange for another form of goods,
// services, or financial assets in acceptable proportions that have been previously agreed upon by all parties involved.
service Transaction {

  // RPC method to create an order with fund transfer workflow.
  //
  // An Order is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage transaction requests and their workflow.
  //
  // It returns decision engine's preferred payment protocol, payment instrument and other transaction
  // related info back to the Client.
  // This information will be used by client-
  // 1. To invoke `GetCredentials` in common library.
  // 2. To invoke `InitiatePayment` with the cred block which executes the transaction
  rpc CreateFundTransferOrder (CreateFundTransferOrderRequest) returns (CreateFundTransferOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = false;
  };

  // RPC method to create an order for P2P Collect workflow.
  //
  // It returns order id which client can use as an identifier of the request.
  // This information will be used by client to invoke `InitiateCollect` which registers collect request.
  // The RPC only accepts collect request between two individuals.
  // Hence, Any Collect request to merchant VPA will be rejected
  rpc CreateP2PCollectOrder (CreateP2PCollectOrderRequest) returns (CreateP2PCollectOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };


  // RPC method to create transaction and initiate the payment.
  //
  // This is an asynchronous API such that, payment may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement message immediately.
  //
  // Any updates on the payment has to be polled with `GetOrderStatus` RPC method
  rpc InitiatePayment (InitiatePaymentRequest) returns (InitiatePaymentResponse) {
    option (rpc.auth_required) = true;
    // All high value transactions are expected to pass device integrity checks
    // Low value transactions, based on risk assessment, may require device integrity checks
    option (rpc.device_integrity_check) = CONDITIONAL;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = false;
  };

  // RPC facilitates enquiry of order status - identified by a unique `order_id`
  // This can also give the status of a recurring payment registration.
  rpc GetOrderStatus (GetOrderStatusRequest) returns (GetOrderStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC method to return necessary transaction related information for a given collect order.
  // UPI CL requires transaction parameters to capture the credentials
  // that are required to process the transaction
  //
  // This method is to be used by client to fetch transaction parameters
  // when user wishes to make a payment against a collect request.
  //
  // RPC returns a list of eligible accounts through which user can initiate the payment
  // It also returns parameters such as payment instrument, txn-id, merchant_ref_id
  // that are required to complete the transaction
  //
  // Client can use this information for the following:
  // 1. To invoke `GetCredentials` in common library.
  // 2. To invoke `InitiatePayment` with the cred block which executes the transaction
  rpc GetCollectOrderTransactionDetails (GetCollectOrderTransactionDetailsRequest) returns (GetCollectOrderTransactionDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch the accounts eligible for a user to send and receive a payment
  // contains information related to eligible accounts for making a payment. Client can surface this information to the user while he/she is trying to make a payment.
  // Since this information will be static mostly client can choose to cache this information
  // This RPC has been deprecated and is merged with frontend.user.GetUserSessionDetails.
  rpc GetEligibleAccountsForPayment (GetEligibleAccountsForPaymentRequest) returns (GetEligibleAccountsForPaymentResponse) {
    option (rpc.auth_required) = true;
    option deprecated = true;
  };

  // RPC to initiate a collect request with the system.
  //
  // This is an asynchronous API. So, collect registration may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement message immediately.
  //
  // Any updates on the collect status has to be polled with `GetOrderStatus` RPC method
  rpc InitiateCollect (InitiateCollectRequest) returns (InitiateCollectResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to dismiss a collect request
  // A client can use this when a user wants to dismiss a collect request. A user can dismiss a collect request in the following scenarios-
  //  1) On receiving a collect request from another actor be it internal or external.
  //  2) When a user sends a collect request to an internal epiFi user.
  //  NOTE: a collect order whose payment is in progress can't be dismissed
  rpc DismissCollect (DismissCollectRequest) returns (DismissCollectResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC method to create an order for outgoing intents/ dynamic QR
  // eg. Adding funds to epifi account using 3rd party apps
  //
  // An Order is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage transaction requests and their workflow.
  //
  // returns the orderId and URN to the client
  // Client can use it to create dynamic QR or Intent to the 3rd party app based on user's preference,
  // Urn will contain parameters such as payeeVPA, amount, txnId, refId etc and will be signed by the PSP's key.
  // 3rd party app is expected to use txnId and refId from URN to initiate the payment with NPCI
  rpc CreateURNOrder (CreateURNOrderRequest) returns (CreateURNOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC method to fetch an order receipt
  // Client is expected to use this method in order to fetch granular details
  // regarding the state of a payment and display it to the user.
  //
  // It returns necessary information such as transaction id, RRN/UTR, etc. A
  // A user can refer to the information for various purposes, but not limited to:
  // 1. User can share this information as a proof of payment with the other actor.
  // 2. User can raise dispute against an suspicious transaction and claim refunds.
  rpc GetOrderReceipt (GetOrderReceiptRequest) returns (GetOrderReceiptResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC method to raise dispute for a transaction
  // A client can use this to raise dispute for any transaction. Dispute can be raised for a transaction only after the
  // cooldown period for the transaction which is 24 hours for UPI transaction and 3 hours for NEFT/IMPS/IntraBank/RTGS
  // transaction. Dispute can be raised only if the transaction state is not expired or cancelled. Once dispute is raised
  // for a transaction client cannot re raise a dispute for that transaction
  rpc RaiseDispute (RaiseDisputeRequest) returns (RaiseDisputeResponse) {
    option (rpc.auth_required) = true;
    option deprecated = true;
  }

  // RPC method to return parameters required for the add funds screen
  rpc GetAddFundParams (GetAddFundParamsRequest) returns (GetAddFundParamsResponse) {
    option (rpc.auth_required) = true;
  }

  // AuthoriseFundTransfer will initiate fund transfer payment with auth. It will make required cred block(encrypted pin/secure pin)
  // of user, validate the authorise request and payment details. After that it will initiate payment with vendor.
  //
  // Order should be created for such payment.
  rpc AuthoriseFundTransfer (AuthoriseFundTransferRequest) returns (AuthoriseFundTransferResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC method to check whether we want to allow the user to add funds
  // we show them a warning if they are nearing the min kyc usage limit and also do not allow them to add funds if not applicable
  rpc CheckIfAddFundsAllowed (CheckIfAddFundsAllowedRequest) returns (CheckIfAddFundsAllowedResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetEligibleAccountsForPaymentV1 calls the backend rpc to fetch all active accounts using actorId.
  // It fetches the active tpap accounts and also checks if it includes internal account
  // If internal account is not present in the list of active tpaps accounts, it adds the internal account in the given list.
  rpc GetEligibleAccountsForPaymentV1 (GetEligibleAccountsForPaymentV1Request) returns (GetEligibleAccountsForPaymentV1Response) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetChatHeadsForPaymentViaNumber returns a list of chat heads for the given phone number
  // rpc checks internally if number belongs to an existing user
  // if yes then also returns that with other chat heads for vpas linked with npci
  rpc GetChatHeadsForPaymentViaNumber (GetChatHeadsForPaymentViaNumberRequest) returns (GetChatHeadsForPaymentViaNumberResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetAddFundsScreenDetailsV2 returns add funds screen details based on different entry points
  // Takes ui entry point in request
  // Returns a one of in response which contains parameter details specific to the entry point
  rpc GetAddFundsScreenDetailsV2 (GetAddFundsScreenDetailsV2Request) returns (GetAddFundsScreenDetailsV2Response) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ManualBalanceRefreshForOnbAddFundsV2 to be used via user-action for manual balance update during onboarding add-funds
  // It returns success if the updated balance passes the minimum required amount for onb-add-funds.
  // Non-success response if the updated balance is less than the minimum required amount for onb-add-funds
  rpc ManualBalanceRefreshForOnbAddFundsV2 (ManualBalanceRefreshForOnbAddFundsV2Request) returns (ManualBalanceRefreshForOnbAddFundsV2Response) {
    option (rpc.auth_required) = true;
  }

  // GetPaymentOptions provides a mechanism to perform a payment by providing various methods/options for the payment.
  // It looks into the availability and applicability/possibility for using a payment option and generates the response accordingly.
  // todo: add more details regarding the sections, ordering etc
  rpc GetPaymentOptions (GetPaymentOptionsRequest) returns (GetPaymentOptionsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // CreateFundTransferOrderV1 RPC method is responsible to initiate order creation for the transactions
  //
  // Order is an important step of the payment life cycle and required to be created before a transaction can be processed.
  // An Order is a concept between client and Epifi and is typically abstracted from the partner banks or payment gateways. It helps to manage transaction requests and their workflow transitions from payment initiation to settlement.
  //
  // Payment details such as preferred payment protocol, payment instrument and other transaction
  // related info are typically determined at this step and passed back to the client.
  //
  // Additionally, RPC abstracts the order creation step at vendor's side, if required, such as when using payment gateways like Razorpay.
  // After execution, it provides the vendor_order_id in the response to satisfy the client's requirements for further actions.
  //
  // This information is to be used by client to
  // 1. To invoke `GetCredentials` in common library and `InitiatePayment` with the cred block which executes the transaction
  // 2. Invoke SDK or corresponding flow of payment gateway to execute a transaction
  rpc CreateFundTransferOrderV1 (CreateFundTransferOrderV1Request) returns (CreateFundTransferOrderV1Response) {
    option (rpc.auth_required) = true;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // MarkPostPayment RPC can be used by client to inform/update backend regarding the payment done by the user via
  // app SDKs. These payments can be the ones done via external vendor integration such as RazorPay where the payment
  // creation doesn't go via Fi backend servers.
  // The logic in here can include steps to be performed depending on the type of payment + vendor. For e.g.
  // 1. Signalling the backend workflows upon authorisation, i.e. payment for mandate setup.
  // 2. todo: add more if extend support tomorrow.
  //
  // Note: relying only client invocation to update backend servers regarding action performed by user is subject to
  // failure. Please make sure to have fallback logics which are backend driven to maintain consistency.
  rpc MarkPostPayment (MarkPostPaymentRequest) returns (MarkPostPaymentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
}

message GetPaymentOptionsRequest {
  frontend.header.RequestHeader req = 1;

  // amount in consideration for the payment, for e.g. add-funds amount
  api.typesv2.Money amount = 2;
  // entry point for the payment-options.
  // basis this, the payment-options page will be built (by applying the rules)
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 3;

  // Metadata is a serialised construct of fields that can evolve over a period of time
  // and is expected to help with any orchestration requirements
  // Field will get from Domain service (eg:CC),
  // this also helps to decide payment options available for users.
  // It also aids in identifying the vendor (e.g., payment gateway like CC) responsible for processing the payment.
  // we will fallback to transaction_ui_entry_point point to identifying the
  // vendor(e.g., payment gateway like CC) responsible for processing the payment
  // if value of this filed is not populated
  bytes orchestration_metadata = 4 [(validate.rules).bytes.max_len = 1000];

  // Refers to the list of payment options in the order in which the response should be displayed
  // If empty, the result will be sorted in a default order set at backend.
  repeated PaymentOptionType ordered_payment_option_types = 5;
}

message GetPaymentOptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // title of the page/screen, for e.g. "Select a payment method"
  api.typesv2.common.Text title = 2;
  // list of payment options available
  // Note: ordering matters, i.e. client should render it in the order the options are received
  repeated PaymentOption payment_options = 3;
  // footer for the page/screen, for eg: Secure banking with federal bank footer
  api.typesv2.ui.IconTextComponent footer = 4;
  // background color for the payment-options screen
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 5;

  // Metadata comprises serialized fields that evolve over time to support orchestration needs.
  // Retrieved from the domain service (e.g., CC), it guides the determination of available payment options for users.
  // Additionally, it assists in identifying the responsible vendor (e.g., payment gateway like CC) for processing payments.
  // Ensure this field is included in the request for the `CreateFundTransferOrderV1` RPC.
  bytes orchestration_metadata = 6 [(validate.rules).bytes.max_len = 1000];

  // Optional: Denotes the time at which the payment options screen will expire. The client needs to display a timer on the UI
  // based on the expiry time if the time is not nil, and needs to call MarkPostPayment with PAYMENT_OPTIONS_EVENT_TYPE_PAYMENT_EXPIRED
  // if the timer expires while the user is unable to complete the PaymentFlow. This is needed in cases where backend flows
  // expect the user to complete the payment within a certain timeout.
  // Timer design https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?t=bhAFnyRpOwZoddG4-0
  google.protobuf.Timestamp payment_options_screen_expiry_time = 7;

  // example of top and bottom sections:
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/%F0%9F%9B%A0%EF%B8%8F-Central-Growth---Workfile?node-id=18057-58991&t=zEWDdhzJgdtWp7yM-4
  // displayed above payment options
  api.typesv2.ui.sdui.sections.Section top_section = 8;
  // displayed below payment options
  api.typesv2.ui.sdui.sections.Section bottom_section = 9;

  // screen or bottom sheet to be shown when user drops off from the Payment options screen without completing the payment
  // sample: https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-72545&t=mXS9zsieZAJj8r2h-4
  deeplink.Deeplink drop_off_screen = 10;

  // optional: additional text below CTA
  // eg: https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/%F0%9F%9B%A0%EF%B8%8F-Central-Growth---Workfile?node-id=18129-50266&t=zYvJQ0FtpJrJrfOP-4
  // client will display additional_text_below_cta above footer component
  api.typesv2.ui.IconTextComponent additional_text_below_cta = 11;

  message PaymentOption {
    // title of the payment option, for e.g. "Pay with UPI ID"
    api.typesv2.common.Text option_title = 1;
    // subtitle to be shown below the title even in case of collapsed state.
    // for e.g. "Initiate a transfer from any of your existing bank accounts..."
    // Note: can be nil
    api.typesv2.common.Text option_subtitle = 2;
    // tag to be shown beside the title, for e.g. "UNAVAILABLE"
    api.typesv2.ui.IconTextComponent tag = 3;
    // default expand-collapse state of the option
    // note: should never be UNSPECIFIED.
    ExpandCollapseState expand_collapse_state = 4;
    // tells whether the option is collapsible or not.
    // Note: If it's not collapsible, then we shouldn't show the button to collapse.
    bool is_collapsible = 5;
    // down arrow image to be used in case the option is collapsible.
    // Note: client will rotate the image in case of showing the up arrow.
    // Note: can be nil in case the option is not collapsible
    api.typesv2.common.VisualElement down_arrow = 6;
    // tells about the availability of the whole option
    // todo: define the use-case
    AvailabilityState payment_option_state = 7;
    // type of payment option so that client can decide the set of APIs/flow to go ahead with
    PaymentOptionType payment_option_type = 8;
    // background color for the payment option
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 9;
    // cta to be used at the bottom if user selects this particular payment option.
    // Note: client to handle the disabled state of the CTA.
    // Note: if $AMOUNT$ is present, client needs to replace $AMOUNT$ with user entered value, otherwise as it is.
    deeplink.Cta payment_option_cta = 10;
    // Checkbox widget item for each payment option to show with their corresponding cta
    // eg for TPAP payment option: By proceeding I consent to Epifi Tech reactivating the displayed accounts with my UPI ID
    // eg figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=22815-146614&mode=design&t=AfJJX2lUp2Q0MVPT-0
    api.typesv2.common.ui.widget.CheckboxItem payment_option_checkbox = 11;

    oneof option {
      // UPI accounts / TPAP option which shows the list of accounts for the payment
      TpapPaymentOption upi_accounts_option = 15;
      // intent flow, i.e. Recommended Apps section to initiate the intent flow
      IntentPaymentOption intent_option = 16;
      // manual UPI ID input flow, i.e. collect flow
      CollectPaymentOption upi_id_input_option = 17;
      // NEFT/IMPS option which shows how a user can transfer money via other means
      NeftImpsPaymentOption neft_imps_option = 18;
      // Debit/Credit card option, shows input fields to enter card details
      CardPaymentOption card_payment_option = 19;
      // Shows list of banks from where user can select a bank and complete payment via Netbanking
      NetBankingPaymentOption netbanking_option = 20;
    }
  }
}

message GetAddFundsScreenDetailsV2Request {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;
  // ui entry point for which params are to be fetched
  frontend.pay.transaction.UIEntryPoint ui_entry_point = 2;
  // minor version for the page for which params are to be fetched - "ZERO", "ONE", "TWO", ....
  // on the basis of this client can render specific versions the page - v2.0, v2.1, v2.2
  // this to allow for further classifications on the basis of entry point and should not be used in a standalone manner for new logic chains
  // Note - if this is not passed by client or empty, minor version is considered to be zero
  string minor_version = 3;
}

message GetAddFundsScreenDetailsV2Response {
  enum Status {
    OK = 0;

    INTERNAL = 12;

    UNIMPLEMENTED = 13;
  }
  frontend.header.ResponseHeader resp_header = 15;
  // min amount to be added
  api.typesv2.Money min_amount = 1;
  // max amount to be added
  api.typesv2.Money max_amount = 2;
  // default amount to be added
  api.typesv2.Money default_amount = 3;
  oneof details {
    // add funds params details for ui entry point ONBOARD_ADD_FUNDS
    frontend.pay.add_funds_v2.OnboardingAddFundsDetails onboarding_add_funds_details = 4;
  }

  // Flag used in android, instead of navigating to add money status for polling navigate to pay status.
  bool intent_navigate_to_pay_status = 5;
  bool collect_navigate_to_pay_status = 6;
}

message ManualBalanceRefreshForOnbAddFundsV2Request {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;
}

message ManualBalanceRefreshForOnbAddFundsV2Response {
  enum Status {
    OK = 0;

    INTERNAL = 13;

    // status to denote that the balance is below minimum required for onboarding add-funds
    BALANCE_BELOW_MINIMUM_REQUIRED = 101;
  }

  // bottom-sheet to be shown if the response code is 13 / 101
  frontend.pay.add_funds_v2.onboarding.AcknowledgementBottomSheet balance_below_min_required_bottom_sheet = 1;

  frontend.header.ResponseHeader resp_header = 15;

}

// Transactions of high risk e.g., high valued transaction or new install
// may mandate additional level of authentication for security reasons.
//
// `pin_required` denotes if additional authentication is required from the user
//
// TODO(pruthvi): Do we need to generalise this to include PIN/OTP etc..
enum PinRequiredType {
  PIN_REQUIRED_TYPE_UNSPECIFIED = 0;
  // For UPI payments client needs to generate cred block via NPCI CL.
  NPCI = 1;
  // For payments those are carried through NEFT, IMPS, etc.
  // client need to generate cred block via partner bank's CL.
  SECURE_PIN = 2;
  // No pin required to execute the transaction. The client can hit initiate transaction
  // without any cred block.
  NONE = 3;
}

// TransactionAttribute contains a set of fields that are necessary to initiate a transaction
// also, contains information to call CL's `GetCredential`
message TransactionAttribute {
  // A unique identifier belonging to the payer account
  string payer_account_id = 1;

  // A unique identifier to represent the P2P transaction itself
  // Transaction ID will be used in the request payloads with partner banks
  // 1. Be it as a unique transaction reference ID
  // 2. Part of secure PIN block

  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a transaction_id is already present in the QR or the intent. For those cases
  // the`transaction_id` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this txn-id to generate cred block for UPI
  // as well as for non-UPI payments.
  //
  // Deprecated: in favour of request_id as per convention we call it request id for vendor ref
  string transaction_id = 2;

  // A unique identifier referring to a order number, subscription number, Bill ID,
  // booking ID, insurance, renewal reference, etc. from the merchant's system.
  //
  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a merchant_ref_id is already present in the QR or the intent. For those cases
  // the`merchant_ref_id` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this merchant reference id as a part of `payInfo`
  // to generate cred block for UPI payments.
  // merchant-ref-id is referred to as `refId` in NPCI's CL.
  string merchant_ref_id = 3;

  // Payment protocol suggested by decision engine for the transaction.
  frontend.pay.PaymentProtocol payment_protocol = 4;

  // transaction reference URL is used by client to get credentials. This should be a URL when clicked provides customer with further transaction details
  // like complete bill details, bill copy, order copy, ticket details, etc.
  //
  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a reference url is already present in the QR or the intent. For those cases
  // the `reference_url` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this reference url as a part of `payInfo`
  // to generate cred block for UPI payments.
  // reference url is referred to as `refUrl` in NPCI's CL.
  string reference_url = 5;

  // payee actor name ot be used by client to get credentials.

  // The client is supposed to use this merchant-ref-id to generate cred block for UPI payments.
  // reference url is referred to as refUrl in NPCI's CL.
  //
  // The client is supposed to use this field as a part of `payInfo`
  // to generate cred block for UPI payments.
  // payee actor name is referred as `payeeName` in NPCI's CL.
  string payee_actor_name = 6;

  // masked account number to belonging to the account to which payer pi belongs to
  // It is meant to be used by client to call NPCI CL's `Get Credential` service
  //
  // The client is supposed to use this field as a part of `payInfo`
  // to generate cred block for UPI payments.
  // masked account number is referred as `account` in NPCI's CL.
  // TODO(nitesh): check for optimisation as client might already have this info
  string payer_masked_account_number = 7;

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 8 [(validate.rules).message.required = true];

  // A note on the transaction by the user
  // This can either be one of the system generated tags or a custom note entered by the user
  string remarks = 9;

  // payment instrument information via which money is being sent. The payment instrument
  // can vary depending on the payment protocol. e.g.
  // In case of bank transfer based transfer it will be the masked account number of payer
  // In case of UPI based transfer it will be the the VPA belonging to the payer
  //
  // The client should consume this information as `payerAddr` as a part of `payInfo`
  // while generating cred block for UPI transfers
  //
  // NOTE: using any other address apart from the one passed from server while generating
  // cred block will lead to failure in transaction
  string payer_payment_instrument = 10;

  // payment instrument information via which money is being received. The payment instrument
  // can vary depending on the payment protocol. e.g.
  // In case of bank transfer based transfer it will be the masked account number of payee
  // In case of UPI based transfer it will be the the VPA belonging to the payee
  //
  // The client should consume this information as `payeeAddr` as a part of `payInfo`
  // while generating cred block for UPI transfers
  //
  // NOTE: using any other address apart from the one passed from server while generating
  // cred block will lead to failure in transaction
  string payee_payment_instrument = 11;

  // payment instrument info to be displayed.The payment instrument
  // can vary depending on the payment protocol. e.g.
  // In case of bank transfer based transfer it will be the masked account number of payee
  // In case of UPI based transfer if account@ifsc based then masked account number else vpa
  string display_payee_payment_instrument = 12;

  // A unique identifier to represent the P2P transaction itself
  // RequestId will be used in the request payloads with partner banks
  // 1. It will be unique reference for a transaction.
  // 2. Part of secure PIN block

  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a transaction_id is already present in the QR or the intent. For those cases
  // the`request_id` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this request-id to generate cred block for UPI
  // as well as for non-UPI payments.
  string request_id = 13;

  // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
  // like saving_account_id, tpap_account_id, connected_account id etc.
  string derived_account_id = 14;

  // upi number of the payer if the transaction is done from a upi number
  string payer_upi_number = 15;

  // upi number of the payee if the transaction is done to a upi number
  string payee_upi_number = 16;
}

message CreateFundTransferOrderRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Identifier of the account form which monies are transferred
  // use payer_user_identifier
  string payer_account_id = 2 [deprecated = true];

  // Unique identifier of the payee actor.
  // use payer_user_identifier
  string payee_actor_id = 3 [deprecated = true];

  // Determines the time at which the transaction is to be executed
  // - Transactions with empty timestamp will be executed immediately
  // - Transactions with past timestamp are rejected
  // - Transactions with future timestamp will be scheduled at the denoted timestamp
  // TODO(pruthvi): Do we need to set a constraint on how far in the future a transaction can be scheduled?
  //  Mention list of protocols that support future execution
  google.protobuf.Timestamp execute_at = 4;

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 5 [(validate.rules).message.required = true];

  // A note on the transaction by the user
  // This can either be one of the system generated tags or a custom note entered by the user
  // remark should be alpha numeric
  string remarks = 6 [(validate.rules).string = {pattern: "^[a-zA-Z0-9 ]*$"}];

  // Optional: urn to be provided by the client for QR/intent based payments
  // For QR and intent based payments.. merchant system will send the urn to epifi app
  //
  // urn will be used for
  //  1) signature verification
  //  2) parsing to get the relevant fields like, transactionId, RefId, RefUrl etc.
  //
  // eg. urn- intentURL:upi://pay?pa=test@npci&pn=test%20rath&mc=9999&tid=cxnkjcnkj
  //          dfdvjndkjfvn&tr=4894398cndhcd23&tn=Pay%20to%20test%20store&am=10&
  //          mam=null&cu=INR&url=https://test.com&mode=05&&orgid=000000&mid=12
  //          34&msid=3432&mtid=1212
  string upi_urn = 7 [deprecated = true];

  // entry point on the client for order creation
  // UIEntryPoint is deprecated, please use frontend.deeplink.timeline.TransactionUIEntryPoint instead
  UIEntryPoint ui_entry_point = 8 [deprecated = true];

  // qr_data contain the upi urn or the bharat QR provided by the client for QR/intent based payments
  // For QR and intent based payments, merchant system will send the urn to epifi app
  //
  //  bharat qr or the upi urn will be used for
  //  1) signature verification
  //  2) parsing to get the relevant fields like, transactionId, RefId, RefUrl etc.
  //
  // eg. urn- intentURL:upi://pay?pa=test@npci&pn=test%20rath&mc=9999&tid=cxnkjcnkj
  //          dfdvjndkjfvn&tr=4894398cndhcd23&tn=Pay%20to%20test%20store&am=10&
  //          mam=null&cu=INR&url=https://test.com&mode=05&&orgid=000000&mid=12
  //          34&msid=3432&mtid=1212
  // bharatQr - "00020101021202164701000000000087041651334400000000800616610059000000008926350010A0000005240117
  // razorpaybqr@icici27350010A0000005240117RZP9sqTqIrlvyG7Pr52045399530335654042.005802IN5908RAZORPAY6009BANGALORE
  // 6106560030622805149sqTqIrlvyG7Pr07061234566304DBAC"
  string qr_data = 9;

  // entry point on the client for order creation
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 10;

  // Type of the account form which monies are transferred
  // use payer_user_identifier
  accounts.Type payer_account_type = 11 [deprecated = true];

  // User identifier for the payer
  UserIdentifier payer_user_identifier = 12 [deprecated = true];

  // User identifier for the payee
  UserIdentifier payee_user_identifier = 13 [deprecated = true];

  // Base amount in quote currency involved in payment transaction in case of international payment
  api.typesv2.Money base_amount_quote_currency = 14;

  // Optional: This deeplink is where user can be redirected to post payment.
  // Redirection depends upon client consuming this deeplink from the Order.
  // In Pay flows, this happens via `GetOrderStatus` RPC.
  frontend.deeplink.Deeplink post_payment_deeplink = 16;

  // Metadata is a serialised construct of fields that can evolve over a period of time
  // and is expected to help with any orchestration requirements.
  // Field is to be passed as is from the response of `GetPaymentOptions` RPC response.
  // Refer pay.ClientIdentificationTxnMetaData proto for internals.
  //
  // Note:
  // 1. This was intended for the new flow, i.e. CreateFundTransferOrderV1. Though, some payment flows
  // which need not depend upon external vendors (i.e. PG) can still go via this RPC. Thus, to support to some
  // extent, this field can help in propagating the required fields via BUs.
  bytes orchestration_metadata = 17 [(validate.rules).bytes.max_len = 1000];

  // User identifier for the payer
  api.typesv2.pay.user_identifier.UserIdentifier payer_user_identifier_v2 = 18;

  // User identifier for the payee
  api.typesv2.pay.user_identifier.UserIdentifier payee_user_identifier_v2 = 19;
}
message CreateFundTransferOrderResponse {
  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  frontend.header.Metadata metadata = 5;

  enum Status {
    OK = 0;
    // The actor does not have permission to execute the specified operation.
    // the reason can be many but not limited to-
    // 1) account id passed in the request doesnt belong to the logged in actor
    // 2) logged in actor has blocked/reported payee actor
    // 3) logged in actor is blocked/reported by payee actor
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // The service is currently unavailable.  This is most likely a
    // transient condition, which can be corrected by retrying after some interval.
    // e.g., Downtime at partner banks, health checks failing for a given payment protocol
    UNAVAILABLE = 14;
    // for QR/intent based payments client needs to pass the urn in CreateFundTransferOrderRequest
    // server needs to verify if the signature present in urn is valid or not.
    // in case of invalid signature this status will be returned
    INVALID_SIGNATURE = 100;
    // user VPA is disabled and can not create the order
    // in cases wherr only pi transaction is possible and the current actor VPA is disabled,
    // this error code will be sen to the client
    VPA_DISABLED = 101;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 102;
    // amount limit for the urn payment exceeded
    // for QR/intent based payments there is a limit on max value per txn
    // based on if the qr/intent is signed or not
    URN_AMOUNT_LIMIT_EXCEEDED = 103;
    // returned when user exhausts daily txn amount limit for upi
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED = 104;
    // returned when user exhausts daily txn count limit for upi
    UPI_TOTAL_TXN_COUNT_EXCEEDED = 105;
    // after a device is registered, user account enters a cooldown phase
    // during this period, user has a total txn amount restriction for upi transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    // Deprecated, use UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE instead
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 106;
    // txn amount not in range permitted for upi protocol
    UPI_TXN_AMOUNT_EXCEEDED = 107;
    // after the user resets their upi pin for a period of 12 hrs
    // during this period, user has a total txn amount restriction for upi transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_AFTER_PIN_RESET = 108;
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 109;
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 110;
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 111;
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 112;
    // during Cooldown period, user has a total txn amount restriction for UPI transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 113;
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 114;
    // user has limit on the total transaction amount in the given time duration for all transactions like rtgs, upi, neft , imps , intra
    // returned when user has breached the total transaction amount in the given time duration
    TOTAL_TXN_AMOUNT_EXCEEDED_FOR_USER_IN_CONFIGURED_TIME = 115;
    // for certain mccs only intent is allowed as entry point
    // this status will be returned if the payment is initiated using an invalid entry point
    INVALID_ENTRY_POINT = 116;
    // upi payments for remitter account is unhealthy
    UPI_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 117;
    // IMPS payments for remitter account is unhealthy
    IMPS_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 118;
    // RTGS payments for remitter account is unhealthy
    RTGS_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 119;
    // NEFT payments for remitter account is unhealthy
    NEFT_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 120;
    // INTRA payments for remitter account is unhealthy
    INTRA_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 121;
    // Beneficiary is in cooldown phase
    BENEFICIARY_IS_IN_COOLDOWN_PHASE = 122;
    // - Indicates accounts that cannot perform transactions due to their status (Closed, Dormant, Inactive).
    // - Not Applies on Debit Freeze & Total Freeze, since we will be covering those in ACCOUNT_FROZEN
    // For Example these cases:
    // Account Closed (Freeze Status is either Credit Freeze or No Freeze)
    // Account Dormant (Freeze Status is either Credit Freeze or No Freeze)
    // Account Inactive (Freeze Status is either Credit Freeze or No Freeze)
    ACCOUNT_NOT_ACTIVE = 123;
    // - Specifically targets the freeze status of an account (Total Freeze, Debit Freeze, Credit Freeze) which restricts to perform transactions.
    // - Credit Freeze is considered only if the account is active, as it restricts incoming transactions but allows outgoing ones.
    // For Example these cases:
    // Total Freeze
    // Debit Freeze
    // Credit Freeze (only if Account is Active)
    ACCOUNT_FROZEN = 124;
    // payments are not supported for the given IFSC
    IFSC_NOT_SUPPORTED = 125;
    // RuPay credit card is not accepted by the merchant for the transaction
    RUPAY_CREDIT_CARD_NOT_ACCEPTED = 126;
  }

  // Denotes the status of the request
  rpc.Status status = 1;

  // If pin based authorization is required to execute payment for a given order.
  PinRequiredType pin_required = 2;

  // A unique identifier to represent the P2P Transaction request
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage the request
  // 1. initiate the transaction for the request
  // 2. Get status of the request
  // 3. Client will also use this order id to dismiss any invalid notifications. Dismissible notifs will have ref id - {order_id}_DISMISSIBLE)
  string order_id = 3;

  // Transaction attributes are required for the client to:
  //  i. Generate the credentials using a common library i.e., NPCI CL or Partner bank's CL
  //  ii. Present the transaction information e.g., payment protocol to the user
  //
  // TODO(nitesh): need to revisit and pass list when we want to allow users to overwrite decision engine's suggestion
  TransactionAttribute transaction_attribute = 4;
  // contains the different messages to be shown to the user for failed transactions
  // will be returned nil in case of non failed transactions
  ErrorView error_view = 6;

  // bottom sheet to show when transaction is not initiated with the vendor becuase beneficiary is in the cooldown phase=
  // ctas in the sheet are useful to initiate the auth flow if the beneficiary added is in cooldown phase
  BeneficiaryCoolDownBottomSheet cooldown_bottom_sheet = 7;

  frontend.header.ResponseHeader resp_header = 15;

  // message to show on order creation screen
  string transaction_info = 22;

}


message CreateP2PCollectOrderRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Identifier of the account to which monies are transferred
  // use payee_user_identifier
  string payee_account_id = 2 [deprecated = true];

  // Unique identifier of the payer actor.
  // use payer_user_identifier
  string payer_actor_id = 3 [deprecated = true];

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 4 [(validate.rules).message.required = true];

  // A note on the transaction by the user
  // This can either be one of the system generated tags or a custom note entered by the user
  // remark should be alpha numeric
  string remarks = 5 [(validate.rules).string = {pattern: "^[a-zA-Z0-9 ]*$"}];

  // UI entry point for the order creation
  // Based on the screen like TRANSFER_IN, TIMELINE ui entry
  // point will vary accordingly
  // UIEntryPoint is deprecated, please use frontend.deeplink.timeline.TransactionUIEntryPoint instead
  UIEntryPoint ui_entry_point = 6 [deprecated = true];

  // boolean flag to denote if the collect request is for add funds
  bool is_add_funds = 7;

  // entry point on the client for order creation
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 8;

  // Type of the account to which monies are transferred
  // use payee_user_identifier
  accounts.Type payee_account_type = 9 [deprecated = true];

  // user identifier for the payer
  UserIdentifier payer_user_identifier = 10 [deprecated = true];

  // user identifier for the payee
  UserIdentifier payee_user_identifier = 11 [deprecated = true];

  // User identifier for the payer
  api.typesv2.pay.user_identifier.UserIdentifier payer_user_identifier_v2 = 12;

  // User identifier for the payee
  api.typesv2.pay.user_identifier.UserIdentifier payee_user_identifier_v2 = 13;
}

message CreateP2PCollectOrderResponse {
  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  frontend.header.Metadata metadata = 4;

  enum Status {
    // collect order request successfully registered
    OK = 0;
    // The actor does not have permission to execute the specified operation.
    // the reason can be many but not limited to-
    // 1) account id passed in the request doesnt belong to the logged in actor
    // 2) logged in actor has blocked/reported payee actor
    // 3) logged in actor is blocked/reported by payee actor
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // The service is currently unavailable.  This is most likely a
    // transient condition, which can be corrected by retrying after some interval.
    UNAVAILABLE = 14;
    // for a collect request(non short circuit) there is a max limit of 5,0000 per transaction
    AMOUNT_LIMIT_EXCEEDED = 100;
    // user VPA is disabled and can not create the order
    // in cases wherr only pi transaction is possible and the current actor VPA is disabled,
    // this error code will be sen to the client
    VPA_DISABLED = 101;
    // a user has raise more collect request than provided quota
    // for protection against fraud cases NPCI has enforced a limit
    // of 5 collect request in 24hrs.
    COLLECT_VELOCITY_LIMIT_EXCEEDED = 102;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 103;
    // amount limit for the urn payment exceeded
    // for QR/intent based payments there is a limit on max value per txn
    // based on if the qr/intent is signed or not
    URN_AMOUNT_LIMIT_EXCEEDED = 104;
    // returned when user exhausts daily txn amount limit for upi
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED = 105;
    // returned when user exhausts daily txn count limit for upi
    UPI_TOTAL_TXN_COUNT_EXCEEDED = 106;
    // after a device is registered, user account enters a cooldown phase
    // during this period, user has a total txn amount restriction for upi transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    // Deprecated, use UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE instead
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 107;
    // txn amount not in range permitted for upi protocol
    UPI_TXN_AMOUNT_EXCEEDED = 108;
    // after the user resets their upi pin for a period of 12 hrs
    // during this period, user has a total txn amount restriction for upi transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_AFTER_PIN_RESET = 109;
    // amount addition not allowed for the user, since name match check has failed
    ADD_FUNDS_NAME_MATCH_FAILED = 110;
    // add funds failure due to account duration check failed for a min kyc kyc user
    ADD_FUNDS_MIN_KYC_ACCOUNT_DURATION_CHECK_FAILED = 111;
    // add funds failure due to maximum credit limit check failed for a min kyc kyc user
    ADD_FUNDS_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILED = 112;
    // add funds failure due to maximum balance check failed for a min kyc kyc user
    ADD_FUNDS_MIN_KYC_MAX_BALANCE_CHECK_FAILED = 113;
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 114;
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 115;
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 116;
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 117;
    // during Cooldown period, user has a total txn amount restriction for UPI transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 118;
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 119;
  }

  // Denotes the status of the request
  rpc.Status status = 1;

  // A unique identifier to represent the P2P Transaction request
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage the request
  // 1. initiate the transaction for the request
  // 2. Get status of the request
  string order_id = 2;

  // Transaction attributes are required for the client to:
  //  i. Present the transaction information e.g., payment protocol to the user
  TransactionAttribute transaction_attribute = 3;
  // contains the different messages to be shown to the user for failed transactions
  // will be returned nil in case of non failed transactions
  ErrorView error_view = 5;
  frontend.header.ResponseHeader resp_header = 15;
}

message InitiatePaymentRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // A unique identifier to represent the P2P Transaction request
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage the request
  // 1. initiate the transaction for the request
  // 2. Get status of the request
  string order_id = 2 [(validate.rules).string = {min_len: 4, max_len: 35}];

  // A unique identifier to represent the P2P transaction of an order
  // An order can have multiple transactions and `transaction_id`
  // helps to uniquely identify a transaction within an order
  //
  // Transaction ID will be used in the request payloads with partner banks
  // 1. Be it as a unique transaction reference ID
  // 2. Part of cred block
  // For UPI payments it is referred as `txnId` while for non-UPI payments
  // it is referred as `txnReqId` across.
  //
  // Client usually get transaction id generated from server either as part of
  // `CreateFundTransferOrder` or `GetCollectOrderTransactionDetails`. Client has to
  // pass it back based on user's selection.
  // To begin with we wont be allowing user to select or over write decision engine result
  // hence client can directly pass the transaction id received in above mentioned calls.
  // Note: the transaction id passed must match the one used in cred block generation.
  string transaction_id = 3 [(validate.rules).string = {min_len: 1, max_len: 35}];

  // Credential contains a cred block that acts as second auth factor for the transactions
  // which require another factor of authentication
  //
  // Cred block can either be generated through NPCI CL or Partner Bank's SDK
  // They typically contain Salt parameters in addition to PIN such as, but not limited to:
  // 1. Transaction ID
  // 2. Amount
  // 3. Timestamp
  oneof credential {
    // Financial transactions such as Intra Bank, NEFT, IMPS RTGS require `Secure PIN`
    // as second factor authentication. Secure PIN is the same as UPI PIN
    //
    // However, the encrypted PIN is generated through Partner bank's SDK and is a
    // Base64 encoded JSON string that can be passed as it is
    string partner_sdk_cred_block = 9;
    // All the UPI transactions require Cred block generated through NPCI common library
    // Cred block is to be passed to NPCI in XML format
    // To prevent errors in data transformation and to keep business logic out of client,
    // frontend expects NPCI's cred block in a structured fashion
    // Deprecated in favour of NpciCredBlocks
    frontend.account.upi.CredBlock npci_cred_block = 10 [deprecated = true];

    // All the UPI transactions require Cred block generated through NPCI common library
    // Cred block is to be passed to NPCI in XML format
    // To prevent errors in data transformation and to keep business logic out of client,
    // frontend expects NPCI's cred block in a structured fashion
    // There can be cases where we might need multiple cred block to pass in request, for e.g.
    // in case of UPI Lite client passes the set of UPI PIN and ARQC
    NpciCredBlocks npci_cred_blocks = 14;
  }
  frontend.account.upi.CredBlock cred_block = 4 [deprecated = true];

  // A unique identifier referring to a order number, subscription number, Bill ID,
  // booking ID, insurance, renewal reference, etc. from the merchant's system.
  //
  //
  // Client usually get merchant ref id generated from server either as part of
  // `CreateFundTransferOrder` or `GetCollectOrderTransactionDetails`. Client has to
  // pass it back based on user's selection.
  // To begin with we wont be allowing user to select or over write decision engine result
  // hence client can directly pass the merchant ref id received in above mentioned calls.
  // Note: the merchant ref id passed must match the one used in cred block generation.
  // merchant ref id is only required for UPI based payments. For all other payments carried through NEFT, IMPS, etc.
  // it can be left empty.
  string merchant_ref_id = 5 [(validate.rules).string.max_len = 35];

  // preferred payment protocol by the user
  // The field allows user to over write the decision engine's decision on payment protocol for given payment request.
  //
  // Note: to begin with we don't give user option to overwrite payment protocol chose by decision engine.
  // The client should pass back payment protocol returned from `CreateFundTransferOrder` or `GetCollectOrderTransactionDetails`
  frontend.pay.PaymentProtocol preferred_payment_protocol = 6 [(validate.rules).enum = {not_in: [0]}];

  // transaction reference URL. This should be a URL when clicked provides customer with further transaction details
  // like complete bill details, bill copy, order copy, ticket details, etc.
  //
  // Client is expected to pass back that is returned in transaction attributes in
  // `CreateFundTransferOrder` or `GetCollectOrderTransactionDetails`
  // Note: reference url must match the one passed during cred block generation call to NPCI CL.
  // Only valid for UPI payments.
  string reference_url = 7 [(validate.rules).string.max_len = 35];

  // Optional: account id from which monies is to be debited in case of collect based payment.
  // The account id passed here will be used as for making the payment with the bank.
  // This field is meant only to be used in case of SHORT_CIRCUIT based collect where payer user has an option
  // to pay from any of his/her preferred account.
  // For all other cases it can be left empty.
  string payer_account_id = 8;

  //  Amount of monies involved in payment transaction (Mandatory in case of international payments)
  api.typesv2.Money amount = 11;

  // Base amount in quote currency involved in payment transaction in case of international payment
  api.typesv2.Money base_amount_quote_currency = 12;

  // urn[optional]
  //  - UPI urn passed during create order
  //  - required in some flows where qr payload needs to be passed to vendor
  //    in order to initiate payment
  //    E.g. upi international payments
  string urn = 13;

  // upi number of the payer if the transaction is done from a upi number
  string payer_upi_number = 16;

  // upi number of the payee if the transaction is done to a upi number
  string payee_upi_number = 17;

  // [OPTIONAL] This blob will contain domain specific data that we might need to
  // notify domain workflows after transaction initiation.
  // It includes workflow identification information needed for callbacks.
  bytes orchestration_metadata = 18;
}

message InitiatePaymentResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like order id invalid, order state invalid, etc.
    INVALID_ARGUMENT = 3;
    // order has expired as payment shouldn'be initiated against this order
    // Some orders can possibly have expiration associated with them .. taking a typical case of gold
    // transfer where price of an item say gold fluctuates and is locked for the next 5 minutes.
    DEADLINE_EXCEEDED = 4;
    // The actor does not have permission to execute the specified operation.
    // the reason can be many but not limited to-
    // 1) actor doesn't have access to perform operation on the given order id
    // 2) logged in actor has blocked/reported payee actor
    // 3) logged in actor is blocked/reported by payee actor
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // payment failed due to failure at bank end. This can be due to various reasons like
    // user account is frozen, etc.
    // in this case response will also contain error view
    // with granular level messages to be shown to the user
    PAYMENT_FAILED = 100;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 101;
  }
  // Denotes the status of the payment request
  rpc.Status status = 1;
  // A timer for the client post which the client should request for status
  // Any attempt prior to this timer may not be honored & result in an error
  google.protobuf.Duration status_timer = 3;
  // contains the different messages to be shown to the user for failed transactions
  // will be returned nil in case of non failed transactions
  ErrorView error_view = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetOrderStatusRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // A unique identifier to represent the P2P Transaction request.
  // This order id can also represent a recurring payment registration
  // order. In that case, this will be equivalent to the recurring payment
  // id.
  string order_id = 2;
  // This blob will contain domain specific data that we might need to
  // fetch the order from the db. For eg. Ownership has to be fetched from
  // here to query the correct database
  bytes orchestration_metadata = 3;
}

message GetOrderStatusResponse {
  enum Status {
    OK = 0;
    // invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // order record couldn't be found corresponding to the order-id
    NOT_FOUND = 5;
    // The actor does not have permission to execute the specified operations.
    // One of the reason could be that Actor is not involved in the order.
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // payment failed due to failure at bank end. This can be due to various reasons like
    // user account is frozen, etc.
    // in this case response will also contain error view
    // with granular level messages to be shown to the user
    PAYMENT_FAILED = 100;
    // This error comes for upi transactions. The error comes from NPCI and
    // we have observed that refreshing the NPCI CL token generally solves the issue.
    // Adding this status code so that client can force refresh the token
    DEVICE_FINGERPRINT_MISMATCH = 101;
  }
  // Denotes the status of the P2P transaction request
  rpc.Status status = 1;

  // Order timeline event object returned as a part of status call.
  // It contains updated order status.
  // Entire object needs to be returned so that client side can persist the order event for timeline.
  // The assumption here is that client side database is not reliable and can be corrupted easily.
  // Returning entire object ensures protection against client side data corruption.
  frontend.pay.OrderEvent order = 2;

  // A timer for the client post which the client should request for status
  // Any attempt prior to this timer may not be honored & result in an error
  google.protobuf.Duration retry_timer = 3;
  // TODO(pruthvi): Do Meta blob offer help with retry logic

  // contains the different messages to be shown to the user for failed transactions
  // will be returned nil in case of non failed transactions
  ErrorView error_view = 4;

  // response params to be returned for intent base payment
  // Optional: will be populated only for failed/success intent based payments.
  IntentResponseParams intent_response_params = 5;
  frontend.header.ResponseHeader resp_header = 15;

  // the field dictates which screen the user should be taken to post payment termination.
  // the client should use this when populated.
  frontend.deeplink.Deeplink next_action = 6;

  // list of categories to which given payment belongs to. Transactions are categorised on a best effort basis based
  // on the current knowledge about the transaction.
  // Hence, there is a possibility that few transactions are not categorised. In such case, categories wont be populated
  repeated frontend.pay.TransactionCategory txn_categories = 7;

  // flag to enable/disable category editing option.
  // For some transaction category can't be editable but
  // for some transaction it is allowed to edit category
  bool is_category_editable = 8;
}

message GetCollectOrderTransactionDetailsRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // unique order identifier against which transaction details needs to be fetched
  string order_id = 2 [(validate.rules).string = {min_len: 1, max_len: 35}];
}

message GetCollectOrderTransactionDetailsResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like order id invalid, order state invalid, etc.
    INVALID_ARGUMENT = 3;
    // order has expired as payment shouldn'be initiated against this order
    // Some orders can possibly have expiration associated with them .. taking a typical case of gold
    // transfer where price of an item say gold fluctuates and is locked for the next 5 minutes.
    DEADLINE_EXCEEDED = 4;
    // order record not found
    RECORD_NOT_FOUND = 5;
    // user doesn't have access to perform operation on the given order id
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // order has expired, payment shouldn'be initiated against this order
    ORDER_EXPIRED = 100;
    // order status is in valid. Typically returned when order is not in `CREATED` state.
    ORDER_STATUS_INVALID = 101;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    ORDER_COOL_OFF_VALIDATION_FAILED = 102;
  }
  // Denotes the status of the P2P transaction request
  rpc.Status status = 1;

  // informs client about what kind of pin is required in order to
  PinRequiredType pin_required = 2;

  // a list of eligible accounts along with their transaction attribute for making payment for a given collect order.
  repeated TransactionAttribute transaction_attributes = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetEligibleAccountsForPaymentRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // TransactionScope - represents the scope of the payment for which eligible accounts need to be found
  // By default scope will be considered as DOMESTIC
  frontend.pay.PaymentScope payment_scope = 2;
}

message GetEligibleAccountsForPaymentResponse {
  enum Status {
    // Returned an success
    OK = 0;
    // invalid argument passed by the client
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Denotes the status of the request
  rpc.Status status = 1;
  // contains information related to eligible accounts for making a payment. Client can surface this information to the user while he is trying to make a payment.
  message AccountAttributes {
    // account Id
    string account_id = 1;

    // masked account number
    string mask_account_number = 2;

    // denotes if the account is primary or not
    bool is_primary = 3;
    // name of the partner bank
    string partner_bank_name = 4;

    // Type of the account
    accounts.Type account_type = 5;

    // partner bank icon url
    string bank_icon_url = 6;

    // Account Product Offering associated with the AccountType.
    // 1. This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
    //    We can consider UNSPECIFIED equivalent to APO_REGULAR in such cases.
    //
    // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
    api.typesv2.account.AccountProductOffering account_product_offering = 7;
  }

  // accounts attributes corresponding to the account accounts available for actor
  repeated AccountAttributes account_attributes = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message InitiateCollectRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // A unique identifier to represent the P2P Transaction request
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage the request
  // 1. initiate the transaction for the request
  // 2. Get status of the request
  // Client is supposed to pass the same request id that it receives from
  // `CreateP2PCollectOrder`
  string order_id = 2 [(validate.rules).string = {min_len: 4, max_len: 35}];
}

message InitiateCollectResponse {
  enum Status {
    // Successful ack from the initiation
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like order id invalid, order state invalid, etc.
    INVALID_ARGUMENT = 3;
    // order has expired as collect request can't be initiated against this order
    DEADLINE_EXCEEDED = 4;
    // user doesn't have access to perform operation on the given order id
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // collect registration failed. This can be due to various reasons like
    // invalid payer address, etc.
    COLLECT_REGISTRATION_FAILED = 100;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 101;
  }

  // Denotes the status of the request
  rpc.Status status = 1;

  // A timer for the client post which the client should request for status
  // Any attempt prior to this timer may not be honored & result in an error
  google.protobuf.Duration status_timer = 2;
  // contains the different messages to be shown to the user for failed transactions
  // will be returned nil in case of non failed transactions
  ErrorView error_view = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message DismissCollectRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // A unique identifier to represent a collect request
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks.
  string order_id = 2;
}

message DismissCollectResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like order id invalid, order state invalid, etc.
    INVALID_ARGUMENT = 3;
    // user doesn't have access to perform operation on the given order id
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
  }
  // denotes the status of the DismissCollect Request
  rpc.Status status = 1;

  // It contains updated order status.
  // Entire object needs to be returned so that client side can persist the order event for timeline.
  // The assumption here is that client side database is not reliable and can be corrupted easily.
  // Returning entire object ensures protection against client side data corruption.
  frontend.pay.OrderEvent order = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message CreateURNOrderRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // account_id of the payee.
  string payee_account_id = 3;

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 6 [(validate.rules).message.required = true];

  // indicates if the payment is intent based or not
  // for intent based payments the initiation mode will be "04"
  // for dynamic QR the initiation mode will be "02"
  enum InitiationMode {
    // unspecified
    INITIATION_MODE_UNSPECIFIED = 0;
    // QR code based payment
    QR_CODE = 1;
    // intent based payment
    INTENT = 2;
  }
  // initiation  mode of the payment
  // eg. QR code, Intent etc.
  InitiationMode initiation_mode = 7;

  // UI entry point for the order creation
  // UIEntryPoint is deprecated, please use frontend.deeplink.timeline.TransactionUIEntryPoint instead
  UIEntryPoint ui_entry_point = 8 [deprecated = true];

  // entry point on the client for order creation
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 9;

  // UPI app name/enum for which intent is to be generated
  // Field to be used by iOS only
  frontend.pay.add_funds_v2.UpiApp upi_app = 10;
}

message CreateURNOrderResponse {
  enum Status {
    // collect order request successfully registered
    OK = 0;
    // invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // The actor does not have permission to execute the specified operation.
    // One of the reasons could be that Actor is not owner of the account
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // The service is currently unavailable.  This is most likely a
    // transient condition, which can be corrected by retrying after some interval.
    UNAVAILABLE = 14;
  }
  // Denotes the status of the request
  rpc.Status status = 1;

  // A unique identifier to represent an order
  // Order is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage transaction requests and their workflow.
  string order_id = 2;

  // urn that needs to be passed to the 3rd party app
  string urn = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetOrderReceiptRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Unique order representation in the system
  string order_id = 2 [deprecated = true];

  oneof identifier {
    string orders_id = 3;
    string aa_txn_id = 4;
  }

}

message GetOrderReceiptResponse {
  reserved 2, 3, 4, 9;

  enum Status {
    // collect order request successfully registered
    OK = 0;
    // invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // order record couldn't be found corresponding to the order id
    NOT_FOUND = 5;
    // The actor does not have permission to execute the specified operation.
    // One of the reasons could be that Actor is not involved in the order
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
  }
  // Denotes the status of the request
  rpc.Status status = 1;

  // category to which a given payment belongs to. It can be either added by user or
  // system generated after parsing the payment details.
  // To begin with category will be empty. It will be added slowly as the system becomes more intelligent
  string category = 5;

  // Defines the order stage
  // A stage is nothing but description of an order step of the order execution
  // along with the status
  message OrderStage {
    enum Status {
      // Default value for the status enum.
      STATUS_UNSPECIFIED = 0;
      // Order stage is yet to be started
      PENDING = 1;
      // Order stage has started but yet to be finished
      IN_PROGRESS = 2;
      // Order stage has finished successfully
      SUCCESS = 3;
      // Order stage execution has failed
      FAILED = 4;
    }
    Status status = 1;

    // Detailed description of the status in a human understandable language. The
    // client may choose to display this string as-is to the user.
    string localized_description = 2;

    // time of execution of the order stage to be displayed to the user.
    // depending on the details available it may be or may not be populated
    string execution_time = 3;

    // Text giving more info about a particular stage.
    // for eg. in case of reversal will contain time stamp of
    // when the amount is credited back to the account
    string detailed_description = 4;
  }

  // upper level brief description of the order payment
  OrderStage overall_stage = 6;

  // granular order narration to be displayed to the user
  // It gives details of all the possible steps involved in order execution
  // along with their status
  repeated OrderStage order_stages = 7;

  // bank account details from/to which txn has happened
  message BankAccountInfo {
    string bank_icon_url = 1;
    string display_info = 2;
  }

  message PaymentDetail {
    // user interpretable description of the payment detail
    string description = 1;

    // actual value of the payment details
    // It can contains a wide variety depending on payment detail
    // e.g. in case of payer/payee details the value will be the payment instrument used to make the payment
    // in case of external transaction id, it can be utr or rrn number to be referred by the customer
    string value = 2;

    // OPTIONAL - only sent in case of UPI transactions
    BankAccountInfo bank_account_info = 3;
  }

  // list of payment details returned with the receipt.
  // A user can use this information to share the transaction confirmation
  // with the payee or to raise a request against the payment.
  //
  // repeated list because the payment details to be displayed in the receipt
  // can vary from on case by case basis.
  // e.g. a bill payment order can have additional details like recharge fulfillment
  // id, along with the usual payment details.
  // or an order with refund or reversal can have reversal transaction id.
  //
  // Also, repeated list allows send back the response in the specified ordered
  // sequence to be displayed
  // while using a plan map would not honour the ordering here.
  repeated PaymentDetail details = 8;

  // amount of monies refunded back to the payer.
  // This field is only applicable to the orders involving
  // refund. The refunds can be partial or full depending on
  // case by case basis but not greater than the original amount.
  api.typesv2.Money refund_amount = 10;


  // signifies if raise dispute button should be enabled
  // for a given transaction. It can be calculated as a function
  // various parameters like payment risk assessment scores, etc. in backend
  bool is_raise_dispute_enabled = 11 [deprecated = true];

  // provides information about the dispute raised by the user
  // e.g. time when dispute was raised by the user, the status, etc.
  // Note: the field will be populated only in case user has raised dispute against
  // the transaction
  string dispute_status_info = 12;

  // ReceiptHead contains information about the receipt head
  message ReceiptHead {
    // Receipt heading
    // contains the heading of the receipts.
    // e.g. Received From in case of credit
    // and Paid To in case of debit
    string title = 1;

    // Subtitle of the receipt
    // contains details from/to where money movement happened
    // contains payer/payee actor information in case of payment
    // contains deposit information in case of deposit money movement
    string subtitle = 2;

    // Icon image url, to give user easy understanding on what the transaction
    // is about.
    string icon_url = 3;

    // Color code of the icon back ground. In the absence of icon url client can
    // generate an icon using the initials from subtitle
    string icon_colour_code = 4;

    // colour code for receipt background
    string bg_colour_code = 5;

    // amount of monies involved in the payment
    api.typesv2.Money amount = 6;

    // a.k.a transaction notes
    // it can be entered by user as well system generated remarks
    string remarks = 7;

    // boolean to denote if the transaction is reversed.
    bool is_reversal = 8;

    // url for the short icon the receipt head.
    // will contain bank icon url
    string short_icon_url = 9;

    // deep-link to the screen, to which the client should redirect the user, on clicking icon in transaction receipt

    deeplink.Deeplink icon_deeplink = 10;

    // Icon image url to show the status of the order
    string status_icon = 11;

    // banner to show in case of pending payment
    // Deprecated, please use deeplink_banner for similar use cases
    api.typesv2.common.Text banner = 12 [deprecated = true];

    //amount of money in foreign currency involved in payment in case of international payment
    api.typesv2.Money base_amount_quote_currency = 13;

    // banner with deeplink based upon different scenerios (but not limited to)
    // ecs/enach charges
    // deemed transactions
    api.typesv2.ui.IconTextComponent deeplink_banner = 14;

    // trusted_merchant_check_icon to signify if merchant is trusted
    // icon will be available for the transactions with trusted merchants
    // design ref: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=23736-82044&mode=design&t=jGGy2JCjwVjGqioS-0
    api.typesv2.common.VisualElement trusted_merchant_icon = 15;

    // transaction categories related data (e.g. categories, suggested categories, info texts to display, etc)
    frontend.pay.TransactionCategoriesData txn_categories_data = 30;

    // Order receipt banner to be shown on the top of the receipt.
    // This can be used for various purposes such as:
    // 1. for taking the user to a story based on a combination of multiple attributes associated with the txn, such as status, payer-payee status-code etc.
    // 2. for redirecting the user to another related order-receipt.
    // 3. for show details regarding a dispute ticket.
    // Note: If this field is present, prioritise it over inapphelp_media_params. Else, fallback to the earlier logic.
    api.typesv2.ui.IconTextComponent order_receipt_banner = 31;

    //  bottom info banner to be shown on the receipt. eg- 'This is a recurring mandate. View mandate details'
    // design ref: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=13969-24692&t=w2qqHoj0at2WDzMu-4
    api.typesv2.ui.IconTextComponent bottom_info_banner = 32;
  }

  ReceiptHead receipt_head = 14;

  // NOTE : This might be empty in some scenarios,
  // like collect request raised, collect request cancelled, etc
  string transaction_id = 15;

  // Error view contains the detailed reasoning why payment has failed
  // and what is the expected to be done in case of payment has failed
  // It will be only populated in case payment has failed
  // Please Note: actions will be empty for the error view in receipts
  ErrorView error_view = 16;

  // external order id is human readable shorter analogous to the order id
  // to be used to display to the end user
  string order_external_id = 17;

  // Payment protocol used for the transaction. It can be UNSPECIFIED for some transactions.
  frontend.pay.PaymentProtocol payment_protocol = 18;
  frontend.header.ResponseHeader resp_header = 19;

  // list of components to be shown for receipt
  // eg- should dispute raise option be enabled, show get help section etc
  repeated frontend.pay.transaction.ReceiptComponent receipt_components = 20;

  // message to show on receipt
  string receipt_info = 22;
  // list of categories to which given payment belongs to. Transactions are categorised on a best effort basis based
  // one the current knowledge about the transaction. Hence, there is a possibility that few transactions are not categorised. In such case, categories wont be populated
  repeated frontend.pay.TransactionCategory txn_categories = 21;

  // Parameters to pass for showing story (or other inapphelp media) on the receipt screen
  // eg cases- UPI deemed success, UPI txn failure P2P, P2M etc
  // Deprecated: prioritise `OrderReceiptBanner` (within `ReceiptHead`) over this field.
  api.typesv2.InapphelpMediaUIContextMeta.TxnReceiptMeta inapphelp_media_params = 23 [deprecated = true];

  // flag is set to true, will give client option to edit categories otherwise not
  bool can_update_category = 24;

  // Can Contain multiple ctas from list of UPI help, Get Help and FAQs.
  repeated api.typesv2.ui.IconTextComponent footer_ctas = 25;

  // footer message for the receipt
  api.typesv2.ui.IconTextComponent footer_message = 26;

  // user caution banner for the receipt
  api.typesv2.ui.IconTextComponent user_caution = 27;

  // to decide whether to expand order stage on the transaction screen or not
  OrderStageExpandState expand_order_stage = 28;

  // suggested transaction categories user can apply as main category
  // maximum of 3 categories are suggested
  repeated frontend.pay.TransactionCategory suggested_categories = 29 [deprecated = true];

  // Tiles will used to add new different type of component in order receipt
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=17665-4297&mode=design&t=4Ix8ZRxT4npbZ1p3-0
  repeated Tile tiles = 30;

  // provides information about all the rewards earned for given order
  // e.g. shows total fi coins & cash reward earned for this txn.
  // figma: https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4240-12243&mode=design&t=czILGoiezN25iTOV-0
  RewardDetails reward_details = 31;
  // partner tag to be shown for different type of txns
  // E.g. UPI Payments, UPI AutoPay txn
  api.typesv2.common.VisualElement partner_tag = 32;

  // display error descriptions & CTAs for failed transactions
  // Figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=26564-28330&t=6k718OXtlQ8ZNarr-1
  message ReceiptErrorInfo {
    api.typesv2.ui.IconTextComponent title = 1;
    api.typesv2.common.Text description = 2;
    repeated api.typesv2.ui.IconTextComponent ctas = 3;
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  }
  ReceiptErrorInfo receipt_error_info = 33;

  // component to be used in OrderStage for redirection to another screen
  // Currently used to display an associated transaction, e.x. forex fees & corresponding original transaction
  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=22750-40316&t=XtpnXnspz5kOLvl3-1
  api.typesv2.ui.IconTextComponent associated_transaction = 34;
}


message RewardDetails {
  // rewards section on order receipt to show summary of rewards earned.
  RewardSummarySection summary = 1;
  // bottom sheet to show earned reward details
  RewardDetailsBottomSheet bottom_sheet = 2;
}

message RewardSummarySection {
  // title of the section eg: "You earned"
  api.typesv2.common.Text title = 1;
  // sub title of the section eg: "on Infinite Plan"
  api.typesv2.common.Text sub_title = 2;
  // shows max of 3 tiles with actual earned value sorted in descending and then projected values in same descending sorted other.
  // eg: shows 50 fi coins, 30 rupees, 40 rupees (projected), 10 fi coins (projected)
  repeated EarnedValueChip reward_values = 3;

  message EarnedValueChip {
    // icon: fi-coins/rupee, text: value of the reward.
    api.typesv2.ui.IconTextComponent value = 1;
    // boolean flag to show chip in washed out shade.
    bool show_washed_out_chip = 2;
  }
  // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=8916-15750&t=SfH9gZ2uZBXDQW7K-0
  // footer icon text component having announcement in the info section at the top of the transaction receipt
  api.typesv2.ui.IconTextComponent footer_info_text_component = 4;
}

message RewardDetailsBottomSheet {
  // section title of the bottom sheet eg: "You earned"
  api.typesv2.common.Text title = 1;
  // list of details of the rewards earned
  repeated RewardDetailsRow reward_details_cards = 2;
  // background color for bottom sheet
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  // projected rewards info text under the reward bottom rows. E.g. 'Rewards shown as projected are indicative and subject to specific reward conditions, internal policy and Fi's discretion.'. Figma:
  // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6349-16639&t=4FBMVTMflBROeyXs-0
  api.typesv2.common.Text bottom_sheet_info_text = 4;

  message RewardDetailsRow {
    // icon to show on left of title
    api.typesv2.common.VisualElement icon = 1;
    // title describing type of reward ex: Fi-Coins, Cashback
    api.typesv2.common.Text title = 2;
    // value describing how much a user earn through this, eg: Rs 30
    api.typesv2.ui.IconTextComponent earned_value = 3;
    // screen to redirected to on clicking the card
    frontend.deeplink.Deeplink deeplink = 4;
    // show washed out card describing the reward is yet to be processed.
    bool show_washed_out_card = 5;
    // if show washed out card is true then show below details
    // eg: icon can be timer_icon and text can be "Expected credit: 3 June"
    api.typesv2.ui.IconTextComponent desc = 6;
    // background color for row
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 7;
  }
  // Contains a deeplink in the reward details bottom sheet
  frontend.deeplink.Cta cta = 5;
}

message Tile {
  oneof display_tile {
    ChargeDescriptionDetails charge_description_details = 1;
  }
}

// ChargeDescriptionDetails will consist of all the details
// which we need to show for charges in order receipt
// ref: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=17669-104381&mode=design&t=BbwvWDhwB7sUiiM1-0
message ChargeDescriptionDetails {
  api.typesv2.common.VisualElement charge_description_icon = 1;
  api.typesv2.common.Text charge_title = 2;
  api.typesv2.common.Text charge_description = 3;
  repeated api.typesv2.ui.IconTextComponent cta_list = 4;
}

// contains the message that needs to be shown on the client in case of transaction failure/errors
message ErrorView {
  // title of the error view - eg. Transaction Failed
  string title = 1;
  // sub_title of the error view - eg. We are facing some internal issues
  string sub_title = 2 [(validate.rules).string.max_len = 50];
  // description of the error view - eg. UPI daily limit exhausted
  string description = 3 [(validate.rules).string.max_len = 100];
  // error view icon url
  string icon_url = 4;
  // actions to the user
  // eg. retry option in case of invalid beneficiary details,
  repeated frontend.timeline.TimelineAction actions = 5;
}

message RaiseDisputeRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // A unique identifier to represent an order
  // Order is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage transaction requests and their workflow.
  string order_id = 2 [(validate.rules).string = {min_len: 1, max_len: 35}];
}

message RaiseDisputeResponse {
  enum Status {
    // call successful
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // dispute is already raised for the transaction
    ALREADY_PROCESSED = 50;
    // Order id does not belong to the actor logged in
    PERMISSION_DENIED = 7;
    // if order is not in right state
    FAILED_PRECONDITION = 9;
  }
  // Status of the RPC call.
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

message BottomSheetActions {
  enum ActionType {
    // No action can be taken on the event.
    ACTION_TYPE_UNSPECIFIED = 0;
    // Ok allows user to dismiss the pop-up
    OK = 1;
    // Allows a user to raise dispute against a transaction
    RAISE_DISPUTE = 2;
    // Help takes an user to the customer support page
    HELP = 3;
  }

  ActionType action = 1;

  // Defines action precedence.
  // Based on an action precedence client can choose to display an action differently.
  // e.g. One of the possible way would be to show primary actionable in bold while secondary actionable with
  // normal text.
  // or PRIMARY can pre-selected on the UI while secondary can be non-selected
  enum Precedence {
    PRECEDENCE_UNSPECIFIED = 0;

    PRIMARY = 1;

    SECONDARY = 2;
  }

  Precedence action_precedence = 2;

  // action button text display value
  string display_value = 3;
}

// response params to be returned for intent based payment
message IntentResponseParams {
  // post successful/failure transaction "tid"
  // should be sent same as generated  by intent app
  string txn_id = 1;
  // response code of the txn post success/failure
  string response_code = 2;
  // post successful/failure  transaction "tr" should be sent
  // as generated  by intent app
  string tr_txn_ref = 4;
  // rrn used for the txn
  string approval_ref_number = 5;
}

// UIEntryPoint is deprecated, please use [frontend.deeplink.timeline.TransactionUIEntryPoint] instead
enum UIEntryPoint {
  // unspecified
  UI_ENTRY_POINT_UNSPECIFIED = 0;
  // Signifies order was created using timeline
  // wil be used as default in case entry point is unspecified
  TIMELINE = 1;
  // Signifies QR code scan as the entry point for order creation
  QR_CODE = 2;
  // Signifies Third Party Intent as the entry point for order creation
  INTENT = 3;
  // Denotes order was created for add funds flow during onboarding
  ONBOARD_ADD_FUNDS = 4;
  // Signifies Transfer In the entry point for order creation
  TRANSFER_IN = 5;
  // Signifies add funds order was created from home screen
  HOME = 6;
  // Signifies order was created from account details screen
  ACCOUNT_DETAILS = 7;
  // Signifies order was created from account summary screen
  ACCOUNT_SUMMARY = 8;
  // Signifies order was created from referrals screen
  REFERRALS = 9;
  // Signifies order created from Home screen's persistent Add funds button
  HOME_PERSISTENT_CTA = 10;
  // Signifies order was created from Deposit creation flow, if funds were insufficient
  // for deposit creation
  DEPOSIT_CREATION = 11;
  // Signifies order was created from Bonus jar creation flow, if funds were insufficient
  // for bonus jar creation
  BONUS_JAR_CREATION = 12;
  // Signifies order was created from One time mutual fund Investment flow
  MF_BUY_ONE_TIME = 13;
  // Signifies order was created from P2P invest flow
  P2P_INVEST = 14;
  // Signifies order was created from US stocks invest flow
  US_STOCKS = 15;
  // Signifies order was created from chequebook creation
  CHEQUEBOOK = 16;
  // Signifies order was created from all plans join plus cta
  ALL_PLANS_JOIN_PLUS = 17;
  // Signifies order was created from all plans join infinite cta
  ALL_PLANS_JOIN_INFINITE = 18;
  // Signifies order was created from AskFi result summary
  ASK_FI = 19;
  // Signifies order is getting created while activation of UPI Lite
  ACTIVATE_UPI_LITE = 20;
  // Signifies order is getting created for UPI Lite top up
  TOP_UP_UPI_LITE = 21;
  // Signifies order is getting created while deletion of UPI Lite
  DELETE_UPI_LITE = 22;
  // to be used for the qr payments done via qr share and pay options.
  // E.g. uploading a qr from gallery received via whatsapp or some
  // other medium etc.
  QR_SHARE_AND_PAY = 23;
  // Signifies order is getting created in AA salary add funds flow
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=19696-28555&mode=design&t=13Y3cc6K5WNDYx1P-0
  AA_SALARY = 24;
  // Signifies order was initiated from order physical debit card flow
  PHYSICAL_DEBIT_CARD_CHARGES = 25;
  // if a user has insufficient fund in savings account, we show the payment options to add funds
  ADD_FUNDS_USS = 26;
  // signifies that the order creation has been done from the preapproved
  // loans prepay's end
  UI_ENTRY_POINT_LOAN_PREPAYMENT = 27;
  // signifies that the order creation has been done to register a mandate
  // using an external route (eg. PG)
  UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION = 28;
  // Add funds flow initiated from account closure screen for pending charges
  ACCOUNT_CLOSURE_PENDING_CHARGES = 29;
  // Signifies order creation was initiated by pre-funding add funds flow during onboarding
  UI_ENTRY_POINT_ONBOARD_ADD_FUNDS_PRE_FUNDING = 30;
  // Use for pitching tiers to user from add funds drop off screen
  // Deprecated: Use AA_SALARY,ALL_PLANS_JOIN_INFINITE,ALL_PLANS_JOIN_PLUS instead
  TIER_ALL_PLANS_PAGE_INFINITE = 31 [deprecated = true];
  TIER_ALL_PLANS_PAGE_PRIME = 32 [deprecated = true];
  TIER_ALL_PLANS_PAGE_PLUS = 33 [deprecated = true];
  ALL_PLANS_JOIN_PRIME = 34;
  // Add funds flow initiated from AMB details screen
  AMB_DETAILS = 35;
  // payment initiated from recharge flow
  RECHARGE_PAYMENT = 36;
}

message GetAddFundParamsRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;

  // UI entry point for the add funds flow
  UIEntryPoint ui_entry_point = 2;

  // entry point on the client for order creation
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 3;
}

// Enum to specify add funds version to display
enum AddFundsVersion {
  ADD_FUNDS_VERSION_UNSPECIFIED = 0;
  ADD_FUNDS_VERSION_V1 = 1;
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-60680&mode=design&t=x4uoemlNigT4wMXl-0
  ADD_FUNDS_VERSION_V2 = 2;
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-61390&mode=design&t=x4uoemlNigT4wMXl-0
  ADD_FUNDS_VERSION_V3 = 3;
  // this version involves integration with PaymentOptions screen
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=2083-54863&mode=dev
  ADD_FUNDS_VERSION_V4 = 4;
}

enum PaymentCtaOperationType {
  PAYMENT_CTA_OPERATION_TYPE_UNSPECIFIED = 0;
  PAYMENT_CTA_OPERATION_TYPE_BOTTOM_SHEET = 1;
  PAYMENT_CTA_OPERATION_TYPE_DEEPLINK = 2;
}

// PaymentCtaOperationDetails can be used for scenarios where we'd want to perform some pre-steps/actions before actually
// proceeding for the payment.
// for e.g., open a bottom-sheet before performing add-funds, or directly navigate to a screen upon tapping the pay button
message PaymentCtaOperationDetails {
  // possible operations upon tap of the payment initiation cta.
  // for e.g. "Add Funds" in case of add-funds flow
  repeated Operation operations = 1;

  message Operation {
    // type of operation upon tap of CTA
    PaymentCtaOperationType payment_cta_operation_type = 1;
    // minimum amount the operation is applicable for [inclusive]
    api.typesv2.Money min_amount = 2;
    // maximum amount the operation is applicable for [inclusive]
    api.typesv2.Money max_amount = 3;

    // operation-type specific fields
    oneof operation_type_details {
      // bottom-sheet to be shown if the operation type is BOTTOM_SHEET
      BottomSheet bottom_sheet = 10;
      // deeplink to be navigated to if the operation type is DEEPLINK
      deeplink.Deeplink deeplink = 11;
    }
  }

  message BottomSheet {
    // image at the top of the bottom sheet
    api.typesv2.common.VisualElement image = 1;
    // title, note: shouldn't be nil
    api.typesv2.common.Text title = 2;
    // description, note: can be nil
    api.typesv2.common.Text description = 3;
    // CTAs to be shown on the bottom-sheet
    repeated deeplink.Cta cta = 4;
  }
}

message GetAddFundParamsResponse {
  enum Status {
    // call successful
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the RPC call.
  rpc.Status status = 1;

  frontend.header.ResponseHeader resp_header = 2;

  // default amount to be pre-selected for the add funds flow
  api.typesv2.Money default_amount = 3;

  // min amount allowed for add funds flow
  api.typesv2.Money min_amount = 4;

  // max amount allowed for add funds flow
  api.typesv2.Money max_amount = 5;

  // list of suggested amounts
  repeated SuggestedAmount suggested_amounts = 6;

  // remarks to be passed in the intent for add funds flow
  string remarks = 7;

  // rewards banner to be shown to the user during add funds flow
  RewardsBanner rewards_banner = 8;

  // denotes if the user should be blocked on the screen
  // until add funds is complete
  //
  // in case of ONBOARD_ADD_FUND wwe will block the user from moving forward until
  // at least the first leg of transaction is complete
  bool is_blocking = 9;

  // Flag to decide whether tiering is enabled for this user or not
  // Deprecated: Use add_funds_version instead
  api.typesv2.common.BooleanEnum is_tiering_enabled = 10 [deprecated = true];

  // If the flag "is_tiering_enabled" is true, client takes user to the new add funds page with tier card
  // Otherwise it renders the existing add funds page with the parameters populated above.
  frontend.pay.add_funds_v2.AddFundsV2ScreenDetails add_funds_v2_tiering_screen = 11;

  // Enum to specify add funds version to show
  // This acts a flag to switch between different versions(V1, V2, V3 etc)
  AddFundsVersion add_funds_version = 12;

  // Screen details needed for add funds v3 screen which will reuse the AddFundsV2ScreenDetails
  frontend.pay.add_funds_v2.AddFundsV2ScreenDetails add_funds_v3_screen_details = 13;

  // field for performing different operations upon tapping the "Add funds" cta.
  // Note: will be applicable from version v4 onwards (though, data will be used from existing fields as well).
  PaymentCtaOperationDetails add_funds_cta_operation_details = 14;

  // Tag to be shown with the suggested amounts
  // eg. "Popular"
  message Tag {
    // title for the tag
    string title = 1;
    // icon url for the tag
    string icon_url = 2;
  }

  // Suggested amounts for the add fund flow
  // user will see a list of suggested amounts to add in the account
  message SuggestedAmount {
    // suggested amount value
    api.typesv2.Money amount = 1;
    // list of tags for the suggested amounts
    repeated Tag tags = 2;
  }

  // Rewards banner to be shown to the user during add funds flow
  message RewardsBanner {
    // description for the rewards banner
    string description = 1;
    // icon url for the rewards banner
    string icon_url = 2;
  }

  // Flag used in android, instead of navigating to add money status for polling navigate to pay status.
  bool intent_navigate_to_pay_status = 15;
  bool collect_navigate_to_pay_status = 16;

  // Back navigation drop off for the add funds flow
  // This will be displayed if it is there otherwise not
  deeplink.Deeplink back_navigation_drop_off_deeplink = 17;
}

message CheckIfAddFundsAllowedRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 2 [(validate.rules).message.required = true];

  // [Optional] User identifier of payee(since user can have multiple accounts)
  // If empty, will fetch account based on APO hierarchy - REGULAR > NRO > NRE
  api.typesv2.pay.user_identifier.UserIdentifier payee_user_identifier = 3;

  UIEntryPoint ui_entry_point = 4;
}

message CheckIfAddFundsAllowedResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 3;
  }

  // pop up option shows the message to warn user or stop them from adding funds
  frontend.deeplink.InformationPopupOptions pop_up_option = 1;

  frontend.header.ResponseHeader resp_header = 15;
}

enum ReceiptComponent {
  RECEIPT_COMPONENT_UNSPECIFIED = 0;
  USER_CAUTION = 1;
  SHOW_HELP = 2;
  // When this enum is present in Component then frontend will provide option to raise dispute.
  RAISE_DISPUTE = 3;
  // when this component is present, frontend need to disable the
  // raise dispute option. User will be able to see raise dispute option but not able
  // to create dispute.
  DISABLE_DISPUTE = 4;
  // When this enum is detected within the Component, users will have the option to report fraud directly from the order receipt page.
  // The reporting timeframe will be defined by the protocol, allowing users to report any fraudulent transactions within the specified duration.
  REPORT_FRAUD = 5;
}

message AuthoriseFundTransferRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 15;

  string client_request_id = 1 [(validate.rules).string = {min_len: 4, max_len: 36}];

  // Credential contains a cred block that acts as second auth factor for the transactions
  // which require another factor of authentication
  //
  // Cred block can either be generated through NPCI CL or Partner Bank's SDK
  // They typically contain Salt parameters in addition to PIN such as, but not limited to:
  // 1. Transaction ID
  // 2. Amount
  // 3. Timestamp
  oneof credential {
    // Financial transactions such as Intra Bank, NEFT, IMPS RTGS require `Secure PIN`
    // as second factor authentication. Secure PIN is the same as UPI PIN
    //
    // However, the encrypted PIN is generated through Partner bank's SDK and is a
    // Base64 encoded JSON string that can be passed as it is
    string partner_sdk_cred_block = 2;
    // All the UPI transactions require Cred block generated through NPCI common library
    // Cred block is to be passed to NPCI in XML format
    // To prevent errors in data transformation and to keep business logic out of client,
    // frontend expects NPCI's cred block in a structured fashion
    frontend.account.upi.CredBlock npci_cred_block = 3;
  }

  // payment related data from client. This data is required to validate authorise call.
  // A transaction can only be created by these payment details.
  pay.transaction.payload.PaymentDetails payment_details = 4;
}

message AuthoriseFundTransferResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like client request id invalid, order state invalid, etc.
    INVALID_ARGUMENT = 3;
    // order has expired as payment shouldn'be initiated against this order
    // Some orders can possibly have expiration associated with them .. taking a typical case of gold
    // transfer where price of an item say gold fluctuates and is locked for the next 5 minutes.
    DEADLINE_EXCEEDED = 4;
    // The actor does not have permission to execute the specified operation.
    // the reason can be many but not limited to-
    // 1) actor doesn't have access to perform operation on the given order id
    // 2) logged in actor has blocked/reported payee actor
    // 3) logged in actor is blocked/reported by payee actor
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // payment failed due to failure at bank end. This can be due to various reasons like
    // user account is frozen, etc.
    // in this case response will also contain error view
    // with granular level messages to be shown to the user
    PAYMENT_FAILED = 100;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 101;
  }
  frontend.header.ResponseHeader resp_header = 15;

  // deeplink which helps client to navigate post authorisation call being successful
  frontend.deeplink.Deeplink post_authorise_deeplink = 16;
}

message GetEligibleAccountsForPaymentV1Request {
  frontend.header.RequestHeader req = 15;
  // TransactionScope - represents the scope of the payment for which eligible accounts need to be found
  // By default scope will be considered as DOMESTIC
  frontend.pay.PaymentScope payment_scope = 1;
  // actor id of the second actor to which payment is done
  string second_actor_id = 2;
  // entrypoint from where GetEligibleAccountsForPaymentV1 RPC is called
  EligibleAccountsUIEntryPoint eligible_accounts_ui_entry_point = 3;
  // pi id of payee, to which payment needs to be done
  string payee_pi_id = 4;
}

message GetEligibleAccountsForPaymentV1Response {
  frontend.header.ResponseHeader resp_header = 15;
  // list of account info containing account details like account id, masked account number etc.
  repeated frontend.account.AccountInfo account_infos = 1;
  // entrypoint for actions (e.g. linking upi accounts)
  repeated api.typesv2.ui.IconTextComponent entrypoints = 2;
  // UI for the selector to be shown to the user.
  frontend.pay.AccountSelectorCard account_selector_card = 3;
  // partner tag to be shown in ENTER_AMOUNT_SCREEN
  // Example: "Powered by Rupay Credit on UPI" in case of Merchant Payments (P2M) & User has connected rupay credit card.
  // Note: In case of few merchant payments we will not be showing rupay credit card as option for payment hence in those cases partner tag will also be of "powered by Bhim Upi"
  api.typesv2.common.VisualElement partner_tag = 4;
}

// Information related to user like actorId, derivedAccountID, accountType, piId.
// It is not mandatory to have all the field.
message UserIdentifier {
  // actor id for the user
  string actor_id = 1;
  // derived_account_id - account id derived from combination of :
  // internal account id, tpap account id, connected account id, deposit account id
  string derived_account_id = 2;
  // Account type like Savings, Current
  .accounts.Type account_type = 3;
  // payment instrument id for the user
  string pi_id = 4;
  // 1. upi number corresponding to a vpa / PI.
  // 2. a user can receive money on a UPI number.
  // Note - If client wants to pay to a upi number, they
  // should send existing identifiers (actor-id, pi-id,
  // or account id) along with it. Decision Engine doesn't
  // have the capability to decide PI for payments soley
  // on the basis of upi number for now. But it could be
  // added eventually.
  string upi_number = 5;
  // Account Product Offering associated with the AccountType.
  // 1. This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  //    We can default to APO_REGULAR in such cases.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering account_product_offering = 6;
}

message GetChatHeadsForPaymentViaNumberRequest {
  frontend.header.RequestHeader req = 15;
  // number for which we need the chat heads
  string number = 1;

  // UI entry point for initiating the request
  PhoneNumberSearchUIEntryPoint ui_entry_point = 2;
}

message GetChatHeadsForPaymentViaNumberResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like client request id invalid, order state invalid, etc.
    INVALID_ARGUMENT = 3;
    // no chat head found for the number
    RECORD_NOT_FOUND = 5;
    // internal server error while processing th request
    INTERNAL = 13;
  }

  // list of chat heads for the given phone number
  repeated frontend.ChatHead chat_heads = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

enum PhoneNumberSearchUIEntryPoint {
  // unspecified
  PHONE_NUMBER_SEARCH_UI_ENTRY_POINT_UNSPECIFIED = 0;

  // UI entry point to pay via upi id/number
  PHONE_NUMBER_SEARCH_UI_ENTRY_POINT_PAY_VIA_UPI = 1;

  // UI entry point to pay via number
  PHONE_NUMBER_SEARCH_UI_ENTRY_POINT_PAY_VIA_NUMBER = 2;
}

enum OrderStageExpandState {

  // unspecified
  UNSPECIFIED = 0;

  // expand order stages on txn receipt screen
  EXPAND = 1;

  // do not expand order stages on txn receipt screen
  COLLAPSE = 2;
}

enum EligibleAccountsUIEntryPoint {
  // unspecified
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_UNSPECIFIED = 0;

  // UI entry point where amount is entered for payment
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_ENTER_AMOUNT = 1;

  // UI entry point of recurring payment creation
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_RECURRING_PAYMENT = 2;

  // UI entry point where self qr is shown to receive payment
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_SELF_QR = 3;

  // UI entry point of loans payment
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_LENDING = 4;

  // UI entry point of cc payment
  ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_CREDIT_CARD = 5;
}

// As per new requirements seen in UPI Lite now its possible that client can send multiple cred blocks in case of upi payments
// In Upi Lite during top up client will pass a set of two cred blocks : UPI PIN and ARQC
message NpciCredBlocks {
  repeated frontend.account.upi.CredBlock cred_blocks = 1;
}

message CreateFundTransferOrderV1Request {
  frontend.header.RequestHeader req = 1;

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 2 [(validate.rules).message.required = true];

  // Base amount in quote currency involved in payment transaction in case of international payment
  api.typesv2.Money base_amount_quote_currency = 3;

  // entry point on the client for order creation
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 4;


  // User identifier for the payer
  UserIdentifier payer_user_identifier = 5;

  // Metadata is a serialised construct of fields that can evolve over a period of time
  // and is expected to help with any orchestration requirements
  // Field is to be passed as is from the response of `GetPaymentOptions` RPC response
  bytes orchestration_metadata = 6 [(validate.rules).bytes.max_len = 1000];

  // integration_specs will used to identify the vendor
  // and the mode of integration with the vendor, it could be MOBILE_SDK, SERVER_API, etc.
  IntegrationSpecs integration_specs = 7;

  // [Optional] recurring payment id which will only be non empty for a
  // recurring payment i.e if this is populated, it can be assumed that
  // the payment is for a recurring payment
  string recurring_payment_id = 8;
}

// BankPaymentRequestData represents a collection of required data
// for initiating a payment request through a bank.
message BankPaymentRequestData {
  // If pin based authorization is required to execute payment for a given order.
  PinRequiredType pin_required_type = 1;

  // Transaction attributes are required for the client to:
  //  i. Generate the credentials using a common library i.e., NPCI CL or Partner bank's CL
  //  ii. Present the transaction information e.g., payment protocol to the user
  TransactionAttribute transaction_attribute = 2;
}


// PaymentGatewayPaymentDetails represents a collection of required data for processing
// a payment through the payment gateway.
message PaymentGatewayPaymentDetails {
  // vendor_order_id will be a unique id at vendor level to identify the
  // order at vendor side.
  string vendor_order_id = 1;

  // integration_specs will used to identify the vendor
  // and the mode of integration with the vendor, it could be MOBILE_SDK, SERVER_API, etc.
  IntegrationSpecs integration_specs = 2;

  // Amount of monies involved in the transaction
  api.typesv2.Money amount = 3 [(validate.rules).message.required = true];
  // details specific to a recurring payment. If this is non null, then it
  // can be assumed that the given payment is a recurring payment
  RecurringPaymentDetails recurring_payment_details = 4;
}

message RecurringPaymentDetails {
  // [Optional] identifier for user on pg vendor's end. This is only applicable for
  // recurring payments.
  string vendor_customer_id = 1;
  RecurringPaymentMethod recurring_payment_method = 2;
}

enum RecurringPaymentMethod {
  RECURRING_PAYMENT_METHOD_UNSPECIFIED = 0;
  RECURRING_PAYMENT_METHOD_EMANDATE = 1;
  RECURRING_PAYMENT_METHOD_UPI = 2;
}

message CreateFundTransferOrderV1Response {
  enum Status {
    OK = 0;
    // The actor does not have permission to execute the specified operation.
    // the reason can be many but not limited to-
    // 1) account id passed in the request doesnt belong to the logged in actor
    // 2) logged in actor has blocked/reported payee actor
    // 3) logged in actor is blocked/reported by payee actor
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // The service is currently unavailable.  This is most likely a
    // transient condition, which can be corrected by retrying after some interval.
    // e.g., Downtime at partner banks, health checks failing for a given payment protocol
    UNAVAILABLE = 14;
  }

  frontend.header.ResponseHeader resp_header = 1;

  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  // TODO(Rahul): https://github.com/epiFi/protos/pull/15781/files#r1502452027
  frontend.header.Metadata metadata = 2;

  // A unique identifier to represent the order entity
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage the request
  // 1. initiate the transaction for the request
  // 2. Get status of the request
  string order_id = 3;

  // as client need separate data for different flow based
  // based on flow(PG/Bank) we will send one of these data to client
  oneof payment_request_data {
    BankPaymentRequestData bank_payment_request_data = 20;
    PaymentGatewayPaymentDetails payment_gateway_details = 21;
  }
}

message MarkPostPaymentRequest {
  frontend.header.RequestHeader req = 1;

  // Metadata is a serialised construct of fields that can evolve over a period of time
  // and is expected to help with any orchestration requirements
  // Field is to be passed as is from the response of `GetPaymentOptions` RPC response.
  // Its value should be same as that of CreateFundTransferOrderV1Request
  bytes orchestration_metadata = 2 [(validate.rules).bytes.max_len = 1000];

  // integration_specs will be used to identify the vendor
  // and the mode of integration with the vendor, it could be MOBILE_SDK, SERVER_API, etc.
  // Its value should be same as that of CreateFundTransferOrderV1Request
  IntegrationSpecs integration_specs = 3;

  // [Optional] recurring payment id which will only be non empty for a
  // recurring payment i.e if this is populated, it can be assumed that
  // the RPC call is for a recurring payment.
  // Its value should be same as that of CreateFundTransferOrderV1Request
  string recurring_payment_id = 4;

  // [Optional] order id which will only be non empty for one time payment
  // i.e if this is populated, it can be assumed that
  // the RPC call is for a one-time payment case.
  // Its value should be same as that of CreateFundTransferOrderResponse/CreateFundTransferOrderV1Response,
  // if the order id is present with the client, otherwise empty. Passing this as non-empty value will signal
  // the RPC that an order is created in the flow, and the logic in the RPC will try to take the order to terminal
  // state for payment cancellation, initiation failure, etc.
  string order_id = 5;

  // entry point as part of which the payment options screen's flow is invoked.
  // Basis this, the appropriate domain service RPC will be invoked to signal payment completion/failure/cancellation by
  // evaluating the value of this field.
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 6;

  // PaymentOptionsActionType denotes the actions corresponding to a user's action on the payment options screen,
  // that the client sends to the backend to signal certain actions/intent that the user has performed in the payment
  // options flow.
  PaymentOptionsActionType action_type = 7;

  // [Optional] PgClientSdkResponsePayload can be used by the client to propagate any additional payload returned by the
  // payment-gateway client SDK (if any) to the backend so that the backend is notified of any client SDK statuses.
  oneof PgClientSdkResponsePayload {
    // Denotes the payload returned by the razorpay client SDK on successful completion of the payment flow.
    RazorpaySuccessPayload razorpay_client_success_payload = 101;

    // Denotes the payload returned by the razorpay client SDK on payment initiation failure.
    RazorpayFailurePayload razorpay_client_failure_payload = 102;
  }
}

message RazorpaySuccessPayload {
  string razorpay_payment_id = 1;
  string razorpay_order_id = 2;
  string razorpay_signature = 3;
}

message RazorpayFailurePayload {
  string http_status_code = 1;
  RazorpayClientSdkError error = 2;
}

message RazorpayClientSdkError {
  string code = 1;
  string description = 2;
  string source = 3;
  string step = 4;
  string reason = 5;
  string field = 6;
}

message MarkPostPaymentResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // [Optional] screen to navigate to.
  // If not present, client should fallback to the default logic as documented in
  // https://docs.google.com/document/d/1zNK649_Mup40khDE8Yc00MTvOj1-hPNqx9kdY4bZDgs/edit?tab=t.0#heading=h.well19av5iyn
  frontend.deeplink.Deeplink next_action = 2;
}
