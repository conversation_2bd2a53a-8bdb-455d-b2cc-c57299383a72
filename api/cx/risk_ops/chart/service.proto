syntax = "proto3";

package cx.risk_ops.chart;

import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/cx/risk_ops/chart/chart.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/risk_ops/chart";
option java_package = "com.github.epifi.gamma.api.cx.risk_ops.chart";

service ChartService {
  // GetCharts returns list of default backend driven charts to be displayed for the given case
  rpc GetCharts(GetChartsRequest) returns (GetChartsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetChartsRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  string case_id = 2 [(validate.rules).string.min_len = 1];
}

message GetChartsResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 1;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
  }

  rpc.Status status = 1;

  repeated Chart Charts = 2;
}
