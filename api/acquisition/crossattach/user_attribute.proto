syntax = "proto3";

package acquisition.crossattach;

import "api/insights/networth/service.proto";
import "api/preapprovedloan/lendability/enum.proto";
import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/acquisition/crossattach";
option java_package = "com.github.epifi.gamma.api.acquisition.crossattach";

// Enum to identify type of user attribute
// Each type defined below is mapped to a value which is used for cross attach
enum UserAttribute {
    USER_ATTRIBUTE_UNSPECIFIED = 0;
    USER_ATTRIBUTE_SCREENER_CHECK_RESULT = 1;
    USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY = 2;
    // Probability of default category
    USER_ATTRIBUTE_PD_CATEGORY = 3;
    USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP = 4;
    USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP = 5;
    USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST = 7;
}

// UserAttributeValue is used to store value of user attribute. Each value maps to one user attribute type.
message UserAttributeValue {
    message NetWorthAssetValueList {
        repeated insights.networth.AssetValue net_worth_asset_values = 1;
    }
    oneof value {
        // Maps to USER_ATTRIBUTE_SCREENER_STATUS
        bool screener_check_result = 1;
        // Maps to USER_ATTRIBUTE_LOAN_AFFINITY_CATEGORY
        lendability.LoanAffinityCategory loan_affinity_category = 2;
        // Maps to USER_ATTRIBUTE_PD_CATEGORY
        lendability.PDCategory pd_category = 3;
        // Maps to USER_ATTRIBUTE_HOME_LANDED_TIMESTAMP
        google.protobuf.Timestamp home_landed_timestamp = 4;
        // Maps to USER_ATTRIBUTE_LATEST_ASSET_CONNECTED_TIMESTAMP
        google.protobuf.Timestamp latest_asset_connected_timestamp = 5;
        // Maps to USER_ATTRIBUTE_WEALTH_BUILDER_CONNECTED
        bool wealth_builder_connected = 6;
        // Maps to USER_ATTRIBUTE_NET_WORTH_ASSET_VALUES_LIST
        NetWorthAssetValueList net_worth_asset_value_list = 7;
    }
}

