syntax = "proto3";

package api.typesv2.tiering;

option go_package = "github.com/epifi/gamma/api/typesv2/tiering";
option java_package = "com.github.epifi.gamma.api.typesv2.tiering";

message TieringScreenMetaData {
  oneof metadata {
    NotificationLandingScreenMetadata notification_landing_screen_metadata = 1;
    DetailedBenefitsScreenMetadata detailed_benefits_screen_metadata = 2;
    CashbackDetailsBottomSheet cashback_details_bottom_sheet = 3;
    PlansV2Metadata plans_v2_metadata = 4;
    SuccessV2MetaData success_v2_metadata = 5;
  }
}

// Metadata to mark, which plan success screen got displayed
message SuccessV2MetaData {
  // tiering.external.Tier.String()
  string tier = 1;
}

message PlansV2Metadata {
  string tier_to_focus = 1;
}

// metadata will have the tier and the criteria option to pitch to user for upgrading
// eg: https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
message NotificationLandingScreenMetadata {
  // tiering.external.Tier.String()
  string tier = 1;
  // tiering.enums.CriteriaOptionType.String()
  string criteria_option = 2;
}

// bottom sheet for detailed benefits which will be displayed on the tier all plans v2 screen
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10043-11580&t=CutvBGxUGPg2XuAk-4
message DetailedBenefitsScreenMetadata {
  // tiering.external.Tier.String()
  string tier = 1;
}

// bottom sheet for cashback details which will be displayed on the tier all plans v2 screen
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10088-2968&t=CutvBGxUGPg2XuAk-4
message CashbackDetailsBottomSheet {
  // tiering.external.Tier.String()
  string tier = 1;
}
