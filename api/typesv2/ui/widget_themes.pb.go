// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/widget_themes.proto

// Please start using typesv2.ui.widget package since this causes cyclic dependency issues

package ui

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Deprecated: Marked as deprecated in api/typesv2/ui/widget_themes.proto.
type BackgroundColour struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Colour:
	//	*BackgroundColour_RadialGradient
	//	*BackgroundColour_BlockColour
	Colour isBackgroundColour_Colour `protobuf_oneof:"colour"`
}

func (x *BackgroundColour) Reset() {
	*x = BackgroundColour{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BackgroundColour) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackgroundColour) ProtoMessage() {}

func (x *BackgroundColour) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackgroundColour.ProtoReflect.Descriptor instead.
func (*BackgroundColour) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_widget_themes_proto_rawDescGZIP(), []int{0}
}

func (m *BackgroundColour) GetColour() isBackgroundColour_Colour {
	if m != nil {
		return m.Colour
	}
	return nil
}

func (x *BackgroundColour) GetRadialGradient() *RadialGradient {
	if x, ok := x.GetColour().(*BackgroundColour_RadialGradient); ok {
		return x.RadialGradient
	}
	return nil
}

func (x *BackgroundColour) GetBlockColour() string {
	if x, ok := x.GetColour().(*BackgroundColour_BlockColour); ok {
		return x.BlockColour
	}
	return ""
}

type isBackgroundColour_Colour interface {
	isBackgroundColour_Colour()
}

type BackgroundColour_RadialGradient struct {
	// Radial gradient - the colour of the component changes radially
	RadialGradient *RadialGradient `protobuf:"bytes,1,opt,name=radial_gradient,json=radialGradient,proto3,oneof"`
}

type BackgroundColour_BlockColour struct {
	// Block colour - single colour to be used for the background
	BlockColour string `protobuf:"bytes,2,opt,name=block_colour,json=blockColour,proto3,oneof"`
}

func (*BackgroundColour_RadialGradient) isBackgroundColour_Colour() {}

func (*BackgroundColour_BlockColour) isBackgroundColour_Colour() {}

//
//Radial gradient:
//The colour varies in the component radially. Eg https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=1137%3A22747.
//A gradient that might seem to be varying from left to the right, can be perceived as a radial gradient with a relatively large radius.
//Using the centre, radius and an array of colours the client can render the radial background for the component.
//RadialGradient can be defined using center, radius, and an array of colours equally spaced along the radius from the center.
//We will assume that the gradients are circular.
type RadialGradient struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Center of the radial gradient
	Center *CenterCoordinates `protobuf:"bytes,1,opt,name=center,proto3" json:"center,omitempty"`
	// Radius of the gradiant
	OuterRadius int32 `protobuf:"varint,2,opt,name=outer_radius,json=outerRadius,proto3" json:"outer_radius,omitempty"`
	// Sequence of equally spaced colours to render for the gradiant
	Colours []string `protobuf:"bytes,3,rep,name=colours,proto3" json:"colours,omitempty"`
}

func (x *RadialGradient) Reset() {
	*x = RadialGradient{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RadialGradient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RadialGradient) ProtoMessage() {}

func (x *RadialGradient) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RadialGradient.ProtoReflect.Descriptor instead.
func (*RadialGradient) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_widget_themes_proto_rawDescGZIP(), []int{1}
}

func (x *RadialGradient) GetCenter() *CenterCoordinates {
	if x != nil {
		return x.Center
	}
	return nil
}

func (x *RadialGradient) GetOuterRadius() int32 {
	if x != nil {
		return x.OuterRadius
	}
	return 0
}

func (x *RadialGradient) GetColours() []string {
	if x != nil {
		return x.Colours
	}
	return nil
}

// center of gradient - x and y coordinates
type CenterCoordinates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// x coordinate
	CenterX int32 `protobuf:"varint,1,opt,name=center_x,json=centerX,proto3" json:"center_x,omitempty"`
	// y coordinate
	CenterY int32 `protobuf:"varint,2,opt,name=center_y,json=centerY,proto3" json:"center_y,omitempty"`
}

func (x *CenterCoordinates) Reset() {
	*x = CenterCoordinates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CenterCoordinates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CenterCoordinates) ProtoMessage() {}

func (x *CenterCoordinates) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CenterCoordinates.ProtoReflect.Descriptor instead.
func (*CenterCoordinates) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_widget_themes_proto_rawDescGZIP(), []int{2}
}

func (x *CenterCoordinates) GetCenterX() int32 {
	if x != nil {
		return x.CenterX
	}
	return 0
}

func (x *CenterCoordinates) GetCenterY() int32 {
	if x != nil {
		return x.CenterY
	}
	return 0
}

//
//Some widget components can have a shadow effect - https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A23705
type Shadow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Height of the shadow
	Height int32 `protobuf:"varint,1,opt,name=height,proto3" json:"height,omitempty"`
	// Blur parameter
	Blur int32 `protobuf:"varint,2,opt,name=blur,proto3" json:"blur,omitempty"`
	// opacity of shadow component. Value denotes percentage and ranges between 0-100
	Opacity int32 `protobuf:"varint,4,opt,name=opacity,proto3" json:"opacity,omitempty"`
	// Background colour for the shadow
	Colour *BackgroundColour `protobuf:"bytes,3,opt,name=colour,proto3" json:"colour,omitempty"`
}

func (x *Shadow) Reset() {
	*x = Shadow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Shadow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Shadow) ProtoMessage() {}

func (x *Shadow) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_widget_themes_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Shadow.ProtoReflect.Descriptor instead.
func (*Shadow) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_widget_themes_proto_rawDescGZIP(), []int{3}
}

func (x *Shadow) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Shadow) GetBlur() int32 {
	if x != nil {
		return x.Blur
	}
	return 0
}

func (x *Shadow) GetOpacity() int32 {
	if x != nil {
		return x.Opacity
	}
	return 0
}

func (x *Shadow) GetColour() *BackgroundColour {
	if x != nil {
		return x.Colour
	}
	return nil
}

var File_api_typesv2_ui_widget_themes_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_widget_themes_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x22, 0x90, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x49, 0x0a, 0x0f, 0x72, 0x61, 0x64,
	0x69, 0x61, 0x6c, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x52, 0x61, 0x64, 0x69, 0x61, 0x6c, 0x47, 0x72, 0x61, 0x64, 0x69, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x61, 0x64, 0x69, 0x61, 0x6c, 0x47, 0x72, 0x61, 0x64,
	0x69, 0x65, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x3a, 0x02, 0x18, 0x01, 0x42, 0x08, 0x0a,
	0x06, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0x88, 0x01, 0x0a, 0x0e, 0x52, 0x61, 0x64, 0x69,
	0x61, 0x6c, 0x47, 0x72, 0x61, 0x64, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x06, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x43, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x52, 0x06, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x5f, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x75, 0x74,
	0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x73, 0x22, 0x49, 0x0a, 0x11, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x5f, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x58, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x59, 0x22, 0x88, 0x01,
	0x0a, 0x06, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x6c, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x62, 0x6c, 0x75, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x38,
	0x0a, 0x06, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x06, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_widget_themes_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_widget_themes_proto_rawDescData = file_api_typesv2_ui_widget_themes_proto_rawDesc
)

func file_api_typesv2_ui_widget_themes_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_widget_themes_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_widget_themes_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_widget_themes_proto_rawDescData)
	})
	return file_api_typesv2_ui_widget_themes_proto_rawDescData
}

var file_api_typesv2_ui_widget_themes_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_typesv2_ui_widget_themes_proto_goTypes = []interface{}{
	(*BackgroundColour)(nil),  // 0: api.typesv2.ui.BackgroundColour
	(*RadialGradient)(nil),    // 1: api.typesv2.ui.RadialGradient
	(*CenterCoordinates)(nil), // 2: api.typesv2.ui.CenterCoordinates
	(*Shadow)(nil),            // 3: api.typesv2.ui.Shadow
}
var file_api_typesv2_ui_widget_themes_proto_depIdxs = []int32{
	1, // 0: api.typesv2.ui.BackgroundColour.radial_gradient:type_name -> api.typesv2.ui.RadialGradient
	2, // 1: api.typesv2.ui.RadialGradient.center:type_name -> api.typesv2.ui.CenterCoordinates
	0, // 2: api.typesv2.ui.Shadow.colour:type_name -> api.typesv2.ui.BackgroundColour
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_widget_themes_proto_init() }
func file_api_typesv2_ui_widget_themes_proto_init() {
	if File_api_typesv2_ui_widget_themes_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_widget_themes_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BackgroundColour); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_widget_themes_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RadialGradient); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_widget_themes_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CenterCoordinates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_widget_themes_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Shadow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_ui_widget_themes_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*BackgroundColour_RadialGradient)(nil),
		(*BackgroundColour_BlockColour)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_widget_themes_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_widget_themes_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_widget_themes_proto_depIdxs,
		MessageInfos:      file_api_typesv2_ui_widget_themes_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_widget_themes_proto = out.File
	file_api_typesv2_ui_widget_themes_proto_rawDesc = nil
	file_api_typesv2_ui_widget_themes_proto_goTypes = nil
	file_api_typesv2_ui_widget_themes_proto_depIdxs = nil
}
