package deeplink_builder

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	feWealthAnalyserPb "github.com/epifi/gamma/api/insights/secrets/frontend"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	deeplinkSecrets "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	"github.com/epifi/gamma/api/user/onboarding"

	"context"
	"fmt"

	"go.uber.org/zap"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/insights"
	investmentUi "github.com/epifi/gamma/api/frontend/investment/ui"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	typesPb "github.com/epifi/gamma/api/typesv2"
	analyserDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/analyser"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"
	epfScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/epf"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/manual_asset_form"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	mfScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/mutualfund"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	manualAssetPkg "github.com/epifi/gamma/pkg/networth"
)

func GetOtpScreenOptionsForSendingOtp(uanNumber, clientReqId, epfSessionId string, isConsentTaken bool) *anyPb.Any {
	return deeplinkv3.GetScreenOptionV2WithoutError(&epfScreenOptions.EpfPassbookImportOtpScreenOptions{
		UanNumber: uanNumber,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Verify with OTP",
			},
		},
		OtpStageMessage: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Sending an OTP to your mobile number registered with EPFO",
			},
		},
		ClientReqId:        clientReqId,
		IsConsentTaken:     isConsentTaken,
		EpfImportSessionId: epfSessionId,
	})
}

func GetNetworthHubScreenDeeplink() (*deeplinkPb.Deeplink, error) {
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&networth.NetWorthHubScreenOptions{
		LoadingNessage: commontypes.GetTextFromStringFontColourFontStyle("Taking you to Net Worth", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert NetWorthHubScreenOptions to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_NET_WORTH_HUB_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

func IsWealthAnalyserUser(featureDetails map[string]*onboarding.FeatureInfo) bool {
	isWealthAnalyserUser := false
	if _, ok := featureDetails[onboarding.Feature_FEATURE_WEALTH_ANALYSER.String()]; ok {
		featureStatus := featureDetails[onboarding.Feature_FEATURE_WEALTH_ANALYSER.String()].GetFeatureStatus()
		if featureStatus == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE ||
			featureStatus == onboarding.FeatureStatus_FEATURE_STATUS_INACTIVE {
			isWealthAnalyserUser = true
		}
	}
	return isWealthAnalyserUser
}

func GetNetworthHubScreenDeeplinkWithoutError() *deeplinkPb.Deeplink {
	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_NET_WORTH_HUB_SCREEN, &networth.NetWorthHubScreenOptions{
		LoadingNessage: &commontypes.Text{
			FontColor: "#F6F9FD",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Taking you to Net Worth",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
			},
		},
	})
}

func GetWealthBuilderDashboardDeeplink() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEALTH_BUILDER_LANDING_SCREEN,
	}
}

func InitMfHoldingScreenDeeplinkForRefresh(externalId string, exitDeeplink *deeplinkPb.Deeplink, provenance mfExternalPb.Provenance) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MF_HOLDINGS_IMPORT_INITIATE_SCREEN_V2,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&mfScreenOptions.MFHoldingsImportInitiateScreenOptions{
			ExternalId:   externalId,
			ExitDeeplink: exitDeeplink,
			Provenance:   provenance.String(),
			FlowType:     mfExternalPb.FlowType_FLOW_TYPE_MF_HOLDINGS_REFRESH.String(),
		}),
	}
}

// GetEpfUanListScreenDeeplink get the deeplink for uan dashboard
func GetEpfUanListScreenDeeplink(epfImportSessionId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&epfScreenOptions.EpfPassbookImportUanListScreenOptions{
			EpfImportSessionId: epfImportSessionId,
		}),
	}
}

func EpfDashboardRefreshAllDeeplink(epfImportSessionId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&epfScreenOptions.EpfPassbookImportUanListScreenOptions{
			Source:             insights.UanListScreenSource_UAN_LIST_SCREEN_SOURCE_REFRESH_ALL,
			EpfImportSessionId: epfImportSessionId,
		}),
	}
}

func ManualAssetDashboardDeeplink(ctx context.Context, assetType networthBePb.AssetType, releaseEvaluator release.IEvaluator, actorId string) *deeplinkPb.Deeplink {
	isAssetLandingPageEnabled, err := releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_ASSET_LANDING_PAGE_FOR_MANUAL_ASSET).WithActorId(actorId))
	instrumentType := typesPb.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED
	for k, v := range manualAssetPkg.ManualAssetsInstrumentTypeToAssetTypeMap {
		if v == assetType {
			instrumentType = k
		}
	}
	// Handle errors gracefully by displaying the previous screen instead of introducing breaking changes.
	if err == nil && isAssetLandingPageEnabled && instrumentType != typesPb.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED &&
		// TODO: remove esops ignore check from here post its asset v2 handling
		instrumentType != typesPb.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_ASSET_LANDING_PAGE,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&assetandanalysis.AssetLandingPageScreenOptions{
				Params: &investmentUi.LandingPageRequestParams{
					InstrumentType: instrumentType.String(),
				},
			}),
		}
	}
	if err != nil {
		logger.Error(ctx, "error while getting feature flag", zap.Error(err))
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MANUAL_ASSET_DASHBOARD_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&manual_asset_form.ManualAssetDashboardScreenOptions{
			AssetType: assetType.String(),
		}),
	}
}

func InitCreditReportDownloadScreen(externalId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&analyserDlOptions.InitiateCreditReportDownloadScreenOptions{
			ExternalId: externalId,
		}),
	}
}

func ManualAssetNewFormDeeplink(assetType networthBePb.AssetType) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MANUAL_ASSET_FORM_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&manual_asset_form.ManualAssetFormScreenOptions{
			FormIdentifier: &typesPb.ManualAssetFormIdentifier{
				Identifier: &typesPb.ManualAssetFormIdentifier_AssetType{
					AssetType: assetType.String(),
				},
			},
		}),
	}
}

func ManualAssetPrefilledFormDeeplink(externalId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MANUAL_ASSET_FORM_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&manual_asset_form.ManualAssetFormScreenOptions{
			FormIdentifier: &typesPb.ManualAssetFormIdentifier{
				Identifier: &typesPb.ManualAssetFormIdentifier_ExternalId{
					ExternalId: externalId,
				},
			},
		}),
	}
}

func WealthAnalyserReportDeeplink(reportType feWealthAnalyserPb.WealthAnalyserReportType) *deeplinkPb.Deeplink {
	return deeplinkv3.GetDeeplinkV3WithoutError(
		deeplinkPb.Screen_WEALTH_ANALYSER_REPORT_SCREEN,
		&deeplinkSecrets.WealthAnalyserReportScreenOptions{ReportType: reportType.String()})
}
