//go:generate gen_sql -types=UserLead,PersonalDetails,AdditionalDetails
syntax = "proto3";

package leads;

import "api/leads/enums.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/address.proto";
import "google/type/date.proto";
import "api/typesv2/employment_type.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/leads";
option java_package = "com.github.epifi.gamma.api.leads";

message UserLead {
  string id = 1;
  string actor_id = 2;
  string client_request_id = 3;
  ProductType product_type = 4;
  string client_id = 5;
  string pan = 6;
  string mobile_number = 7;
  string email = 8;
  PersonalDetails personal_details = 9;
  AdditionalDetails additional_details = 10;
  UserLeadStatus lead_status = 11;
  google.protobuf.Timestamp completed_at = 12;
  google.protobuf.Timestamp expired_at = 13;
  google.protobuf.Timestamp created_at = 14;
  google.protobuf.Timestamp updated_at = 15;
}

// PersonalDetails contains personal details of the user
message PersonalDetails {
  api.typesv2.PostalAddress current_address = 1;
  api.typesv2.common.Name name = 2;
  EmploymentDetails employment_details = 3;
  google.type.Date dob = 4;
}

// AdditionalDetails contains product specific details
// Whenever we are adding a new product, we can add
// their specific details in this oneof
message AdditionalDetails {
  oneof details {
    FiPersonalLoanDetails fi_personal_loan_details = 1;
  }
}

message FiPersonalLoanDetails {
  LoanRequirement loan_requirement = 1;
  // type of evaluation performed for the lead before creating it in the system
  FiLoansEvaluationType evaluation_type = 2;
}

message EmploymentDetails {
  api.typesv2.EmploymentType employment_type = 1;
  google.type.Money monthly_income = 2;
}

message LoanRequirement {
  google.type.Money desired_loan_amount = 1;
}

message FiLoansLeadResponse {
  // list of available lender types for the user to apply on Fi app
  // populated only if evaluation type in request is BASIC and lead is accepted
  repeated LenderType available_lender_types = 1;
}
