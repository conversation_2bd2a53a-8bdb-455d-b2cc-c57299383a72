// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=<PERSON><PERSON><PERSON>,LoanRequestType,LoanRequestStatus,LoanRequestSubStatus,LoanAccountStatus,LoanType,LoanRequestFieldMask,LoanAccountFieldMask,LoanOfferFieldMask,LoanStepExecutionStatus,LoanStepExecutionSubStatus,LoanStepExecutionFieldMask,LoanStepExecutionFlow,LoanStepExecutionStepName,LoanOfferEligibilityCriteriaFieldMask,LoanOfferEligibilityCriteriaStatus,LoanOfferEligibilityCriteriaSubStatus,LoanActivityType,LoanActivityFieldMask,LoanPaymentRequestType,LoanPaymentRequestStatus,LoanPaymentRequestSubStatus,LoanInstallmentInfoStatus,LoanInstallmentInfoFieldMask,LoanInstallmentPayoutStatus,LoanInstallmentPayoutFieldMask,LoanApplicationStatus,LoanApplicationSubStatus,LoanProgram,LoanApplicantFieldMask,LoanApplicantSubStatus,LoanApplicantStatus,GroupStage,LoanProgram,AssetType,FetchedAssetFieldMask,IdfcCkycAddressPinCodeType,OtpType,OtpStatus,MandateRequestFieldMask,MandateRequestStatus,DataRequirementType,LoanPaymentAccountType,LoanOfferType,PreEligibilityOfferFieldMask,Provenance
syntax = "proto3";

package preapprovedloan;

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

enum Vendor {
  VENDOR_UNSPECIFIED = 0;
  FEDERAL = 1;
  LIQUILOANS = 2;
  IDFC = 3;
  FIFTYFIN = 4;
  ABFL = 5;
  EPIFI_TECH = 6;
  MONEYVIEW = 7;
  VENDOR_MF_CENTRAL = 8;
  STOCK_GUARDIAN_LSP = 9;
  LENDEN = 10;
}

enum LoanRequestType {
  LOAN_REQUEST_TYPE_UNSPECIFIED = 0;
  LOAN_REQUEST_TYPE_CREATION = 1; // Represents LoanRequests in Creation Flow (Offer, Application)
  LOAN_REQUEST_TYPE_CLOSURE = 2; // Represents LoanRequests in Closure Flow (Closure, Enquiry)
  LOAN_REQUEST_TYPE_ELIGIBILITY = 3;
  LOAN_REQUEST_TYPE_NEW_PORTFOLIO_FETCH = 4;
  LOAN_REQUEST_TYPE_REFRESH_PORTFOLIO = 5;
  LOAN_REQUEST_TYPE_UPDATE_USER = 6;
  LOAN_REQUEST_TYPE_SETUP_SI = 7;
  // This type of loan request is used for non-financial updates required in mutual fund folios, such as updating phone number and email address associated with the folios.
  LOAN_REQUEST_TYPE_MUTUAL_FUND_NFT = 8;
  LOAN_REQUEST_TYPE_PRE_ELIGIBILITY = 9;
}

enum LoanRequestStatus {
  LOAN_REQUEST_STATUS_UNSPECIFIED = 0;

  // Loan request is created in the database and is ready to be processed, i.e. ready for eligibility checks, etc.
  LOAN_REQUEST_STATUS_CREATED = 1;

  // This state is when workflow is blocked for verification through vkyc, liveness, manual review, ot OTP
  // SUBSTATUS: VKYC, LIVENESS, MANUAL REVIEW, OTP
  LOAN_REQUEST_STATUS_PENDING = 2;

  // Failed verification state (not terminal)
  // SUBSTATUS: FAILED_OTP, FAILED_OTP_LAST_ATTEMPT, FAILED_KYC, etc.
  LOAN_REQUEST_STATUS_VERIFICATION_FAILED = 3;

  // After OTP is verified successfully
  // SUBSTATUS: OTP, LIVENESS, KYC, etc.
  LOAN_REQUEST_STATUS_VERIFIED = 4;

  // This is the state when loan application request has been raised with vendor.
  // Sub statuses will define at what stage of initialisation are we at.
  LOAN_REQUEST_STATUS_INITIATED = 5;

  // Terminal failure states
  // Loan Application is permanently marked failed due to failure at vendor or permanent failure at any of the previous
  // steps such as OTP_MAX_ATTEMPT_FAILURE, MANUAL_REVIEW_FAILURE, etc.
  LOAN_REQUEST_STATUS_FAILED = 6;

  // LOAN APPLICATION SUCCESS STATE
  // Money is disbursed from vendor and is verified in user's account.
  LOAN_REQUEST_STATUS_SUCCESS = 7;

  // LOAN APPLICATION DISBURSED STATE
  // Money is disbursed from vendor
  LOAN_REQUEST_STATUS_DISBURSED = 8;

  // Caused due to a technical issue such as API failure
  // or some internal system is down. retryable error.
  LOAN_REQUEST_STATUS_UNKNOWN = 9;

  // State when user cancels loan application
  LOAN_REQUEST_STATUS_CANCELLED = 10;

  // State when all retries have been exhausted and loan request required manual intervention
  LOAN_REQUEST_STATUS_MANUAL_INTERVENTION = 11;
}

enum LoanRequestSubStatus {
  LOAN_REQUEST_SUB_STATUS_UNSPECIFIED = 0;
  LOAN_REQUEST_SUB_STATUS_CREATED = 1;
  LOAN_REQUEST_SUB_STATUS_PENDING_VKYC = 2;
  LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS = 3;
  LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW = 4;
  LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP = 5;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP = 7;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_LIVENESS = 8;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_MANUAL_REVIEW = 9;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_VKYC = 10;
  LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED = 11;
  LOAN_REQUEST_SUB_STATUS_FAILED_LIVENESS = 12;
  LOAN_REQUEST_SUB_STATUS_FAILED_VKYC = 13;
  LOAN_REQUEST_SUB_STATUS_FAILED_MANUAL_REVIEW = 14;
  LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS = 15;
  LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS = 16;
  LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW_IN_PROGRESS = 17;
  LOAN_REQUEST_SUB_STATUS_INITIATED_IN_PROGRESS = 18;
  LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR = 19;
  LOAN_REQUEST_SUB_STATUS_SUCCESS = 20;
  LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH = 21;
  LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH_IN_PROGRESS = 22;
  LOAN_REQUEST_SUB_STATUS_FAILED_FACE_MATCH = 23;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_FACE_MATCH = 24;
  LOAN_REQUEST_SUB_STATUS_PENDING_OTP = 25;
  LOAN_REQUEST_SUB_STATUS_OTP_GENERATED = 26;
  LOAN_REQUEST_SUB_STATUS_CANCELLED = 27;
  LOAN_REQUEST_SUB_STATUS_PENDING_KFS = 28;
  LOAN_REQUEST_SUB_STATUS_PENDING_KFS_IN_PROGRESS = 29;
  LOAN_REQUEST_SUB_STATUS_FAILED_KFS = 30;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_KFS = 31;
  LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION = 32;
  LOAN_REQUEST_SUB_STATUS_VERIFIED_PROFILE_VALIDATION = 33;
  LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION = 34;
  LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_AFTER_VKYC = 35;
  LOAN_REQUEST_SUB_STATUS_PENDING_KFS_AT_BANK = 36;
  LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT = 37;
  LOAN_REQUEST_SUB_STATUS_FAILED_RISK_CHECK = 38;
  LOAN_REQUEST_SUB_STATUS_FAILED_PRE_CONDITION = 39;
  LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED = 40;
  LOAN_REQUEST_SUB_STATUS_EMAIL_ALREADY_USED = 41;
  LOAN_REQUEST_SUB_STATUS_PHONE_ALREADY_USED = 42;
  LOAN_REQUEST_SUB_STATUS_PORTFOLIO_NOT_FOUND = 43;
  LOAN_REQUEST_SUB_STATUS_ALL_OTPS_NOT_ENTERED = 44;
  LOAN_REQUEST_SUB_STATUS_LOW_PORTFOLIO = 45;
  LOAN_REQUEST_SUB_STATUS_EMAIL_SELECTION_FAILURE = 46;
  LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED = 47;
  LOAN_REQUEST_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED = 48;
  LOAN_REQUEST_SUB_STATUS_CAS_DETAILED_FETCH_FAILED = 49;
  LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_SUCCESS = 50;
  LOAN_REQUEST_SUB_STATUS_LOAN_APPLICATION_VERIFICATION_FAILED = 51;
}

enum LoanAccountStatus {
  LOAN_ACCOUNT_STATUS_UNSPECIFIED = 0;
  LOAN_ACCOUNT_STATUS_ACTIVE = 1;
  LOAN_ACCOUNT_STATUS_CLOSED = 2;
}

enum LoanType {
  // LoanType specifies the particular kind of loan product.
  // Ex: Personal loan, home loan, etc.
  // Please ensure that there is a one-to-one mapping between frontend &
  // backend loanType enum values, so that the conversion is automatically handled.
  LOAN_TYPE_UNSPECIFIED = 0;
  LOAN_TYPE_PERSONAL = 1;
  LOAN_TYPE_EARLY_SALARY = 2;
  LOAN_TYPE_SECURED_LOAN = 3;
}

enum LoanRequestFieldMask {
  LOAN_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_REQUEST_FIELD_MASK_OFFER_ID = 1;
  LOAN_REQUEST_FIELD_MASK_ORCH_ID = 2;
  LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER = 3;
  LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID = 4;
  LOAN_REQUEST_FIELD_MASK_VENDOR = 5;
  LOAN_REQUEST_FIELD_MASK_DETAILS = 6;
  LOAN_REQUEST_FIELD_MASK_TYPE = 7;
  LOAN_REQUEST_FIELD_MASK_STATUS = 8;
  LOAN_REQUEST_FIELD_MASK_SUB_STATUS = 9;
  LOAN_REQUEST_FIELD_MASK_COMPLETED_AT = 10;
  LOAN_REQUEST_FIELD_MASK_NEXT_ACTION = 11;
  LOAN_REQUEST_FIELD_MASK_LOAN_PROGRAM = 12;
}

enum LoanAccountFieldMask {
  LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_ACCOUNT_FIELD_MASK_LOAN_ACCOUNT_NUMBER = 1;
  LOAN_ACCOUNT_FIELD_MASK_LOAN_TYPE = 2;
  LOAN_ACCOUNT_FIELD_MASK_IFSC_CODE = 3;
  LOAN_ACCOUNT_FIELD_MASK_LOAN_AMOUNT = 4;
  LOAN_ACCOUNT_FIELD_MASK_DISBURSED_AMOUNT = 5;
  LOAN_ACCOUNT_FIELD_MASK_OUTSTANDING_AMOUNT = 6;
  LOAN_ACCOUNT_FIELD_MASK_TOTAL_PAYABLE_AMOUNT = 7;
  LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE = 8;
  LOAN_ACCOUNT_FIELD_MASK_MATURITY_DATE = 9;
  LOAN_ACCOUNT_FIELD_MASK_VENDOR = 10;
  LOAN_ACCOUNT_FIELD_MASK_DETAILS = 11;
  LOAN_ACCOUNT_FIELD_MASK_STATUS = 12;
  LOAN_ACCOUNT_FIELD_MASK_LMS_PARTNER_LOAN_ID = 13;
}

enum LoanOfferFieldMask {
  LOAN_OFFER_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID = 1;
  LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS = 2;
  // deprecated since an offer (by design) is an immutable entity and therefore its configuration shouldn't be updated once the offer is created.
  // If anything needs to be changed in the offer config, then the existing offer should be marked as deactivated and
  // a new instance of the offer should be created for auditing purposes
  LOAN_OFFER_FIELD_MASK_PRECESSING_INFO = 3 [deprecated = true];
  LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT = 4;
  LOAN_OFFER_FIELD_MASK_UPDATED_AT = 5;
  LOAN_OFFER_FIELD_MASK_LOAN_OFFER_ELIGIBILITY_CRITERIA_ID = 6;
  LOAN_OFFER_FIELD_MASK_LOAN_PROGRAM = 7;
  LOAN_OFFER_FIELD_MASK_LAST_VIEWED_AT = 8;
}

enum LoanApplicantFieldMask {
  LOAN_APPLICANT_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_APPLICANT_FIELD_MASK_STATUS = 1;
  LOAN_APPLICANT_FIELD_MASK_SUB_STATUS = 2;
  LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID = 3;
  LOAN_APPLICANT_FIELD_MASK_VENDOR_REQUEST_ID = 4;
  LOAN_APPLICANT_FIELD_MASK_EMPLOYMENT_DETAILS = 5;
  LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS = 6;
  LOAN_APPLICANT_FIELD_MASK_LOAN_APPLICANT_DETAILS = 7;
}

enum LoanStepExecutionStatus {
  LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED = 0;
  LOAN_STEP_EXECUTION_STATUS_CREATED = 1;
  LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS = 2;
  LOAN_STEP_EXECUTION_STATUS_FAILED = 3;
  LOAN_STEP_EXECUTION_STATUS_SUCCESS = 4;
  LOAN_STEP_EXECUTION_STATUS_CANCELLED = 5;
  LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION = 6;
  // LSE will be maked in EXPIRED status when stage gets failed due to retry exhaustion/ timed out on waiting for user action.
  LOAN_STEP_EXECUTION_STATUS_EXPIRED = 7;
}

enum LoanStepExecutionSubStatus {
  LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED = 0;
  LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_CREATED = 1;
  LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED = 2;
  LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED = 3;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED = 4;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW = 5;
  LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_OTP_FAILED = 6;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR = 7;
  LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED = 8;
  LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_STATUS_FETCHED = 9;
  LOAN_STEP_EXECUTION_SUB_STATUS_PAN_VERIFIED = 10;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED = 11;
  LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED = 12;
  LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED = 13;
  LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED = 14;
  LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD = 15 [deprecated = true];
  LOAN_STEP_EXECUTION_SUB_STATUS_RISKY_USER_CHECK = 16;
  LOAN_STEP_EXECUTION_SUB_STATUS_RISK_CREDIT_CHECK = 17;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_USER_BALANCE_IS_ZERO = 18;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_EXECUTED = 19;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED = 20;
  LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND = 21;
  LOAN_STEP_EXECUTION_SUB_STATUS_CREDIT_REPORT_FETCH_INITIATED = 22;
  LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_EXPIRED = 23;
  LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_LINKED = 24;
  LOAN_STEP_EXECUTION_SUB_STATUS_PAN_AADHAAR_NOT_LINKED = 25;
  LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFIED = 26;
  LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_INITIATED = 27;
  LOAN_STEP_EXECUTION_SUB_STATUS_BANK_LINKED = 28;
  LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED = 29;
  LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_SIGNED = 30;
  LOAN_STEP_EXECUTION_SUB_STATUS_MISSING_BANK_DETAILS = 31;
  LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE = 32;
  LOAN_STEP_EXECUTION_SUB_STATUS_NO_LIEN_MARKED_PORTFOLIO = 33;
  LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LIEN_MARKED_PORTFOLIO = 34;
  LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATED_SUCCESSFULLY = 35;
  // can be used when waiting on any data pull or polling an api
  LOAN_STEP_EXECUTION_SUB_STATUS_WAITING_FOR_DATA = 36;
  LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_MAKER_CHECK_APPROVED = 37;
  LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_VERIFICATION_FAILED = 38;
  // we were not able to mark lien on entire portfolio but the marked portfolio is enough to
  // disburse the requested loan amount.
  LOAN_STEP_EXECUTION_SUB_STATUS_PARTIAL_LIEN_MARKED = 39;
  LOAN_STEP_EXECUTION_SUB_STATUS_NO_PORTFOLIO_FOUND = 40;
  LOAN_STEP_EXECUTION_SUB_STATUS_LOW_PORTFOLIO_VALUE = 41;
  LOAN_STEP_EXECUTION_SUB_STATUS_ALL_OTPS_NOT_ENTERED = 42;
  LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_EXPIRED = 43;
  LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED = 44;
  LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED = 45;
  LOAN_STEP_EXECUTION_SUB_STATUS_KYC_REJECTED = 46;
  LOAN_STEP_EXECUTION_SUB_STATUS_KYC_EXPIRED = 47;
  // applicable for LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION
  // accounts connected but at least one of the accounts is in DATA_SYNC_PENDING status
  // we cannot estimate income from accounts in this status, we have to wait until status changes to DATA_SYNC_ON
  LOAN_STEP_EXECUTION_SUB_STATUS_CA_DATA_SYNC_PENDING = 48;
  LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED = 49;
  // user gets rejected at vendor's end as user details (PAN, phnNum etc..) already registered with vendor against some other user
  LOAN_STEP_EXECUTION_SUB_STATUS_DEDUPE_CHECK_FAILURE = 50;
  // user got rejected during BRE checks run at the vendor's end
  LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION = 51;
  LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN = 52;
  LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE = 53;
  LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED = 54;
  LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS = 55;
  LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT = 56;
  LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED = 57;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED = 58;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE = 59;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_AUTHENTICATION_FAILED = 60;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_VALIDATION_FAILED = 61;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PAN_AND_KYC_MISMATCH = 62;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_CKYC_ACCOUNT_TYPE = 63;
  // sub status to be used when user is out of india
  // example usage: while doing IDFC VKYC, we set this sub-status when the user is out of india
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_OUT_OF_INDIA = 64;

  // applicable for LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION
  LOAN_STEP_EXECUTION_SUB_STATUS_CONNECTED_ACCOUNTS_NOT_FOUND = 65;
  LOAN_STEP_EXECUTION_SUB_STATUS_ALL_CONNECTED_ACCOUNTS_ARE_CURRENT_ACCOUNTS = 66;
  LOAN_STEP_EXECUTION_SUB_STATUS_STALE_CONNECTED_ACCOUNTS = 67;
  // valid cases where income could not be estimated using the existing connected accounts
  LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_CANNOT_BE_ESTIMATED = 68;
  LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_FAILED_WITH_UNKNOWN_REASON = 69;
  LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_ESTIMATOR_LOW_CONFIDENCE = 70;
  // user started connected account flow for income verification
  LOAN_STEP_EXECUTION_SUB_STATUS_CA_FLOW_STARTED = 71;
  LOAN_STEP_EXECUTION_SUB_STATUS_INSUFFICIENT_LOAN_AMOUNT_POST_FUND_VERIFICATION = 72;
  LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_AGE_CRITERIA_NOT_SATISFIED = 73;
  LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED = 74;
  LOAN_STEP_EXECUTION_SUB_STATUS_AADHAAR_DETAILS_ADDED = 75;
  // when ckyc record is not found for the user
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND = 76;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_SELECTED_BANK_NOT_FOUND = 77;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_MERCHANT_CODE_MISMATCH = 78;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_DUPLICATE_TXN_ID = 79;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_IMPS_NAME_EMPTY = 80;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_PAN_NAME_EMPTY = 81;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_NAME_MISMATCH = 82;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILURE_ACCOUNT_HOLDER_NAME_EMPTY = 83;
  // example usage: IDFCValidateOffer activity
  LOAN_STEP_EXECUTION_SUB_STATUS_PHOTO_MATCH_FAILED = 84;
  LOAN_STEP_EXECUTION_SUB_STATUS_EPFO_DATA_NOT_FOUND = 85;
  LOAN_STEP_EXECUTION_SUB_STATUS_APPLICATION_NOT_FOUND_AT_VENDOR = 86;
  LOAN_STEP_EXECUTION_SUB_STATUS_LEAD_CANCELLATION_FAILED = 87;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_NOT_FOUNT = 88;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_SI_REVOKED = 89;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_FAILED_AMOUNT_LESS_THAN_MINIMUM = 90;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DOB_MISMATCH = 91;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_MISMATCH = 92;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_MOBILE_NUMBER_DOES_NOT_EXIST_FOR_CKYC_NUMBER = 93;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_INVALID_VALUE_ENTERED_IN_AUTHENTICATION_FACTOR_TYPE = 94;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_IMAGES_CANNOT_BE_LOADED = 95;
  LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_FAILED = 96;
  LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_EMI_GREATER_THAN_MAX_EMI_ALLOWED = 97;
  LOAN_STEP_EXECUTION_SUB_STATUS_DRAWDOWN_AMOUNT_IS_NOT_WITHIN_THE_PRESCRIBED_LIMITS = 98;
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_ACTION_WAIT_TIME_OUT = 99;
  LOAN_STEP_EXECUTION_SUB_STATUS_RECURRING_PAYMENT_MANDATE_REVOKED = 100;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_MAX_ATTEMPTS_REACHED = 101;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_FAILED_FROM_VENDOR = 102;
  // denotes that mandate init deeplink is generated for user to take action and setup auto-pay
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_DEEPLINK_GENERATED = 103;
  LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_LOAN_APPLICATION_EXPIRED = 104;
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_REJECTED_KFS = 105;
  LOAN_STEP_EXECUTION_SUB_STATUS_ITR_INTIMATION_STARTED = 106;
  LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK = 107;
  LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL = 108;
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_NOT_ENTERED = 109;
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_VERIFICATION_FAILED = 110;
  LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_NOT_ENTERED = 111;
  LOAN_STEP_EXECUTION_SUB_STATUS_UPDATE_DETAILS_VERIFICATION_OTP_FAILED = 112;
  // example usage: if we get this error from LL "Monthly income should be between Rs 2,000 to Rs 1,00,00,000"
  // we can fail the loan application with this sub-status
  LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_NOT_IN_ACCEPTABLE_RANGE_FOR_VENDOR = 113;
  LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_EMAIL_FETCH_FAILED = 114;
  LOAN_STEP_EXECUTION_SUB_STATUS_CAS_SUMMARY_PHONE_FETCH_FAILED = 115;
  LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_WITHDRAWN = 116;
  LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_INITIATION_FAILED = 117;
  LOAN_STEP_EXECUTION_SUB_STATUS_DISBURSAL_TIMED_OUT = 118;
  LOAN_STEP_EXECUTION_SUB_STATUS_STUCK_IN_READY_TO_DISBURSE_STATE = 119;
  // used in adding skipping alternate contact information if user is not risky
  LOAN_STEP_EXECUTION_SUB_STATUS_SKIP_NON_RISKY_USER = 120;
  // used to identify if the user is in cool off period after exhausting attempt limits for OTPs
  LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD = 121;
  LOAN_STEP_EXECUTION_SUB_STATUS_WEB_URL_FETCHED = 122;
  // can be used when step not relevant for a particular loan process.
  // ex. While processing a full loan amount pre-payment request, we have a step to close user's loan account at lender end.
  // This Closure step is processed only for loan programs/lenders where the functionality is supported, for other
  // programs/lenders the step is skipped with LOAN_STEP_EXECUTION_SUB_STATUS_STEP_INAPPLICABLE sub-status.
  LOAN_STEP_EXECUTION_SUB_STATUS_STEP_INAPPLICABLE = 123;
  LOAN_STEP_EXECUTION_SUB_STATUS_CAS_DETAILED_FETCH_FAILED = 124;
  LOAN_STEP_EXECUTION_SUB_STATUS_LIEN_ATTEMPT_EXPIRED = 125;
  LOAN_STEP_EXECUTION_SUB_STATUS_ACTIVE_LOAN_WITH_VENDOR = 126;
  LOAN_STEP_EXECUTION_SUB_STATUS_DOB_DOES_NOT_MATCH_PAN_DATA = 127;
  LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL = 128;
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_DROPPED_OFF_FROM_JOURNEY = 129;
  LOAN_STEP_EXECUTION_SUB_STATUS_PREVIOUS_MANDATE_ALREADY_SUCCESSFUL = 130;
  LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_ACCOUNT_ALREADY_CLOSED = 131;
  LOAN_STEP_EXECUTION_SUB_STATUS_INVALID_DRAWDOWN_ARGUMENT = 132;
  LOAN_STEP_EXECUTION_SUB_STATUS_SI_NOT_SETUP = 133;
  LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_INITIATED = 134;
  LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW = 135;
  LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_IN_PROGRESS = 136;
  LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE = 137;
  // to mark payments failed due to user related issues like insufficient balance, wrong otp etc.
  LOAN_STEP_EXECUTION_SUB_STATUS_PAYMENT_FAILED_USER_ERROR = 138;

  // added bifurcation in skipped
  // 1: Skipped because it is already done
  // 2: Skipped because it is not needed
  LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_ALREADY_DONE = 139;
  LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_NOT_NEEDED = 140;
  // User temporarily blocked to prevent excessive retries
  LOAN_STEP_EXECUTION_SUB_STATUS_USER_TEMPORARY_BLOCKED = 141;
  LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_PENDING = 142;
  LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_SUCCESSFUL = 143;
  LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_FAILED = 144;
  // manual check is required for the step execution, this is used when we need to do manual review of the step execution
  LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_CHECK_FAILED = 145;
  LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DETAILS_VERIFIED = 146;
  LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_SELECTION_FAILURE = 147;
}

enum LoanStepExecutionFlow {
  LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED = 0;
  LOAN_STEP_EXECUTION_FLOW_LOAN_OFFER = 1;
  LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION = 2;
  LOAN_STEP_EXECUTION_FLOW_LOAN_CLOSURE = 3;
  // flow name for loan prepayments done on Fi app.
  LOAN_STEP_EXECUTION_FLOW_PRE_PAY = 4;
  LOAN_STEP_EXECUTION_FLOW_COLLECTIONS = 5;
  LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY = 6;
  LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH = 7;
  LOAN_STEP_EXECUTION_FLOW_SETUP_SI = 8;
  // flow name loan prepayments done outside Fi app.
  LOAN_STEP_EXECUTION_FLOW_OFF_APP_PRE_PAY = 9;
  // This flow is used for non-financial updates required in mutual fund folios, such as updating phone numbers and email addresses associated with the folios.
  LOAN_STEP_EXECUTION_FLOW_MUTUAL_FUND_NFT = 10;
  // This flow is used for orchestrating loan repayments by executing an (already setup) auto-pay mandate.
  LOAN_STEP_EXECUTION_FLOW_AUTO_PAY = 11;
  // This flow is used for orchestrating pre eligibility check
  LOAN_STEP_EXECUTION_FLOW_PRE_ELIGIBILITY = 12;
}

enum LoanStepExecutionStepName {
  LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED = 0;
  LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK = 1;
  LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK = 2;
  // loan step execution for manual review of liveness
  LOAN_STEP_EXECUTION_STEP_NAME_MR_LIVENESS = 3;
  LOAN_STEP_EXECUTION_STEP_NAME_FACE_MATCH = 4;
  // loan step execution for manual review of face-match
  LOAN_STEP_EXECUTION_STEP_NAME_MR_FACE_MATCH = 5;
  // loan step execution status for E-Sign
  LOAN_STEP_EXECUTION_STEP_NAME_KFS = 6;
  // loan step for liveness federal review
  LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW = 7;
  LOAN_STEP_EXECUTION_STEP_NAME_CKYC = 8;
  // loan step for onboarding group stage, can be used in other group stages as well if needed
  LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION = 9;
  LOAN_STEP_EXECUTION_STEP_NAME_MANDATE = 10;
  LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER = 11;
  LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION = 12;
  LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION = 13;
  LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL = 14;
  LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN = 15;
  LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS = 16;
  LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK = 17;
  LOAN_STEP_EXECUTION_STEP_NAME_PAYMENT = 18;
  LOAN_STEP_EXECUTION_STEP_NAME_RECON_PAYMENT = 19;
  LOAN_STEP_EXECUTION_STEP_NAME_UPLOAD_FILE = 20;
  LOAN_STEP_EXECUTION_STEP_NAME_FETCH_COLLECTION_BALANCE = 21;
  LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_SI = 22;
  LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LMS = 23;
  LOAN_STEP_EXECUTION_STEP_NAME_NAME_AND_GENDER = 24;
  LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH = 25;
  LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS = 26;
  LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE = 27;
  LOAN_STEP_EXECUTION_STEP_NAME_VKYC = 28;
  LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS = 29;
  LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB = 30;
  LOAN_STEP_EXECUTION_STEP_NAME_CALL_VENDOR = 31;
  LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH = 32;
  LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR = 33;
  LOAN_STEP_EXECUTION_STEP_NAME_OFFER_GENERATION = 34;
  LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LEAD_DETAILS = 35;
  LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_PRE_BRE = 36;
  // Stage to validate if the pan used for onboarding was already onboarded with us or not
  LOAN_STEP_EXECUTION_STEP_NAME_PAN_UNIQUE_CHECK = 37;
  LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN = 38;
  LOAN_STEP_EXECUTION_STEP_NAME_INITIALISE_LOAN = 39;
  LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS = 40;
  LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT = 41;
  LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE = 42;
  LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE = 43;
  LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES = 44;
  LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD = 45;
  // Stage to cover multiple stages happening in vendor pwa flow under a single step/stage on our side, metadata of this stage can optionally store the details of the stages which were processed in the vendor pwa flow.
  // **Note** : should be used in cases multiple steps can take place within a vendor pwa flow and we want to map it to a single step on our end to avoid tight coupling with vendor flow/stages.
  LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES = 46;
  LOAN_STEP_EXECUTION_STEP_NAME_INCOME_VERIFICATION = 47;
  LOAN_STEP_EXECUTION_STEP_NAME_LAT_LONG = 48;
  LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS = 49;
  LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT = 50;
  LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION = 51;
  LOAN_STEP_EXECUTION_STEP_NAME_RESET_VENDOR_LOAN_APPLICATION = 52;
  LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_USER_CREATION = 53;
  LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_CREATION = 54;
  LOAN_STEP_EXECUTION_STEP_NAME_PARTNER_LMS_LOAN_DISBURSAL = 55;
  LOAN_STEP_EXECUTION_STEP_NAME_CREATE_REPAYMENT_SCHEDULE_AT_LENDER = 56;
  LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS = 57;
  LOAN_STEP_EXECUTION_STEP_NAME_RECORD_PAYMENT_IN_PARTNER_LMS = 58;
  LOAN_STEP_EXECUTION_STEP_NAME_REFRESH_MIRRORED_LMS_FROM_VENDOR = 59;
  LOAN_STEP_EXECUTION_STEP_NAME_CREATE_PAYMENT_LOAN_ACTIVITY = 60;
  LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH = 61;
  // This loan step is used to update details in mutual fund folios, such as email addresses and phone numbers.
  LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_MF_FOLIO_DETAILS = 62;
  // loan step to denote the account aggregator data fetch from the vendor
  LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH = 63;
  // loan step to execute an already setup recurring payment setup for performing auto-repayment to the loan account.
  LOAN_STEP_EXECUTION_STEP_NAME_EXECUTE_RECURRING_PAYMENT = 64;
  // loan step to store alternate phone number for risky users
  LOAN_STEP_EXECUTION_STEP_NAME_CONTACTABILITY = 65;
  LOAN_STEP_EXECUTION_STEP_NAME_LENDER_LOAN_ACCOUNT_CLOSURE = 66;
  // to evaluate the current user state at the end of a data collection flow and set the next action in LR
  LOAN_STEP_EXECUTION_STEP_NAME_SET_NEXT_ACTION = 67;
  // to update the data collection status in LOEC after the data collection is done
  LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LOEC_DATA_STATUS = 68;
  LOAN_STEP_EXECUTION_STEP_NAME_FETCH_EPFO_DATA = 69;
  LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP = 70;
  // loan step to fetch the data before calling vendors bre and after calling fi-pre bre in eligibility flow of vendors like lenden.
  LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_DATA_FETCH_LOAN_DETAILS = 71;
  // loan step to fetch the consent before calling vendors bre and after calling fi-pre bre in eligibility flow of vendors like lenden.
  LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_CONSENT = 72;
  LOAN_STEP_EXECUTION_STEP_NAME_AML = 73;
  LOAN_STEP_EXECUTION_STEP_NAME_CHECK_DATA_COMPLETENESS = 74;
  LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION = 75;
  LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION = 76;
  LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_ADD_DETAILS = 77;
  LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION = 78;
  LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE = 79;
  LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE = 80;
  LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE = 90;
  LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE = 91;
  LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION = 92;
  LOAN_STEP_EXECUTION_STEP_NAME_RE_KFS = 93;
  LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION = 94;

  LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_OWNED_PAYMENT = 95;
  // It can be used for kyc document download steps like CKYC/ Digilocker
  LOAN_STEP_EXECUTION_STEP_NAME_KYC_DOCUMENT_DOWNLOAD = 96;
  LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT_CHECK = 97;
  LOAN_STEP_EXECUTION_STEP_NAME_EXPERIAN_REPORT_FETCH = 98;
  LOAN_STEP_EXECUTION_STEP_NAME_BANK_ACCOUNT_VERIFICATION = 99;
}

enum LoanStepExecutionFieldMask {
  LOAN_STEP_EXECUTION_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_STEP_EXECUTION_FIELD_MASK_REF_ID = 1;
  LOAN_STEP_EXECUTION_FIELD_MASK_FLOW = 2;
  LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID = 3;
  LOAN_STEP_EXECUTION_FIELD_MASK_STEP_NAME = 4;
  LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS = 5;
  LOAN_STEP_EXECUTION_FIELD_MASK_STATUS = 6;
  LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS = 7;
  LOAN_STEP_EXECUTION_FIELD_MASK_STALED_AT = 8;
  LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT = 9;
  LOAN_STEP_EXECUTION_FIELD_MASK_GROUP_STAGE = 10;
}

enum LoanOfferEligibilityCriteriaFieldMask {
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS = 1;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE = 2;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID = 3;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_CREATED_AT = 4;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_BATCH_ID = 5;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS = 6;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_LOAN_SCHEME = 7;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS = 8;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_REQUEST_ID = 9;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT = 10;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS = 11;
}

enum LoanOfferEligibilityCriteriaStatus {
  LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_UNSPECIFIED = 0;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED = 1;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED = 2;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED = 3;
}

enum LoanOfferEligibilityCriteriaSubStatus {
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED = 0;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR = 1;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI = 2;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR = 3;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI = 4;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI_PRE_BRE = 5;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE = 6;
  // prequalified by vendor for real time eligibility/BRE checks
  // current use case - to determine the users who are qualified for AA income based real time eligibility check
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_PRE_QUALIFIED_BY_VENDOR = 7;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_REPORT_DOWNLOAD_FAILED = 8;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_EPFO_DATA_NOT_FOUND = 9;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI_PRE_BRE_WITH_DATA_REQUIREMENT = 10;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_WITH_CHANGED_OFFER = 11;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE_WITH_DATA_REQUIREMENT = 12;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_PRE_BRE = 13;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_PRE_BRE = 14;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_CHECK = 15;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_SERVICEABILITY_CHECK = 16;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_RESTRICTED_OCCUPATION_OR_QUALIFICATION = 17;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_PASSED_BY_RETRY = 18;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_INVALID_OFFER_FROM_VENDOR = 19;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_MAX_ACTIVE_LOANS_WITH_LENDER_REACHED = 20;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_BLACK_BOX = 21;
  LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR_BLACK_BOX = 22;
}

enum LoanInstallmentInfoStatus {
  LOAN_INSTALLMENT_INFO_STATUS_UNSPECIFIED = 0;
  LOAN_INSTALLMENT_INFO_STATUS_ACTIVE = 1;
  LOAN_INSTALLMENT_INFO_STATUS_COMPLETED = 2;
  LOAN_INSTALLMENT_INFO_STATUS_CLOSED = 3;
  LOAN_INSTALLMENT_INFO_STATUS_UNKNOWN = 4;
}

enum LoanInstallmentInfoFieldMask {
  LOAN_INSTALLMENT_INFO_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_ACCOUNT_ID = 1;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_AMOUNT = 2;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_START_DATE = 3;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_END_DATE = 4;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_TOTAL_INSTALLMENT_COUNT = 5;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE = 6;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS = 7;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_STATUS = 8;
  LOAN_INSTALLMENT_INFO_FIELD_MASK_DEACTIVATED_AT = 9;
}

enum LoanActivityType {
  LOAN_ACTIVITY_TYPE_UNSPECIFIED = 0;
  LOAN_ACTIVITY_TYPE_EMI = 1;
  LOAN_ACTIVITY_TYPE_LUMPSUM = 2;
  LOAN_ACTIVITY_TYPE_LATE_FEE = 3;
  LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT = 4;
}

enum LoanActivityFieldMask {
  LOAN_ACTIVITY_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_ACTIVITY_FIELD_MASK_LOAN_ACCOUNT_ID = 1;
  LOAN_ACTIVITY_FIELD_MASK_TYPE = 2;
  LOAN_ACTIVITY_FIELD_MASK_DETAILS = 3;
  LOAN_ACTIVITY_FIELD_MASK_REFERENCE_ID = 4;
  LOAN_ACTIVITY_FIELD_MASK_CREATED_AT = 5;
}

enum LoanPaymentRequestType {
  LOAN_PAYMENT_REQUEST_TYPE_UNSPECIFIED = 0;
  LOAN_PAYMENT_REQUEST_TYPE_EMI = 1;
  LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM = 2;
  LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE = 3;
  // Parent LPR type to orchestrate a list of transactions for collections
  LOAN_PAYMENT_REQUEST_TYPE_BATCH_COLLECTION = 4;
  // Individual collection LPR type to orchestrate a single payment
  LOAN_PAYMENT_REQUEST_TYPE_COLLECTION_PAYMENT = 5;
  // To orchestrate loan repayments made by executing (a already setup) recurring payment e.g SI or NACH mandate execution for loan repayments.
  LOAN_PAYMENT_REQUEST_TYPE_RECURRING_PAYMENT_EXECUTION = 6;
}

enum LoanPaymentRequestStatus {
  LOAN_PAYMENT_REQUEST_STATUS_UNSPECIFIED = 0;
  // when db entry is created
  LOAN_PAYMENT_REQUEST_STATUS_CREATED = 1;
  // when the payment process is initialised
  LOAN_PAYMENT_REQUEST_STATUS_INITIATED = 2;
  // when request is sent to the vendor
  LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS = 3;
  // when vendor responds with Successful transaction
  LOAN_PAYMENT_REQUEST_STATUS_SUCCESS = 4;
  // when vendor responds with transaction failed
  LOAN_PAYMENT_REQUEST_STATUS_FAILED = 5;
  // for any other corner encountered case
  LOAN_PAYMENT_REQUEST_STATUS_UNKNOWN = 6;
  // when request doesn't complete in any given time
  LOAN_PAYMENT_REQUEST_STATUS_EXPIRED = 7;
  // when request is not complete and needs manual efforts to complete
  LOAN_PAYMENT_REQUEST_STATUS_MANUAL_INTERVENTION = 8;
}

enum LoanPaymentRequestSubStatus {
  LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED = 0;
}

enum LoanPaymentRequestFieldMask {
  LOAN_PAYMENT_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_ACTOR_ID = 1;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_ACCOUNT_ID = 2;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_ORCH_ID = 3;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_AMOUNT = 4;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_DETAILS = 5;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_TYPE = 6;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_STATUS = 7;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_SUB_STATUS = 8;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_DELETED_AT = 9;
  LOAN_PAYMENT_REQUEST_FIELD_MASK_PARENT_ID = 10;
}

enum LoanInstallmentPayoutStatus {
  LOAN_INSTALLMENT_PAYOUT_STATUS_UNSPECIFIED = 0;
  // Loan installment is yet to be paid
  LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING = 1;
  // Loan installment was successfully paid to completion
  LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS = 2;
  // TODO: Deprecate if not needed
  LOAN_INSTALLMENT_PAYOUT_STATUS_FAILED = 3;
  // When user makes partial payment
  LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID = 4;
  // When loan is cancelled, the EMIs can also get cancelled.
  // (After the loan amount is disbursed, there is certain cool-off period in which the user is allowed to cancel their loan)
  LOAN_INSTALLMENT_PAYOUT_STATUS_CANCELLED = 5;
  // When user's instalment was settled due to preclosure
  LOAN_INSTALLMENT_PAYOUT_STATUS_PRE_CLOSURE = 6;
}

enum LoanInstallmentPayoutFieldMask {
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_STATUS = 1;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS = 2;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PAYOUT_DATE = 3;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_INSTALLMENT_INFO_ID = 4;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_AMOUNT = 5;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_DATE = 6;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_PRINCIPAL_AMT = 7;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_INTEREST_AMT = 8;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DUE_AMT = 9;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_LOAN_ACCOUNT_ID = 10;
  LOAN_INSTALLMENT_PAYOUT_FIELD_MASK_DETAILS_POSTED_TO_VENDOR = 11;
}

enum LoanApplicationStatus {
  LOAN_APPLICATION_STATUS_UNSPECIFIED = 0;
  LOAN_APPLICATION_STATUS_CREATED = 1;
  LOAN_APPLICATION_STATUS_IN_PROGRESS = 2;
  LOAN_APPLICATION_STATUS_ACCOUNT_CREATED = 3;
  LOAN_APPLICATION_STATUS_CANCELLED = 4;
  LOAN_APPLICATION_STATUS_FAILED = 5;
  LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION = 6;
}

enum LoanApplicationSubStatus {
  LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED = 0;
  LOAN_APPLICATION_SUB_STATUS_VKYC = 1;
  LOAN_APPLICATION_SUB_STATUS_LIVENESS = 2;
  LOAN_APPLICATION_SUB_STATUS_FACEMATCH = 3;
  LOAN_APPLICATION_SUB_STATUS_MANUAL_REVIEW = 4;
  LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION = 5;
  LOAN_APPLICATION_SUB_STATUS_ESIGN = 6;
  LOAN_APPLICATION_SUB_STATUS_APPLICATION_SUBMITTED = 7;
  LOAN_APPLICATION_SUB_STATUS_DISBURSED = 8;
}

enum GroupStage {
  GROUP_STAGE_UNSPECIFIED = 0;
  GROUP_STAGE_ONBOARDING = 1;
  GROUP_STAGE_RISK = 2;
  GROUP_STAGE_DRAWDOWN = 3;
  GROUP_STAGE_MANDATE = 4;
  GROUP_STAGE_AUTH = 5;
  GROUP_STAGE_E_SIGN = 6;
  GROUP_STAGE_DISBURSAL = 8;
  GROUP_STAGE_GET_BALANCES = 9;
  GROUP_STAGE_EXECUTE_PAYMENT = 10;
  GROUP_STAGE_LMS = 11;
  GROUP_STAGE_PAYMENT = 12;
  GROUP_STAGE_CHECK_BRE = 13;
  GROUP_STAGE_KYC = 14;
  GROUP_STAGE_REVIEW_DETAILS = 15;
  GROUP_STAGE_PORTFOLIO_FETCH = 16;
  GROUP_STAGE_OFFER_CREATION = 17;
  GROUP_STAGE_LIEN_MARK = 18;
  GROUP_STAGE_INIT_LOAN = 19;
  GROUP_STAGE_LOAN_ACCOUNT_CREATION = 20;
  GROUP_STAGE_VENDOR_PWA = 21;
  GROUP_STAGE_VKYC = 22;
  GROUP_STAGE_VERIFY_LOAN_DETAILS = 23;
  GROUP_STAGE_KFS = 24;
  GROUP_STAGE_USER_REGISTRATION = 25;
  GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION = 26;
  GROUP_STAGE_PRE_KFS = 27;
  GROUP_STAGE_MUTUAL_FUND_NFT_UPDATE_DETAILS = 28;
  GROUP_STAGE_CONTACTABILITY = 29;
  GROUP_STAGE_VENDOR_LOAN_ACCOUNT_CLOSURE = 30;
  GROUP_STAGE_REFRESH_LMS = 31;
  GROUP_STAGE_DATA_COLLECTION = 32;
  GROUP_STAGE_UPDATE_LOECS = 33;
  GROUP_STAGE_CHECK_DATA_COMPLETENESS = 34;
}

enum LoanProgram {
  LOAN_PROGRAM_UNSPECIFIED = 0;
  LOAN_PROGRAM_PRE_APPROVED_LOAN = 1;
  LOAN_PROGRAM_EARLY_SALARY = 2;
  LOAN_PROGRAM_FLDG = 3;
  LOAN_PROGRAM_FI_LITE_PL = 4;
  LOAN_PROGRAM_FED_REAL_TIME = 5;
  LOAN_PROGRAM_ACQ_TO_LEND = 6;
  LOAN_PROGRAM_LAMF = 7;
  // program to identify real time eligibility check for ETB users
  LOAN_PROGRAM_REAL_TIME_ETB = 8;
  // Distribution program where we do not have pre-approved offers from vendor
  LOAN_PROGRAM_REAL_TIME_DISTRIBUTION = 9;
  // small token personal loan
  LOAN_PROGRAM_STPL = 10;
  // Subvention program with realtime BRE capability
  LOAN_PROGRAM_REALTIME_SUBVENTION = 11;
  // STPL program with realtime BRE capability
  LOAN_PROGRAM_REALTIME_STPL = 12;
  // generic program for Lending eligibility flows
  LOAN_PROGRAM_ELIGIBILITY = 13;
  LOAN_PROGRAM_NON_FI_CORE_STPL = 14;
  LOAN_PROGRAM_NON_FI_CORE_SUBVENTION = 15;
  // this loan program is getting added to provide more flexibility
  // for addition of program in realtime distribution
  LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_V1 = 16 [deprecated = true];
  // program to identify real time eligibility check for NTB users
  LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB = 17;
  //program for early salaryV2
  LOAN_PROGRAM_EARLY_SALARY_V2 = 18;
}

enum LoanApplicantStatus {
  LOAN_APPLICANT_STATUS_UNSPECIFIED = 0;
  // loan applicant is created in our system. Using sub-status we can figure out if applicant
  // is created at vendor's end or not.
  LOAN_APPLICANT_STATUS_CREATED = 1;
  // denotes the state where loan applicant is created at the vendor, and all the necessary
  // details needed to onboard applicant at vendor are added.
  LOAN_APPLICANT_STATUS_APPROVED = 2;
  // applicant is blocked either by Fi or by vendor due to compliance or any other reason.
  LOAN_APPLICANT_STATUS_BLOCKED = 3;
  LOAN_APPLICANT_STATUS_REJECTED = 4;
}

enum LoanApplicantSubStatus {
  LOAN_APPLICANT_SUB_STATUS_UNSPECIFIED = 0;
  // Loan applicant is created but only at Fi, creation at vendor need to be initiated or is in progress.
  LOAN_APPLICANT_SUB_STATUS_CREATED_AT_FI = 1;
  // Loan applicant is created at both Fi and vendor's end.
  LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR = 2;
}

message LoanHeader {
  LoanProgram loan_program = 1;
  Vendor vendor = 2;
  // Deprecated: Use loan type in GetLandingInfoV2Request instead.
  LoanType loan_type = 3;
  // to be used in flows where data ownership and workflow ownership is not same.
  Vendor data_owner = 4;
}

enum PaymentAllocationType {
  PAYMENT_ALLOCATION_TYPE_UNSPECIFIED = 0;
  // Payment made by the user is allocated in the sequential order of EMIs.
  // The first EMI's principal, interest and charges is settled first and the then the next EMI is settled
  PAYMENT_ALLOCATION_TYPE_SEQUENTIAL = 1;
  // Payment made by the user is first allocated to all the pending due EMIs and then the charges is settled
  PAYMENT_ALLOCATION_TYPE_PRINCIPAL_AND_INTEREST_FIRST = 2;
}

enum CkycPositiveConfirmationStatus {
  CKYC_POSITIVE_CONFIRMATION_STATUS_UNSPECIFIED = 0;
  CKYC_POSITIVE_CONFIRMATION_STATUS_YES = 1;
  CKYC_POSITIVE_CONFIRMATION_STATUS_NO = 2;
  CKYC_POSITIVE_CONFIRMATION_STATUS_INPROGRESS = 3;
}

enum LoanUserDetailsType {
  LOAN_USER_DETAILS_TYPE_UNSPECIFIED = 0;
  LOAN_USER_DETAILS_TYPE_KYC = 1;
  LOAN_USER_DETAILS_TYPE_PERMANENT_ADDRESS = 2;
}

enum AssetType {
  ASSET_TYPE_UNSPECIFIED = 0;
  ASSET_TYPE_MUTUAL_FUNDS = 1;
}

enum FetchedAssetFieldMask {
  FETCHED_ASSET_FIELD_MASK_UNSPECIFIED = 0;
  FETCHED_ASSET_FIELD_MASK_VENDOR_ASSET_ID = 1;
  FETCHED_ASSET_FIELD_MASK_USER_ASSET_IDENTIFIER = 2;
  FETCHED_ASSET_FIELD_MASK_FETCHED_AT = 3;
  FETCHED_ASSET_FIELD_MASK_ID = 4;
  FETCHED_ASSET_FIELD_MASK_ACTOR_ID = 5;
  FETCHED_ASSET_FIELD_MASK_VENDOR = 6;
  FETCHED_ASSET_FIELD_MASK_ASSET_TYPE = 7;
  FETCHED_ASSET_FIELD_MASK_DETAILS = 8;
  FETCHED_ASSET_FIELD_MASK_CREATED_AT = 9;
  FETCHED_ASSET_FIELD_MASK_UPDATED_AT = 10;
  FETCHED_ASSET_FIELD_MASK_DELETED_AT = 11;
}

enum IdfcCkycAddressPinCodeType {
  IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_UNSPECIFIED = 0;
  IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_URBAN = 1;
  IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_CORE = 2;
  IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_IFBL = 3;
}

enum OtpType {
  OTP_TYPE_UNSPECIFIED = 0;
  OTP_TYPE_CAMS_PF_FETCH = 1;
  OTP_TYPE_KARVY_PF_FETCH = 2;
  OTP_TYPE_CAMS_LIEN_MARK = 3;
  OTP_TYPE_KARVY_LIEN_MARK = 4;
  OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH = 5;
  OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH = 6;
  OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH = 7;
  OTP_TYPE_MF_CENTRAL_NFT_UPDATE_DETAIL_VERIFICATION = 8;
}

enum OtpStatus {
  OTP_STATUS_UNSPECIFIED = 0;
  OTP_STATUS_GENERATED = 1;
  OTP_STATUS_SUCCESS = 2;
  OTP_STATUS_FAILED = 3;
  OTP_STATUS_NOT_FOUND = 4;
  // otp verification wasn't done within a time window.
  OTP_STATUS_EXPIRED = 5;
}

// MutualFundFacilitator specifies which vendor is facilitating access to folio units to obtain secured loan
enum MutualFundFacilitator {
  MUTUAL_FUND_FACILITATOR_UNSPECIFIED = 0;
  MUTUAL_FUND_FACILITATOR_CAMS = 1;
  MUTUAL_FUND_FACILITATOR_KARVY = 2;
}

enum LoansUserStatus {
  LOANS_USER_STATUS_UNSPECIFIED = 0;
  LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT = 2;
  LOANS_USER_STATUS_CLOSED_LOAN_ACCOUNT = 3;
  LOANS_USER_STATUS_ACTIVE_LOAN_APPLICATION = 4;
  LOANS_USER_STATUS_NOT_AN_ACTIVE_LOANS_USER = 5;
}

enum ItrFormType {
  ITR_FORM_TYPE_UNSPECIFIED = 0;
  ITR_FORM_TYPE_ONE = 1;
  ITR_FORM_TYPE_TWO = 2;
  ITR_FORM_TYPE_THREE = 3;
  ITR_FORM_TYPE_FOUR = 4;
  ITR_FORM_TYPE_FIVE = 5;
  ITR_FORM_TYPE_SIX = 6;
  ITR_FORM_TYPE_SEVEN = 7;
}

enum ItrTaxpayerStatus {
  ITR_TAXPAYER_STATUS_UNSPECIFIED = 0;
  ITR_TAXPAYER_STATUS_INDIVIDUAL = 1;
}

enum ItrTaxpayerResidentialStatus {
  ITR_TAXPAYER_RESIDENTIAL_STATUS_UNSPECIFIED = 0;
  ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT = 1;
  ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT_BUT_NOT_ORDINARILY_RESIDENT = 2;
  ITR_TAXPAYER_RESIDENTIAL_STATUS_NON_RESIDENT = 3;
}


enum MandateRequestFieldMask {
  MANDATE_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  MANDATE_REQUEST_FIELD_MASK_ID = 1;
  MANDATE_REQUEST_FIELD_MASK_ACTOR_ID = 2;
  MANDATE_REQUEST_FIELD_MASK_VENDOR_MANDATE_ID = 3;
  MANDATE_REQUEST_FIELD_MASK_UPDATED_AT = 4;
  MANDATE_REQUEST_FIELD_MASK_CREATED_AT = 5;
  MANDATE_REQUEST_FIELD_MASK_DELETED_AT = 6;
  MANDATE_REQUEST_FIELD_MASK_DETAILS = 7;
  MANDATE_REQUEST_FIELD_MASK_STATUS = 8;
  MANDATE_REQUEST_FIELD_MASK_LOAN_PROGRAM = 9;
  MANDATE_REQUEST_FIELD_MASK_LOAN_VENDOR = 10;
}

enum RecordUserActionIdentifier {
  RECORD_USER_ACTION_IDENTIFIER_UNSPECIFIED = 0;
  RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_MOBILE = 1;
  RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_NFT_UPDATE_EMAIL = 2;
  RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF = 3;
  RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_RESTART_STAGE = 4;
}

enum FiftyFinLamfOfferSource {
  FIFTYFIN_LAMF_OFFER_SOURCE_UNSPECIFIED = 0;
  FIFTYFIN_LAMF_OFFER_SOURCE_CAS_SUMMARY_AND_DETAILED = 1;
}
enum MandateRequestStatus {
  MANDATE_REQUEST_STATUS_UNSPECIFIED = 0;
  // This status has to be used when mandate is successfully setup by the user
  MANDATE_REQUEST_STATUS_SUCCESS = 1;
  // This status has to be used when we receive failed status code from vendor when user tries to setup mandate
  // vendor can return failed status code because of multiple reasons like incorrect details shared by the customer
  MANDATE_REQUEST_STATUS_FAILED = 2;
  // This is to be used only if user doesn't complete the mandate and the request gets expired.
  MANDATE_REQUEST_STATUS_EXPIRED = 3;
  // This status has to be used when mandate is initiated at the vendor side such that
  // vendor mandate id is known to the vendor in both cases including cases where
  // mandate id generated by the vendor or by us in our backend system
  MANDATE_REQUEST_STATUS_INITIATED = 4;
  // This status has to be used when mandate is initiated but it is pending because of user has not
  // completed all actions necessary to setup mandate on SDK or webview flow
  MANDATE_REQUEST_STATUS_PENDING = 5;
  // This status has to be used when user cancels or drops off from the mandate journey either from SDK or webview journey
  MANDATE_REQUEST_STATUS_CANCELED_BY_USER = 6;
}

enum MfOfferApprovalStatus {
  MF_OFFER_APPROVAL_STATUS_UNSPECIFIED = 0;
  MF_OFFER_APPROVAL_STATUS_ALREADY_LIEN_MARKED = 1;
  MF_OFFER_APPROVAL_STATUS_UNAVAILABLE_UNITS_NOT_ZERO = 2;
  MF_OFFER_APPROVAL_STATUS_IS_DEMAT = 3;
  MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST = 4;
  MF_OFFER_APPROVAL_STATUS_NOT_IN_APPROVED_ISIN_LIST_IN_LOAN_ELIGIBILITY_CHECK_API = 5;
  MF_OFFER_APPROVAL_STATUS_PHONE_AND_EMAIL_ARE_NOT_PRIMARY = 6;
}

enum DataRequirementType {
  DATA_REQUIREMENT_TYPE_UNSPECIFIED = 0;
  // deprecated, use DATA_REQUIREMENT_TYPE_TECH_AA
  DATA_REQUIREMENT_TYPE_AA = 1;
  DATA_REQUIREMENT_TYPE_CIBIL = 2;
  DATA_REQUIREMENT_TYPE_EPFO = 3;
  // here common represents  DATA_REQUIREMENT_TYPES which will be common for data collection for all policies
  // for eg: NameGender,PanDob, Address, Employment,CreditReport
  DATA_REQUIREMENT_TYPE_COMMON = 4;
  // loan Data we need to fetch prior calling lenden bre.
  DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION = 5;
  // consent data we need to fetch prior calling lenden bre.
  DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_CONSENT = 6;
  // income estimation with ownership of epifi tech
  DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME = 7;
  // consent data we need to fetch prior calling federal bre.
  DATA_REQUIREMENT_TYPE_FEDERAL_BRE_CONSENT = 8;
  // pan verification from onboarding needs to be done prior calling federal bre.
  DATA_REQUIREMENT_TYPE_FEDERAL_PAN_VERIFICATION = 9;
  // some lender evaluations require full address, this will be used to identify and collect full address in such cases
  DATA_REQUIREMENT_TYPE_FULL_ADDRESS = 10;
  DATA_REQUIREMENT_TYPE_EXPERIAN = 11;
}

enum LoanDocType {
  // (default) Used when there is a single esign document
  LOAN_DOC_TYPE_UNSPECIFIED = 0;
  // Used to generate the KFS document
  LOAN_DOC_TYPE_KFS = 1;
  // Used to generate Loan agreement document
  LOAN_DOC_TYPE_LOAN_AGREEMENT = 2;
}

// For certain lenders, interest and principal payments should be made against separate accounts
// this will be used to differentiate between loan payment account type in a loan payment request
enum LoanPaymentAccountType {
  LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED = 0;
  // for only interest loan payments
  LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST = 1;
  // for only principal loan payments
  LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL = 2;
  // for all types of loan payments
  LOAN_PAYMENT_ACCOUNT_TYPE_COMMON = 3;
}

enum LoanOfferType {
  LOAN_OFFER_TYPE_UNSPECIFIED = 0;
  LOAN_OFFER_TYPE_PRE_QUALIFIED = 1;
  LOAN_OFFER_TYPE_SOFT = 2;
  LOAN_OFFER_TYPE_HARD = 3;
}

enum PreEligibilityOfferFieldMask {
  PRE_ELIGIBILITY_OFFER_FIELD_MASK_UNSPECIFIED = 0;
  PRE_ELIGIBILITY_OFFER_FIELD_MASK_ACTOR_ID = 1;
  PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_SINCE = 2;
  PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_VALID_TILL = 3;
  PRE_ELIGIBILITY_OFFER_FIELD_MASK_OFFER_DETAILS = 4;
}

// Deprecated
// Create a new message that applies whitelisting/blacklisting logic for the below specific components
// LoanRequest, LoanAccount, LoanOption
// Whitelisting approach example: filter type = whitelist, LR option = FEDERAL:REAL_TIME_DISTRIBUTION_NTB => Shows only given LR
// Blacklisting approach example: filter type = blacklist, LO option = FEDERAL:REAL_TIME_DISTRIBUTION_NTB => Shows every other offer except given LO
enum LandingInfoFilter {
  LANDING_INFO_FILTERS_UNSPECIFIED = 0;
  // Skips failed loan request fetch in GETLANDINGINFO_V2 RPC to bypass dashboard screen and direct users to new second look screens
  LANDING_INFO_SKIP_FAILED_LR_FETCH = 1 [deprecated = true];
  // overrides the landing page to offer screen and shows only the offers corresponding to the program and vendor of currently active loan request
  SHOW_ONLY_CURRENT_LR_OFFERS = 2 [deprecated = true];
}

// UserStatus represents the current status of a user in the system.
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  // user having active eligibility loan request
  USER_STATUS_ACTIVE_ELIGIBILITY = 1;
  // user having active application loan request
  USER_STATUS_ACTIVE_APPLICATION = 2;
  // user having active loan account
  USER_STATUS_ACTIVE_LOAN = 3;
  // user having active loan offer
  USER_STATUS_OFFER_AVAILABLE = 4;
  // user is either rejected or not eligible for beginning any jounrey in loans
  USER_STATUS_REJECTED = 5;
  // user is eligible to apply for a loan offer, i.e. user can start eligibility journey
  USER_STATUS_ELIGIBLE_TO_APPLY = 6;
}

// KycType is added to give visibility od kyc types to LSP, but this can be
// removed if LSP only will get kyc data not according to the type.
enum KycType {
  KYC_TYPE_UNSPECIFIED = 0;
  // CKYC type is used for CKYC based KYC verification
  KYC_TYPE_CKYC = 1;
  // Digilocker type is used for Digilocker based KYC verification
  KYC_TYPE_DIGILOCKER = 2;
}

enum PrepayStatus {
  PREPAY_OK = 0;
  FAILED_EXISTING_PAYMENT_IN_PROGRESS = 1;
  FAILED_PREPAY_BLACKOUT_PERIOD = 2;
  EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR = 3;
}

// to define the caller type i.e. if initiated by user or initiated through some scripts
enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;
  PROVENANCE_USER_INITIATED = 1;
  PROVENANCE_SYSTEM_INITIATED = 2;
}
