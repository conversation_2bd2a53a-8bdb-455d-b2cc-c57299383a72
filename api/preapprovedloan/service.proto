syntax = "proto3";

package preapprovedloan;

import "api/accounts/account_type.proto";
import "api/auth/liveness/internal/liveness_attempt.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/preapprovedloan/pal_enums/enums.proto";
import "api/order/payment/payment_protocol.proto";
import "api/pay/attribute/transaction_attribute.proto";
import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/enums/calculator.proto";
import "api/preapprovedloan/enums/form_data.proto";
import "api/preapprovedloan/enums/payment_provenance.proto";
import "api/preapprovedloan/enums/pre_close.proto";
import "api/preapprovedloan/enums/reconciliation.proto";
import "api/preapprovedloan/external.proto";
import "api/preapprovedloan/loan_account.proto";
import "api/preapprovedloan/loan_applicant.proto";
import "api/preapprovedloan/loan_info.proto";
import "api/preapprovedloan/loan_installment_info.proto";
import "api/preapprovedloan/loan_installment_payout.proto";
import "api/preapprovedloan/loan_offer.proto";
import "api/preapprovedloan/loan_offer_eligibility_criteria.proto";
import "api/preapprovedloan/loan_payment_request.proto";
import "api/preapprovedloan/loan_request.proto";
import "api/preapprovedloan/loan_step_execution.proto";
import "api/preapprovedloan/mutual_fund.proto";
import "api/preapprovedloan/pledge_details.proto";
import "api/preapprovedloan/types.proto";
import "api/recurringpayment/enach/enums/enums.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/bank_account_details.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/components.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/enums.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/form.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/marital_status.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

service PreApprovedLoan {
  // This rpc can be used to decide on which screen to land user in the pre-approved-loan flow.
  // If the user doesn't have any loan requests/accounts, but a loan offer,
  // they will be taken to the landing screen otherwise the management screen by rpc status
  // RPC status - Offer -> User doesn't have any loan requests, but has an active offer
  // RPC status - Applications -> User already has some loan requests
  // RPC status - StatusPermissionDenied -> User doesn't have any loan requests and is not eligible for any loans
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse) {}
  // This rpc is a successor of GetLandingInfo RPC, that decides on which screen to land user in the pre-approved loan flow.
  // If the user has a loan account/loan request then dashboard is displayed, else if user has loan offer, then loan
  // offer is displayed.
  // RPC status - Offer -> User doesn't have any loan requests, but has an active offer
  // RPC status - Applications -> User already has some loan requests
  // RPC status - StatusPermissionDenied -> User doesn't have any loan requests and is not eligible for any loans
  rpc GetLandingInfoV2 (GetLandingInfoV2Request) returns (GetLandingInfoV2Response) {}
  // This rpc is a successor of GetLandingInfoV2 RPC
  // This doesn't take the decision and instead passes on all the details required to create landing screen
  rpc GetLandingInfoV3 (GetLandingInfoV3Request) returns (GetLandingInfoV3Response) {}
  // used to show the details of the loan offer
  rpc GetOfferDetails (GetOfferDetailsRequest) returns (GetOfferDetailsResponse) {}
  // to generate confirmation code(for now OTP)
  rpc GenerateConfirmationCode (GenerateConfirmationCodeRequest) returns (GenerateConfirmationCodeResponse) {}
  // starts workflow for loan application
  rpc ApplyForLoan (ApplyForLoanRequest) returns (ApplyForLoanResponse) {}
  // submit application to vendor
  rpc ConfirmApplication (ConfirmApplicationRequest) returns (ConfirmApplicationResponse) {}
  // fetch status of application
  rpc GetApplicationStatus (GetApplicationStatusRequest) returns (GetApplicationStatusResponse) {}
  // fetch status of application using sync proxy workflow
  rpc GetApplicationStatusSync (GetApplicationStatusSyncRequest) returns (GetApplicationStatusSyncResponse) {}
  // to enable status poll for pre approved loan initiated liveness
  rpc GetLivenessStatus (GetLivenessStatusRequest) returns (GetLivenessStatusResponse) {}
  // to power loan management screen
  rpc GetDashboard (GetDashboardRequest) returns (GetDashboardResponse) {}
  // to power loan management screen
  rpc GetDashboardV2 (GetDashboardRequest) returns (GetDashboardResponse) {}
  // to cancel an ongoing loan application
  rpc CancelApplication (CancelApplicationRequest) returns (CancelApplicationResponse) {}
  // to initiate E-Sign (KFS) process
  rpc InitiateESign (InitiateESignRequest) returns (InitiateESignResponse) {}
  // to get loan details for a user for a loan
  rpc GetLoanDetails (GetLoanDetailsRequest) returns (GetLoanDetailsResponse) {}
  // to make loan pre payment
  rpc PrePayLoan (PrePayLoanRequest) returns (PrePayLoanResponse) {}
  // to check the make payment order status for pre-pay/close
  // RPC to check the status related to any pre-approved loan activity.
  // The RPC can be used to fetch he status of pre-payment of a loan account of pre-closure of a loan account.
  rpc GetLoanActivityStatus (GetLoanActivityStatusRequest) returns (GetLoanActivityStatusResponse) {}
  // rpc to fetch all transactions/activities for a loan account
  // loads the data from db (loan_activities, loan_payment_requests) and populate the fields accordingly date wise sorted in desc order (latest first)
  rpc GetAllTransactions (GetAllTransactionsRequest) returns (GetAllTransactionsResponse) {}
  // rpc to fetch the transaction receipt/details according to a loan activity
  rpc GetTransactionReceipt (GetTransactionReceiptRequest) returns (GetTransactionReceiptResponse) {}
  // External rpc to fetch loan details for any user (used by ask fi)
  // The default behavior of the RPC is to send details for all loan accounts belonging to the actor irrespective of the loan status (active/closed).
  // To fetch loan details for specific loan accounts, the loanAccountId list field can be used.
  // If loanAccountIds is nil, fetch details of all loan accounts by actorId.
  rpc GetLoanAccountDetails (GetLoanAccountDetailsRequest) returns (GetLoanAccountDetailsResponse) {}
  // External rpc to fetch latest loan offer for the user, if any, for each vendor.
  // If no loan offer exists, rpc returns loan_offers nil
  // Else, responds with a list of latest loan offers from each vendor which are eligible.
  // Each loan offer is active and user can take loan against that, if is_active field is true.
  // If is_active is false, the offer might be expired, or user would have taken loan against that offer
  rpc GetLoanOffers (GetLoanOffersRequest) returns (GetLoanOffersResponse) {}
  // External rpc to fetch recent loan application started by the user, if any.
  // Fetches recent loan applications by actor id for all the vendors, if any.
  rpc GetRecentLoanApplicationDetails (GetRecentLoanApplicationDetailsRequest) returns (GetRecentLoanApplicationDetailsResponse) {}
  // External paginated rpc to fetch transactions for a user
  // If the LoanAccountId list is populated, send the transaction details of only those loans present in the same.
  // If not then send the transaction details of all loan accounts (active / closed) associated with the actor.
  // Sorted in decreasing order of txn time.
  rpc GetLoanTransactions (GetLoanTransactionsRequest) returns (GetLoanTransactionsResponse) {}
  // for populating different fields in pal home card, it will return response data based on type of card to be shown
  rpc GetLoanSummaryForHome (GetLoanSummaryForHomeRequest) returns (GetLoanSummaryForHomeResponse) {};
  // External rpc to estimate credit limit utilised through loan, including applications completed by user
  rpc EstimateCreditUtilised (EstimateCreditUtilisedRequest) returns (EstimateCreditUtilisedResponse);
  // rpc to persist the user's address in db
  rpc AddAddressDetails (AddAddressDetailsRequest) returns (AddAddressDetailsResponse);
  // rpc to persist the user's employment in db
  rpc AddEmploymentDetails (AddEmploymentDetailsRequest) returns (AddEmploymentDetailsResponse);
  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}
  // DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse);
  // rpc to let customer confirm the revised loan details based on their occupation. This is called from the hustle screen and takes user to poll screen
  rpc ConfirmRevisedLoanDetails (ConfirmRevisedLoanDetailsRequest) returns (ConfirmRevisedLoanDetailsResponse) {}
  // this rpc is to acknowledge client callbacks
  // whenever clients have to send any callback to BE, they can use this RPC. Not necessarily only for success cases,
  // callbacks could just be that the SDK flow has started.
  rpc ClientCallback (ClientCallbackRequest) returns (ClientCallbackResponse) {}
  // rpc to make a vg call in idfc flow to verify the PAN details entered by the user
  rpc VerifyDetails (VerifyDetailsRequest) returns (VerifyDetailsResponse) {}
  // external rpc to get early salary details for salary client
  // returns multiple stats which could be possible and data needed to be shown for them
  rpc GetEarlySalaryDetails (GetEarlySalaryDetailsRequest) returns (GetEarlySalaryDetailsResponse) {}
  // rpc to record consent for ckyc verification from the user
  rpc VerifyCkycDetails (VerifyCkycDetailsRequest) returns (VerifyCkycDetailsResponse) {}
  // rpc to update loan schedule of a loan account
  rpc RefreshLMSSchedule (RefreshLmsScheduleRequest) returns (RefreshLmsScheduleResponse) {}
  // rpc to fetch web view from vendor
  rpc GetMandateViewData (GetMandateViewDataRequest) returns (GetMandateViewDataResponse) {}
  // rpc to execute collections for any flow
  rpc ExecuteCollection (ExecuteCollectionRequest) returns (ExecuteCollectionResponse) {}
  // rpc to reconcile loan txns from vendor
  rpc ReconLoanActivity (ReconLoanActivityRequest) returns (ReconLoanActivityResponse);
  // Returns loan schedule if present in our DB
  rpc GetLoanSchedule (GetLoanScheduleRequest) returns (GetLoanScheduleResponse);
  // rpc to persist the user entered PAN and DOB in Db
  rpc AddPanAndDobData (AddPanAndDobDataRequest) returns (AddPanAndDobDataResponse);
  // GetLoanInstallmentPayoutDetails RPC to fetch the loan installment payout details, installment info and
  // loan account details for a given installment id
  rpc GetLoanInstallmentPayoutDetails (GetLoanInstallmentPayoutDetailsRequest) returns (GetLoanInstallmentPayoutDetailsResponse);
  // rpc to persist the user entered Name and Gender
  rpc AddNameAndGender (AddNameAndGenderRequest) returns (AddNameAndGenderResponse);
  // rpc to onboard a user for loan journey and starts workflow for loan eligibility
  // creates loan applicant and loan request if not present already
  rpc CheckLoanEligibility (CheckLoanEligibilityRequest) returns (CheckLoanEligibilityResponse) {}
  // rpc to initiate the credit report fetch process for the user
  rpc FetchCreditReport (FetchCreditReportRequest) returns (FetchCreditReportResponse);
  // GetLoanDefaultDetails RPC provides an aggregated view of all the defaults associated with a loan account across payouts
  rpc GetLoanDefaultDetails (GetLoanDefaultDetailsRequest) returns (GetLoanDefaultDetailsResponse) {}
  // rpc to persist the user's banking details in db
  rpc AddBankingDetails (AddBankingDetailsRequest) returns (AddBankingDetailsResponse) {};
  // rpc to get review details for loan application/eligibility summary details screen
  rpc GetLoanReviewDetails (GetLoanReviewDetailsRequest) returns (GetLoanReviewDetailsResponse) {}
  // rpc to submit post user reviews the loan application/eligibility summary details
  rpc SubmitReviewLoanDetails (SubmitReviewLoanDetailsRequest) returns (SubmitReviewLoanDetailsResponse) {};
  // external rpc to fetch all data stored for an applicant during a loan journey. Journey could be loan eligibility or loan application.
  rpc GetLoanUserDetails (GetLoanUserDetailsRequest) returns (GetLoanUserDetailsResponse) {};
  // rpc to acquire LoanId using LoanHeader And ActorId can return errors if no loanId can be found for given LoanHeader and ActorId
  // or if there are multiple loanId for given LoanHeader And ActorId then log error and proceed with the latest loanId
  rpc GetLoanIdByHeaderAndActorId (GetLoanIdByHeaderAndActorIdRequest) returns (GetLoanIdByHeaderAndActorIdResponse) {}
  // GetDowntimeStatus RPC to check if the vendor is down for maintenance
  rpc GetDowntimeStatus (GetDowntimeStatusRequest) returns (GetDowntimeStatusResponse) {};
  // InitiateSiSetup RPC creates loan request and triggers SI workflow
  rpc InitiateSiSetup (InitiateSiSetupRequest) returns (InitiateSiSetupResponse) {};
  // RPC to get completed loan requests for an actor by check all the existing loan requests
  rpc GetCompletedLoanRequests (GetCompletedLoanRequestsRequest) returns (GetCompletedLoanRequestsResponse) {};
  // RPC to get active loan requests for an actor by check all the existing loan requests
  rpc GetActiveLoanRequests (GetActiveLoanRequestsRequest) returns (GetActiveLoanRequestsResponse) {};
  // GetVendorApplicantId fetches the applicant id at vendor's end for the given user based on the loan header
  rpc GetVendorApplicantId (GetVendorApplicantIdRequest) returns (GetVendorApplicantIdResponse) {};
  // rpc to let the backend know which income verification option is selected by the user
  rpc SubmitIncomeVerificationOption (SubmitIncomeVerificationOptionRequest) returns (SubmitIncomeVerificationOptionResponse) {}
  // rpc to fetch mandate view details like webview/sdk and necessary params for the same from backend
  rpc GetMandateViewDataV2 (GetMandateViewDataV2Request) returns (GetMandateViewDataV2Response) {}

  // rpc to fetch pwa redirection details from vendor for redirecting the user to the pwa flow,
  // some flows are driven through a pwa journey on vendor side so this rpc is useful to fetch the pwa redirection details from the vendor to redirect the user to the pwa flow
  // e.g moneyview lender loan application and loan servicing flows are mostly driven on their pwa.
  rpc GetPWARedirectionDetails (GetPWARedirectionDetailsRequest) returns (GetPWARedirectionDetailsResponse) {}

  // RecordUserAction rpc can be used to record some client action in a loan flow and return some action based on the response.
  rpc RecordUserAction (RecordUserActionRequest) returns (RecordUserActionResponse) {
  };

  // ProcessOffAppLoanRepayment processes loan repayment that was done off app
  rpc ProcessOffAppLoanRepayment (ProcessOffAppLoanRepaymentRequest) returns (ProcessOffAppLoanRepaymentResponse);

  // GetLoansUserStatus gives the loans status of an actor, if any loan account or loan request is active for the user.
  // This is an external api that can be used by other services to get the status of an user at loans end
  rpc GetLoansUserStatus (GetLoansUserStatusRequest) returns (GetLoansUserStatusResponse) {}
  // GetIncomeVerificationInfo gives income verification info related to income verifications methods in pl flows,
  // for Income Verification through ITR, it will give max attempts reached or not
  rpc GetIncomeVerificationInfo (GetIncomeVerificationInfoRequest) returns (GetIncomeVerificationInfoResponse) {}

  // GetVkycDeeplink can be used to do the vkyc related executions in the flow.
  rpc GetVkycDeeplink (GetVkycDeeplinkRequest) returns (GetVkycDeeplinkResponse) {}

  // GetForeclosureDetails RPC simplifies fetching loan foreclosure details from backend
  // Currently we are only fetching foreclosure details from vendor
  // This RPC aims to eliminate complex wire dependencies currently present in the foreclosure details interface and
  // provide one common API which can be used across all the places to fetch the details
  rpc GetForeclosureDetails (GetForeclosureDetailsRequest) returns (GetForeclosureDetailsResponse) {}

  // InitiateLoanClosure RPC initiates loan closure workflow in the backend for given loan account id
  // RPC returns OK and loan request id to identify workflow
  // RPC returns ALREADY EXISTS status code if some closure workflow already exists in successful state
  rpc InitiateLoanClosure (InitiateLoanClosureRequest) returns (InitiateLoanClosureResponse) {}

  // CheckLmsDataDifference RPC is used to find differences in lms details for a given loan account id
  // between two lms data sources.
  rpc CheckLmsDataDifference (CheckLmsDataDifferenceRequest) returns (CheckLmsDataDifferenceResponse) {}
  // This rpc generates esign agreement url stored in aws  if previous url has expired or does not exist.
  rpc GenerateEsignAgreement (GenerateEsignAgreementRequest) returns (GenerateEsignAgreementResponse);
  // rpc to fetch pre-pay details like emi, charges, overdue, foreclosue amount etc
  rpc GetPrePayDetails (GetPrePayDetailsRequest) returns (GetPrePayDetailsResponse);
  rpc AddEmploymentDetailsSync (AddEmploymentDetailsRequest) returns (AddEmploymentDetailsResponse);
  // rpc to save details for alternate contact information
  rpc SaveContactDetails (SaveContactDetailsRequest) returns (SaveContactDetailsResponse) {}
  // rpc to initiate auto pay execution for a given loan account id, it is responsible for deciding the auto-pay execution amount as well
  // returns FailedPrecondition if the loan account does not have any due/overdue amount to be collected
  rpc InitiateAutoPayExecution (InitiateAutoPayExecutionRequest) returns (InitiateAutoPayExecutionResponse) {}

  // rpc to add address details in sync fashion
  rpc AddAddressDetailsSync (AddAddressDetailsRequest) returns (AddAddressDetailsResponse);
  // RPC to get active loan requests for an actor by check all the existing loan requests
  rpc GetActiveLoanPaymentRequests (GetActiveLoanPaymentRequestsRequest) returns (GetActiveLoanPaymentRequestsResponse) {};

  // ModifyLoanTerms will modify the loan terms (loan amount and tenure) for an active loan application
  // this is required when the offer is changed during the loan application and user needs to select the amount and tenure again after checking the revised offer
  rpc ModifyLoanTerms (ModifyLoanTermsRequest) returns (ModifyLoanTermsResponse) {};

  rpc GetAAConsentCollectionDetails (GetAAConsentCollectionDetailsRequest) returns (GetAAConsentCollectionDetailsResponse) {};
  // TODO: Temporary rpc to unblock nbfc testing
  rpc GetApplicantByVendorApplicantId (GetApplicantByVendorApplicantIdRequest) returns (GetApplicantByVendorApplicantIdResponse) {};
  // To collect and save data like user selected loan amount, consents etc
  rpc CollectData (CollectDataRequest) returns (CollectDataResponse);
  // To get Customer details like BRE Reference Number for Federal Loan
  rpc GetFederalLoanCustomerDetails (GetFederalLoanCustomerDetailsRequest) returns (GetFederalLoanCustomerDetailsResponse) {};
  // GetRedirectDL: RPC that determines the next screen to show based on loan header, actor ID and client request ID.
  // Used by service providers to get appropriate deeplink for redirecting users to the next step in loan flow.
  // Returns a deeplink response that contains the next screen to display.
  rpc GetRedirectDL (GetRedirectDLRequest) returns (GetRedirectDLResponse) {}
  // To collect various types of form data from the user
  rpc CollectFormData (CollectFormDataRequest) returns (CollectFormDataResponse);
  // To get the current status of the loan user and what user can do next
  rpc GetLoanUserStatusV2 (GetLoanUserStatusV2Request) returns (GetLoanUserStatusV2Response);
}

message GetLoanUserStatusV2Request {
  string actor_id = 1;
  // optional this timestamp will be used to reduce the data fetched from the DB by querying only for the entries that are updated after the given timestamp
  // this will be used as a filter only for orchestration entities like loan_requests and not for entities like loan_accounts
  google.protobuf.Timestamp updated_at_after = 2;
}

message GetLoanUserStatusV2Response {
  rpc.Status status = 1;
  UserStatus user_status = 3;
  LoanEventTimestamps loan_event_timestamps = 4;

  message LoanEventTimestamps {
    google.protobuf.Timestamp latest_eligibility_req_start_timestamp = 1;
    google.protobuf.Timestamp latest_application_req_start_timestamp = 2;
    google.protobuf.Timestamp latest_loan_disbursed_timestamp = 3;
    google.protobuf.Timestamp latest_rejection_timestamp = 4;
  }
}

message CollectFormDataRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_request_id = 3;
  repeated FormFieldValue form_data_list = 4;
  message FormFieldValue {
    enums.FieldId field_id = 1;
    oneof field_value {
      string string_value = 2;
      int32 int_value = 3;
      google.type.Money money_value = 4;
      // this value should be same as the value passed in api.typesv2.ui.OptionSelectionItem->identifier
      // can be string of enums like Gender, MaritalStatus, EmploymentType etc
      string selected_option_id = 5;
      google.type.Date date_value = 6;
    }
  }
}

message CollectFormDataResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetRedirectDLRequest {
  // Loan header to identify the loan program and vendor
  LoanHeader loan_header = 1;
  // Actor ID for whom the next screen needs to be determined
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // Client request ID used to track the workflow
  string client_request_id = 3 [(validate.rules).string.min_len = 1];
}

message GetRedirectDLResponse {
  // RPC response status
  rpc.Status status = 1;
  // Next screen to show in the form of a deeplink
  frontend.deeplink.Deeplink redirect_deeplink = 2;
}

message CollectDataRequest {
  LoanHeader loan_header = 1;
  LoanStepExecutionFlow flow = 2;
  string actor_id = 3;
  string loan_request_id = 4;
  oneof Data {
    LoanPreferences loan_preferences = 5;
    Consent consent = 6;
    ModifiedInterestRate modified_interest_rate = 7;
  }

  message LoanPreferences {
    int64 loan_amount = 1;
    int64 interest = 2;
  }
  message Consent {
    // the client request id against which we store consents
    string request_id = 1;
    repeated string consent_ids = 2;
  }

  message ModifiedInterestRate {
    double interest_rate = 1;
  }
}


message CollectDataResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}
message InitiateAutoPayExecutionRequest {
  LoanHeader loan_header = 1;
  string loan_account_id = 2;
}

message InitiateAutoPayExecutionResponse {
  rpc.Status status = 1;
}

message CheckLmsDataDifferenceRequest {
  LoanHeader loan_header = 1;
  string loan_account_id = 2;
  enums.LmsDataSource source_a = 3;
  enums.LmsDataSource source_b = 4;
}

message CheckLmsDataDifferenceResponse {
  enum Status {
    OK = 0;
    // denotes failure in checking difference due to expected delay (within SLA) in lms creation at vendor
    EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR = 101;
  }
  rpc.Status status = 1;
  enums.LmsDataDifferenceStatus difference_status = 2;
}

message GetVkycDeeplinkRequest {
  LoanHeader loan_header = 1;
  string loan_request_id = 2;
}

message GetVkycDeeplinkResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}


message ProcessOffAppLoanRepaymentRequest {
  // loan account id
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // total money user repaid
  google.type.Money repaid_amount = 2 [(validate.rules).message.required = true];
  // unique payment ref id
  string payment_reference_id = 3 [(validate.rules).string.min_len = 1];
  // time of payment
  google.protobuf.Timestamp payment_time = 4 [(validate.rules).timestamp.required = true];
  // mode of payment eg; UPi
  order.payment.PaymentProtocol payment_protocol = 5;
  // payment processing source
  enums.PaymentProvenance payment_provenance = 6;
  // timetamp when transaction was settled at vendor side
  // in the case it is not provided make it equal to payment_time
  google.protobuf.Timestamp txn_settlement_time = 7 [(validate.rules).timestamp.required = true];
  //if this is true, no amount will be allocated towards charges even if charges are pending, this can be passed as true when posting very old payments
  bool skip_charge_collection = 8;
}

message ProcessOffAppLoanRepaymentResponse {
  rpc.Status status = 1;
}

message RecordUserActionRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  LoanHeader loan_header = 2;

  // these can be used to identify user actions that needs to be done.
  // for eg: lrId, lseId can be used for performing user actions in loan application flow.
  // loan_id can be used for actions after disbursal, for eg: loan closure via CX.
  string loan_request_id = 3;
  string loan_step_id = 4;
  string loan_id = 7;

  frontend.deeplink.Screen screen = 5;
  // encoded action details
  string action_details = 6;
}

message RecordUserActionResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message SubmitIncomeVerificationOptionRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  LoanHeader loan_header = 2;
  string loan_request_id = 3;
  // id of the ListItem in LoansIncomeVerificationIntroScreenOptions
  string income_verification_option_id = 4;
}

message SubmitIncomeVerificationOptionResponse {
  rpc.Status status = 1;
  bool is_account_connected = 2;
  ItrInfo itr_info = 3;
  message ItrInfo {
    string current_attempt_id = 1;
    bool is_attempt_in_pending_state = 2;
  }
}

message GetCompletedLoanRequestsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  LoanRequestType loan_request_type = 2;
}

message GetCompletedLoanRequestsResponse {
  rpc.Status status = 1;
  // loan header can be constructed using data from loan request since it can be different for each loan request
  repeated LoanRequest completed_loan_requests = 3;
}

message GetActiveLoanRequestsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  LoanRequestType loan_request_type = 2;
}

message GetActiveLoanRequestsResponse {
  rpc.Status status = 1;
  // loan header can be constructed using data from loan request since it can be different for each loan request
  repeated LoanRequest active_loan_requests = 3;
}

message GetActiveLoanPaymentRequestsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  LoanPaymentRequestType loan_payment_request_type = 2;
}

message GetActiveLoanPaymentRequestsResponse {
  rpc.Status status = 1;
  // loan header can be constructed using data from loan request since it can be different for each loan request
  repeated LoanPaymentRequest active_loan_payment_requests = 2;
}

message GetDowntimeStatusRequest {
  LoanHeader loan_header = 1;
}

message GetDowntimeStatusResponse {
  rpc.Status status = 1;
  bool is_downtime = 2;
  // if is_downtime is true, then this field will be populated with the downtime end time
  google.protobuf.Timestamp next_available_time = 3;
}

message GetLoanDefaultDetailsRequest {
  LoanHeader loan_header = 1;
  string loan_account_id = 2;
}

message GetLoanDefaultDetailsResponse {
  rpc.Status status = 1;
  LoanAccount loan_account = 2;
  LoanInstallmentInfo loan_installment_info = 3;
  // if empty, then there are no defaults associated with the loan account
  LoanDefaultDetails default_details = 4;
  // Payout details
  repeated LoanInstallmentPayout schedule = 5;
  // Repayment account details
  api.typesv2.BankAccountDetails bank_account_details = 6;
  // Foreclosure details
  ForeclosureDetails foreclosure_details = 7;
  // contact details of user
  ContactDetails contact_details = 8;
  // user image data
  string user_image_link = 9;
}

message ContactDetails {
  // alternate contact number of user as stored in loan applicants.
  api.typesv2.common.PhoneNumber alternate_contact_number = 1;
}

message ForeclosureDetails {
  google.type.Money total_outstanding_amount = 1;
  google.type.Money principal_outstanding_amount = 2;
  google.type.Money interest_outstanding_amount = 3;
  google.type.Money penalty_amt = 4;
  google.type.Money fees_amt = 5;
  google.type.Money other_charges = 6;
  google.type.Money pre_close_charges = 7;
}

message LoanDefaultDetails {
  // first default date of the loan account
  // if multiple installments are in default, then first_default_date is the date of the first installment payout's due date
  google.type.Date first_default_date = 1;
  // aggregate amount of all the installment payouts of the loan account till current date
  // (P + I + C)
  google.type.Money total_due_amount = 2;
  // aggregated charges applied on all the installment payouts of the loan account till current date
  ChargesApplied charges_applied = 3;
  // Received amount - amount paid by the user
  google.type.Money received_amount = 4;
  // expected emi amount includes only the (P + I)
  google.type.Money expected_emi_amount = 5;
}

message GetLoanInstallmentPayoutDetailsRequest {
  LoanHeader loan_header = 1;
  // unique id of the loan installment payout
  string loan_installment_payout_id = 2;
}

message GetLoanInstallmentPayoutDetailsResponse {
  rpc.Status status = 1;
  LoanInstallmentPayout loan_installment_payout = 2;
  LoanInstallmentInfo loan_installment_info = 3;
  LoanAccount loan_account = 4;
}

message GetOfferDetailsRequest {
  string actor_id = 1;
  string offer_id = 2;
  google.type.Money loan_amount = 3;
  int32 tenure_in_months = 4;
  LoanHeader loan_header = 5;
  PledgeDetails pledge_details = 6;
  //  to be used when we need to calculate the offer info using a specific calculator
  // (optional) if not passed, then it'd be calculated using the calculators based on vendor and loan program
  enums.CalculatorAccuracy calculator_accuracy = 7;
}

message GetOfferDetailsResponse {
  // rpc response status
  rpc.Status status = 1;
  string offer_id = 2;
  OfferInfo offer_info = 3;
  LoanInfo loan_info = 4;
  Vendor vendor = 5;
  repeated preapprovedloan.LoanPlan loan_plans = 6;
  message OfferInfo {
    google.type.Money min_loan_amount = 1;
    google.type.Money max_loan_amount = 2;
    double interest_rate = 3;
    int32 min_tenure_in_months = 4;
    int32 max_tenure_in_months = 5;
    google.type.Money max_emi_amount = 6;
    double gst_percentage = 7;
    double apr_rate = 8;
    google.type.Date valid_till = 9;
    double processing_fee_percentage = 10;
    google.type.Money min_processing_fee = 12;
    google.type.Money max_processing_fee = 13;
    // this field will contain additional constraint details
    oneof additional_constraints {
      // constraint info for FIFTYFIN LAMF program
      FiftyFinLamfConstraints fiftyfin_lamf_constraints = 11;
    }
    // adding this new field as we send the loan_amount after rounding it off as of now.
    // this field will return the exact max loan amount in loan offer
    // explicitly assigning it 20 as 12-24 can be used in above oneof field
    google.type.Money exact_max_loan_amount = 25;
  }
  message LoanInfo {
    google.type.Money amount = 1;
    int32 tenure_in_months = 2;
    google.type.Money disbursal_amount = 3;
    google.type.Money emi_amount = 4;
    Deductions deductions = 5;
    Constrains constrains = 6;
    google.type.Money total_payable = 7;
    PledgeDetails pledge_details = 8;
    // applicable only for LAMF program
    google.type.Money min_monthly_due = 9;
    google.type.Date first_emi_due_date = 10;
    google.protobuf.Duration grace_period = 11;
    message Deductions {
      google.type.Money total_deductions = 1;
      google.type.Money gst = 2;
      google.type.Money processing_fee = 3;
      google.type.Money advance_interest = 4;
    }
    message Constrains {
      google.type.Money min_loan_amount = 1;
      google.type.Money max_loan_amount = 2;
      int32 min_tenure_in_months = 3;
      int32 max_tenure_in_months = 4;
      // this field will contain additional constraint details
      oneof additional_constraints {
        // constraint info for FIFTYFIN LAMF program
        FiftyFinLamfConstraints fiftyfin_lamf_constraints = 5;
      }
    }
  }

}

message FiftyFinLamfConstraints {
  MfPortfolioConstraint mf_portfolio_constraint = 1;
}

message GenerateConfirmationCodeRequest {
  string actor_id = 1;
  string loan_request_id = 2;
  LoanHeader loan_header = 3;
  // device needs to be passed for generating otp using internal auth service
  api.typesv2.common.Device device = 4;
  // *optional field*
  // to pass in case multiple OTPs need to be verified during the flow
  // lse is needed to determine for which step verification needs to be done
  string loan_step_execution_id = 5;
  frontend.preapprovedloan.pal_enums.OtpFlow otp_flow = 6;

}

message GenerateConfirmationCodeResponse {
  enum Status {
    STATUS_UNSPECIFIED = 0;

    // OTP sending failed permanently, no point in retrying
    STATUS_FAILED = 101;
    // User tried to re-send OTP to their phone too quickly
    STATUS_OTP_RESEND_BLOCKED = 102;
    // User temporarily blocked to prevent excessive OTP generations via retries
    STATUS_TEMP_BLOCKED = 103;
  }
  // rpc response status
  rpc.Status status = 1;
  LoanRequest loan_request = 2;
  // auth token to be used in OTP verification
  string token = 3;
  LoanStepExecution loan_step_execution = 4;
  string display_msg = 5;
}


message ConfirmApplicationRequest {
  string actor_id = 1;
  string loan_request_id = 2;
  string otp = 3;
  OtpFlow otp_flow = 4 [deprecated = true];
  LoanHeader loan_header = 5;
  string token = 6;
  api.typesv2.common.Device device = 7;
  // *optional field*
  // to pass in case multiple OTPs need to be verified during the flow
  // lse is needed to determine for which step verification needs to be done
  string loan_step_execution_id = 8;
  enum OtpFlow {
    OTP_FLOW_UNSPECIFIED = 0;
    OTP_FLOW_E_SIGN = 1;
  }
  frontend.preapprovedloan.pal_enums.OtpFlow otp_flow_identifier = 9;
}

message ConfirmApplicationResponse {
  enum Status {
    UNSPECIFIED = 0;
    OTP_EXPIRED = 100;
    INCORRECT_OTP = 101;
    // User temporarily blocked to prevent excessive OTP verifications via retries
    TEMP_BLOCKED = 102;
  }
  // rpc response status
  rpc.Status status = 1;
  LoanRequest loan_request = 2;
  LoanStepExecution loan_step = 3;
  // can be used to hint the caller that more OTPs left for user to enter
  // in case user has to enter multiple OTPs back-to-back.
  bool more_otp_left = 4;
  string display_msg = 5;
}

message ModifyLoanTermsRequest {
  string actor_id = 1;
  // revised offer id based on which the interest rate, amount, tenure, PF etc. were shown to the user
  string revised_offer_id = 2;
  google.type.Money loan_amount = 3;
  int32 tenure_in_months = 4;
  LoanHeader loan_header = 5;
  // loan_request_id of the active application for which the loan terms (amount and tenure) need to be modified
  string loan_request_id = 6;
}

message ModifyLoanTermsResponse {
  rpc.Status status = 1;
}

message ApplyForLoanRequest {
  string actor_id = 1;
  string offer_id = 2;
  google.type.Money loan_amount = 3;
  int32 tenure_in_months = 4;
  LoanHeader loan_header = 5;
  PledgeDetails pledge_details = 6;
  // location token we get from client side when we apply for loan.
  string location_token = 7;
  bool cancel_current_loan_request = 8;
}

message ApplyForLoanResponse {
  // rpc response status
  rpc.Status status = 1;
  // id of the newly created loan request for the chosen offer
  string loan_request_id = 2;
  LoanRespHeader resp_header = 3;
  // this will be populated when user has a currently active loan account
  string active_loan_account_id = 4;
  // loan request which is currently active in case the response status is AlreadyExists
  // loan request which was cancelled in case cancellation was requested and response status is OK
  LoanRequest active_loan_request = 5;
  // lse which is currently active in case the response status is AlreadyExists
  // lse which was cancelled in case cancellation was requested and response status is OK
  LoanStepExecution active_lse = 6;
}

message GetApplicationStatusRequest {
  string loan_request_id = 2;
  string actor_id = 1;
  LoanHeader loan_header = 3;
}

message GetApplicationStatusResponse {
  // rpc response status
  rpc.Status status = 1;
  LoanRequest loan_request = 2;
  LoanStepExecution current_execution = 3;
}

message GetLandingInfoRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
}

message GetLandingInfoResponse {
  rpc.Status status = 1;
  LoanOffer loan_offer = 2;
  LoanRequest active_loan_request = 3;
  LoanAccount active_loan_account = 4;
  EarlySalaryDetails early_salary_details = 5;

  enum Status {
    OK = 0;

    INTERNAL = 13;

    // User has never applied for loan, but have an active offer
    LOAN_OFFER = 101;

    // User have already applied for loan, so loan management status
    LOAN_APPLICATIONS = 102;

    // User have an active loan (where we show only loan details page not dashboard)
    LOAN_DETAILS = 103;

    // User doesn't have an active salary account
    NON_SALARY_USER = 104;
  }

  message EarlySalaryDetails {
    int32 past_loans_count = 1;
    repeated google.protobuf.Timestamp past_salary_timestamps = 2;
    int32 min_salary_credits = 3;
    int32 min_days_post_salary_credit = 4;
  }
}

message GetLandingInfoV2Request {
  string actor_id = 1;
  LoanHeader loan_header = 2;
  LoanType loan_type = 3;
  // Deprecated, refer to the enum definition for the reason
  repeated LandingInfoFilter landing_info_filters = 4 [deprecated = true];
}

message LoanOption {
  oneof loan_option_type {
    LoanOffer loan_offer = 1;
    // this means that the user can be evaluated for this vendor + loan program combination
    LoanHeader eligibility_header = 2;
  }
}

message GetLandingInfoV2Response {
  rpc.Status status = 1;
  // loan_offer is deprecated in favour of loan_offers
  // to support multiple offers landing page variants
  LoanOffer loan_offer = 2 [deprecated = true];
  LoanRequest active_loan_request = 3;
  LoanAccount active_loan_account = 4;
  LoanHeader loan_header = 5;
  rpc.Status landing_status = 6;
  repeated LoanOffer loan_offers = 7 [deprecated = true];
  // priority based ordered list of loan options
  repeated LoanOption loan_options = 8;
  repeated LoanHeader rejected_loan_offers = 9;


  // Use Status to show the status of the RPC call
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }

  // Use LandingStatus to convey to the client as to which page to show next.
  enum LandingStatus {
    LANDING_STATUS_UNSPECIFIED = 0;

    // The status codes for these enum values are same as the status codes for
    // the enum values in GetLandingInfoResponse message. This is done for
    // backwards compatibility with GetLandingInfoResponse.

    // User has never applied for loan, but have an active offer
    LANDING_STATUS_LOAN_OFFER = 101;

    // User has already applied for loan, so go to loan dashboard
    LANDING_STATUS_LOAN_APPLICATIONS = 102;

    // User has an option to check their real time loan eligibility
    // 103 and 104 are reserved for statuses from LandingInfo (v1). The handling right now is based on status code
    // Once (v1) is completely deprecated, 103, 104 are not used much and can be removed
    LANDING_STATUS_CHECK_LOAN_ELIGIBILITY = 105;
  }
}

message GetLandingInfoV3Request {
  string actor_id = 1;
  LoanHeader loan_header = 2;
  // Deprecated, refer to the enum definition for the reason
  repeated LandingInfoFilter landing_info_filters = 3 [deprecated = true];
}

message GetLandingInfoV3Response {
  rpc.Status status = 1;
  // loan account and respective LIIs sorted by created_at desc
  repeated LoanInfo loan_account_infos = 2;
  // loan request and respective LSEs sorted by created_at desc
  repeated LoanRequestInfo loan_request_infos = 3;
  // priority based ordered list of loan options
  repeated LoanOption loan_options = 4;
  repeated LoanHeader rejected_loan_offers = 5;
}

message GetLivenessStatusRequest {
  string loan_request_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message GetLivenessStatusResponse {
  rpc.Status status = 1;
  auth.liveness.LivenessAttempt liveness_attempt = 2;
  LoanRequest loan_request = 3;
}

message GetDashboardRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
  // if true, also return details for about eligibility flow (lr, lses)
  bool fetch_eligibility_request = 3;
}

message GetDashboardResponse {
  rpc.Status status = 1;
  LoanRequest recent_loan_request = 2;
  // A list containing details of each loan account along with installment details.
  repeated LoanInfo loan_info_list = 3;
  // Deprecated: CheckUserEligibility denotes if user is eligible to view a new loan offer. Deprecated in favour of active_loan_offer
  bool check_user_eligibility = 4 [deprecated = true];
  repeated LoanStepExecution loan_steps = 5;
  LoanOffer active_loan_offer = 6 [deprecated = true];
  LoanOfferEligibilityCriteria loec = 7 [deprecated = true];
  // priority based ordered list of loan options
  repeated LoanOption loan_options = 8;
}

message CancelApplicationRequest {
  string loan_request_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message CancelApplicationResponse {
  rpc.Status status = 1;
  // offer_id will be used to show offer details screen after cancelling application
  string offer_id = 2;
  // for new loan selection flow, a new amount selection screen is present that needs loan offer.
  // as in A2L we are directly calling loan selection screen upon cancellation, adding this data in response
  LoanOffer loan_offer = 3;
}

message InitiateESignRequest {
  string loan_request_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message InitiateESignResponse {
  rpc.Status status = 1;
  string sign_url = 2;
  string exit_url = 3;
}

message GetLoanDetailsRequest {
  string actor_id = 1;
  string loan_id = 2;
  LoanHeader loan_header = 3;

}

message GetLoanDetailsResponse {
  rpc.Status status = 1;
  LoanAccount loan_account = 2;
  LoanInstallmentInfo loan_installment_info = 3;
  LoanOffer loan_offer = 4;
  LoanRequest loan_request = 5;
  TransactionActivity transaction = 6;
  PreClosureDetails pre_closure_details = 7;
  repeated LoanInstallmentPayout loan_installment_payouts = 8;
  // if this field is populated in response that means alternate account flow was on for mandate stage for this actor
  // this field will not be populated if alternate account flow is not used during mandate stage
  DisbursalAndEmiAccountDetails disbursal_and_emi_account_details = 9;
  LoanCancellationDetails loan_cancellation_details = 10;

  message PreClosureDetails {
    google.type.Money loan_pre_close_amount = 1;
    google.type.Money loan_pre_close_charges = 2;
    bool is_pre_close_blocked = 3;
    // if is_pre_close_blocked is true, then this field will be populated with the reason why pre-closure is not allowed
    enums.PreCloseBlockedReason pre_close_blocked_reason = 4;
  }

  message DisbursalAndEmiAccountDetails {
    string last_four_digit_account_number = 1;
    string bank_name = 2;
  }
}

message LoanCancellationDetails {
  bool is_cancellation_allowed = 3;
  google.type.Money cancellation_amount = 2;
}

message PrePayLoanRequest {
  string actor_id = 1;
  string loan_id = 2;
  google.type.Money amount = 3;
  LoanHeader loan_header = 4;
  bool is_pre_close = 5;
  message UserIdentifier {
    string actor_id = 1;
    string account_id = 2;
    accounts.Type account_type = 3;
  }
  // optional field to be sent in case payer computation needs to be skipped
  UserIdentifier payer_user_identifier = 6;
  // used to check if prepayment via payment gateway need to be done
  bool use_pg_for_prepay = 7;
  // reserved for a deprecated field, do not use.
  reserved 8;
  // the account type against which payment is being made, eg - interest, principal
  LoanPaymentAccountType account_type = 9;
}

message PrePayLoanResponse {
  enum Status {
    OK = 0;
    // for cases when there is a known error scenario in pay API, e.g: user in cool down period etc
    // TODO bifurcate this into PAYMENT_TECHNICAL_FAILURE and PAYMENT_BUSINESS_FAILURE once pay supports
    KNOWN_ERROR_FROM_PAY = 101;
    // denotes the prepay is not allowed currently due to some existing payment on the loan account being in_progress state.
    FAILED_PRECONDITION_EXISTING_PAYMENT_IN_PROGRESS = 102;
    // denotes the prepay is not allowed due to payment falling in prepayment blackout period for the given loan account.
    FAILED_PRECONDITION_PREPAY_BLACKOUT_PERIOD = 103;
    // denotes failure in prepay due to expected delay (within SLA) in lms creation at vendor
    EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR = 104;
    // denotes that prepay is not allowed since it is done for the purpose of pre closure of the loan account in the blackout period.
    FAILED_PRECONDITION_PRE_CLOSURE_BLACKOUT_PERIOD = 105;
  }
  rpc.Status status = 1;
  // reference ID to be used for payment
  // could be order's client request ID to be used or authorisation of fund transfer
  string reference_id = 2;
  // transaction attributes needed by client to generate cred block to authenticate payment
  pay.attribute.TransactionAttribute transaction_attribute = 3;
  // order id needed by client for UPI flows
  string order_id = 4;
  // error code returned by pay API, used for constructing detailed error views for user
  string pay_error_code_for_payer = 5;
  // will denote the expiration time of the payment request.
  // if the order is not created in the payments service post this time then the payment request can be considered at expired/failed
  // but if the order is created and is in an in_progress state, then the payment request shouldn't be considered as expired/failed.
  google.protobuf.Timestamp request_expiry_time = 6;

  // payment link to be used for payment
  string payment_url = 7;
  // payment request id to be used for payment
  string loan_payment_request_id = 8;
}

message GetLoanActivityStatusRequest {
  string ref_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message GetLoanActivityStatusResponse {
  rpc.Status status = 1;
  ActivityStatus activity_status = 2;
  string loan_account_id = 3;
  enum ActivityStatus {
    UNSPECIFIED = 0;
    PENDING = 1;
    COMPLETED = 2;
    FAILED = 3;
  }
}

message GetAllTransactionsRequest {
  string loan_account_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message GetAllTransactionsResponse {
  rpc.Status status = 1;
  repeated TransactionActivity transactions = 2;
}

message TransactionActivity {
  google.protobuf.Timestamp payment_timestamp = 1;
  Status status = 2;
  Details details = 3;
  string failure_message = 4;
  string utr = 5;
  string loan_activity_id = 6;
  LoanHeader loan_header = 7;
  string txn_particulars = 8;

  enum Status {
    UNSPECIFIED = 0;
    SUCCESS = 1;
    FAILED = 2;
    PROCESSING = 3;
  }

  message Details {
    Type type = 1;
    google.type.Money amount = 2;
    // Only in case of type EMI, else 0
    int32 emi_number = 3;
    enum Type {
      UNSPECIFIED = 0;
      EMI = 1;
      LUMPSUM = 2;
      LATE_FEE = 3;
      DISBURSEMENT = 4;
    }
  }
}

message GetTransactionReceiptRequest {
  string loan_activity_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message GetTransactionReceiptResponse {
  rpc.Status status = 1;
  TransactionActivity transaction = 2;
  string loan_name = 3;
}

message GetLoanAccountDetailsRequest {
  string actor_id = 1;
  repeated string loan_account_ids = 2;
  LoanHeader loan_header = 3;
}

message GetLoanAccountDetailsResponse {
  rpc.Status status = 1;
  repeated LoanDetail loan_details = 2;
}

message GetLoanOffersRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
  bool get_best_offer = 3;
}

message GetLoanOffersResponse {
  rpc.Status status = 1;
  repeated Offer loan_offers = 2;
  // loan offer entity
  repeated LoanOffer stored_loan_offers = 3;
  LoanOffer best_offer = 4;
}

message GetRecentLoanApplicationDetailsRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
}

message GetRecentLoanApplicationDetailsResponse {
  rpc.Status status = 1;
  repeated Application applications = 2;
}

message GetLoanTransactionsRequest {
  string actor_id = 1;
  repeated string loan_account_ids = 2;
  // for pagination
  rpc.PageContextRequest page_context = 3;
  LoanHeader loan_header = 4;
}

message GetLoanTransactionsResponse {
  rpc.Status status = 1;
  repeated Transaction transactions = 2;
  // for pagination
  rpc.PageContextResponse page_context = 3;
}
message GetLoanSummaryForHomeRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
}

message GetLoanSummaryForHomeResponse {
  rpc.Status status = 1;
  HomeCardType home_card_type = 2;
  oneof HomeCardData {
    // for active loan account sending installment info to calculate emi related info
    CardActiveLoanAccount card_active_loan_account = 3;
    // for loan application sending loan request to determine application status
    CardLoanApplication card_loan_application = 4;
    CardLoanOffer card_loan_offer = 5;
  }
  enum HomeCardType {
    HOME_CARD_TYPE_UNSPECIFIED = 0;
    HOME_CARD_TYPE_LOAN_ACCOUNT_ACTIVE = 1;
    HOME_CARD_TYPE_LOAN_APPLICATION = 2;
    HOME_CARD_TYPE_LOAN_OFFER = 3;
    HOME_CARD_TYPE_NO_OFFER = 4;
    HOME_CARD_TYPE_CHECK_ELIGIBILITY = 5;
  }
  message CardLoanOffer {
    LoanOffer loan_offer = 1;
    bool past_loans_application_record = 2;
  }
  message CardActiveLoanAccount {
    repeated LoanAccountInfo loan_account_infos = 1;
    LoanOffer loan_offer = 2;
    EmiTimelineState emi_timeline_state = 3;
    // used to check if mandate is set up on account other than fed savings account
    bool is_mandate_on_fi_account = 4;
  }
  message CardLoanApplication {
    LoanRequest loan_request = 1;
  }
  message LoanAccountInfo {
    LoanAccount loan_account = 1;
    LoanInstallmentInfo loan_installment_info = 2;
  }
  // this denotes possible states of home card, in pre-payment period
  // this will only be present for HOME_CARD_TYPE_LOAN_ACCOUNT_ACTIVE
  enum EmiTimelineState {
    EMI_TIMELINE_STATE_UNSPECIFIED = 0;
    // this represents time  period till which a user can prepay their emi
    EMI_TIMELINE_STATE_EARLY_PRE_PAYMENT = 1;
    // this represents period before the due date during which pre payment is blocked
    EMI_TIMELINE_STATE_PRE_DUE = 2;
  }
}

message EstimateCreditUtilisedRequest {
  string actor_id = 1;
  preapprovedloan.Vendor vendor = 2;
  LoanHeader loan_header = 3;
}

message EstimateCreditUtilisedResponse {
  rpc.Status status = 1;
  google.type.Money credit_utilised = 2;
}

message AddAddressDetailsRequest {
  string loan_request_id = 1;
  api.typesv2.PostalAddress address = 2;
  string actor_id = 3;
  LoanHeader loan_header = 4;
  // location token we get from client side when adding address details.
  string location_token = 5;
}

message AddAddressDetailsResponse {
  rpc.Status status = 1;
}

message AddEmploymentDetailsRequest {
  string loan_request_id = 1;
  api.typesv2.EmploymentType occupation_type = 2;
  string organization_name = 3;
  google.type.Money monthly_income = 4;
  string actor_id = 5;
  string work_email = 6;
  LoanHeader loan_header = 7;
  api.typesv2.PostalAddress office_address = 8;
  google.type.Money desired_loan_amount = 9;
}

message AddEmploymentDetailsResponse {
  rpc.Status status = 1;
}

message ConfirmRevisedLoanDetailsRequest {
  string loan_request_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message ConfirmRevisedLoanDetailsResponse {
  rpc.Status status = 1;
  LoanRequest loan_request = 2;
}

message ClientCallbackRequest {
  string actor_id = 1;
  string loan_request_id = 2 [deprecated = true];
  Type type = 3;
  Result result = 4;
  int32 vendor_code = 5;
  LoanHeader loan_header = 6;
  // contains payload regarding call back events like mandate sdk
  CallbackPayload callback_payload = 7;
  // denotes client app platform
  api.typesv2.common.Platform device_platform = 8;
  // Since this is a generic api, we don't want to couple it with just one identifier (i.e. loan_request_id)
  // and hence deprecated this field with backward compatible service changes
  // For newer implementations, need to use these identifiers
  oneof Identifier {
    // intentional index gap

    // e.g. loanId, loanRequestId, etc
    string loan_req_id = 10;
    // e.g. orchId, clientRequestID, etc
    string step_orch_id = 11;
    // mandatory when type is OFFER_VIEWED
    string loan_offer_id = 12;
  }

  enum Type {
    TYPE_UNSPECIFIED = 0;
    MANDATE = 1;
    E_SIGN = 2;
    SI = 3;
    // denotes that offer was shown to the user on S1/S2 screen ONLY
    OFFER_VIEWED = 4;
  }

  enum Result {
    RESULT_UNSPECIFIED = 0;
    SUCCESS = 1;
    FAILED = 2;
    PENDING = 3;
    INITIATED = 4;
  }
}

message CallbackPayload {
  oneof paylaod {
    MandatePayload mandate_payload = 1;
  }
}

message MandatePayload {
  api.typesv2.deeplink_screen_option.preapprovedloans.MandateViewType mandate_view_type = 1;
  api.typesv2.deeplink_screen_option.preapprovedloans.MandateSdkVendor mandate_sdk_vendor = 2;
  MandateSdkEvent mandate_sdk_event = 3;
}

message MandateSdkEvent {
  // expecting json here which will be unmarshalled to the respective event type eg digiosdksuccessresponse,digiosdkfailureresponse and digiosdkgateway
  string event_payload = 1;
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_SUCCESS = 1;
    EVENT_TYPE_FAILURE = 2;
    EVENT_TYPE_GATEWAY = 3;
  }
  EventType event_type = 2;
}

message ClientCallbackResponse {
  rpc.Status status = 1;
}

message LoanRespHeader {
  LoanProgram loan_program = 1;
  Vendor vendor = 2;
}

message VerifyDetailsRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  api.typesv2.deeplink_screen_option.preapprovedloans.DetailsType details_type = 3;
  string value = 4;
  api.typesv2.EmploymentType employment_type = 5;
  string req_id = 6;
  string lse_id = 7;
  UserInput user_input = 8;
}

message UserInput {
  oneof user_input {
    PhoneAndEmailDetails phone_and_email_details = 1;
    AdditionalKycDetails additional_kyc_details = 2;
    SelfieDetails selfie_details = 3;
    FormDetails form_details = 4;
  }
}

message FormDetails {
  repeated api.typesv2.FormField form_fields = 1;
}

message PhoneAndEmailDetails {
  api.typesv2.common.PhoneNumber phone_number = 1;
  string email = 2;
}

message AdditionalKycDetails {
  string employment_type = 1;
  string marital_status = 2;
  string residence_type = 3;
  string father_name = 4;
  string mother_name = 5;
}

message SelfieDetails {
  api.typesv2.common.Image selfie_image = 1;
}

message VerifyDetailsResponse {
  rpc.Status status = 1;
}

message VerifyCkycDetailsRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_req_id = 3;
  string location_token = 4;
}

message VerifyCkycDetailsResponse {
  rpc.Status status = 1;
}

message GetEarlySalaryDetailsRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
}

message GetEarlySalaryDetailsResponse {
  // RPC status
  rpc.Status status = 1;
  // Overall user's early salary state
  SalaryState salary_state = 2;
  // If already availed a loan, contains data for repayment details
  RepaymentDetails repayment_details = 3;
  // details that need to be show on banner
  BannerDetails banner_details = 4;

  message BannerDetails {
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=20928-42343&t=17cOXIJd0Iyw2KYa-4
    // Message to be shown at the bottom of early salary banner
    // If empty string, can remove the text at all, e.g.- https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=20928-42323&t=17cOXIJd0Iyw2KYa-4
    string bottom_msg = 1;
  }

  // e.g. to show "₹30,250 repayment for Instant Salary is due on 05 June 2023"
  // populated when in ACTIVE state
  message RepaymentDetails {
    google.type.Date due_date = 1;
    google.type.Money due_amount = 2;
  }

  enum SalaryState {
    SALARY_STATE_UNSPECIFIED = 0;
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=20928-42139&t=ZThofUg1TmBHi7OM-4
    INELIGIBLE = 1;
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=20923-42648&t=ZThofUg1TmBHi7OM-4
    ELIGIBLE = 2;
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?type=design&node-id=20923-42648&t=ZThofUg1TmBHi7OM-4
    // Early salary loan in-progress/availed currently
    // Repayment Details populated when in this state
    ACTIVE = 3;
  }
}

message RefreshLmsScheduleRequest {
  // actor id
  string actor_id = 1;
  // Loan header
  LoanHeader loan_header = 2;
}

message RefreshLmsScheduleResponse {
  enum Status {
    OK = 0;
    // denotes failure in LMS refresh due to expected delay (within SLA) in lms creation at vendor
    EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR = 101;
  }
  // status
  rpc.Status status = 1;
}

message GetMandateViewDataRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_request_id = 3;
}

message GetMandateViewDataResponse {
  rpc.Status status = 1;
  string base64EncodedHtml = 2;
}

message ExecuteCollectionRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
}

message ExecuteCollectionResponse {
  rpc.Status status = 1;
  repeated string loan_payment_request_id = 2;
}

message ReconLoanActivityRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
}

message ReconLoanActivityResponse {
  rpc.Status status = 1;
}

message GetLoanScheduleRequest {
  LoanHeader loan_header = 1;
  string loan_id = 2;
}

message GetLoanScheduleResponse {
  repeated LoanInstallmentPayout schedule = 1;
  rpc.Status status = 2;
}

message AddPanAndDobDataRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string pan = 3;
  google.type.Date dob = 4;
  string req_id = 5;
}

message AddPanAndDobDataResponse {
  rpc.Status status = 1;
}

message AddNameAndGenderRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_req_id = 3;
  string name = 4;
  api.typesv2.Gender gender = 5;
}

message AddNameAndGenderResponse {
  rpc.Status status = 1;
}

message CheckLoanEligibilityRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
  bool cancel_current_loan_request = 3;
}

message CheckLoanEligibilityResponse {
  // rpc response status
  rpc.Status status = 1;
  LoanRespHeader resp_header = 2;
  // id of the newly created loan request for orchestrating eligibility
  string loan_request_id = 3;
  // loan request which is currently active in case the response status is AlreadyExists
  // loan request which was cancelled in case cancellation was requested and response status is OK
  LoanRequest active_loan_request = 4;
  // lse which is currently active in case the response status is AlreadyExists
  // lse which was cancelled in case cancellation was requested and response status is OK
  LoanStepExecution active_lse = 5;
  // this will be populated when user has a currently active loan account
  string active_loan_account_id = 6;
}

message FetchCreditReportRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_req_id = 3;
  string loan_step_execution_id = 4;
  api.typesv2.deeplink_screen_option.preapprovedloans.CreditReportVendor credit_report_vendor = 5;
}

message FetchCreditReportResponse {
  rpc.Status status = 1;
}

message AddBankingDetailsRequest {
  string loan_request_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
  BankingDetails banking_details = 4;
  // if lse id is present then identify loan step via this id
  // this will allow this RPC to be used in generic manner
  // across various banking details flow in lending
  // if lse id is not present then use loan_request_id and actor_id
  // which will make this change backward compatible
  string lse_id = 5;
  message BankingDetails {
    string account_number = 1 [deprecated = true];
    string account_holder_name = 2;
    string ifsc_code = 3;
    string bank_name = 4;
    oneof account_num {
      string complete_account_number = 5;
      string account_number_prefix = 6;
    }
  }
}


message AddBankingDetailsResponse {
  enum Status {
    OK = 0;
    // BANK_NOT_SUPPORTED denotes if banking details shared by the user is not allowed by us
    // This can be due to several reasons like collections, compliance, etc
    BANK_NOT_SUPPORTED = 101;
  }

  rpc.Status status = 1;
  // FE deeplink to be returned use-case by basis
  // this will allow deeplink logic to stay at a central place in backend
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetLoanReviewDetailsRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_request_id = 3;
}

message GetLoanReviewDetailsResponse {
  rpc.Status status = 1;
  PersonalDetails personal_details = 2;
  ContactDetails contact_details = 3;
  OnboardingData onboarding_data = 4;
  google.type.Money desired_loan_amount = 5;
  preapprovedloan.enums.LoanPurpose loan_purpose = 6;

  message PersonalDetails {
    api.typesv2.common.Name name = 1;
    string pan = 2;
    google.type.Date dob = 3;
    api.typesv2.Gender gender = 4;
    api.typesv2.MaritalStatus marital_status = 5;
  }

  message ContactDetails {
    string email_id = 1;
    api.typesv2.common.PhoneNumber phone_number = 2;
  }
}

message SubmitReviewLoanDetailsRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_request_id = 3;
}

message SubmitReviewLoanDetailsResponse {
  rpc.Status status = 1;
}

message GetLoanUserDetailsRequest {
  // this is not a mandatory field. If left unspecified, will search in all the DBs and fetch info whatever is available.
  // if info not present, accordingly will return nil values
  LoanHeader loan_header = 1 [deprecated = true];
  string actor_id = 2;
  LoanUserDetailsType loan_user_details_type = 3;
  // should be specified if loan user details need to be fetched from the data owned by a specific entity
  // otherwise it will return the latest details from the data of all entities
  api.typesv2.common.Owner owner = 4;
}

message GetLoanUserDetailsResponse {
  rpc.Status status = 1;
  oneof loan_user_details {
    // intentionally leaving some gap in numbering
    LoanUserKycDetails kyc_details = 5;
  }
}

message GetLoanIdByHeaderAndActorIdRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
}

message GetLoanIdByHeaderAndActorIdResponse {
  rpc.Status status = 1;
  string loan_id = 2;
}

message InitiateSiSetupRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
  string loan_account_id = 3;
}

message InitiateSiSetupResponse {
  rpc.Status status = 1;
  string loan_request_id = 2;
}

message GetVendorApplicantIdRequest {
  LoanHeader loan_header = 1;
  string actor_id = 2;
}

message GetVendorApplicantIdResponse {
  rpc.Status status = 1;
  string vendor_applicant_id = 2;
}

message GetMandateViewDataV2Request {
  string actor_id = 1;
  LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string lse_id = 4;
  MandateDetails mandate_details = 5;
  message MandateDetails {
    // user selected bank account for mandate coming from the client
    api.typesv2.deeplink_screen_option.preapprovedloans.MandateBankAccountDetails bank_account_details = 1;
    // user selected VPA authentication method for e-nach
    recurringpayment.enach.enums.EnachRegistrationAuthMode auth_type = 2;
  }
}

message GetMandateViewDataV2Response {
  rpc.Status status = 1;
  // FE deeplink to be returned use-case by basis
  frontend.deeplink.Deeplink deeplink = 2;
  // this error msg can be used FE to surface more contextual error messages to the customer
  string user_facing_err_msg = 3;
}

message GetPWARedirectionDetailsRequest {
  LoanHeader loan_header = 1;
  // denotes the unique identifier for the loan request/account for which the pwa redirection details needs to be fetched.
  oneof loan_identifier {
    string loan_request_id = 2;
    string loan_account_id = 3;
  }

}

message GetPWARedirectionDetailsResponse {
  rpc.Status status = 1;
  // denotes the url where the user needs to be redirected to for the pwa flow.
  string pwa_url = 2;
}

message GetLoansUserStatusRequest {
  string actor_id = 1;
}

message GetLoansUserStatusResponse {
  rpc.Status status = 1;
  LoansUserStatus loans_user_status = 2;
  // ActiveSince is the timestamp at which the user became an active loans user
  // This filed will be populated only if LoansUserStatus is Active (active account or active request)
  // This will be equal to the created_at of the latest loan request (if it is in success or any non-terminal status)
  google.protobuf.Timestamp activated_at = 3;
  bool is_loan_overdue = 4;
}

message GetApplicationStatusSyncRequest {
  LoanHeader loan_header = 1 [(validate.rules).message.required = true];
  string actor_id = 2;
  string loan_request_id = 3;
  // deprecated as this can be fetched from the loan request id
  string workflow_id = 4 [deprecated = true];
}

message GetApplicationStatusSyncResponse {
  rpc.Status status = 1;
  LoanRequest loan_request = 2;
  frontend.deeplink.Deeplink next_action = 3;
}

message GetIncomeVerificationInfoRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  LoanHeader loan_header = 2;
  string loan_request_id = 3;
}

message GetIncomeVerificationInfoResponse {
  rpc.Status status = 1;
  ItrInfo itr_info = 2;
  message ItrInfo {
    bool is_max_attempts_reached = 1;
  }
}

message GetForeclosureDetailsRequest {
  // optional loan header, if passed then only vendor specific account details will be fetched
  // if not passed then loan_account_id will be used to fetch loan account details
  LoanHeader loan_header = 1;
  // loan account id for which foreclosure details has to be fetched
  string loan_account_id = 2 [(validate.rules).string.min_len = 1];
}

message GetForeclosureDetailsResponse {
  rpc.Status status = 1;
  Details details = 2;
  message Details {
    // pending amount which has to be paid to foreclose the loan
    google.type.Money foreclosure_amount = 1;
  }
}

message InitiateLoanClosureRequest {
  // optional loan header, if passed then only vendor specific account details will be fetched
  // if not passed then loan_account_id will be used to fetch loan account details
  LoanHeader loan_header = 1;
  string loan_account_id = 2 [(validate.rules).string.min_len = 1];
  // loan payment request id denotes payment which was already done to foreclose the loan.
  string loan_payment_request_id = 3 [(validate.rules).string.min_len = 1];
}

message InitiateLoanClosureResponse {
  rpc.Status status = 1;
  // loan request id identifies closure workflow triggered in the backend
  string loan_request_id = 2;
}
message GenerateEsignAgreementRequest {
  // optional loan header but always good to pass, if passed then only the corresponding DB will be queried
  // if not passed then all the DBs will be queried to find the loan request
  LoanHeader loan_header = 1;
  string loan_request_id = 2 [(validate.rules).string.min_len = 1];
  LoanDocType loan_doc_type = 3;
}
message GenerateEsignAgreementResponse {
  rpc.Status status = 1;
  string signed_url = 2;
}

message GetPrePayDetailsRequest {
  string loan_id = 1;
  string actor_id = 2;
  LoanHeader loan_header = 3;
}

message GetPrePayDetailsResponse {
  rpc.Status status = 1;
  ForeclosureDetails foreclosure_details = 2;
  LoanAccount loan_account = 3;
  // this will represent monthly emi amount
  google.type.Money upcoming_emi = 4;
  // this will represent amount user needs to pay for that month,
  // for eg: if upcoming emi was not paid on due date it can include remaining unpaid amount  alongwith current emi amount
  google.type.Money over_due_amount = 5;
  LoanCancellationDetails loan_cancellation_details = 6;
  // this will represent the last loan payment request made by the user
  LoanPaymentRequest latest_loan_payment_request = 7;
}

message SaveContactDetailsRequest {
  string lse_id = 1;
  LoanHeader loan_header = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
}

message SaveContactDetailsResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetAAConsentCollectionDetailsRequest {
  string actor_id = 1;
}

message GetAAConsentCollectionDetailsResponse {
  rpc.Status status = 1;
  // will be true if user has at least 1 AA account linked
  bool account_linked = 2;
  // will be true if user has already given consent for sharing AA data for lending
  bool consent_given = 3;
  // time when last consent collection was attempted for the user.
  // NOTE: This will be present only if consent has not been given by the user.
  google.protobuf.Timestamp last_consent_attempt = 4;
}

message GetApplicantByVendorApplicantIdRequest {
  LoanHeader loan_header = 1;
  string vendor_applicant_id = 2;
}

message GetApplicantByVendorApplicantIdResponse {
  rpc.Status status = 1;
  LoanApplicant loan_applicant = 2;
}

message GetFederalLoanCustomerDetailsRequest {
  string actor_id = 1;
}

message GetFederalLoanCustomerDetailsResponse {
  rpc.Status status = 1;
  string bre_ref_number = 2;
}
