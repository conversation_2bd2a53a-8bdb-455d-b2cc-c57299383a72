// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/usstocks/enums.proto

package usstocks

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//go:generate gen_sql -types=AggregatedRemittanceTransactionType,RemittanceType,AggregatedRemittanceTransactionState,OrderState,OrderSide,WalletOrderType,WalletOrderStatus,WalletOrderSubType,RemittanceProcessStatus,OrderFundingType,Vendor,OrderType,TimeInForce
type OrderState int32

const (
	OrderState_ORDER_STATE_UNSPECIFIED OrderState = 0
	// signifies new order, which is not yet picked for processing
	OrderState_ORDER_CREATED OrderState = 1
	// signifies order is under processing
	OrderState_ORDER_INITIATED OrderState = 2
	// successful allocation/sell of US Stocks is done.
	// This state marks the completion of buy order and workflow is completed
	// It is possible that order has been fulfilled by broker and few checks are required to be completed
	// so, SELL option on UI/BE should be disabled until order transitions to ORDER_SUCCESS state
	OrderState_ORDER_SUCCESS OrderState = 3
	// order failed. This is terminal state for order
	OrderState_ORDER_FAILED OrderState = 4
	// order processing failed with some unexpected error, needs manual intervention
	OrderState_ORDER_MANUAL_INTERVENTION OrderState = 5
	// order is canceled by user and it is being cancel at vendor side
	OrderState_ORDER_CANCELED OrderState = 6
	// order was not initiated completely
	// eg: starting IFT workflow resulted into ISE
	// orders with state ORDER_INITIATION_FAILED should not be displayed to user
	OrderState_ORDER_INITIATION_FAILED OrderState = 7
	// this would happen if the order could not be executed within the expected time
	// e.g. in case of limit order if time in force is 1 DAY, and the stock's market price did not reach to limit price, then order will expire
	OrderState_ORDER_EXPIRED OrderState = 8
)

// Enum value maps for OrderState.
var (
	OrderState_name = map[int32]string{
		0: "ORDER_STATE_UNSPECIFIED",
		1: "ORDER_CREATED",
		2: "ORDER_INITIATED",
		3: "ORDER_SUCCESS",
		4: "ORDER_FAILED",
		5: "ORDER_MANUAL_INTERVENTION",
		6: "ORDER_CANCELED",
		7: "ORDER_INITIATION_FAILED",
		8: "ORDER_EXPIRED",
	}
	OrderState_value = map[string]int32{
		"ORDER_STATE_UNSPECIFIED":   0,
		"ORDER_CREATED":             1,
		"ORDER_INITIATED":           2,
		"ORDER_SUCCESS":             3,
		"ORDER_FAILED":              4,
		"ORDER_MANUAL_INTERVENTION": 5,
		"ORDER_CANCELED":            6,
		"ORDER_INITIATION_FAILED":   7,
		"ORDER_EXPIRED":             8,
	}
)

func (x OrderState) Enum() *OrderState {
	p := new(OrderState)
	*p = x
	return p
}

func (x OrderState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[0].Descriptor()
}

func (OrderState) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[0]
}

func (x OrderState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderState.Descriptor instead.
func (OrderState) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{0}
}

type OrderSide int32

const (
	OrderSide_ORDER_SIDE_UNSPECIFIED OrderSide = 0
	OrderSide_BUY                    OrderSide = 1
	OrderSide_SELL                   OrderSide = 2
)

// Enum value maps for OrderSide.
var (
	OrderSide_name = map[int32]string{
		0: "ORDER_SIDE_UNSPECIFIED",
		1: "BUY",
		2: "SELL",
	}
	OrderSide_value = map[string]int32{
		"ORDER_SIDE_UNSPECIFIED": 0,
		"BUY":                    1,
		"SELL":                   2,
	}
)

func (x OrderSide) Enum() *OrderSide {
	p := new(OrderSide)
	*p = x
	return p
}

func (x OrderSide) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderSide) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[1].Descriptor()
}

func (OrderSide) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[1]
}

func (x OrderSide) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderSide.Descriptor instead.
func (OrderSide) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{1}
}

type WalletOrderType int32

const (
	WalletOrderType_WALLET_ORDER_TYPE_UNSPECIFIED WalletOrderType = 0
	// order is placed for adding funds to wallet
	WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS WalletOrderType = 1
	// order is placed for withdrawing funds from wallet
	WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS WalletOrderType = 2
)

// Enum value maps for WalletOrderType.
var (
	WalletOrderType_name = map[int32]string{
		0: "WALLET_ORDER_TYPE_UNSPECIFIED",
		1: "WALLET_ORDER_TYPE_ADD_FUNDS",
		2: "WALLET_ORDER_TYPE_WITHDRAW_FUNDS",
	}
	WalletOrderType_value = map[string]int32{
		"WALLET_ORDER_TYPE_UNSPECIFIED":    0,
		"WALLET_ORDER_TYPE_ADD_FUNDS":      1,
		"WALLET_ORDER_TYPE_WITHDRAW_FUNDS": 2,
	}
)

func (x WalletOrderType) Enum() *WalletOrderType {
	p := new(WalletOrderType)
	*p = x
	return p
}

func (x WalletOrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WalletOrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[2].Descriptor()
}

func (WalletOrderType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[2]
}

func (x WalletOrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WalletOrderType.Descriptor instead.
func (WalletOrderType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{2}
}

type WalletOrderSubType int32

const (
	WalletOrderSubType_WALLET_ORDER_SUB_TYPE_UNSPECIFIED WalletOrderSubType = 0
	// For adding funds and placing trades directly with the stockbroker
	//
	// Deprecated: Marked as deprecated in api/usstocks/enums.proto.
	WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_INSTANT_WALLET_FUNDING WalletOrderSubType = 1
	// For adding funds to US stocks wallet
	WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_NON_INSTANT_WALLET_FUNDING WalletOrderSubType = 2
	// For rewarding user by adding funds to user's US stocks wallet,
	// via moving cash from Epifi's firm account to user's wallet account
	WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_REWARDS WalletOrderSubType = 3
	// For withdrawing funds from US stocks wallet
	WalletOrderSubType_WALLET_ORDER_SUB_TYPE_NON_INSTANT_WALLET_WITHDRAWAL WalletOrderSubType = 4
	// For adding funds to wallet for SIPs created by the user
	WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP WalletOrderSubType = 5
)

// Enum value maps for WalletOrderSubType.
var (
	WalletOrderSubType_name = map[int32]string{
		0: "WALLET_ORDER_SUB_TYPE_UNSPECIFIED",
		1: "WALLET_ORDER_SUB_TYPE_ADD_FUNDS_INSTANT_WALLET_FUNDING",
		2: "WALLET_ORDER_SUB_TYPE_ADD_FUNDS_NON_INSTANT_WALLET_FUNDING",
		3: "WALLET_ORDER_SUB_TYPE_ADD_FUNDS_REWARDS",
		4: "WALLET_ORDER_SUB_TYPE_NON_INSTANT_WALLET_WITHDRAWAL",
		5: "WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP",
	}
	WalletOrderSubType_value = map[string]int32{
		"WALLET_ORDER_SUB_TYPE_UNSPECIFIED":                          0,
		"WALLET_ORDER_SUB_TYPE_ADD_FUNDS_INSTANT_WALLET_FUNDING":     1,
		"WALLET_ORDER_SUB_TYPE_ADD_FUNDS_NON_INSTANT_WALLET_FUNDING": 2,
		"WALLET_ORDER_SUB_TYPE_ADD_FUNDS_REWARDS":                    3,
		"WALLET_ORDER_SUB_TYPE_NON_INSTANT_WALLET_WITHDRAWAL":        4,
		"WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP":                        5,
	}
)

func (x WalletOrderSubType) Enum() *WalletOrderSubType {
	p := new(WalletOrderSubType)
	*p = x
	return p
}

func (x WalletOrderSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WalletOrderSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[3].Descriptor()
}

func (WalletOrderSubType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[3]
}

func (x WalletOrderSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WalletOrderSubType.Descriptor instead.
func (WalletOrderSubType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{3}
}

type WalletOrderStatus int32

const (
	WalletOrderStatus_WALLET_ORDER_STATUS_UNSPECIFIED WalletOrderStatus = 0
	// signifies new order, which is not yet picked for processing
	WalletOrderStatus_WALLET_ORDER_STATUS_CREATED WalletOrderStatus = 1
	// signifies order is under processing
	WalletOrderStatus_WALLET_ORDER_STATUS_INITIATED WalletOrderStatus = 2
	// successful allocation/sell of US Stocks is done.
	// This state marks the completion of add/withdraw funds order and workflow is completed
	WalletOrderStatus_WALLET_ORDER_STATUS_SUCCESS WalletOrderStatus = 3
	// order failed. This is terminal state for order
	WalletOrderStatus_WALLET_ORDER_STATUS_FAILED WalletOrderStatus = 4
	// order processing failed with some unexpected error, needs manual intervention
	WalletOrderStatus_WALLET_ORDER_STATUS_MANUAL_INTERVENTION WalletOrderStatus = 5
	// order was not initiated completely
	// eg: starting IFT workflow resulted into ISE
	// orders with state WALLET_ORDER_STATUS_INITIATION_FAILED should not be displayed to user
	WalletOrderStatus_WALLET_ORDER_STATUS_INITIATION_FAILED WalletOrderStatus = 6
)

// Enum value maps for WalletOrderStatus.
var (
	WalletOrderStatus_name = map[int32]string{
		0: "WALLET_ORDER_STATUS_UNSPECIFIED",
		1: "WALLET_ORDER_STATUS_CREATED",
		2: "WALLET_ORDER_STATUS_INITIATED",
		3: "WALLET_ORDER_STATUS_SUCCESS",
		4: "WALLET_ORDER_STATUS_FAILED",
		5: "WALLET_ORDER_STATUS_MANUAL_INTERVENTION",
		6: "WALLET_ORDER_STATUS_INITIATION_FAILED",
	}
	WalletOrderStatus_value = map[string]int32{
		"WALLET_ORDER_STATUS_UNSPECIFIED":         0,
		"WALLET_ORDER_STATUS_CREATED":             1,
		"WALLET_ORDER_STATUS_INITIATED":           2,
		"WALLET_ORDER_STATUS_SUCCESS":             3,
		"WALLET_ORDER_STATUS_FAILED":              4,
		"WALLET_ORDER_STATUS_MANUAL_INTERVENTION": 5,
		"WALLET_ORDER_STATUS_INITIATION_FAILED":   6,
	}
)

func (x WalletOrderStatus) Enum() *WalletOrderStatus {
	p := new(WalletOrderStatus)
	*p = x
	return p
}

func (x WalletOrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WalletOrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[4].Descriptor()
}

func (WalletOrderStatus) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[4]
}

func (x WalletOrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WalletOrderStatus.Descriptor instead.
func (WalletOrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{4}
}

type Vendor int32

const (
	Vendor_VENDOR_UNSPECIFIED Vendor = 0
	Vendor_ALPACA             Vendor = 1
)

// Enum value maps for Vendor.
var (
	Vendor_name = map[int32]string{
		0: "VENDOR_UNSPECIFIED",
		1: "ALPACA",
	}
	Vendor_value = map[string]int32{
		"VENDOR_UNSPECIFIED": 0,
		"ALPACA":             1,
	}
)

func (x Vendor) Enum() *Vendor {
	p := new(Vendor)
	*p = x
	return p
}

func (x Vendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[5].Descriptor()
}

func (Vendor) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[5]
}

func (x Vendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vendor.Descriptor instead.
func (Vendor) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{5}
}

type PortfolioSortOptionType int32

const (
	PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED PortfolioSortOptionType = 0
	// Sort By Market value of investment
	PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_MARKET_VALUE PortfolioSortOptionType = 1
	// Sort By invested amount
	PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_INVESTED_AMOUNT PortfolioSortOptionType = 2
	// Sort By returns gained
	PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_RETURNS PortfolioSortOptionType = 3
	// Sort By returns Percentage
	PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_RETURNS_PERCENTAGE PortfolioSortOptionType = 4
)

// Enum value maps for PortfolioSortOptionType.
var (
	PortfolioSortOptionType_name = map[int32]string{
		0: "PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED",
		1: "PORTFOLIO_SORT_OPTION_TYPE_MARKET_VALUE",
		2: "PORTFOLIO_SORT_OPTION_TYPE_INVESTED_AMOUNT",
		3: "PORTFOLIO_SORT_OPTION_TYPE_RETURNS",
		4: "PORTFOLIO_SORT_OPTION_TYPE_RETURNS_PERCENTAGE",
	}
	PortfolioSortOptionType_value = map[string]int32{
		"PORTFOLIO_SORT_OPTION_TYPE_UNSPECIFIED":        0,
		"PORTFOLIO_SORT_OPTION_TYPE_MARKET_VALUE":       1,
		"PORTFOLIO_SORT_OPTION_TYPE_INVESTED_AMOUNT":    2,
		"PORTFOLIO_SORT_OPTION_TYPE_RETURNS":            3,
		"PORTFOLIO_SORT_OPTION_TYPE_RETURNS_PERCENTAGE": 4,
	}
)

func (x PortfolioSortOptionType) Enum() *PortfolioSortOptionType {
	p := new(PortfolioSortOptionType)
	*p = x
	return p
}

func (x PortfolioSortOptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PortfolioSortOptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[6].Descriptor()
}

func (PortfolioSortOptionType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[6]
}

func (x PortfolioSortOptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PortfolioSortOptionType.Descriptor instead.
func (PortfolioSortOptionType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{6}
}

type UsStockNotificationType int32

const (
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_UNSPECIFIED UsStockNotificationType = 0
	// notification text: Payment of ₹X received. We have placed for your order for buying X stock
	//
	// Deprecated: Marked as deprecated in api/usstocks/enums.proto.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_PAYMENT_SUCCESSFUL UsStockNotificationType = 1
	// eg: Your payment of ₹X to buy Y stock has failed. Tap to retry
	//
	// Deprecated: Marked as deprecated in api/usstocks/enums.proto.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_PAYMENT_FAILED UsStockNotificationType = 2
	// notification text: Yayy! Your order is completed. You now own stocks of X
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_FULFILLMENT_SUCCESSFUL UsStockNotificationType = 3
	// notification text: Your order for buying stocks of X could not be completed as you've exceeded the LRS limit
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_FULFILLMENT_FAILED UsStockNotificationType = 4
	// notification text: We have cancelled your buy order request of X stock of ₹Y
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_CANCELLED UsStockNotificationType = 5
	// notification text: Your sell order for X stock has been placed successfully
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_PLACED_SUCCESSFUL UsStockNotificationType = 6
	// notification text: Your sell order for X stock could not be placed. Tap to retry
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_PLACED_FAILED UsStockNotificationType = 7
	// notification text: Your sell order of X stock is completed / You have successfully sold your X stock
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_FULFILLMENT_SUCCESSFUL UsStockNotificationType = 8
	// notification text: Your sell order of X stock could not be completed. Tap to retry
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_FULFILLMENT_FAILED UsStockNotificationType = 9
	// notification text: We have initiated the transfer of ₹X - your sell order funds to your bank account. It will reach you in x-y days
	//
	// Deprecated: Marked as deprecated in api/usstocks/enums.proto.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CREDIT_INITIATED UsStockNotificationType = 10
	// notification text: ₹X for selling X stocks have reached your account.
	//
	// Deprecated: Marked as deprecated in api/usstocks/enums.proto.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CREDIT_SUCCESSFUL UsStockNotificationType = 11
	// notification text: We have cancelled your sell order request of X stock of ₹Y
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CANCELLED UsStockNotificationType = 12
	// notification text: Buy your first stock now, most users picked X:- No commission- Real-time purchase - X% gains in last Y years
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_ACCOUNT_CREATED_SUCCESSFULLY_START_INVESTING UsStockNotificationType = 13
	// notification text: Upload Pan Card - User Pan Card Missing
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_MANUAL_PAN_REUPLOAD UsStockNotificationType = 14
	// notification text: Yayy! Your order is completed. You now own stocks of X
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_FOREIGN_REMITTANCE_COMPLETED UsStockNotificationType = 15
	// To notify user that GST has been deducted against a stock's dividend and refund has been initiated.
	// You've been charged ₹X as GST for the credit of <₹Y dividend amt> as dividend for <stock name> stock. Your refund of ₹45 will be processed in <x> days.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_GST_REFUND_INITIATED_FOR_DIVIDEND UsStockNotificationType = 16
	// Your dividend of <₹X> for your <stock name> stock has been credited!
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_DIVIDEND_CREDITED UsStockNotificationType = 17
	// ₹X has been refunded against the GST charged for your dividend transaction on ₹Y
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_GST_REFUND_SUCCESSFUL_FOR_DIVIDEND UsStockNotificationType = 18
	// To notify user that GST has been deducted against a stock's dividend. No refund applicable.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_GST_DEDUCTED_FOR_STOCK_DIVIDEND UsStockNotificationType = 19
	// To notify user that GST has been deducted against aggregated dividends and refund has been initiated.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_GST_REFUND_INITIATED_FOR_AGGREGATED_DIVIDENDS UsStockNotificationType = 20
	// To notify user that GST has been refunded against aggregated dividends.
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_GST_REFUND_SUCCESSFUL_FOR_AGGREGATED_DIVIDENDS UsStockNotificationType = 21
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_POOL_ACCOUNT_TRANSFER_SUCCESSFUL               UsStockNotificationType = 22
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_POOL_ACCOUNT_TRANSFER_FAILED                   UsStockNotificationType = 23
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_PLACED_FAILED                        UsStockNotificationType = 24
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_PLACED_SUCCESSFUL                    UsStockNotificationType = 25
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_TO_WALLET_SUCCESSFUL                 UsStockNotificationType = 26
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_INITIATED             UsStockNotificationType = 27
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_DELAYED               UsStockNotificationType = 28
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_SUCCESSFUL            UsStockNotificationType = 29
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_FROM_WALLET_FAILED              UsStockNotificationType = 30
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_FROM_WALLET_SUCCESSFUL          UsStockNotificationType = 31
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_SUCCESSFUL        UsStockNotificationType = 32
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_DELAYED           UsStockNotificationType = 33
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_FAILED            UsStockNotificationType = 34
	// notification to send in case of order fulfillment failure due to stock price not reached to set limit price
	// will use this in case of order status order_expired from vendor
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_BUY_LIMIT_ORDER_FULFILLMENT_FAILED  UsStockNotificationType = 35
	UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_SELL_LIMIT_ORDER_FULFILLMENT_FAILED UsStockNotificationType = 36
)

// Enum value maps for UsStockNotificationType.
var (
	UsStockNotificationType_name = map[int32]string{
		0:  "US_STOCK_NOTIFICATION_TYPE_UNSPECIFIED",
		1:  "US_STOCK_NOTIFICATION_TYPE_BUY_PAYMENT_SUCCESSFUL",
		2:  "US_STOCK_NOTIFICATION_TYPE_BUY_PAYMENT_FAILED",
		3:  "US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_FULFILLMENT_SUCCESSFUL",
		4:  "US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_FULFILLMENT_FAILED",
		5:  "US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_CANCELLED",
		6:  "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_PLACED_SUCCESSFUL",
		7:  "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_PLACED_FAILED",
		8:  "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_FULFILLMENT_SUCCESSFUL",
		9:  "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_FULFILLMENT_FAILED",
		10: "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CREDIT_INITIATED",
		11: "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CREDIT_SUCCESSFUL",
		12: "US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CANCELLED",
		13: "US_STOCK_NOTIFICATION_TYPE_ACCOUNT_CREATED_SUCCESSFULLY_START_INVESTING",
		14: "US_STOCK_NOTIFICATION_TYPE_MANUAL_PAN_REUPLOAD",
		15: "US_STOCK_NOTIFICATION_TYPE_FOREIGN_REMITTANCE_COMPLETED",
		16: "US_STOCK_NOTIFICATION_TYPE_GST_REFUND_INITIATED_FOR_DIVIDEND",
		17: "US_STOCK_NOTIFICATION_TYPE_DIVIDEND_CREDITED",
		18: "US_STOCK_NOTIFICATION_TYPE_GST_REFUND_SUCCESSFUL_FOR_DIVIDEND",
		19: "US_STOCK_NOTIFICATION_TYPE_GST_DEDUCTED_FOR_STOCK_DIVIDEND",
		20: "US_STOCK_NOTIFICATION_TYPE_GST_REFUND_INITIATED_FOR_AGGREGATED_DIVIDENDS",
		21: "US_STOCK_NOTIFICATION_TYPE_GST_REFUND_SUCCESSFUL_FOR_AGGREGATED_DIVIDENDS",
		22: "US_STOCK_NOTIFICATION_TYPE_POOL_ACCOUNT_TRANSFER_SUCCESSFUL",
		23: "US_STOCK_NOTIFICATION_TYPE_POOL_ACCOUNT_TRANSFER_FAILED",
		24: "US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_PLACED_FAILED",
		25: "US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_PLACED_SUCCESSFUL",
		26: "US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_TO_WALLET_SUCCESSFUL",
		27: "US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_INITIATED",
		28: "US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_DELAYED",
		29: "US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_SUCCESSFUL",
		30: "US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_FROM_WALLET_FAILED",
		31: "US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_FROM_WALLET_SUCCESSFUL",
		32: "US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_SUCCESSFUL",
		33: "US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_DELAYED",
		34: "US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_FAILED",
		35: "US_STOCK_NOTIFICATION_TYPE_BUY_LIMIT_ORDER_FULFILLMENT_FAILED",
		36: "US_STOCK_NOTIFICATION_TYPE_SELL_LIMIT_ORDER_FULFILLMENT_FAILED",
	}
	UsStockNotificationType_value = map[string]int32{
		"US_STOCK_NOTIFICATION_TYPE_UNSPECIFIED":                                    0,
		"US_STOCK_NOTIFICATION_TYPE_BUY_PAYMENT_SUCCESSFUL":                         1,
		"US_STOCK_NOTIFICATION_TYPE_BUY_PAYMENT_FAILED":                             2,
		"US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_FULFILLMENT_SUCCESSFUL":               3,
		"US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_FULFILLMENT_FAILED":                   4,
		"US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_CANCELLED":                            5,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_PLACED_SUCCESSFUL":                   6,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_PLACED_FAILED":                       7,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_FULFILLMENT_SUCCESSFUL":              8,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_FULFILLMENT_FAILED":                  9,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CREDIT_INITIATED":                    10,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CREDIT_SUCCESSFUL":                   11,
		"US_STOCK_NOTIFICATION_TYPE_SELL_ORDER_CANCELLED":                           12,
		"US_STOCK_NOTIFICATION_TYPE_ACCOUNT_CREATED_SUCCESSFULLY_START_INVESTING":   13,
		"US_STOCK_NOTIFICATION_TYPE_MANUAL_PAN_REUPLOAD":                            14,
		"US_STOCK_NOTIFICATION_TYPE_FOREIGN_REMITTANCE_COMPLETED":                   15,
		"US_STOCK_NOTIFICATION_TYPE_GST_REFUND_INITIATED_FOR_DIVIDEND":              16,
		"US_STOCK_NOTIFICATION_TYPE_DIVIDEND_CREDITED":                              17,
		"US_STOCK_NOTIFICATION_TYPE_GST_REFUND_SUCCESSFUL_FOR_DIVIDEND":             18,
		"US_STOCK_NOTIFICATION_TYPE_GST_DEDUCTED_FOR_STOCK_DIVIDEND":                19,
		"US_STOCK_NOTIFICATION_TYPE_GST_REFUND_INITIATED_FOR_AGGREGATED_DIVIDENDS":  20,
		"US_STOCK_NOTIFICATION_TYPE_GST_REFUND_SUCCESSFUL_FOR_AGGREGATED_DIVIDENDS": 21,
		"US_STOCK_NOTIFICATION_TYPE_POOL_ACCOUNT_TRANSFER_SUCCESSFUL":               22,
		"US_STOCK_NOTIFICATION_TYPE_POOL_ACCOUNT_TRANSFER_FAILED":                   23,
		"US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_PLACED_FAILED":                        24,
		"US_STOCK_NOTIFICATION_TYPE_BUY_ORDER_PLACED_SUCCESSFUL":                    25,
		"US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_TO_WALLET_SUCCESSFUL":                 26,
		"US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_INITIATED":             27,
		"US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_DELAYED":               28,
		"US_STOCK_NOTIFICATION_TYPE_ADD_FUNDS_FAILURE_REFUND_SUCCESSFUL":            29,
		"US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_FROM_WALLET_FAILED":              30,
		"US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_FROM_WALLET_SUCCESSFUL":          31,
		"US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_SUCCESSFUL":        32,
		"US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_DELAYED":           33,
		"US_STOCK_NOTIFICATION_TYPE_WITHDRAW_FUNDS_AMOUNT_CREDIT_FAILED":            34,
		"US_STOCK_NOTIFICATION_TYPE_BUY_LIMIT_ORDER_FULFILLMENT_FAILED":             35,
		"US_STOCK_NOTIFICATION_TYPE_SELL_LIMIT_ORDER_FULFILLMENT_FAILED":            36,
	}
)

func (x UsStockNotificationType) Enum() *UsStockNotificationType {
	p := new(UsStockNotificationType)
	*p = x
	return p
}

func (x UsStockNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UsStockNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[7].Descriptor()
}

func (UsStockNotificationType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[7]
}

func (x UsStockNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UsStockNotificationType.Descriptor instead.
func (UsStockNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{7}
}

type MarketStatus int32

const (
	MarketStatus_MARKET_STATUS_UNSPECIFIED MarketStatus = 0
	// market is open to place new orders
	MarketStatus_MARKET_STATUS_OPEN MarketStatus = 1
	// market is closed to place new orders
	MarketStatus_MARKET_STATUS_CLOSED MarketStatus = 2
)

// Enum value maps for MarketStatus.
var (
	MarketStatus_name = map[int32]string{
		0: "MARKET_STATUS_UNSPECIFIED",
		1: "MARKET_STATUS_OPEN",
		2: "MARKET_STATUS_CLOSED",
	}
	MarketStatus_value = map[string]int32{
		"MARKET_STATUS_UNSPECIFIED": 0,
		"MARKET_STATUS_OPEN":        1,
		"MARKET_STATUS_CLOSED":      2,
	}
)

func (x MarketStatus) Enum() *MarketStatus {
	p := new(MarketStatus)
	*p = x
	return p
}

func (x MarketStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarketStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[8].Descriptor()
}

func (MarketStatus) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[8]
}

func (x MarketStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarketStatus.Descriptor instead.
func (MarketStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{8}
}

type RemittanceType int32

const (
	RemittanceType_REMITTANCE_TYPE_UNSPECIFIED RemittanceType = 0
	// represent the aggregate transaction for inward/ money that will be received by user
	RemittanceType_REMITTANCE_TYPE_INWARD RemittanceType = 1
	// represent the aggregate transaction for outward/ money that will be paid by user
	RemittanceType_REMITTANCE_TYPE_OUTWARD RemittanceType = 2
)

// Enum value maps for RemittanceType.
var (
	RemittanceType_name = map[int32]string{
		0: "REMITTANCE_TYPE_UNSPECIFIED",
		1: "REMITTANCE_TYPE_INWARD",
		2: "REMITTANCE_TYPE_OUTWARD",
	}
	RemittanceType_value = map[string]int32{
		"REMITTANCE_TYPE_UNSPECIFIED": 0,
		"REMITTANCE_TYPE_INWARD":      1,
		"REMITTANCE_TYPE_OUTWARD":     2,
	}
)

func (x RemittanceType) Enum() *RemittanceType {
	p := new(RemittanceType)
	*p = x
	return p
}

func (x RemittanceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RemittanceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[9].Descriptor()
}

func (RemittanceType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[9]
}

func (x RemittanceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RemittanceType.Descriptor instead.
func (RemittanceType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{9}
}

// RemittanceProcessStatus denotes status of a foreign remittance process.
type RemittanceProcessStatus int32

const (
	RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_UNSPECIFIED RemittanceProcessStatus = 0
	// remittance process has been created but processing hasn't started yet
	RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_CREATED RemittanceProcessStatus = 1
	// remittance process is initiated right now. Granular status can be fetched from celestial
	// workflow_requests table.
	RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_INITIATED RemittanceProcessStatus = 2
	// [TERMINAL STATE] remittance process completed successfully. Some/All txns were remitted successfully.
	RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_SUCCESS RemittanceProcessStatus = 3
	// [TERMINAL STATE] we encountered some non-retryable failure while processing.
	RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_FAILED RemittanceProcessStatus = 4
)

// Enum value maps for RemittanceProcessStatus.
var (
	RemittanceProcessStatus_name = map[int32]string{
		0: "REMITTANCE_PROCESS_STATUS_UNSPECIFIED",
		1: "REMITTANCE_PROCESS_STATUS_CREATED",
		2: "REMITTANCE_PROCESS_STATUS_INITIATED",
		3: "REMITTANCE_PROCESS_STATUS_SUCCESS",
		4: "REMITTANCE_PROCESS_STATUS_FAILED",
	}
	RemittanceProcessStatus_value = map[string]int32{
		"REMITTANCE_PROCESS_STATUS_UNSPECIFIED": 0,
		"REMITTANCE_PROCESS_STATUS_CREATED":     1,
		"REMITTANCE_PROCESS_STATUS_INITIATED":   2,
		"REMITTANCE_PROCESS_STATUS_SUCCESS":     3,
		"REMITTANCE_PROCESS_STATUS_FAILED":      4,
	}
)

func (x RemittanceProcessStatus) Enum() *RemittanceProcessStatus {
	p := new(RemittanceProcessStatus)
	*p = x
	return p
}

func (x RemittanceProcessStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RemittanceProcessStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[10].Descriptor()
}

func (RemittanceProcessStatus) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[10]
}

func (x RemittanceProcessStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RemittanceProcessStatus.Descriptor instead.
func (RemittanceProcessStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{10}
}

type AggregatedRemittanceTransactionState int32

const (
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_UNSPECIFIED AggregatedRemittanceTransactionState = 0
	// this represent the aggregated transaction is created and corresponding mapping activity are added into db
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREATED AggregatedRemittanceTransactionState = 1
	// if we have successful of payment initiation then all aggregate transaction move to this state
	// corresponding aggregate transaction amount is consider for inward remittance
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_INITIATED AggregatedRemittanceTransactionState = 2
	// if we have successful of payment acknowledge then aggregate transaction move to this state
	// corresponding aggregate transaction are marked file generate for acknowledge file
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_ACKNOWLEDGE AggregatedRemittanceTransactionState = 3
	// if we have failed to move payment acknowledge state then aggregate transaction move to this state
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_FAILED AggregatedRemittanceTransactionState = 4
	// if we face credit freeze during payment acknowledge then aggregated transaction will move to credit freeze
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE AggregatedRemittanceTransactionState = 5
	// if we have received credit notification of amount received for aggregate transaction
	AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_SUCCESSFUL AggregatedRemittanceTransactionState = 6
)

// Enum value maps for AggregatedRemittanceTransactionState.
var (
	AggregatedRemittanceTransactionState_name = map[int32]string{
		0: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_UNSPECIFIED",
		1: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREATED",
		2: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_INITIATED",
		3: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_ACKNOWLEDGE",
		4: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_FAILED",
		5: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE",
		6: "AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_SUCCESSFUL",
	}
	AggregatedRemittanceTransactionState_value = map[string]int32{
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_UNSPECIFIED":         0,
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREATED":             1,
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_INITIATED":   2,
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_ACKNOWLEDGE": 3,
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_FAILED":              4,
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_CREDIT_FREEZE":       5,
		"AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_SUCCESSFUL":  6,
	}
)

func (x AggregatedRemittanceTransactionState) Enum() *AggregatedRemittanceTransactionState {
	p := new(AggregatedRemittanceTransactionState)
	*p = x
	return p
}

func (x AggregatedRemittanceTransactionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggregatedRemittanceTransactionState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[11].Descriptor()
}

func (AggregatedRemittanceTransactionState) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[11]
}

func (x AggregatedRemittanceTransactionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggregatedRemittanceTransactionState.Descriptor instead.
func (AggregatedRemittanceTransactionState) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{11}
}

// WalletTransferStatus denotes status of fund transfer between wallet and firm account
type WalletTransferStatus int32

const (
	WalletTransferStatus_WALLET_TRANSFER_STATUS_UNSPECIFIED WalletTransferStatus = 0
	// money transfer from/to wallet to/from firm account successful
	WalletTransferStatus_WALLET_TRANSFER_STATUS_SUCCESSFUL WalletTransferStatus = 1
	// money transfer from/to wallet to/from firm account failed
	WalletTransferStatus_WALLET_TRANSFER_STATUS_FAILED WalletTransferStatus = 2
	// money transfer from/to wallet to/from firm account successful
	WalletTransferStatus_WALLET_TRANSFER_STATUS_MANUAL_INTERVENTION WalletTransferStatus = 3
	// wallet transfer is initiated
	WalletTransferStatus_WALLET_TRANSFER_STATUS_INITIATED WalletTransferStatus = 4
	// wallet transfer is canceled by the user
	WalletTransferStatus_WALLET_TRANSFER_STATUS_CANCELED WalletTransferStatus = 5
)

// Enum value maps for WalletTransferStatus.
var (
	WalletTransferStatus_name = map[int32]string{
		0: "WALLET_TRANSFER_STATUS_UNSPECIFIED",
		1: "WALLET_TRANSFER_STATUS_SUCCESSFUL",
		2: "WALLET_TRANSFER_STATUS_FAILED",
		3: "WALLET_TRANSFER_STATUS_MANUAL_INTERVENTION",
		4: "WALLET_TRANSFER_STATUS_INITIATED",
		5: "WALLET_TRANSFER_STATUS_CANCELED",
	}
	WalletTransferStatus_value = map[string]int32{
		"WALLET_TRANSFER_STATUS_UNSPECIFIED":         0,
		"WALLET_TRANSFER_STATUS_SUCCESSFUL":          1,
		"WALLET_TRANSFER_STATUS_FAILED":              2,
		"WALLET_TRANSFER_STATUS_MANUAL_INTERVENTION": 3,
		"WALLET_TRANSFER_STATUS_INITIATED":           4,
		"WALLET_TRANSFER_STATUS_CANCELED":            5,
	}
)

func (x WalletTransferStatus) Enum() *WalletTransferStatus {
	p := new(WalletTransferStatus)
	*p = x
	return p
}

func (x WalletTransferStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WalletTransferStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[12].Descriptor()
}

func (WalletTransferStatus) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[12]
}

func (x WalletTransferStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WalletTransferStatus.Descriptor instead.
func (WalletTransferStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{12}
}

// OrderFundingType defines type of funding applicable for the corresponding market order
type OrderFundingType int32

const (
	OrderFundingType_ORDER_FUNDING_TYPE_UNSPECIFIED OrderFundingType = 0
	// International remittance of funds takes few days to reach international account
	// Just in time (JIT) funding for market orders empowers users to place instant buy order with US Stocks broker
	// even if the funds have not reached broker's international account
	// the funds are expected to reach broker by T + X days
	OrderFundingType_ORDER_FUNDING_TYPE_JUST_IN_TIME OrderFundingType = 1
	// Wallet funding enables user to place buy order, funds will be deducted from user's wallet at broker
	// funds are expected to be present in user's wallet for successful execution of the market order
	OrderFundingType_ORDER_FUNDING_TYPE_WALLET OrderFundingType = 2
)

// Enum value maps for OrderFundingType.
var (
	OrderFundingType_name = map[int32]string{
		0: "ORDER_FUNDING_TYPE_UNSPECIFIED",
		1: "ORDER_FUNDING_TYPE_JUST_IN_TIME",
		2: "ORDER_FUNDING_TYPE_WALLET",
	}
	OrderFundingType_value = map[string]int32{
		"ORDER_FUNDING_TYPE_UNSPECIFIED":  0,
		"ORDER_FUNDING_TYPE_JUST_IN_TIME": 1,
		"ORDER_FUNDING_TYPE_WALLET":       2,
	}
)

func (x OrderFundingType) Enum() *OrderFundingType {
	p := new(OrderFundingType)
	*p = x
	return p
}

func (x OrderFundingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderFundingType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[13].Descriptor()
}

func (OrderFundingType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[13]
}

func (x OrderFundingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderFundingType.Descriptor instead.
func (OrderFundingType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{13}
}

type OrderType int32

const (
	OrderType_ORDER_TYPE_UNSPECIFIED OrderType = 0
	// order for a specified amount is placed
	// eg: Buy AAPL for $100, Sell NFLX for $100
	OrderType_ORDER_TYPE_NOTIONAL_VALUE OrderType = 1
	// order for a specified quantity is placed
	// eg: Buy 0.5 units of AAPL, Sell 0.5 units of NFLX
	// order to be placed at market price for the specified quantity
	OrderType_ORDER_TYPE_QUANTITY_VALUE OrderType = 2
	// order type to sell all holdings of a particular symbol
	OrderType_ORDER_TYPE_SELL_ALL OrderType = 3
	// order type for buying notional value orders placed for rewarding the user
	OrderType_ORDER_TYPE_REWARDS_NOTIONAL_VALUE OrderType = 4
	// order type for buying notional value orders placed for User's SIP
	// Client order ids for such orders would be fittt execution id
	OrderType_ORDER_TYPE_SIP_NOTIONAL_VALUE OrderType = 5
	// order type for buying specified quantity at limit price entered by user
	OrderType_ORDER_TYPE_LIMIT_ORDER OrderType = 6
	// order type for selling all holdings of a particular symbol at limit price entered by user
	OrderType_ORDER_TYPE_SELL_ALL_LIMIT_ORDER OrderType = 7
	// order type to sell all holdings in a symbol based on quantity
	OrderType_ORDER_TYPE_SELL_ALL_QUANTITY OrderType = 8
)

// Enum value maps for OrderType.
var (
	OrderType_name = map[int32]string{
		0: "ORDER_TYPE_UNSPECIFIED",
		1: "ORDER_TYPE_NOTIONAL_VALUE",
		2: "ORDER_TYPE_QUANTITY_VALUE",
		3: "ORDER_TYPE_SELL_ALL",
		4: "ORDER_TYPE_REWARDS_NOTIONAL_VALUE",
		5: "ORDER_TYPE_SIP_NOTIONAL_VALUE",
		6: "ORDER_TYPE_LIMIT_ORDER",
		7: "ORDER_TYPE_SELL_ALL_LIMIT_ORDER",
		8: "ORDER_TYPE_SELL_ALL_QUANTITY",
	}
	OrderType_value = map[string]int32{
		"ORDER_TYPE_UNSPECIFIED":            0,
		"ORDER_TYPE_NOTIONAL_VALUE":         1,
		"ORDER_TYPE_QUANTITY_VALUE":         2,
		"ORDER_TYPE_SELL_ALL":               3,
		"ORDER_TYPE_REWARDS_NOTIONAL_VALUE": 4,
		"ORDER_TYPE_SIP_NOTIONAL_VALUE":     5,
		"ORDER_TYPE_LIMIT_ORDER":            6,
		"ORDER_TYPE_SELL_ALL_LIMIT_ORDER":   7,
		"ORDER_TYPE_SELL_ALL_QUANTITY":      8,
	}
)

func (x OrderType) Enum() *OrderType {
	p := new(OrderType)
	*p = x
	return p
}

func (x OrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[14].Descriptor()
}

func (OrderType) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[14]
}

func (x OrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderType.Descriptor instead.
func (OrderType) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{14}
}

// Time in force is an instruction in trading that defines how long an order will remain active before it is executed or expires.
type TimeInForce int32

const (
	TimeInForce_TIME_IN_FORCE_UNSPECIFIED TimeInForce = 0
	// order is valid for the day
	TimeInForce_TIME_IN_FORCE_DAY TimeInForce = 1
	// order is valid till cancelled
	TimeInForce_TIME_IN_FORCE_GTC TimeInForce = 2
)

// Enum value maps for TimeInForce.
var (
	TimeInForce_name = map[int32]string{
		0: "TIME_IN_FORCE_UNSPECIFIED",
		1: "TIME_IN_FORCE_DAY",
		2: "TIME_IN_FORCE_GTC",
	}
	TimeInForce_value = map[string]int32{
		"TIME_IN_FORCE_UNSPECIFIED": 0,
		"TIME_IN_FORCE_DAY":         1,
		"TIME_IN_FORCE_GTC":         2,
	}
)

func (x TimeInForce) Enum() *TimeInForce {
	p := new(TimeInForce)
	*p = x
	return p
}

func (x TimeInForce) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeInForce) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_enums_proto_enumTypes[15].Descriptor()
}

func (TimeInForce) Type() protoreflect.EnumType {
	return &file_api_usstocks_enums_proto_enumTypes[15]
}

func (x TimeInForce) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeInForce.Descriptor instead.
func (TimeInForce) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_enums_proto_rawDescGZIP(), []int{15}
}

var File_api_usstocks_enums_proto protoreflect.FileDescriptor

var file_api_usstocks_enums_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x75, 0x73, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x73, 0x2a, 0xd9, 0x01, 0x0a, 0x0a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1d, 0x0a,
	0x19, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x06,
	0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x11, 0x0a,
	0x0d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x08,
	0x2a, 0x3a, 0x0a, 0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x69, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x55, 0x59,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x45, 0x4c, 0x4c, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x0f,
	0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x1d, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41,
	0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x02, 0x2a, 0xca, 0x02, 0x0a, 0x12, 0x57, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x21, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3e, 0x0a, 0x36, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41,
	0x4e, 0x54, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x3e, 0x0a, 0x3a, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x53, 0x10, 0x03, 0x12, 0x37, 0x0a, 0x33, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f,
	0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54,
	0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x27, 0x0a,
	0x23, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53,
	0x5f, 0x53, 0x49, 0x50, 0x10, 0x05, 0x2a, 0x95, 0x02, 0x0a, 0x11, 0x57, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f,
	0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x2c,
	0x0a, 0x06, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4c, 0x50, 0x41, 0x43, 0x41, 0x10, 0x01, 0x2a, 0xfd, 0x01, 0x0a,
	0x17, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x4f, 0x52, 0x54,
	0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49,
	0x4f, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10,
	0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x53,
	0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x02, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x53,
	0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x10, 0x03, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x4f, 0x52,
	0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x10, 0x04, 0x2a, 0xc0, 0x12, 0x0a,
	0x17, 0x55, 0x73, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x39, 0x0a, 0x31, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x35, 0x0a, 0x2d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55,
	0x59, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x3f, 0x0a, 0x3b, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46,
	0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x03, 0x12, 0x3b, 0x0a, 0x37, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x3b, 0x0a, 0x37, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x46, 0x55, 0x4c, 0x10, 0x06, 0x12, 0x37, 0x0a, 0x33, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50,
	0x4c, 0x41, 0x43, 0x45, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x40,
	0x0a, 0x3c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x08,
	0x12, 0x3c, 0x0a, 0x38, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c,
	0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x09, 0x12, 0x3e,
	0x0a, 0x36, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x0a, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x3f,
	0x0a, 0x37, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x0b, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x33, 0x0a, 0x2f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45,
	0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c,
	0x45, 0x44, 0x10, 0x0c, 0x12, 0x4b, 0x0a, 0x47, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f,
	0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x0d, 0x12, 0x32, 0x0a, 0x2e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x0e, 0x12, 0x3b, 0x0a, 0x37, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x4d, 0x49,
	0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x0f, 0x12, 0x40, 0x0a, 0x3c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x47, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45,
	0x4e, 0x44, 0x10, 0x10, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x45, 0x44, 0x10, 0x11, 0x12, 0x41, 0x0a, 0x3d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44,
	0x49, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x10, 0x12, 0x12, 0x3e, 0x0a, 0x3a, 0x55, 0x53, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x44, 0x55,
	0x43, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44,
	0x49, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x10, 0x13, 0x12, 0x4c, 0x0a, 0x48, 0x55, 0x53, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x49, 0x56, 0x49,
	0x44, 0x45, 0x4e, 0x44, 0x53, 0x10, 0x14, 0x12, 0x4d, 0x0a, 0x49, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x44, 0x53, 0x10, 0x15, 0x12, 0x3f, 0x0a, 0x3b, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4f, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x16, 0x12, 0x3b, 0x0a, 0x37, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4f, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x17, 0x12, 0x36, 0x0a, 0x32, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x4c, 0x41,
	0x43, 0x45, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x18, 0x12, 0x3a, 0x0a, 0x36,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x19, 0x12, 0x3d, 0x0a, 0x39, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53,
	0x5f, 0x54, 0x4f, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x1a, 0x12, 0x41, 0x0a, 0x3d, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x3f, 0x0a, 0x3b, 0x55, 0x53,
	0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x44, 0x45, 0x4c, 0x41, 0x59, 0x45, 0x44, 0x10, 0x1c, 0x12, 0x42, 0x0a, 0x3e, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x1d, 0x12,
	0x40, 0x0a, 0x3c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x1e, 0x12, 0x44, 0x0a, 0x40, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46,
	0x52, 0x4f, 0x4d, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x1f, 0x12, 0x46, 0x0a, 0x42, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x53, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x20, 0x12,
	0x43, 0x0a, 0x3f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x41, 0x4d, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x41, 0x59,
	0x45, 0x44, 0x10, 0x21, 0x12, 0x42, 0x0a, 0x3e, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x22, 0x12, 0x41, 0x0a, 0x3d, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x23, 0x12, 0x42, 0x0a, 0x3e, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49,
	0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x24, 0x2a,
	0x5f, 0x0a, 0x0c, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x19, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16,
	0x0a, 0x12, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x02,
	0x2a, 0x6a, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12,
	0x1b, 0x0a, 0x17, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x55, 0x54, 0x57, 0x41, 0x52, 0x44, 0x10, 0x02, 0x2a, 0xe1, 0x01, 0x0a,
	0x17, 0x52, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x45, 0x4d, 0x49,
	0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x45,
	0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45,
	0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x2a, 0xc3, 0x03, 0x0a, 0x24, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x65, 0x6d, 0x69, 0x74, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x33, 0x41, 0x47, 0x47,
	0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x33, 0x0a, 0x2f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x3d, 0x0a, 0x39, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x3f, 0x0a, 0x3b, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x4b, 0x4e, 0x4f, 0x57,
	0x4c, 0x45, 0x44, 0x47, 0x45, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x39, 0x0a, 0x35, 0x41,
	0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x46, 0x52,
	0x45, 0x45, 0x5a, 0x45, 0x10, 0x05, 0x12, 0x3e, 0x0a, 0x3a, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x46, 0x55, 0x4c, 0x10, 0x06, 0x2a, 0x83, 0x02, 0x0a, 0x14, 0x57, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x26, 0x0a, 0x22, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x01, 0x12, 0x21,
	0x0a, 0x1d, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x03, 0x12, 0x24, 0x0a, 0x20, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x7a, 0x0a, 0x10,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x55, 0x53, 0x54, 0x5f,
	0x49, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x2a, 0xab, 0x02, 0x0a, 0x09, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10,
	0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x02,
	0x12, 0x17, 0x0a, 0x13, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x45, 0x4c, 0x4c, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x04,
	0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x49, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x56, 0x41, 0x4c, 0x55,
	0x45, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x06, 0x12,
	0x23, 0x0a, 0x1f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45,
	0x4c, 0x4c, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x51, 0x55, 0x41, 0x4e,
	0x54, 0x49, 0x54, 0x59, 0x10, 0x08, 0x2a, 0x5a, 0x0a, 0x0b, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e,
	0x46, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x49, 0x4e,
	0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x5f,
	0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x54,
	0x49, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x47, 0x54, 0x43,
	0x10, 0x02, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x5a, 0x23, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_usstocks_enums_proto_rawDescOnce sync.Once
	file_api_usstocks_enums_proto_rawDescData = file_api_usstocks_enums_proto_rawDesc
)

func file_api_usstocks_enums_proto_rawDescGZIP() []byte {
	file_api_usstocks_enums_proto_rawDescOnce.Do(func() {
		file_api_usstocks_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_usstocks_enums_proto_rawDescData)
	})
	return file_api_usstocks_enums_proto_rawDescData
}

var file_api_usstocks_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_api_usstocks_enums_proto_goTypes = []interface{}{
	(OrderState)(0),                           // 0: usstocks.OrderState
	(OrderSide)(0),                            // 1: usstocks.OrderSide
	(WalletOrderType)(0),                      // 2: usstocks.WalletOrderType
	(WalletOrderSubType)(0),                   // 3: usstocks.WalletOrderSubType
	(WalletOrderStatus)(0),                    // 4: usstocks.WalletOrderStatus
	(Vendor)(0),                               // 5: usstocks.Vendor
	(PortfolioSortOptionType)(0),              // 6: usstocks.PortfolioSortOptionType
	(UsStockNotificationType)(0),              // 7: usstocks.UsStockNotificationType
	(MarketStatus)(0),                         // 8: usstocks.MarketStatus
	(RemittanceType)(0),                       // 9: usstocks.RemittanceType
	(RemittanceProcessStatus)(0),              // 10: usstocks.RemittanceProcessStatus
	(AggregatedRemittanceTransactionState)(0), // 11: usstocks.AggregatedRemittanceTransactionState
	(WalletTransferStatus)(0),                 // 12: usstocks.WalletTransferStatus
	(OrderFundingType)(0),                     // 13: usstocks.OrderFundingType
	(OrderType)(0),                            // 14: usstocks.OrderType
	(TimeInForce)(0),                          // 15: usstocks.TimeInForce
}
var file_api_usstocks_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_usstocks_enums_proto_init() }
func file_api_usstocks_enums_proto_init() {
	if File_api_usstocks_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_usstocks_enums_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_usstocks_enums_proto_goTypes,
		DependencyIndexes: file_api_usstocks_enums_proto_depIdxs,
		EnumInfos:         file_api_usstocks_enums_proto_enumTypes,
	}.Build()
	File_api_usstocks_enums_proto = out.File
	file_api_usstocks_enums_proto_rawDesc = nil
	file_api_usstocks_enums_proto_goTypes = nil
	file_api_usstocks_enums_proto_depIdxs = nil
}
