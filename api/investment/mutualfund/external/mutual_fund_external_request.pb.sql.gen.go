// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/investment/mutualfund/external/mutual_fund_external_request.pb.go

package external

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the Provenance in string format in DB
func (p Provenance) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Provenance while reading from DB
func (p *Provenance) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Provenance_value[val]
	if !ok {
		return fmt.Errorf("unexpected Provenance value: %s", val)
	}
	*p = Provenance(valInt)
	return nil
}

// Marshaler interface implementation for Provenance
func (x Provenance) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Provenance
func (x *Provenance) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Provenance(Provenance_value[val])
	return nil
}

// Valuer interface implementation for storing the State in string format in DB
func (p State) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing State while reading from DB
func (p *State) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := State_value[val]
	if !ok {
		return fmt.Errorf("unexpected State value: %s", val)
	}
	*p = State(valInt)
	return nil
}

// Marshaler interface implementation for State
func (x State) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for State
func (x *State) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = State(State_value[val])
	return nil
}

// Valuer interface implementation for storing the FailureReason in string format in DB
func (p FailureReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FailureReason while reading from DB
func (p *FailureReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FailureReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected FailureReason value: %s", val)
	}
	*p = FailureReason(valInt)
	return nil
}

// Marshaler interface implementation for FailureReason
func (x FailureReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FailureReason
func (x *FailureReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FailureReason(FailureReason_value[val])
	return nil
}

// Valuer interface implementation for storing the FlowType in string format in DB
func (p FlowType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FlowType while reading from DB
func (p *FlowType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FlowType_value[val]
	if !ok {
		return fmt.Errorf("unexpected FlowType value: %s", val)
	}
	*p = FlowType(valInt)
	return nil
}

// Marshaler interface implementation for FlowType
func (x FlowType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FlowType
func (x *FlowType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FlowType(FlowType_value[val])
	return nil
}

// Valuer interface implementation for storing the NFTRequestType in string format in DB
func (p NFTRequestType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing NFTRequestType while reading from DB
func (p *NFTRequestType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := NFTRequestType_value[val]
	if !ok {
		return fmt.Errorf("unexpected NFTRequestType value: %s", val)
	}
	*p = NFTRequestType(valInt)
	return nil
}

// Marshaler interface implementation for NFTRequestType
func (x NFTRequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for NFTRequestType
func (x *NFTRequestType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = NFTRequestType(NFTRequestType_value[val])
	return nil
}

// Valuer interface implementation for storing the NFTRequestStatus in string format in DB
func (p NFTRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing NFTRequestStatus while reading from DB
func (p *NFTRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := NFTRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected NFTRequestStatus value: %s", val)
	}
	*p = NFTRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for NFTRequestStatus
func (x NFTRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for NFTRequestStatus
func (x *NFTRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = NFTRequestStatus(NFTRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the NFTRequestSubStatus in string format in DB
func (p NFTRequestSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing NFTRequestSubStatus while reading from DB
func (p *NFTRequestSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := NFTRequestSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected NFTRequestSubStatus value: %s", val)
	}
	*p = NFTRequestSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for NFTRequestSubStatus
func (x NFTRequestSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for NFTRequestSubStatus
func (x *NFTRequestSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = NFTRequestSubStatus(NFTRequestSubStatus_value[val])
	return nil
}

// Scanner interface implementation for parsing VendorData while reading from DB
func (a *VendorData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the VendorData in string format in DB
func (a *VendorData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for VendorData
func (a *VendorData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for VendorData
func (a *VendorData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing MFHoldingsImportRequestMetaData while reading from DB
func (a *MFHoldingsImportRequestMetaData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the MFHoldingsImportRequestMetaData in string format in DB
func (a *MFHoldingsImportRequestMetaData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for MFHoldingsImportRequestMetaData
func (a *MFHoldingsImportRequestMetaData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for MFHoldingsImportRequestMetaData
func (a *MFHoldingsImportRequestMetaData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing NFTDetails while reading from DB
func (a *NFTDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the NFTDetails in string format in DB
func (a *NFTDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for NFTDetails
func (a *NFTDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for NFTDetails
func (a *NFTDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
