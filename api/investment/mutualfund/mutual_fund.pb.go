// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/mutualfund/mutual_fund.proto

package mutualfund

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	date "google.golang.org/genproto/googleapis/type/date"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Fund is available for investment or not
type FundInvestmentStatus int32

const (
	FundInvestmentStatus_FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED FundInvestmentStatus = 0
	// fund is available for new investments
	FundInvestmentStatus_AVAILABLE_FOR_INVESTMENT FundInvestmentStatus = 1
	// amc is not accepting new investments for the fund
	FundInvestmentStatus_AVAILABLE_ONLY_FOR_EXISTING_INVESTORS FundInvestmentStatus = 2
	// amc not accepting any investment
	FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT FundInvestmentStatus = 3
	// mutual-fund has been merged and is not available for new investments
	// Merged Funds: When two funds are merged, the assets of one fund are transferred to the assets of another,
	// The transferor fund then ceases to exist.
	FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT_MERGED FundInvestmentStatus = 4
	// mutual-fund has been liquidated and is not available for new investments
	// Liquidated Funds: A fund liquidation occurs when a fund closes down its operations completely, sells off its assets and generally distributes substantially all of
	// its assets in cash to its shareholders
	FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT_LIQUIDATED FundInvestmentStatus = 5
)

// Enum value maps for FundInvestmentStatus.
var (
	FundInvestmentStatus_name = map[int32]string{
		0: "FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED",
		1: "AVAILABLE_FOR_INVESTMENT",
		2: "AVAILABLE_ONLY_FOR_EXISTING_INVESTORS",
		3: "UNAVAILABLE_FOR_INVESTMENT",
		4: "UNAVAILABLE_FOR_INVESTMENT_MERGED",
		5: "UNAVAILABLE_FOR_INVESTMENT_LIQUIDATED",
	}
	FundInvestmentStatus_value = map[string]int32{
		"FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED": 0,
		"AVAILABLE_FOR_INVESTMENT":                                1,
		"AVAILABLE_ONLY_FOR_EXISTING_INVESTORS":                   2,
		"UNAVAILABLE_FOR_INVESTMENT":                              3,
		"UNAVAILABLE_FOR_INVESTMENT_MERGED":                       4,
		"UNAVAILABLE_FOR_INVESTMENT_LIQUIDATED":                   5,
	}
)

func (x FundInvestmentStatus) Enum() *FundInvestmentStatus {
	p := new(FundInvestmentStatus)
	*p = x
	return p
}

func (x FundInvestmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FundInvestmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[0].Descriptor()
}

func (FundInvestmentStatus) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[0]
}

func (x FundInvestmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FundInvestmentStatus.Descriptor instead.
func (FundInvestmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{0}
}

// Amc defines the list of different AMCs we will be supporting.
type Amc int32

const (
	Amc_AMC_UNSPECIFIED      Amc = 0
	Amc_ICICI_PRUDENTIAL     Amc = 1
	Amc_HDFC                 Amc = 2
	Amc_ADITYA_BIRLA         Amc = 3
	Amc_RELIANCE             Amc = 4
	Amc_SBI                  Amc = 5
	Amc_NIPPON_LIFE          Amc = 6
	Amc_UTI                  Amc = 7
	Amc_SUNDARAM             Amc = 8
	Amc_DSP                  Amc = 9
	Amc_IDFC                 Amc = 10
	Amc_INVESCO              Amc = 11
	Amc_KOTAK_MAHINDRA       Amc = 12
	Amc_AXIS                 Amc = 13
	Amc_LNT                  Amc = 14
	Amc_CANARA_ROBECO        Amc = 15
	Amc_EDELWEISS            Amc = 16
	Amc_PGIM                 Amc = 17
	Amc_BNP_PARIBAS          Amc = 18
	Amc_TATA                 Amc = 19
	Amc_MIRAE                Amc = 20
	Amc_LIC                  Amc = 21
	Amc_IDBI                 Amc = 22
	Amc_JM_FINANCIAL         Amc = 23
	Amc_BOI_AXA              Amc = 24
	Amc_UNION                Amc = 25
	Amc_HSBC                 Amc = 26
	Amc_QUANT_MONEY_MANAGERS Amc = 27
	Amc_BARODA               Amc = 28
	Amc_MOTILAL_OSWAL        Amc = 29
	Amc_IIFL                 Amc = 30
	Amc_MAHINDRA             Amc = 31
	Amc_FRANKLIN_TEMPLETON   Amc = 32
	Amc_QUANTUM              Amc = 33
	Amc_PRINCIPAL            Amc = 34
	Amc_PPFAS                Amc = 35
	Amc_TAURUS               Amc = 36
	Amc_INDIABULLS           Amc = 37
	Amc_WHITEOAK_CAPITAL     Amc = 38
	Amc_SHRIRAM              Amc = 39
	Amc_NAVI                 Amc = 40
	Amc_ITI                  Amc = 41
	Amc_TRUST                Amc = 42
	Amc_NJ                   Amc = 43
	Amc_SAMCO                Amc = 44
	Amc_SAHARA               Amc = 45
	Amc_BAJAJ_FINSERV        Amc = 46
	Amc_HELIOS               Amc = 47
	Amc_ZERODHA              Amc = 48
	Amc_OLD_BRIDGE           Amc = 49
	Amc_UNIFI                Amc = 50
	Amc_ANGEL_ONE            Amc = 51
	Amc_JIO_BLACKROCK        Amc = 52
)

// Enum value maps for Amc.
var (
	Amc_name = map[int32]string{
		0:  "AMC_UNSPECIFIED",
		1:  "ICICI_PRUDENTIAL",
		2:  "HDFC",
		3:  "ADITYA_BIRLA",
		4:  "RELIANCE",
		5:  "SBI",
		6:  "NIPPON_LIFE",
		7:  "UTI",
		8:  "SUNDARAM",
		9:  "DSP",
		10: "IDFC",
		11: "INVESCO",
		12: "KOTAK_MAHINDRA",
		13: "AXIS",
		14: "LNT",
		15: "CANARA_ROBECO",
		16: "EDELWEISS",
		17: "PGIM",
		18: "BNP_PARIBAS",
		19: "TATA",
		20: "MIRAE",
		21: "LIC",
		22: "IDBI",
		23: "JM_FINANCIAL",
		24: "BOI_AXA",
		25: "UNION",
		26: "HSBC",
		27: "QUANT_MONEY_MANAGERS",
		28: "BARODA",
		29: "MOTILAL_OSWAL",
		30: "IIFL",
		31: "MAHINDRA",
		32: "FRANKLIN_TEMPLETON",
		33: "QUANTUM",
		34: "PRINCIPAL",
		35: "PPFAS",
		36: "TAURUS",
		37: "INDIABULLS",
		38: "WHITEOAK_CAPITAL",
		39: "SHRIRAM",
		40: "NAVI",
		41: "ITI",
		42: "TRUST",
		43: "NJ",
		44: "SAMCO",
		45: "SAHARA",
		46: "BAJAJ_FINSERV",
		47: "HELIOS",
		48: "ZERODHA",
		49: "OLD_BRIDGE",
		50: "UNIFI",
		51: "ANGEL_ONE",
		52: "JIO_BLACKROCK",
	}
	Amc_value = map[string]int32{
		"AMC_UNSPECIFIED":      0,
		"ICICI_PRUDENTIAL":     1,
		"HDFC":                 2,
		"ADITYA_BIRLA":         3,
		"RELIANCE":             4,
		"SBI":                  5,
		"NIPPON_LIFE":          6,
		"UTI":                  7,
		"SUNDARAM":             8,
		"DSP":                  9,
		"IDFC":                 10,
		"INVESCO":              11,
		"KOTAK_MAHINDRA":       12,
		"AXIS":                 13,
		"LNT":                  14,
		"CANARA_ROBECO":        15,
		"EDELWEISS":            16,
		"PGIM":                 17,
		"BNP_PARIBAS":          18,
		"TATA":                 19,
		"MIRAE":                20,
		"LIC":                  21,
		"IDBI":                 22,
		"JM_FINANCIAL":         23,
		"BOI_AXA":              24,
		"UNION":                25,
		"HSBC":                 26,
		"QUANT_MONEY_MANAGERS": 27,
		"BARODA":               28,
		"MOTILAL_OSWAL":        29,
		"IIFL":                 30,
		"MAHINDRA":             31,
		"FRANKLIN_TEMPLETON":   32,
		"QUANTUM":              33,
		"PRINCIPAL":            34,
		"PPFAS":                35,
		"TAURUS":               36,
		"INDIABULLS":           37,
		"WHITEOAK_CAPITAL":     38,
		"SHRIRAM":              39,
		"NAVI":                 40,
		"ITI":                  41,
		"TRUST":                42,
		"NJ":                   43,
		"SAMCO":                44,
		"SAHARA":               45,
		"BAJAJ_FINSERV":        46,
		"HELIOS":               47,
		"ZERODHA":              48,
		"OLD_BRIDGE":           49,
		"UNIFI":                50,
		"ANGEL_ONE":            51,
		"JIO_BLACKROCK":        52,
	}
)

func (x Amc) Enum() *Amc {
	p := new(Amc)
	*p = x
	return p
}

func (x Amc) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Amc) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[1].Descriptor()
}

func (Amc) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[1]
}

func (x Amc) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Amc.Descriptor instead.
func (Amc) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{1}
}

// BankAccountType denotes the type of bank account.
type BankAccountType int32

const (
	BankAccountType_BANK_ACCOUNT_TYPE_UNSPECIFIED BankAccountType = 0
	// Savings deposit means a form of interest bearing demand deposit which is a deposit account
	// whether designated as "Savings Account", "Savings Bank Account", "Savings Deposit Account",
	// "Basic Savings Bank Deposit Account (BSBDA)" or other account by whatever name called
	// which is subject to the restrictions as to the number of withdrawals as also
	// the amounts of withdrawals permitted by the bank during any specified period
	BankAccountType_SAVINGS BankAccountType = 1
	// Current Account means a form of non-interest bearing demand deposit
	// where from withdrawals are allowed any number of times depending upon the balance in the account
	// or up to a particular agreed amount.
	// And shall also be deemed to include other deposit accounts which are neither Savings Deposit nor Term Deposit.
	BankAccountType_CURRENT BankAccountType = 2
)

// Enum value maps for BankAccountType.
var (
	BankAccountType_name = map[int32]string{
		0: "BANK_ACCOUNT_TYPE_UNSPECIFIED",
		1: "SAVINGS",
		2: "CURRENT",
	}
	BankAccountType_value = map[string]int32{
		"BANK_ACCOUNT_TYPE_UNSPECIFIED": 0,
		"SAVINGS":                       1,
		"CURRENT":                       2,
	}
)

func (x BankAccountType) Enum() *BankAccountType {
	p := new(BankAccountType)
	*p = x
	return p
}

func (x BankAccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankAccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[2].Descriptor()
}

func (BankAccountType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[2]
}

func (x BankAccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankAccountType.Descriptor instead.
func (BankAccountType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{2}
}

// The type of plan offer for the mutual fund.
// At Epifi, we only offer DIRECT mutual funds. As RIA, we can only offer DIRECT funds (TODO: confirm with Mohit/Mayank?)
type PlanType int32

const (
	PlanType_PLAN_TYPE_UNSPECIFIED PlanType = 0
	// Direct Mutual Fund is the type of mutual fund that is directly offered by the AMC or fund house.
	// In other words, there is no involvement of third party agents – brokers or distributors.
	// There are no commissions and brokerage. Hence the expense ratio of a direct mutual fund is lower and return is
	// higher.
	PlanType_DIRECT PlanType = 1
	// Regular plans are those mutual fund plans that are bought through an intermediary like brokers, advisors,
	// or distributors. The expense ratio for regular mutual funds is slightly higher than direct mutual funds.
	// Hence the returns tend to be a little higher for direct plans.
	PlanType_REGULAR PlanType = 2
)

// Enum value maps for PlanType.
var (
	PlanType_name = map[int32]string{
		0: "PLAN_TYPE_UNSPECIFIED",
		1: "DIRECT",
		2: "REGULAR",
	}
	PlanType_value = map[string]int32{
		"PLAN_TYPE_UNSPECIFIED": 0,
		"DIRECT":                1,
		"REGULAR":               2,
	}
)

func (x PlanType) Enum() *PlanType {
	p := new(PlanType)
	*p = x
	return p
}

func (x PlanType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlanType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[3].Descriptor()
}

func (PlanType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[3]
}

func (x PlanType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlanType.Descriptor instead.
func (PlanType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{3}
}

// The type of option offered for the mutual fund.
type OptionType int32

const (
	OptionType_OPTION_TYPE_UNSPECIFIED OptionType = 0
	// In case of a dividend option, profits made by the scheme are not reinvested in the scheme.
	// Instead, gains will be distributed among the investors by way of dividends; on a quarterly, half-yearly or annual
	// basis. However, the fund doesn’t guarantee as regards the amount and frequency of dividend payment.
	OptionType_DIVIDEND OptionType = 1
	// The profits made by the scheme are not paid by way of dividend. Instead, these get accumulated and form part
	// of the scheme via reinvestment. So, whenever the scheme makes a profit, its NAV rises automatically.
	OptionType_GROWTH OptionType = 2
)

// Enum value maps for OptionType.
var (
	OptionType_name = map[int32]string{
		0: "OPTION_TYPE_UNSPECIFIED",
		1: "DIVIDEND",
		2: "GROWTH",
	}
	OptionType_value = map[string]int32{
		"OPTION_TYPE_UNSPECIFIED": 0,
		"DIVIDEND":                1,
		"GROWTH":                  2,
	}
)

func (x OptionType) Enum() *OptionType {
	p := new(OptionType)
	*p = x
	return p
}

func (x OptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[4].Descriptor()
}

func (OptionType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[4]
}

func (x OptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OptionType.Descriptor instead.
func (OptionType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{4}
}

// The type of option for the dividend scheme options only. For Growth options, this is unspecified.
type DividendReinvestmentOptionType int32

const (
	DividendReinvestmentOptionType_DIV_REINVESTMENT_OPTION_UNSPECIFIED DividendReinvestmentOptionType = 0
	// In a dividend payout scenario, dividend distributions made by the mutual fund are paid out directly to the shareholder.
	// If the shareholder chooses this option, dividends are usually swept directly into a cash account,
	// transferred electronically into a bank account, or sent out by check.
	DividendReinvestmentOptionType_PAYOUT_ONLY DividendReinvestmentOptionType = 1
	// Instead of paying out the dividends, the amount is used to buy more units of the fund on behalf of the investor.
	// The investor’s account gets credited with the new units.
	DividendReinvestmentOptionType_REINVESTMENT_ONY DividendReinvestmentOptionType = 2
	// Both of 1 and 2 are available for investor to choose from.
	DividendReinvestmentOptionType_PAYOUT_AND_REINVESTMENT DividendReinvestmentOptionType = 3
	DividendReinvestmentOptionType_BONUS                   DividendReinvestmentOptionType = 4
)

// Enum value maps for DividendReinvestmentOptionType.
var (
	DividendReinvestmentOptionType_name = map[int32]string{
		0: "DIV_REINVESTMENT_OPTION_UNSPECIFIED",
		1: "PAYOUT_ONLY",
		2: "REINVESTMENT_ONY",
		3: "PAYOUT_AND_REINVESTMENT",
		4: "BONUS",
	}
	DividendReinvestmentOptionType_value = map[string]int32{
		"DIV_REINVESTMENT_OPTION_UNSPECIFIED": 0,
		"PAYOUT_ONLY":                         1,
		"REINVESTMENT_ONY":                    2,
		"PAYOUT_AND_REINVESTMENT":             3,
		"BONUS":                               4,
	}
)

func (x DividendReinvestmentOptionType) Enum() *DividendReinvestmentOptionType {
	p := new(DividendReinvestmentOptionType)
	*p = x
	return p
}

func (x DividendReinvestmentOptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DividendReinvestmentOptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[5].Descriptor()
}

func (DividendReinvestmentOptionType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[5]
}

func (x DividendReinvestmentOptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DividendReinvestmentOptionType.Descriptor instead.
func (DividendReinvestmentOptionType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{5}
}

// The asset class represents the groupings of similar investment instruments.
// https://www.investopedia.com/terms/a/assetclasses.asp
type AssetClass int32

const (
	AssetClass_ASSET_CLASS_UNSPECIFIED AssetClass = 0
	AssetClass_DEBT                    AssetClass = 1
	AssetClass_EQUITY                  AssetClass = 2
	AssetClass_CASH                    AssetClass = 3
	AssetClass_HYBRID                  AssetClass = 4
	AssetClass_BONDS                   AssetClass = 5
	AssetClass_REAL_STATE              AssetClass = 6
	AssetClass_CRYPTO_CURRENCIES       AssetClass = 7
	AssetClass_CURRENCY                AssetClass = 8
	AssetClass_FUTURE                  AssetClass = 9
	AssetClass_OPTIONS                 AssetClass = 10
	AssetClass_COMMODITY               AssetClass = 11
	// Real Estate Investment Trust
	// https://www.investopedia.com/terms/r/reit.asp
	AssetClass_REIT AssetClass = 12
	// anything not defined above goes into OTHERS.
	AssetClass_OTHERS AssetClass = 13
)

// Enum value maps for AssetClass.
var (
	AssetClass_name = map[int32]string{
		0:  "ASSET_CLASS_UNSPECIFIED",
		1:  "DEBT",
		2:  "EQUITY",
		3:  "CASH",
		4:  "HYBRID",
		5:  "BONDS",
		6:  "REAL_STATE",
		7:  "CRYPTO_CURRENCIES",
		8:  "CURRENCY",
		9:  "FUTURE",
		10: "OPTIONS",
		11: "COMMODITY",
		12: "REIT",
		13: "OTHERS",
	}
	AssetClass_value = map[string]int32{
		"ASSET_CLASS_UNSPECIFIED": 0,
		"DEBT":                    1,
		"EQUITY":                  2,
		"CASH":                    3,
		"HYBRID":                  4,
		"BONDS":                   5,
		"REAL_STATE":              6,
		"CRYPTO_CURRENCIES":       7,
		"CURRENCY":                8,
		"FUTURE":                  9,
		"OPTIONS":                 10,
		"COMMODITY":               11,
		"REIT":                    12,
		"OTHERS":                  13,
	}
)

func (x AssetClass) Enum() *AssetClass {
	p := new(AssetClass)
	*p = x
	return p
}

func (x AssetClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetClass) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[6].Descriptor()
}

func (AssetClass) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[6]
}

func (x AssetClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetClass.Descriptor instead.
func (AssetClass) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{6}
}

// Investment type is a function of investment flexibility and the ease with which they can or cannot be bought or sold.
type InvestmentType int32

const (
	InvestmentType_END_TYPE_UNSPECIFIED InvestmentType = 0
	// A closed ended mutual fund scheme is where your investment is locked in for a specified period of time.
	// You can subscribe to close ended schemes only during the new fund offer period (NFO) and redeem the units
	// only after the lock in period or the tenure of the scheme is over.
	InvestmentType_CLOSED InvestmentType = 1
	// Open ended funds are always open to investment and redemptions, hence, the name open ended funds. Most common.
	// These funds do not have any lock-in period or maturities.
	InvestmentType_OPEN InvestmentType = 2
)

// Enum value maps for InvestmentType.
var (
	InvestmentType_name = map[int32]string{
		0: "END_TYPE_UNSPECIFIED",
		1: "CLOSED",
		2: "OPEN",
	}
	InvestmentType_value = map[string]int32{
		"END_TYPE_UNSPECIFIED": 0,
		"CLOSED":               1,
		"OPEN":                 2,
	}
)

func (x InvestmentType) Enum() *InvestmentType {
	p := new(InvestmentType)
	*p = x
	return p
}

func (x InvestmentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[7].Descriptor()
}

func (InvestmentType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[7]
}

func (x InvestmentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentType.Descriptor instead.
func (InvestmentType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{7}
}

// TODO(anand): discuss with Mohit if this abstraction looks fine.
type SchemeTransactionType int32

const (
	SchemeTransactionType_TXN_TYPE_UNSPECIFIED SchemeTransactionType = 0
	SchemeTransactionType_LUMPSUM              SchemeTransactionType = 1
	SchemeTransactionType_REDEMPTION           SchemeTransactionType = 2
	SchemeTransactionType_SIP                  SchemeTransactionType = 3
	SchemeTransactionType_SWP                  SchemeTransactionType = 4
	SchemeTransactionType_STP                  SchemeTransactionType = 5
	SchemeTransactionType_SWITCH_IN            SchemeTransactionType = 6
	SchemeTransactionType_SWITCH_OUT           SchemeTransactionType = 7
)

// Enum value maps for SchemeTransactionType.
var (
	SchemeTransactionType_name = map[int32]string{
		0: "TXN_TYPE_UNSPECIFIED",
		1: "LUMPSUM",
		2: "REDEMPTION",
		3: "SIP",
		4: "SWP",
		5: "STP",
		6: "SWITCH_IN",
		7: "SWITCH_OUT",
	}
	SchemeTransactionType_value = map[string]int32{
		"TXN_TYPE_UNSPECIFIED": 0,
		"LUMPSUM":              1,
		"REDEMPTION":           2,
		"SIP":                  3,
		"SWP":                  4,
		"STP":                  5,
		"SWITCH_IN":            6,
		"SWITCH_OUT":           7,
	}
)

func (x SchemeTransactionType) Enum() *SchemeTransactionType {
	p := new(SchemeTransactionType)
	*p = x
	return p
}

func (x SchemeTransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SchemeTransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[8].Descriptor()
}

func (SchemeTransactionType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[8]
}

func (x SchemeTransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SchemeTransactionType.Descriptor instead.
func (SchemeTransactionType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{8}
}

type AipFrequency int32

const (
	AipFrequency_AipFrequency_UNSPECIFIED AipFrequency = 0
	AipFrequency_DAILY                    AipFrequency = 1
	AipFrequency_WEEKLY                   AipFrequency = 2
	AipFrequency_MONTHLY                  AipFrequency = 3
	AipFrequency_ANNUALLY                 AipFrequency = 4
	AipFrequency_QUARTERLY                AipFrequency = 5
	AipFrequency_BI_WEEKLY                AipFrequency = 6
	AipFrequency_SEMI_ANNUALLY            AipFrequency = 7
)

// Enum value maps for AipFrequency.
var (
	AipFrequency_name = map[int32]string{
		0: "AipFrequency_UNSPECIFIED",
		1: "DAILY",
		2: "WEEKLY",
		3: "MONTHLY",
		4: "ANNUALLY",
		5: "QUARTERLY",
		6: "BI_WEEKLY",
		7: "SEMI_ANNUALLY",
	}
	AipFrequency_value = map[string]int32{
		"AipFrequency_UNSPECIFIED": 0,
		"DAILY":                    1,
		"WEEKLY":                   2,
		"MONTHLY":                  3,
		"ANNUALLY":                 4,
		"QUARTERLY":                5,
		"BI_WEEKLY":                6,
		"SEMI_ANNUALLY":            7,
	}
)

func (x AipFrequency) Enum() *AipFrequency {
	p := new(AipFrequency)
	*p = x
	return p
}

func (x AipFrequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AipFrequency) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[9].Descriptor()
}

func (AipFrequency) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[9]
}

func (x AipFrequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AipFrequency.Descriptor instead.
func (AipFrequency) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{9}
}

// MutualFundFieldMask denotes the column field masks used during update or select
type MutualFundFieldMask int32

const (
	MutualFundFieldMask_MUTUAL_FUND_FIELD_MASK_UNSPECIFIED MutualFundFieldMask = 0
	MutualFundFieldMask_AMC                                MutualFundFieldMask = 1
	MutualFundFieldMask_NAME_DATA                          MutualFundFieldMask = 2
	MutualFundFieldMask_PLAN_TYPE                          MutualFundFieldMask = 3
	MutualFundFieldMask_INVESTMENT_TYPE                    MutualFundFieldMask = 4
	MutualFundFieldMask_OPTION_TYPE                        MutualFundFieldMask = 5
	MutualFundFieldMask_DIV_REINV_TYPE                     MutualFundFieldMask = 6
	MutualFundFieldMask_NAV                                MutualFundFieldMask = 7
	MutualFundFieldMask_ASSET_CLASS                        MutualFundFieldMask = 8
	MutualFundFieldMask_SIP_ALLOWED                        MutualFundFieldMask = 9
	MutualFundFieldMask_SWP_ALLOWED                        MutualFundFieldMask = 10
	MutualFundFieldMask_STP_ALLOWED                        MutualFundFieldMask = 11
	MutualFundFieldMask_ISIN_NUMBER                        MutualFundFieldMask = 12
	MutualFundFieldMask_TXN_CONSTRAINT                     MutualFundFieldMask = 13
	MutualFundFieldMask_INTERNAL_STATUS                    MutualFundFieldMask = 14
	MutualFundFieldMask_FUNDHOUSE_DEFINED_RISK             MutualFundFieldMask = 15
	MutualFundFieldMask_DAILY_NAV_CHANGE                   MutualFundFieldMask = 16
	MutualFundFieldMask_RETURNS                            MutualFundFieldMask = 17
	MutualFundFieldMask_FI_SCORE                           MutualFundFieldMask = 18
	MutualFundFieldMask_FI_CONTENT                         MutualFundFieldMask = 19
	MutualFundFieldMask_FUND_FUNDAMENTAL_DETAILS           MutualFundFieldMask = 20
	MutualFundFieldMask_PERFORMANCE_METRICS                MutualFundFieldMask = 21
	MutualFundFieldMask_BENCHMARK_DETAILS                  MutualFundFieldMask = 22
	MutualFundFieldMask_AVG_RETURN_FIVE_YEAR               MutualFundFieldMask = 23
	MutualFundFieldMask_CURRENT_AUM                        MutualFundFieldMask = 24
	MutualFundFieldMask_CURRENT_EXPENSE_RATIO              MutualFundFieldMask = 25
	MutualFundFieldMask_UPDATED_AT                         MutualFundFieldMask = 26
	MutualFundFieldMask_CATEGORY_NAME                      MutualFundFieldMask = 27
	MutualFundFieldMask_ALLOWED_USER_GRPOUPS               MutualFundFieldMask = 28
	MutualFundFieldMask_MIN_VERSION_SUPPORT_ANDROID        MutualFundFieldMask = 29
	MutualFundFieldMask_MIN_VERSION_SUPPORT_IOS            MutualFundFieldMask = 30
	MutualFundFieldMask_VERSION_SUPPORT_INFO               MutualFundFieldMask = 31
	MutualFundFieldMask_FI_DEFINED_CATEGORY                MutualFundFieldMask = 32
	MutualFundFieldMask_MUTUAL_FUND_FIELD_MASK_CREATED_AT  MutualFundFieldMask = 33
	MutualFundFieldMask_AVG_RETURN_THREE_YEAR              MutualFundFieldMask = 34
	MutualFundFieldMask_AVG_RETURN_ONE_YEAR                MutualFundFieldMask = 35
	MutualFundFieldMask_FUND_INCEPTION_DATE                MutualFundFieldMask = 36
	MutualFundFieldMask_FUND_INVESTMENT_STATUS             MutualFundFieldMask = 37
	MutualFundFieldMask_HISTORICAL_RETURNS                 MutualFundFieldMask = 38
	MutualFundFieldMask_MIN_SIP_AMOUNT                     MutualFundFieldMask = 39
	MutualFundFieldMask_OBSOLETE_DATE                      MutualFundFieldMask = 40
	MutualFundFieldMask_SCHEME_CODE                        MutualFundFieldMask = 41
	MutualFundFieldMask_FIELDS_EXEMPT_FROM_SYNC            MutualFundFieldMask = 42
	MutualFundFieldMask_VENDOR_METADATA                    MutualFundFieldMask = 43
)

// Enum value maps for MutualFundFieldMask.
var (
	MutualFundFieldMask_name = map[int32]string{
		0:  "MUTUAL_FUND_FIELD_MASK_UNSPECIFIED",
		1:  "AMC",
		2:  "NAME_DATA",
		3:  "PLAN_TYPE",
		4:  "INVESTMENT_TYPE",
		5:  "OPTION_TYPE",
		6:  "DIV_REINV_TYPE",
		7:  "NAV",
		8:  "ASSET_CLASS",
		9:  "SIP_ALLOWED",
		10: "SWP_ALLOWED",
		11: "STP_ALLOWED",
		12: "ISIN_NUMBER",
		13: "TXN_CONSTRAINT",
		14: "INTERNAL_STATUS",
		15: "FUNDHOUSE_DEFINED_RISK",
		16: "DAILY_NAV_CHANGE",
		17: "RETURNS",
		18: "FI_SCORE",
		19: "FI_CONTENT",
		20: "FUND_FUNDAMENTAL_DETAILS",
		21: "PERFORMANCE_METRICS",
		22: "BENCHMARK_DETAILS",
		23: "AVG_RETURN_FIVE_YEAR",
		24: "CURRENT_AUM",
		25: "CURRENT_EXPENSE_RATIO",
		26: "UPDATED_AT",
		27: "CATEGORY_NAME",
		28: "ALLOWED_USER_GRPOUPS",
		29: "MIN_VERSION_SUPPORT_ANDROID",
		30: "MIN_VERSION_SUPPORT_IOS",
		31: "VERSION_SUPPORT_INFO",
		32: "FI_DEFINED_CATEGORY",
		33: "MUTUAL_FUND_FIELD_MASK_CREATED_AT",
		34: "AVG_RETURN_THREE_YEAR",
		35: "AVG_RETURN_ONE_YEAR",
		36: "FUND_INCEPTION_DATE",
		37: "FUND_INVESTMENT_STATUS",
		38: "HISTORICAL_RETURNS",
		39: "MIN_SIP_AMOUNT",
		40: "OBSOLETE_DATE",
		41: "SCHEME_CODE",
		42: "FIELDS_EXEMPT_FROM_SYNC",
		43: "VENDOR_METADATA",
	}
	MutualFundFieldMask_value = map[string]int32{
		"MUTUAL_FUND_FIELD_MASK_UNSPECIFIED": 0,
		"AMC":                                1,
		"NAME_DATA":                          2,
		"PLAN_TYPE":                          3,
		"INVESTMENT_TYPE":                    4,
		"OPTION_TYPE":                        5,
		"DIV_REINV_TYPE":                     6,
		"NAV":                                7,
		"ASSET_CLASS":                        8,
		"SIP_ALLOWED":                        9,
		"SWP_ALLOWED":                        10,
		"STP_ALLOWED":                        11,
		"ISIN_NUMBER":                        12,
		"TXN_CONSTRAINT":                     13,
		"INTERNAL_STATUS":                    14,
		"FUNDHOUSE_DEFINED_RISK":             15,
		"DAILY_NAV_CHANGE":                   16,
		"RETURNS":                            17,
		"FI_SCORE":                           18,
		"FI_CONTENT":                         19,
		"FUND_FUNDAMENTAL_DETAILS":           20,
		"PERFORMANCE_METRICS":                21,
		"BENCHMARK_DETAILS":                  22,
		"AVG_RETURN_FIVE_YEAR":               23,
		"CURRENT_AUM":                        24,
		"CURRENT_EXPENSE_RATIO":              25,
		"UPDATED_AT":                         26,
		"CATEGORY_NAME":                      27,
		"ALLOWED_USER_GRPOUPS":               28,
		"MIN_VERSION_SUPPORT_ANDROID":        29,
		"MIN_VERSION_SUPPORT_IOS":            30,
		"VERSION_SUPPORT_INFO":               31,
		"FI_DEFINED_CATEGORY":                32,
		"MUTUAL_FUND_FIELD_MASK_CREATED_AT":  33,
		"AVG_RETURN_THREE_YEAR":              34,
		"AVG_RETURN_ONE_YEAR":                35,
		"FUND_INCEPTION_DATE":                36,
		"FUND_INVESTMENT_STATUS":             37,
		"HISTORICAL_RETURNS":                 38,
		"MIN_SIP_AMOUNT":                     39,
		"OBSOLETE_DATE":                      40,
		"SCHEME_CODE":                        41,
		"FIELDS_EXEMPT_FROM_SYNC":            42,
		"VENDOR_METADATA":                    43,
	}
)

func (x MutualFundFieldMask) Enum() *MutualFundFieldMask {
	p := new(MutualFundFieldMask)
	*p = x
	return p
}

func (x MutualFundFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[10].Descriptor()
}

func (MutualFundFieldMask) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[10]
}

func (x MutualFundFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundFieldMask.Descriptor instead.
func (MutualFundFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{10}
}

type MutualFundIdentifier int32

const (
	MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_UNSPECIFIED MutualFundIdentifier = 0
	MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ID          MutualFundIdentifier = 1
	MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ISIN        MutualFundIdentifier = 2
)

// Enum value maps for MutualFundIdentifier.
var (
	MutualFundIdentifier_name = map[int32]string{
		0: "MUTUAL_FUND_IDENTIFIER_UNSPECIFIED",
		1: "MUTUAL_FUND_IDENTIFIER_ID",
		2: "MUTUAL_FUND_IDENTIFIER_ISIN",
	}
	MutualFundIdentifier_value = map[string]int32{
		"MUTUAL_FUND_IDENTIFIER_UNSPECIFIED": 0,
		"MUTUAL_FUND_IDENTIFIER_ID":          1,
		"MUTUAL_FUND_IDENTIFIER_ISIN":        2,
	}
)

func (x MutualFundIdentifier) Enum() *MutualFundIdentifier {
	p := new(MutualFundIdentifier)
	*p = x
	return p
}

func (x MutualFundIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[11].Descriptor()
}

func (MutualFundIdentifier) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[11]
}

func (x MutualFundIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundIdentifier.Descriptor instead.
func (MutualFundIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{11}
}

// AmcFieldMask denotes the column field masks used during update
type AmcFieldMask int32

const (
	AmcFieldMask_AMC_FIELD_MASK_UNSPECIFIED AmcFieldMask = 0
	AmcFieldMask_BANK_ACCOUNT_PI_ID         AmcFieldMask = 1
	AmcFieldMask_ACTOR_ID                   AmcFieldMask = 2
	AmcFieldMask_AMC_NAME                   AmcFieldMask = 3
	AmcFieldMask_AMC_CREDIT_ACCOUNT         AmcFieldMask = 4
)

// Enum value maps for AmcFieldMask.
var (
	AmcFieldMask_name = map[int32]string{
		0: "AMC_FIELD_MASK_UNSPECIFIED",
		1: "BANK_ACCOUNT_PI_ID",
		2: "ACTOR_ID",
		3: "AMC_NAME",
		4: "AMC_CREDIT_ACCOUNT",
	}
	AmcFieldMask_value = map[string]int32{
		"AMC_FIELD_MASK_UNSPECIFIED": 0,
		"BANK_ACCOUNT_PI_ID":         1,
		"ACTOR_ID":                   2,
		"AMC_NAME":                   3,
		"AMC_CREDIT_ACCOUNT":         4,
	}
)

func (x AmcFieldMask) Enum() *AmcFieldMask {
	p := new(AmcFieldMask)
	*p = x
	return p
}

func (x AmcFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmcFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[12].Descriptor()
}

func (AmcFieldMask) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[12]
}

func (x AmcFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmcFieldMask.Descriptor instead.
func (AmcFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{12}
}

// MutualFundStatus denotes if a fund is available for investment on our App. This is internal to our offering and has nothing to do with the market.
type MutualFundInternalStatus int32

const (
	MutualFundInternalStatus_MUTUAL_FUND_INTERNAL_STATUS_UNSPECIFIED MutualFundInternalStatus = 0
	MutualFundInternalStatus_AVAILABLE                               MutualFundInternalStatus = 1
	MutualFundInternalStatus_UNAVAILABLE                             MutualFundInternalStatus = 2
)

// Enum value maps for MutualFundInternalStatus.
var (
	MutualFundInternalStatus_name = map[int32]string{
		0: "MUTUAL_FUND_INTERNAL_STATUS_UNSPECIFIED",
		1: "AVAILABLE",
		2: "UNAVAILABLE",
	}
	MutualFundInternalStatus_value = map[string]int32{
		"MUTUAL_FUND_INTERNAL_STATUS_UNSPECIFIED": 0,
		"AVAILABLE":   1,
		"UNAVAILABLE": 2,
	}
)

func (x MutualFundInternalStatus) Enum() *MutualFundInternalStatus {
	p := new(MutualFundInternalStatus)
	*p = x
	return p
}

func (x MutualFundInternalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundInternalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[13].Descriptor()
}

func (MutualFundInternalStatus) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[13]
}

func (x MutualFundInternalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundInternalStatus.Descriptor instead.
func (MutualFundInternalStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{13}
}

type MutualFundCategoryName int32

const (
	MutualFundCategoryName_MutualFundCategoryName_UNSPECIFIED    MutualFundCategoryName = 0
	MutualFundCategoryName_AGGRESSIVE_ALLOCATION                 MutualFundCategoryName = 1
	MutualFundCategoryName_BALANCED_ALLOCATION                   MutualFundCategoryName = 2
	MutualFundCategoryName_CHILDREN                              MutualFundCategoryName = 3
	MutualFundCategoryName_CONSERVATIVE_ALLOCATION               MutualFundCategoryName = 4
	MutualFundCategoryName_DYNAMIC_ASSET_ALLOCATION              MutualFundCategoryName = 5
	MutualFundCategoryName_EQUITY_SAVINGS                        MutualFundCategoryName = 6
	MutualFundCategoryName_FUND_OF_FUNDS                         MutualFundCategoryName = 7
	MutualFundCategoryName_MULTI_ASSET_ALLOCATION                MutualFundCategoryName = 8
	MutualFundCategoryName_RETIREMENT                            MutualFundCategoryName = 9
	MutualFundCategoryName_ARBITRAGE_FUND                        MutualFundCategoryName = 10
	MutualFundCategoryName_SECTOR_PRECIOUS_METALS                MutualFundCategoryName = 11
	MutualFundCategoryName_CONTRA                                MutualFundCategoryName = 12
	MutualFundCategoryName_DIVIDEND_YIELD                        MutualFundCategoryName = 13
	MutualFundCategoryName_ELSS_TAX_SAVING                       MutualFundCategoryName = 14
	MutualFundCategoryName_EQUITY_CONSUMPTION                    MutualFundCategoryName = 15
	MutualFundCategoryName_EQUITY_ESG                            MutualFundCategoryName = 16
	MutualFundCategoryName_EQUITY_INFRASTRUCTURE                 MutualFundCategoryName = 17
	MutualFundCategoryName_EQUITY_OTHER                          MutualFundCategoryName = 18
	MutualFundCategoryName_FLEXI_CAP                             MutualFundCategoryName = 19
	MutualFundCategoryName_FOCUSED_FUND                          MutualFundCategoryName = 20
	MutualFundCategoryName_GLOBAL_OTHER                          MutualFundCategoryName = 21
	MutualFundCategoryName_INDEX_FUNDS                           MutualFundCategoryName = 22
	MutualFundCategoryName_LARGE_AND_MID_CAP                     MutualFundCategoryName = 23
	MutualFundCategoryName_LARGE_CAP                             MutualFundCategoryName = 24
	MutualFundCategoryName_MID_CAP                               MutualFundCategoryName = 25
	MutualFundCategoryName_MULTI_CAP                             MutualFundCategoryName = 26
	MutualFundCategoryName_SECTOR_FINANCIAL_SERVICES             MutualFundCategoryName = 27
	MutualFundCategoryName_SECTOR_FMCG                           MutualFundCategoryName = 28
	MutualFundCategoryName_SECTOR_HEALTHCARE                     MutualFundCategoryName = 29
	MutualFundCategoryName_SECTOR_TECHNOLOGY                     MutualFundCategoryName = 30
	MutualFundCategoryName_SMALL_CAP                             MutualFundCategoryName = 31
	MutualFundCategoryName_TEN_YR_GOVERNMENT_BOND                MutualFundCategoryName = 32
	MutualFundCategoryName_BANKING_AND_PSU                       MutualFundCategoryName = 33
	MutualFundCategoryName_CORPORATE_BOND                        MutualFundCategoryName = 34
	MutualFundCategoryName_CREDIT_RISK                           MutualFundCategoryName = 35
	MutualFundCategoryName_DYNAMIC_BOND                          MutualFundCategoryName = 36
	MutualFundCategoryName_FLOATING_RATE                         MutualFundCategoryName = 37
	MutualFundCategoryName_GOVERNMENT_BOND                       MutualFundCategoryName = 38
	MutualFundCategoryName_LONG_DURATION                         MutualFundCategoryName = 39
	MutualFundCategoryName_LOW_DURATION                          MutualFundCategoryName = 40
	MutualFundCategoryName_MEDIUM_DURATION                       MutualFundCategoryName = 41
	MutualFundCategoryName_MEDIUM_TO_LONG_DURATION               MutualFundCategoryName = 42
	MutualFundCategoryName_MONEY_MARKET                          MutualFundCategoryName = 43
	MutualFundCategoryName_OTHER_BOND                            MutualFundCategoryName = 44
	MutualFundCategoryName_SHORT_DURATION                        MutualFundCategoryName = 45
	MutualFundCategoryName_ULTRA_SHORT_DURATION                  MutualFundCategoryName = 46
	MutualFundCategoryName_LIQUID                                MutualFundCategoryName = 47
	MutualFundCategoryName_OVERNIGHT                             MutualFundCategoryName = 48
	MutualFundCategoryName_VALUE                                 MutualFundCategoryName = 49
	MutualFundCategoryName_INDEX_FUNDS_LARGE_CAP                 MutualFundCategoryName = 50
	MutualFundCategoryName_INDEX_FUNDS_MID_CAP                   MutualFundCategoryName = 51
	MutualFundCategoryName_INDEX_FUNDS_SMALL_CAP                 MutualFundCategoryName = 52
	MutualFundCategoryName_INDEX_FUNDS_BANKING                   MutualFundCategoryName = 53
	MutualFundCategoryName_INDEX_FUNDS_EQUAL_WEIGHT              MutualFundCategoryName = 54
	MutualFundCategoryName_INDEX_FUNDS_S_AND_P_500               MutualFundCategoryName = 55
	MutualFundCategoryName_INDEX_FUNDS_NASDAQ                    MutualFundCategoryName = 56
	MutualFundCategoryName_INDEX_FUNDS_SMART_BETA                MutualFundCategoryName = 57
	MutualFundCategoryName_INDEX_FUNDS_US_TOTAL_MARKET           MutualFundCategoryName = 58
	MutualFundCategoryName_INDEX_FUNDS_TOTAL_MARKET              MutualFundCategoryName = 59
	MutualFundCategoryName_INDEX_FUNDS_HEALTH_CARE               MutualFundCategoryName = 60
	MutualFundCategoryName_INDEX_FUNDS_LARGE_AND_MID             MutualFundCategoryName = 61
	MutualFundCategoryName_INDEX_FUNDS_ASIA_TECH                 MutualFundCategoryName = 62
	MutualFundCategoryName_INDEX_FUNDS_NYSE_FAANG                MutualFundCategoryName = 63
	MutualFundCategoryName_GOLD_FUNDS                            MutualFundCategoryName = 64
	MutualFundCategoryName_SILVER_FUNDS                          MutualFundCategoryName = 65
	MutualFundCategoryName_INDEX_FUNDS_FIXED_INCOME              MutualFundCategoryName = 66
	MutualFundCategoryName_FIXED_MATURITY_INTERMEDIATE_TERM_BOND MutualFundCategoryName = 67
	MutualFundCategoryName_FIXED_MATURITY_SHORT_TERM_BOND        MutualFundCategoryName = 68
	MutualFundCategoryName_FIXED_MATURITY_ULTRASHORT_BOND        MutualFundCategoryName = 69
	MutualFundCategoryName_SECTOR_ENERGY                         MutualFundCategoryName = 70
)

// Enum value maps for MutualFundCategoryName.
var (
	MutualFundCategoryName_name = map[int32]string{
		0:  "MutualFundCategoryName_UNSPECIFIED",
		1:  "AGGRESSIVE_ALLOCATION",
		2:  "BALANCED_ALLOCATION",
		3:  "CHILDREN",
		4:  "CONSERVATIVE_ALLOCATION",
		5:  "DYNAMIC_ASSET_ALLOCATION",
		6:  "EQUITY_SAVINGS",
		7:  "FUND_OF_FUNDS",
		8:  "MULTI_ASSET_ALLOCATION",
		9:  "RETIREMENT",
		10: "ARBITRAGE_FUND",
		11: "SECTOR_PRECIOUS_METALS",
		12: "CONTRA",
		13: "DIVIDEND_YIELD",
		14: "ELSS_TAX_SAVING",
		15: "EQUITY_CONSUMPTION",
		16: "EQUITY_ESG",
		17: "EQUITY_INFRASTRUCTURE",
		18: "EQUITY_OTHER",
		19: "FLEXI_CAP",
		20: "FOCUSED_FUND",
		21: "GLOBAL_OTHER",
		22: "INDEX_FUNDS",
		23: "LARGE_AND_MID_CAP",
		24: "LARGE_CAP",
		25: "MID_CAP",
		26: "MULTI_CAP",
		27: "SECTOR_FINANCIAL_SERVICES",
		28: "SECTOR_FMCG",
		29: "SECTOR_HEALTHCARE",
		30: "SECTOR_TECHNOLOGY",
		31: "SMALL_CAP",
		32: "TEN_YR_GOVERNMENT_BOND",
		33: "BANKING_AND_PSU",
		34: "CORPORATE_BOND",
		35: "CREDIT_RISK",
		36: "DYNAMIC_BOND",
		37: "FLOATING_RATE",
		38: "GOVERNMENT_BOND",
		39: "LONG_DURATION",
		40: "LOW_DURATION",
		41: "MEDIUM_DURATION",
		42: "MEDIUM_TO_LONG_DURATION",
		43: "MONEY_MARKET",
		44: "OTHER_BOND",
		45: "SHORT_DURATION",
		46: "ULTRA_SHORT_DURATION",
		47: "LIQUID",
		48: "OVERNIGHT",
		49: "VALUE",
		50: "INDEX_FUNDS_LARGE_CAP",
		51: "INDEX_FUNDS_MID_CAP",
		52: "INDEX_FUNDS_SMALL_CAP",
		53: "INDEX_FUNDS_BANKING",
		54: "INDEX_FUNDS_EQUAL_WEIGHT",
		55: "INDEX_FUNDS_S_AND_P_500",
		56: "INDEX_FUNDS_NASDAQ",
		57: "INDEX_FUNDS_SMART_BETA",
		58: "INDEX_FUNDS_US_TOTAL_MARKET",
		59: "INDEX_FUNDS_TOTAL_MARKET",
		60: "INDEX_FUNDS_HEALTH_CARE",
		61: "INDEX_FUNDS_LARGE_AND_MID",
		62: "INDEX_FUNDS_ASIA_TECH",
		63: "INDEX_FUNDS_NYSE_FAANG",
		64: "GOLD_FUNDS",
		65: "SILVER_FUNDS",
		66: "INDEX_FUNDS_FIXED_INCOME",
		67: "FIXED_MATURITY_INTERMEDIATE_TERM_BOND",
		68: "FIXED_MATURITY_SHORT_TERM_BOND",
		69: "FIXED_MATURITY_ULTRASHORT_BOND",
		70: "SECTOR_ENERGY",
	}
	MutualFundCategoryName_value = map[string]int32{
		"MutualFundCategoryName_UNSPECIFIED":    0,
		"AGGRESSIVE_ALLOCATION":                 1,
		"BALANCED_ALLOCATION":                   2,
		"CHILDREN":                              3,
		"CONSERVATIVE_ALLOCATION":               4,
		"DYNAMIC_ASSET_ALLOCATION":              5,
		"EQUITY_SAVINGS":                        6,
		"FUND_OF_FUNDS":                         7,
		"MULTI_ASSET_ALLOCATION":                8,
		"RETIREMENT":                            9,
		"ARBITRAGE_FUND":                        10,
		"SECTOR_PRECIOUS_METALS":                11,
		"CONTRA":                                12,
		"DIVIDEND_YIELD":                        13,
		"ELSS_TAX_SAVING":                       14,
		"EQUITY_CONSUMPTION":                    15,
		"EQUITY_ESG":                            16,
		"EQUITY_INFRASTRUCTURE":                 17,
		"EQUITY_OTHER":                          18,
		"FLEXI_CAP":                             19,
		"FOCUSED_FUND":                          20,
		"GLOBAL_OTHER":                          21,
		"INDEX_FUNDS":                           22,
		"LARGE_AND_MID_CAP":                     23,
		"LARGE_CAP":                             24,
		"MID_CAP":                               25,
		"MULTI_CAP":                             26,
		"SECTOR_FINANCIAL_SERVICES":             27,
		"SECTOR_FMCG":                           28,
		"SECTOR_HEALTHCARE":                     29,
		"SECTOR_TECHNOLOGY":                     30,
		"SMALL_CAP":                             31,
		"TEN_YR_GOVERNMENT_BOND":                32,
		"BANKING_AND_PSU":                       33,
		"CORPORATE_BOND":                        34,
		"CREDIT_RISK":                           35,
		"DYNAMIC_BOND":                          36,
		"FLOATING_RATE":                         37,
		"GOVERNMENT_BOND":                       38,
		"LONG_DURATION":                         39,
		"LOW_DURATION":                          40,
		"MEDIUM_DURATION":                       41,
		"MEDIUM_TO_LONG_DURATION":               42,
		"MONEY_MARKET":                          43,
		"OTHER_BOND":                            44,
		"SHORT_DURATION":                        45,
		"ULTRA_SHORT_DURATION":                  46,
		"LIQUID":                                47,
		"OVERNIGHT":                             48,
		"VALUE":                                 49,
		"INDEX_FUNDS_LARGE_CAP":                 50,
		"INDEX_FUNDS_MID_CAP":                   51,
		"INDEX_FUNDS_SMALL_CAP":                 52,
		"INDEX_FUNDS_BANKING":                   53,
		"INDEX_FUNDS_EQUAL_WEIGHT":              54,
		"INDEX_FUNDS_S_AND_P_500":               55,
		"INDEX_FUNDS_NASDAQ":                    56,
		"INDEX_FUNDS_SMART_BETA":                57,
		"INDEX_FUNDS_US_TOTAL_MARKET":           58,
		"INDEX_FUNDS_TOTAL_MARKET":              59,
		"INDEX_FUNDS_HEALTH_CARE":               60,
		"INDEX_FUNDS_LARGE_AND_MID":             61,
		"INDEX_FUNDS_ASIA_TECH":                 62,
		"INDEX_FUNDS_NYSE_FAANG":                63,
		"GOLD_FUNDS":                            64,
		"SILVER_FUNDS":                          65,
		"INDEX_FUNDS_FIXED_INCOME":              66,
		"FIXED_MATURITY_INTERMEDIATE_TERM_BOND": 67,
		"FIXED_MATURITY_SHORT_TERM_BOND":        68,
		"FIXED_MATURITY_ULTRASHORT_BOND":        69,
		"SECTOR_ENERGY":                         70,
	}
)

func (x MutualFundCategoryName) Enum() *MutualFundCategoryName {
	p := new(MutualFundCategoryName)
	*p = x
	return p
}

func (x MutualFundCategoryName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundCategoryName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[14].Descriptor()
}

func (MutualFundCategoryName) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[14]
}

func (x MutualFundCategoryName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundCategoryName.Descriptor instead.
func (MutualFundCategoryName) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{14}
}

type FundhouseDefinedRiskLevel int32

const (
	FundhouseDefinedRiskLevel_FundhouseDefinedRiskLevel_UNSPECIFIED FundhouseDefinedRiskLevel = 0
	FundhouseDefinedRiskLevel_VERY_HIGH_RISK                        FundhouseDefinedRiskLevel = 1
	FundhouseDefinedRiskLevel_MODERATELY_HIGH_RISK                  FundhouseDefinedRiskLevel = 2
	FundhouseDefinedRiskLevel_MODERATE_RISK                         FundhouseDefinedRiskLevel = 3
	FundhouseDefinedRiskLevel_LOW_TO_MODERATE_RISK                  FundhouseDefinedRiskLevel = 4
	FundhouseDefinedRiskLevel_LOW_RISK                              FundhouseDefinedRiskLevel = 5
	FundhouseDefinedRiskLevel_HIGH_RISK                             FundhouseDefinedRiskLevel = 6
)

// Enum value maps for FundhouseDefinedRiskLevel.
var (
	FundhouseDefinedRiskLevel_name = map[int32]string{
		0: "FundhouseDefinedRiskLevel_UNSPECIFIED",
		1: "VERY_HIGH_RISK",
		2: "MODERATELY_HIGH_RISK",
		3: "MODERATE_RISK",
		4: "LOW_TO_MODERATE_RISK",
		5: "LOW_RISK",
		6: "HIGH_RISK",
	}
	FundhouseDefinedRiskLevel_value = map[string]int32{
		"FundhouseDefinedRiskLevel_UNSPECIFIED": 0,
		"VERY_HIGH_RISK":                        1,
		"MODERATELY_HIGH_RISK":                  2,
		"MODERATE_RISK":                         3,
		"LOW_TO_MODERATE_RISK":                  4,
		"LOW_RISK":                              5,
		"HIGH_RISK":                             6,
	}
)

func (x FundhouseDefinedRiskLevel) Enum() *FundhouseDefinedRiskLevel {
	p := new(FundhouseDefinedRiskLevel)
	*p = x
	return p
}

func (x FundhouseDefinedRiskLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FundhouseDefinedRiskLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[15].Descriptor()
}

func (FundhouseDefinedRiskLevel) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[15]
}

func (x FundhouseDefinedRiskLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FundhouseDefinedRiskLevel.Descriptor instead.
func (FundhouseDefinedRiskLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{15}
}

// based on the sentiment of a nugget user will be able to make a better decision
// 'Least fund house expense' is a positive sentiment
// 'Market risk sensitive' is a negative sentiment
type NuggetSentiment int32

const (
	NuggetSentiment_NUGGET_SENTIMENT_UNSPECIFIED NuggetSentiment = 0
	NuggetSentiment_NEUTRAL                      NuggetSentiment = 1
	NuggetSentiment_POSITIVE                     NuggetSentiment = 2
	NuggetSentiment_NEGATIVE                     NuggetSentiment = 3
)

// Enum value maps for NuggetSentiment.
var (
	NuggetSentiment_name = map[int32]string{
		0: "NUGGET_SENTIMENT_UNSPECIFIED",
		1: "NEUTRAL",
		2: "POSITIVE",
		3: "NEGATIVE",
	}
	NuggetSentiment_value = map[string]int32{
		"NUGGET_SENTIMENT_UNSPECIFIED": 0,
		"NEUTRAL":                      1,
		"POSITIVE":                     2,
		"NEGATIVE":                     3,
	}
)

func (x NuggetSentiment) Enum() *NuggetSentiment {
	p := new(NuggetSentiment)
	*p = x
	return p
}

func (x NuggetSentiment) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NuggetSentiment) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[16].Descriptor()
}

func (NuggetSentiment) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[16]
}

func (x NuggetSentiment) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NuggetSentiment.Descriptor instead.
func (NuggetSentiment) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{16}
}

type GlobalEquitySector int32

const (
	GlobalEquitySector_GlobalEquitySector_UNSPECIFIED GlobalEquitySector = 0
	GlobalEquitySector_BASIC_MATERIALS_NET            GlobalEquitySector = 1
	GlobalEquitySector_COMMUNICATION_SERVICES_NET     GlobalEquitySector = 2
	GlobalEquitySector_CONSUMER_CYCLICAL_NET          GlobalEquitySector = 3
	GlobalEquitySector_CONSUMER_DEFENSIVE_NET         GlobalEquitySector = 4
	GlobalEquitySector_ENERGY_NET                     GlobalEquitySector = 5
	GlobalEquitySector_FINANCIAL_SERVICES_NET         GlobalEquitySector = 6
	GlobalEquitySector_HEALTHCARE_NET                 GlobalEquitySector = 7
	GlobalEquitySector_INDUSTRIALS_NET                GlobalEquitySector = 8
	GlobalEquitySector_REALESTATE_NET                 GlobalEquitySector = 9
	GlobalEquitySector_TECHNOLOGY_NET                 GlobalEquitySector = 10
	GlobalEquitySector_UTILITIES_NET                  GlobalEquitySector = 11
)

// Enum value maps for GlobalEquitySector.
var (
	GlobalEquitySector_name = map[int32]string{
		0:  "GlobalEquitySector_UNSPECIFIED",
		1:  "BASIC_MATERIALS_NET",
		2:  "COMMUNICATION_SERVICES_NET",
		3:  "CONSUMER_CYCLICAL_NET",
		4:  "CONSUMER_DEFENSIVE_NET",
		5:  "ENERGY_NET",
		6:  "FINANCIAL_SERVICES_NET",
		7:  "HEALTHCARE_NET",
		8:  "INDUSTRIALS_NET",
		9:  "REALESTATE_NET",
		10: "TECHNOLOGY_NET",
		11: "UTILITIES_NET",
	}
	GlobalEquitySector_value = map[string]int32{
		"GlobalEquitySector_UNSPECIFIED": 0,
		"BASIC_MATERIALS_NET":            1,
		"COMMUNICATION_SERVICES_NET":     2,
		"CONSUMER_CYCLICAL_NET":          3,
		"CONSUMER_DEFENSIVE_NET":         4,
		"ENERGY_NET":                     5,
		"FINANCIAL_SERVICES_NET":         6,
		"HEALTHCARE_NET":                 7,
		"INDUSTRIALS_NET":                8,
		"REALESTATE_NET":                 9,
		"TECHNOLOGY_NET":                 10,
		"UTILITIES_NET":                  11,
	}
)

func (x GlobalEquitySector) Enum() *GlobalEquitySector {
	p := new(GlobalEquitySector)
	*p = x
	return p
}

func (x GlobalEquitySector) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GlobalEquitySector) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[17].Descriptor()
}

func (GlobalEquitySector) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[17]
}

func (x GlobalEquitySector) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GlobalEquitySector.Descriptor instead.
func (GlobalEquitySector) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{17}
}

type GlobalbondSector int32

const (
	GlobalbondSector_GlobalbondSector_UNSPECIFIED                       GlobalbondSector = 0
	GlobalbondSector_PRIMARY_SECTOR_AGENCY_MORTGAGE_BACKED_NET          GlobalbondSector = 1
	GlobalbondSector_PRIMARY_SECTOR_ASSET_BACKED_NET                    GlobalbondSector = 2
	GlobalbondSector_PRIMARY_SECTOR_BANK_LOAN_NET                       GlobalbondSector = 3
	GlobalbondSector_PRIMARY_SECTOR_CASH_EQUIVALENTS_NET                GlobalbondSector = 4
	GlobalbondSector_PRIMARY_SECTOR_COMMERCIAL_MORTGAGE_BACKED_NET      GlobalbondSector = 5
	GlobalbondSector_PRIMARY_SECTOR_CONVERTIBLE_NET                     GlobalbondSector = 6
	GlobalbondSector_PRIMARY_SECTOR_CORPORATE_BOND_NET                  GlobalbondSector = 7
	GlobalbondSector_PRIMARY_SECTOR_COVERED_BOND_NET                    GlobalbondSector = 8
	GlobalbondSector_PRIMARY_SECTOR_FORWARD_FUTURE_NET                  GlobalbondSector = 9
	GlobalbondSector_PRIMARY_SECTOR_GOVERNMENT_NET                      GlobalbondSector = 10
	GlobalbondSector_PRIMARY_SECTOR_GOVERNMENT_RELATED_NET              GlobalbondSector = 11
	GlobalbondSector_PRIMARY_SECTOR_MUNICIPAL_TAX_EXEMPT_NET            GlobalbondSector = 12
	GlobalbondSector_PRIMARY_SECTOR_MUNICIPAL_TAXABLE_NET               GlobalbondSector = 13
	GlobalbondSector_PRIMARY_SECTOR_NON_AGENCY_RESIDENTIAL_MORTGAGE_NET GlobalbondSector = 14
	GlobalbondSector_PRIMARY_SECTOR_OPTION_WARRANT_NET                  GlobalbondSector = 15
	GlobalbondSector_PRIMARY_SECTOR_PREFERRED_STOCK_NET                 GlobalbondSector = 16
	GlobalbondSector_PRIMARY_SECTOR_SWAP_NET                            GlobalbondSector = 17
	GlobalbondSector_SUPER_SECTOR_CASH_EQUIVALENTS_NET                  GlobalbondSector = 18
	GlobalbondSector_SUPER_SECTOR_CORPORATE_NET                         GlobalbondSector = 19
	GlobalbondSector_SUPER_SECTOR_DERIVATIVE_NET                        GlobalbondSector = 20
	GlobalbondSector_SUPER_SECTOR_GOVERNMENT_NET                        GlobalbondSector = 21
	GlobalbondSector_SUPER_SECTOR_MUNICIPAL_NET                         GlobalbondSector = 22
	GlobalbondSector_SUPER_SECTOR_SECURITIZED_NET                       GlobalbondSector = 23
)

// Enum value maps for GlobalbondSector.
var (
	GlobalbondSector_name = map[int32]string{
		0:  "GlobalbondSector_UNSPECIFIED",
		1:  "PRIMARY_SECTOR_AGENCY_MORTGAGE_BACKED_NET",
		2:  "PRIMARY_SECTOR_ASSET_BACKED_NET",
		3:  "PRIMARY_SECTOR_BANK_LOAN_NET",
		4:  "PRIMARY_SECTOR_CASH_EQUIVALENTS_NET",
		5:  "PRIMARY_SECTOR_COMMERCIAL_MORTGAGE_BACKED_NET",
		6:  "PRIMARY_SECTOR_CONVERTIBLE_NET",
		7:  "PRIMARY_SECTOR_CORPORATE_BOND_NET",
		8:  "PRIMARY_SECTOR_COVERED_BOND_NET",
		9:  "PRIMARY_SECTOR_FORWARD_FUTURE_NET",
		10: "PRIMARY_SECTOR_GOVERNMENT_NET",
		11: "PRIMARY_SECTOR_GOVERNMENT_RELATED_NET",
		12: "PRIMARY_SECTOR_MUNICIPAL_TAX_EXEMPT_NET",
		13: "PRIMARY_SECTOR_MUNICIPAL_TAXABLE_NET",
		14: "PRIMARY_SECTOR_NON_AGENCY_RESIDENTIAL_MORTGAGE_NET",
		15: "PRIMARY_SECTOR_OPTION_WARRANT_NET",
		16: "PRIMARY_SECTOR_PREFERRED_STOCK_NET",
		17: "PRIMARY_SECTOR_SWAP_NET",
		18: "SUPER_SECTOR_CASH_EQUIVALENTS_NET",
		19: "SUPER_SECTOR_CORPORATE_NET",
		20: "SUPER_SECTOR_DERIVATIVE_NET",
		21: "SUPER_SECTOR_GOVERNMENT_NET",
		22: "SUPER_SECTOR_MUNICIPAL_NET",
		23: "SUPER_SECTOR_SECURITIZED_NET",
	}
	GlobalbondSector_value = map[string]int32{
		"GlobalbondSector_UNSPECIFIED":                       0,
		"PRIMARY_SECTOR_AGENCY_MORTGAGE_BACKED_NET":          1,
		"PRIMARY_SECTOR_ASSET_BACKED_NET":                    2,
		"PRIMARY_SECTOR_BANK_LOAN_NET":                       3,
		"PRIMARY_SECTOR_CASH_EQUIVALENTS_NET":                4,
		"PRIMARY_SECTOR_COMMERCIAL_MORTGAGE_BACKED_NET":      5,
		"PRIMARY_SECTOR_CONVERTIBLE_NET":                     6,
		"PRIMARY_SECTOR_CORPORATE_BOND_NET":                  7,
		"PRIMARY_SECTOR_COVERED_BOND_NET":                    8,
		"PRIMARY_SECTOR_FORWARD_FUTURE_NET":                  9,
		"PRIMARY_SECTOR_GOVERNMENT_NET":                      10,
		"PRIMARY_SECTOR_GOVERNMENT_RELATED_NET":              11,
		"PRIMARY_SECTOR_MUNICIPAL_TAX_EXEMPT_NET":            12,
		"PRIMARY_SECTOR_MUNICIPAL_TAXABLE_NET":               13,
		"PRIMARY_SECTOR_NON_AGENCY_RESIDENTIAL_MORTGAGE_NET": 14,
		"PRIMARY_SECTOR_OPTION_WARRANT_NET":                  15,
		"PRIMARY_SECTOR_PREFERRED_STOCK_NET":                 16,
		"PRIMARY_SECTOR_SWAP_NET":                            17,
		"SUPER_SECTOR_CASH_EQUIVALENTS_NET":                  18,
		"SUPER_SECTOR_CORPORATE_NET":                         19,
		"SUPER_SECTOR_DERIVATIVE_NET":                        20,
		"SUPER_SECTOR_GOVERNMENT_NET":                        21,
		"SUPER_SECTOR_MUNICIPAL_NET":                         22,
		"SUPER_SECTOR_SECURITIZED_NET":                       23,
	}
)

func (x GlobalbondSector) Enum() *GlobalbondSector {
	p := new(GlobalbondSector)
	*p = x
	return p
}

func (x GlobalbondSector) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GlobalbondSector) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[18].Descriptor()
}

func (GlobalbondSector) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[18]
}

func (x GlobalbondSector) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GlobalbondSector.Descriptor instead.
func (GlobalbondSector) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{18}
}

// DeferLoadBreakPointUnit is the unit of tenure when a defer load(as defined below) is applicable
type DeferLoadBreakPointUnit int32

const (
	DeferLoadBreakPointUnit_DeferLoadBreakPointUnit_UNSPECIFIED DeferLoadBreakPointUnit = 0
	DeferLoadBreakPointUnit_YEARS                               DeferLoadBreakPointUnit = 1
	DeferLoadBreakPointUnit_MONTHS                              DeferLoadBreakPointUnit = 2
	DeferLoadBreakPointUnit_DAYS                                DeferLoadBreakPointUnit = 3
)

// Enum value maps for DeferLoadBreakPointUnit.
var (
	DeferLoadBreakPointUnit_name = map[int32]string{
		0: "DeferLoadBreakPointUnit_UNSPECIFIED",
		1: "YEARS",
		2: "MONTHS",
		3: "DAYS",
	}
	DeferLoadBreakPointUnit_value = map[string]int32{
		"DeferLoadBreakPointUnit_UNSPECIFIED": 0,
		"YEARS":                               1,
		"MONTHS":                              2,
		"DAYS":                                3,
	}
)

func (x DeferLoadBreakPointUnit) Enum() *DeferLoadBreakPointUnit {
	p := new(DeferLoadBreakPointUnit)
	*p = x
	return p
}

func (x DeferLoadBreakPointUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeferLoadBreakPointUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[19].Descriptor()
}

func (DeferLoadBreakPointUnit) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[19]
}

func (x DeferLoadBreakPointUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeferLoadBreakPointUnit.Descriptor instead.
func (DeferLoadBreakPointUnit) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{19}
}

type DeferLoadUnit int32

const (
	DeferLoadUnit_DeferLoadUnit_UNSPECIFIED DeferLoadUnit = 0
	DeferLoadUnit_PERCENTAGE                DeferLoadUnit = 1
)

// Enum value maps for DeferLoadUnit.
var (
	DeferLoadUnit_name = map[int32]string{
		0: "DeferLoadUnit_UNSPECIFIED",
		1: "PERCENTAGE",
	}
	DeferLoadUnit_value = map[string]int32{
		"DeferLoadUnit_UNSPECIFIED": 0,
		"PERCENTAGE":                1,
	}
)

func (x DeferLoadUnit) Enum() *DeferLoadUnit {
	p := new(DeferLoadUnit)
	*p = x
	return p
}

func (x DeferLoadUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeferLoadUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[20].Descriptor()
}

func (DeferLoadUnit) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[20]
}

func (x DeferLoadUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeferLoadUnit.Descriptor instead.
func (DeferLoadUnit) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{20}
}

type CategoryAvgFieldMask int32

const (
	CategoryAvgFieldMask_CategoryAvgFieldMask_UNDEFINED CategoryAvgFieldMask = 0
	CategoryAvgFieldMask_CATEGORY                       CategoryAvgFieldMask = 1
	CategoryAvgFieldMask_AVG_RETURNS                    CategoryAvgFieldMask = 3
	CategoryAvgFieldMask_EXPENSE_RATIO                  CategoryAvgFieldMask = 4
	CategoryAvgFieldMask_TRACKING_ERROR                 CategoryAvgFieldMask = 5
	CategoryAvgFieldMask_SHARPE_RATIO                   CategoryAvgFieldMask = 6
	CategoryAvgFieldMask_INFORMATION_RATIO              CategoryAvgFieldMask = 7
	CategoryAvgFieldMask_ALPHA                          CategoryAvgFieldMask = 8
	CategoryAvgFieldMask_AUM                            CategoryAvgFieldMask = 9
	CategoryAvgFieldMask_FUND_AGE                       CategoryAvgFieldMask = 10
	CategoryAvgFieldMask_CAT_AVG_HISTORICAL_RETURNS     CategoryAvgFieldMask = 11
)

// Enum value maps for CategoryAvgFieldMask.
var (
	CategoryAvgFieldMask_name = map[int32]string{
		0:  "CategoryAvgFieldMask_UNDEFINED",
		1:  "CATEGORY",
		3:  "AVG_RETURNS",
		4:  "EXPENSE_RATIO",
		5:  "TRACKING_ERROR",
		6:  "SHARPE_RATIO",
		7:  "INFORMATION_RATIO",
		8:  "ALPHA",
		9:  "AUM",
		10: "FUND_AGE",
		11: "CAT_AVG_HISTORICAL_RETURNS",
	}
	CategoryAvgFieldMask_value = map[string]int32{
		"CategoryAvgFieldMask_UNDEFINED": 0,
		"CATEGORY":                       1,
		"AVG_RETURNS":                    3,
		"EXPENSE_RATIO":                  4,
		"TRACKING_ERROR":                 5,
		"SHARPE_RATIO":                   6,
		"INFORMATION_RATIO":              7,
		"ALPHA":                          8,
		"AUM":                            9,
		"FUND_AGE":                       10,
		"CAT_AVG_HISTORICAL_RETURNS":     11,
	}
)

func (x CategoryAvgFieldMask) Enum() *CategoryAvgFieldMask {
	p := new(CategoryAvgFieldMask)
	*p = x
	return p
}

func (x CategoryAvgFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CategoryAvgFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[21].Descriptor()
}

func (CategoryAvgFieldMask) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[21]
}

func (x CategoryAvgFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CategoryAvgFieldMask.Descriptor instead.
func (CategoryAvgFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{21}
}

// MutualFundNavSource defines the source of the mutual fund nav value
type MutualFundNavSource int32

const (
	MutualFundNavSource_MUTUAL_FUND_NAV_SOURCE_UNSPECIFIED MutualFundNavSource = 0
	// nav value sourced from a vendor e.g. morningstar feed
	MutualFundNavSource_MUTUAL_FUND_NAV_SOURCE_VENDOR MutualFundNavSource = 1
	// nav on market holidays is derived from the last market close value
	MutualFundNavSource_MUTUAL_FUND_NAV_SOURCE_HOLIDAY_DERIVED MutualFundNavSource = 2
)

// Enum value maps for MutualFundNavSource.
var (
	MutualFundNavSource_name = map[int32]string{
		0: "MUTUAL_FUND_NAV_SOURCE_UNSPECIFIED",
		1: "MUTUAL_FUND_NAV_SOURCE_VENDOR",
		2: "MUTUAL_FUND_NAV_SOURCE_HOLIDAY_DERIVED",
	}
	MutualFundNavSource_value = map[string]int32{
		"MUTUAL_FUND_NAV_SOURCE_UNSPECIFIED":     0,
		"MUTUAL_FUND_NAV_SOURCE_VENDOR":          1,
		"MUTUAL_FUND_NAV_SOURCE_HOLIDAY_DERIVED": 2,
	}
)

func (x MutualFundNavSource) Enum() *MutualFundNavSource {
	p := new(MutualFundNavSource)
	*p = x
	return p
}

func (x MutualFundNavSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundNavSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[22].Descriptor()
}

func (MutualFundNavSource) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[22]
}

func (x MutualFundNavSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundNavSource.Descriptor instead.
func (MutualFundNavSource) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{22}
}

type MutualFundNavVendor int32

const (
	MutualFundNavVendor_MUTUAL_FUND_NAV_VENDOR_UNSPECIFIED MutualFundNavVendor = 0
	MutualFundNavVendor_MUTUAL_FUND_NAV_VENDOR_MORNINGSTAR MutualFundNavVendor = 1
)

// Enum value maps for MutualFundNavVendor.
var (
	MutualFundNavVendor_name = map[int32]string{
		0: "MUTUAL_FUND_NAV_VENDOR_UNSPECIFIED",
		1: "MUTUAL_FUND_NAV_VENDOR_MORNINGSTAR",
	}
	MutualFundNavVendor_value = map[string]int32{
		"MUTUAL_FUND_NAV_VENDOR_UNSPECIFIED": 0,
		"MUTUAL_FUND_NAV_VENDOR_MORNINGSTAR": 1,
	}
)

func (x MutualFundNavVendor) Enum() *MutualFundNavVendor {
	p := new(MutualFundNavVendor)
	*p = x
	return p
}

func (x MutualFundNavVendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundNavVendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_mutual_fund_proto_enumTypes[23].Descriptor()
}

func (MutualFundNavVendor) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_mutual_fund_proto_enumTypes[23]
}

func (x MutualFundNavVendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundNavVendor.Descriptor instead.
func (MutualFundNavVendor) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{23}
}

// A mutual fund is an investment instrument that pools money from many investors which is further invested by a
// professional fund manager. The fund manager can invest this pooled money to purchase securities like stocks, bonds,
// gold or any combination of these.
//
// Every mutual fund works around certain investment objectives and attempts to achieve the same. The fund manager plans
// the investment accordingly and allocates the asset between stocks and bonds. Combining all, these securities
// form the portfolio composition of the selected scheme.
//
//go:generate gen_sql -types=DeferLoad,ExitLoad,AipDetail,SwpMetadata,StpMetadata,SipMetadata,FundManager,FundManagers,GlobalBondSectors,GlobalEquitySectors,MarketCap,MaturityBreakdown,CreditQualityBreakdown,EquityCountryExposureBreakdown,EquityCountryExposure,AssetAllocationBreakdown,TrackingError,Returns,VendorMetadata,MorningstarData,MutualFundCategoryName,FundhouseDefinedRiskLevel,PerformanceMetrics,BenchmarkDetails,FundFundamentalDetails,FiContent,GlobalEquitySector,GlobalbondSector,SharpeRatio,InformationRatio,Alpha,Holdings,Aum,MutualFundCategoryAverage,DeferLoadBreakPointUnit,DeferLoadUnit,ExpenseRatio,VersionSupportInfo,NuggetSentiment,Nugget,FundInvestmentStatus,HistoricalReturns,ReturnData,MutualFundNavVendor,MutualFundNavSource,MutualFundFieldMask,FieldsExemptFromSync,HoldingDetail
type MutualFund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Primary identifier to a mutual_fund database model.
	Id                 string                         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Amc                Amc                            `protobuf:"varint,2,opt,name=amc,proto3,enum=api.investment.mutualfund.Amc" json:"amc,omitempty"`
	NameData           *FundNameMetadata              `protobuf:"bytes,3,opt,name=name_data,json=nameData,proto3" json:"name_data,omitempty"`
	PlanType           PlanType                       `protobuf:"varint,4,opt,name=plan_type,json=planType,proto3,enum=api.investment.mutualfund.PlanType" json:"plan_type,omitempty"`
	InvestmentType     InvestmentType                 `protobuf:"varint,12,opt,name=investment_type,json=investmentType,proto3,enum=api.investment.mutualfund.InvestmentType" json:"investment_type,omitempty"`
	OptionType         OptionType                     `protobuf:"varint,5,opt,name=option_type,json=optionType,proto3,enum=api.investment.mutualfund.OptionType" json:"option_type,omitempty"`
	DivReinvOptionType DividendReinvestmentOptionType `protobuf:"varint,6,opt,name=div_reinv_option_type,json=divReinvOptionType,proto3,enum=api.investment.mutualfund.DividendReinvestmentOptionType" json:"div_reinv_option_type,omitempty"`
	// NAV stands for 'Net Asset Value'
	// In simple words, NAV is the market value of the securities held by the scheme.
	// Since market value of securities changes every day, NAV of a scheme also varies on day to day basis.
	// The NAV per unit is the market value of securities of a scheme divided by the total number of units of the
	// scheme on any particular date.
	Nav        *money.Money `protobuf:"bytes,7,opt,name=nav,proto3" json:"nav,omitempty"`
	AssetClass AssetClass   `protobuf:"varint,8,opt,name=asset_class,json=assetClass,proto3,enum=api.investment.mutualfund.AssetClass" json:"asset_class,omitempty"`
	// true if SIP transactions are allowed
	SipAllowed bool `protobuf:"varint,9,opt,name=sip_allowed,json=sipAllowed,proto3" json:"sip_allowed,omitempty"`
	// true if SWP transactions are allowed
	SwpAllowed bool `protobuf:"varint,10,opt,name=swp_allowed,json=swpAllowed,proto3" json:"swp_allowed,omitempty"`
	// true if STP transactions are allowed
	StpAllowed bool `protobuf:"varint,11,opt,name=stp_allowed,json=stpAllowed,proto3" json:"stp_allowed,omitempty"`
	// https://www.isin.net/isin/mutual-fund/
	// https://www.investorq.com/question/what-is-the-isin-number-in-sip--mutual-funds----its-importance-
	IsinNumber     string                  `protobuf:"bytes,13,opt,name=isin_number,json=isinNumber,proto3" json:"isin_number,omitempty"`
	TxnConstraints *TransactionConstraints `protobuf:"bytes,14,opt,name=txn_constraints,json=txnConstraints,proto3" json:"txn_constraints,omitempty"`
	// Standard timestamp fields
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Scheme code is a unique to each fund and is required when investing. RTA needs scheme code in the forward file.
	SchemeCode string `protobuf:"bytes,18,opt,name=scheme_code,json=schemeCode,proto3" json:"scheme_code,omitempty"`
	// If mutual fund is available/unavailable for investment in our app
	Status MutualFundInternalStatus `protobuf:"varint,19,opt,name=status,proto3,enum=api.investment.mutualfund.MutualFundInternalStatus" json:"status,omitempty"`
	// will have vendor related metadata for a fund
	VendorMetadata *VendorMetadata `protobuf:"bytes,20,opt,name=vendor_metadata,json=vendorMetadata,proto3" json:"vendor_metadata,omitempty"`
	AmfiCode       string          `protobuf:"bytes,21,opt,name=amfi_code,json=amfiCode,proto3" json:"amfi_code,omitempty"`
	// The name of the assigned category of a fund(index fund etc..)
	// Not Modifiable
	CategoryName MutualFundCategoryName `protobuf:"varint,22,opt,name=category_name,json=categoryName,proto3,enum=api.investment.mutualfund.MutualFundCategoryName" json:"category_name,omitempty"`
	// Risk level of a fund that is decided by a fundhouse(High risk/moderate risk/etc..)
	// Modifiable montlhy by AMC
	FundhouseDefinedRiskLevel FundhouseDefinedRiskLevel `protobuf:"varint,24,opt,name=fundhouse_defined_risk_level,json=fundhouseDefinedRiskLevel,proto3,enum=api.investment.mutualfund.FundhouseDefinedRiskLevel" json:"fundhouse_defined_risk_level,omitempty"`
	// Daily change is Nav of a mutual fund
	// updated daily
	DailyNavChange float32 `protobuf:"fixed32,25,opt,name=daily_nav_change,json=dailyNavChange,proto3" json:"daily_nav_change,omitempty"`
	// % avg return by the fund over different intervals
	// Funds can be sorted based on this field
	// updated daily
	Returns *Returns `protobuf:"bytes,26,opt,name=returns,proto3" json:"returns,omitempty"`
	// https://www.investopedia.com/terms/b/benchmark.asp
	// https://www.investopedia.com/articles/mutualfund/04/032404.asp
	// % abs return of the benchmark of a fund
	BenchmarkDetails *BenchmarkDetails `protobuf:"bytes,27,opt,name=benchmark_details,json=benchmarkDetails,proto3" json:"benchmark_details,omitempty"`
	// various matrices like (tracking error/ alpha/ information ratio/capture ratio) to evalute fund performance
	PerformanceMetrics *PerformanceMetrics `protobuf:"bytes,28,opt,name=performance_metrics,json=performanceMetrics,proto3" json:"performance_metrics,omitempty"`
	// fundamental information about a fund like(AUM, MarketCap, FundManagers, expense ratio etc..)
	FundFundamentalDetails *FundFundamentalDetails `protobuf:"bytes,29,opt,name=fund_fundamental_details,json=fundFundamentalDetails,proto3" json:"fund_fundamental_details,omitempty"`
	// internal fields to fi team
	// Modifiable montlhy by Fi content team
	FiScore float32 `protobuf:"fixed32,30,opt,name=fi_score,json=fiScore,proto3" json:"fi_score,omitempty"`
	// internal fields for display display
	// Modifiable montlhy by Fi content team
	FiContent *FiContent `protobuf:"bytes,31,opt,name=fi_content,json=fiContent,proto3" json:"fi_content,omitempty"`
	// array of groups, to which rule should be allowed
	// if null, there is no restriction on user group, i.e the rule is allowed to all groups
	AllowedUserGroups  []common.UserGroup  `protobuf:"varint,32,rep,packed,name=allowed_user_groups,json=allowedUserGroups,proto3,enum=api.typesv2.common.UserGroup" json:"allowed_user_groups,omitempty"`
	VersionSupportInfo *VersionSupportInfo `protobuf:"bytes,33,opt,name=version_support_info,json=versionSupportInfo,proto3" json:"version_support_info,omitempty"`
	// The Name of the category for a fund decided by internal team
	FiDefinedCategory MutualFundCategoryName `protobuf:"varint,34,opt,name=fi_defined_category,json=fiDefinedCategory,proto3,enum=api.investment.mutualfund.MutualFundCategoryName" json:"fi_defined_category,omitempty"`
	// Fund is available for investment or not
	// amc may/may not accept new investment in a fund
	// can be extended if amc/rta blocks withdrawal from fund
	FundInvestmentStatus FundInvestmentStatus `protobuf:"varint,35,opt,name=fund_investment_status,json=fundInvestmentStatus,proto3,enum=api.investment.mutualfund.FundInvestmentStatus" json:"fund_investment_status,omitempty"`
	// HistoricalReturns is used to store data points required to plot a fund's return graph.
	// We store the historical return values in specified granularity
	HistoricalReturns *HistoricalReturns `protobuf:"bytes,36,opt,name=historical_returns,json=historicalReturns,proto3" json:"historical_returns,omitempty"`
	// Min sip amount derived from 'TransactionConstraints'
	// Would be max(Int32) i.e '2147483647' if Sip is not supported
	ComputedMinSipAmount int32 `protobuf:"varint,37,opt,name=computed_min_sip_amount,json=computedMinSipAmount,proto3" json:"computed_min_sip_amount,omitempty"`
	// obsolete_date is the date at which the MF was discontinued for investment
	ObsoleteDate *timestamppb.Timestamp `protobuf:"bytes,38,opt,name=obsolete_date,json=obsoleteDate,proto3" json:"obsolete_date,omitempty"`
	// represent field to ignore during catalog sync
	FieldsExemptFromSync *FieldsExemptFromSync `protobuf:"bytes,39,opt,name=fields_exempt_from_sync,json=fieldsExemptFromSync,proto3" json:"fields_exempt_from_sync,omitempty"`
}

func (x *MutualFund) Reset() {
	*x = MutualFund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutualFund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutualFund) ProtoMessage() {}

func (x *MutualFund) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutualFund.ProtoReflect.Descriptor instead.
func (*MutualFund) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{0}
}

func (x *MutualFund) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MutualFund) GetAmc() Amc {
	if x != nil {
		return x.Amc
	}
	return Amc_AMC_UNSPECIFIED
}

func (x *MutualFund) GetNameData() *FundNameMetadata {
	if x != nil {
		return x.NameData
	}
	return nil
}

func (x *MutualFund) GetPlanType() PlanType {
	if x != nil {
		return x.PlanType
	}
	return PlanType_PLAN_TYPE_UNSPECIFIED
}

func (x *MutualFund) GetInvestmentType() InvestmentType {
	if x != nil {
		return x.InvestmentType
	}
	return InvestmentType_END_TYPE_UNSPECIFIED
}

func (x *MutualFund) GetOptionType() OptionType {
	if x != nil {
		return x.OptionType
	}
	return OptionType_OPTION_TYPE_UNSPECIFIED
}

func (x *MutualFund) GetDivReinvOptionType() DividendReinvestmentOptionType {
	if x != nil {
		return x.DivReinvOptionType
	}
	return DividendReinvestmentOptionType_DIV_REINVESTMENT_OPTION_UNSPECIFIED
}

func (x *MutualFund) GetNav() *money.Money {
	if x != nil {
		return x.Nav
	}
	return nil
}

func (x *MutualFund) GetAssetClass() AssetClass {
	if x != nil {
		return x.AssetClass
	}
	return AssetClass_ASSET_CLASS_UNSPECIFIED
}

func (x *MutualFund) GetSipAllowed() bool {
	if x != nil {
		return x.SipAllowed
	}
	return false
}

func (x *MutualFund) GetSwpAllowed() bool {
	if x != nil {
		return x.SwpAllowed
	}
	return false
}

func (x *MutualFund) GetStpAllowed() bool {
	if x != nil {
		return x.StpAllowed
	}
	return false
}

func (x *MutualFund) GetIsinNumber() string {
	if x != nil {
		return x.IsinNumber
	}
	return ""
}

func (x *MutualFund) GetTxnConstraints() *TransactionConstraints {
	if x != nil {
		return x.TxnConstraints
	}
	return nil
}

func (x *MutualFund) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MutualFund) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MutualFund) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *MutualFund) GetSchemeCode() string {
	if x != nil {
		return x.SchemeCode
	}
	return ""
}

func (x *MutualFund) GetStatus() MutualFundInternalStatus {
	if x != nil {
		return x.Status
	}
	return MutualFundInternalStatus_MUTUAL_FUND_INTERNAL_STATUS_UNSPECIFIED
}

func (x *MutualFund) GetVendorMetadata() *VendorMetadata {
	if x != nil {
		return x.VendorMetadata
	}
	return nil
}

func (x *MutualFund) GetAmfiCode() string {
	if x != nil {
		return x.AmfiCode
	}
	return ""
}

func (x *MutualFund) GetCategoryName() MutualFundCategoryName {
	if x != nil {
		return x.CategoryName
	}
	return MutualFundCategoryName_MutualFundCategoryName_UNSPECIFIED
}

func (x *MutualFund) GetFundhouseDefinedRiskLevel() FundhouseDefinedRiskLevel {
	if x != nil {
		return x.FundhouseDefinedRiskLevel
	}
	return FundhouseDefinedRiskLevel_FundhouseDefinedRiskLevel_UNSPECIFIED
}

func (x *MutualFund) GetDailyNavChange() float32 {
	if x != nil {
		return x.DailyNavChange
	}
	return 0
}

func (x *MutualFund) GetReturns() *Returns {
	if x != nil {
		return x.Returns
	}
	return nil
}

func (x *MutualFund) GetBenchmarkDetails() *BenchmarkDetails {
	if x != nil {
		return x.BenchmarkDetails
	}
	return nil
}

func (x *MutualFund) GetPerformanceMetrics() *PerformanceMetrics {
	if x != nil {
		return x.PerformanceMetrics
	}
	return nil
}

func (x *MutualFund) GetFundFundamentalDetails() *FundFundamentalDetails {
	if x != nil {
		return x.FundFundamentalDetails
	}
	return nil
}

func (x *MutualFund) GetFiScore() float32 {
	if x != nil {
		return x.FiScore
	}
	return 0
}

func (x *MutualFund) GetFiContent() *FiContent {
	if x != nil {
		return x.FiContent
	}
	return nil
}

func (x *MutualFund) GetAllowedUserGroups() []common.UserGroup {
	if x != nil {
		return x.AllowedUserGroups
	}
	return nil
}

func (x *MutualFund) GetVersionSupportInfo() *VersionSupportInfo {
	if x != nil {
		return x.VersionSupportInfo
	}
	return nil
}

func (x *MutualFund) GetFiDefinedCategory() MutualFundCategoryName {
	if x != nil {
		return x.FiDefinedCategory
	}
	return MutualFundCategoryName_MutualFundCategoryName_UNSPECIFIED
}

func (x *MutualFund) GetFundInvestmentStatus() FundInvestmentStatus {
	if x != nil {
		return x.FundInvestmentStatus
	}
	return FundInvestmentStatus_FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED
}

func (x *MutualFund) GetHistoricalReturns() *HistoricalReturns {
	if x != nil {
		return x.HistoricalReturns
	}
	return nil
}

func (x *MutualFund) GetComputedMinSipAmount() int32 {
	if x != nil {
		return x.ComputedMinSipAmount
	}
	return 0
}

func (x *MutualFund) GetObsoleteDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ObsoleteDate
	}
	return nil
}

func (x *MutualFund) GetFieldsExemptFromSync() *FieldsExemptFromSync {
	if x != nil {
		return x.FieldsExemptFromSync
	}
	return nil
}

type FieldsExemptFromSync struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FieldMasks []MutualFundFieldMask `protobuf:"varint,1,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=api.investment.mutualfund.MutualFundFieldMask" json:"field_masks,omitempty"`
}

func (x *FieldsExemptFromSync) Reset() {
	*x = FieldsExemptFromSync{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldsExemptFromSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldsExemptFromSync) ProtoMessage() {}

func (x *FieldsExemptFromSync) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldsExemptFromSync.ProtoReflect.Descriptor instead.
func (*FieldsExemptFromSync) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{1}
}

func (x *FieldsExemptFromSync) GetFieldMasks() []MutualFundFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

// Historical Data required to plot a graph
type HistoricalReturns struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// monthly_returns is list of returns a fund has given on monthly basis
	MonthlyReturns []*ReturnData `protobuf:"bytes,1,rep,name=monthly_returns,json=monthlyReturns,proto3" json:"monthly_returns,omitempty"`
}

func (x *HistoricalReturns) Reset() {
	*x = HistoricalReturns{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HistoricalReturns) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoricalReturns) ProtoMessage() {}

func (x *HistoricalReturns) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoricalReturns.ProtoReflect.Descriptor instead.
func (*HistoricalReturns) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{2}
}

func (x *HistoricalReturns) GetMonthlyReturns() []*ReturnData {
	if x != nil {
		return x.MonthlyReturns
	}
	return nil
}

type ReturnData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date at which return was recorded
	ReturnDate *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=return_date,json=returnDate,proto3" json:"return_date,omitempty"`
	// return data at return_date
	ReturnData float64 `protobuf:"fixed64,2,opt,name=return_data,json=returnData,proto3" json:"return_data,omitempty"`
}

func (x *ReturnData) Reset() {
	*x = ReturnData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReturnData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnData) ProtoMessage() {}

func (x *ReturnData) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnData.ProtoReflect.Descriptor instead.
func (*ReturnData) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{3}
}

func (x *ReturnData) GetReturnDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ReturnDate
	}
	return nil
}

func (x *ReturnData) GetReturnData() float64 {
	if x != nil {
		return x.ReturnData
	}
	return 0
}

// An Asset Management Company (AMC) is a firm that invests the funds pooled from individual investors in securities
// with the objective of optimal return for investors in exchange for a fee. AMC maintains the diversity of portfolio
// by investing in both high-risk and low-risk securities such as stock, debt, real-estate, shares and bonds, pension-funds
// etc. Examples are HDFC Mutual Fund, ICICI Prudential Mutual Fund
type AmcInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary identifier to the database model
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// uniquely identifies an AMC, internal name
	Amc Amc `protobuf:"varint,2,opt,name=amc,proto3,enum=api.investment.mutualfund.Amc" json:"amc,omitempty"`
	// RTA code for AMC is determined from a dynamic map now.
	// AMC create the code and name. RTA shares it programmatically.
	// AMC does not change RTA, its a huge task to do it, so yes you can assume amc_code will not change.
	// AMC Name can change if merger or something happens but that is also rare event.
	//
	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	AmcCode string `protobuf:"bytes,3,opt,name=amc_code,json=amcCode,proto3" json:"amc_code,omitempty"`
	AmcName string `protobuf:"bytes,4,opt,name=amc_name,json=amcName,proto3" json:"amc_name,omitempty"`
	// bank account details where the payment needs to be credited.
	CreditAccount *BankAccountDetails `protobuf:"bytes,5,opt,name=credit_account,json=creditAccount,proto3" json:"credit_account,omitempty"`
	// RTA expands to Registrar and Transfer agents. RTAs help mutual fund companies with record maintenance.
	// For all mutual fund orders, we directly interact with an RTA.
	//
	// Each AMC is served by a single RTA.
	Rta vendorgateway.Vendor `protobuf:"varint,6,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	// Standard timestamp fields
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// payment instrument identifier for the credit bank account. This is a static value.
	CreditAccountPiId string `protobuf:"bytes,10,opt,name=credit_account_pi_id,json=creditAccountPiId,proto3" json:"credit_account_pi_id,omitempty"`
	// external actor id created for this AMC. This is a static value.
	AmcActorId string `protobuf:"bytes,11,opt,name=amc_actor_id,json=amcActorId,proto3" json:"amc_actor_id,omitempty"`
}

func (x *AmcInfo) Reset() {
	*x = AmcInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmcInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmcInfo) ProtoMessage() {}

func (x *AmcInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmcInfo.ProtoReflect.Descriptor instead.
func (*AmcInfo) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{4}
}

func (x *AmcInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AmcInfo) GetAmc() Amc {
	if x != nil {
		return x.Amc
	}
	return Amc_AMC_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *AmcInfo) GetAmcCode() string {
	if x != nil {
		return x.AmcCode
	}
	return ""
}

func (x *AmcInfo) GetAmcName() string {
	if x != nil {
		return x.AmcName
	}
	return ""
}

func (x *AmcInfo) GetCreditAccount() *BankAccountDetails {
	if x != nil {
		return x.CreditAccount
	}
	return nil
}

func (x *AmcInfo) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *AmcInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AmcInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AmcInfo) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *AmcInfo) GetCreditAccountPiId() string {
	if x != nil {
		return x.CreditAccountPiId
	}
	return ""
}

func (x *AmcInfo) GetAmcActorId() string {
	if x != nil {
		return x.AmcActorId
	}
	return ""
}

// BankAccountDetails is used to hold the necessary data useful to identify bank account of various
// participants(for eg: AMC, user, RTA etc) in the investment.
type BankAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountName         string          `protobuf:"bytes,1,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`
	AccountNumber       string          `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	MaskedAccountNumber string          `protobuf:"bytes,3,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	AccountType         BankAccountType `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=api.investment.mutualfund.BankAccountType" json:"account_type,omitempty"`
	Ifsc                string          `protobuf:"bytes,5,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	BankName            string          `protobuf:"bytes,6,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
}

func (x *BankAccountDetails) Reset() {
	*x = BankAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountDetails) ProtoMessage() {}

func (x *BankAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountDetails.ProtoReflect.Descriptor instead.
func (*BankAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{5}
}

func (x *BankAccountDetails) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *BankAccountDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BankAccountDetails) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *BankAccountDetails) GetAccountType() BankAccountType {
	if x != nil {
		return x.AccountType
	}
	return BankAccountType_BANK_ACCOUNT_TYPE_UNSPECIFIED
}

func (x *BankAccountDetails) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *BankAccountDetails) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

// metadata associated with mutual fund naming.
type FundNameMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// For example: "Equity&Debt Fund - Growth"
	ShortName string `protobuf:"bytes,1,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// For example: "ICICI Prudential Equity & Debt Fund - Growth"
	LongName string `protobuf:"bytes,2,opt,name=long_name,json=longName,proto3" json:"long_name,omitempty"`
	// can be same as one of the above or something entirely different.
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *FundNameMetadata) Reset() {
	*x = FundNameMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundNameMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundNameMetadata) ProtoMessage() {}

func (x *FundNameMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundNameMetadata.ProtoReflect.Descriptor instead.
func (*FundNameMetadata) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{6}
}

func (x *FundNameMetadata) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *FundNameMetadata) GetLongName() string {
	if x != nil {
		return x.LongName
	}
	return ""
}

func (x *FundNameMetadata) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

// constraints data for various transactions permitted on the scheme.
// For example: min amount, min units during normal purchase. Similar for Rdemption, SIP, STP, SWP transaction type.
type TransactionConstraints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// LUMPSUM: new purchase
	// New purchase minimum value
	NewpMnval *money.Money `protobuf:"bytes,1,opt,name=newp_mnval,json=newpMnval,proto3" json:"newp_mnval,omitempty"`
	// New purchase maximum value
	NewpMxval *money.Money `protobuf:"bytes,2,opt,name=newp_mxval,json=newpMxval,proto3" json:"newp_mxval,omitempty"`
	// Additional purchase minimum amount
	AdpMnAmt *money.Money `protobuf:"bytes,3,opt,name=adp_mn_amt,json=adpMnAmt,proto3" json:"adp_mn_amt,omitempty"`
	// Additional purchase maximum amount
	AdpMxAmt *money.Money `protobuf:"bytes,4,opt,name=adp_mx_amt,json=adpMxAmt,proto3" json:"adp_mx_amt,omitempty"`
	// Additional purchase minimum units
	AdpMnUnt int32 `protobuf:"varint,5,opt,name=adp_mn_unt,json=adpMnUnt,proto3" json:"adp_mn_unt,omitempty"`
	// Additional purchase maximum units
	AdpMxUnt int32 `protobuf:"varint,6,opt,name=adp_mx_unt,json=adpMxUnt,proto3" json:"adp_mx_unt,omitempty"`
	// Initial Purchase minimum increment amount
	PMnIncr *money.Money `protobuf:"bytes,7,opt,name=p_mn_incr,json=pMnIncr,proto3" json:"p_mn_incr,omitempty"`
	// Additional Purchase minimum increment amount
	AdpMnIncr *money.Money `protobuf:"bytes,18,opt,name=adp_mn_incr,json=adpMnIncr,proto3" json:"adp_mn_incr,omitempty"`
	// REDEMPTION
	// Redemption minimum amount
	RedMnAmt *money.Money `protobuf:"bytes,8,opt,name=red_mn_amt,json=redMnAmt,proto3" json:"red_mn_amt,omitempty"`
	// Redemption maximum amount
	RedMxAmt *money.Money `protobuf:"bytes,9,opt,name=red_mx_amt,json=redMxAmt,proto3" json:"red_mx_amt,omitempty"`
	// Redemption minimum units
	RedMnUnt int32 `protobuf:"varint,10,opt,name=red_mn_unt,json=redMnUnt,proto3" json:"red_mn_unt,omitempty"`
	// Redemption maximum units
	RedMxUnt int32 `protobuf:"varint,11,opt,name=red_mx_unt,json=redMxUnt,proto3" json:"red_mx_unt,omitempty"`
	// Redemption incremental
	RedIncr *money.Money `protobuf:"bytes,12,opt,name=red_incr,json=redIncr,proto3" json:"red_incr,omitempty"`
	// Modifiable montlhy by AMC
	SipMetadata *SipMetadata `protobuf:"bytes,13,opt,name=sip_metadata,json=sipMetadata,proto3" json:"sip_metadata,omitempty"`
	// Modifiable montlhy by AMC
	StpMetadata *StpMetadata `protobuf:"bytes,14,opt,name=stp_metadata,json=stpMetadata,proto3" json:"stp_metadata,omitempty"`
	// Modifiable montlhy by AMC
	SwpMetadata *SwpMetadata `protobuf:"bytes,15,opt,name=swp_metadata,json=swpMetadata,proto3" json:"swp_metadata,omitempty"`
	// This shows the breakdown of the maturities in a fixed-income portfolio. It reveals the percentage of fixed-income securities that fall within each maturity range.
	// Modifiable montlhy by AMC
	MaturityBreakdown *MaturityBreakdown `protobuf:"bytes,16,opt,name=maturity_breakdown,json=maturityBreakdown,proto3" json:"maturity_breakdown,omitempty"`
	// The lock-in period in mutual funds means the investor cannot redeem the units before completing a predetermined period from the date of investment.
	// https://cleartax.in/s/elss-lock-in-period
	// Not Modifiable
	//
	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	LockinPeriod     float32           `protobuf:"fixed32,17,opt,name=lockin_period,json=lockinPeriod,proto3" json:"lockin_period,omitempty"`
	FundLockinPeriod *FundLockInPeriod `protobuf:"bytes,19,opt,name=fund_lockin_period,json=fundLockinPeriod,proto3" json:"fund_lockin_period,omitempty"`
	// frequencies when sip is allowed in our system
	// note: fund may support multiple SIP frequencies but we will only support sip frequencies shared by product team for a fund
	AllowedSipFrequencies []AipFrequency `protobuf:"varint,20,rep,packed,name=allowed_sip_frequencies,json=allowedSipFrequencies,proto3,enum=api.investment.mutualfund.AipFrequency" json:"allowed_sip_frequencies,omitempty"`
}

func (x *TransactionConstraints) Reset() {
	*x = TransactionConstraints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionConstraints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionConstraints) ProtoMessage() {}

func (x *TransactionConstraints) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionConstraints.ProtoReflect.Descriptor instead.
func (*TransactionConstraints) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{7}
}

func (x *TransactionConstraints) GetNewpMnval() *money.Money {
	if x != nil {
		return x.NewpMnval
	}
	return nil
}

func (x *TransactionConstraints) GetNewpMxval() *money.Money {
	if x != nil {
		return x.NewpMxval
	}
	return nil
}

func (x *TransactionConstraints) GetAdpMnAmt() *money.Money {
	if x != nil {
		return x.AdpMnAmt
	}
	return nil
}

func (x *TransactionConstraints) GetAdpMxAmt() *money.Money {
	if x != nil {
		return x.AdpMxAmt
	}
	return nil
}

func (x *TransactionConstraints) GetAdpMnUnt() int32 {
	if x != nil {
		return x.AdpMnUnt
	}
	return 0
}

func (x *TransactionConstraints) GetAdpMxUnt() int32 {
	if x != nil {
		return x.AdpMxUnt
	}
	return 0
}

func (x *TransactionConstraints) GetPMnIncr() *money.Money {
	if x != nil {
		return x.PMnIncr
	}
	return nil
}

func (x *TransactionConstraints) GetAdpMnIncr() *money.Money {
	if x != nil {
		return x.AdpMnIncr
	}
	return nil
}

func (x *TransactionConstraints) GetRedMnAmt() *money.Money {
	if x != nil {
		return x.RedMnAmt
	}
	return nil
}

func (x *TransactionConstraints) GetRedMxAmt() *money.Money {
	if x != nil {
		return x.RedMxAmt
	}
	return nil
}

func (x *TransactionConstraints) GetRedMnUnt() int32 {
	if x != nil {
		return x.RedMnUnt
	}
	return 0
}

func (x *TransactionConstraints) GetRedMxUnt() int32 {
	if x != nil {
		return x.RedMxUnt
	}
	return 0
}

func (x *TransactionConstraints) GetRedIncr() *money.Money {
	if x != nil {
		return x.RedIncr
	}
	return nil
}

func (x *TransactionConstraints) GetSipMetadata() *SipMetadata {
	if x != nil {
		return x.SipMetadata
	}
	return nil
}

func (x *TransactionConstraints) GetStpMetadata() *StpMetadata {
	if x != nil {
		return x.StpMetadata
	}
	return nil
}

func (x *TransactionConstraints) GetSwpMetadata() *SwpMetadata {
	if x != nil {
		return x.SwpMetadata
	}
	return nil
}

func (x *TransactionConstraints) GetMaturityBreakdown() *MaturityBreakdown {
	if x != nil {
		return x.MaturityBreakdown
	}
	return nil
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *TransactionConstraints) GetLockinPeriod() float32 {
	if x != nil {
		return x.LockinPeriod
	}
	return 0
}

func (x *TransactionConstraints) GetFundLockinPeriod() *FundLockInPeriod {
	if x != nil {
		return x.FundLockinPeriod
	}
	return nil
}

func (x *TransactionConstraints) GetAllowedSipFrequencies() []AipFrequency {
	if x != nil {
		return x.AllowedSipFrequencies
	}
	return nil
}

// will have vendor related metadata for a fund
type VendorMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MorningstarData *MorningstarData `protobuf:"bytes,1,opt,name=morningstar_data,json=morningstarData,proto3" json:"morningstar_data,omitempty"`
}

func (x *VendorMetadata) Reset() {
	*x = VendorMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorMetadata) ProtoMessage() {}

func (x *VendorMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorMetadata.ProtoReflect.Descriptor instead.
func (*VendorMetadata) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{8}
}

func (x *VendorMetadata) GetMorningstarData() *MorningstarData {
	if x != nil {
		return x.MorningstarData
	}
	return nil
}

type MorningstarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This is the Morningstar id for a share class of an investment. All share classes of the same investment are linked together using the morningstar_fund_id.
	MstarId string `protobuf:"bytes,1,opt,name=mstar_id,json=mstarId,proto3" json:"mstar_id,omitempty"`
	// This is the Morningstar id for the investment, which is sometimes called the distinct portfolio level. Each shareclass will have the same morningstar_fund_id.
	MorningstarFundId string `protobuf:"bytes,2,opt,name=morningstar_fund_id,json=morningstarFundId,proto3" json:"morningstar_fund_id,omitempty"`
	// It’s the Morningstar identifier for a performance stream of a share class. It presents performances of a class in
	// various currencies and exchanges (for example ETFs). All performances of the same class are linked together using
	// the Fund Share Class Id (SecId). The one which is in the base currency (originally stated by Fund Company) of a share
	// classes called primary. Some share classes may have only one performance stream and some may have multiple
	// performance streams.
	// It’s the Morningstar identifier for a performance stream of a share class. It presents performances of a class in
	// various currencies and exchanges (for example ETFs). All performances of the same class are linked together using
	// the Fund Share Class Id (SecId). The one which is in the base currency (originally stated by Fund Company) of a
	// share classes called primary. Some share classes may have only one performance stream and some may have multiple performance streams.
	MorningstarPerformanceId string `protobuf:"bytes,3,opt,name=morningstar_performance_id,json=morningstarPerformanceId,proto3" json:"morningstar_performance_id,omitempty"`
}

func (x *MorningstarData) Reset() {
	*x = MorningstarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MorningstarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MorningstarData) ProtoMessage() {}

func (x *MorningstarData) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MorningstarData.ProtoReflect.Descriptor instead.
func (*MorningstarData) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{9}
}

func (x *MorningstarData) GetMstarId() string {
	if x != nil {
		return x.MstarId
	}
	return ""
}

func (x *MorningstarData) GetMorningstarFundId() string {
	if x != nil {
		return x.MorningstarFundId
	}
	return ""
}

func (x *MorningstarData) GetMorningstarPerformanceId() string {
	if x != nil {
		return x.MorningstarPerformanceId
	}
	return ""
}

type Returns struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvgFundReturnOneYear       float32 `protobuf:"fixed32,1,opt,name=avg_fund_return_one_year,json=avgFundReturnOneYear,proto3" json:"avg_fund_return_one_year,omitempty"`
	AvgFundReturnThreeYear     float32 `protobuf:"fixed32,2,opt,name=avg_fund_return_three_year,json=avgFundReturnThreeYear,proto3" json:"avg_fund_return_three_year,omitempty"`
	AvgFundReturnFiveYear      float32 `protobuf:"fixed32,3,opt,name=avg_fund_return_five_year,json=avgFundReturnFiveYear,proto3" json:"avg_fund_return_five_year,omitempty"`
	AvgReturnMaxPeriod         float32 `protobuf:"fixed32,4,opt,name=avg_return_max_period,json=avgReturnMaxPeriod,proto3" json:"avg_return_max_period,omitempty"`
	AvgCategoryReturnOneYear   float32 `protobuf:"fixed32,5,opt,name=avg_category_return_one_year,json=avgCategoryReturnOneYear,proto3" json:"avg_category_return_one_year,omitempty"`
	AvgCategoryReturnThreeYear float32 `protobuf:"fixed32,6,opt,name=avg_category_return_three_year,json=avgCategoryReturnThreeYear,proto3" json:"avg_category_return_three_year,omitempty"`
	AvgCategoryReturnFiveYear  float32 `protobuf:"fixed32,7,opt,name=avg_category_return_five_year,json=avgCategoryReturnFiveYear,proto3" json:"avg_category_return_five_year,omitempty"`
	AvgFundReturnOneMonth      float32 `protobuf:"fixed32,8,opt,name=avg_fund_return_one_month,json=avgFundReturnOneMonth,proto3" json:"avg_fund_return_one_month,omitempty"`
	AvgFundReturnSixMonth      float32 `protobuf:"fixed32,9,opt,name=avg_fund_return_six_month,json=avgFundReturnSixMonth,proto3" json:"avg_fund_return_six_month,omitempty"`
	// it indicates the returns you will get if you hold the fund till the underlying bonds mature
	YieldToMaturity float32 `protobuf:"fixed32,10,opt,name=yield_to_maturity,json=yieldToMaturity,proto3" json:"yield_to_maturity,omitempty"`
}

func (x *Returns) Reset() {
	*x = Returns{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Returns) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Returns) ProtoMessage() {}

func (x *Returns) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Returns.ProtoReflect.Descriptor instead.
func (*Returns) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{10}
}

func (x *Returns) GetAvgFundReturnOneYear() float32 {
	if x != nil {
		return x.AvgFundReturnOneYear
	}
	return 0
}

func (x *Returns) GetAvgFundReturnThreeYear() float32 {
	if x != nil {
		return x.AvgFundReturnThreeYear
	}
	return 0
}

func (x *Returns) GetAvgFundReturnFiveYear() float32 {
	if x != nil {
		return x.AvgFundReturnFiveYear
	}
	return 0
}

func (x *Returns) GetAvgReturnMaxPeriod() float32 {
	if x != nil {
		return x.AvgReturnMaxPeriod
	}
	return 0
}

func (x *Returns) GetAvgCategoryReturnOneYear() float32 {
	if x != nil {
		return x.AvgCategoryReturnOneYear
	}
	return 0
}

func (x *Returns) GetAvgCategoryReturnThreeYear() float32 {
	if x != nil {
		return x.AvgCategoryReturnThreeYear
	}
	return 0
}

func (x *Returns) GetAvgCategoryReturnFiveYear() float32 {
	if x != nil {
		return x.AvgCategoryReturnFiveYear
	}
	return 0
}

func (x *Returns) GetAvgFundReturnOneMonth() float32 {
	if x != nil {
		return x.AvgFundReturnOneMonth
	}
	return 0
}

func (x *Returns) GetAvgFundReturnSixMonth() float32 {
	if x != nil {
		return x.AvgFundReturnSixMonth
	}
	return 0
}

func (x *Returns) GetYieldToMaturity() float32 {
	if x != nil {
		return x.YieldToMaturity
	}
	return 0
}

type TrackingError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingErrorOneYear         float32 `protobuf:"fixed32,1,opt,name=tracking_error_one_year,json=trackingErrorOneYear,proto3" json:"tracking_error_one_year,omitempty"`
	TrackingErrorThreeYear       float32 `protobuf:"fixed32,2,opt,name=tracking_error_three_year,json=trackingErrorThreeYear,proto3" json:"tracking_error_three_year,omitempty"`
	TrackingErrorFiveYear        float32 `protobuf:"fixed32,3,opt,name=tracking_error_five_year,json=trackingErrorFiveYear,proto3" json:"tracking_error_five_year,omitempty"`
	TrackingErrorMaxPeriod       float32 `protobuf:"fixed32,4,opt,name=tracking_error_max_period,json=trackingErrorMaxPeriod,proto3" json:"tracking_error_max_period,omitempty"`
	CatAvgTrackingErrorOneYear   float32 `protobuf:"fixed32,5,opt,name=cat_avg_tracking_error_one_year,json=catAvgTrackingErrorOneYear,proto3" json:"cat_avg_tracking_error_one_year,omitempty"`
	CatAvgTrackingErrorThreeYear float32 `protobuf:"fixed32,6,opt,name=cat_avg_tracking_error_three_year,json=catAvgTrackingErrorThreeYear,proto3" json:"cat_avg_tracking_error_three_year,omitempty"`
	CatAvgTrackingErrorFiveYear  float32 `protobuf:"fixed32,7,opt,name=cat_avg_tracking_error_five_year,json=catAvgTrackingErrorFiveYear,proto3" json:"cat_avg_tracking_error_five_year,omitempty"`
}

func (x *TrackingError) Reset() {
	*x = TrackingError{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackingError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingError) ProtoMessage() {}

func (x *TrackingError) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingError.ProtoReflect.Descriptor instead.
func (*TrackingError) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{11}
}

func (x *TrackingError) GetTrackingErrorOneYear() float32 {
	if x != nil {
		return x.TrackingErrorOneYear
	}
	return 0
}

func (x *TrackingError) GetTrackingErrorThreeYear() float32 {
	if x != nil {
		return x.TrackingErrorThreeYear
	}
	return 0
}

func (x *TrackingError) GetTrackingErrorFiveYear() float32 {
	if x != nil {
		return x.TrackingErrorFiveYear
	}
	return 0
}

func (x *TrackingError) GetTrackingErrorMaxPeriod() float32 {
	if x != nil {
		return x.TrackingErrorMaxPeriod
	}
	return 0
}

func (x *TrackingError) GetCatAvgTrackingErrorOneYear() float32 {
	if x != nil {
		return x.CatAvgTrackingErrorOneYear
	}
	return 0
}

func (x *TrackingError) GetCatAvgTrackingErrorThreeYear() float32 {
	if x != nil {
		return x.CatAvgTrackingErrorThreeYear
	}
	return 0
}

func (x *TrackingError) GetCatAvgTrackingErrorFiveYear() float32 {
	if x != nil {
		return x.CatAvgTrackingErrorFiveYear
	}
	return 0
}

// various matrices like (tracking error/ alpha/ information ratio/capture ratio) to evalute fund performance
type PerformanceMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tracking error is the difference in actual performance between a position (usually an entire portfolio) and its corresponding benchmark.
	// https://www.investopedia.com/terms/t/trackingerror.asp
	// Modifiable montlhy by AMC
	TrackingError *TrackingError `protobuf:"bytes,2,opt,name=tracking_error,json=trackingError,proto3" json:"tracking_error,omitempty"`
	// Alpha is a measure of an investment's performance on a risk-adjusted basis.
	// Simply stated, alpha is often considered to represent the value that a portfolio manager adds or subtracts from a fund portfolio's return.
	// An alpha of 1.0 means the fund has outperformed its benchmark index by 1%.
	// Modifiable montlhy by AMC
	Alpha *Alpha `protobuf:"bytes,3,opt,name=alpha,proto3" json:"alpha,omitempty"`
	// Information ratio shows the consistency of the fund manager in generating superior risk adjusted performance.
	// A higher information ratio shows that fund manager has outshined other fund managers and has delivered consistent returns over a specified period.
	// Modifiable montlhy by AMC
	InformationRatio *InformationRatio `protobuf:"bytes,4,opt,name=information_ratio,json=informationRatio,proto3" json:"information_ratio,omitempty"`
	// https://www.investopedia.com/terms/s/sharperatio.asp
	SharpeRatio *SharpeRatio `protobuf:"bytes,6,opt,name=sharpe_ratio,json=sharpeRatio,proto3" json:"sharpe_ratio,omitempty"`
	// ref: https://www.investopedia.com/terms/b/batting-average.asp
	BattingAverage *BattingAverage `protobuf:"bytes,7,opt,name=batting_average,json=battingAverage,proto3" json:"batting_average,omitempty"`
	// ref: https://groww.in/p/capture-ratio
	UpsideCaptureRatio *UpsideCaptureRatio `protobuf:"bytes,8,opt,name=upside_capture_ratio,json=upsideCaptureRatio,proto3" json:"upside_capture_ratio,omitempty"`
	// ref: https://groww.in/p/capture-ratio
	DownsideCaptureRatio *DownsideCaptureRatio `protobuf:"bytes,9,opt,name=downside_capture_ratio,json=downsideCaptureRatio,proto3" json:"downside_capture_ratio,omitempty"`
}

func (x *PerformanceMetrics) Reset() {
	*x = PerformanceMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerformanceMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceMetrics) ProtoMessage() {}

func (x *PerformanceMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceMetrics.ProtoReflect.Descriptor instead.
func (*PerformanceMetrics) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{12}
}

func (x *PerformanceMetrics) GetTrackingError() *TrackingError {
	if x != nil {
		return x.TrackingError
	}
	return nil
}

func (x *PerformanceMetrics) GetAlpha() *Alpha {
	if x != nil {
		return x.Alpha
	}
	return nil
}

func (x *PerformanceMetrics) GetInformationRatio() *InformationRatio {
	if x != nil {
		return x.InformationRatio
	}
	return nil
}

func (x *PerformanceMetrics) GetSharpeRatio() *SharpeRatio {
	if x != nil {
		return x.SharpeRatio
	}
	return nil
}

func (x *PerformanceMetrics) GetBattingAverage() *BattingAverage {
	if x != nil {
		return x.BattingAverage
	}
	return nil
}

func (x *PerformanceMetrics) GetUpsideCaptureRatio() *UpsideCaptureRatio {
	if x != nil {
		return x.UpsideCaptureRatio
	}
	return nil
}

func (x *PerformanceMetrics) GetDownsideCaptureRatio() *DownsideCaptureRatio {
	if x != nil {
		return x.DownsideCaptureRatio
	}
	return nil
}

// https://www.investopedia.com/terms/s/sharperatio.asp
type SharpeRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundOneYear             float32 `protobuf:"fixed32,1,opt,name=fund_one_year,json=fundOneYear,proto3" json:"fund_one_year,omitempty"`
	FundThreeYear           float32 `protobuf:"fixed32,2,opt,name=fund_three_year,json=fundThreeYear,proto3" json:"fund_three_year,omitempty"`
	FundFiveYear            float32 `protobuf:"fixed32,3,opt,name=fund_five_year,json=fundFiveYear,proto3" json:"fund_five_year,omitempty"`
	CategoryAvgOneYear      float32 `protobuf:"fixed32,4,opt,name=category_avg_one_year,json=categoryAvgOneYear,proto3" json:"category_avg_one_year,omitempty"`
	CategoryAvgThreeYear    float32 `protobuf:"fixed32,5,opt,name=category_avg_three_year,json=categoryAvgThreeYear,proto3" json:"category_avg_three_year,omitempty"`
	CategoryAvgFiveYear     float32 `protobuf:"fixed32,6,opt,name=category_avg_five_year,json=categoryAvgFiveYear,proto3" json:"category_avg_five_year,omitempty"`
	PercentileRankOneYear   float32 `protobuf:"fixed32,8,opt,name=percentile_rank_one_year,json=percentileRankOneYear,proto3" json:"percentile_rank_one_year,omitempty"`
	PercentileRankThreeYear float32 `protobuf:"fixed32,9,opt,name=percentile_rank_three_year,json=percentileRankThreeYear,proto3" json:"percentile_rank_three_year,omitempty"`
	PercentileRankFiveYear  float32 `protobuf:"fixed32,10,opt,name=percentile_rank_five_year,json=percentileRankFiveYear,proto3" json:"percentile_rank_five_year,omitempty"`
}

func (x *SharpeRatio) Reset() {
	*x = SharpeRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharpeRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharpeRatio) ProtoMessage() {}

func (x *SharpeRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharpeRatio.ProtoReflect.Descriptor instead.
func (*SharpeRatio) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{13}
}

func (x *SharpeRatio) GetFundOneYear() float32 {
	if x != nil {
		return x.FundOneYear
	}
	return 0
}

func (x *SharpeRatio) GetFundThreeYear() float32 {
	if x != nil {
		return x.FundThreeYear
	}
	return 0
}

func (x *SharpeRatio) GetFundFiveYear() float32 {
	if x != nil {
		return x.FundFiveYear
	}
	return 0
}

func (x *SharpeRatio) GetCategoryAvgOneYear() float32 {
	if x != nil {
		return x.CategoryAvgOneYear
	}
	return 0
}

func (x *SharpeRatio) GetCategoryAvgThreeYear() float32 {
	if x != nil {
		return x.CategoryAvgThreeYear
	}
	return 0
}

func (x *SharpeRatio) GetCategoryAvgFiveYear() float32 {
	if x != nil {
		return x.CategoryAvgFiveYear
	}
	return 0
}

func (x *SharpeRatio) GetPercentileRankOneYear() float32 {
	if x != nil {
		return x.PercentileRankOneYear
	}
	return 0
}

func (x *SharpeRatio) GetPercentileRankThreeYear() float32 {
	if x != nil {
		return x.PercentileRankThreeYear
	}
	return 0
}

func (x *SharpeRatio) GetPercentileRankFiveYear() float32 {
	if x != nil {
		return x.PercentileRankFiveYear
	}
	return 0
}

// ref: https://www.investopedia.com/terms/b/batting-average.asp
type BattingAverage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryAvgThreeYear float32 `protobuf:"fixed32,1,opt,name=category_avg_three_year,json=categoryAvgThreeYear,proto3" json:"category_avg_three_year,omitempty"`
}

func (x *BattingAverage) Reset() {
	*x = BattingAverage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattingAverage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattingAverage) ProtoMessage() {}

func (x *BattingAverage) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattingAverage.ProtoReflect.Descriptor instead.
func (*BattingAverage) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{14}
}

func (x *BattingAverage) GetCategoryAvgThreeYear() float32 {
	if x != nil {
		return x.CategoryAvgThreeYear
	}
	return 0
}

// ref: https://groww.in/p/capture-ratio
type UpsideCaptureRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryAvgThreeYear float32 `protobuf:"fixed32,1,opt,name=category_avg_three_year,json=categoryAvgThreeYear,proto3" json:"category_avg_three_year,omitempty"`
}

func (x *UpsideCaptureRatio) Reset() {
	*x = UpsideCaptureRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsideCaptureRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsideCaptureRatio) ProtoMessage() {}

func (x *UpsideCaptureRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsideCaptureRatio.ProtoReflect.Descriptor instead.
func (*UpsideCaptureRatio) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{15}
}

func (x *UpsideCaptureRatio) GetCategoryAvgThreeYear() float32 {
	if x != nil {
		return x.CategoryAvgThreeYear
	}
	return 0
}

// ref: https://groww.in/p/capture-ratio
type DownsideCaptureRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryAvgThreeYear float32 `protobuf:"fixed32,1,opt,name=category_avg_three_year,json=categoryAvgThreeYear,proto3" json:"category_avg_three_year,omitempty"`
}

func (x *DownsideCaptureRatio) Reset() {
	*x = DownsideCaptureRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownsideCaptureRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownsideCaptureRatio) ProtoMessage() {}

func (x *DownsideCaptureRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownsideCaptureRatio.ProtoReflect.Descriptor instead.
func (*DownsideCaptureRatio) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{16}
}

func (x *DownsideCaptureRatio) GetCategoryAvgThreeYear() float32 {
	if x != nil {
		return x.CategoryAvgThreeYear
	}
	return 0
}

// Information ratio shows the consistency of the fund manager in generating superior risk adjusted performance.
// A higher information ratio shows that fund manager has outshined other fund managers and has delivered consistent returns over a specified period.
// Modifiable montlhy by AMC
type InformationRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundOneYear             float32 `protobuf:"fixed32,1,opt,name=fund_one_year,json=fundOneYear,proto3" json:"fund_one_year,omitempty"`
	FundThreeYear           float32 `protobuf:"fixed32,2,opt,name=fund_three_year,json=fundThreeYear,proto3" json:"fund_three_year,omitempty"`
	FundFiveYear            float32 `protobuf:"fixed32,3,opt,name=fund_five_year,json=fundFiveYear,proto3" json:"fund_five_year,omitempty"`
	CategoryAvgOneYear      float32 `protobuf:"fixed32,4,opt,name=category_avg_one_year,json=categoryAvgOneYear,proto3" json:"category_avg_one_year,omitempty"`
	CategoryAvgThreeYear    float32 `protobuf:"fixed32,5,opt,name=category_avg_three_year,json=categoryAvgThreeYear,proto3" json:"category_avg_three_year,omitempty"`
	CategoryAvgFiveYear     float32 `protobuf:"fixed32,6,opt,name=category_avg_five_year,json=categoryAvgFiveYear,proto3" json:"category_avg_five_year,omitempty"`
	PercentileRankOneYear   float32 `protobuf:"fixed32,8,opt,name=percentile_rank_one_year,json=percentileRankOneYear,proto3" json:"percentile_rank_one_year,omitempty"`
	PercentileRankThreeYear float32 `protobuf:"fixed32,9,opt,name=percentile_rank_three_year,json=percentileRankThreeYear,proto3" json:"percentile_rank_three_year,omitempty"`
	PercentileRankFiveYear  float32 `protobuf:"fixed32,10,opt,name=percentile_rank_five_year,json=percentileRankFiveYear,proto3" json:"percentile_rank_five_year,omitempty"`
}

func (x *InformationRatio) Reset() {
	*x = InformationRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InformationRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InformationRatio) ProtoMessage() {}

func (x *InformationRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InformationRatio.ProtoReflect.Descriptor instead.
func (*InformationRatio) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{17}
}

func (x *InformationRatio) GetFundOneYear() float32 {
	if x != nil {
		return x.FundOneYear
	}
	return 0
}

func (x *InformationRatio) GetFundThreeYear() float32 {
	if x != nil {
		return x.FundThreeYear
	}
	return 0
}

func (x *InformationRatio) GetFundFiveYear() float32 {
	if x != nil {
		return x.FundFiveYear
	}
	return 0
}

func (x *InformationRatio) GetCategoryAvgOneYear() float32 {
	if x != nil {
		return x.CategoryAvgOneYear
	}
	return 0
}

func (x *InformationRatio) GetCategoryAvgThreeYear() float32 {
	if x != nil {
		return x.CategoryAvgThreeYear
	}
	return 0
}

func (x *InformationRatio) GetCategoryAvgFiveYear() float32 {
	if x != nil {
		return x.CategoryAvgFiveYear
	}
	return 0
}

func (x *InformationRatio) GetPercentileRankOneYear() float32 {
	if x != nil {
		return x.PercentileRankOneYear
	}
	return 0
}

func (x *InformationRatio) GetPercentileRankThreeYear() float32 {
	if x != nil {
		return x.PercentileRankThreeYear
	}
	return 0
}

func (x *InformationRatio) GetPercentileRankFiveYear() float32 {
	if x != nil {
		return x.PercentileRankFiveYear
	}
	return 0
}

// Alpha is a measure of an investment's performance on a risk-adjusted basis. Simply stated,
// alpha is often considered to represent the value that a portfolio manager adds or subtracts from a fund portfolio's return.
// An alpha of 1.0 means the fund has outperformed its benchmark index by 1%.
// Modifiable montlhy by AMC
type Alpha struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundOneYear             float32 `protobuf:"fixed32,1,opt,name=fund_one_year,json=fundOneYear,proto3" json:"fund_one_year,omitempty"`
	FundThreeYear           float32 `protobuf:"fixed32,2,opt,name=fund_three_year,json=fundThreeYear,proto3" json:"fund_three_year,omitempty"`
	FundFiveYear            float32 `protobuf:"fixed32,3,opt,name=fund_five_year,json=fundFiveYear,proto3" json:"fund_five_year,omitempty"`
	CategoryAvgOneYear      float32 `protobuf:"fixed32,4,opt,name=category_avg_one_year,json=categoryAvgOneYear,proto3" json:"category_avg_one_year,omitempty"`
	CategoryAvgThreeYear    float32 `protobuf:"fixed32,5,opt,name=category_avg_three_year,json=categoryAvgThreeYear,proto3" json:"category_avg_three_year,omitempty"`
	CategoryAvgFiveYear     float32 `protobuf:"fixed32,6,opt,name=category_avg_five_year,json=categoryAvgFiveYear,proto3" json:"category_avg_five_year,omitempty"`
	PercentileRankOneYear   float32 `protobuf:"fixed32,8,opt,name=percentile_rank_one_year,json=percentileRankOneYear,proto3" json:"percentile_rank_one_year,omitempty"`
	PercentileRankThreeYear float32 `protobuf:"fixed32,9,opt,name=percentile_rank_three_year,json=percentileRankThreeYear,proto3" json:"percentile_rank_three_year,omitempty"`
	PercentileRankFiveYear  float32 `protobuf:"fixed32,10,opt,name=percentile_rank_five_year,json=percentileRankFiveYear,proto3" json:"percentile_rank_five_year,omitempty"`
}

func (x *Alpha) Reset() {
	*x = Alpha{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Alpha) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Alpha) ProtoMessage() {}

func (x *Alpha) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Alpha.ProtoReflect.Descriptor instead.
func (*Alpha) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{18}
}

func (x *Alpha) GetFundOneYear() float32 {
	if x != nil {
		return x.FundOneYear
	}
	return 0
}

func (x *Alpha) GetFundThreeYear() float32 {
	if x != nil {
		return x.FundThreeYear
	}
	return 0
}

func (x *Alpha) GetFundFiveYear() float32 {
	if x != nil {
		return x.FundFiveYear
	}
	return 0
}

func (x *Alpha) GetCategoryAvgOneYear() float32 {
	if x != nil {
		return x.CategoryAvgOneYear
	}
	return 0
}

func (x *Alpha) GetCategoryAvgThreeYear() float32 {
	if x != nil {
		return x.CategoryAvgThreeYear
	}
	return 0
}

func (x *Alpha) GetCategoryAvgFiveYear() float32 {
	if x != nil {
		return x.CategoryAvgFiveYear
	}
	return 0
}

func (x *Alpha) GetPercentileRankOneYear() float32 {
	if x != nil {
		return x.PercentileRankOneYear
	}
	return 0
}

func (x *Alpha) GetPercentileRankThreeYear() float32 {
	if x != nil {
		return x.PercentileRankThreeYear
	}
	return 0
}

func (x *Alpha) GetPercentileRankFiveYear() float32 {
	if x != nil {
		return x.PercentileRankFiveYear
	}
	return 0
}

// https://www.investopedia.com/terms/b/benchmark.asp
// https://www.investopedia.com/articles/mutualfund/04/032404.asp
// % abs return of the benchmark of a fund
type BenchmarkDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Benchmark                string  `protobuf:"bytes,1,opt,name=benchmark,proto3" json:"benchmark,omitempty"`                                                                     // Modifiable montlhy by AMC
	BenchmarkReturnOneYear   float32 `protobuf:"fixed32,2,opt,name=benchmark_return_one_year,json=benchmarkReturnOneYear,proto3" json:"benchmark_return_one_year,omitempty"`       // updated daily
	BenchmarkReturnFiveYear  float32 `protobuf:"fixed32,3,opt,name=benchmark_return_five_year,json=benchmarkReturnFiveYear,proto3" json:"benchmark_return_five_year,omitempty"`    // updated daily
	BenchmarkReturnMaxPeriod float32 `protobuf:"fixed32,4,opt,name=benchmark_return_max_period,json=benchmarkReturnMaxPeriod,proto3" json:"benchmark_return_max_period,omitempty"` // updated daily
	BenchmarkReturnThreeYear float32 `protobuf:"fixed32,5,opt,name=benchmark_return_three_year,json=benchmarkReturnThreeYear,proto3" json:"benchmark_return_three_year,omitempty"` // updated daily
}

func (x *BenchmarkDetails) Reset() {
	*x = BenchmarkDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenchmarkDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenchmarkDetails) ProtoMessage() {}

func (x *BenchmarkDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenchmarkDetails.ProtoReflect.Descriptor instead.
func (*BenchmarkDetails) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{19}
}

func (x *BenchmarkDetails) GetBenchmark() string {
	if x != nil {
		return x.Benchmark
	}
	return ""
}

func (x *BenchmarkDetails) GetBenchmarkReturnOneYear() float32 {
	if x != nil {
		return x.BenchmarkReturnOneYear
	}
	return 0
}

func (x *BenchmarkDetails) GetBenchmarkReturnFiveYear() float32 {
	if x != nil {
		return x.BenchmarkReturnFiveYear
	}
	return 0
}

func (x *BenchmarkDetails) GetBenchmarkReturnMaxPeriod() float32 {
	if x != nil {
		return x.BenchmarkReturnMaxPeriod
	}
	return 0
}

func (x *BenchmarkDetails) GetBenchmarkReturnThreeYear() float32 {
	if x != nil {
		return x.BenchmarkReturnThreeYear
	}
	return 0
}

// fundamental information about a fund like(AUM, MarketCap, FundManagers, expense ratio etc..)
type FundFundamentalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// https://www.investopedia.com/terms/e/equity_stylebox.asp
	// Modifiable montlhy
	EquityStyleBox string `protobuf:"bytes,1,opt,name=equity_style_box,json=equityStyleBox,proto3" json:"equity_style_box,omitempty"`
	// Modifiable montlhy
	BondStyleBox string `protobuf:"bytes,2,opt,name=bond_style_box,json=bondStyleBox,proto3" json:"bond_style_box,omitempty"`
	// Assets under management (AUM) is the total market value of the investments that a MF manages on behalf of clients.
	// Modifiable montlhy by AMC
	Aum *Aum `protobuf:"bytes,3,opt,name=aum,proto3" json:"aum,omitempty"`
	// allocation of assets among bond, cash and equity
	// Modifiable montlhy by AMC
	AssetAllocationBreakdown *AssetAllocationBreakdown `protobuf:"bytes,4,opt,name=asset_allocation_breakdown,json=assetAllocationBreakdown,proto3" json:"asset_allocation_breakdown,omitempty"`
	// investment of a fund in different countries
	// Modifiable montlhy by AMC
	EquityCountryExposure *EquityCountryExposure `protobuf:"bytes,5,opt,name=equity_country_exposure,json=equityCountryExposure,proto3" json:"equity_country_exposure,omitempty"`
	// https://www.investopedia.com/terms/c/creditquality.asp
	// Modifiable montlhy by AMC
	CreditQualityBreakdown *CreditQualityBreakdown `protobuf:"bytes,6,opt,name=credit_quality_breakdown,json=creditQualityBreakdown,proto3" json:"credit_quality_breakdown,omitempty"`
	// http://awgmain.morningstar.com/webhelp/glossary_definitions/mutual_fund/Morningstar_Market_Capitalization_Definitions.htm
	// Modifiable montlhy by AMC
	MarketCap *MarketCap `protobuf:"bytes,7,opt,name=market_cap,json=marketCap,proto3" json:"market_cap,omitempty"`
	// https://indexes.morningstar.com/resources/PDF/Methodology%20Documents/SectorArticle.pdf
	// Modifiable montlhy by AMC
	GlobalEquitySectors *GlobalEquitySectors `protobuf:"bytes,8,opt,name=global_equity_sectors,json=globalEquitySectors,proto3" json:"global_equity_sectors,omitempty"`
	// https://www.morningstar.com/content/dam/marketing/shared/research/methodology/829856-Morningstar_Global_Fixed_Income_Classification_Methodology.pdf
	// Modifiable montlhy by AMC
	GlobalBondSectors *GlobalBondSectors `protobuf:"bytes,9,opt,name=global_bond_sectors,json=globalBondSectors,proto3" json:"global_bond_sectors,omitempty"`
	// The expense ratio is defined as the annual fee that an investor is charged for the management of his or her funds.
	// https://cleartax.in/s/expense-ratio-mutual-funds
	// Modifiable montlhy by AMC
	ExpenseRatio *ExpenseRatio `protobuf:"bytes,10,opt,name=expense_ratio,json=expenseRatio,proto3" json:"expense_ratio,omitempty"`
	// Modifiable montlhy by AMC
	FundManagers *FundManagers `protobuf:"bytes,11,opt,name=fund_managers,json=fundManagers,proto3" json:"fund_managers,omitempty"`
	// Modifiable montlhy by AMC
	//
	//	An exit load refers to the fee that the Asset Management Companies (AMCs) charge investors at the time of exiting or redeeming their fund units.
	ExitLoad *ExitLoad `protobuf:"bytes,12,opt,name=exit_load,json=exitLoad,proto3" json:"exit_load,omitempty"`
	// The date on which a fund was started
	// Not Modifiable
	InceptionDate *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=inception_date,json=inceptionDate,proto3" json:"inception_date,omitempty"`
	// holdings of a fund in different assets
	Holdings *Holdings `protobuf:"bytes,14,opt,name=holdings,proto3" json:"holdings,omitempty"`
	// url for scheme document(basically amc website for now)
	SchemeDocument string `protobuf:"bytes,15,opt,name=scheme_document,json=schemeDocument,proto3" json:"scheme_document,omitempty"`
}

func (x *FundFundamentalDetails) Reset() {
	*x = FundFundamentalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundFundamentalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundFundamentalDetails) ProtoMessage() {}

func (x *FundFundamentalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundFundamentalDetails.ProtoReflect.Descriptor instead.
func (*FundFundamentalDetails) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{20}
}

func (x *FundFundamentalDetails) GetEquityStyleBox() string {
	if x != nil {
		return x.EquityStyleBox
	}
	return ""
}

func (x *FundFundamentalDetails) GetBondStyleBox() string {
	if x != nil {
		return x.BondStyleBox
	}
	return ""
}

func (x *FundFundamentalDetails) GetAum() *Aum {
	if x != nil {
		return x.Aum
	}
	return nil
}

func (x *FundFundamentalDetails) GetAssetAllocationBreakdown() *AssetAllocationBreakdown {
	if x != nil {
		return x.AssetAllocationBreakdown
	}
	return nil
}

func (x *FundFundamentalDetails) GetEquityCountryExposure() *EquityCountryExposure {
	if x != nil {
		return x.EquityCountryExposure
	}
	return nil
}

func (x *FundFundamentalDetails) GetCreditQualityBreakdown() *CreditQualityBreakdown {
	if x != nil {
		return x.CreditQualityBreakdown
	}
	return nil
}

func (x *FundFundamentalDetails) GetMarketCap() *MarketCap {
	if x != nil {
		return x.MarketCap
	}
	return nil
}

func (x *FundFundamentalDetails) GetGlobalEquitySectors() *GlobalEquitySectors {
	if x != nil {
		return x.GlobalEquitySectors
	}
	return nil
}

func (x *FundFundamentalDetails) GetGlobalBondSectors() *GlobalBondSectors {
	if x != nil {
		return x.GlobalBondSectors
	}
	return nil
}

func (x *FundFundamentalDetails) GetExpenseRatio() *ExpenseRatio {
	if x != nil {
		return x.ExpenseRatio
	}
	return nil
}

func (x *FundFundamentalDetails) GetFundManagers() *FundManagers {
	if x != nil {
		return x.FundManagers
	}
	return nil
}

func (x *FundFundamentalDetails) GetExitLoad() *ExitLoad {
	if x != nil {
		return x.ExitLoad
	}
	return nil
}

func (x *FundFundamentalDetails) GetInceptionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InceptionDate
	}
	return nil
}

func (x *FundFundamentalDetails) GetHoldings() *Holdings {
	if x != nil {
		return x.Holdings
	}
	return nil
}

func (x *FundFundamentalDetails) GetSchemeDocument() string {
	if x != nil {
		return x.SchemeDocument
	}
	return ""
}

// The expense ratio is defined as the annual fee that an investor is charged for the management of his or her funds.
// https://cleartax.in/s/expense-ratio-mutual-funds
// Modifiable montlhy by AMC
type ExpenseRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundCurrent          float32 `protobuf:"fixed32,1,opt,name=fund_current,json=fundCurrent,proto3" json:"fund_current,omitempty"`
	FundOneYearAvg       float32 `protobuf:"fixed32,2,opt,name=fund_one_year_avg,json=fundOneYearAvg,proto3" json:"fund_one_year_avg,omitempty"`
	FundThreeYearAvg     float32 `protobuf:"fixed32,3,opt,name=fund_three_year_avg,json=fundThreeYearAvg,proto3" json:"fund_three_year_avg,omitempty"`
	CategoryCurrent      float32 `protobuf:"fixed32,4,opt,name=category_current,json=categoryCurrent,proto3" json:"category_current,omitempty"`
	CategoryOneYearAvg   float32 `protobuf:"fixed32,5,opt,name=category_one_year_avg,json=categoryOneYearAvg,proto3" json:"category_one_year_avg,omitempty"`
	CategoryThreeYearAvg float32 `protobuf:"fixed32,6,opt,name=category_three_year_avg,json=categoryThreeYearAvg,proto3" json:"category_three_year_avg,omitempty"`
}

func (x *ExpenseRatio) Reset() {
	*x = ExpenseRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpenseRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpenseRatio) ProtoMessage() {}

func (x *ExpenseRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpenseRatio.ProtoReflect.Descriptor instead.
func (*ExpenseRatio) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{21}
}

func (x *ExpenseRatio) GetFundCurrent() float32 {
	if x != nil {
		return x.FundCurrent
	}
	return 0
}

func (x *ExpenseRatio) GetFundOneYearAvg() float32 {
	if x != nil {
		return x.FundOneYearAvg
	}
	return 0
}

func (x *ExpenseRatio) GetFundThreeYearAvg() float32 {
	if x != nil {
		return x.FundThreeYearAvg
	}
	return 0
}

func (x *ExpenseRatio) GetCategoryCurrent() float32 {
	if x != nil {
		return x.CategoryCurrent
	}
	return 0
}

func (x *ExpenseRatio) GetCategoryOneYearAvg() float32 {
	if x != nil {
		return x.CategoryOneYearAvg
	}
	return 0
}

func (x *ExpenseRatio) GetCategoryThreeYearAvg() float32 {
	if x != nil {
		return x.CategoryThreeYearAvg
	}
	return 0
}

// Assets under management (AUM) is the total market value of the investments that a MF manages on behalf of clients.
// Modifiable montlhy by AMC
type Aum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundAum          float64 `protobuf:"fixed64,1,opt,name=fund_aum,json=fundAum,proto3" json:"fund_aum,omitempty"`
	CategoryAvgAum   float64 `protobuf:"fixed64,2,opt,name=category_avg_aum,json=categoryAvgAum,proto3" json:"category_avg_aum,omitempty"`
	CategoryTotalAum float64 `protobuf:"fixed64,3,opt,name=category_total_aum,json=categoryTotalAum,proto3" json:"category_total_aum,omitempty"`
}

func (x *Aum) Reset() {
	*x = Aum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Aum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Aum) ProtoMessage() {}

func (x *Aum) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Aum.ProtoReflect.Descriptor instead.
func (*Aum) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{22}
}

func (x *Aum) GetFundAum() float64 {
	if x != nil {
		return x.FundAum
	}
	return 0
}

func (x *Aum) GetCategoryAvgAum() float64 {
	if x != nil {
		return x.CategoryAvgAum
	}
	return 0
}

func (x *Aum) GetCategoryTotalAum() float64 {
	if x != nil {
		return x.CategoryTotalAum
	}
	return 0
}

// Holdings represents the constituents of the fund.
type Holdings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	Holdings map[string]float32 `protobuf:"bytes,1,rep,name=holdings,proto3" json:"holdings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	Details  []*HoldingDetail   `protobuf:"bytes,2,rep,name=details,proto3" json:"details,omitempty"`
}

func (x *Holdings) Reset() {
	*x = Holdings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Holdings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Holdings) ProtoMessage() {}

func (x *Holdings) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Holdings.ProtoReflect.Descriptor instead.
func (*Holdings) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{23}
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *Holdings) GetHoldings() map[string]float32 {
	if x != nil {
		return x.Holdings
	}
	return nil
}

func (x *Holdings) GetDetails() []*HoldingDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

// HoldingDetail represents the data fields for a particular holding (constituent) of the fund.
type HoldingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// weight represents the percentage share held in the fund.
	Weight float32 `protobuf:"fixed32,2,opt,name=weight,proto3" json:"weight,omitempty"`
	// ISIN stands for International Securities Identification Numbering system (defined by ISO 6166) and is the global ISO
	// standard for unique identification of financial and referential instruments, including equity, debt, derivatives and indices.
	Isin string `protobuf:"bytes,3,opt,name=isin,proto3" json:"isin,omitempty"`
	// unique identifier of the holding specific defined by the vendor.
	// NOTE: the vendor name can be referred in the `VendorMetadata` message.
	VendorId string `protobuf:"bytes,4,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	// TODO(anand): revisit this definition to see how can this be extended based on morning star categorisation
	AssetClass AssetClass `protobuf:"varint,5,opt,name=asset_class,json=assetClass,proto3,enum=api.investment.mutualfund.AssetClass" json:"asset_class,omitempty"`
	// type of holding as indicated by vendor
	// Eg: E -> Equity, B -> Bond etc.
	// ToDo: add doc to mapping of holding type to asset class
	VendorHoldingType string `protobuf:"bytes,6,opt,name=vendor_holding_type,json=vendorHoldingType,proto3" json:"vendor_holding_type,omitempty"`
}

func (x *HoldingDetail) Reset() {
	*x = HoldingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HoldingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HoldingDetail) ProtoMessage() {}

func (x *HoldingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HoldingDetail.ProtoReflect.Descriptor instead.
func (*HoldingDetail) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{24}
}

func (x *HoldingDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HoldingDetail) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *HoldingDetail) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *HoldingDetail) GetVendorId() string {
	if x != nil {
		return x.VendorId
	}
	return ""
}

func (x *HoldingDetail) GetAssetClass() AssetClass {
	if x != nil {
		return x.AssetClass
	}
	return AssetClass_ASSET_CLASS_UNSPECIFIED
}

func (x *HoldingDetail) GetVendorHoldingType() string {
	if x != nil {
		return x.VendorHoldingType
	}
	return ""
}

// internal fields for display display
// Modifiable montlhy by Fi content team
type FiContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// internal fields to fi team
	// Not Modifiable
	SimplifiedCategoryDescription string `protobuf:"bytes,1,opt,name=simplified_category_description,json=simplifiedCategoryDescription,proto3" json:"simplified_category_description,omitempty"`
	// internal fields to fi team
	// Modifiable montlhy by fi content team
	UniqueSpecs  string `protobuf:"bytes,2,opt,name=unique_specs,json=uniqueSpecs,proto3" json:"unique_specs,omitempty"`
	TaxTreatment string `protobuf:"bytes,3,opt,name=tax_treatment,json=taxTreatment,proto3" json:"tax_treatment,omitempty"`
	//	https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--FFF-%2F-Wealth-%2F-Mutual-Funds-%2F-v1.1-%2F-Mar-2022?node-id=10%3A17872
	//
	// required to support nuggets tile
	// nuggets are tags like 'Least fund house expense', 'Market risk sensitive' etc
	Nuggets []*Nugget `protobuf:"bytes,4,rep,name=nuggets,proto3" json:"nuggets,omitempty"`
}

func (x *FiContent) Reset() {
	*x = FiContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiContent) ProtoMessage() {}

func (x *FiContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiContent.ProtoReflect.Descriptor instead.
func (*FiContent) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{25}
}

func (x *FiContent) GetSimplifiedCategoryDescription() string {
	if x != nil {
		return x.SimplifiedCategoryDescription
	}
	return ""
}

func (x *FiContent) GetUniqueSpecs() string {
	if x != nil {
		return x.UniqueSpecs
	}
	return ""
}

func (x *FiContent) GetTaxTreatment() string {
	if x != nil {
		return x.TaxTreatment
	}
	return ""
}

func (x *FiContent) GetNuggets() []*Nugget {
	if x != nil {
		return x.Nuggets
	}
	return nil
}

type Nugget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// text like 'Least fund house expense', 'Market risk sensitive' that will be shown in nuggets
	NuggetText string          `protobuf:"bytes,1,opt,name=nugget_text,json=nuggetText,proto3" json:"nugget_text,omitempty"`
	Sentiment  NuggetSentiment `protobuf:"varint,2,opt,name=sentiment,proto3,enum=api.investment.mutualfund.NuggetSentiment" json:"sentiment,omitempty"`
}

func (x *Nugget) Reset() {
	*x = Nugget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nugget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nugget) ProtoMessage() {}

func (x *Nugget) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nugget.ProtoReflect.Descriptor instead.
func (*Nugget) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{26}
}

func (x *Nugget) GetNuggetText() string {
	if x != nil {
		return x.NuggetText
	}
	return ""
}

func (x *Nugget) GetSentiment() NuggetSentiment {
	if x != nil {
		return x.Sentiment
	}
	return NuggetSentiment_NUGGET_SENTIMENT_UNSPECIFIED
}

// allocation of assets among bond, cash and equity
// Modifiable montlhy by AMC
type AssetAllocationBreakdown struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BondNet   float32 `protobuf:"fixed32,1,opt,name=bond_net,json=bondNet,proto3" json:"bond_net,omitempty"`
	CashNet   float32 `protobuf:"fixed32,2,opt,name=cash_net,json=cashNet,proto3" json:"cash_net,omitempty"`
	EquityNet float32 `protobuf:"fixed32,3,opt,name=equity_net,json=equityNet,proto3" json:"equity_net,omitempty"`
	OtherNet  float32 `protobuf:"fixed32,4,opt,name=other_net,json=otherNet,proto3" json:"other_net,omitempty"`
}

func (x *AssetAllocationBreakdown) Reset() {
	*x = AssetAllocationBreakdown{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetAllocationBreakdown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetAllocationBreakdown) ProtoMessage() {}

func (x *AssetAllocationBreakdown) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetAllocationBreakdown.ProtoReflect.Descriptor instead.
func (*AssetAllocationBreakdown) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{27}
}

func (x *AssetAllocationBreakdown) GetBondNet() float32 {
	if x != nil {
		return x.BondNet
	}
	return 0
}

func (x *AssetAllocationBreakdown) GetCashNet() float32 {
	if x != nil {
		return x.CashNet
	}
	return 0
}

func (x *AssetAllocationBreakdown) GetEquityNet() float32 {
	if x != nil {
		return x.EquityNet
	}
	return 0
}

func (x *AssetAllocationBreakdown) GetOtherNet() float32 {
	if x != nil {
		return x.OtherNet
	}
	return 0
}

// investment of a fund in different countries
// Modifiable montlhy by AMC
type EquityCountryExposure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Breakdown []*EquityCountryExposureBreakdown `protobuf:"bytes,1,rep,name=breakdown,proto3" json:"breakdown,omitempty"`
}

func (x *EquityCountryExposure) Reset() {
	*x = EquityCountryExposure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquityCountryExposure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquityCountryExposure) ProtoMessage() {}

func (x *EquityCountryExposure) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquityCountryExposure.ProtoReflect.Descriptor instead.
func (*EquityCountryExposure) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{28}
}

func (x *EquityCountryExposure) GetBreakdown() []*EquityCountryExposureBreakdown {
	if x != nil {
		return x.Breakdown
	}
	return nil
}

type EquityCountryExposureBreakdown struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Country       string  `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	ExposureValue float32 `protobuf:"fixed32,2,opt,name=exposure_value,json=exposureValue,proto3" json:"exposure_value,omitempty"`
}

func (x *EquityCountryExposureBreakdown) Reset() {
	*x = EquityCountryExposureBreakdown{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquityCountryExposureBreakdown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquityCountryExposureBreakdown) ProtoMessage() {}

func (x *EquityCountryExposureBreakdown) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquityCountryExposureBreakdown.ProtoReflect.Descriptor instead.
func (*EquityCountryExposureBreakdown) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{29}
}

func (x *EquityCountryExposureBreakdown) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *EquityCountryExposureBreakdown) GetExposureValue() float32 {
	if x != nil {
		return x.ExposureValue
	}
	return 0
}

// https://www.investopedia.com/terms/c/creditquality.asp
// Modifiable montlhy by AMC
//
//	Investment-grade bonds typically have ratings of AAA, AA, A, or BBB.
//	Non-investment-grade bonds, also referred to as high-yield or junk bonds, have lower credit quality and, therefore, usually present a higher risk to investors.
//	Non-investment-grade bonds typically have ratings of BB, B, CCC, CC, and C. These ratings indicate that there's a good chance that the bond issuer will renege on its obligations, or default.
//	In fact, D, the lowest grade, is reserved for bonds that are already in default.
type CreditQualityBreakdown struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditQualA        float32                `protobuf:"fixed32,1,opt,name=credit_qual_a,json=creditQualA,proto3" json:"credit_qual_a,omitempty"`
	CreditQualAa       float32                `protobuf:"fixed32,2,opt,name=credit_qual_aa,json=creditQualAa,proto3" json:"credit_qual_aa,omitempty"`
	CreditQualAaa      float32                `protobuf:"fixed32,3,opt,name=credit_qual_aaa,json=creditQualAaa,proto3" json:"credit_qual_aaa,omitempty"`
	CreditQualB        float32                `protobuf:"fixed32,4,opt,name=credit_qual_b,json=creditQualB,proto3" json:"credit_qual_b,omitempty"`
	CreditQualBb       float32                `protobuf:"fixed32,5,opt,name=credit_qual_bb,json=creditQualBb,proto3" json:"credit_qual_bb,omitempty"`
	CreditQualBbb      float32                `protobuf:"fixed32,6,opt,name=credit_qual_bbb,json=creditQualBbb,proto3" json:"credit_qual_bbb,omitempty"`
	CreditQualBelowB   float32                `protobuf:"fixed32,7,opt,name=credit_qual_below_b,json=creditQualBelowB,proto3" json:"credit_qual_below_b,omitempty"`
	CreditQualNotrated float32                `protobuf:"fixed32,8,opt,name=credit_qual_notrated,json=creditQualNotrated,proto3" json:"credit_qual_notrated,omitempty"`
	CreditQualDate     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=credit_qual_date,json=creditQualDate,proto3" json:"credit_qual_date,omitempty"`
}

func (x *CreditQualityBreakdown) Reset() {
	*x = CreditQualityBreakdown{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditQualityBreakdown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditQualityBreakdown) ProtoMessage() {}

func (x *CreditQualityBreakdown) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditQualityBreakdown.ProtoReflect.Descriptor instead.
func (*CreditQualityBreakdown) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{30}
}

func (x *CreditQualityBreakdown) GetCreditQualA() float32 {
	if x != nil {
		return x.CreditQualA
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualAa() float32 {
	if x != nil {
		return x.CreditQualAa
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualAaa() float32 {
	if x != nil {
		return x.CreditQualAaa
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualB() float32 {
	if x != nil {
		return x.CreditQualB
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualBb() float32 {
	if x != nil {
		return x.CreditQualBb
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualBbb() float32 {
	if x != nil {
		return x.CreditQualBbb
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualBelowB() float32 {
	if x != nil {
		return x.CreditQualBelowB
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualNotrated() float32 {
	if x != nil {
		return x.CreditQualNotrated
	}
	return 0
}

func (x *CreditQualityBreakdown) GetCreditQualDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreditQualDate
	}
	return nil
}

// This shows the breakdown of the maturities in a fixed-income portfolio. It reveals the percentage of fixed-income securities that fall within each maturity range.
// Modifiable montlhy by AMC
type MaturityBreakdown struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Breakdown_10To15Years  float32 `protobuf:"fixed32,1,opt,name=breakdown_10to15years,json=breakdown10to15years,proto3" json:"breakdown_10to15years,omitempty"`
	Breakdown_15To20Years  float32 `protobuf:"fixed32,2,opt,name=breakdown_15to20years,json=breakdown15to20years,proto3" json:"breakdown_15to20years,omitempty"`
	Breakdown_183To364Days float32 `protobuf:"fixed32,3,opt,name=breakdown_183to364days,json=breakdown183to364days,proto3" json:"breakdown_183to364days,omitempty"`
	Breakdown_1To3Years    float32 `protobuf:"fixed32,4,opt,name=breakdown_1to3years,json=breakdown1to3years,proto3" json:"breakdown_1to3years,omitempty"`
	Breakdown_1To7Days     float32 `protobuf:"fixed32,5,opt,name=breakdown_1to7days,json=breakdown1to7days,proto3" json:"breakdown_1to7days,omitempty"`
	Breakdown_20To30Years  float32 `protobuf:"fixed32,6,opt,name=breakdown_20to30years,json=breakdown20to30years,proto3" json:"breakdown_20to30years,omitempty"`
	Breakdown_31To90Days   float32 `protobuf:"fixed32,7,opt,name=breakdown_31to90days,json=breakdown31to90days,proto3" json:"breakdown_31to90days,omitempty"`
	Breakdown_3To5Years    float32 `protobuf:"fixed32,8,opt,name=breakdown_3to5years,json=breakdown3to5years,proto3" json:"breakdown_3to5years,omitempty"`
	Breakdown_5To7Years    float32 `protobuf:"fixed32,9,opt,name=breakdown_5to7years,json=breakdown5to7years,proto3" json:"breakdown_5to7years,omitempty"`
	Breakdown_7To10Years   float32 `protobuf:"fixed32,10,opt,name=breakdown_7to10years,json=breakdown7to10years,proto3" json:"breakdown_7to10years,omitempty"`
	Breakdown_8To30Days    float32 `protobuf:"fixed32,11,opt,name=breakdown_8to30days,json=breakdown8to30days,proto3" json:"breakdown_8to30days,omitempty"`
	Breakdown_91To182Days  float32 `protobuf:"fixed32,12,opt,name=breakdown_91to182days,json=breakdown91to182days,proto3" json:"breakdown_91to182days,omitempty"`
	BreakdownOver30Years   float32 `protobuf:"fixed32,13,opt,name=breakdown_over30years,json=breakdownOver30years,proto3" json:"breakdown_over30years,omitempty"`
}

func (x *MaturityBreakdown) Reset() {
	*x = MaturityBreakdown{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaturityBreakdown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaturityBreakdown) ProtoMessage() {}

func (x *MaturityBreakdown) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaturityBreakdown.ProtoReflect.Descriptor instead.
func (*MaturityBreakdown) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{31}
}

func (x *MaturityBreakdown) GetBreakdown_10To15Years() float32 {
	if x != nil {
		return x.Breakdown_10To15Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_15To20Years() float32 {
	if x != nil {
		return x.Breakdown_15To20Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_183To364Days() float32 {
	if x != nil {
		return x.Breakdown_183To364Days
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_1To3Years() float32 {
	if x != nil {
		return x.Breakdown_1To3Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_1To7Days() float32 {
	if x != nil {
		return x.Breakdown_1To7Days
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_20To30Years() float32 {
	if x != nil {
		return x.Breakdown_20To30Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_31To90Days() float32 {
	if x != nil {
		return x.Breakdown_31To90Days
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_3To5Years() float32 {
	if x != nil {
		return x.Breakdown_3To5Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_5To7Years() float32 {
	if x != nil {
		return x.Breakdown_5To7Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_7To10Years() float32 {
	if x != nil {
		return x.Breakdown_7To10Years
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_8To30Days() float32 {
	if x != nil {
		return x.Breakdown_8To30Days
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdown_91To182Days() float32 {
	if x != nil {
		return x.Breakdown_91To182Days
	}
	return 0
}

func (x *MaturityBreakdown) GetBreakdownOver30Years() float32 {
	if x != nil {
		return x.BreakdownOver30Years
	}
	return 0
}

// http://awgmain.morningstar.com/webhelp/glossary_definitions/mutual_fund/Morningstar_Market_Capitalization_Definitions.htm
// Giant-cap stocks are defined as those that account for the top 40% of the capitalization of each style zone;
// large-cap stocks represent the next 30%; mid-cap stocks represent the next 20%;
// small-cap stocks represent the next 7% and micro-cap stocks represent the smallest 3%.
// Modifiable montlhy by AMC
type MarketCap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiantLongRescaled float32 `protobuf:"fixed32,1,opt,name=giant_long_rescaled,json=giantLongRescaled,proto3" json:"giant_long_rescaled,omitempty"`
	LargeLongRescaled float32 `protobuf:"fixed32,2,opt,name=large_long_rescaled,json=largeLongRescaled,proto3" json:"large_long_rescaled,omitempty"`
	MicroLongRescaled float32 `protobuf:"fixed32,3,opt,name=micro_long_rescaled,json=microLongRescaled,proto3" json:"micro_long_rescaled,omitempty"`
	MidLongRescaled   float32 `protobuf:"fixed32,4,opt,name=mid_long_rescaled,json=midLongRescaled,proto3" json:"mid_long_rescaled,omitempty"`
	SmallLongRescaled float32 `protobuf:"fixed32,5,opt,name=small_long_rescaled,json=smallLongRescaled,proto3" json:"small_long_rescaled,omitempty"`
}

func (x *MarketCap) Reset() {
	*x = MarketCap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketCap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketCap) ProtoMessage() {}

func (x *MarketCap) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketCap.ProtoReflect.Descriptor instead.
func (*MarketCap) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{32}
}

func (x *MarketCap) GetGiantLongRescaled() float32 {
	if x != nil {
		return x.GiantLongRescaled
	}
	return 0
}

func (x *MarketCap) GetLargeLongRescaled() float32 {
	if x != nil {
		return x.LargeLongRescaled
	}
	return 0
}

func (x *MarketCap) GetMicroLongRescaled() float32 {
	if x != nil {
		return x.MicroLongRescaled
	}
	return 0
}

func (x *MarketCap) GetMidLongRescaled() float32 {
	if x != nil {
		return x.MidLongRescaled
	}
	return 0
}

func (x *MarketCap) GetSmallLongRescaled() float32 {
	if x != nil {
		return x.SmallLongRescaled
	}
	return 0
}

// https://indexes.morningstar.com/resources/PDF/Methodology%20Documents/SectorArticle.pdf
// Modifiable montlhy by AMC
type GlobalEquitySectors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EquitySectors map[string]float32 `protobuf:"bytes,1,rep,name=equity_sectors,json=equitySectors,proto3" json:"equity_sectors,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *GlobalEquitySectors) Reset() {
	*x = GlobalEquitySectors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalEquitySectors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalEquitySectors) ProtoMessage() {}

func (x *GlobalEquitySectors) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalEquitySectors.ProtoReflect.Descriptor instead.
func (*GlobalEquitySectors) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{33}
}

func (x *GlobalEquitySectors) GetEquitySectors() map[string]float32 {
	if x != nil {
		return x.EquitySectors
	}
	return nil
}

// https://www.morningstar.com/content/dam/marketing/shared/research/methodology/829856-Morningstar_Global_Fixed_Income_Classification_Methodology.pdf
// Modifiable montlhy by AMC
type GlobalBondSectors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BondSectors map[string]float32 `protobuf:"bytes,1,rep,name=bond_sectors,json=bondSectors,proto3" json:"bond_sectors,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *GlobalBondSectors) Reset() {
	*x = GlobalBondSectors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalBondSectors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalBondSectors) ProtoMessage() {}

func (x *GlobalBondSectors) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalBondSectors.ProtoReflect.Descriptor instead.
func (*GlobalBondSectors) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{34}
}

func (x *GlobalBondSectors) GetBondSectors() map[string]float32 {
	if x != nil {
		return x.BondSectors
	}
	return nil
}

type FundManagers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Managers []*FundManager `protobuf:"bytes,1,rep,name=managers,proto3" json:"managers,omitempty"`
}

func (x *FundManagers) Reset() {
	*x = FundManagers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundManagers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundManagers) ProtoMessage() {}

func (x *FundManagers) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundManagers.ProtoReflect.Descriptor instead.
func (*FundManagers) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{35}
}

func (x *FundManagers) GetManagers() []*FundManager {
	if x != nil {
		return x.Managers
	}
	return nil
}

type FundManager struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Role      string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	StartDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	Tenure    float32                `protobuf:"fixed32,4,opt,name=tenure,proto3" json:"tenure,omitempty"`
}

func (x *FundManager) Reset() {
	*x = FundManager{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundManager) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundManager) ProtoMessage() {}

func (x *FundManager) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundManager.ProtoReflect.Descriptor instead.
func (*FundManager) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{36}
}

func (x *FundManager) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FundManager) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *FundManager) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *FundManager) GetTenure() float32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

type SipMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SiDetails map[string]*AipDetail `protobuf:"bytes,1,rep,name=si_details,json=siDetails,proto3" json:"si_details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // key will be frequency(weekly/biweekly/monthly.. etc)
}

func (x *SipMetadata) Reset() {
	*x = SipMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SipMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SipMetadata) ProtoMessage() {}

func (x *SipMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SipMetadata.ProtoReflect.Descriptor instead.
func (*SipMetadata) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{37}
}

func (x *SipMetadata) GetSiDetails() map[string]*AipDetail {
	if x != nil {
		return x.SiDetails
	}
	return nil
}

type StpMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SiDetails map[string]*AipDetail `protobuf:"bytes,1,rep,name=si_details,json=siDetails,proto3" json:"si_details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // key will be frequency(weekly/biweekly/monthly.. etc)
}

func (x *StpMetadata) Reset() {
	*x = StpMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StpMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StpMetadata) ProtoMessage() {}

func (x *StpMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StpMetadata.ProtoReflect.Descriptor instead.
func (*StpMetadata) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{38}
}

func (x *StpMetadata) GetSiDetails() map[string]*AipDetail {
	if x != nil {
		return x.SiDetails
	}
	return nil
}

type SwpMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SiDetails map[string]*AipDetail `protobuf:"bytes,1,rep,name=si_details,json=siDetails,proto3" json:"si_details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // key will be frequency(weekly/biweekly/monthly.. etc)
}

func (x *SwpMetadata) Reset() {
	*x = SwpMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwpMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwpMetadata) ProtoMessage() {}

func (x *SwpMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwpMetadata.ProtoReflect.Descriptor instead.
func (*SwpMetadata) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{39}
}

func (x *SwpMetadata) GetSiDetails() map[string]*AipDetail {
	if x != nil {
		return x.SiDetails
	}
	return nil
}

// AIP stands for automated investments plans
type AipDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	Frequency string `protobuf:"bytes,1,opt,name=frequency,proto3" json:"frequency,omitempty"` // frequency(weekly/biweekly/monthly.. etc)
	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	FrequencyDate    string `protobuf:"bytes,2,opt,name=frequencyDate,proto3" json:"frequencyDate,omitempty"`           // date of month appended by | ex 1|16 means investment is only allowed on 1 and 16 of a month
	MinTenure        int32  `protobuf:"varint,3,opt,name=min_tenure,json=minTenure,proto3" json:"min_tenure,omitempty"` // min tenure for which to continue AIP
	MinAmount        int32  `protobuf:"varint,4,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	SubsequentAmount int32  `protobuf:"varint,5,opt,name=subsequent_amount,json=subsequentAmount,proto3" json:"subsequent_amount,omitempty"`
	// frequency(weekly/biweekly/monthly.. etc)
	AipFrequency AipFrequency `protobuf:"varint,6,opt,name=aip_frequency,json=aipFrequency,proto3,enum=api.investment.mutualfund.AipFrequency" json:"aip_frequency,omitempty"`
	// date of month when sip is allowed
	// eg. if sip allowed only on 1,8,15 and 22nd of a month then
	// allowed_dates = [1,8,15,22]
	AllowedDates []*date.Date `protobuf:"bytes,7,rep,name=allowed_dates,json=allowedDates,proto3" json:"allowed_dates,omitempty"`
	// days of week when sip is allowed
	// eg. if sip allowed only on Monday, Wednesday and Friday then
	// allowed_days = [1,3,5]
	AllowedDays []dayofweek.DayOfWeek `protobuf:"varint,8,rep,packed,name=allowed_days,json=allowedDays,proto3,enum=google.type.DayOfWeek" json:"allowed_days,omitempty"`
}

func (x *AipDetail) Reset() {
	*x = AipDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AipDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AipDetail) ProtoMessage() {}

func (x *AipDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AipDetail.ProtoReflect.Descriptor instead.
func (*AipDetail) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{40}
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *AipDetail) GetFrequency() string {
	if x != nil {
		return x.Frequency
	}
	return ""
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *AipDetail) GetFrequencyDate() string {
	if x != nil {
		return x.FrequencyDate
	}
	return ""
}

func (x *AipDetail) GetMinTenure() int32 {
	if x != nil {
		return x.MinTenure
	}
	return 0
}

func (x *AipDetail) GetMinAmount() int32 {
	if x != nil {
		return x.MinAmount
	}
	return 0
}

func (x *AipDetail) GetSubsequentAmount() int32 {
	if x != nil {
		return x.SubsequentAmount
	}
	return 0
}

func (x *AipDetail) GetAipFrequency() AipFrequency {
	if x != nil {
		return x.AipFrequency
	}
	return AipFrequency_AipFrequency_UNSPECIFIED
}

func (x *AipDetail) GetAllowedDates() []*date.Date {
	if x != nil {
		return x.AllowedDates
	}
	return nil
}

func (x *AipDetail) GetAllowedDays() []dayofweek.DayOfWeek {
	if x != nil {
		return x.AllowedDays
	}
	return nil
}

// An exit load refers to the fee that the Asset Management Companies (AMCs) charge investors at the time of exiting or redeeming their fund units.
type ExitLoad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Loads []*DeferLoad `protobuf:"bytes,1,rep,name=loads,proto3" json:"loads,omitempty"`
}

func (x *ExitLoad) Reset() {
	*x = ExitLoad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitLoad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitLoad) ProtoMessage() {}

func (x *ExitLoad) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitLoad.ProtoReflect.Descriptor instead.
func (*ExitLoad) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{41}
}

func (x *ExitLoad) GetLoads() []*DeferLoad {
	if x != nil {
		return x.Loads
	}
	return nil
}

// A deferred load is a sales charge or fee associated with a mutual fund that is charged when the investor redeems
// their holdings.
// e.g. if DeferLoadBreakPointUnit = DAYS, high_break_point is 2, load_value is 1 and defer_load_unit is % then
// 1% of the amount redeemed is charged as exit load if withdrawn before 2 days of investment
// e.g. if DeferLoadBreakPointUnit = MONTHS,low_break_point is 1 high_break_point is 2, load_value is 1 and defer_load_unit is % then
// 1% of the amount redeemed is charged as exit load if withdrawn after 1 month and before 2 months of investment
type DeferLoad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LowBreakPoint  int32 `protobuf:"varint,1,opt,name=low_break_point,json=lowBreakPoint,proto3" json:"low_break_point,omitempty"`
	HighBreakPoint int32 `protobuf:"varint,2,opt,name=high_break_point,json=highBreakPoint,proto3" json:"high_break_point,omitempty"`
	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	BreakpointUnit string  `protobuf:"bytes,3,opt,name=breakpoint_unit,json=breakpointUnit,proto3" json:"breakpoint_unit,omitempty"` // Years ...
	LoadValue      float32 `protobuf:"fixed32,4,opt,name=load_value,json=loadValue,proto3" json:"load_value,omitempty"`              // ex: if unit is % then this can be 1% if breakpoint_unit is fixed this can be 100 rs
	// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
	LoadUnit           string                  `protobuf:"bytes,5,opt,name=load_unit,json=loadUnit,proto3" json:"load_unit,omitempty"`                                                                                         // %charge or fixed charge by amc
	LoadBreakpointUnit DeferLoadBreakPointUnit `protobuf:"varint,6,opt,name=load_breakpoint_unit,json=loadBreakpointUnit,proto3,enum=api.investment.mutualfund.DeferLoadBreakPointUnit" json:"load_breakpoint_unit,omitempty"` // Years ...
	DeferLoadUnit      DeferLoadUnit           `protobuf:"varint,7,opt,name=defer_load_unit,json=deferLoadUnit,proto3,enum=api.investment.mutualfund.DeferLoadUnit" json:"defer_load_unit,omitempty"`                          // %charge or fixed charge by amc
}

func (x *DeferLoad) Reset() {
	*x = DeferLoad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeferLoad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeferLoad) ProtoMessage() {}

func (x *DeferLoad) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeferLoad.ProtoReflect.Descriptor instead.
func (*DeferLoad) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{42}
}

func (x *DeferLoad) GetLowBreakPoint() int32 {
	if x != nil {
		return x.LowBreakPoint
	}
	return 0
}

func (x *DeferLoad) GetHighBreakPoint() int32 {
	if x != nil {
		return x.HighBreakPoint
	}
	return 0
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *DeferLoad) GetBreakpointUnit() string {
	if x != nil {
		return x.BreakpointUnit
	}
	return ""
}

func (x *DeferLoad) GetLoadValue() float32 {
	if x != nil {
		return x.LoadValue
	}
	return 0
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/mutual_fund.proto.
func (x *DeferLoad) GetLoadUnit() string {
	if x != nil {
		return x.LoadUnit
	}
	return ""
}

func (x *DeferLoad) GetLoadBreakpointUnit() DeferLoadBreakPointUnit {
	if x != nil {
		return x.LoadBreakpointUnit
	}
	return DeferLoadBreakPointUnit_DeferLoadBreakPointUnit_UNSPECIFIED
}

func (x *DeferLoad) GetDeferLoadUnit() DeferLoadUnit {
	if x != nil {
		return x.DeferLoadUnit
	}
	return DeferLoadUnit_DeferLoadUnit_UNSPECIFIED
}

// MutualFundCategoryAverage is used to hold category averages of following fields
// Returns 1,3,5 years
// Fund house expense
// Fund size
// Tracking error 1,3,5 years
// Alpha 1,3,5 years
// Sharpe ratio 1,3,5 years
// Information ratio 1,3,5 years
// Fund age
type MutualFundCategoryAverage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary identifier for mf_category_averages database
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CategoryName     MutualFundCategoryName `protobuf:"varint,2,opt,name=category_name,json=categoryName,proto3,enum=api.investment.mutualfund.MutualFundCategoryName" json:"category_name,omitempty"`
	Returns          *Returns               `protobuf:"bytes,3,opt,name=returns,proto3" json:"returns,omitempty"`
	ExpenseRatio     *ExpenseRatio          `protobuf:"bytes,4,opt,name=expense_ratio,json=expenseRatio,proto3" json:"expense_ratio,omitempty"`
	TrackingError    *TrackingError         `protobuf:"bytes,5,opt,name=tracking_error,json=trackingError,proto3" json:"tracking_error,omitempty"`
	SharpeRatio      *SharpeRatio           `protobuf:"bytes,6,opt,name=sharpe_ratio,json=sharpeRatio,proto3" json:"sharpe_ratio,omitempty"`
	InformationRatio *InformationRatio      `protobuf:"bytes,7,opt,name=information_ratio,json=informationRatio,proto3" json:"information_ratio,omitempty"`
	Alpha            *Alpha                 `protobuf:"bytes,8,opt,name=alpha,proto3" json:"alpha,omitempty"`
	Aum              *Aum                   `protobuf:"bytes,9,opt,name=aum,proto3" json:"aum,omitempty"`
	CategoryAvgAge   float64                `protobuf:"fixed64,10,opt,name=category_avg_age,json=categoryAvgAge,proto3" json:"category_avg_age,omitempty"`
	// Standard timestamp fields
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// HistoricalReturns is used to store data points required to plot a fund's return graph.
	// We store the historical return values in specified granularity
	HistoricalReturns *HistoricalReturns `protobuf:"bytes,14,opt,name=historical_returns,json=historicalReturns,proto3" json:"historical_returns,omitempty"`
}

func (x *MutualFundCategoryAverage) Reset() {
	*x = MutualFundCategoryAverage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutualFundCategoryAverage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutualFundCategoryAverage) ProtoMessage() {}

func (x *MutualFundCategoryAverage) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutualFundCategoryAverage.ProtoReflect.Descriptor instead.
func (*MutualFundCategoryAverage) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{43}
}

func (x *MutualFundCategoryAverage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MutualFundCategoryAverage) GetCategoryName() MutualFundCategoryName {
	if x != nil {
		return x.CategoryName
	}
	return MutualFundCategoryName_MutualFundCategoryName_UNSPECIFIED
}

func (x *MutualFundCategoryAverage) GetReturns() *Returns {
	if x != nil {
		return x.Returns
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetExpenseRatio() *ExpenseRatio {
	if x != nil {
		return x.ExpenseRatio
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetTrackingError() *TrackingError {
	if x != nil {
		return x.TrackingError
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetSharpeRatio() *SharpeRatio {
	if x != nil {
		return x.SharpeRatio
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetInformationRatio() *InformationRatio {
	if x != nil {
		return x.InformationRatio
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetAlpha() *Alpha {
	if x != nil {
		return x.Alpha
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetAum() *Aum {
	if x != nil {
		return x.Aum
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetCategoryAvgAge() float64 {
	if x != nil {
		return x.CategoryAvgAge
	}
	return 0
}

func (x *MutualFundCategoryAverage) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *MutualFundCategoryAverage) GetHistoricalReturns() *HistoricalReturns {
	if x != nil {
		return x.HistoricalReturns
	}
	return nil
}

// mutual funds can have dependencies on client
// so mutual funds require minimum supported version check based on client platform
type VersionSupportInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinSupportedAndroidAppVersion uint32 `protobuf:"varint,1,opt,name=min_supported_android_app_version,json=minSupportedAndroidAppVersion,proto3" json:"min_supported_android_app_version,omitempty"`
	MinSupportedIosAppVersion     uint32 `protobuf:"varint,2,opt,name=min_supported_ios_app_version,json=minSupportedIosAppVersion,proto3" json:"min_supported_ios_app_version,omitempty"`
}

func (x *VersionSupportInfo) Reset() {
	*x = VersionSupportInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionSupportInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionSupportInfo) ProtoMessage() {}

func (x *VersionSupportInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionSupportInfo.ProtoReflect.Descriptor instead.
func (*VersionSupportInfo) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{44}
}

func (x *VersionSupportInfo) GetMinSupportedAndroidAppVersion() uint32 {
	if x != nil {
		return x.MinSupportedAndroidAppVersion
	}
	return 0
}

func (x *VersionSupportInfo) GetMinSupportedIosAppVersion() uint32 {
	if x != nil {
		return x.MinSupportedIosAppVersion
	}
	return 0
}

type FundLockInPeriod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years  uint32 `protobuf:"varint,1,opt,name=years,proto3" json:"years,omitempty"`
	Months uint32 `protobuf:"varint,2,opt,name=months,proto3" json:"months,omitempty"`
	Days   uint32 `protobuf:"varint,3,opt,name=days,proto3" json:"days,omitempty"`
}

func (x *FundLockInPeriod) Reset() {
	*x = FundLockInPeriod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundLockInPeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundLockInPeriod) ProtoMessage() {}

func (x *FundLockInPeriod) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundLockInPeriod.ProtoReflect.Descriptor instead.
func (*FundLockInPeriod) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{45}
}

func (x *FundLockInPeriod) GetYears() uint32 {
	if x != nil {
		return x.Years
	}
	return 0
}

func (x *FundLockInPeriod) GetMonths() uint32 {
	if x != nil {
		return x.Months
	}
	return 0
}

func (x *FundLockInPeriod) GetDays() uint32 {
	if x != nil {
		return x.Days
	}
	return 0
}

// MutualFundNavHistory represents the net asset value of a fund at a date
type MutualFundNavHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary identifier to a mutual_fund_nav_history database model.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique identifier for the mutual fund
	//
	//	ISIN stands for International Securities Identification Numbering system (defined by ISO 6166) and is the global ISO
	//	standard for unique identification of financial and referential instruments, including equity, debt, derivatives and indices.
	Isin string `protobuf:"bytes,2,opt,name=isin,proto3" json:"isin,omitempty"`
	// date for which the nav is valid
	NavDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=nav_date,json=navDate,proto3" json:"nav_date,omitempty"`
	// value of one unit of the scheme
	Nav *money.Money `protobuf:"bytes,4,opt,name=nav,proto3" json:"nav,omitempty"`
	// vendor for the nav data
	Vendor MutualFundNavVendor `protobuf:"varint,5,opt,name=vendor,proto3,enum=api.investment.mutualfund.MutualFundNavVendor" json:"vendor,omitempty"`
	// source of the nav given the nav_vendor
	// e.g. if vendor is morning_star, source signifies how is the data point derived whether it came from morningstar
	// or we derived it from some previous morning_star data
	NavSource MutualFundNavSource `protobuf:"varint,6,opt,name=nav_source,json=navSource,proto3,enum=api.investment.mutualfund.MutualFundNavSource" json:"nav_source,omitempty"`
	// if nav_source is holiday derived then it stores the date the nav is derived from i.e. some previous date
	NavDerivedDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=nav_derived_date,json=navDerivedDate,proto3" json:"nav_derived_date,omitempty"`
	// mutual_fund_id is a unique internal identifier for a mutual fund.
	MutualFundId string `protobuf:"bytes,11,opt,name=mutual_fund_id,json=mutualFundId,proto3" json:"mutual_fund_id,omitempty"`
	// standard timestamp fields
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *MutualFundNavHistory) Reset() {
	*x = MutualFundNavHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutualFundNavHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutualFundNavHistory) ProtoMessage() {}

func (x *MutualFundNavHistory) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_mutual_fund_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutualFundNavHistory.ProtoReflect.Descriptor instead.
func (*MutualFundNavHistory) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP(), []int{46}
}

func (x *MutualFundNavHistory) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MutualFundNavHistory) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *MutualFundNavHistory) GetNavDate() *timestamppb.Timestamp {
	if x != nil {
		return x.NavDate
	}
	return nil
}

func (x *MutualFundNavHistory) GetNav() *money.Money {
	if x != nil {
		return x.Nav
	}
	return nil
}

func (x *MutualFundNavHistory) GetVendor() MutualFundNavVendor {
	if x != nil {
		return x.Vendor
	}
	return MutualFundNavVendor_MUTUAL_FUND_NAV_VENDOR_UNSPECIFIED
}

func (x *MutualFundNavHistory) GetNavSource() MutualFundNavSource {
	if x != nil {
		return x.NavSource
	}
	return MutualFundNavSource_MUTUAL_FUND_NAV_SOURCE_UNSPECIFIED
}

func (x *MutualFundNavHistory) GetNavDerivedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.NavDerivedDate
	}
	return nil
}

func (x *MutualFundNavHistory) GetMutualFundId() string {
	if x != nil {
		return x.MutualFundId
	}
	return ""
}

func (x *MutualFundNavHistory) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MutualFundNavHistory) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MutualFundNavHistory) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_investment_mutualfund_mutual_fund_proto protoreflect.FileDescriptor

var file_api_investment_mutualfund_mutual_fund_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x14, 0x0a, 0x0a, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x46, 0x75, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x6d, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x6d,
	0x63, 0x52, 0x03, 0x61, 0x6d, 0x63, 0x12, 0x48, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6e, 0x61, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x40, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x50, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6c,
	0x0a, 0x15, 0x64, 0x69, 0x76, 0x5f, 0x72, 0x65, 0x69, 0x6e, 0x76, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x44, 0x69, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x64, 0x69, 0x76, 0x52, 0x65, 0x69,
	0x6e, 0x76, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x03,
	0x6e, 0x61, 0x76, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x03, 0x6e,
	0x61, 0x76, 0x12, 0x46, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0a,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69,
	0x70, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x73, 0x69, 0x70, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x77, 0x70, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x73, 0x77, 0x70, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x74, 0x70, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x73, 0x74, 0x70, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x69, 0x73, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x69, 0x73, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x5a,
	0x0a, 0x0f, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x0e, 0x74, 0x78, 0x6e, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4b, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46,
	0x75, 0x6e, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x52, 0x0a, 0x0f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x6d, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x6d, 0x66, 0x69, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x75, 0x0a, 0x1c, 0x66, 0x75, 0x6e, 0x64, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x19,
	0x66, 0x75, 0x6e, 0x64, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64,
	0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0e, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x61, 0x76, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x3c, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x52, 0x07, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x73, 0x12, 0x58, 0x0a, 0x11, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61,
	0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x62, 0x65, 0x6e, 0x63, 0x68,
	0x6d, 0x61, 0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5e, 0x0a, 0x13, 0x70,
	0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x12, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x6b, 0x0a, 0x18, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x46, 0x75,
	0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x16, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x66, 0x69, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x66,
	0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18,
	0x20, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x5f, 0x0a, 0x14, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x61, 0x0a, 0x13, 0x66, 0x69, 0x5f, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x11, 0x66, 0x69, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x65, 0x0a, 0x16, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x66, 0x75,
	0x6e, 0x64, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x5b, 0x0a, 0x12, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c,
	0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x69, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x52, 0x11, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x12,
	0x35, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x5f,
	0x73, 0x69, 0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x53, 0x69, 0x70,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x6f, 0x62, 0x73, 0x6f, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6f, 0x62, 0x73, 0x6f, 0x6c,
	0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x66, 0x0a, 0x17, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x5f, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x78, 0x65, 0x6d, 0x70,
	0x74, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x14, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x45, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x79, 0x6e, 0x63, 0x22,
	0x67, 0x0a, 0x14, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x46,
	0x72, 0x6f, 0x6d, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x4f, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46,
	0x75, 0x6e, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x63, 0x0a, 0x11, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x12, 0x4e, 0x0a,
	0x0f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x22, 0x6a, 0x0a,
	0x0a, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0b, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x88, 0x04, 0x0a, 0x07, 0x41, 0x6d,
	0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x6d, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41,
	0x6d, 0x63, 0x52, 0x03, 0x61, 0x6d, 0x63, 0x12, 0x1d, 0x0a, 0x08, 0x61, 0x6d, 0x63, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x61,
	0x6d, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6d, 0x63, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6d, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x54, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x72, 0x74, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x03, 0x72, 0x74, 0x61,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x6d, 0x63, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x6d, 0x63, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0x92, 0x02, 0x0a, 0x12, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x71, 0x0a, 0x10, 0x46, 0x75, 0x6e,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x6f, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf4, 0x08, 0x0a,
	0x16, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x70, 0x5f,
	0x6d, 0x6e, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x09, 0x6e, 0x65, 0x77, 0x70, 0x4d, 0x6e, 0x76, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x0a, 0x6e, 0x65,
	0x77, 0x70, 0x5f, 0x6d, 0x78, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x70, 0x4d, 0x78, 0x76, 0x61, 0x6c, 0x12, 0x30, 0x0a,
	0x0a, 0x61, 0x64, 0x70, 0x5f, 0x6d, 0x6e, 0x5f, 0x61, 0x6d, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x61, 0x64, 0x70, 0x4d, 0x6e, 0x41, 0x6d, 0x74, 0x12,
	0x30, 0x0a, 0x0a, 0x61, 0x64, 0x70, 0x5f, 0x6d, 0x78, 0x5f, 0x61, 0x6d, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x61, 0x64, 0x70, 0x4d, 0x78, 0x41, 0x6d,
	0x74, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x64, 0x70, 0x5f, 0x6d, 0x6e, 0x5f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x70, 0x4d, 0x6e, 0x55, 0x6e, 0x74, 0x12,
	0x1c, 0x0a, 0x0a, 0x61, 0x64, 0x70, 0x5f, 0x6d, 0x78, 0x5f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x70, 0x4d, 0x78, 0x55, 0x6e, 0x74, 0x12, 0x2e, 0x0a,
	0x09, 0x70, 0x5f, 0x6d, 0x6e, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x70, 0x4d, 0x6e, 0x49, 0x6e, 0x63, 0x72, 0x12, 0x32, 0x0a,
	0x0b, 0x61, 0x64, 0x70, 0x5f, 0x6d, 0x6e, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x61, 0x64, 0x70, 0x4d, 0x6e, 0x49, 0x6e, 0x63,
	0x72, 0x12, 0x30, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x5f, 0x6d, 0x6e, 0x5f, 0x61, 0x6d, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x72, 0x65, 0x64, 0x4d, 0x6e,
	0x41, 0x6d, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x5f, 0x6d, 0x78, 0x5f, 0x61, 0x6d,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x72, 0x65, 0x64,
	0x4d, 0x78, 0x41, 0x6d, 0x74, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x5f, 0x6d, 0x6e, 0x5f,
	0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x64, 0x4d, 0x6e,
	0x55, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x5f, 0x6d, 0x78, 0x5f, 0x75, 0x6e,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x64, 0x4d, 0x78, 0x55, 0x6e,
	0x74, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x63, 0x72,
	0x12, 0x49, 0x0a, 0x0c, 0x73, 0x69, 0x70, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x53, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b,
	0x73, 0x69, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x0c, 0x73,
	0x74, 0x70, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x53, 0x74,
	0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x73, 0x74, 0x70, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x0c, 0x73, 0x77, 0x70, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x53, 0x77, 0x70, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x73, 0x77, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x5b, 0x0a, 0x12, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x72,
	0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x11, 0x6d, 0x61, 0x74,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x27,
	0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x6b, 0x69,
	0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x59, 0x0a, 0x12, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x46, 0x75, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x52, 0x10, 0x66, 0x75, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x12, 0x5f, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x69,
	0x70, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x14, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x41, 0x69, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x15, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x69, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x69, 0x65, 0x73, 0x22, 0x67, 0x0a, 0x0e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x10, 0x6d, 0x6f, 0x72, 0x6e, 0x69, 0x6e, 0x67,
	0x73, 0x74, 0x61, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x6f, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x73, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x6d, 0x6f, 0x72,
	0x6e, 0x69, 0x6e, 0x67, 0x73, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9a, 0x01, 0x0a,
	0x0f, 0x4d, 0x6f, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x19, 0x0a, 0x08, 0x6d, 0x73, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x73, 0x74, 0x61, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x6d,
	0x6f, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x74, 0x61, 0x72, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x6f, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x73, 0x74, 0x61, 0x72, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x1a, 0x6d,
	0x6f, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x6d, 0x6f, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x74, 0x61, 0x72, 0x50, 0x65, 0x72, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xd0, 0x04, 0x0a, 0x07, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x73, 0x12, 0x36, 0x0a, 0x18, 0x61, 0x76, 0x67, 0x5f, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x61, 0x76, 0x67, 0x46, 0x75, 0x6e, 0x64,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x3a, 0x0a,
	0x1a, 0x61, 0x76, 0x67, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x16, 0x61, 0x76, 0x67, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x38, 0x0a, 0x19, 0x61, 0x76, 0x67,
	0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x66, 0x69, 0x76,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x61, 0x76,
	0x67, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x46, 0x69, 0x76, 0x65, 0x59,
	0x65, 0x61, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x61, 0x76, 0x67, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x12, 0x61, 0x76, 0x67, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4d, 0x61, 0x78,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x3e, 0x0a, 0x1c, 0x61, 0x76, 0x67, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x6f, 0x6e,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18, 0x61, 0x76,
	0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4f,
	0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x42, 0x0a, 0x1e, 0x61, 0x76, 0x67, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1a,
	0x61, 0x76, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x40, 0x0a, 0x1d, 0x61, 0x76,
	0x67, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x19, 0x61, 0x76, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x38, 0x0a, 0x19,
	0x61, 0x76, 0x67, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f,
	0x6f, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x15, 0x61, 0x76, 0x67, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4f, 0x6e,
	0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x38, 0x0a, 0x19, 0x61, 0x76, 0x67, 0x5f, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x73, 0x69, 0x78, 0x5f, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x61, 0x76, 0x67, 0x46, 0x75,
	0x6e, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x53, 0x69, 0x78, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x12, 0x2a, 0x0a, 0x11, 0x79, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x6d, 0x61, 0x74,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x79, 0x69, 0x65,
	0x6c, 0x64, 0x54, 0x6f, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x22, 0xca, 0x03, 0x0a,
	0x0d, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x35,
	0x0a, 0x17, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x14, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4f, 0x6e,
	0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x39, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65,
	0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72,
	0x12, 0x37, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x15, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x39, 0x0a, 0x19, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x61, 0x78, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x61, 0x78, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x12, 0x43, 0x0a, 0x1f, 0x63, 0x61, 0x74, 0x5f, 0x61, 0x76, 0x67, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6f,
	0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1a, 0x63,
	0x61, 0x74, 0x41, 0x76, 0x67, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x47, 0x0a, 0x21, 0x63, 0x61, 0x74,
	0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x1c, 0x63, 0x61, 0x74, 0x41, 0x76, 0x67, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x45, 0x0a, 0x20, 0x63, 0x61, 0x74, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x66, 0x69, 0x76,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1b, 0x63, 0x61,
	0x74, 0x41, 0x76, 0x67, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0xde, 0x04, 0x0a, 0x12, 0x50, 0x65,
	0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x12, 0x4f, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x36, 0x0a, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x6c, 0x70,
	0x68, 0x61, 0x52, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x12, 0x58, 0x0a, 0x11, 0x69, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x52, 0x10, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x49, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65, 0x5f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x52,
	0x0a, 0x0f, 0x62, 0x61, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x52, 0x0e, 0x62, 0x61, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x12, 0x5f, 0x0a, 0x14, 0x75, 0x70, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x63, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x55, 0x70, 0x73,
	0x69, 0x64, 0x65, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52,
	0x12, 0x75, 0x70, 0x73, 0x69, 0x64, 0x65, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x65, 0x0a, 0x16, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x5f,
	0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x52, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0xcf, 0x03, 0x0a, 0x0b, 0x53,
	0x68, 0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0b, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x26,
	0x0a, 0x0f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66,
	0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c,
	0x66, 0x75, 0x6e, 0x64, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x31, 0x0a, 0x15,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x6f, 0x6e, 0x65,
	0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12,
	0x35, 0x0a, 0x17, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x14, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x41, 0x76, 0x67, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6f,
	0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4f, 0x6e, 0x65,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x3b, 0x0a, 0x1a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69,
	0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65,
	0x61, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61,
	0x72, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f,
	0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65,
	0x52, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0x47, 0x0a, 0x0e,
	0x42, 0x61, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x35,
	0x0a, 0x17, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x14, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x54, 0x68, 0x72, 0x65,
	0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0x4b, 0x0a, 0x12, 0x55, 0x70, 0x73, 0x69, 0x64, 0x65, 0x43,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x35, 0x0a, 0x17, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65,
	0x61, 0x72, 0x22, 0x4d, 0x0a, 0x14, 0x44, 0x6f, 0x77, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65,
	0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61,
	0x72, 0x22, 0xd4, 0x03, 0x0a, 0x10, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f,
	0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f,
	0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x64,
	0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x41, 0x76, 0x67, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x35, 0x0a, 0x17, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61,
	0x76, 0x67, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x13, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x46,
	0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72,
	0x12, 0x3b, 0x0a, 0x1a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65,
	0x52, 0x61, 0x6e, 0x6b, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x39, 0x0a,
	0x19, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b,
	0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x16, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b,
	0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0xc9, 0x03, 0x0a, 0x05, 0x41, 0x6c, 0x70,
	0x68, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0d, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x24,
	0x0a, 0x0e, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x69, 0x76, 0x65,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x31, 0x0a, 0x15, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x61, 0x76, 0x67, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67,
	0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65,
	0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x41, 0x76, 0x67, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x33,
	0x0a, 0x16, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x66,
	0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x46, 0x69, 0x76, 0x65, 0x59,
	0x65, 0x61, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c,
	0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x3b, 0x0a, 0x1a,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x17, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b,
	0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x66, 0x69, 0x76,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x76, 0x65,
	0x59, 0x65, 0x61, 0x72, 0x22, 0xa6, 0x02, 0x0a, 0x10, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61,
	0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x65, 0x6e,
	0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x65,
	0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x39, 0x0a, 0x19, 0x62, 0x65, 0x6e, 0x63, 0x68,
	0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x6f, 0x6e, 0x65, 0x5f,
	0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x62, 0x65, 0x6e, 0x63,
	0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x65, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x3b, 0x0a, 0x1a, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x66, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72,
	0x6b, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x46, 0x69, 0x76, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12,
	0x3d, 0x0a, 0x1b, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x18, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x4d, 0x61, 0x78, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x3d,
	0x0a, 0x1b, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x18, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0xf6, 0x08,
	0x0a, 0x16, 0x46, 0x75, 0x6e, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x71, 0x75, 0x69,
	0x74, 0x79, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f, 0x62, 0x6f, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x42,
	0x6f, 0x78, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x6f, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65,
	0x5f, 0x62, 0x6f, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x6f, 0x6e, 0x64,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x75, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x41, 0x75, 0x6d, 0x52, 0x03, 0x61, 0x75, 0x6d, 0x12, 0x71, 0x0a, 0x1a, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62,
	0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64,
	0x6f, 0x77, 0x6e, 0x52, 0x18, 0x61, 0x73, 0x73, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x68, 0x0a,
	0x17, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x15, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x45,
	0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x6b, 0x0a, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64,
	0x6f, 0x77, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x16, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x64, 0x6f, 0x77, 0x6e, 0x12, 0x43, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x70, 0x52, 0x09,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x70, 0x12, 0x62, 0x0a, 0x15, 0x67, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x45, 0x71, 0x75, 0x69, 0x74,
	0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x13, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x5c, 0x0a,
	0x13, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6e, 0x64, 0x5f, 0x73, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x6f, 0x6e,
	0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x11, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x42, 0x6f, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x65,
	0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x45,
	0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0c, 0x65, 0x78, 0x70,
	0x65, 0x6e, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x4c, 0x0a, 0x0d, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x75, 0x6e,
	0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x64, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x09, 0x65, 0x78, 0x69, 0x74, 0x5f,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x45, 0x78, 0x69, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x52,
	0x08, 0x65, 0x78, 0x69, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x41, 0x0a, 0x0e, 0x69, 0x6e, 0x63,
	0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x69,
	0x6e, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x08,
	0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xa0, 0x02, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x65, 0x6e,
	0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x66,
	0x75, 0x6e, 0x64, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x11, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x61, 0x76, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x59, 0x65,
	0x61, 0x72, 0x41, 0x76, 0x67, 0x12, 0x2d, 0x0a, 0x13, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x61, 0x76, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x10, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61,
	0x72, 0x41, 0x76, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x31, 0x0a, 0x15, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6f, 0x6e, 0x65, 0x5f,
	0x79, 0x65, 0x61, 0x72, 0x5f, 0x61, 0x76, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x41,
	0x76, 0x67, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x61, 0x76, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x14, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x41, 0x76, 0x67, 0x22, 0x78, 0x0a, 0x03, 0x41, 0x75, 0x6d,
	0x12, 0x19, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x10, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x61, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41,
	0x76, 0x67, 0x41, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x10, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x75, 0x6d, 0x22, 0xde, 0x01, 0x0a, 0x08, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x51, 0x0a, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x48,
	0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x3b, 0x0a, 0x0d, 0x48, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xe4, 0x01, 0x0a, 0x0d, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x09,
	0x46, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x1f, 0x73, 0x69, 0x6d,
	0x70, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1d, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x61, 0x78, 0x5f, 0x74, 0x72, 0x65, 0x61,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x61, 0x78,
	0x54, 0x72, 0x65, 0x61, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x07, 0x6e, 0x75, 0x67,
	0x67, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x52, 0x07, 0x6e,
	0x75, 0x67, 0x67, 0x65, 0x74, 0x73, 0x22, 0x73, 0x0a, 0x06, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x48, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x18,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x6f, 0x6e, 0x64,
	0x5f, 0x6e, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x62, 0x6f, 0x6e, 0x64,
	0x4e, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x6e, 0x65, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x63, 0x61, 0x73, 0x68, 0x4e, 0x65, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x09, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x4e, 0x65, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x08, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x65, 0x74, 0x22, 0x70, 0x0a, 0x15, 0x45, 0x71,
	0x75, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x12, 0x57, 0x0a, 0x09, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77,
	0x6e, 0x52, 0x09, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x22, 0x61, 0x0a, 0x1e,
	0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0d, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xa3, 0x03, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x41, 0x12, 0x24,
	0x0a, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x61, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75,
	0x61, 0x6c, 0x41, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71,
	0x75, 0x61, 0x6c, 0x5f, 0x61, 0x61, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x41, 0x61, 0x61, 0x12, 0x22, 0x0a, 0x0d,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x62, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x42,
	0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f,
	0x62, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x51, 0x75, 0x61, 0x6c, 0x42, 0x62, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x62, 0x62, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0d, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x42, 0x62, 0x62, 0x12, 0x2d,
	0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x62, 0x65,
	0x6c, 0x6f, 0x77, 0x5f, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x42, 0x65, 0x6c, 0x6f, 0x77, 0x42, 0x12, 0x30, 0x0a,
	0x14, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x72, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x44, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x51, 0x75, 0x61,
	0x6c, 0x44, 0x61, 0x74, 0x65, 0x22, 0xac, 0x05, 0x0a, 0x11, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x12, 0x33, 0x0a, 0x15, 0x62,
	0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x31, 0x30, 0x74, 0x6f, 0x31, 0x35, 0x79,
	0x65, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x31, 0x30, 0x74, 0x6f, 0x31, 0x35, 0x79, 0x65, 0x61, 0x72, 0x73,
	0x12, 0x33, 0x0a, 0x15, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x31, 0x35,
	0x74, 0x6f, 0x32, 0x30, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x14, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x31, 0x35, 0x74, 0x6f, 0x32, 0x30,
	0x79, 0x65, 0x61, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x16, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f,
	0x77, 0x6e, 0x5f, 0x31, 0x38, 0x33, 0x74, 0x6f, 0x33, 0x36, 0x34, 0x64, 0x61, 0x79, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e,
	0x31, 0x38, 0x33, 0x74, 0x6f, 0x33, 0x36, 0x34, 0x64, 0x61, 0x79, 0x73, 0x12, 0x2f, 0x0a, 0x13,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x31, 0x74, 0x6f, 0x33, 0x79, 0x65,
	0x61, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x64, 0x6f, 0x77, 0x6e, 0x31, 0x74, 0x6f, 0x33, 0x79, 0x65, 0x61, 0x72, 0x73, 0x12, 0x2d, 0x0a,
	0x12, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x31, 0x74, 0x6f, 0x37, 0x64,
	0x61, 0x79, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x64, 0x6f, 0x77, 0x6e, 0x31, 0x74, 0x6f, 0x37, 0x64, 0x61, 0x79, 0x73, 0x12, 0x33, 0x0a, 0x15,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x30, 0x74, 0x6f, 0x33, 0x30,
	0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x32, 0x30, 0x74, 0x6f, 0x33, 0x30, 0x79, 0x65, 0x61, 0x72,
	0x73, 0x12, 0x31, 0x0a, 0x14, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x33,
	0x31, 0x74, 0x6f, 0x39, 0x30, 0x64, 0x61, 0x79, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x13, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x33, 0x31, 0x74, 0x6f, 0x39, 0x30,
	0x64, 0x61, 0x79, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77,
	0x6e, 0x5f, 0x33, 0x74, 0x6f, 0x35, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x12, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x33, 0x74, 0x6f, 0x35,
	0x79, 0x65, 0x61, 0x72, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f,
	0x77, 0x6e, 0x5f, 0x35, 0x74, 0x6f, 0x37, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x12, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x35, 0x74, 0x6f,
	0x37, 0x79, 0x65, 0x61, 0x72, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64,
	0x6f, 0x77, 0x6e, 0x5f, 0x37, 0x74, 0x6f, 0x31, 0x30, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x37,
	0x74, 0x6f, 0x31, 0x30, 0x79, 0x65, 0x61, 0x72, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x38, 0x74, 0x6f, 0x33, 0x30, 0x64, 0x61, 0x79, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77,
	0x6e, 0x38, 0x74, 0x6f, 0x33, 0x30, 0x64, 0x61, 0x79, 0x73, 0x12, 0x33, 0x0a, 0x15, 0x62, 0x72,
	0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x39, 0x31, 0x74, 0x6f, 0x31, 0x38, 0x32, 0x64,
	0x61, 0x79, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x64, 0x6f, 0x77, 0x6e, 0x39, 0x31, 0x74, 0x6f, 0x31, 0x38, 0x32, 0x64, 0x61, 0x79, 0x73, 0x12,
	0x33, 0x0a, 0x15, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x33, 0x30, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x64, 0x6f, 0x77, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x33, 0x30, 0x79,
	0x65, 0x61, 0x72, 0x73, 0x22, 0xf7, 0x01, 0x0a, 0x09, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43,
	0x61, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x69, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x11, 0x67, 0x69, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x61, 0x6c,
	0x65, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6c, 0x6f, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x11, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x61, 0x6c,
	0x65, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x6c, 0x6f, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x11, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x61, 0x6c,
	0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x69, 0x64, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x6d,
	0x69, 0x64, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x73, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x73,
	0x63, 0x61, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x73, 0x6d, 0x61,
	0x6c, 0x6c, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x22, 0xc1,
	0x01, 0x0a, 0x13, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x68, 0x0a, 0x0e, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x45,
	0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0d, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73,
	0x1a, 0x40, 0x0a, 0x12, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xb5, 0x01, 0x0a, 0x11, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x6f, 0x6e,
	0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x60, 0x0a, 0x0c, 0x62, 0x6f, 0x6e, 0x64,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x42, 0x6f, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x42, 0x6f, 0x6e,
	0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x62,
	0x6f, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x42, 0x6f,
	0x6e, 0x64, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x52, 0x0a, 0x0c, 0x46, 0x75,
	0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x42, 0x0a, 0x08, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x22, 0x88,
	0x01, 0x0a, 0x0b, 0x46, 0x75, 0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x0b, 0x53, 0x69,
	0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x0a, 0x73, 0x69, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x53, 0x69, 0x70, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x73, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a,
	0x62, 0x0a, 0x0e, 0x53, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41,
	0x69, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xc7, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x0a, 0x73, 0x69, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x53, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x53, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09,
	0x73, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x62, 0x0a, 0x0e, 0x53, 0x69, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x69, 0x70, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc7, 0x01,
	0x0a, 0x0b, 0x53, 0x77, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a,
	0x0a, 0x73, 0x69, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x53, 0x77,
	0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x69, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x73, 0x69, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x1a, 0x62, 0x0a, 0x0e, 0x53, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x41, 0x69, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x83, 0x03, 0x0a, 0x09, 0x41, 0x69, 0x70, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x66, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x28, 0x0a, 0x0d, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0d, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x0d,
	0x61, 0x69, 0x70, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x41, 0x69, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0c, 0x61, 0x69,
	0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x36, 0x0a, 0x0d, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b,
	0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x44, 0x61, 0x79, 0x73, 0x22, 0x46, 0x0a,
	0x08, 0x45, 0x78, 0x69, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x3a, 0x0a, 0x05, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x44, 0x65, 0x66, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x05,
	0x6c, 0x6f, 0x61, 0x64, 0x73, 0x22, 0x82, 0x03, 0x0a, 0x09, 0x44, 0x65, 0x66, 0x65, 0x72, 0x4c,
	0x6f, 0x61, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x77, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6c, 0x6f,
	0x77, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x68,
	0x69, 0x67, 0x68, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x68, 0x69, 0x67, 0x68, 0x42, 0x72, 0x65, 0x61, 0x6b,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x0f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0e, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x6e,
	0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1f, 0x0a, 0x09, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x6e,
	0x69, 0x74, 0x12, 0x64, 0x0a, 0x14, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x44, 0x65, 0x66,
	0x65, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x55, 0x6e, 0x69, 0x74, 0x52, 0x12, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x50, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x65,
	0x72, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x44, 0x65,
	0x66, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x65, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x22, 0xa7, 0x07, 0x0a, 0x19, 0x4d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3c, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x73, 0x52, 0x07, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x12, 0x4c,
	0x0a, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0c,
	0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x4f, 0x0a, 0x0e,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x49, 0x0a,
	0x0c, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x53, 0x68, 0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0b, 0x73, 0x68, 0x61,
	0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x58, 0x0a, 0x11, 0x69, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x52, 0x10, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x12, 0x36, 0x0a, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x6c,
	0x70, 0x68, 0x61, 0x52, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x75,
	0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x41, 0x75, 0x6d, 0x52, 0x03, 0x61, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x10,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x61, 0x67, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x41, 0x76, 0x67, 0x41, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x5b, 0x0a, 0x12, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x73, 0x52, 0x11, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x12, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x48, 0x0a, 0x21, 0x6d,
	0x69, 0x6e, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1d, 0x6d, 0x69, 0x6e, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x1d, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6f, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x6d, 0x69,
	0x6e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x49, 0x6f, 0x73, 0x41, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x54, 0x0a, 0x10, 0x46, 0x75, 0x6e, 0x64, 0x4c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x79,
	0x65, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x79, 0x65, 0x61, 0x72,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x22, 0xdf, 0x04,
	0x0a, 0x14, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x76, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x12, 0x35, 0x0a, 0x08, 0x6e, 0x61,
	0x76, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x6e, 0x61, 0x76, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x24, 0x0a, 0x03, 0x6e, 0x61, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x03, 0x6e, 0x61, 0x76, 0x12, 0x50, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x4e, 0x61,
	0x76, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x57, 0x0a, 0x0a, 0x6e, 0x61, 0x76,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x46, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x76, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x6e, 0x61, 0x76, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x6e, 0x61, 0x76, 0x5f, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6e, 0x61, 0x76, 0x44, 0x65, 0x72,
	0x69, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a,
	0x8e, 0x02, 0x0a, 0x14, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x37, 0x46, 0x75, 0x6e, 0x64,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x02, 0x12, 0x1e,
	0x0a, 0x1a, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x25,
	0x0a, 0x21, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x52,
	0x47, 0x45, 0x44, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x05,
	0x2a, 0xe0, 0x05, 0x0a, 0x03, 0x41, 0x6d, 0x63, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x4d, 0x43, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x43, 0x49, 0x43, 0x49, 0x5f, 0x50, 0x52, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x44, 0x46, 0x43, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x0c, 0x41, 0x44, 0x49, 0x54, 0x59, 0x41, 0x5f, 0x42, 0x49, 0x52, 0x4c, 0x41, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x04, 0x12, 0x07, 0x0a,
	0x03, 0x53, 0x42, 0x49, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x49, 0x50, 0x50, 0x4f, 0x4e,
	0x5f, 0x4c, 0x49, 0x46, 0x45, 0x10, 0x06, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x54, 0x49, 0x10, 0x07,
	0x12, 0x0c, 0x0a, 0x08, 0x53, 0x55, 0x4e, 0x44, 0x41, 0x52, 0x41, 0x4d, 0x10, 0x08, 0x12, 0x07,
	0x0a, 0x03, 0x44, 0x53, 0x50, 0x10, 0x09, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x46, 0x43, 0x10,
	0x0a, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x43, 0x4f, 0x10, 0x0b, 0x12, 0x12,
	0x0a, 0x0e, 0x4b, 0x4f, 0x54, 0x41, 0x4b, 0x5f, 0x4d, 0x41, 0x48, 0x49, 0x4e, 0x44, 0x52, 0x41,
	0x10, 0x0c, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x58, 0x49, 0x53, 0x10, 0x0d, 0x12, 0x07, 0x0a, 0x03,
	0x4c, 0x4e, 0x54, 0x10, 0x0e, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x4e, 0x41, 0x52, 0x41, 0x5f,
	0x52, 0x4f, 0x42, 0x45, 0x43, 0x4f, 0x10, 0x0f, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x44, 0x45, 0x4c,
	0x57, 0x45, 0x49, 0x53, 0x53, 0x10, 0x10, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x47, 0x49, 0x4d, 0x10,
	0x11, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x4e, 0x50, 0x5f, 0x50, 0x41, 0x52, 0x49, 0x42, 0x41, 0x53,
	0x10, 0x12, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x41, 0x54, 0x41, 0x10, 0x13, 0x12, 0x09, 0x0a, 0x05,
	0x4d, 0x49, 0x52, 0x41, 0x45, 0x10, 0x14, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x49, 0x43, 0x10, 0x15,
	0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x42, 0x49, 0x10, 0x16, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x4d,
	0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x17, 0x12, 0x0b, 0x0a, 0x07,
	0x42, 0x4f, 0x49, 0x5f, 0x41, 0x58, 0x41, 0x10, 0x18, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x4e, 0x49,
	0x4f, 0x4e, 0x10, 0x19, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x53, 0x42, 0x43, 0x10, 0x1a, 0x12, 0x18,
	0x0a, 0x14, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x4d, 0x41,
	0x4e, 0x41, 0x47, 0x45, 0x52, 0x53, 0x10, 0x1b, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x41, 0x52, 0x4f,
	0x44, 0x41, 0x10, 0x1c, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x54, 0x49, 0x4c, 0x41, 0x4c, 0x5f,
	0x4f, 0x53, 0x57, 0x41, 0x4c, 0x10, 0x1d, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x49, 0x46, 0x4c, 0x10,
	0x1e, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x41, 0x48, 0x49, 0x4e, 0x44, 0x52, 0x41, 0x10, 0x1f, 0x12,
	0x16, 0x0a, 0x12, 0x46, 0x52, 0x41, 0x4e, 0x4b, 0x4c, 0x49, 0x4e, 0x5f, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x4f, 0x4e, 0x10, 0x20, 0x12, 0x0b, 0x0a, 0x07, 0x51, 0x55, 0x41, 0x4e, 0x54,
	0x55, 0x4d, 0x10, 0x21, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41,
	0x4c, 0x10, 0x22, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x50, 0x46, 0x41, 0x53, 0x10, 0x23, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x41, 0x55, 0x52, 0x55, 0x53, 0x10, 0x24, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e,
	0x44, 0x49, 0x41, 0x42, 0x55, 0x4c, 0x4c, 0x53, 0x10, 0x25, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x48,
	0x49, 0x54, 0x45, 0x4f, 0x41, 0x4b, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x10, 0x26,
	0x12, 0x0b, 0x0a, 0x07, 0x53, 0x48, 0x52, 0x49, 0x52, 0x41, 0x4d, 0x10, 0x27, 0x12, 0x08, 0x0a,
	0x04, 0x4e, 0x41, 0x56, 0x49, 0x10, 0x28, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x54, 0x49, 0x10, 0x29,
	0x12, 0x09, 0x0a, 0x05, 0x54, 0x52, 0x55, 0x53, 0x54, 0x10, 0x2a, 0x12, 0x06, 0x0a, 0x02, 0x4e,
	0x4a, 0x10, 0x2b, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x41, 0x4d, 0x43, 0x4f, 0x10, 0x2c, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x41, 0x48, 0x41, 0x52, 0x41, 0x10, 0x2d, 0x12, 0x11, 0x0a, 0x0d, 0x42, 0x41,
	0x4a, 0x41, 0x4a, 0x5f, 0x46, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x56, 0x10, 0x2e, 0x12, 0x0a, 0x0a,
	0x06, 0x48, 0x45, 0x4c, 0x49, 0x4f, 0x53, 0x10, 0x2f, 0x12, 0x0b, 0x0a, 0x07, 0x5a, 0x45, 0x52,
	0x4f, 0x44, 0x48, 0x41, 0x10, 0x30, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x4c, 0x44, 0x5f, 0x42, 0x52,
	0x49, 0x44, 0x47, 0x45, 0x10, 0x31, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x10,
	0x32, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4e, 0x47, 0x45, 0x4c, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x33,
	0x12, 0x11, 0x0a, 0x0d, 0x4a, 0x49, 0x4f, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x52, 0x4f, 0x43,
	0x4b, 0x10, 0x34, 0x2a, 0x4e, 0x0a, 0x0f, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x41, 0x56,
	0x49, 0x4e, 0x47, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e,
	0x54, 0x10, 0x02, 0x2a, 0x3e, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x15, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x49,
	0x52, 0x45, 0x43, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x47, 0x55, 0x4c, 0x41,
	0x52, 0x10, 0x02, 0x2a, 0x43, 0x0a, 0x0a, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x47, 0x52, 0x4f, 0x57, 0x54, 0x48, 0x10, 0x02, 0x2a, 0x98, 0x01, 0x0a, 0x1e, 0x44, 0x69, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x44,
	0x49, 0x56, 0x5f, 0x52, 0x45, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x4f,
	0x4e, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x59, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x50,
	0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x49, 0x4e, 0x56, 0x45,
	0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4f, 0x4e, 0x55,
	0x53, 0x10, 0x04, 0x2a, 0xd3, 0x01, 0x0a, 0x0a, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x44, 0x45, 0x42, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x51, 0x55,
	0x49, 0x54, 0x59, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x53, 0x48, 0x10, 0x03, 0x12,
	0x0a, 0x0a, 0x06, 0x48, 0x59, 0x42, 0x52, 0x49, 0x44, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x42,
	0x4f, 0x4e, 0x44, 0x53, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f,
	0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x49, 0x45, 0x53, 0x10, 0x07, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x08, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x55, 0x54, 0x55, 0x52, 0x45, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x10, 0x0b, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x45, 0x49, 0x54, 0x10, 0x0c, 0x12, 0x0a, 0x0a,
	0x06, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x0d, 0x2a, 0x40, 0x0a, 0x0e, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x45,
	0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x02, 0x2a, 0x88, 0x01, 0x0a, 0x15,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x4c, 0x55, 0x4d, 0x50, 0x53, 0x55, 0x4d, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03,
	0x53, 0x49, 0x50, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x57, 0x50, 0x10, 0x04, 0x12, 0x07,
	0x0a, 0x03, 0x53, 0x54, 0x50, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x57, 0x49, 0x54, 0x43,
	0x48, 0x5f, 0x49, 0x4e, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x4f, 0x55, 0x54, 0x10, 0x07, 0x2a, 0x8f, 0x01, 0x0a, 0x0c, 0x41, 0x69, 0x70, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x69, 0x70, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x4e,
	0x55, 0x41, 0x4c, 0x4c, 0x59, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x51, 0x55, 0x41, 0x52, 0x54,
	0x45, 0x52, 0x4c, 0x59, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x49, 0x5f, 0x57, 0x45, 0x45,
	0x4b, 0x4c, 0x59, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x41, 0x4e,
	0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x10, 0x07, 0x2a, 0xd6, 0x07, 0x0a, 0x13, 0x4d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x26, 0x0a, 0x22, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4d, 0x43, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02,
	0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12,
	0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x49, 0x56, 0x5f, 0x52, 0x45, 0x49,
	0x4e, 0x56, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x06, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x41, 0x56,
	0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x49, 0x50, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57,
	0x45, 0x44, 0x10, 0x09, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x57, 0x50, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x57, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x50, 0x5f, 0x41, 0x4c, 0x4c,
	0x4f, 0x57, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x53, 0x49, 0x4e, 0x5f, 0x4e,
	0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x58, 0x4e, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x54, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0e,
	0x12, 0x1a, 0x0a, 0x16, 0x46, 0x55, 0x4e, 0x44, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x44, 0x45,
	0x46, 0x49, 0x4e, 0x45, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x0f, 0x12, 0x14, 0x0a, 0x10,
	0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0x10, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x10, 0x11, 0x12,
	0x0c, 0x0a, 0x08, 0x46, 0x49, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x12, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x13, 0x12, 0x1c, 0x0a,
	0x18, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x41, 0x4d, 0x45, 0x4e, 0x54, 0x41,
	0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x14, 0x12, 0x17, 0x0a, 0x13, 0x50,
	0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49,
	0x43, 0x53, 0x10, 0x15, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x45, 0x4e, 0x43, 0x48, 0x4d, 0x41, 0x52,
	0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x16, 0x12, 0x18, 0x0a, 0x14, 0x41,
	0x56, 0x47, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x5f, 0x59,
	0x45, 0x41, 0x52, 0x10, 0x17, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54,
	0x5f, 0x41, 0x55, 0x4d, 0x10, 0x18, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e,
	0x54, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10,
	0x19, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x1a, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x10, 0x1b, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x50, 0x4f, 0x55, 0x50, 0x53, 0x10, 0x1c, 0x12, 0x1f,
	0x0a, 0x1b, 0x4d, 0x49, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x1d, 0x12,
	0x1b, 0x0a, 0x17, 0x4d, 0x49, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x49, 0x4f, 0x53, 0x10, 0x1e, 0x12, 0x18, 0x0a, 0x14,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x1f, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x5f, 0x44, 0x45, 0x46,
	0x49, 0x4e, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x20, 0x12,
	0x25, 0x0a, 0x21, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x21, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x56, 0x47, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x10,
	0x22, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x56, 0x47, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f,
	0x4f, 0x4e, 0x45, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x10, 0x23, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x24, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x45,
	0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x25, 0x12,
	0x16, 0x0a, 0x12, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x53, 0x10, 0x26, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x49, 0x4e, 0x5f, 0x53,
	0x49, 0x50, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x27, 0x12, 0x11, 0x0a, 0x0d, 0x4f,
	0x42, 0x53, 0x4f, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x28, 0x12, 0x0f,
	0x0a, 0x0b, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x29, 0x12,
	0x1b, 0x0a, 0x17, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x53, 0x5f, 0x45, 0x58, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x10, 0x2a, 0x12, 0x13, 0x0a, 0x0f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10,
	0x2b, 0x2a, 0x7e, 0x0a, 0x14, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46,
	0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01,
	0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x49, 0x4e, 0x10,
	0x02, 0x2a, 0x7a, 0x0a, 0x0c, 0x41, 0x6d, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x4d, 0x43, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x50, 0x49, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4d, 0x43, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x4d, 0x43, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x2a, 0x67, 0x0a,
	0x18, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0xe9, 0x0c, 0x0a, 0x16, 0x4d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x47, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x49, 0x56, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x44,
	0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x48, 0x49, 0x4c, 0x44, 0x52, 0x45, 0x4e, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x52, 0x56, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x59, 0x4e, 0x41,
	0x4d, 0x49, 0x43, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59,
	0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x4f, 0x46, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x07, 0x12, 0x1a, 0x0a,
	0x16, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x4c, 0x4c,
	0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x54,
	0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x52, 0x42,
	0x49, 0x54, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x0a, 0x12, 0x1a, 0x0a,
	0x16, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x4f, 0x55, 0x53,
	0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x53, 0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x41, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x44, 0x5f, 0x59, 0x49, 0x45, 0x4c, 0x44, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4c, 0x53,
	0x53, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x10, 0x0e, 0x12, 0x16,
	0x0a, 0x12, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59,
	0x5f, 0x45, 0x53, 0x47, 0x10, 0x10, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59,
	0x5f, 0x49, 0x4e, 0x46, 0x52, 0x41, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x10,
	0x11, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x10, 0x12, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x4c, 0x45, 0x58, 0x49, 0x5f, 0x43, 0x41, 0x50,
	0x10, 0x13, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x4f, 0x43, 0x55, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x10, 0x14, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x15, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x16, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x41, 0x52, 0x47, 0x45,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4d, 0x49, 0x44, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x17, 0x12, 0x0d,
	0x0a, 0x09, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x18, 0x12, 0x0b, 0x0a,
	0x07, 0x4d, 0x49, 0x44, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x19, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x55,
	0x4c, 0x54, 0x49, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x1a, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x43,
	0x54, 0x4f, 0x52, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x1b, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x46, 0x4d, 0x43, 0x47, 0x10, 0x1c, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x43,
	0x54, 0x4f, 0x52, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x43, 0x41, 0x52, 0x45, 0x10, 0x1d,
	0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x45, 0x43, 0x48, 0x4e,
	0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x10, 0x1e, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x4d, 0x41, 0x4c, 0x4c,
	0x5f, 0x43, 0x41, 0x50, 0x10, 0x1f, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45, 0x4e, 0x5f, 0x59, 0x52,
	0x5f, 0x47, 0x4f, 0x56, 0x45, 0x52, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x4e, 0x44,
	0x10, 0x20, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x50, 0x53, 0x55, 0x10, 0x21, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x52, 0x50, 0x4f,
	0x52, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x10, 0x22, 0x12, 0x0f, 0x0a, 0x0b, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x23, 0x12, 0x10, 0x0a, 0x0c,
	0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x10, 0x24, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10,
	0x25, 0x12, 0x13, 0x0a, 0x0f, 0x47, 0x4f, 0x56, 0x45, 0x52, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x42, 0x4f, 0x4e, 0x44, 0x10, 0x26, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x4f, 0x4e, 0x47, 0x5f, 0x44,
	0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x27, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x4f, 0x57,
	0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x28, 0x12, 0x13, 0x0a, 0x0f, 0x4d,
	0x45, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x29,
	0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x54, 0x4f, 0x5f, 0x4c, 0x4f,
	0x4e, 0x47, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x2a, 0x12, 0x10, 0x0a,
	0x0c, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x10, 0x2b, 0x12,
	0x0e, 0x0a, 0x0a, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x10, 0x2c, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x2d, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4c, 0x54, 0x52, 0x41, 0x5f, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x2e, 0x12, 0x0a, 0x0a,
	0x06, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x10, 0x2f, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x56, 0x45,
	0x52, 0x4e, 0x49, 0x47, 0x48, 0x54, 0x10, 0x30, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x41, 0x4c, 0x55,
	0x45, 0x10, 0x31, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x53, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x32, 0x12, 0x17,
	0x0a, 0x13, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4d, 0x49,
	0x44, 0x5f, 0x43, 0x41, 0x50, 0x10, 0x33, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x44, 0x45, 0x58,
	0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x41, 0x50,
	0x10, 0x34, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x35, 0x12, 0x1c, 0x0a, 0x18, 0x49,
	0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x36, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x44,
	0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50,
	0x5f, 0x35, 0x30, 0x30, 0x10, 0x37, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4e, 0x41, 0x53, 0x44, 0x41, 0x51, 0x10, 0x38, 0x12, 0x1a,
	0x0a, 0x16, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x53, 0x4d,
	0x41, 0x52, 0x54, 0x5f, 0x42, 0x45, 0x54, 0x41, 0x10, 0x39, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e,
	0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x55, 0x53, 0x5f, 0x54, 0x4f, 0x54,
	0x41, 0x4c, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x10, 0x3a, 0x12, 0x1c, 0x0a, 0x18, 0x49,
	0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c,
	0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x10, 0x3b, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x44,
	0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x43, 0x41, 0x52, 0x45, 0x10, 0x3c, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x4d, 0x49, 0x44, 0x10, 0x3d, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x53, 0x5f, 0x41, 0x53, 0x49, 0x41, 0x5f, 0x54, 0x45, 0x43, 0x48, 0x10, 0x3e,
	0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f,
	0x4e, 0x59, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x41, 0x4e, 0x47, 0x10, 0x3f, 0x12, 0x0e, 0x0a, 0x0a,
	0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x40, 0x12, 0x10, 0x0a, 0x0c,
	0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x41, 0x12, 0x1c,
	0x0a, 0x18, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x46, 0x49,
	0x58, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x42, 0x12, 0x29, 0x0a, 0x25,
	0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d,
	0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x10, 0x43, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x49, 0x58, 0x45, 0x44,
	0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f,
	0x54, 0x45, 0x52, 0x4d, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x10, 0x44, 0x12, 0x22, 0x0a, 0x1e, 0x46,
	0x49, 0x58, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4c,
	0x54, 0x52, 0x41, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x10, 0x45, 0x12,
	0x11, 0x0a, 0x0d, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59,
	0x10, 0x46, 0x2a, 0xbe, 0x01, 0x0a, 0x19, 0x46, 0x75, 0x6e, 0x64, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x29, 0x0a, 0x25, 0x46, 0x75, 0x6e, 0x64, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x56,
	0x45, 0x52, 0x59, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x01, 0x12,
	0x18, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x48, 0x49,
	0x47, 0x48, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x44,
	0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14,
	0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f,
	0x52, 0x49, 0x53, 0x4b, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x49,
	0x53, 0x4b, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x52, 0x49, 0x53,
	0x4b, 0x10, 0x06, 0x2a, 0x5c, 0x0a, 0x0f, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x53, 0x65, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x55, 0x47, 0x47, 0x45, 0x54,
	0x5f, 0x53, 0x45, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x55, 0x54,
	0x52, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x45, 0x47, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x03, 0x2a, 0xb8, 0x02, 0x0a, 0x12, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x45, 0x71, 0x75, 0x69,
	0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x1e, 0x47, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f,
	0x4e, 0x45, 0x54, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x5f,
	0x4e, 0x45, 0x54, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x45,
	0x52, 0x5f, 0x43, 0x59, 0x43, 0x4c, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x03,
	0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46,
	0x45, 0x4e, 0x53, 0x49, 0x56, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a,
	0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16,
	0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x53, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x06, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x43, 0x41, 0x52, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f,
	0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x4e, 0x45, 0x54, 0x10,
	0x08, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x41, 0x4c, 0x45, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x4e, 0x45, 0x54, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x45, 0x43, 0x48, 0x4e, 0x4f, 0x4c,
	0x4f, 0x47, 0x59, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x54, 0x49,
	0x4c, 0x49, 0x54, 0x49, 0x45, 0x53, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0b, 0x2a, 0xb1, 0x07, 0x0a,
	0x10, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x62, 0x6f, 0x6e, 0x64, 0x53, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x62, 0x6f, 0x6e, 0x64, 0x53,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53,
	0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x4d, 0x4f, 0x52,
	0x54, 0x47, 0x41, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x4e, 0x45, 0x54,
	0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x45,
	0x44, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x52, 0x49, 0x4d, 0x41,
	0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x52, 0x49,
	0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x53, 0x48,
	0x5f, 0x45, 0x51, 0x55, 0x49, 0x56, 0x41, 0x4c, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x4e, 0x45, 0x54,
	0x10, 0x04, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x52, 0x43, 0x49, 0x41, 0x4c, 0x5f,
	0x4d, 0x4f, 0x52, 0x54, 0x47, 0x41, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x45, 0x44, 0x5f,
	0x4e, 0x45, 0x54, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59,
	0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x54, 0x49,
	0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x06, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x49,
	0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x52, 0x50,
	0x4f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x07,
	0x12, 0x23, 0x0a, 0x1f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x5f,
	0x4e, 0x45, 0x54, 0x10, 0x08, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59,
	0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x46, 0x55, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x09, 0x12, 0x21, 0x0a, 0x1d,
	0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x47,
	0x4f, 0x56, 0x45, 0x52, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0a, 0x12,
	0x29, 0x0a, 0x25, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f,
	0x52, 0x5f, 0x47, 0x4f, 0x56, 0x45, 0x52, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4c,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0b, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x52,
	0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4d, 0x55, 0x4e,
	0x49, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x45, 0x58, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0c, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x52, 0x49, 0x4d, 0x41,
	0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x49,
	0x50, 0x41, 0x4c, 0x5f, 0x54, 0x41, 0x58, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10,
	0x0d, 0x12, 0x36, 0x0a, 0x32, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43,
	0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x52,
	0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4d, 0x4f, 0x52, 0x54, 0x47,
	0x41, 0x47, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0e, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x49,
	0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x57, 0x41, 0x52, 0x52, 0x41, 0x4e, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x0f,
	0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x10, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x49, 0x4d,
	0x41, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x53, 0x57, 0x41, 0x50, 0x5f,
	0x4e, 0x45, 0x54, 0x10, 0x11, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x53,
	0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x56,
	0x41, 0x4c, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x12, 0x12, 0x1e, 0x0a, 0x1a,
	0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x52,
	0x50, 0x4f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x13, 0x12, 0x1f, 0x0a, 0x1b,
	0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x52,
	0x49, 0x56, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x14, 0x12, 0x1f, 0x0a,
	0x1b, 0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x47, 0x4f,
	0x56, 0x45, 0x52, 0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x15, 0x12, 0x1e,
	0x0a, 0x1a, 0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4d,
	0x55, 0x4e, 0x49, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x16, 0x12, 0x20,
	0x0a, 0x1c, 0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x17,
	0x2a, 0x63, 0x0a, 0x17, 0x44, 0x65, 0x66, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x72, 0x65,
	0x61, 0x6b, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x27, 0x0a, 0x23, 0x44,
	0x65, 0x66, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x59, 0x45, 0x41, 0x52, 0x53, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x44,
	0x41, 0x59, 0x53, 0x10, 0x03, 0x2a, 0x3e, 0x0a, 0x0d, 0x44, 0x65, 0x66, 0x65, 0x72, 0x4c, 0x6f,
	0x61, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x65, 0x66, 0x65, 0x72, 0x4c,
	0x6f, 0x61, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x41, 0x47, 0x45, 0x10, 0x01, 0x2a, 0xeb, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x41, 0x76, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x22,
	0x0a, 0x1e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x76, 0x67, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x56, 0x47, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x10,
	0x03, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x58, 0x50, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x48, 0x41, 0x52,
	0x50, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e,
	0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10,
	0x07, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x4c, 0x50, 0x48, 0x41, 0x10, 0x08, 0x12, 0x07, 0x0a, 0x03,
	0x41, 0x55, 0x4d, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x47,
	0x45, 0x10, 0x0a, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x54, 0x5f, 0x41, 0x56, 0x47, 0x5f, 0x48,
	0x49, 0x53, 0x54, 0x4f, 0x52, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e,
	0x53, 0x10, 0x0b, 0x2a, 0x8c, 0x01, 0x0a, 0x13, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75,
	0x6e, 0x64, 0x4e, 0x61, 0x76, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x4d,
	0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c,
	0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x48, 0x4f, 0x4c, 0x49, 0x44, 0x41, 0x59, 0x5f, 0x44, 0x45, 0x52, 0x49, 0x56, 0x45, 0x44,
	0x10, 0x02, 0x2a, 0x65, 0x0a, 0x13, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64,
	0x4e, 0x61, 0x76, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4d, 0x4f, 0x52, 0x4e,
	0x49, 0x4e, 0x47, 0x53, 0x54, 0x41, 0x52, 0x10, 0x01, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x5a, 0x30, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_mutualfund_mutual_fund_proto_rawDescOnce sync.Once
	file_api_investment_mutualfund_mutual_fund_proto_rawDescData = file_api_investment_mutualfund_mutual_fund_proto_rawDesc
)

func file_api_investment_mutualfund_mutual_fund_proto_rawDescGZIP() []byte {
	file_api_investment_mutualfund_mutual_fund_proto_rawDescOnce.Do(func() {
		file_api_investment_mutualfund_mutual_fund_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_mutualfund_mutual_fund_proto_rawDescData)
	})
	return file_api_investment_mutualfund_mutual_fund_proto_rawDescData
}

var file_api_investment_mutualfund_mutual_fund_proto_enumTypes = make([]protoimpl.EnumInfo, 24)
var file_api_investment_mutualfund_mutual_fund_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_api_investment_mutualfund_mutual_fund_proto_goTypes = []interface{}{
	(FundInvestmentStatus)(0),              // 0: api.investment.mutualfund.FundInvestmentStatus
	(Amc)(0),                               // 1: api.investment.mutualfund.Amc
	(BankAccountType)(0),                   // 2: api.investment.mutualfund.BankAccountType
	(PlanType)(0),                          // 3: api.investment.mutualfund.PlanType
	(OptionType)(0),                        // 4: api.investment.mutualfund.OptionType
	(DividendReinvestmentOptionType)(0),    // 5: api.investment.mutualfund.DividendReinvestmentOptionType
	(AssetClass)(0),                        // 6: api.investment.mutualfund.AssetClass
	(InvestmentType)(0),                    // 7: api.investment.mutualfund.InvestmentType
	(SchemeTransactionType)(0),             // 8: api.investment.mutualfund.SchemeTransactionType
	(AipFrequency)(0),                      // 9: api.investment.mutualfund.AipFrequency
	(MutualFundFieldMask)(0),               // 10: api.investment.mutualfund.MutualFundFieldMask
	(MutualFundIdentifier)(0),              // 11: api.investment.mutualfund.MutualFundIdentifier
	(AmcFieldMask)(0),                      // 12: api.investment.mutualfund.AmcFieldMask
	(MutualFundInternalStatus)(0),          // 13: api.investment.mutualfund.MutualFundInternalStatus
	(MutualFundCategoryName)(0),            // 14: api.investment.mutualfund.MutualFundCategoryName
	(FundhouseDefinedRiskLevel)(0),         // 15: api.investment.mutualfund.FundhouseDefinedRiskLevel
	(NuggetSentiment)(0),                   // 16: api.investment.mutualfund.NuggetSentiment
	(GlobalEquitySector)(0),                // 17: api.investment.mutualfund.GlobalEquitySector
	(GlobalbondSector)(0),                  // 18: api.investment.mutualfund.GlobalbondSector
	(DeferLoadBreakPointUnit)(0),           // 19: api.investment.mutualfund.DeferLoadBreakPointUnit
	(DeferLoadUnit)(0),                     // 20: api.investment.mutualfund.DeferLoadUnit
	(CategoryAvgFieldMask)(0),              // 21: api.investment.mutualfund.CategoryAvgFieldMask
	(MutualFundNavSource)(0),               // 22: api.investment.mutualfund.MutualFundNavSource
	(MutualFundNavVendor)(0),               // 23: api.investment.mutualfund.MutualFundNavVendor
	(*MutualFund)(nil),                     // 24: api.investment.mutualfund.MutualFund
	(*FieldsExemptFromSync)(nil),           // 25: api.investment.mutualfund.FieldsExemptFromSync
	(*HistoricalReturns)(nil),              // 26: api.investment.mutualfund.HistoricalReturns
	(*ReturnData)(nil),                     // 27: api.investment.mutualfund.ReturnData
	(*AmcInfo)(nil),                        // 28: api.investment.mutualfund.AmcInfo
	(*BankAccountDetails)(nil),             // 29: api.investment.mutualfund.BankAccountDetails
	(*FundNameMetadata)(nil),               // 30: api.investment.mutualfund.FundNameMetadata
	(*TransactionConstraints)(nil),         // 31: api.investment.mutualfund.TransactionConstraints
	(*VendorMetadata)(nil),                 // 32: api.investment.mutualfund.VendorMetadata
	(*MorningstarData)(nil),                // 33: api.investment.mutualfund.MorningstarData
	(*Returns)(nil),                        // 34: api.investment.mutualfund.Returns
	(*TrackingError)(nil),                  // 35: api.investment.mutualfund.TrackingError
	(*PerformanceMetrics)(nil),             // 36: api.investment.mutualfund.PerformanceMetrics
	(*SharpeRatio)(nil),                    // 37: api.investment.mutualfund.SharpeRatio
	(*BattingAverage)(nil),                 // 38: api.investment.mutualfund.BattingAverage
	(*UpsideCaptureRatio)(nil),             // 39: api.investment.mutualfund.UpsideCaptureRatio
	(*DownsideCaptureRatio)(nil),           // 40: api.investment.mutualfund.DownsideCaptureRatio
	(*InformationRatio)(nil),               // 41: api.investment.mutualfund.InformationRatio
	(*Alpha)(nil),                          // 42: api.investment.mutualfund.Alpha
	(*BenchmarkDetails)(nil),               // 43: api.investment.mutualfund.BenchmarkDetails
	(*FundFundamentalDetails)(nil),         // 44: api.investment.mutualfund.FundFundamentalDetails
	(*ExpenseRatio)(nil),                   // 45: api.investment.mutualfund.ExpenseRatio
	(*Aum)(nil),                            // 46: api.investment.mutualfund.Aum
	(*Holdings)(nil),                       // 47: api.investment.mutualfund.Holdings
	(*HoldingDetail)(nil),                  // 48: api.investment.mutualfund.HoldingDetail
	(*FiContent)(nil),                      // 49: api.investment.mutualfund.FiContent
	(*Nugget)(nil),                         // 50: api.investment.mutualfund.Nugget
	(*AssetAllocationBreakdown)(nil),       // 51: api.investment.mutualfund.AssetAllocationBreakdown
	(*EquityCountryExposure)(nil),          // 52: api.investment.mutualfund.EquityCountryExposure
	(*EquityCountryExposureBreakdown)(nil), // 53: api.investment.mutualfund.EquityCountryExposureBreakdown
	(*CreditQualityBreakdown)(nil),         // 54: api.investment.mutualfund.CreditQualityBreakdown
	(*MaturityBreakdown)(nil),              // 55: api.investment.mutualfund.MaturityBreakdown
	(*MarketCap)(nil),                      // 56: api.investment.mutualfund.MarketCap
	(*GlobalEquitySectors)(nil),            // 57: api.investment.mutualfund.GlobalEquitySectors
	(*GlobalBondSectors)(nil),              // 58: api.investment.mutualfund.GlobalBondSectors
	(*FundManagers)(nil),                   // 59: api.investment.mutualfund.FundManagers
	(*FundManager)(nil),                    // 60: api.investment.mutualfund.FundManager
	(*SipMetadata)(nil),                    // 61: api.investment.mutualfund.SipMetadata
	(*StpMetadata)(nil),                    // 62: api.investment.mutualfund.StpMetadata
	(*SwpMetadata)(nil),                    // 63: api.investment.mutualfund.SwpMetadata
	(*AipDetail)(nil),                      // 64: api.investment.mutualfund.AipDetail
	(*ExitLoad)(nil),                       // 65: api.investment.mutualfund.ExitLoad
	(*DeferLoad)(nil),                      // 66: api.investment.mutualfund.DeferLoad
	(*MutualFundCategoryAverage)(nil),      // 67: api.investment.mutualfund.MutualFundCategoryAverage
	(*VersionSupportInfo)(nil),             // 68: api.investment.mutualfund.VersionSupportInfo
	(*FundLockInPeriod)(nil),               // 69: api.investment.mutualfund.FundLockInPeriod
	(*MutualFundNavHistory)(nil),           // 70: api.investment.mutualfund.MutualFundNavHistory
	nil,                                    // 71: api.investment.mutualfund.Holdings.HoldingsEntry
	nil,                                    // 72: api.investment.mutualfund.GlobalEquitySectors.EquitySectorsEntry
	nil,                                    // 73: api.investment.mutualfund.GlobalBondSectors.BondSectorsEntry
	nil,                                    // 74: api.investment.mutualfund.SipMetadata.SiDetailsEntry
	nil,                                    // 75: api.investment.mutualfund.StpMetadata.SiDetailsEntry
	nil,                                    // 76: api.investment.mutualfund.SwpMetadata.SiDetailsEntry
	(*money.Money)(nil),                    // 77: google.type.Money
	(*timestamppb.Timestamp)(nil),          // 78: google.protobuf.Timestamp
	(common.UserGroup)(0),                  // 79: api.typesv2.common.UserGroup
	(vendorgateway.Vendor)(0),              // 80: vendorgateway.Vendor
	(*date.Date)(nil),                      // 81: google.type.Date
	(dayofweek.DayOfWeek)(0),               // 82: google.type.DayOfWeek
}
var file_api_investment_mutualfund_mutual_fund_proto_depIdxs = []int32{
	1,   // 0: api.investment.mutualfund.MutualFund.amc:type_name -> api.investment.mutualfund.Amc
	30,  // 1: api.investment.mutualfund.MutualFund.name_data:type_name -> api.investment.mutualfund.FundNameMetadata
	3,   // 2: api.investment.mutualfund.MutualFund.plan_type:type_name -> api.investment.mutualfund.PlanType
	7,   // 3: api.investment.mutualfund.MutualFund.investment_type:type_name -> api.investment.mutualfund.InvestmentType
	4,   // 4: api.investment.mutualfund.MutualFund.option_type:type_name -> api.investment.mutualfund.OptionType
	5,   // 5: api.investment.mutualfund.MutualFund.div_reinv_option_type:type_name -> api.investment.mutualfund.DividendReinvestmentOptionType
	77,  // 6: api.investment.mutualfund.MutualFund.nav:type_name -> google.type.Money
	6,   // 7: api.investment.mutualfund.MutualFund.asset_class:type_name -> api.investment.mutualfund.AssetClass
	31,  // 8: api.investment.mutualfund.MutualFund.txn_constraints:type_name -> api.investment.mutualfund.TransactionConstraints
	78,  // 9: api.investment.mutualfund.MutualFund.created_at:type_name -> google.protobuf.Timestamp
	78,  // 10: api.investment.mutualfund.MutualFund.updated_at:type_name -> google.protobuf.Timestamp
	78,  // 11: api.investment.mutualfund.MutualFund.deleted_at:type_name -> google.protobuf.Timestamp
	13,  // 12: api.investment.mutualfund.MutualFund.status:type_name -> api.investment.mutualfund.MutualFundInternalStatus
	32,  // 13: api.investment.mutualfund.MutualFund.vendor_metadata:type_name -> api.investment.mutualfund.VendorMetadata
	14,  // 14: api.investment.mutualfund.MutualFund.category_name:type_name -> api.investment.mutualfund.MutualFundCategoryName
	15,  // 15: api.investment.mutualfund.MutualFund.fundhouse_defined_risk_level:type_name -> api.investment.mutualfund.FundhouseDefinedRiskLevel
	34,  // 16: api.investment.mutualfund.MutualFund.returns:type_name -> api.investment.mutualfund.Returns
	43,  // 17: api.investment.mutualfund.MutualFund.benchmark_details:type_name -> api.investment.mutualfund.BenchmarkDetails
	36,  // 18: api.investment.mutualfund.MutualFund.performance_metrics:type_name -> api.investment.mutualfund.PerformanceMetrics
	44,  // 19: api.investment.mutualfund.MutualFund.fund_fundamental_details:type_name -> api.investment.mutualfund.FundFundamentalDetails
	49,  // 20: api.investment.mutualfund.MutualFund.fi_content:type_name -> api.investment.mutualfund.FiContent
	79,  // 21: api.investment.mutualfund.MutualFund.allowed_user_groups:type_name -> api.typesv2.common.UserGroup
	68,  // 22: api.investment.mutualfund.MutualFund.version_support_info:type_name -> api.investment.mutualfund.VersionSupportInfo
	14,  // 23: api.investment.mutualfund.MutualFund.fi_defined_category:type_name -> api.investment.mutualfund.MutualFundCategoryName
	0,   // 24: api.investment.mutualfund.MutualFund.fund_investment_status:type_name -> api.investment.mutualfund.FundInvestmentStatus
	26,  // 25: api.investment.mutualfund.MutualFund.historical_returns:type_name -> api.investment.mutualfund.HistoricalReturns
	78,  // 26: api.investment.mutualfund.MutualFund.obsolete_date:type_name -> google.protobuf.Timestamp
	25,  // 27: api.investment.mutualfund.MutualFund.fields_exempt_from_sync:type_name -> api.investment.mutualfund.FieldsExemptFromSync
	10,  // 28: api.investment.mutualfund.FieldsExemptFromSync.field_masks:type_name -> api.investment.mutualfund.MutualFundFieldMask
	27,  // 29: api.investment.mutualfund.HistoricalReturns.monthly_returns:type_name -> api.investment.mutualfund.ReturnData
	78,  // 30: api.investment.mutualfund.ReturnData.return_date:type_name -> google.protobuf.Timestamp
	1,   // 31: api.investment.mutualfund.AmcInfo.amc:type_name -> api.investment.mutualfund.Amc
	29,  // 32: api.investment.mutualfund.AmcInfo.credit_account:type_name -> api.investment.mutualfund.BankAccountDetails
	80,  // 33: api.investment.mutualfund.AmcInfo.rta:type_name -> vendorgateway.Vendor
	78,  // 34: api.investment.mutualfund.AmcInfo.created_at:type_name -> google.protobuf.Timestamp
	78,  // 35: api.investment.mutualfund.AmcInfo.updated_at:type_name -> google.protobuf.Timestamp
	78,  // 36: api.investment.mutualfund.AmcInfo.deleted_at:type_name -> google.protobuf.Timestamp
	2,   // 37: api.investment.mutualfund.BankAccountDetails.account_type:type_name -> api.investment.mutualfund.BankAccountType
	77,  // 38: api.investment.mutualfund.TransactionConstraints.newp_mnval:type_name -> google.type.Money
	77,  // 39: api.investment.mutualfund.TransactionConstraints.newp_mxval:type_name -> google.type.Money
	77,  // 40: api.investment.mutualfund.TransactionConstraints.adp_mn_amt:type_name -> google.type.Money
	77,  // 41: api.investment.mutualfund.TransactionConstraints.adp_mx_amt:type_name -> google.type.Money
	77,  // 42: api.investment.mutualfund.TransactionConstraints.p_mn_incr:type_name -> google.type.Money
	77,  // 43: api.investment.mutualfund.TransactionConstraints.adp_mn_incr:type_name -> google.type.Money
	77,  // 44: api.investment.mutualfund.TransactionConstraints.red_mn_amt:type_name -> google.type.Money
	77,  // 45: api.investment.mutualfund.TransactionConstraints.red_mx_amt:type_name -> google.type.Money
	77,  // 46: api.investment.mutualfund.TransactionConstraints.red_incr:type_name -> google.type.Money
	61,  // 47: api.investment.mutualfund.TransactionConstraints.sip_metadata:type_name -> api.investment.mutualfund.SipMetadata
	62,  // 48: api.investment.mutualfund.TransactionConstraints.stp_metadata:type_name -> api.investment.mutualfund.StpMetadata
	63,  // 49: api.investment.mutualfund.TransactionConstraints.swp_metadata:type_name -> api.investment.mutualfund.SwpMetadata
	55,  // 50: api.investment.mutualfund.TransactionConstraints.maturity_breakdown:type_name -> api.investment.mutualfund.MaturityBreakdown
	69,  // 51: api.investment.mutualfund.TransactionConstraints.fund_lockin_period:type_name -> api.investment.mutualfund.FundLockInPeriod
	9,   // 52: api.investment.mutualfund.TransactionConstraints.allowed_sip_frequencies:type_name -> api.investment.mutualfund.AipFrequency
	33,  // 53: api.investment.mutualfund.VendorMetadata.morningstar_data:type_name -> api.investment.mutualfund.MorningstarData
	35,  // 54: api.investment.mutualfund.PerformanceMetrics.tracking_error:type_name -> api.investment.mutualfund.TrackingError
	42,  // 55: api.investment.mutualfund.PerformanceMetrics.alpha:type_name -> api.investment.mutualfund.Alpha
	41,  // 56: api.investment.mutualfund.PerformanceMetrics.information_ratio:type_name -> api.investment.mutualfund.InformationRatio
	37,  // 57: api.investment.mutualfund.PerformanceMetrics.sharpe_ratio:type_name -> api.investment.mutualfund.SharpeRatio
	38,  // 58: api.investment.mutualfund.PerformanceMetrics.batting_average:type_name -> api.investment.mutualfund.BattingAverage
	39,  // 59: api.investment.mutualfund.PerformanceMetrics.upside_capture_ratio:type_name -> api.investment.mutualfund.UpsideCaptureRatio
	40,  // 60: api.investment.mutualfund.PerformanceMetrics.downside_capture_ratio:type_name -> api.investment.mutualfund.DownsideCaptureRatio
	46,  // 61: api.investment.mutualfund.FundFundamentalDetails.aum:type_name -> api.investment.mutualfund.Aum
	51,  // 62: api.investment.mutualfund.FundFundamentalDetails.asset_allocation_breakdown:type_name -> api.investment.mutualfund.AssetAllocationBreakdown
	52,  // 63: api.investment.mutualfund.FundFundamentalDetails.equity_country_exposure:type_name -> api.investment.mutualfund.EquityCountryExposure
	54,  // 64: api.investment.mutualfund.FundFundamentalDetails.credit_quality_breakdown:type_name -> api.investment.mutualfund.CreditQualityBreakdown
	56,  // 65: api.investment.mutualfund.FundFundamentalDetails.market_cap:type_name -> api.investment.mutualfund.MarketCap
	57,  // 66: api.investment.mutualfund.FundFundamentalDetails.global_equity_sectors:type_name -> api.investment.mutualfund.GlobalEquitySectors
	58,  // 67: api.investment.mutualfund.FundFundamentalDetails.global_bond_sectors:type_name -> api.investment.mutualfund.GlobalBondSectors
	45,  // 68: api.investment.mutualfund.FundFundamentalDetails.expense_ratio:type_name -> api.investment.mutualfund.ExpenseRatio
	59,  // 69: api.investment.mutualfund.FundFundamentalDetails.fund_managers:type_name -> api.investment.mutualfund.FundManagers
	65,  // 70: api.investment.mutualfund.FundFundamentalDetails.exit_load:type_name -> api.investment.mutualfund.ExitLoad
	78,  // 71: api.investment.mutualfund.FundFundamentalDetails.inception_date:type_name -> google.protobuf.Timestamp
	47,  // 72: api.investment.mutualfund.FundFundamentalDetails.holdings:type_name -> api.investment.mutualfund.Holdings
	71,  // 73: api.investment.mutualfund.Holdings.holdings:type_name -> api.investment.mutualfund.Holdings.HoldingsEntry
	48,  // 74: api.investment.mutualfund.Holdings.details:type_name -> api.investment.mutualfund.HoldingDetail
	6,   // 75: api.investment.mutualfund.HoldingDetail.asset_class:type_name -> api.investment.mutualfund.AssetClass
	50,  // 76: api.investment.mutualfund.FiContent.nuggets:type_name -> api.investment.mutualfund.Nugget
	16,  // 77: api.investment.mutualfund.Nugget.sentiment:type_name -> api.investment.mutualfund.NuggetSentiment
	53,  // 78: api.investment.mutualfund.EquityCountryExposure.breakdown:type_name -> api.investment.mutualfund.EquityCountryExposureBreakdown
	78,  // 79: api.investment.mutualfund.CreditQualityBreakdown.credit_qual_date:type_name -> google.protobuf.Timestamp
	72,  // 80: api.investment.mutualfund.GlobalEquitySectors.equity_sectors:type_name -> api.investment.mutualfund.GlobalEquitySectors.EquitySectorsEntry
	73,  // 81: api.investment.mutualfund.GlobalBondSectors.bond_sectors:type_name -> api.investment.mutualfund.GlobalBondSectors.BondSectorsEntry
	60,  // 82: api.investment.mutualfund.FundManagers.managers:type_name -> api.investment.mutualfund.FundManager
	78,  // 83: api.investment.mutualfund.FundManager.start_date:type_name -> google.protobuf.Timestamp
	74,  // 84: api.investment.mutualfund.SipMetadata.si_details:type_name -> api.investment.mutualfund.SipMetadata.SiDetailsEntry
	75,  // 85: api.investment.mutualfund.StpMetadata.si_details:type_name -> api.investment.mutualfund.StpMetadata.SiDetailsEntry
	76,  // 86: api.investment.mutualfund.SwpMetadata.si_details:type_name -> api.investment.mutualfund.SwpMetadata.SiDetailsEntry
	9,   // 87: api.investment.mutualfund.AipDetail.aip_frequency:type_name -> api.investment.mutualfund.AipFrequency
	81,  // 88: api.investment.mutualfund.AipDetail.allowed_dates:type_name -> google.type.Date
	82,  // 89: api.investment.mutualfund.AipDetail.allowed_days:type_name -> google.type.DayOfWeek
	66,  // 90: api.investment.mutualfund.ExitLoad.loads:type_name -> api.investment.mutualfund.DeferLoad
	19,  // 91: api.investment.mutualfund.DeferLoad.load_breakpoint_unit:type_name -> api.investment.mutualfund.DeferLoadBreakPointUnit
	20,  // 92: api.investment.mutualfund.DeferLoad.defer_load_unit:type_name -> api.investment.mutualfund.DeferLoadUnit
	14,  // 93: api.investment.mutualfund.MutualFundCategoryAverage.category_name:type_name -> api.investment.mutualfund.MutualFundCategoryName
	34,  // 94: api.investment.mutualfund.MutualFundCategoryAverage.returns:type_name -> api.investment.mutualfund.Returns
	45,  // 95: api.investment.mutualfund.MutualFundCategoryAverage.expense_ratio:type_name -> api.investment.mutualfund.ExpenseRatio
	35,  // 96: api.investment.mutualfund.MutualFundCategoryAverage.tracking_error:type_name -> api.investment.mutualfund.TrackingError
	37,  // 97: api.investment.mutualfund.MutualFundCategoryAverage.sharpe_ratio:type_name -> api.investment.mutualfund.SharpeRatio
	41,  // 98: api.investment.mutualfund.MutualFundCategoryAverage.information_ratio:type_name -> api.investment.mutualfund.InformationRatio
	42,  // 99: api.investment.mutualfund.MutualFundCategoryAverage.alpha:type_name -> api.investment.mutualfund.Alpha
	46,  // 100: api.investment.mutualfund.MutualFundCategoryAverage.aum:type_name -> api.investment.mutualfund.Aum
	78,  // 101: api.investment.mutualfund.MutualFundCategoryAverage.created_at:type_name -> google.protobuf.Timestamp
	78,  // 102: api.investment.mutualfund.MutualFundCategoryAverage.updated_at:type_name -> google.protobuf.Timestamp
	78,  // 103: api.investment.mutualfund.MutualFundCategoryAverage.deleted_at:type_name -> google.protobuf.Timestamp
	26,  // 104: api.investment.mutualfund.MutualFundCategoryAverage.historical_returns:type_name -> api.investment.mutualfund.HistoricalReturns
	78,  // 105: api.investment.mutualfund.MutualFundNavHistory.nav_date:type_name -> google.protobuf.Timestamp
	77,  // 106: api.investment.mutualfund.MutualFundNavHistory.nav:type_name -> google.type.Money
	23,  // 107: api.investment.mutualfund.MutualFundNavHistory.vendor:type_name -> api.investment.mutualfund.MutualFundNavVendor
	22,  // 108: api.investment.mutualfund.MutualFundNavHistory.nav_source:type_name -> api.investment.mutualfund.MutualFundNavSource
	78,  // 109: api.investment.mutualfund.MutualFundNavHistory.nav_derived_date:type_name -> google.protobuf.Timestamp
	78,  // 110: api.investment.mutualfund.MutualFundNavHistory.created_at:type_name -> google.protobuf.Timestamp
	78,  // 111: api.investment.mutualfund.MutualFundNavHistory.updated_at:type_name -> google.protobuf.Timestamp
	78,  // 112: api.investment.mutualfund.MutualFundNavHistory.deleted_at:type_name -> google.protobuf.Timestamp
	64,  // 113: api.investment.mutualfund.SipMetadata.SiDetailsEntry.value:type_name -> api.investment.mutualfund.AipDetail
	64,  // 114: api.investment.mutualfund.StpMetadata.SiDetailsEntry.value:type_name -> api.investment.mutualfund.AipDetail
	64,  // 115: api.investment.mutualfund.SwpMetadata.SiDetailsEntry.value:type_name -> api.investment.mutualfund.AipDetail
	116, // [116:116] is the sub-list for method output_type
	116, // [116:116] is the sub-list for method input_type
	116, // [116:116] is the sub-list for extension type_name
	116, // [116:116] is the sub-list for extension extendee
	0,   // [0:116] is the sub-list for field type_name
}

func init() { file_api_investment_mutualfund_mutual_fund_proto_init() }
func file_api_investment_mutualfund_mutual_fund_proto_init() {
	if File_api_investment_mutualfund_mutual_fund_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutualFund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldsExemptFromSync); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HistoricalReturns); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReturnData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmcInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundNameMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionConstraints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MorningstarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Returns); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackingError); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerformanceMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharpeRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattingAverage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsideCaptureRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownsideCaptureRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InformationRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Alpha); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenchmarkDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundFundamentalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpenseRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Aum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Holdings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HoldingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nugget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetAllocationBreakdown); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquityCountryExposure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquityCountryExposureBreakdown); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditQualityBreakdown); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaturityBreakdown); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketCap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalEquitySectors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalBondSectors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundManagers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundManager); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SipMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StpMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwpMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AipDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitLoad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeferLoad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutualFundCategoryAverage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionSupportInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundLockInPeriod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_mutual_fund_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutualFundNavHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_mutualfund_mutual_fund_proto_rawDesc,
			NumEnums:      24,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_investment_mutualfund_mutual_fund_proto_goTypes,
		DependencyIndexes: file_api_investment_mutualfund_mutual_fund_proto_depIdxs,
		EnumInfos:         file_api_investment_mutualfund_mutual_fund_proto_enumTypes,
		MessageInfos:      file_api_investment_mutualfund_mutual_fund_proto_msgTypes,
	}.Build()
	File_api_investment_mutualfund_mutual_fund_proto = out.File
	file_api_investment_mutualfund_mutual_fund_proto_rawDesc = nil
	file_api_investment_mutualfund_mutual_fund_proto_goTypes = nil
	file_api_investment_mutualfund_mutual_fund_proto_depIdxs = nil
}
