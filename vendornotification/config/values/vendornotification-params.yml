Flags:
  TrimDebugMessageFromStatus: true
  EnableCCTransactionProcessingViaTemporal: false
  EnableNewEndpointInboundNotification: false

EnachTransactionStatusCodeJson: "./mappingJson/enachTransactionStatusCodes.json"

SyncRespHandler:
  CallbackHandler:
    OverallTimeout: "10s"
    PerRetryTimeout: "3s"
    RetryBackOffDur: "100ms"
    Retries: 3

UPIWhitelist:
  EnableWhitelist: false
  SoftBlock: true

FreshchatWhitelist:
  EnableWhitelist: false

SenseforthWhitelist:
  EnableWhitelist: false

TssWhitelist:
  EnableWhitelist: false

SmallcaseWhitelist:
  EnableWhitelist: false

RiskcovryWhitelist:
  EnableWhitelist: false

SprinklrWhitelist:
  EnableWhitelist: false

FiftyfinWhitelist:
  EnableWhitelist: false

MoneyviewWhiteList:
  EnableWhitelist: false

AbflWhitelist:
  EnableWhitelist: false

CredgenicsWhitelist:
  EnableWhitelist: false

AirtelWhitelist:
  EnableWhitelist: false

FederalWhitelist:
  EnableWhitelist: false

SavenWhiteList:
  EnableWhitelist: false

NuggetWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: ""
  SoftBlock: true


# Non prod environments do not have ALB. Only Grpc Gateway header appends the client IP to the header
# Prod environments have an ALB in its path. Both ALB and Grpc Gateway appends the client IPs to the header
NumberOfHopsThatAddXForwardedFor: 1
VpcCidrIPPrefix: "10.50"

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/vendornotification/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

Ozonetel:
  DefaultChannelNumber: 1
  RoutingChannelToNumberMapping:
    ROUTING_CHANNEL_ACCOUNT_HOLDERS: 1
    ROUTING_CHANNEL_CURRENTLY_ONBOARDING: 2
    ROUTING_CHANNEL_UNREGISTERED: 3
    #    ROUTING_CHANNEL_BLOCKED_USER_BALANCE_ABOVE_10K: 4
    #    ROUTING_CHANNEL_BLOCKED_USER_BALANCE_ABOVE_1K: 5
    #    ROUTING_CHANNEL_BLOCKED_USER_BALANCE_ABOVE_100: 6
    #    ROUTING_CHANNEL_BLOCKED_USER_DEFAULT: 7
    #    ROUTING_CHANNEL_BLOCKED_USER_LOW_PRIORITY: 8
    ROUTING_CHANNEL_CKYC_O_FLAG_ACCOUNT_FREEZED_USERS: 5
    ROUTING_CHANNEL_CREDIT_CARD_USERS: 9
    ROUTING_CHANNEL_SALARY_PROGRAM_USERS: 10
    ROUTING_CHANNEL_ACTIVE_LOAN_USERS: 11
  CallTypeMapping:
    PROGRESSIVE: "CALL_TYPE_PROGRESSIVE"
    INBOUND: "CALL_TYPE_INBOUND"
    MANUAL: "CALL_TYPE_MANUAL"
    PREVIEW: "CALL_TYPE_PREVIEW"
    MAIL: "CALL_TYPE_MAIL"
    IVR: "CALL_TYPE_IVR"
    CHAT: "CALL_TYPE_CHAT"
    PREDICTIVE: "CALL_TYPE_PREDICTIVE"
  CallStatusMapping:
    ANSWERED: "CALL_HANDLE_STATUS_ANSWERED"
    NOTANSWERED: "CALL_HANDLE_STATUS_NOT_ANSWERED"
  AgentStatusMapping:
    ANSWERED: "AGENT_CALL_STATUS_ANSWERED"
    NOTANSWERED: "AGENT_CALL_STATUS_NOT_ANSWERED"
    NOTDIALED: "AGENT_CALL_STATUS_NOT_DIALED"
    USERDISCONNECTED: "AGENT_CALL_STATUS_USER_DISCONNECTED"
  CustomerStatusMapping:
    INVALIDNUMBER: "CUSTOMER_STATUS_INVALID_NUMBER"
    CONGESTION: "CUSTOMER_STATUS_CONGESTION"
    ANSWERED: "CUSTOMER_STATUS_ANSWERED"
    NOTANSWERED: "CUSTOMER_STATUS_NOT_ANSWERED"
    NOROUTEDESTINATION: "CUSTOMER_STATUS_NO_ROUTE_DESTINATION"
    DIALING: "CUSTOMER_STATUS_DIALING"
    NORESPONSE: "CUSTOMER_STATUS_NO_RESPONSE"
    ISDDISABLED: "CUSTOMER_STATUS_ISD_DISABLED"
    RING: "CUSTOMER_STATUS_RING"
    EXCEPTION: "CUSTOMER_STATUS_EXCEPTION"
    SUBCRIBERABSENT: "CUSTOMER_STATUS_SUBSCRIBER_ABSENT"
    INVALIDNUMBERFORMAT: "CUSTOMER_STATUS_INVALID_NUMBER_FORMAT"
    BUSY: "CUSTOMER_STATUS_BUSY"
  HangUpByMapping:
    USERHANGUP: "HANG_UP_BY_USER_HANGUP"
    AGENTHANGUP: "HANG_UP_BY_AGENT_HANGUP"
    SYSTEMHANGUP: "HANG_UP_BY_SYSTEM_HANGUP"
  IsPriorityRoutingEnabled: false
  RecordingIdToNumberMapping:
    RECORDING_IDENTIFIER_HIGH_RISK_SCORE: 1
    RECORDING_IDENTIFIER_SCREENER_REJECT: 2
    RECORDING_IDENTIFIER_REDIRECT_TO_ISSUE_REPORTING_FLOW: 3
    RECORDING_IDENTIFIER_CALLBACK_REQUEST: 4
  InternalQuestionIDToOzonetelQuestionID:
    1: # QUESTION_ENTER_REGISTERED_MOBILE_NUMBER
      "LANGUAGE_ENGLISH": 1
    2: # QUESTION_MOBILE_NUMBER_NOT_REGISTERED
      "LANGUAGE_ENGLISH": 2
    3:  # QUESTION_AUTHENTICATE
      "LANGUAGE_ENGLISH": 3
    4: # QUESTION_ENTER_SAVINGS_ACCOUNT_DIGITS
      "LANGUAGE_ENGLISH": 4
    5: # QUESTION_AUTHENTICATION_FAILED
      "LANGUAGE_ENGLISH": 5
    6: # QUESTION_SELECT_LANGUAGE
      "LANGUAGE_ENGLISH": 6
    7: # QUESTION_WRONG_OPTION_SELECTED
      "LANGUAGE_ENGLISH": 9
      "LANGUAGE_HINDI": 10
    8: # QUESTION_EMAIL_ASSISTANCE
      "LANGUAGE_ENGLISH": 7
      "LANGUAGE_HINDI": 8
    9: # QUESTION_ARRANGE_CALLBACK
      "LANGUAGE_ENGLISH": 11
      "LANGUAGE_HINDI": 12
    10: # QUESTION_CALL_ARRANGED
      "LANGUAGE_ENGLISH": 13
      "LANGUAGE_HINDI": 14
    48: # QUESTION_CHECK_AVAILABILITY_OF_VALID_DEBIT_CARD
      "LANGUAGE_ENGLISH": 48
    50: # QUESTION_DEBIT_CARD_BLOCK_REQUEST
      "LANGUAGE_ENGLISH": 50
    52: # QUESTION_CHECK_PRESENCE_OF_CC
      "LANGUAGE_ENGLISH": 52




Tracing:
  Enable: false

AA:
  AaSecretsVersionToUse: "V1"

Chatbot:
  VnToCxEntityMap:
    FAQ: "WORK_FLOW_ENTITY_FAQ"
    USER_DETAILS: "WORKFLOW_ENTITY_USER_DETAILS"
    TXN_LIST: "WORKFLOW_ENTITY_TXN_LIST"
    TXN_DETAILS: "WORKFLOW_ENTITY_TXN_DETAILS"
    DEBIT_CARD_TRACKING: "WORKFLOW_ENTITY_DEBIT_CARD_TRACKING"
    CREDIT_CARD_STATE: "WORKFLOW_ENTITY_CREDIT_CARD_STATE"
    CREDIT_CARD_TXN_LIST: "WORKFLOW_ENTITY_CREDIT_CARD_TXN_LIST"
    EMPLOYMENT_DATA: "WORKFLOW_ENTITY_EMPLOYMENT_DATA"
    DISPUTE_DETAILS: "WORKFLOW_ENTITY_DISPUTE_DETAILS"
    LIVENESS_DATA: "WORKFLOW_ENTITY_LIVENESS"
    FETCH_CHARGES_FOR_ACTOR: "WORKFLOW_ENTITY_FETCH_CHARGES_FOR_ACTOR"
    DISPLAY_TXN_REASON_FOR_ACTOR: "WORKFLOW_ENTITY_DISPLAY_TXN_REASON_FOR_ACTOR"
    FETCH_FAILED_TRANSACTIONS_FOR_ACTOR: "WORKFLOW_ENTITY_FETCH_FAILED_TRANSACTIONS_FOR_ACTOR"
    CHECK_SALARY_PROGRAM_AMAZON_VOUCHER_ELIGIBILITY: "WORKFLOW_ENTITY_CHECK_SALARY_PROGRAM_AMAZON_VOUCHER_ELIGIBILITY"
    FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS: "WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS"
    FETCH_REWARD_OFFERS_FOR_USER: "WORKFLOW_ENTITY_FETCH_REWARD_OFFERS_FOR_USER"
    FETCH_REWARDS_EVENT_DETAILS: "WORKFLOW_ENTITY_FETCH_REWARDS_EVENT_DETAILS"
    FETCH_REWARD_FOR_EVENT: "WORKFLOW_ENTITY_FETCH_REWARD_FOR_EVENT"
    REFRESH_BALANCE: "WORKFLOW_ENTITY_REFRESH_BALANCE"
    PREDEFINED_MESSAGE_TEMPLATE: "WORKFLOW_ENTITY_PREDEFINED_MESSAGE_TEMPLATE"
    FETCH_USER_TRANSACTIONS: "WORKFLOW_ENTITY_FETCH_USER_TRANSACTIONS"

  VnToCxActionMap:
    CREATE_TICKET: "WORKFLOW_ACTION_CREATE_TICKET"
  VnToCxTransactionType:
    ATM: "TRANSACTION_TYPE_ATM_WITHDRAWALS"
    SUCCESS: "TRANSACTION_TYPE_SUCCESSFUL"
    FAILURE: "TRANSACTION_TYPE_FAILURE"
    ENACH_CHARGES: "TRANSACTION_TYPE_ENACH_CHARGES"
    ALL: "TRANSACTION_TYPE_ALL"
  # whether to read request parameters from header or body/query params
  # possible values header_params, query_params
  # using query_params because Senseforth doesn't support dynamic variables (like short_token) inside header
  ReadWorkflowReqParamsFrom: "query_params"

RateLimitConfig:
  Namespace: "vendornotification"
  ResourceMap:
    chatbot_user_api_default:
      Rate: 100
      Period: 1m
    chatbot_user_api_validatetoken:
      Rate: 100
      Period: 1m
    chatbot_user_api_initiateconversation:
      Rate: 20
      Period: 1m
    chatbot_user_api_pushusermessage:
      Rate: 60
      Period: 1m
    chatbot_user_api_notifyuser:
      Rate: 20
      Period: 1m

Freshdesk:
  GroupEnumToGroupIdMapping:
    GROUP_CALLBACK: 82000152984
    GROUP_EPIFI_ESCALATION: 82000121914
    GROUP_ESCALATED_CASES_CLOSURE: 82000121915
    GROUP_FEDERAL_ESCALATIONS: 82000121911
    GROUP_L1_SUPPORT: 82000121912
    GROUP_L2_SUPPORT: 82000152983
    GROUP_NON_SFTP_ESCALATIONS: 82000121913
    GROUP_SFTP_ESCALATIONS: 82000152982
    GROUP_SFTP_PENDING_GROUP: ***********
    GROUP_FEDERAL_UPDATES: ***********
    GROUP_L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    PRODUCT_CATEGORY_TRANSACTION: "Transactions"
    PRODUCT_CATEGORY_ACCOUNTS: "Accounts"
    PRODUCT_CATEGORY_ONBOARDING: "Onboarding"
    PRODUCT_CATEGORY_SAVE: "Save"
    PRODUCT_CATEGORY_WAITLIST: "Waitlist"
    PRODUCT_CATEGORY_RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General Enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY: "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
    PRODUCT_CATEGORY_GENERAL_ENQUIRY: "General Enquiry"
    PRODUCT_CATEGORY_APP_RELATED: "App Related"
    PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS: "Deposits & Investments"
    PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION: "Incomplete Conversation"
    PRODUCT_CATEGORY_LOANS: "Loans"
    PRODUCT_CATEGORY_NET_WORTH: "Net Worth"
    PRODUCT_CATEGORY_SERVICE_REQUESTS: "Service Requests"
  TransactionTypeEnumToValueMapping:
    TRANSACTION_TYPE_DEBIT_CARD: "Debit Card"
    TRANSACTION_TYPE_IMPS: "IMPS"
    TRANSACTION_TYPE_NEFT: "NEFT"
    TRANSACTION_TYPE_RTGS: "RTGS"
    TRANSACTION_TYPE_UPI: "UPI"
    TRANSACTION_TYPE_INTRA_BANK: "Intra Bank"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    DISPUTE_STATUS_ACCEPTED: "Accepted"
    DISPUTE_STATUS_REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    STATUS_OPEN: 2
    STATUS_PENDING: 3
    STATUS_RESOLVED: 4
    STATUS_CLOSED: 5
    STATUS_WAITING_ON_THIRD_PARTY: 7
    STATUS_ESCALATED_TO_L2: 8
    STATUS_ESCALATED_TO_FEDERAL: 9
    STATUS_SEND_TO_PRODUCT: 10
    STATUS_WAITING_ON_PRODUCT: 11
    STATUS_REOPEN: 12
    STATUS_NEEDS_CLARIFICATION_FROM_CX: 13
    STATUS_WAITING_ON_CUSTOMER: 6
  ProductCategoryDetailsEnumToValueMapping:
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE: "App download issue"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE: "Device check failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_PHONE_NUMBER_OTP: "OTP"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_EMAIL_SELECTION_FAILURE: "Email selection failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_MOTHER_FATHER_NAME: ""
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_KYC: "Manual KYC"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_LIVENESS: "Liveness"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_FACEMATCH_FAIL: "Face-match fail"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UN_NAME_CHECK: "UN Name blacklist"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_CONSENT_FAILURE: "UPI Consent failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_CREATION_FAILURE: "Card creation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_PIN_SET_FAILURE: ""
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_SETUP_FAILURE: "UPI setup failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_VKYC: "VKYC"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT: "Fixed Deposit"
    PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT: "Smart Deposit"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST: "Account Closure Request"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES: "Account Opening Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE: "Account Upgrade/Downgrade"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER: "Balance Transfer"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND: "Category Not Found"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED: "Chequebook Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT: "Dormant"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES: "Fees & Charges"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED: "KYC Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT: "Lien On Account"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES: "Re Login Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM: "Salary Program"
    PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES: "App Related Issues"
    PRODUCT_CATEGORY_DETAILS_BANK_INCOMING: "Bank Incoming"
    PRODUCT_CATEGORY_DETAILS_CX_INCOMING: "CX Incoming"
    PRODUCT_CATEGORY_DETAILS_BLOCK_PERMANENTLY: "Block Permanently"
    PRODUCT_CATEGORY_DETAILS_CARD_REQUEST: "Card Request"
    PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS: "Card Settings"
    PRODUCT_CATEGORY_DETAILS_FIT_RULES: "FIT Rules"
    PRODUCT_CATEGORY_DETAILS_JUMP: "Jump"
    PRODUCT_CATEGORY_DETAILS_MF_INVESTMENTS: "MF Investments"
    PRODUCT_CATEGORY_DETAILS_MF_ONBOARDING: "MF Onboarding"
    PRODUCT_CATEGORY_DETAILS_MF_WITHDRAWALS: "MF Withdrawals"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS: "US Stocks"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES: "US Stocks Wallet Issues"
    PRODUCT_CATEGORY_DETAILS_DEPRECATED_PRODUCT: "Deprecated Product"
    PRODUCT_CATEGORY_DETAILS_BLANK_CHAT: "Blank Chat"
    PRODUCT_CATEGORY_DETAILS_CALL_DROP_DISCONNECTED: "Call Drop/Disconnected"
    PRODUCT_CATEGORY_DETAILS_INCOMPLETE_EMAIL: "Incomplete Email"
    PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL: "Application/Disbursal Issue"
    PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL: "Bureau/Cibil"
    PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS: "Collections"
    PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED: "EMI Related Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL: "LAMF Shortfall"
    PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE_REQUEST: "Loan Closure Request/Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING: "Outcalling"
    PRODUCT_CATEGORY_DETAILS_LOAN_PERSONAL_DETAILS: "Personal Details Updatation"
    PRODUCT_CATEGORY_DETAILS_LOAN_REFUND_WAIVER: "Refund/Waiver Request"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_CONNECT: "Unable To Connect"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_DISCONNECT: "Unable to Disconnect"
    PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS: "Gift Cards"
    PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD: "Incorrect Reward Received"
    PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED: "Reward Not Received"
    PRODUCT_CATEGORY_DETAILS_BANK_INITIATED_FREEZE: "Bank Initated Freeze"
    PRODUCT_CATEGORY_DETAILS_INVESTMENT_WITHDRAWALS: "Investment Withdrawals"
    PRODUCT_CATEGORY_DETAILS_LEA_NPCI_COMPLAINT: "LEA/NPCI Complaint"
    PRODUCT_CATEGORY_DETAILS_CALLBACK_REQUEST: "Callback Request"
    PRODUCT_CATEGORY_DETAILS_DATA_DELETION: "Data Deletion"
    PRODUCT_CATEGORY_DETAILS_NACH_AND_MANDATES: "Nach & Mandates"
    PRODUCT_CATEGORY_DETAILS_REVOKE_APP_ACCESS: "Revoke App Access"
    PRODUCT_CATEGORY_DETAILS_STOP_SERVICES: "Stop Services"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED: "Amount Debited"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited But Not Credited"
    PRODUCT_CATEGORY_DETAILS_AUTOMATED_PAYMENTS: "Automated Payments"
    PRODUCT_CATEGORY_DETAILS_UNAUTHORISED_FRAUD_TRANSACTIONS: "Unauthorised/Fraud Transactions"
    PRODUCT_CATEGORY_DETAILS_CHEQUE_TRANSACTION: "Cheque Transaction"
    PRODUCT_CATEGORY_DETAILS_DATA_NOT_REFRESHED: "Data Not Refreshed"
    PRODUCT_CATEGORY_DETAILS_BUYING_US_STOCKS: "Buying US Stocks"
    PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION: "Business Collaboration"
    PRODUCT_CATEGORY_DETAILS_NET_BANKING: "Net Banking"
    PRODUCT_CATEGORY_DETAILS_UNREGISTERED_USER: "Unregistered User"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_PAY: "Unable To Pay"
    PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI: "Credit Pending To Fi"
    PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST: "Document Request"
  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED: "Units not allotted"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE: "Pre-Closure FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY: "Mature FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE: "Pre-Closure SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY: "Mature SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_ONBOARDING_VKYC_VKYC_REVIEW_STATUS: "VKYC Review Status"

    # Account Closure Request L3 categories
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT: "Freeze On Account"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED: "In App Request Received (Auto ID)"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST: "Manual Request"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES: "Not Closable Due To Pending Charges"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP: "Redirected To App"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED: "Full Kyc Account Closed"

    # Account Opening Issues L3 categories
    SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP: "Add Funds On App"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD: "App Download"
    SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE: "Card Creation & Pin Setup Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED: "Consent Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION: "Customer Creation"
    SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED: "KYC Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE: "Liveness & Facematch Issue"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING: "App Screening"
    SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS: "Refund For Add Funds"
    SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT: "Reopen Closed Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION: "Stuck At Email Verfication"
    SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES: "Vkyc Issues"

    # Account Upgrade/Downgrade L3 categories
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED: "Funds Added But A/C Not Upgraded"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE: "Tier Downgrade"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD: "Within Cool Off Period"

    # Account KYC Related L3 categories
    SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED: "Min Kyc Expired"
    SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM: "Unable To Submit Form"
    SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED: "KYC Not Updated"
    SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED: "KYC Updated but A/C Not Activated"
    SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED: "Signature Not Updated"

    # Chequebook Related L3 categories
    SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_CHARGES_WAIVER: "Chequebook Charges Waiver"
    SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_NOT_RECEIVED: "Chequebook Not Received"
    SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNABLE_TO_UPLOAD_SIGNATURE: "Unable To Upload Signature"

    # Account Fees and Charges L3 categories
    SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_AMB: "AMB"

    # Account Information L3 categories
    SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_INFO: "Account Info"
    SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_TIER_DETAILS_BENEFITS: "Account Tier Details/ Benefits"
    SUB_CATEGORY_ACCOUNTS_INFO_APP_SETTINGS: "App Settings"
    SUB_CATEGORY_ACCOUNTS_INFO_NR_ACCOUNT: "NR Account"
    SUB_CATEGORY_ACCOUNTS_INFO_RE_KYC_ISSUES: "Re KYC Issues"
    SUB_CATEGORY_ACCOUNTS_INFO_INFORMATION_REGARDING_LIEN: "Information Regarding Lien"

    # App Login Issues L3 categories
    SUB_CATEGORY_ACCOUNTS_APP_LOGIN_ISSUES: "App Login Issues"
    SUB_CATEGORY_ACCOUNTS_APP_LOGIN_DEVICE_PASSWORD_NOT_ACCEPTED: "Device Password Not Accepted"
    SUB_CATEGORY_ACCOUNTS_APP_LOGIN_RE_LOGIN_BEFORE_SIGNUP: "Re Login Before Signup"

    # App Related Issues L3 categories
    SUB_CATEGORY_APP_RELATED_GENERAL_QUERIES: "General Queries"
    SUB_CATEGORY_APP_RELATED_INSURANCE_RELATED: "Insurance Related"
    SUB_CATEGORY_APP_RELATED_REGISTRATION: "Registration"
    SUB_CATEGORY_APP_RELATED_UPGRADE_DOWNGRADE_ISSUE: "Upgrade/ Downgrade Issue"
    SUB_CATEGORY_APP_RELATED_APP_CRASH: "App Crash"
    SUB_CATEGORY_APP_RELATED_FEATURE_NOT_LOADING: "Feature Not Loading"
    SUB_CATEGORY_APP_RELATED_USER_FEEDBACK: "User Feedback"
    SUB_CATEGORY_APP_RELATED_FULFILLMENT_RELATED: "Fulfillment Related"
    SUB_CATEGORY_APP_RELATED_REWARDS_RELATED: "Rewards Related"
    SUB_CATEGORY_APP_RELATED_REDIRECTED_TO_BANK: "Redirected To Bank"

    # Card Request L3 categories
    SUB_CATEGORY_CARD_REQUEST_CARD_DAMAGED: "Card Damaged"
    SUB_CATEGORY_CARD_REQUEST_CARD_NOT_REQUIRED: "Card Not Required"
    SUB_CATEGORY_CARD_REQUEST_LOST_STOLEN: "Lost/ Stolen"
    SUB_CATEGORY_CARD_REQUEST_DIGITAL_CARD: "Digital Card"

    # Card Settings L3 categories
    SUB_CATEGORY_CARD_SETTINGS_ACTIVATE_CARD: "Activate Card"
    SUB_CATEGORY_CARD_SETTINGS_CHANGE_USAGE_SETTINGS: "Change Usage Settings"
    SUB_CATEGORY_CARD_SETTINGS_PIN_FAILING: "PIN Failing"
    SUB_CATEGORY_CARD_SETTINGS_TEMPORARY_FREEZE: "Temporary Freeze"
    SUB_CATEGORY_CARD_SETTINGS_UNABLE_TO_CHANGE_USAGE_SETTINGS: "Unable To Change Usage Settings"

    # Card Delivery L3 categories
    SUB_CATEGORY_CARD_DELIVERY_RTO_REDISPATCH: "RTO (Redispatch)"
    SUB_CATEGORY_CARD_DELIVERY_RTO_REFUND: "RTO (Refund)"

    # Card Charges L3 categories
    SUB_CATEGORY_CARD_CHARGES_AMC: "AMC"
    SUB_CATEGORY_CARD_CHARGES_ECOM_POS_DECLINE_FEES: "ECOM/POS Decline Fees"
    SUB_CATEGORY_CARD_CHARGES_OTHER_CHARGES: "Other Charges"

    # Card Info L3 categories
    SUB_CATEGORY_CARD_INFO_CARD_DETAILS: "Card Details"
    SUB_CATEGORY_CARD_INFO_DELIVERY_RELATED: "Delivery Related"
    SUB_CATEGORY_CARD_INFO_MIN_KYC_USER: "Min Kyc User"

    # ATM Transactions L3 categories
    SUB_CATEGORY_ATM_TRANSACTIONS_CDM_CASH_DEPOSIT_NOT_CREDITED: "CDM Cash Deposit Not Credited"
    SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_NOT_DISPENSED: "Debited But Not Dispensed"
    SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_PARTIALLY_DISPENSED: "Debited But Partially Dispensed"
    SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_DOMESTIC: "Unable To Withdraw (Domestic)"
    SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_INTERNATIONAL: "Unable To Withdraw (International)"

    # Transaction Issues L3 categories
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_BUT_NOT_CREDITED: "Amount Debited But Not Credited"
    SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_DOMESTIC: "Txn Failed (Domestic)"
    SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_INTERNATIONAL: "Txn Failed (International)"
    SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_FAILED_REFUND_NOT_RECEIVED: "Order Failed Refund Not Received"
    SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_PENDING: "Order Pending"

    # Fixed Deposit and Smart Deposit L3 categories
    SUB_CATEGORY_FD_SD_UNABLE_TO_CREATE: "Unable To Create"
    SUB_CATEGORY_FD_SD_UNABLE_TO_MODIFY: "Unable To Modify"
    SUB_CATEGORY_FD_SD_UNABLE_TO_PAUSE: "Unable To Pause"
    SUB_CATEGORY_FD_SD_CANCEL_AUTO_RENEWAL: "Cancel Auto Renewal"
    SUB_CATEGORY_FD_SD_FD_CLOSED_BUT_AMOUNT_NOT_RECEIVED: "FD Closed But Amount Not Received"
    SUB_CATEGORY_FD_SD_INCORRECT_MATURITY_AMOUNT: "Incorrect Maturity Amount"
    SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_FD: "Unable To Close FD"
    SUB_CATEGORY_FD_SD_SD_CLOSED_BUT_AMOUNT_NOT_RECEIVED: "SD Closed But Amount Not Received"
    SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_SD: "Unable To Close SD"

    # FIT Rules L3 categories
    SUB_CATEGORY_FIT_RULES_FIT_RULE_NOT_EXECUTED: "FIT Rule Not Executed"
    SUB_CATEGORY_FIT_RULES_INCORRECT_AMOUNT_DEPOSITED: "Incorrect Amount Deposited"
    SUB_CATEGORY_FIT_RULES_FIT_RULE_INFORMATION: "FIT Rule Information"

    # Jump L3 categories
    SUB_CATEGORY_JUMP_PORTFOLIO_MISMATCH: "Portfolio Mismatch"
    SUB_CATEGORY_JUMP_WITHDRAWAL_ISSUES: "Withdrawal Issues"

    # Mutual Funds L3 categories
    SUB_CATEGORY_MUTUAL_FUNDS_SIP_NOT_DEDUCTED: "SIP Not Deducted"
    SUB_CATEGORY_MUTUAL_FUNDS_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED: "Transaction Successful Units Not Allotted"
    SUB_CATEGORY_MUTUAL_FUNDS_STUCK_IN_SCREENING: "Stuck In Screening"
    SUB_CATEGORY_MUTUAL_FUNDS_WITHDRAWAL_FROM_OTHER_PLATFORM: "Withdrawal From Other Platform"
    SUB_CATEGORY_MUTUAL_FUNDS_INCORRECT_AMOUNT_CREDITED: "Incorrect Amount Credited"
    SUB_CATEGORY_MUTUAL_FUNDS_PAUSE_AUTO_INVESTMENT: "Pause Auto Investment"

    # US Stocks L3 categories
    SUB_CATEGORY_US_STOCKS_AMOUNT_DEBITED_NOT_CREDITED_TO_WALLET: "Amount Debited Not Credited To Wallet"

    # Fi Store L3 categories
    SUB_CATEGORY_FI_STORE_DIRECT_TO_HOME: "Direct To Home"
    SUB_CATEGORY_FI_STORE_PHYSICAL_MERCHANDISE: "Fi Store Physical Merchandise"

    # Salary Programs L3 categories
    SUB_CATEGORY_SALARY_PROGRAMS_INSTANT_SALARY: "Instant Salary"
    SUB_CATEGORY_SALARY_PROGRAMS_SALARY_LITE: "Salary Lite"
    SUB_CATEGORY_SALARY_PROGRAMS_INFORMATION_REGARDING_CHARGES: "Information Regarding Charges"

    # Communication L3 categories
    SUB_CATEGORY_COMMUNICATION_SPAM: "SPAM"
    SUB_CATEGORY_COMMUNICATION_CALLBACK: "Callback"
    SUB_CATEGORY_COMMUNICATION_REQUEST_FOR_MORE_INFO: "Request For More Info"

    # Loans L3 categories
    SUB_CATEGORY_LOANS_APPLICATION_FAILED: "Application Failed"
    SUB_CATEGORY_LOANS_DISBURSAL_PENDING: "Disbursal Pending"
    SUB_CATEGORY_LOANS_ISSUE_WITH_LOAN_APPLICATION: "Issue With Loan Application"
    SUB_CATEGORY_LOANS_LOAN_DISBURSED_BUT_ACCOUNT_NOT_CREATED: "Loan Disbursed But A/C Not Created"
    SUB_CATEGORY_LOANS_CONSENT_WITHDRAWAL_FOR_CIBIL_ENQUIRY: "Consent Withdrawal For Cibil Enquiry"
    SUB_CATEGORY_LOANS_REQUEST_FOR_BUREAU_CORRECTION: "Request For Bureau Correction"
    SUB_CATEGORY_LOANS_BORROWERS_DEMISE: "Borrower's Demise"
    SUB_CATEGORY_LOANS_HARASSMENT_COMPLAINT: "Harrasment Complaint"
    SUB_CATEGORY_LOANS_PAYMENT_LINK_TO_BE_SENT: "Payment Link To Be Sent"
    SUB_CATEGORY_LOANS_REQUEST_FOR_SETTLEMENT: "Request For Settlement"
    SUB_CATEGORY_LOANS_REQUESTING_EMI_EXTENSION: "Requesting for EMI Extension"
    SUB_CATEGORY_LOANS_REPAYMENT_SCHEDULE: "Repayment Schedule"
    SUB_CATEGORY_LOANS_EMI_NOT_DEDUCTED: "EMI Not Deducted"
    SUB_CATEGORY_LOANS_NACH_RE_REGISTRATION: "Nach Re Registration"
    SUB_CATEGORY_LOANS_PAYMENT_STATUS_NOT_UPDATED: "Payment Status Not Updated"
    SUB_CATEGORY_LOANS_LOAN_DETAILS_AND_STATUS: "Loan Details & Status"
    SUB_CATEGORY_LOANS_LOAN_PRE_CLOSURE: "Loan Pre Closure"
    SUB_CATEGORY_LOANS_LOAN_REPAYMENT: "Loan Repayment"
    SUB_CATEGORY_LOANS_PAY_MARGIN_AMOUNT: "Pay Margin Amount"
    SUB_CATEGORY_LOANS_PLEDGE_MORE_FUNDS: "Pledge More Funds"
    SUB_CATEGORY_LOANS_PLEDGED_MUTUAL_FUNDS_SOLD: "Pledged Mutual Funds Sold"
    SUB_CATEGORY_LOANS_DELAY_IN_CLOSURE: "Delay in Closure"
    SUB_CATEGORY_LOANS_PAID_BUT_MF_NOT_UNPLEDGED: "Paid But MF Not Unpledged"
    SUB_CATEGORY_LOANS_PRE_CLOSURE: "Pre Closure"
    SUB_CATEGORY_LOANS_PRE_DISBURSEMENT: "Pre Disbursement"
    SUB_CATEGORY_LOANS_SALES: "Sales"
    SUB_CATEGORY_LOANS_SERVICE: "Service"
    SUB_CATEGORY_LOANS_EMI_PAID_BUT_ECS_NACH_RETURN_CHARGED: "EMI Paid But ECS/NACH Return Charged"
    SUB_CATEGORY_LOANS_LATE_PAYMENT_FEES: "Late Payment Fees"
    SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_APP: "Unable To Pay Via App"
    SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_COLLECTIONS_LINK: "Unable To Pay Via Collections Link"

    # Assets L3 categories
    SUB_CATEGORY_ASSETS_ASSETS_VIA_MANUAL_FORMS: "Assets Via Manual Forms"
    SUB_CATEGORY_ASSETS_EPFO: "EPFO"
    SUB_CATEGORY_ASSETS_INDIAN_STOCKS: "Indian Stocks"
    SUB_CATEGORY_ASSETS_LOANS: "Loans"
    SUB_CATEGORY_ASSETS_NPS: "NPS"
    SUB_CATEGORY_ASSETS_OTHER_BANK_ACCOUNTS: "Other Bank Accounts"
    SUB_CATEGORY_ASSETS_ABOUT_NEGATIVE_BALANCE: "About Negative Balance"
    SUB_CATEGORY_ASSETS_ABOUT_NETWORTH: "About Networth"
    SUB_CATEGORY_ASSETS_CONNECTED_ACCOUNTS: "Connected Accounts"
    SUB_CATEGORY_ASSETS_UNABLE_TO_ADD_ASSETS_LIABILITIES: "Unable To Add Assets/Liabilities"

    # Rewards L3 categories
    SUB_CATEGORY_REWARDS_CONVERT_TO_CASH: "Convert To Cash"
    SUB_CATEGORY_REWARDS_PLAY_AND_WIN: "Play And Win"
    SUB_CATEGORY_REWARDS_POWER_UP: "Power Up"
    SUB_CATEGORY_REWARDS_TRAVEL_MILES: "Travel Miles"
    SUB_CATEGORY_REWARDS_HOW_TO_GET_REWARDS: "How To Get Rewards"
    SUB_CATEGORY_REWARDS_HOW_TO_REFER: "How To Refer"
    SUB_CATEGORY_REWARDS_REWARDS_STATEMENT: "Rewards Statement"
    SUB_CATEGORY_REWARDS_AMOUNT_NOT_REFUNDED: "Amount Not Refunded"
    SUB_CATEGORY_REWARDS_EXPIRED_VOUCHER_RECEIVED: "Expired Voucher Received"
    SUB_CATEGORY_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    SUB_CATEGORY_REWARDS_CAMPAIGN_SPECIFIC: "Campaign Specific"
    SUB_CATEGORY_REWARDS_DEBIT_CARD_OFFERS: "Debit Card Offers"
    SUB_CATEGORY_REWARDS_REFERRAL: "Referral"
    SUB_CATEGORY_REWARDS_TIERING_REWARDS: "Tiering Rewards"

    # KYC L3 categories
    SUB_CATEGORY_KYC_NON_KYC_RELATED: "Non KYC Related"
    SUB_CATEGORY_KYC_VKYC_RELATED: "VKYC Related"

    # Account Security L3 categories
    SUB_CATEGORY_ACCOUNT_SECURITY_REQUEST_TO_UNFREEZE: "Request To Unfreeze"
    SUB_CATEGORY_ACCOUNT_SECURITY_FREEZE_RELATED: "Freeze Related"
    SUB_CATEGORY_ACCOUNT_SECURITY_ADDITIONAL_INFORMATION: "Additional Information"
    SUB_CATEGORY_ACCOUNT_SECURITY_NOC_RELATED: "NOC Related"

    # Language Support L3 categories
    SUB_CATEGORY_LANGUAGE_ASSAMESE: "Assamese"
    SUB_CATEGORY_LANGUAGE_BENGALI: "Bengali"
    SUB_CATEGORY_LANGUAGE_HINDI: "Hindi"
    SUB_CATEGORY_LANGUAGE_KANNADA: "Kannada"
    SUB_CATEGORY_LANGUAGE_MALAYALAM: "Malayalam"
    SUB_CATEGORY_LANGUAGE_ORIYA: "Oriya"
    SUB_CATEGORY_LANGUAGE_TAMIL: "Tamil"
    SUB_CATEGORY_LANGUAGE_TELUGU: "Telugu"

    # Data and Statements L3 categories
    SUB_CATEGORY_DATA_STATEMENTS_REQUEST_DATA_DELETION: "Request Data Deletion"
    SUB_CATEGORY_DATA_STATEMENTS_BANK_STATEMENT: "Bank Statement"
    SUB_CATEGORY_DATA_STATEMENTS_MUTUAL_FUNDS_STATEMENT: "Mutual Funds Statement"
    SUB_CATEGORY_DATA_STATEMENTS_SIGNED_BANK_STATEMENT: "Signed Bank Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_JUMP: "Tax Statement (Jump)"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_MUTUAL_FUNDS: "Tax Statement (Mutual Funds)"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_US_STOCKS: "Tax Statement (US Stocks)"
    SUB_CATEGORY_DATA_STATEMENTS_US_STOCKS_STATEMENT: "US Stocks Statement"

    # Mandates L3 categories
    SUB_CATEGORY_MANDATES_ACTIVE_MANDATES_DETAILS: "Active Mandates Details"
    SUB_CATEGORY_MANDATES_CANCEL_SI_NACH_MANDATES: "Cancel SI/NACH Mandates"

    # Profile Updates L3 categories
    SUB_CATEGORY_PROFILE_UPDATES_CHANGE_EMPLOYMENT_DETAILS: "Change Employment Details"
    SUB_CATEGORY_PROFILE_UPDATES_CONTACT_DETAILS_UPDATE: "Contact Details Update"
    SUB_CATEGORY_PROFILE_UPDATES_DOB_CHANGE: "DOB Change"
    SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_FATHER_MOTHER: "Name Change (Father/Mother)"
    SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_USER: "Name Change (User)"

    # Device Issues L3 categories
    SUB_CATEGORY_DEVICE_ISSUES_CARDS: "Cards"
    SUB_CATEGORY_DEVICE_ISSUES_DEVICE_LOST: "Device Lost"
    SUB_CATEGORY_DEVICE_ISSUES_PROMOTIONAL_COMMS: "Promotional Comms"

    # Transaction Types L3 categories
    SUB_CATEGORY_TRANSACTION_TYPES_GOODS_SERVICES_NOT_DELIVERED: "Goods/Services Not Delivered"
    SUB_CATEGORY_TRANSACTION_TYPES_INCORRECT_AMOUNT: "Incorrect Amount"
    SUB_CATEGORY_TRANSACTION_TYPES_NOT_VISIBLE_ON_APP: "Not Visible On App"
    SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2M: "In App UPI (P2M)"
    SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2P: "In App UPI (P2P)"
    SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2M: "Off App UPI (P2M)"
    SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2P: "Off App UPI (P2P)"
    SUB_CATEGORY_TRANSACTION_TYPES_INTRA_BANK: "INTRA BANK"
    SUB_CATEGORY_TRANSACTION_TYPES_NACH_ECS_CHARGES: "NACH/ECS Charges"
    SUB_CATEGORY_TRANSACTION_TYPES_RECURRING_PAYMENT_CANCELLED_BUT_AMOUNT_DEBITED: "Recurring Payment cancelled But Amount Debited"
    SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE_DEPOSIT: "Cheque Deposit"
    SUB_CATEGORY_TRANSACTION_TYPES_INTERNATIONAL_REMITTANCE: "International Remittance"
    SUB_CATEGORY_TRANSACTION_TYPES_MERCHANT_REFUND: "Merchant Refund"
    SUB_CATEGORY_TRANSACTION_TYPES_OTHER_DOMESTIC_TRANSACTIONS: "Other Domestic Transactions"
    SUB_CATEGORY_TRANSACTION_TYPES_DEPOSITING_CASH: "Depositing Cash"
    SUB_CATEGORY_TRANSACTION_TYPES_IPO: "IPO"
    SUB_CATEGORY_TRANSACTION_TYPES_TRANSACTION_RELATED_ENQUIRY: "Transaction Related Enquiry"

    # UPI Issues L3 categories
    SUB_CATEGORY_UPI_ISSUES_UNABLE_TO_LINK_FEDERAL_ACCOUNT_TO_OTHER_APPS: "Unable To Link Federal Account To Other Apps"
    SUB_CATEGORY_UPI_ISSUES_BANK_TRANSFER: "Bank Transfer"
    SUB_CATEGORY_UPI_ISSUES_INTERNATIONAL_TRANSACTIONS: "International Transactions"
    SUB_CATEGORY_UPI_ISSUES_LIMIT_EXCEEDED: "Limit Exceeded"
    SUB_CATEGORY_UPI_ISSUES_PIN_TRIES_EXCEEDED: "Pin Tries Exceeded"
    SUB_CATEGORY_UPI_ISSUES_UPI_ISSUE: "UPI Issue"

    # International Transactions L3 categories
    SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_INTERNATIONAL: "International"
    SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_STOP_CHEQUE_PAYMENT: "Stop Cheque Payment"
    SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_FEES_AND_CHARGES: "Fees & Charges"

    # Certificates L3 categories
    SUB_CATEGORY_CERTIFICATES_BALANCE_CERTIFICATE: "Balance Certificate"
    SUB_CATEGORY_CERTIFICATES_INTEREST_CERTIFICATE: "Interest Certificate"

    # Eligibility Issues L3 categories
    SUB_CATEGORY_ELIGIBILITY_NOT_ELIGIBLE: "Not Eligible"
    SUB_CATEGORY_ELIGIBILITY_OTP_NOT_RECEIVED: "OTP Not Received"

    # Profile Changes L3 categories
    SUB_CATEGORY_PROFILE_CHANGES_ADDRESS_CHANGE: "Address Change"
    SUB_CATEGORY_PROFILE_CHANGES_UNAUTHORISED_TRANSACTION: "Unauthorised Transaction"

    # Card Usage Issues L3 categories
    SUB_CATEGORY_CARD_USAGE_CARD_NOT_ACCEPTED: "Card Not Accepted"
    SUB_CATEGORY_CARD_USAGE_CONTACTLESS_NOT_WORKING: "Contactless Not Working"
    SUB_CATEGORY_CARD_USAGE_ATM_DECLINE_FEES: "ATM Decline Fees"
    SUB_CATEGORY_CARD_USAGE_FUEL_CHARGES: "Fuel Charges"
    SUB_CATEGORY_CARD_USAGE_TCS_DEDUCTIONS: "TCS Deductions"

    # Balance Issues L3 categories
    SUB_CATEGORY_BALANCE_ISSUES_BALANCE_NOT_UPDATED: "Balance Not Updated"
    SUB_CATEGORY_BALANCE_ISSUES_DOUBLE_DEBIT: "Double Debit"
    SUB_CATEGORY_BALANCE_ISSUES_INCORRECT_AMOUNT_DEBITED: "Incorrect Amount Debited"
    SUB_CATEGORY_BALANCE_ISSUES_EXCESS_AMOUNT_PAID: "Excess Amount Paid"

    # App Access Issues L3 categories
    SUB_CATEGORY_APP_ACCESS_NO_APP_ACCESS: "No App Access"
    SUB_CATEGORY_APP_ACCESS_UNABLE_TO_INVEST: "Unable to Invest"
    SUB_CATEGORY_APP_ACCESS_UNABLE_TO_WITHDRAW: "Unable To Withdraw"
    SUB_CATEGORY_APP_ACCESS_UNABLE_TO_ADD_FUNDS: "Unable To Add Funds"

    # Payment Issues L3 categories
    SUB_CATEGORY_PAYMENT_ISSUES_SENT_TO_WRONG_USER: "Sent To Wrong User"
    SUB_CATEGORY_PAYMENT_ISSUES_CASH_DEPOSIT_AT_BRANCH: "Cash Deposit At Branch"

    # Fixed/Smart Deposit Issues L3 categories
    SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_FD: "Unable To Create FD"
    SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_SD: "Unable To Create SD"
    SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_REDEEM: "Unable To Redeem"

    # Account Status Issues L3 categories
    SUB_CATEGORY_ACCOUNT_STATUS_ACCOUNT_FROZEN_CLOSED: "Account Frozen/Closed"
    SUB_CATEGORY_ACCOUNT_STATUS_EMAIL_ADDRESS: "Email Address"
    SUB_CATEGORY_ACCOUNT_STATUS_PHONE_NUMBER: "Phone Number"
    SUB_CATEGORY_ACCOUNT_STATUS_BOUNCE_CHARGE: "Bounce Charge"

    # Reward Issues L3 categories
    SUB_CATEGORY_REWARD_ISSUES_VOUCHER_NOT_RECEIVED: "Voucher Not Received"
    SUB_CATEGORY_REWARD_ISSUES_FOREX_RATE_ISSUE: "Forex Rate Issue"

    # Stock Trading Issues L3 categories
    SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_BUY: "Unable To Buy"
    SUB_CATEGORY_STOCK_TRADING_MONEY_NOT_CREDITED: "Money Not Credited"
    SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_SELL: "Unable To Sell"

  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"
  TicketVisibilityEnumToValueMapping:
    TICKET_VISIBILITY_ONLY_AGENT: "Agent"
    TICKET_VISIBILITY_ONLY_CUSTOMER: "Customer"
    TICKET_VISIBILITY_ALL: "All"
  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"
  DefaultPageSize: 100

QuestSdk:
  Disable: true

GrpcRatelimiterParams:
  RateLimitConfig:
    Namespace: "vendornotification-rpc"
    ResourceMap:
      vendornotification_notifications_moengage_moengage_getuserattributes:
        Rate: 3000
        Period: 1m
        PriorityDistribution:
          IsEnabled: false
      vendornotification_fi_coins_accounting_ficoinsaccounting_getuserficoinsbalance:
        Rate: 80
        Period: 1s
        PriorityDistribution:
          IsEnabled: false
      vendornotification_fi_coins_accounting_ficoinsaccounting_transactficoins:
        Rate: 30
        Period: 1s
        PriorityDistribution:
          IsEnabled: false
      vendornotification_fi_coins_accounting_ficoinsaccounting_getuseridfromphonenumber:
        Rate: 30
        Period: 1s
        PriorityDistribution:
          IsEnabled: false

IrisWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***********"
  SoftBlock: true

Auth:
  JwtEncryption:
    Issuer: "epifi"
  JwtDecryption:
    Issuer: "epifi"

Reward:
  DummyRewardProcessingStatus: "PROCESSED"
  DisableDummyApiResponseFlow: false
  RewardTypeToMaxRewardValueMap:
    FI_COIN: 5000
    CASH: 5000
    VOUCHER: 1

Nugget:
  NuggetAccountFreezeDummyDetails:
    U1:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: FREEZE_STATUS_UNSPECIFIED
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U2:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: ACCOUNT_FREEZE_STATUS_UNFROZEN
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U3:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U4:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U5:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U6:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U7:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U8:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U9:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U10:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U11:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U12:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U13:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U14:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U15:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U16:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U17:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U18:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U19:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U20:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U21:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U22:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U23:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "FORM123"
      FormStatus: STATUS_CREATED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U24:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "FORM124"
      FormStatus: STATUS_SUBMITTED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U25:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "FORM125"
      FormStatus: STATUS_CANCELLED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "re"
  NuggetTransactionDummyDetails:
    T1:
      CreatedAt: 2025-08-12 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-12 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DEBIT_CARD_CHARGES
      TransactionAmount: 500.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 12 August 2025 at 3:04 PM
    T2:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ECS_ENACH_CHARGES
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T3:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: AMB_CHARGE
      TransactionAmount: 100.46
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T4:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ANYWHERE_BANKING_CHARGE
      TransactionAmount: 4.37
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T5:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_DCC_FEE_CHARGE
      TransactionAmount: ********.25
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T6:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DEBIT_CARD_AMC_CHARGE
      TransactionAmount: 90.67
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T7:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_FOREX_MARKUP_CHARGE
      TransactionAmount: 111.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T8:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_TCS_FEE_CHARGE
      TransactionAmount: 123.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T9:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: FIRST_CARD_ORDER_FEE
      TransactionAmount: 1234.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T10:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: CHEQUE_BOOK_CHARGES
      TransactionAmount: 12345.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T11:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ECOM_POS_DECLINE_CHARGE
      TransactionAmount: 98.24
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T12:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ATM_DECLINE_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T13:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: OTHER_BANK_ATM_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T14:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DUPLICATE_CARD_FEE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T15:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T16:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: EXTERNAL
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 1.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T17:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 2.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T18:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: ATM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 3.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T19:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 4.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T20:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: ECOMM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 5.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T21:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: RTGS
      Tags: ""
      TransactionAmount: 6.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T22:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 7.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T23:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 10.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T24:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 200.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T25:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: NEFT
      Tags: ""
      TransactionAmount: 20000.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T26:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: IMPS
      Tags: ""
      TransactionAmount: 100000.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T27:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_POS_26
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T28:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_ATM_7
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: ATM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T29:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI1215
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: EXTERNAL
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T30:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_POS_6
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T31:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI130
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T32:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI129
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T33:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI1095
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
