//go:generate conf_gen github.com/epifi/gamma/vendornotification/config Config
package config

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/auth/jwt"
	syncpkg "github.com/epifi/be-common/pkg/syncwrapper/response"

	"github.com/epifi/be-common/pkg/cfg"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

// nolint:gosec
const (
	UPI_LIST_VAE              = "upilistvae"
	SenderCode                = "SenderCode"
	SenderCodeLoans           = "SenderCodeLoans"
	AAVgVnSecretsV1           = "AaVgVnSecretsV1"
	AAVgVnSecretsV2           = "AAVgVnSecretsV2"
	SahamatiPublicKeySecretId = "SahamatiPublicKeyJwk"
	DpandaVnSecrets           = "DpandaVnSecrets"
	PoshvineVnSecrets         = "PoshvineVnSecrets"
	RazorpayVnSecrets         = "RazorpayVnSecrets"
)

func Load() (*Config, error) {
	once.Do(
		func() {
			config, err = loadConfig()
		},
	)

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.VENDOR_NOTIFI_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, fmt.Errorf("error in loading secrets: %w", err)
	}

	keyToSecret, err := cfg.LoadSecrets(conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}

	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(&cfg.DB{GormV2: &cfg.GormV2Conf{}}, c.Secrets, keyToSecret)

	if val, ok := c.Secrets.Ids[AAVgVnSecretsV1]; ok {
		err := json.Unmarshal([]byte(val), &c.AA.AaVgVnSecretsV1)
		if err != nil {
			fmt.Printf("failed to unmarshal Aa Vg Vn secrets v1\n")
		}
	}

	if val, ok := c.Secrets.Ids[AAVgVnSecretsV2]; ok {
		err := json.Unmarshal([]byte(val), &c.AA.AaVgVnSecretsV2)
		if err != nil {
			fmt.Printf("failed to unmarshal Aa Vg Vn v2 secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[SahamatiPublicKeySecretId]; ok {
		c.AA.SahamatiPublicKey = val
	}

	if val, ok := c.Secrets.Ids[DpandaVnSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.DpandaVnSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal dpanda Vn secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[PoshvineVnSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.PoshvineVnSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal poshvine Vn secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[RazorpayVnSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.RazorpayVnSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal razorpay Vn secrets\n")
		}
	}

	c.Auth.JwtEncryption.RSAPrivateKey, err = jwt.ParseRSAPrivateKeyFromPEM([]byte(c.Auth.JwtEncryption.RSAPrivateKeyPEM))
	if err != nil {
		return fmt.Errorf("failed to parse private key %w", err)
	}

	err = json.Unmarshal([]byte(c.Auth.ClientCredentials.IdToSecretString), &c.Auth.ClientCredentials.IdToSecretMap)
	if err != nil {
		return fmt.Errorf("failed to parse client credentials %w", err)
	}

	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type AA struct {
	TokenIssuer           string `dynamic:"true"`
	SahamatiPublicKey     string
	VerifyApiKeyAndJws    bool `dynamic:"true"`
	IPWhiteListing        *IPWhiteListing
	OneMoneyCrId          string `dynamic:"true"`
	FinvuCrId             string `dynamic:"true"`
	EpifiAaKid            string `dynamic:"true"`
	AaVgVnSecretsV1       *AaVgVnSecrets
	AaVgVnSecretsV2       *AaVgVnSecrets
	AaSecretsVersionToUse string `dynamic:"true"`
}

type AaVgVnSecrets struct {
	EpifiAaPrivateKey string `json:"epifi_aa_private_key"`
}

//go:generate conf_gen github.com/epifi/gamma/vendornotification/config Config
type Config struct {
	Application                      *Application
	Server                           *Server
	AWS                              *Aws
	UpdateTransactionEventsPublisher *cfg.SqsPublisher
	InboundTxnPublisher              *cfg.SqsPublisher
	InboundUpiTxnPublisher           *cfg.SqsPublisher
	InboundLoanTxnPublisher          *cfg.SqsPublisher
	GrpcRateLimiterParams            *cfg.GrpcRateLimiterParams
	// Events that are posted as a response for event initiated by Epifi & that don't require direct response
	// e.g., Response to the Events initiated from Epifi e.g., ListKeys, ReqPay, ListAcctProviders
	// Such events will be consumed by Vendor gateway. This queue would help orchestrate a sync wrapper over the
	// UPI's Async calls that were made from vendor gateway
	SyncWrapperPublisher                   *cfg.SqsPublisher
	CreateCardCallbackPublisher            *cfg.SqsPublisher
	DispatchPhysicalCardCallbackPublisher  *cfg.SqsPublisher
	CheckLivenessCallbackPublisher         *cfg.SqsPublisher
	UpdateShippingAddressCallbackPublisher *cfg.SqsPublisher
	// TODO(nitesh): remove below publishers from the config once we have pub-sub based filtering of events enabled
	UPIReqAuthEventPublisher                     *cfg.SqsPublisher
	UPIReqAuthMandateEventPublisher              *cfg.SqsPublisher
	UPIReqAuthValCustEventPublisher              *cfg.SqsPublisher
	UPIReqMandateConfirmationEventPublisher      *cfg.SqsPublisher
	UPIRespPayEventPublisher                     *cfg.SqsPublisher
	UPIRespMandateEventPublisher                 *cfg.SqsPublisher
	UPIReqTxnConfirmationEventPublisher          *cfg.SqsPublisher
	UPIReqValAddressEventPublisher               *cfg.SqsPublisher
	UPIListPspKeysEventPublisher                 *cfg.SqsPublisher
	UPIListVaePublisher                          *cfg.ExtendedSqsPublisher
	CreateDepositCallbackPublisher               *cfg.SqsPublisher
	PreCloseDepositCallbackPublisher             *cfg.SqsPublisher
	FdAutoRenewCallbackPublisher                 *cfg.SqsPublisher
	AclSmsCallbackPublisher                      *cfg.SqsPublisher
	KaleyraSmsCallbackPublisher                  *cfg.SqsPublisher
	AclWhatsappCallbackPublisher                 *cfg.SqsPublisher
	AclWhatsappReplyPublisher                    *cfg.SqsPublisher
	GupshupWhatsappCallbackPublisher             *cfg.SqsPublisher
	GupshupRcsCallbackPublisher                  *cfg.SqsPublisher
	NetCoreSmsCallbackPublisher                  *cfg.SqsPublisher
	AirtelSmsCallbackPublisher                   *cfg.SqsPublisher
	DeviceReRegCallbackPublisher                 *cfg.SqsPublisher
	DeviceRegSMSAckPublisher                     *cfg.SqsPublisher
	CustomerCreationCallbackPublisher            *cfg.SqsPublisher
	BankCustCallbackPublisher                    *cfg.SqsPublisher
	AccountCreationCallbackPublisher             *cfg.SqsPublisher
	FederalVkycUpdatePublisher                   *cfg.SqsPublisher
	KarzaVkycCallEventPublisher                  *cfg.SqsPublisher
	KarzaVkycAgentResponsePublisher              *cfg.SqsPublisher
	KarzaVkycAuditorResponsePublisher            *cfg.SqsPublisher
	EmailCallbackPublisher                       *cfg.SqsPublisher
	ConsentCallbackPublisher                     *cfg.SqsPublisher
	FICallbackPublisher                          *cfg.SqsPublisher
	AccountLinkStatusCallbackPublisher           *cfg.SqsPublisher
	CardTrackingCallbackPublisher                *cfg.SqsPublisher
	UPIReqTxnConfirmationComplaintEventPublisher *cfg.SqsPublisher
	OzonetelCallDetailsPublisher                 *cfg.SqsPublisher
	UPIRespComplaintEventPublisher               *cfg.SqsPublisher
	FreshchatActionCallbackPublisher             *cfg.SqsPublisher
	CCTransactionNotificationPublisher           *cfg.SqsPublisher
	CCStatementNotificationPublisher             *cfg.SqsPublisher
	CCAcsNotificationPublisher                   *cfg.SqsPublisher
	TssWebhookCallBackPublisher                  *cfg.SqsPublisher
	HealthInsurancePolicyIssuanceEventPublisher  *cfg.SqsPublisher
	CCNonFinancialNotificationPublisher          *cfg.SqsPublisher
	SmallcaseProcessMFHoldingsWebhookPublisher   *cfg.ExtendedSqsPublisher
	SignalWorkflowPublisher                      *cfg.SqsPublisher
	LoansFiftyfinCallbackPublisher               *cfg.SqsPublisher
	AirtelWhatsappCallbackPublisher              *cfg.SqsPublisher
	Secrets                                      *cfg.Secrets
	FederalUPISignatureVerifier                  *cfg.XMLDigitalSignatureVerifier
	PayFundTransferStatusCodeJson                string
	PayUpiStatusCodeJson                         string
	EnachTransactionStatusCodeJson               string
	DepositResponseStatusCodeFilePath            string
	CardResponseStatusCodeFilePath               string
	Flags                                        *Flags `dynamic:"true"`
	SecureLogging                                *SecureLogging
	Logging                                      *cfg.Logging
	FeatureFlags                                 *FeatureFlags `dynamic:"true"`
	SyncRespHandler                              *syncpkg.SyncRespHandler
	KarzaVkycWhitelist                           *IPWhiteListing
	AclSmsWhitelist                              *IPWhiteListing
	KaleyraSmsWhitelist                          *IPWhiteListing
	AclWhatsappWhitelist                         *IPWhiteListing
	NetCoreWhitelist                             *IPWhiteListing
	AirtelWhitelist                              *IPWhiteListing
	UPIWhitelist                                 *IPWhiteListing
	OzonetelWhitelist                            *IPWhiteListing
	FreshchatWhitelist                           *IPWhiteListing
	SenseforthWhitelist                          *IPWhiteListing
	TssWhitelist                                 *IPWhiteListing
	RiskcovryWhitelist                           *IPWhiteListing
	SmallcaseWhitelist                           *IPWhiteListing
	SprinklrWhitelist                            *IPWhiteListing
	KarzaEPANWhitelist                           *IPWhiteListing
	M2PWhitelist                                 *IPWhiteListing
	FiftyfinWhitelist                            *IPWhiteListing
	MoneyviewWhiteList                           *IPWhiteListing
	IrisWhitelist                                *IPWhiteListing
	AbflWhitelist                                *IPWhiteListing
	CredgenicsWhitelist                          *IPWhiteListing
	DPandaWhitelist                              *IPWhiteListing
	PoshVineWhitelist                            *IPWhiteListing
	GupshupWhitelist                             *IPWhiteListing
	PaisabazaarWhitelist                         *IPWhiteListing
	SetuWhiteList                                *IPWhiteListing
	FederalWhiteList                             *IPWhiteListing
	SavenWhiteList                               *IPWhiteListing
	LeadsWhiteList                               *IPWhiteListing
	AuthWhiteList                                *IPWhiteListing
	NuggetWhiteList                              *IPWhiteListing
	// X-Forwarded-For header is used to identify the client IP address and used for IP Whitelisting
	//
	// Application Load Balancers and Classic Load Balancers automatically add X-Forwarded-For, X-Forwarded-Proto,
	// and X-Forwarded-Port headers to the request.
	//
	// In addition, Grpc Gateway also adds a X-Forwarded-For header
	//
	// If header already exists, the values are appended in above cases
	//
	// Given that different environments have different architecture, this variable will hold the number of hops
	// a request has gone through where its X-Forwarded-For header is modified
	//
	// Ref: https://docs.aws.amazon.com/elasticloadbalancing/latest/userguide/how-elastic-load-balancing-works.html#http-headers
	// https://github.com/grpc-ecosystem/grpc-gateway/blob/master/README.md#mapping-grpc-to-http
	NumberOfHopsThatAddXForwardedFor int
	// IP Prefix of the CIDR range of Environment e.g., "10.50"
	VpcCidrIPPrefix                                 string
	AA                                              *AA `dynamic:"true"`
	Ozonetel                                        *Ozonetel
	Tracing                                         *cfg.Tracing
	Profiling                                       *cfg.Profiling
	Chatbot                                         *Chatbot
	RateLimitConfig                                 *cfg.RateLimitConfig
	Freshdesk                                       *cfg.Freshdesk
	CardSwitchFinancialNotificationPublisher        *cfg.SqsPublisher
	CardSwitchNonFinancialNotificationPublisher     *cfg.SqsPublisher
	AccountStatusCallBackPublisher                  *cfg.SqsPublisher
	UPIReqMapperConfirmationEventPublisher          *cfg.SqsPublisher
	EnachRegistrationAuthorisationCallbackPublisher *cfg.SqsPublisher
	ProcrastinatorWorkflowPublisher                 *cfg.SqsPublisher
	QuestSdk                                        *sdkconfig.Config `dynamic:"true"`
	QuestRedisOptions                               *cfg.RedisOptions
	// adding this since we don't have any other quest enabled config.
	DummyQuestVariable       string `dynamic:"true" ,quest:"variable"`
	KycStatusUpdatePublisher *cfg.SqsPublisher
	// name of the bucket to store switch transactions notifications for firefly service (Credit Card)
	CcSwitchNotificationsBucketName string `iam:"s3-readwrite"`
	// name of the raw data bucket of data engineering team to store switch transactions notifications for piping the data to snowflake  (Credit Card)
	CcRawSwitchNotificationsBucketName string `iam:"s3-readwrite"`
	EpanCallbackBucketName             string `iam:"s3-readwrite"`
	// file path with file name placeholder to direct the path of notifications into a specific place in firefly (credit card) service's bucket
	M2pFederalSwitchNotificationFilePath string
	// file path with file name placeholder to direct the path of notifications into a specific place in data engineering team's bucket
	// follow doc for naming convention -> https://docs.google.com/document/d/1wrtU6CSMCfoBjpApkHs84eMaQF4aXFMfv13yxesvifU/edit#heading=h.upz3aov1o8xj
	RawBucketM2pFederalSwitchNotificationFilePath string
	DpandaVnSecrets                               *FiStoreVendorSecrets
	PoshvineVnSecrets                             *FiStoreVendorSecrets
	RazorpayVnSecrets                             *FiStoreVendorSecrets
	CredgenicsCallbackStreamProducer              *CredgenicsCallbackStreamProducer
	FederalBankCustKycStateChangePublisher        *cfg.SqsPublisher
	FederalResidentialStatusUpdatePublisher       *cfg.SqsPublisher
	FederalMobileNumberUpdatePublisher            *cfg.SqsPublisher
	PgRazorpayInboundEventPublisher               *cfg.SqsPublisher
	FederalEscalationUpdateEventPublisher         *cfg.SqsPublisher
	CcOnboardingStateUpdateEventPublisher         *cfg.SqsPublisher
	Auth                                          *Auth
	Reward                                        *Reward `dynamic:"true"`
	VendorRewardFulfillmentPublisher              *cfg.SqsPublisher
	SavenRewardVnSecrets                          *SavenRewardVnSecrets
	Nugget                                        *Nugget `dynamic:"true"`
	EnableIndianStocksContentApi                  bool    `dynamic:"true"`
}

type Nugget struct {
	NuggetAccountFreezeDummyDetails map[string]*NuggetAccountFreezeDummyDetails `dynamic:"true"`
	NuggetTransactionDummyDetails   map[string]*NuggetTransactionDummyDetails   `dynamic:"true"`
}

type NuggetAccountFreezeDummyDetails struct {
	AccountStatus         string `json:"account_status" dynamic:"true"`
	ProcessedFreezeReason string `json:"processed_freeze_reason" dynamic:"true"`
	FreezeType            string `json:"freeze_type" dynamic:"true"`
	FormId                string `json:"form_id" dynamic:"true"`
	FormStatus            string `json:"form_status" dynamic:"true"`
	FormExpiryDate        string `json:"form_expiry_date" dynamic:"true"`
	LeaComplaintDetails   string `json:"lea_complaint_details" dynamic:"true"`
}

type NuggetTransactionDummyDetails struct {
	CreatedAt             string `json:"created_at" dynamic:"true"`
	ErrorCode             string `json:"error_code" dynamic:"true"`
	ExecutedAt            string `json:"executed_at" dynamic:"true"`
	P2P_P2M               string `json:"p2p_p2m" dynamic:"true"`
	PaymentProtocol       string `json:"payment_protocol" dynamic:"true"`
	Provenance            string `json:"provenance" dynamic:"true"`
	Tags                  string `json:"tags" dynamic:"true"`
	TransactionAmount     string `json:"transaction_amount" dynamic:"true"`
	TransactionStatus     string `json:"transaction_status" dynamic:"true"`
	CreatedAtReadableTime string `json:"created_at_readable_time" dynamic:"true"`
}

type SavenRewardVnSecrets struct {
	Path                     string `iam:"sm-read"`
	JwtVerificationKeyBase64 string `field:"Path" jsonPath:"JwtVerificationKeyBase64"`
}

type Reward struct {
	DummyRewardProcessingStatus   string             `dynamic:"true"`
	DisableDummyApiResponseFlow   bool               `dynamic:"true"`
	RewardTypeToMaxRewardValueMap map[string]float64 `dynamic:"true"`
}

type Auth struct {
	JwtEncryption     *JwtEncryption
	ClientCredentials *ClientCredentials
}

type JwtEncryption struct {
	Issuer               string
	RSAPrivateKeyPEMPath string `iam:"sm-read"`
	RSAPrivateKeyPEM     string `field:"RSAPrivateKeyPEMPath"`
	RSAPrivateKey        *rsa.PrivateKey
}

type ClientCredentials struct {
	IdToSecretPath   string `iam:"sm-read"`
	IdToSecretString string `field:"IdToSecretPath"`
	IdToSecretMap    map[string]string
}

type SecureLogging struct {
	EnableSecureLog bool
	SecureLogPath   string
	MaxSizeInMBs    int // megabytes
	MaxBackups      int // There will be MaxBackups + 1 total files
}

type Application struct {
	Environment                    string
	Name                           string
	CardResponseStatusCodeFilePath string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Endpoint string
	Region   string
	S3       S3
}

type S3 struct {
	// Deprecated: To follow servergen convention. Use config fields with BucketName suffix instead.
	BucketNames map[string]string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// Flag to determine if cc transaction notification will be processed via temporal
	// If enabled we will use the faas executor to execute the transaction notification activity
	EnableCCTransactionProcessingViaTemporal bool `dynamic:"true"`
	// EnableNewEndpointInboundNotification flag to enable the inbound notification from new endpoint.
	// True means notification from new endpoint will be routed and old endpoint notification will be disabled.
	// False means old will be enabled and new end point will not be routed.
	EnableNewEndpointInboundNotification bool `dynamic:"true"`
}

type FeatureFlags struct {
	AllowCustomerCallbackProcessing bool `dynamic:"true"`
	AllowAccountCallbackProcessing  bool
}

type SyncWrapperPublisher struct {
	Publisher  *cfg.SqsPublisher
	CtxTimeout time.Duration
}

type IPWhiteListing struct {
	EnableWhitelist bool
	WhitelistedIPs  string
	SoftBlock       bool
}

type Ozonetel struct {
	RoutingChannelToNumberMapping map[string]int32
	DefaultChannelNumber          int32
	CallTypeMapping               map[string]string
	CallStatusMapping             map[string]string
	AgentStatusMapping            map[string]string
	CustomerStatusMapping         map[string]string
	HangUpByMapping               map[string]string
	// set this field to true if we start want to pass priority field in GetRoutingChannel API response
	IsPriorityRoutingEnabled   bool
	RecordingIdToNumberMapping map[string]int32
	// InternalQuestionIDToOzonetelQuestionID maps an internal question ID to a nested map where the key is a language
	// and the value is the question ID provided by Ozonetel.
	InternalQuestionIDToOzonetelQuestionID map[int32]map[string]int32
}

type Chatbot struct {
	// stores raw vn string to cx entity enum mapping
	VnToCxEntityMap map[string]string
	// stores raw vn string to cx action enum mapping
	VnToCxActionMap map[string]string
	// specifies if request params has to be read from vn request or http header
	// possible values header_params, query_params
	// the default is reading from body/query params
	// This is done because Senseforth doesn't support dynamic variables (like short token) inside header
	ReadWorkflowReqParamsFrom string
	// stores raw vn string to cx transaction type enum mapping
	VnToCxTransactionType map[string]string
}

type FiStoreVendorSecrets struct {
	SecretKey string `json:"key"`
	Iv        string `json:"iv"`
	Mode      string `json:"mode"`
}

type CredgenicsCallbackStreamProducer struct {
	EmailStream        *cfg.KinesisProducer
	SmsStream          *cfg.KinesisProducer
	WhatsappStream     *cfg.KinesisProducer
	CallingStream      *cfg.KinesisProducer
	VoiceMessageStream *cfg.KinesisProducer
}
