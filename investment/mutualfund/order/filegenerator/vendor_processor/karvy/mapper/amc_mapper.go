//nolint:dupl
package mapper

import "github.com/epifi/gamma/api/investment/mutualfund"

// ref: https://docs.google.com/spreadsheets/d/1c6hu9yJs3FA48CKlPGGiYdUYu7mgWipO/edit#gid=906492001
var AmcMapper = map[mutualfund.Amc]string{
	mutualfund.Amc_AXIS:                 "AXF",
	mutualfund.Amc_BARODA:               "BPF",
	mutualfund.Amc_BNP_PARIBAS:          "178",
	mutualfund.Amc_BOI_AXA:              "BAF",
	mutualfund.Amc_CANARA_ROBECO:        "CRF",
	mutualfund.Amc_EDELWEISS:            "EMF",
	mutualfund.Amc_IDBI:                 "135",
	mutualfund.Amc_INDIABULLS:           "IBM",
	mutualfund.Amc_INVESCO:              "RGF",
	mutualfund.Amc_ITI:                  "ITI",
	mutualfund.Amc_JM_FINANCIAL:         "JMF",
	mutualfund.Amc_LIC:                  "LIC",
	mutualfund.Amc_MIRAE:                "MAF",
	mutualfund.Amc_MOTILAL_OSWAL:        "MOF",
	mutualfund.Amc_NIPPON_LIFE:          "RMF",
	mutualfund.Amc_PGIM:                 "PRF",
	mutualfund.Amc_PRINCIPAL:            "PMF",
	mutualfund.Amc_QUANT_MONEY_MANAGERS: "166",
	mutualfund.Amc_QUANTUM:              "QMF",
	mutualfund.Amc_SUNDARAM:             "176",
	mutualfund.Amc_TAURUS:               "TMF",
	mutualfund.Amc_UTI:                  "UTI",
	mutualfund.Amc_TRUST:                "185",
	mutualfund.Amc_NJ:                   "187",
	mutualfund.Amc_SAMCO:                "188",

	// Agreements with the following AMCs are not signed yet
	// Post signing, payment instruments for these AMC should be configured before allowing orders
	// RTAs not applicable for AMC should be removed
	mutualfund.Amc_BAJAJ_FINSERV: "189",
	mutualfund.Amc_HELIOS:        "HLS",
	mutualfund.Amc_ZERODHA:       "Z",
	mutualfund.Amc_OLD_BRIDGE:    "139",
}
