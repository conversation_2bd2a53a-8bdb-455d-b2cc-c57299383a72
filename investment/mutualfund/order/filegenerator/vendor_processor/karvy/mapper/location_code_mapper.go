//nolint:dupl
package mapper

import (
	"github.com/epifi/gamma/api/investment/mutualfund"
)

/*
LocationCodeMapper is a legacy field that is not used by cams vendor anymore, but is mandatory in the file. So,
we are hard-coding the location code that denotes bangalore for the fund type.
*/
var LocationCodeMapper = map[mutualfund.Amc]string{
	mutualfund.Amc_AXIS:                 "B1", // B1 for Bangalore
	mutualfund.Amc_BARODA:               "B1",
	mutualfund.Amc_BNP_PARIBAS:          "B1",
	mutualfund.Amc_BOI_AXA:              "B1",
	mutualfund.Amc_CANARA_ROBECO:        "B1",
	mutualfund.Amc_EDELWEISS:            "B1",
	mutualfund.Amc_IDBI:                 "B1",
	mutualfund.Amc_INDIABULLS:           "B1",
	mutualfund.Amc_INVESCO:              "B1",
	mutualfund.Amc_ITI:                  "B1",
	mutualfund.Amc_JM_FINANCIAL:         "B1",
	mutualfund.Amc_LIC:                  "B1",
	mutualfund.Amc_MIRAE:                "B1",
	mutualfund.Amc_MOTILAL_OSWAL:        "B1",
	mutualfund.Amc_NIPPON_LIFE:          "B1",
	mutualfund.Amc_PGIM:                 "B1",
	mutualfund.Amc_PRINCIPAL:            "B1",
	mutualfund.Amc_QUANT_MONEY_MANAGERS: "B1",
	mutualfund.Amc_QUANTUM:              "B1",
	mutualfund.Amc_SUNDARAM:             "B1",
	mutualfund.Amc_TAURUS:               "B1",
	mutualfund.Amc_UTI:                  "B1",
	mutualfund.Amc_TRUST:                "B1",

	// Agreements with the following AMCs are not signed yet
	// Post signing, payment instruments for these AMC should be configured before allowing orders
	// RTAs not applicable for AMC should be removed
	mutualfund.Amc_BAJAJ_FINSERV: "B1",
	mutualfund.Amc_HELIOS:        "B1",
	mutualfund.Amc_ZERODHA:       "B1",
	mutualfund.Amc_OLD_BRIDGE:    "B1",
}
