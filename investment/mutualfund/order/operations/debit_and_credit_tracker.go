package operations

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order/operations"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
)

var (
	// amcDebitPI represents the payment instrument from which amount gets debited during a refund or withdrawal.
	amcDebitPI = map[mfPb.Amc]string{
		mfPb.Amc_ICICI_PRUDENTIAL:     "PI210827T2M8TUroSBajr3Zydjyn7g==",
		mfPb.Amc_NIPPON_LIFE:          "PI2203247p7JBBnIRjapK0ZVmKu7NQ==",
		mfPb.Amc_QUANT_MONEY_MANAGERS: "PI210813UOVJapeTSUWuFa9ynUCOGw==",
		mfPb.Amc_AXIS:                 "PI2108062ed+EbZ3RN+w0v1Ph6NlBg==",
		mfPb.Amc_TATA:                 "PI220613yJAeTpsGRSiXiYsgYvslFQ==",
		mfPb.Amc_HDFC:                 "PI211029DC59rZpLQkqVnJp/JbpZUg==",
		mfPb.Amc_NAVI:                 "PI211116AKMBa4bLSaigrxKuQEqZPA==",
		mfPb.Amc_ADITYA_BIRLA:         "PI2107236QGSpsbuSwmih0O/blL14w==",
		mfPb.Amc_PPFAS:                "PI210906wTFK5FFkQFyO9sUWfTUPGg==",
		mfPb.Amc_MOTILAL_OSWAL:        "PI211026fW5yermKRE29nKZtUOnyEA==",
		mfPb.Amc_SBI:                  "PI2107153PyJlhEHTGC2Q1wUFxNaWw==",
		mfPb.Amc_KOTAK_MAHINDRA:       "PI210923b5yM92h9RMCv3I8pd7o/HQ==",

		// Agreements with the following AMCs are not signed yet
		// Post signing, payment instruments for these AMC should be configured before allowing orders
		// TODO(Brijesh): Fill these for reconciling orders once we have sell orders for the AMC
		mfPb.Amc_BAJAJ_FINSERV: "",
		mfPb.Amc_HELIOS:        "",
		mfPb.Amc_ZERODHA:       "",
		mfPb.Amc_OLD_BRIDGE:    "",
		mfPb.Amc_UNIFI:         "",
		mfPb.Amc_ANGEL_ONE:     "",
		mfPb.Amc_JIO_BLACKROCK: "",
	}
	// amcCreditPI represents the payment instrument to which amount gets credited to amc during purchase.
	amcCreditPI = map[mfPb.Amc]string{
		mfPb.Amc_ICICI_PRUDENTIAL:     "paymentinstrument-icici-amc-business-account",
		mfPb.Amc_NIPPON_LIFE:          "paymentinstrument-nippon-amc-business-account",
		mfPb.Amc_QUANT_MONEY_MANAGERS: "paymentinstrument-quant-amc-business-account",
		mfPb.Amc_AXIS:                 "paymentinstrument-axis-amc-business-account",
		mfPb.Amc_TATA:                 "paymentinstrument-tata-amc-business-account",
		mfPb.Amc_HDFC:                 "paymentinstrument-hdfc-amc-business-account",
		mfPb.Amc_NAVI:                 "paymentinstrument-navi-amc-business-account",
		mfPb.Amc_ADITYA_BIRLA:         "paymentinstrument-absl-amc-business-account",
		mfPb.Amc_PPFAS:                "paymentinstrument-ppfas-amc-business-account",
		mfPb.Amc_MOTILAL_OSWAL:        "paymentinstrument-motilal-oswal-amc-business-account",
		mfPb.Amc_SBI:                  "paymentinstrument-sbi-amc-business-account",
		mfPb.Amc_KOTAK_MAHINDRA:       "paymentinstrument-kotak-amc-business-account",

		// Agreements with the following AMCs are not signed yet
		// Post signing, payment instruments for these AMC should be configured before allowing orders
		mfPb.Amc_BAJAJ_FINSERV: "paymentinstrument-bajaj-finserv-amc-business-account",
		mfPb.Amc_HELIOS:        "",
		mfPb.Amc_ZERODHA:       "",
		mfPb.Amc_OLD_BRIDGE:    "",
		mfPb.Amc_UNIFI:         "",
		mfPb.Amc_ANGEL_ONE:     "",
		mfPb.Amc_JIO_BLACKROCK: "",
	}
)

func (s *Service) getAmcDebitAndCreditTracker(ctx context.Context, startTime time.Time, endTime time.Time, request *pb.GenerateMFSummaryFileRequest, timeZoneLocation *time.Location) (*pb.GenerateMFSummaryFileResponse, error) {
	if len(request.GetVendorOrderIds()) != 1 && len(request.GetVendorOrderIds()) != 0 {
		logger.Error(ctx, "only one vendor order id should be passed for generating AMC_TRANSACTIONS file", zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching pi details")}, nil
	}

	vendorOrderID := request.GetVendorOrderIds()[0]

	piResp, err := s.getPIForCustomerSavingsAccount(ctx, request)
	if err != nil {
		logger.Error(ctx, "error in getPIForCustomerSavingsAccount", zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderID), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternalWithDebugMsg("pi for amc is not configured")}, nil
	}

	debitPI, creditPI, err := s.getDebitAndCreditPI(ctx, request.GetAmc())
	if err != nil {
		logger.Error(ctx, "error in getDebitAndCreditPI", zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderID), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternalWithDebugMsg("pi for amc is not configured")}, nil
	}

	debitTransactions, err := s.fetchTransactions(ctx, piResp.GetPaymentInstrument().GetId(), creditPI, startTime, endTime)
	if err != nil {
		logger.Error(ctx, "error in fetchTransactions", zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderID), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternalWithDebugMsg("pi for amc is not configured")}, nil
	}

	creditTransactions, err := s.fetchTransactions(ctx, debitPI, piResp.GetPaymentInstrument().GetId(), startTime, endTime)
	if err != nil {
		logger.Error(ctx, "error in fetchTransactions", zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderID), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternalWithDebugMsg("pi for amc is not configured")}, nil
	}

	fileName := fmt.Sprintf(opsFileNameFormat, startTime.Format(timeLayout), endTime.Format(timeLayout))

	url, err := s.writeTransactionsAndUploadToS3(ctx, creditTransactions, debitTransactions, timeZoneLocation, fileName)
	if err != nil {
		logger.Error(ctx, "error in writeTransactionsAndUploadToS3", zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.VENDOR_ORDER_ID, vendorOrderID), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternalWithDebugMsg("pi for amc is not configured")}, nil
	}

	return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusOk(), FileUrl: url}, nil
}

func (s *Service) getPIForCustomerSavingsAccount(ctx context.Context, request *pb.GenerateMFSummaryFileRequest) (*paymentinstrument.GetPiResponse, error) {
	order, err := s.orderDao.GetByVendorOrderId(ctx, request.GetVendorOrderIds()[0])
	if err != nil {
		return nil, err
	}

	preInvestmentDetails, err := s.getPreInvestmentDetail(ctx, []*orderPb.Order{order})
	if err != nil {
		return nil, err
	}

	piResp, err := s.piClient.GetPi(ctx, &paymentinstrument.GetPiRequest{
		Type: paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &paymentinstrument.GetPiRequest_AccountRequestParams_{
			AccountRequestParams: &paymentinstrument.GetPiRequest_AccountRequestParams{
				ActualAccountNumber: preInvestmentDetails[order.ActorId].GetBankDetails().GetAccountNumber(),
				IfscCode:            preInvestmentDetails[order.ActorId].GetBankDetails().GetIfscCode(),
			},
		},
	})
	if te := epifigrpc.RPCError(piResp, err); te != nil {
		return nil, err
	}
	return piResp, nil
}

func (s *Service) getDebitAndCreditPI(_ context.Context, amc mfPb.Amc) (string, string, error) {

	debitPI, ok := amcDebitPI[amc]
	if !ok {
		return "", "", fmt.Errorf("debit pi for amc is not configured")
	}

	creditPI, ok := amcCreditPI[amc]
	if !ok {
		return "", "", fmt.Errorf("credit pi for amc is not configured")
	}

	return debitPI, creditPI, nil
}

func (s *Service) writeTransactionsAndUploadToS3(ctx context.Context, creditTransactions []*paymentPb.Transaction, debitTransactions []*paymentPb.Transaction, timeZoneLocation *time.Location, fileName string) (string, error) {
	var sb strings.Builder

	sb.WriteString(strings.Join(transactionFileFields, ","))
	sb.WriteString("\n")

	sb.WriteString("DebitTransactions\n")

	for _, order := range debitTransactions {
		value := reflect.ValueOf(order).Elem()
		var row []string

		for _, fieldName := range transactionFileFields {
			row = append(row, s.getRowValue(fieldName, value.FieldByName(fieldName), timeZoneLocation))
		}
		sb.WriteString(strings.Join(row, ","))
		sb.WriteString("\n")
	}

	sb.WriteString("\nCreditTransaction\n")

	for _, order := range creditTransactions {
		value := reflect.ValueOf(order).Elem()
		var row []string

		for _, fieldName := range transactionFileFields {
			row = append(row, s.getRowValue(fieldName, value.FieldByName(fieldName), timeZoneLocation))
		}
		sb.WriteString(strings.Join(row, ","))
		sb.WriteString("\n")
	}

	fileContent := sb.String()

	url, err := s.s3Client.WriteAndGetPreSignedUrl(ctx, fileName, []byte(fileContent), 1800)
	if err != nil {
		logger.Error(ctx, "error in WriteAndGetPreSignedUrl", zap.Error(err))
		return "", err
	}

	return url, nil
}

func (s *Service) fetchTransactions(ctx context.Context, fromPI string, toPI string, startTime time.Time, endTime time.Time) ([]*paymentPb.Transaction, error) {

	var allTxnsForPi []*paymentPb.Transaction
	for {
		var pageSize int32 = 10

		txnByPiRes, err1 := s.paymentClient.GetTxnsByPi(ctx, &paymentPb.GetTxnsByPiRequest{
			PiFrom:        fromPI,
			PiTo:          toPI,
			FromTimestamp: timestamppb.New(startTime),
			ToTimestamp:   timestamppb.New(endTime),
			SortBy:        paymentPb.TransactionFieldMask_CREATED_AT,
			PageSize:      pageSize,
			Offset:        int32(len(allTxnsForPi)),
			Statuses:      []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
		})
		// record not found is not an error case for GetTxnsByPi rpc so explicitly handling that
		// rest all non OK status codes are treated as failure.
		if err1 != nil || (!txnByPiRes.GetStatus().IsRecordNotFound() && !txnByPiRes.GetStatus().IsSuccess()) {
			logger.Error(ctx, "txnClient.GetTxnsByPi call failed", zap.Error(err1))
			return nil, errors.New("txnClient.GetTxnsByPi call failed")
		}

		allTxnsForPi = append(allTxnsForPi, txnByPiRes.GetTransactions()...)
		if len(txnByPiRes.GetTransactions()) < int(pageSize) {
			break
		}
	}
	return allTxnsForPi, nil
}
