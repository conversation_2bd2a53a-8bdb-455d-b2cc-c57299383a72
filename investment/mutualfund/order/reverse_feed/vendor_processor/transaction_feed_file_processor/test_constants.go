package transaction_feed_file_processor

import (
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	camsRevFeed "github.com/epifi/gamma/api/vendors/cams/reverse_feed"
	karvyRevFeed "github.com/epifi/gamma/api/vendors/karvy/reverse_feed"
)

//nolint:dupl
var (
	wbr2FileContentForSplitOrderByCategoriesTest = "T|12345|TDIFGZ|Tata Digital India Fund Direct Plan Growth| Sachin |P21AN|*********|M|Y|EPFIRIA|123456|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|1416.8290|49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| ||NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$123456|ABCDEFG|0||Fresh Purchase|||||0|0|| Distributor/EPIFIRIA - INA200015185|0|T|||N|||||1234567|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#123456|2.50||||\n" +
		"T|14567|TDIFGZ|Tata Digital India Fund Direct Plan Growth|ABCD  |P21AN|*********|R|Y|ABCD|123457|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|-1416.8290|-49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| |Payment not received|NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$1234643|ABCDEFG|0||Fresh Purchase|||||0|0||Payment not received Distributor/SAN|0|T|||N|||||156242|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#**********|-2.50||||\n" +
		"T|12345|TDIFGZ|Tata Digital India Fund Direct Plan Growth| Sachin |R21AN|*********|M|Y|ABCD|123458|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|1416.8290|49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| ||NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$123456|ABCDEFG|0||Fresh Purchase|||||0|0|| Distributor/EPIFIRIA - INA200015185|0|T|||N|||||1234567|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#123456|2.50||||\n" +
		"T|14567|TDIFGZ|Tata Digital India Fund Direct Plan Growth|ABCD  |SI21AN|*********|R|Y|ABCD|123459|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|-1416.8290|-49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| |Payment not received|NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$1234643|ABCDEFG|0||Fresh Purchase|||||0|0||Payment not received Distributor/SAN|0|T|||N|||||156242|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#**********|-2.50||||\n" +
		"T|12345|TDIFGZ|Tata Digital India Fund Direct Plan Growth| Sachin |SO21AN|*********|M|Y|ABCD|123460|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|1416.8290|49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| ||NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$123456|ABCDEFG|0||Fresh Purchase|||||0|0|| Distributor/EPIFIRIA - INA200015185|0|T|||N|||||1234567|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#123456|2.50||||\n" +
		"T|14567|TDIFGZ|Tata Digital India Fund Direct Plan Growth|ABCD  |SI21AN|*********|R|Y|EPFIRIA|123461|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|-1416.8290|-49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| |Payment not received|NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$1234643|ABCDEFG|0||Fresh Purchase|||||0|0||Payment not received Distributor/SAN|0|T|||N|||||156242|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#**********|-2.50||||\n" +
		"T|14567|TDIFGZ|Tata Digital India Fund Direct Plan Growth|ABCD  |SO21AN|*********|R|Y|EPFIRIA|123461|6/8/2022 12:00:00 AM|6/22/2022 12:00:00 AM|35.2883|-1416.8290|-49997.50|INA200015185| |0|0|0|7/21/2022 9:44:29 AM|          |N||Fresh Purchase|0|0|N| |Payment not received|NA||**********|Z||0|Bangalore|Equity(S)|Individual|0|EPIFIRIA$1234643|ABCDEFG|0||Fresh Purchase|||||0|0||Payment not received Distributor/SAN|0|T|||N|||||156242|FEDERAL BANK|1209|||27|0|0|0|Payment not received|EPIFIRIA#**********|-2.50||||"
	wbr2InternalOrdersForSplitOrderByCategoriesTest       = []*camsRevFeed.WBR2File{{AMCCode: "T", FolioNumber: "12345", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: " Sachin ", TransactionTypeCode: "P21AN", TransactionNumber: "*********", TransactionMode: "M", TransactionStatus: "Y", SourceCode: "EPFIRIA", SourceSerialNumber: "123456", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "1416.8290", Amount: "49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$123456", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: " Distributor/EPIFIRIA - INA200015185", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "1234567", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#123456", StampDuty: "2.50"}}
	wbr2ExternalBuyOrdersForSplitOrderByCategoriesTest    = map[mfPb.Amc]map[orderPb.OrderSubType][]*camsRevFeed.WBR2File{mfPb.Amc_TATA: {orderPb.OrderSubType_BUY_EXTERNAL: []*camsRevFeed.WBR2File{{AMCCode: "T", FolioNumber: "14567", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: "ABCD  ", TransactionTypeCode: "P21AN", TransactionNumber: "*********", TransactionMode: "R", TransactionStatus: "Y", SourceCode: "ABCD", SourceSerialNumber: "123457", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "-1416.8290", Amount: "-49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "Payment not received", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$1234643", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: "Payment not received Distributor/SAN", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "156242", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#**********", StampDuty: "-2.50"}}}}
	wbr2ExternalSellOrdersForSplitOrderByCategoriesTest   = map[mfPb.Amc]map[orderPb.OrderSubType][]*camsRevFeed.WBR2File{mfPb.Amc_TATA: {orderPb.OrderSubType_SELL_EXTERNAL: []*camsRevFeed.WBR2File{{AMCCode: "T", FolioNumber: "12345", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: " Sachin ", TransactionTypeCode: "R21AN", TransactionNumber: "*********", TransactionMode: "M", TransactionStatus: "Y", SourceCode: "ABCD", SourceSerialNumber: "123458", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "1416.8290", Amount: "49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$123456", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: " Distributor/EPIFIRIA - INA200015185", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "1234567", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#123456", StampDuty: "2.50"}}}}
	wbr2ExternalSwitchOrdersForSplitOrderByCategoriesTest = map[mfPb.Amc]map[orderPb.OrderSubType][]*camsRevFeed.WBR2File{mfPb.Amc_TATA: {orderPb.OrderSubType_SWITCH_OUT_EXTERNAL: []*camsRevFeed.WBR2File{{AMCCode: "T", FolioNumber: "12345", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: " Sachin ", TransactionTypeCode: "SO21AN", TransactionNumber: "*********", TransactionMode: "M", TransactionStatus: "Y", SourceCode: "ABCD", SourceSerialNumber: "123460", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "1416.8290", Amount: "49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$123456", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: " Distributor/EPIFIRIA - INA200015185", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "1234567", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#123456", StampDuty: "2.50"}},
		orderPb.OrderSubType_SWITCH_IN_EXTERNAL: []*camsRevFeed.WBR2File{{AMCCode: "T", FolioNumber: "14567", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: "ABCD  ", TransactionTypeCode: "SI21AN", TransactionNumber: "*********", TransactionMode: "R", TransactionStatus: "Y", SourceCode: "ABCD", SourceSerialNumber: "123459", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "-1416.8290", Amount: "-49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "Payment not received", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$1234643", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: "Payment not received Distributor/SAN", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "156242", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#**********", StampDuty: "-2.50"}}}}
	wbr2InternalSwitchOrdersForSplitOrderByCategoriesTest = []*camsRevFeed.WBR2File{{AMCCode: "T", FolioNumber: "14567", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: "ABCD  ", TransactionTypeCode: "SI21AN", TransactionNumber: "*********", TransactionMode: "R", TransactionStatus: "Y", SourceCode: "EPFIRIA", SourceSerialNumber: "123461", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "-1416.8290", Amount: "-49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "Payment not received", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$1234643", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: "Payment not received Distributor/SAN", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "156242", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#**********", StampDuty: "-2.50"}, {AMCCode: "T", FolioNumber: "14567", ProductCode: "TDIFGZ", ProductName: "Tata Digital India Fund Direct Plan Growth", InvestorName: "ABCD  ", TransactionTypeCode: "SO21AN", TransactionNumber: "*********", TransactionMode: "R", TransactionStatus: "Y", SourceCode: "EPFIRIA", SourceSerialNumber: "123461", TradeDate: "6/8/2022 12:00:00 AM", PostedDate: "6/22/2022 12:00:00 AM", Price: "35.2883", Units: "-1416.8290", Amount: "-49997.50", BrokerDealerCode: "INA200015185", SubbrokerDealerCode: " ", BrokeragePercentage: "0", BrokerageAmount: "0", InvestorIdentification: "0", DateOfThisDataFeed: "7/21/2022 9:44:29 AM", TimeOfThisDataFeedTIME1: "          ", TransactionSubtype: "N", ApplicationNo: "", TransactionNature: "Fresh Purchase", BasicTDS: "0", TotalTDS: "0", Form15H: "N", MICRNo: " ", Remarks: "Payment not received", Swflag: "NA", OldFolioNo: "", SequenceNo: "**********", ReinvestFlag: "Z", MultBrok: "", Stt: "0", Location: "Bangalore", SchemeType: "Equity(S)", TaxStatus: "Individual", EntryLoad: "0", ScanRefNo: "EPIFIRIA$1234643", Pan: "ABCDEFG", Min: "0", TargSrcScheme: "", TrxnTypeFlag: "Fresh Purchase", TICOBTrxnType: "", TICOBTrxnNo: "", TICOBPostedDate: "", DPID: "", TrxnCharges: "0", EligibAmt: "0", SrcOfTrxn: "", TrxnSuffix: "Payment not received Distributor/SAN", Siptrxnno: "0", TerLocati: "T", Euin: "", EuinValid: "", EuinOpted: "N", SubBrkAr: "", ExchDcFlag: "", SrcBrkCode: "", SysRegnDate: "", AcNo: "156242", BankName: "FEDERAL BANK", ReversalCode: "1209", ExchangeFlag: "", CAInitiatedDate: "", GSTStateCode: "27", IgstAmount: "0", CgstAmount: "0", SgstAmount: "0", EditTrxnCaID: "Payment not received", OriginalTransactionNumber: "EPIFIRIA#**********", StampDuty: "-2.50"}}

	wbr201FileContentForSplitOrderByCategoriesTest = "FMCODE,TD_FUND,TD_ACNO,DIVOPT,FUNDDESC,TD_TRNO,INVNAME,TRNMODE,TRNSTAT,TD_PRDT,TD_UNITS,TD_AMT,TD_AGENT,TD_BROKER,CRDATE,TD_APPNO,TRDESC,TD_TRTYPE,NAVDATE,ASSETTYPE,SUBTRTYPE,CITYCATEG5,EUIN,TRCHARGES,CLIENTID,DPID,STT,IHNO,BRANCHCODE,INWARDNUM1,PAN1,TDSAMOUNT,LOAD1,STATUS,TD_NAV,EVALID,EDECLFLAG,SUBARNCODE,SIPREGDT,SIPREGSLNO,CAN,STAMPDUTY,TRFLAG,ALTFOLIO,FTACCNO,REJTRNOOR2,TD_POP,CHQNO,PRCODE1,REVERSAL_C,TD_TRXNMOD,REMARKS,DUMMY1,DUMMY2,DUMMY3,DUMMY4,DUMMY5,DUMMY6,DUMMY7,DUMMY8,DUMMY9\n" +
		"127NIGD,127,***********,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201453,Name1,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,NEW,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,R999,7986673796,ABDCDE,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n" +
		"127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,ADD,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,3712520370,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n" +
		"127NIGD,127,***********,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201453,Name1,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,RED,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,7986673791,ABDCDE,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n" +
		"127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWIN,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,7986673711,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n" +
		"127NIGD,127,***********,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201453,Name1,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWOF,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,7986673745,ABDCDE,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n" +
		"127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWIN,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,R999,7986673767,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n" +
		"127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWOF,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,R999,7986673767,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,"
	wbr201InternalOrdersForSplitOrderByCategoriesTest       = []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "***********", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201453", InvName: "Name1", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "NEW", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "R999", USRTRXNOInward: "7986673796", PAN: "ABDCDE", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}
	wbr201ExternalBuyOrdersForSplitOrderByCategoriesTest    = map[mfPb.Amc]map[orderPb.OrderSubType][]*karvyRevFeed.WBR201File{mfPb.Amc_MOTILAL_OSWAL: {orderPb.OrderSubType_BUY_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "ADD", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "3712520370", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}}}
	wbr201ExternalSellOrdersForSplitOrderByCategoriesTest   = map[mfPb.Amc]map[orderPb.OrderSubType][]*karvyRevFeed.WBR201File{mfPb.Amc_MOTILAL_OSWAL: {orderPb.OrderSubType_SELL_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "***********", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201453", InvName: "Name1", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "RED", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "7986673791", PAN: "ABDCDE", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}}}
	wbr201ExternalSwitchOrdersForSplitOrderByCategoriesTest = map[mfPb.Amc]map[orderPb.OrderSubType][]*karvyRevFeed.WBR201File{mfPb.Amc_MOTILAL_OSWAL: {orderPb.OrderSubType_SWITCH_IN_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWIN", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "7986673711", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}},
		orderPb.OrderSubType_SWITCH_OUT_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "***********", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201453", InvName: "Name1", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWOF", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "7986673745", PAN: "ABDCDE", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}}}
	wbr201InternalSwitchOrdersForSplitOrderByCategoriesTest = []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWIN", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "R999", USRTRXNOInward: "7986673767", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}, {PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWOF", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "R999", USRTRXNOInward: "7986673767", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}
)
