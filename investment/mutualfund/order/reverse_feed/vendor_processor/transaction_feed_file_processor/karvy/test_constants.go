package karvy

import (
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	karvyRevFeed "github.com/epifi/gamma/api/vendors/karvy/reverse_feed"
)

//nolint:dupl
var (
	wbr201FileContentForWBR201FeedFileProcessor          = "FMCODE,TD_FUND,TD_ACNO,DIVOPT,FUNDDESC,TD_TRNO,INVNAME,TRNMODE,TRNSTAT,TD_PRDT,TD_UNITS,TD_AMT,TD_AGENT,TD_BROKER,CRDATE,TD_APPNO,TRDESC,TD_TRTYPE,NAVDATE,ASSETTYPE,SUBTRTYPE,CITYCATEG5,EUIN,TRCHARGES,CLIENTID,DPID,STT,IHNO,BRANCHCODE,INWARDNUM1,<PERSON>N1,TDSA<PERSON>UNT,LOAD1,STAT<PERSON>,TD_N<PERSON>,EVALID,EDECLFLAG,SUBARNCODE,SIPREGDT,SIPREGSLNO,CAN,STAMPDUTY,TR<PERSON>AG,ALTFOLIO,FTACCNO,REJTRNOOR2,TD_POP,CHQNO,PRCODE1,REVERSAL_C,TD_TRXNMOD,REMARKS,DUMMY1,DUMMY2,DUMMY3,DUMMY4,DUMMY5,DUMMY6,DUMMY7,DUMMY8,DUMMY9\n127NIGD,127,91032733095,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201453,Name1,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,NEW,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,R999,7986673796,ABDCDE,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,ADD,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,3712520370,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n127NIGD,127,91032733095,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201453,Name1,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,RED,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,7986673791,ABDCDE,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWIN,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,7986673711,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n127NIGD,127,91032733095,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201453,Name1,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWOF,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,ABCD,7986673745,ABDCDE,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWIN,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,R999,7986673767,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n127NIGD,127,91032733291,G,Motilal Oswal Nifty 50 Index Fund - Direct Plan,2201597,Name2,N,Y,27/05/2022,37.027,499.98,INA200015185,,18/07/2022,,Purchase,SWOF,26/05/2022,EQUITY FUND,,BEYOND 15 CITIES,,,,,0.00,*********,R999,7986673767,ABDCDED,,0.00,INDIVIDUAL,13.5033,,Y,,,0,,0.02,P,,,,13.5033,111111,,0,,,,,,,,,,,,\n"
	wbr201InternalOrdersForWBR201FeedFileProcessor       = []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733095", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201453", InvName: "Name1", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "NEW", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "R999", USRTRXNOInward: "7986673796", PAN: "ABDCDE", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}
	wbr201ExternalBuyOrdersForWBR201FeedFileProcessor    = map[mfPb.Amc]map[orderPb.OrderSubType][]*karvyRevFeed.WBR201File{mfPb.Amc_MOTILAL_OSWAL: {orderPb.OrderSubType_BUY_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "ADD", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "3712520370", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}}}
	wbr201ExternalSellOrdersForWBR201FeedFileProcessor   = map[mfPb.Amc]map[orderPb.OrderSubType][]*karvyRevFeed.WBR201File{mfPb.Amc_MOTILAL_OSWAL: {orderPb.OrderSubType_SELL_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733095", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201453", InvName: "Name1", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "RED", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "7986673791", PAN: "ABDCDE", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}}}
	wbr201InternalSwitchOrdersForWBR201FeedFileProcessor = []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWIN", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "R999", USRTRXNOInward: "7986673767", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}, {PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWOF", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "R999", USRTRXNOInward: "7986673767", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}
	wbr201ExternalSwitchOrdersForWBR201FeedFileProcessor = map[mfPb.Amc]map[orderPb.OrderSubType][]*karvyRevFeed.WBR201File{mfPb.Amc_MOTILAL_OSWAL: {orderPb.OrderSubType_SWITCH_IN_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733291", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201597", InvName: "Name2", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWIN", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "7986673711", PAN: "ABDCDED", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}, orderPb.OrderSubType_SWITCH_OUT_EXTERNAL: []*karvyRevFeed.WBR201File{{PRODCODE: "127NIGD", AmcCode: "127", FolioNo: "91032733095", DIVOPT: "G", SCHEME: "Motilal Oswal Nifty 50 Index Fund - Direct Plan", TRXNNO: "2201453", InvName: "Name1", TRXNMODE: "N", TRXNSTAT: "Y", POSTDATE: "27/05/2022", UNITS: "37.027", Amount: "499.98", BROKCODE: "INA200015185", SUBBROK: "", RepDate: "18/07/2022", APPLICATION: "", TrxnNature: "Purchase", TRXNTYPE: "SWOF", TRADDATE: "26/05/2022", SchemeType: "EQUITY FUND", TRXNSUBTYPE: "", TerLocation: "BEYOND 15 CITIES", EUIN: "", TrxnCharge: "", ClientID: "", DPID: "", STT: "0.00", USRTRXNOIhno: "*********", USERCODE: "ABCD", USRTRXNOInward: "7986673745", PAN: "ABDCDE", TotalTDS: "", LOAD: "0.00", TaxStatus: "INDIVIDUAL", NAV: "13.5033", EuinValid: "", EuinOpted: "Y", SubBrkArn: "", SysRegnDate: "", SIPTRXNNO: "0", CAN: "", StampDuty: "0.02", TRFlag: "P", ALTFolio: "", FTACCNO: "", REJTRNOOR2: "", PURPRICE: "13.5033", CHQNO: "111111", TargSrcS: "", ReversalC: "0", ExchangeF: "", Remarks: ""}}}}
)
