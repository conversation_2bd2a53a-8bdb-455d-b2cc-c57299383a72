package activity

import (
	"context"
	"fmt"
	"sort"
	"time"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	mfActivity "github.com/epifi/gamma/api/investment/mutualfund/activity"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	wec "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor/withdrawable_entity_calculator"
	"github.com/epifi/gamma/investment/mutualfund/reconciliation/folio_recon"
	"github.com/epifi/gamma/pkg/investment"
)

const (
	updateFolioFromAUM = true
	createFolioFromAUM = false
)

func (p *Processor) CorrectFolioMismatch(ctx context.Context, req *mfActivity.CorrectFolioMismatchRequest) (*mfActivity.CorrectFolioMismatchResponse, error) {
	mismatchesMap, err := p.getGroupedFolioMap(ctx, req.GetGroupedFolioMapUrl())
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	folioStatusInfo, err1 := p.getFolioStatusInfo(ctx, req.GetFolioStatusUrl())
	if err1 != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err1.Error())
	}

	for actorId, amcMap := range mismatchesMap.GetActorIdMap() {
		for amcString := range amcMap.GetAmcMap() {
			amc := mfPb.Amc(mfPb.Amc_value[amcString])
			logger.Info(ctx, fmt.Sprintf("invoking  CorrectMismatchForActorAndAmc for actor: %s, amc: %s", actorId, amc.String()))

			err2 := p.correctMismatchesForActorAndAmc(ctx, actorId, amc, mismatchesMap.GetActorIdMap()[actorId].GetAmcMap()[amcString], folioStatusInfo)
			if err2 != nil {
				logger.Info(ctx, "error in CorrectMismatchForActorAndAmc", zap.Error(err2), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.MF_AMC, amc.String()))
				continue
			}
		}
	}

	err = p.writeToS3FolioStatusUrl(ctx, folioStatusInfo, req.GetFolioStatusUrl())
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	return &mfActivity.CorrectFolioMismatchResponse{
		FolioStatusUrl: req.GetFolioStatusUrl(),
	}, nil
}

func (p *Processor) correctMismatchesForActorAndAmc(ctx context.Context,
	actorId string,
	amc mfPb.Amc,
	mutualFundIdToFolioIDToDetailsMap *mfPb.MutualFundMap,
	folioStatusInfo *mfPb.FolioStatusInfo) error {

	mutualFunds, mfErr := p.fetchAllMutualFundsByAmc(ctx, amc)
	if mfErr != nil {
		logger.Error(ctx, "error when fetching mf schemes under amc", zap.String(logger.MF_AMC, string(amc)), zap.Error(mfErr))
		folioStatusInfo.FolioStatus = append(folioStatusInfo.FolioStatus, &mfPb.FolioInfo{
			Remarks: fmt.Sprintf("error when fetching mf schemes under amc: %v", amc.String()),
		})
		return mfErr
	}

	var mutualFundIDsForAMc []string

	for _, fund := range mutualFunds {
		mutualFundIDsForAMc = append(mutualFundIDsForAMc, fund.GetId())
	}

	logger.Info(ctx, fmt.Sprintf("calling reconcileMismatchFoliosForActorAndAmc"))
	folioReconErr := p.reconcileMismatchFoliosForActorAndAmc(ctx, actorId, mutualFundIDsForAMc, mutualFundIdToFolioIDToDetailsMap)
	if folioReconErr != nil {
		logger.Error(ctx, "error in reconcileMismatchFoliosForActorAndAmc", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(folioReconErr))
		folioStatusInfo.FolioStatus = append(folioStatusInfo.FolioStatus, &mfPb.FolioInfo{
			Status:  mfPb.FolioInfo_STATUS_FAILED,
			Remarks: fmt.Sprintf("error when running folio recon for actorId: %v, amc: %v, err: %v", actorId, amc.String(), folioReconErr),
		})
		return folioReconErr
	}
	return nil
}

func (p *Processor) reconcileMismatchFoliosForActorAndAmc(ctx context.Context,
	actorId string,
	mfIDsForAmc []string,
	mutualFundIdToFolioIDToDetailsMap *mfPb.MutualFundMap) error {
	logger.Info(ctx, fmt.Sprintf("inside reconcileMismatchFoliosForActorAndAmc"))
	txnErr := p.idempotentTxnExecutor.RunIdempotentTxn(ctx, uint(2), func(txnCtx context.Context) error {
		folios, folioFetchErr := p.folioLedgerDao.GetByFilterOptions(txnCtx, mfPb.FolioLedgerMask_CREATED_AT, true, dao.WithActorId(actorId), dao.WithMutualFundIds(mfIDsForAmc))
		if folioFetchErr != nil {
			logger.Info(txnCtx, "error in GetByFilterOptions", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(folioFetchErr))
			return errors.Wrap(folioFetchErr, fmt.Sprintf("error in GetByFilterOptions for actorId: %v", actorId))
		}
		folioNumberAndMFIdToLedgerMap := groupFoliosByFolioAndMFID(folios)
		// logger.Info(txnCtx, fmt.Sprintf("folioNumberAndMFIdToLedgerMap length: %v", len(folioNumberAndMFIdToLedgerMap)))
		for mfId, folioIdToFolioDetailsMap := range mutualFundIdToFolioIDToDetailsMap.GetMutualFundMap() {
			for folioId, folioDetails := range folioIdToFolioDetailsMap.GetFolioMap() {
				if p.checkIfOrderConfirmationForFolioIsPending(ctx, folioId) {
					logger.Info(txnCtx, "orders pending confirmation from vendor, skipping AUM update", zap.String(logger.FOLIO_ID, folioId))
					continue
				}
				// logger.Info(txnCtx, fmt.Sprintf("Performing recon for folio: %v", folioId))
				folioLedgerVal, ok := folioNumberAndMFIdToLedgerMap[folioId][mfId]
				// creating a new folio if we do not have corresponding db entry for folioId, mfId and
				// folio units getting from AUM file is non-zero
				if !ok {
					createFolioLedgerErr := p.createFolioLedger(ctx, actorId, folioId, mfId, folioDetails)
					if createFolioLedgerErr != nil {
						return errors.Wrapf(createFolioLedgerErr, "error in createFolioLedger for folioId: %v, mfId: %v", folioId, mfId)
					}
				} else {
					updateFolioLedgerErr := p.calculateAvgPurchaseNavAndUpdateFolioLedger(ctx, folioId, mfId, folioDetails.GetRtaUnits(), folioDetails, folioLedgerVal)
					if updateFolioLedgerErr != nil {
						return errors.Wrapf(updateFolioLedgerErr, "error in createFolioLedger for folioId: %v, mfId: %v", folioId, mfId)
					}
				}
			}
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error in txnExecutor", zap.Error(txnErr))
		return txnErr
	}
	return nil
}

func (p *Processor) fetchAllMutualFundsByAmc(ctx context.Context, amc mfPb.Amc) ([]*mfPb.MutualFund, error) {
	var funds []*mfPb.MutualFund
	var res *catalogPb.GetPaginatedMutualFundByFilterResponse
	var err error

	res, err = p.catalogManagerClient.GetPaginatedMutualFundByFilter(ctx, &catalogPb.GetPaginatedMutualFundByFilterRequest{
		Amc:         amc,
		PageContext: &rpc.PageContextRequest{PageSize: uint32(500)},
		SortParam:   mfPb.MutualFundFieldMask_CURRENT_AUM,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, te
	}
	funds = append(funds, res.GetMutualFunds()...)
	for res.GetPageContext().GetHasAfter() {
		res, err = p.catalogManagerClient.GetPaginatedMutualFundByFilter(ctx, &catalogPb.GetPaginatedMutualFundByFilterRequest{
			Amc: amc,
			PageContext: &rpc.PageContextRequest{
				Token: &rpc.PageContextRequest_AfterToken{
					AfterToken: res.GetPageContext().GetAfterToken(),
				},
				PageSize: uint32(500),
			},
			SortParam: mfPb.MutualFundFieldMask_CURRENT_AUM,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			return nil, te
		}
		funds = append(funds, res.GetMutualFunds()...)
	}

	logger.Debug(ctx, "total funds for asset class", zap.Int(logger.LENGTH, len(funds)))

	return funds, nil
}

func groupFoliosByFolioAndMFID(folios []*mfPb.FolioLedger) map[string]map[string]*mfPb.FolioLedger {
	folioAndMFIdToFolioMap := make(map[string]map[string]*mfPb.FolioLedger)
	for _, val := range folios {
		_, ok := folioAndMFIdToFolioMap[val.GetFolioId()]
		if !ok {
			folioAndMFIdToFolioMap[val.GetFolioId()] = make(map[string]*mfPb.FolioLedger)
		}
		folioAndMFIdToFolioMap[val.GetFolioId()][val.GetMutualFundId()] = val
	}
	return folioAndMFIdToFolioMap
}

func (p *Processor) checkIfOrderConfirmationForFolioIsPending(ctx context.Context, folioId string) bool {
	orders, oErr := p.orderDao.GetByFolioIDAndFilterOptions(ctx, folioId, dao.WithMfOrderStatus([]string{
		orderPb.OrderStatus_IN_FULFILLMENT.String(),
		orderPb.OrderStatus_NOTIFYING_PAYMENT_CREDIT.String(),
	}))
	if oErr != nil {
		logger.Error(ctx, "error in checking pending orders", zap.Error(oErr))
		return false
	}
	for _, order := range orders {
		if isOrderCreatedBeforeExpectedConfirmationDate(order) {
			logger.Info(ctx, "order created before expected confirmation and still pending", zap.String(logger.VENDOR_ORDER_ID, order.GetVendorOrderId()),
				zap.String(logger.ORDER_ID, order.GetId()))
			return true
		}
	}
	return false
}

func isOrderCreatedBeforeExpectedConfirmationDate(order *orderPb.Order) bool {
	expectedDateForConfirmedOrders := map[string]time.Time{}
	createdTime := order.GetCreatedAt().AsTime()
	today := time.Now().Format("2006-01-02")
	if expectedTime, ok := expectedDateForConfirmedOrders[today]; ok {
		return createdTime.Before(expectedTime)
	}
	expectedDays := 1
	currentExpectedTime := datetime.StartOfDay(time.Now())
	for i := 0; i < expectedDays; i++ {
		currentExpectedTime = currentExpectedTime.AddDate(0, 0, -1)
		if investment.IsWeekend(currentExpectedTime) || investment.IsSebiHoliday(currentExpectedTime) {
			expectedDays++
		}
	}
	expectedDateForConfirmedOrders[today] = currentExpectedTime
	return createdTime.Before(currentExpectedTime)
}

func (p *Processor) getTerminalOrdersSortedByAllotmentDateForFolio(ctx context.Context, folioId string) ([]*orderPb.Order, error) {
	successfulTerminalOrders, err := p.getTerminalMFOrdersForFolio(ctx, folioId)
	if err != nil {
		return nil, err
	}
	if len(successfulTerminalOrders) == 0 {
		logger.Info(ctx, "no terminal MF orders found for folio", zap.String(logger.FOLIO_ID, folioId))
		return nil, nil
	}

	var sortError error
	successfulTerminalOrders, sortError = p.sortOrdersByRTAConfirmationTime(ctx, successfulTerminalOrders)
	if sortError != nil {
		return nil, sortError
	}
	return successfulTerminalOrders, nil
}

func (p *Processor) getTerminalMFOrdersForFolio(ctx context.Context, folioId string) ([]*orderPb.Order, error) {
	orders := make([]*orderPb.Order, 0)
	orderStatus := []string{
		orderPb.OrderStatus_CONFIRMED_BY_RTA.String(),
		orderPb.OrderStatus_SETTLED.String(),
		orderPb.OrderStatus_IN_SETTLEMENT.String(),
	}

	// terminal MF BUY order are in order status confirmed by rta
	orderIdToOrderMap, daoErr := p.orderDao.GetByFolioIDAndFilterOptions(ctx, folioId,
		dao.WithMfOrderStatus(orderStatus))
	if daoErr != nil {
		if errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no terminal orders for folio", zap.String(logger.FOLIO_ID, folioId))
			return nil, nil
		}
		return nil, errors.Wrap(daoErr, fmt.Sprintf("error getting by folio id: %s", folioId))
	}

	for _, order := range orderIdToOrderMap {
		orders = append(orders, order)
	}

	logger.Info(ctx, fmt.Sprintf("fetched: %d terminal BUY orders for folio id: %s", len(orders), folioId))
	return orders, nil
}

func (p *Processor) sortOrdersByRTAConfirmationTime(ctx context.Context, orders []*orderPb.Order) ([]*orderPb.Order, error) {

	orderIDToAllocationDateMap := make(map[string]*timestamp.Timestamp)

	for _, val := range orders {
		orderAllocationDate, err := p.getOrderAllotmentDate(ctx, val)
		if err != nil {
			return nil, err
		}
		orderIDToAllocationDateMap[val.GetId()] = orderAllocationDate
	}

	sort.Slice(orders, func(i, j int) bool {
		cmp := orderIDToAllocationDateMap[orders[i].GetId()].AsTime().Compare(orderIDToAllocationDateMap[orders[j].GetId()].AsTime())
		if cmp == 0 {
			return orders[i].GetOrderType() == orderPb.OrderType_BUY
		}
		return cmp < 0
	})

	return orders, nil
}

func (p *Processor) getOrderAllotmentDate(ctx context.Context, order *orderPb.Order) (*timestamp.Timestamp, error) {

	orderConfirmationInfo, err := p.orderConfirmationDao.GetByOrderId(ctx, order.GetId())
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("error fetching order confirmation info : %s", order.GetId()))
	}
	return orderConfirmationInfo.GetOrderConfirmationMetadata().GetAllotmentDate(), nil
}

func filterOutUnexpectedMFIds(orders []*orderPb.Order, mfId string) []*orderPb.Order {
	return lo.Filter(orders, func(order *orderPb.Order, _ int) bool {
		return order.GetMutualFundId() == mfId
	})
}

func (p *Processor) getDateWiseAvailableUnits(ctx context.Context, orders []*orderPb.Order) (*mfPb.DateWiseAvailableUnits, error) {
	var dateWiseAvailableUnits *mfPb.DateWiseAvailableUnits = nil

	for _, order := range orders {
		orderAllotmentDate, err := p.getOrderAllotmentDate(ctx, order)
		if err != nil {
			return nil, err
		}

		if order.GetOrderType() == orderPb.OrderType_BUY {
			dateWiseAvailableUnits = wec.AddDateToAvailableUnits(ctx, dateWiseAvailableUnits, order.GetRtaConfirmedUnits(), datetime.TimestampToDateInLoc(orderAllotmentDate, datetime.IST))
		} else {
			dateWiseAvailableUnits, err = wec.SubtractDateToAvailableUnits(ctx, dateWiseAvailableUnits, order.GetRtaConfirmedUnits())
			if err != nil {
				return nil, errors.Wrap(err, fmt.Sprintf("error in subtracting units for order: %s", order.GetId()))
			}
		}
	}
	return dateWiseAvailableUnits, nil
}

func (p *Processor) createFolioLedger(ctx context.Context, actorId, folioId, mfId string, folioDetails *mfPb.FolioDetails) error {
	if !createFolioFromAUM {
		logger.Info(ctx, "Not creating folio ledger", zap.String("folioId", folioId))
		return nil
	}
	balanceUnits := folioDetails.GetRtaUnits()
	logger.Info(ctx, fmt.Sprintf("Unable to fetch folio for Folio id: %v and MF id: %v from DB", folioId, mfId))
	if balanceUnits > 0 {
		folioInfo := &mfPb.FolioLedger{
			FolioId:                 folioId,
			ActorId:                 actorId,
			MutualFundId:            mfId,
			BalanceUnits:            balanceUnits,
			AvgPurchaseNav:          money.ParseFloat(balanceUnits, "INR"),
			Provenance:              mfPb.FolioCreationProvenance_FolioCreationProvenance_RECONCILIATION,
			LastTransactionPostedAt: folioDetails.GetPostDate(),
			FolioUpdateProvenance:   mfPb.FolioUpdationProvenance_FUP_AUM_FILE,
		}
		logger.Info(ctx, fmt.Sprintf("FOLIO CHANGES: CREATING FOLIO folio details from AUM: %v, creating folio: %v", folioInfo, folioInfo))
		_, folioCreationErr := p.folioLedgerDao.Create(ctx, folioInfo)
		if folioCreationErr != nil {
			return folioCreationErr
		}
	} else {
		logger.Info(ctx, "Not Creating Folio because getting 0 folio units from AUM file for folio not present in DB",
			zap.String(logger.FOLIO_ID, folioId), zap.String(logger.MF_ID, mfId), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	return nil
}

func (p *Processor) calculateAvgPurchaseNavAndUpdateFolioLedger(ctx context.Context, folioId, mfId string, balanceUnits float64, folioDetails *mfPb.FolioDetails, folioLedgerVal *mfPb.FolioLedger) error {
	if len(folioLedgerVal.GetId()) == 0 {
		return fmt.Errorf("empty primary key for folio")
	}
	orders, oErr := p.getTerminalOrdersSortedByAllotmentDateForFolio(ctx, folioId)
	if oErr != nil {
		return oErr
	}
	orders = filterOutUnexpectedMFIds(orders, mfId)

	avgPurchaseNav, aErr := folio_recon.GetAvgPurchaseNavFifo(orders)
	if aErr != nil {
		return aErr
	}
	dateWiseAvailableUnits, dateWiseAvailableUnitsErr := p.getDateWiseAvailableUnits(ctx, orders)
	if dateWiseAvailableUnitsErr != nil {
		return dateWiseAvailableUnitsErr
	}

	logger.Info(ctx, fmt.Sprintf("FOLIO CHANGES: UPDATING FOLIO [folioId, mfId]: (%v, %v) existing folio details from DB (balance_units, avg_purchase_nav): (%v, %v)"+
		" folio details from AUM (balance_units, avg_purchase_nav): (%v, %v) updating folio (balance_units, avg_purchase_nav): (%v, %v)",
		folioId, mfId, folioLedgerVal.GetBalanceUnits(), folioLedgerVal.GetAvgPurchaseNav(), balanceUnits, avgPurchaseNav, balanceUnits, avgPurchaseNav,
	))

	folioLedgerVal.BalanceUnits = balanceUnits
	folioLedgerVal.AvgPurchaseNav = avgPurchaseNav
	folioLedgerVal.DateWiseAvailableUnits = dateWiseAvailableUnits
	folioLedgerVal.LastTransactionPostedAt = folioDetails.GetPostDate()
	folioLedgerVal.FolioUpdateProvenance = mfPb.FolioUpdationProvenance_FUP_AUM_FILE
	if updateFolioFromAUM {
		_, folioUpdateErr := p.folioLedgerDao.Update(ctx, folioLedgerVal, []mfPb.FolioLedgerMask{
			mfPb.FolioLedgerMask_BALANCE_UNITS,
			mfPb.FolioLedgerMask_LAST_TRANSACTION_POSTED_AT,
			mfPb.FolioLedgerMask_FOLIO_UPDATE_PROVENANCE,
			mfPb.FolioLedgerMask_AVG_PURCHASE_NAV,
			mfPb.FolioLedgerMask_DATE_WISE_AVALIABLE_UNITS,
		})
		if folioUpdateErr != nil {
			return folioUpdateErr
		}
	}
	return nil
}

func (p *Processor) getGroupedFolioMap(ctx context.Context, groupedFolioMapUrl string) (*mfPb.ActorIdMap, error) {
	groupedFolioMapBytes, err := p.s3Client.Read(ctx, groupedFolioMapUrl)
	if err != nil {
		return nil, err
	}
	mismatchesMap := &mfPb.ActorIdMap{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(groupedFolioMapBytes, mismatchesMap); unmarshalErr != nil {
		return nil, unmarshalErr
	}
	return mismatchesMap, nil
}

func (p *Processor) getFolioStatusInfo(ctx context.Context, folioStatusUrl string) (*mfPb.FolioStatusInfo, error) {
	folioStatusUrlInfoBytes, err1 := p.s3Client.Read(ctx, folioStatusUrl)
	if err1 != nil {
		return nil, err1
	}
	folioStatusInfo := &mfPb.FolioStatusInfo{}
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if unmarshalErr := unmarshalOptions.Unmarshal(folioStatusUrlInfoBytes, folioStatusInfo); unmarshalErr != nil {
		return nil, unmarshalErr
	}
	return folioStatusInfo, nil
}

func (p *Processor) writeToS3FolioStatusUrl(ctx context.Context, folioStatusInfo *mfPb.FolioStatusInfo, folioStatusUrl string) error {
	folioStatusInfoBytes, err := protojson.Marshal(folioStatusInfo)
	if err != nil {
		logger.Error(ctx, "error in marshaling folioStatusInfo", zap.Error(err))
		return err
	}
	err1 := p.s3Client.Write(ctx, folioStatusUrl, folioStatusInfoBytes, string(awsS3.ObjectCannedACLBucketOwnerFullControl))
	if err1 != nil {
		logger.Error(ctx, "error while writing folioStatusInfo to s3", zap.Error(err1))
		return err1
	}
	return nil
}
