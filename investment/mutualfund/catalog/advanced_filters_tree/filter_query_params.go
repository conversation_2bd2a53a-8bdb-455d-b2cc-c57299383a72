package advanced_filters_tree

import (
	"time"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
)

type FilterType uint32

const (
	FilterType_UNSPECIFIED FilterType = iota
	// FilterType_TERMED Does an exact key value match
	FilterType_TERMED
	// FilterType_MULTI_TERMED_AND Does an exact match for a list of values. Would be a hit only if all of the terms in the list matches.
	FilterType_MULTI_TERMED_AND
	// FilterType_RANGED Checks if a numeric value is within a given range
	FilterType_RANGED
	// FilterType_DATE_RANGED Checks if a date value is within a given date range
	FilterType_DATE_RANGED
	// FilterType_MULTI_TERMED_OR Does an exact match for a list of values. Would be a hit if any one of the terms in the list matches.
	FilterType_MULTI_TERMED_OR
)

type QueryParam struct {
	QueryType  FilterType
	QueryField string
	// if filter type is RANGED then upper bound and lower bound is stored
	QueryUpperBound float64
	QueryLowerBound float64
	// if filter type is TERMED then QueryTerm is stored
	QueryTerm string
	// if filter type is MULTI_TERMED  then QueryTerms is stored
	QueryTerms []string
	// if filter type is DATE_RANGED then
	QueryDateLowerBound time.Time
	QueryDateUpperBound time.Time
	QueryDateFormat     string
}

var filterQueryParam = map[string]*QueryParam{
	"HYBRID_FUND_TYPE": {
		QueryType:  FilterType_TERMED,
		QueryField: "assetClass",
		QueryTerm:  mfPb.AssetClass_HYBRID.String(),
	},
	"CASH_FUND_TYPE": {
		QueryType:  FilterType_TERMED,
		QueryField: "assetClass",
		QueryTerm:  mfPb.AssetClass_CASH.String(),
	},
	"DEBT_FUND_TYPE": {
		QueryType:  FilterType_TERMED,
		QueryField: "assetClass",
		QueryTerm:  mfPb.AssetClass_DEBT.String(),
	},
	"EQUITY_FUND_TYPE": {
		QueryType:  FilterType_TERMED,
		QueryField: "assetClass",
		QueryTerm:  mfPb.AssetClass_EQUITY.String(),
	},
	"POOR_CREDIT_QUALITY": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fundFundamentalDetails.bondStyleBox",
		QueryTerms: []string{"Limited Sensitivity Low Quality", "Moderate Sensitivity Low Quality", "Extensive Sensitivity Low Quality"},
	},
	"CREDIT_RISK_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_CREDIT_RISK.String(),
	},
	"BANKING_PSU_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_BANKING_AND_PSU.String(),
	},
	"FLOATER_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_FLOATING_RATE.String(),
	},
	"CORPORATE_BOND_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_CORPORATE_BOND.String(),
	},
	"GILT_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_GOVERNMENT_BOND.String(),
	},
	"GILT_10Y_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_TEN_YR_GOVERNMENT_BOND.String(),
	},
	"DYNAMIC_BOND_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_DYNAMIC_BOND.String(),
	},
	"LONG_DURATION_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_LONG_DURATION.String(),
	},
	"MEDIUM_LONG_DURATION_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_MEDIUM_TO_LONG_DURATION.String(),
	},
	"MEDIUM_DURATION_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_MEDIUM_DURATION.String(),
	},
	"SHORT_DURATION_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_SHORT_DURATION.String(),
	},
	"ULTRA_SHORT_DURATION_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_ULTRA_SHORT_DURATION.String(),
	},
	"LIQUID_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_LIQUID.String(),
	},
	"TAX_SAVING_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_ELSS_TAX_SAVING.String(),
	},
	"SECTORAL_EQUITY": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_SECTOR_HEALTHCARE.String(),
			mfPb.MutualFundCategoryName_SECTOR_FMCG.String(),
			mfPb.MutualFundCategoryName_SECTOR_TECHNOLOGY.String(),
		},
	},
	"FOCUSED_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_FOCUSED_FUND.String(),
	},
	"CONTRA_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_CONTRA.String(),
	},
	"VALUE_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_VALUE.String(),
	},
	"DIVIDEND_YIELD_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_DIVIDEND_YIELD.String(),
	},
	"SMALL_CAP_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_SMALL_CAP.String(),
	},
	"MID_CAP_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_MID_CAP.String(),
	},
	"LARGE_AND_MID_CAP_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_LARGE_AND_MID_CAP.String(),
	},
	"LARGE_CAP_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_LARGE_CAP.String(),
	},
	"MULTI_CAP_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_MULTI_CAP.String(),
	},
	"ZERO_TO_3YR_FUND_AGE": {
		QueryType:           FilterType_DATE_RANGED,
		QueryField:          "fundFundamentalDetails.inceptionDate",
		QueryDateLowerBound: time.Now().AddDate(-3, 0, 0),
		QueryDateUpperBound: time.Now(),
		QueryDateFormat:     "2006-01-02",
	},
	"3_TO_5YR_FUND_AGE": {
		QueryType:           FilterType_DATE_RANGED,
		QueryField:          "fundFundamentalDetails.inceptionDate",
		QueryDateLowerBound: time.Now().AddDate(-5, 0, 0),
		QueryDateUpperBound: time.Now().AddDate(-3, 0, 0),
		QueryDateFormat:     "2006-01-02",
	},
	"5_TO_10YR_FUND_AGE": {
		QueryType:           FilterType_DATE_RANGED,
		QueryField:          "fundFundamentalDetails.inceptionDate",
		QueryDateLowerBound: time.Now().AddDate(-10, 0, 0),
		QueryDateUpperBound: time.Now().AddDate(-5, 0, 0),
		QueryDateFormat:     "2006-01-02",
	},
	"MORE_THAN_10YR_FUND_AGE": {
		QueryType:           FilterType_DATE_RANGED,
		QueryField:          "fundFundamentalDetails.inceptionDate",
		QueryDateLowerBound: time.Now().AddDate(-100, 0, 0),
		QueryDateUpperBound: time.Now().AddDate(-10, 0, 0),
		QueryDateFormat:     "2006-01-02",
	},
	"UPTO_POINT5_PER_EXPENSE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.expenseRatio.fundCurrent",
		QueryUpperBound: 0.5,
		QueryLowerBound: 0,
	},
	"POINT51_TO_1_PER_EXPENSE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.expenseRatio.fundCurrent",
		QueryUpperBound: 1,
		QueryLowerBound: 0.51,
	},
	"1POINT1_TO_1POINT5PER_PER_EXPENSE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.expenseRatio.fundCurrent",
		QueryUpperBound: 1.5,
		QueryLowerBound: 1,
	},
	"GREATER_THAN_1POINT5PER_EXPENSE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.expenseRatio.fundCurrent",
		QueryUpperBound: 101,
		QueryLowerBound: 1.5,
	},
	"ADITYA_BIRLA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_ADITYA_BIRLA.String(),
	},
	"WHITEOAK_CAPITAL_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_WHITEOAK_CAPITAL.String(),
	},
	"TAURUS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_TAURUS.String(),
	},
	"ICICI_PRUDENTIAL_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_ICICI_PRUDENTIAL.String(),
	},
	"TRUST_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_TRUST.String(),
	},
	"IIFL_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_IIFL.String(),
	},
	"MOTILAL_OSWAL_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_MOTILAL_OSWAL.String(),
	},
	"HSBC_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_HSBC.String(),
	},
	"NAVI_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_NAVI.String(),
	},
	"BNP_PARIBAS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_BNP_PARIBAS.String(),
	},
	"EDELWEISS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_EDELWEISS.String(),
	},
	"HDFC_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_HDFC.String(),
	},
	"UNION_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_UNION.String(),
	},
	"BOI_AXA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_BOI_AXA.String(),
	},
	"MAHINDRA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_MAHINDRA.String(),
	},
	"PGIM_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_PGIM.String(),
	},
	"INDIABULLS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_INDIABULLS.String(),
	},
	"SAMCO_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_SAMCO.String(),
	},
	"CANARA_ROBECO_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_CANARA_ROBECO.String(),
	},
	"SHRIRAM_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_SHRIRAM.String(),
	},
	"LNT_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_LNT.String(),
	},
	"QUANT_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_QUANT_MONEY_MANAGERS.String(),
	},
	"PRINCIPAL_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_PRINCIPAL.String(),
	},
	"IDFC_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_IDFC.String(),
	},
	"UTI_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_UTI.String(),
	},
	"JM_FINANCIAL_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_JM_FINANCIAL.String(),
	},
	"SUNDARAM_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_SUNDARAM.String(),
	},
	"DSP_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_DSP.String(),
	},
	"AXIS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_AXIS.String(),
	},
	"LIC_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_LIC.String(),
	},
	"INVESCO_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_INVESCO.String(),
	},
	"NJ_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_NJ.String(),
	},
	"NIPPON_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_NIPPON_LIFE.String(),
	},
	"BARODA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_BARODA.String(),
	},
	"IDBI_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_IDBI.String(),
	},
	"ITI_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_ITI.String(),
	},
	"MIRAE_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_MIRAE.String(),
	},
	"SBI_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_SBI.String(),
	},
	"KOTAK_MAHINDRA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_KOTAK_MAHINDRA.String(),
	},
	"PPFAS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_PPFAS.String(),
	},
	"FRANKLIN_TEMPLETON_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_FRANKLIN_TEMPLETON.String(),
	},
	"TATA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_TATA.String(),
	},
	"BAJAJ_FINSERV_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_BAJAJ_FINSERV.String(),
	},
	"HELIOS_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_HELIOS.String(),
	},
	"ZERODHA_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_ZERODHA.String(),
	},
	"OLD_BRIDGE_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_OLD_BRIDGE.String(),
	},
	"UNIFI_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_UNIFI.String(),
	},
	"ANGEL_ONE_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_ANGEL_ONE.String(),
	},
	"JIO_BLACKROCK_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_JIO_BLACKROCK.String(),
	},
	"5001_TO_10KCR_FUND_SIZE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.aum.fundAum",
		QueryUpperBound: 100000000000,
		QueryLowerBound: 50000000000,
	},
	"MORE_THAN_10KCR_FUND_SIZE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.aum.fundAum",
		QueryUpperBound: 1000000000000000,
		QueryLowerBound: 100000000000,
	},
	"UPTO_100CR_FUND_SIZE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.aum.fundAum",
		QueryUpperBound: 1000000000,
		QueryLowerBound: 0,
	},
	"101_TO_1000CR_FUND_SIZE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.aum.fundAum",
		QueryUpperBound: 10000000000,
		QueryLowerBound: 1000000001,
	},
	"1001_TO_5000CR_FUND_SIZE": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.aum.fundAum",
		QueryUpperBound: 50000000000,
		QueryLowerBound: 10000000001,
	},
	"INDEX_FUND_TYPE": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_INDEX_FUNDS.String(),
	},
	"MONEY_MARKET_CASH": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_MONEY_MARKET.String(),
	},
	"OVERNIGHT_CASH": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_OVERNIGHT.String(),
	},
	"GOOD_CREDIT_QUALITY": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fundFundamentalDetails.bondStyleBox",
		QueryTerms: []string{"Limited Sensitivity High Quality", "Moderate Sensitivity High Quality", "Extensive Sensitivity High Quality"},
	},
	"AVG_CREDIT_QUALITY": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fundFundamentalDetails.bondStyleBox",
		QueryTerms: []string{"Limited Sensitivity Medium Quality", "Moderate Sensitivity Medium Quality", "Extensive Sensitivity Medium Quality"},
	},
	// TODO(MIHIR) - AYUSH
	// "INDIA_FOCUSSED_GEO": {
	//	QueryType:       FilterType_RANGED,
	//	QueryField:      "fundFundamentalDetails.aum.fundAum",
	//	QueryUpperBound: 10000000000,
	//	QueryLowerBound: 1000000001,
	// },
	// TODO(MIHIR) - AYUSH
	// "US_FOCUSSED_GEO": {
	//	QueryType:       FilterType_RANGED,
	//	QueryField:      "fundFundamentalDetails.aum.fundAum",
	//	QueryUpperBound: 10000000000,
	//	QueryLowerBound: 1000000001,
	// },
	// TODO(MIHIR) - AYUSH
	// "REST_WORLD_FOCUSSED_GEO": {
	//	QueryType:       0,
	//	QueryField:      "",
	//	QueryUpperBound: 0,
	//	QueryLowerBound: 0,
	//	QueryTerm:       "",
	//	QueryTerms:      nil,
	// },
	"AGGRESSIVE_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_AGGRESSIVE_ALLOCATION.String(),
	},
	"EQUITY_SAVINGS_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_EQUITY_SAVINGS.String(),
	},
	"ARBITRAGE_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_ARBITRAGE_FUND.String(),
	},
	"MULTI_ASSET_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_MULTI_ASSET_ALLOCATION.String(),
	},
	"DYNAMIC_ASSET_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_DYNAMIC_ASSET_ALLOCATION.String(),
	},
	"BALANCED_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_BALANCED_ALLOCATION.String(),
	},
	"CONSERVATIVE_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_CONSERVATIVE_ALLOCATION.String(),
	},
	"GOLD_HYBRID_SOL": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_SECTOR_PRECIOUS_METALS.String(),
	},
	"INTERNATIONAL_EQUITY_SOL": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_GLOBAL_OTHER.String(),
	},
	"CHILDREN_HYBRID_SOL": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_CHILDREN.String(),
	},
	"RETIREMENT_HYBRID_SOL": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_RETIREMENT.String(),
	},
	"LARGE_CAP_INDEX": {
		QueryType:  FilterType_TERMED,
		QueryField: "fiDefinedCategory",
		QueryTerm:  mfPb.MutualFundCategoryName_INDEX_FUNDS_LARGE_CAP.String(),
	},
	"MID_CAP_INDEX": {
		QueryType:  FilterType_TERMED,
		QueryField: "fiDefinedCategory",
		QueryTerm:  mfPb.MutualFundCategoryName_INDEX_FUNDS_MID_CAP.String(),
	},
	"COMMODITIES_FUND_TYPE": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_SECTOR_PRECIOUS_METALS.String(),
	},
	"GOLD_COMMODITIES": {
		QueryType:  FilterType_TERMED,
		QueryField: "fiDefinedCategory",
		QueryTerm:  mfPb.MutualFundCategoryName_GOLD_FUNDS.String(),
	},
	"SILVER_COMMODITIES": {
		QueryType:  FilterType_TERMED,
		QueryField: "fiDefinedCategory",
		QueryTerm:  mfPb.MutualFundCategoryName_SILVER_FUNDS.String(),
	},
	"GOLD_INDEX": {
		QueryType:  FilterType_TERMED,
		QueryField: "fiDefinedCategory",
		QueryTerm:  mfPb.MutualFundCategoryName_GOLD_FUNDS.String(),
	},
	"VALUE_INV_STYLE": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fundFundamentalDetails.equityStyleBox",
		QueryTerms: []string{"Large Value", "Mid Value", "Small Value"},
	},
	"GROWTH_INV_STYLE": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fundFundamentalDetails.equityStyleBox",
		QueryTerms: []string{"Large Growth", "Mid Growth", "Small Growth"},
	},
	"BLEND_INV_STYLE": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fundFundamentalDetails.equityStyleBox",
		QueryTerms: []string{"Large Blend", "Mid Blend", "Small Blend"},
	},
	"UPTO_100_AUTO_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "computedMinSipAmount",
		QueryUpperBound: 100,
		QueryLowerBound: 0,
	},
	"101_TO_500_AUTO_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "computedMinSipAmount",
		QueryUpperBound: 500,
		QueryLowerBound: 101,
	},
	"501_TO_1000_AUTO_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "computedMinSipAmount",
		QueryUpperBound: 1000,
		QueryLowerBound: 501,
	},
	"1001_TO_5000_AUTO_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "computedMinSipAmount",
		QueryUpperBound: 5000,
		QueryLowerBound: 1001,
	},
	"MORE_THAN_5000_AUTO_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "computedMinSipAmount",
		QueryUpperBound: 100000000,
		QueryLowerBound: 5000,
	},
	"UPTO_100_OT_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "txnConstraints.newpMnval.units",
		QueryUpperBound: 100,
		QueryLowerBound: 0,
	},
	"101_TO_500_OT_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "txnConstraints.newpMnval.units",
		QueryUpperBound: 500,
		QueryLowerBound: 101,
	},
	"501_TO_1000_OT_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "txnConstraints.newpMnval.units",
		QueryUpperBound: 1000,
		QueryLowerBound: 501,
	},
	"1001_TO_5000_OT_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "txnConstraints.newpMnval.units",
		QueryUpperBound: 5000,
		QueryLowerBound: 1001,
	},
	"MORE_THAN_5000_OT_INV_AMT": {
		QueryType:       FilterType_RANGED,
		QueryField:      "txnConstraints.newpMnval.units",
		QueryUpperBound: 10000000,
		QueryLowerBound: 5000,
	},
	"GREATER_THAN_20_PER_5_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnFiveYear",
		QueryUpperBound: 1000,
		QueryLowerBound: 20,
	},
	"UPTO_5_PER_6_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnSixMonth",
		QueryUpperBound: 5,
		QueryLowerBound: 0,
	},
	"6_TO_10_PER_6_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnSixMonth",
		QueryUpperBound: 10,
		QueryLowerBound: 6,
	},
	"11_TO_15_PER_6_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnSixMonth",
		QueryUpperBound: 15,
		QueryLowerBound: 11,
	},
	"16_TO_20_PER_6_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnSixMonth",
		QueryUpperBound: 20,
		QueryLowerBound: 16,
	},
	"GREATER_THAN_20_PER_6_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnSixMonth",
		QueryUpperBound: 10000,
		QueryLowerBound: 20,
	},
	"UPTO_5_PER_1_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneMonth",
		QueryUpperBound: 5,
		QueryLowerBound: 0,
	},
	"6_TO_10_PER_1_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneMonth",
		QueryUpperBound: 10,
		QueryLowerBound: 6,
	},
	"11_TO_15_PER_1_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneMonth",
		QueryUpperBound: 15,
		QueryLowerBound: 11,
	},
	"16_TO_20_PER_1_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneMonth",
		QueryUpperBound: 20,
		QueryLowerBound: 16,
	},
	"GREATER_THAN_20_PER_1_MON_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneMonth",
		QueryUpperBound: 10000,
		QueryLowerBound: 20,
	},
	"16_TO_20_PER_3_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnThreeYear",
		QueryUpperBound: 20,
		QueryLowerBound: 16,
	},
	"6_TO_10_PER_5_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnFiveYear",
		QueryUpperBound: 10,
		QueryLowerBound: 6,
	},
	"UPTO_5_PER_1_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneYear",
		QueryUpperBound: 5,
		QueryLowerBound: 0,
	},
	"6_TO_10_PER_3_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnThreeYear",
		QueryUpperBound: 10,
		QueryLowerBound: 6,
	},
	"UPTO_5_PER_3_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnThreeYear",
		QueryUpperBound: 5,
		QueryLowerBound: 0,
	},
	"11_TO_15_PER_1_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneYear",
		QueryUpperBound: 15,
		QueryLowerBound: 11,
	},
	"GREATER_THAN_20_PER_3_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnThreeYear",
		QueryUpperBound: 10000,
		QueryLowerBound: 20,
	},
	"16_TO_20_PER_1_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneYear",
		QueryUpperBound: 20,
		QueryLowerBound: 16,
	},
	"GREATER_THAN_20_PER_1_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneYear",
		QueryUpperBound: 10000,
		QueryLowerBound: 20,
	},
	"6_TO_10_PER_1_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnOneYear",
		QueryUpperBound: 10,
		QueryLowerBound: 6,
	},
	"16_TO_20_PER_5_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnFiveYear",
		QueryUpperBound: 20,
		QueryLowerBound: 16,
	},
	"UPTO_5_PER_5_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnFiveYear",
		QueryUpperBound: 5,
		QueryLowerBound: 0,
	},
	"11_TO_15_PER_5_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnFiveYear",
		QueryUpperBound: 15,
		QueryLowerBound: 11,
	},
	"11_TO_15_PER_3_YR_RETURNS": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.avgFundReturnThreeYear",
		QueryUpperBound: 15,
		QueryLowerBound: 11,
	},
	"VERY_HIGH_RISK": {
		QueryType:  FilterType_TERMED,
		QueryField: "fundhouseDefinedRiskLevel",
		QueryTerm:  mfPb.FundhouseDefinedRiskLevel_VERY_HIGH_RISK.String(),
	},
	"HIGH_RISK": {
		QueryType:  FilterType_TERMED,
		QueryField: "fundhouseDefinedRiskLevel",
		QueryTerm:  mfPb.FundhouseDefinedRiskLevel_HIGH_RISK.String(),
	},
	"MODERATELY_HIGH_RISK": {
		QueryType:  FilterType_TERMED,
		QueryField: "fundhouseDefinedRiskLevel",
		QueryTerm:  mfPb.FundhouseDefinedRiskLevel_MODERATELY_HIGH_RISK.String(),
	},
	"MODERATE_RISK": {
		QueryType:  FilterType_TERMED,
		QueryField: "fundhouseDefinedRiskLevel",
		QueryTerm:  mfPb.FundhouseDefinedRiskLevel_MODERATE_RISK.String(),
	},
	"MODERATELY_LOW_RISK": {
		QueryType:  FilterType_TERMED,
		QueryField: "fundhouseDefinedRiskLevel",
		QueryTerm:  mfPb.FundhouseDefinedRiskLevel_LOW_TO_MODERATE_RISK.String(),
	},
	"LOW_RISK": {
		QueryType:  FilterType_TERMED,
		QueryField: "fundhouseDefinedRiskLevel",
		QueryTerm:  mfPb.FundhouseDefinedRiskLevel_LOW_RISK.String(),
	},
	"IT_51_TO_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.TECHNOLOGY_NET",
		QueryUpperBound: 80,
		QueryLowerBound: 51,
	},
	"BFS_26_TO_50_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.FINANCIAL_SERVICES_NET",
		QueryUpperBound: 50,
		QueryLowerBound: 26,
	},
	"BFS_LESS_THAN_10_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.FINANCIAL_SERVICES_NET",
		QueryUpperBound: 10,
		QueryLowerBound: 0,
	},
	"IT_GREATER_THAN_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.TECHNOLOGY_NET",
		QueryUpperBound: 100,
		QueryLowerBound: 80,
	},
	"IT_26_TO_50_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.TECHNOLOGY_NET",
		QueryUpperBound: 60,
		QueryLowerBound: 26,
	},
	"PHARMA_LESS_THAN_10_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.HEALTHCARE_NET",
		QueryUpperBound: 10,
		QueryLowerBound: 0,
	},
	"INFRA_LESS_THAN_10_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.REALESTATE_NET",
		QueryUpperBound: 10,
		QueryLowerBound: 0,
	},
	"INFRA_11_TO_25_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.REALESTATE_NET",
		QueryUpperBound: 25,
		QueryLowerBound: 11,
	},
	"IT_LESS_THAN_10_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.TECHNOLOGY_NET",
		QueryUpperBound: 10,
		QueryLowerBound: 0,
	},
	"IT_11_TO_25_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.TECHNOLOGY_NET",
		QueryUpperBound: 25,
		QueryLowerBound: 11,
	},
	"BFS_51_TO_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.FINANCIAL_SERVICES_NET",
		QueryUpperBound: 80,
		QueryLowerBound: 51,
	},
	"INFRA_51_TO_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.REALESTATE_NET",
		QueryUpperBound: 80,
		QueryLowerBound: 51,
	},
	"PHARMA_11_TO_25_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.HEALTHCARE_NET",
		QueryUpperBound: 25,
		QueryLowerBound: 11,
	},
	"PHARMA_GREATER_THAN_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.HEALTHCARE_NET",
		QueryUpperBound: 100,
		QueryLowerBound: 80,
	},
	"BFS_11_TO_25_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.FINANCIAL_SERVICES_NET",
		QueryUpperBound: 25,
		QueryLowerBound: 11,
	},
	"PHARMA_51_TO_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.HEALTHCARE_NET",
		QueryUpperBound: 80,
		QueryLowerBound: 51,
	},
	"PHARMA_26_TO_50_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.HEALTHCARE_NET",
		QueryUpperBound: 50,
		QueryLowerBound: 26,
	},
	"BFS_GREATER_THAN_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.FINANCIAL_SERVICES_NET",
		QueryUpperBound: 100,
		QueryLowerBound: 80,
	},
	"INFRA_GREATER_THAN_80_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.REALESTATE_NET",
		QueryUpperBound: 100,
		QueryLowerBound: 80,
	},
	"INFRA_26_TO_50_PER": {
		QueryType:       FilterType_RANGED,
		QueryField:      "fundFundamentalDetails.globalEquitySectors.equitySectors.REALESTATE_NET",
		QueryUpperBound: 50,
		QueryLowerBound: 26,
	},
	"3YR_TO_5YR_INV_DUR": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_MEDIUM_DURATION.String(),
			mfPb.MutualFundCategoryName_DYNAMIC_BOND.String(),
			mfPb.MutualFundCategoryName_CORPORATE_BOND.String(),
			mfPb.MutualFundCategoryName_CREDIT_RISK.String(),
			mfPb.MutualFundCategoryName_BANKING_AND_PSU.String(),
			mfPb.MutualFundCategoryName_GOVERNMENT_BOND.String(),
			mfPb.MutualFundCategoryName_CONSERVATIVE_ALLOCATION.String(),
			mfPb.MutualFundCategoryName_EQUITY_SAVINGS.String(),
			mfPb.MutualFundCategoryName_SECTOR_PRECIOUS_METALS.String(),
		},
	},
	"5YR_TO_7YR_INV_DUR": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_MULTI_CAP.String(),
			mfPb.MutualFundCategoryName_LARGE_CAP.String(),
			mfPb.MutualFundCategoryName_LARGE_AND_MID_CAP.String(),
			mfPb.MutualFundCategoryName_DIVIDEND_YIELD.String(),
			mfPb.MutualFundCategoryName_ELSS_TAX_SAVING.String(),
			mfPb.MutualFundCategoryName_MEDIUM_TO_LONG_DURATION.String(),
			mfPb.MutualFundCategoryName_BALANCED_ALLOCATION.String(),
			mfPb.MutualFundCategoryName_AGGRESSIVE_ALLOCATION.String(),
			mfPb.MutualFundCategoryName_DYNAMIC_ASSET_ALLOCATION.String(),
			mfPb.MutualFundCategoryName_MULTI_ASSET_ALLOCATION.String(),
			mfPb.MutualFundCategoryName_FUND_OF_FUNDS.String(),
			mfPb.MutualFundCategoryName_LARGE_CAP.String(),
		},
	},
	"MORE_THAN_7YR_INV_DUR": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_MID_CAP.String(),
			mfPb.MutualFundCategoryName_SMALL_CAP.String(),
			mfPb.MutualFundCategoryName_VALUE.String(),
			mfPb.MutualFundCategoryName_CONTRA.String(),
			mfPb.MutualFundCategoryName_FOCUSED_FUND.String(),
			mfPb.MutualFundCategoryName_LONG_DURATION.String(),
			mfPb.MutualFundCategoryName_TEN_YR_GOVERNMENT_BOND.String(),
			mfPb.MutualFundCategoryName_RETIREMENT.String(),
			mfPb.MutualFundCategoryName_CHILDREN.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS.String(),
		},
	},
	"LESS_THAN_1MON_INV_DUR": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_OVERNIGHT.String(),
		},
	},
	"6MON_TO_1YR_INV_DUR": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_LIQUID.String(),
			mfPb.MutualFundCategoryName_ULTRA_SHORT_DURATION.String(),
			mfPb.MutualFundCategoryName_MONEY_MARKET.String(),
			mfPb.MutualFundCategoryName_ARBITRAGE_FUND.String(),
		},
	},
	"1YR_TO_3YR_INV_DUR": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "categoryName",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_SECTOR_HEALTHCARE.String(),
			mfPb.MutualFundCategoryName_SECTOR_FMCG.String(),
			mfPb.MutualFundCategoryName_SECTOR_TECHNOLOGY.String(),
			mfPb.MutualFundCategoryName_SECTOR_FINANCIAL_SERVICES.String(),
			mfPb.MutualFundCategoryName_SHORT_DURATION.String(),
			mfPb.MutualFundCategoryName_FLOATING_RATE.String(),
		},
	},
	"MANAGER_AAKASH_MANGHANI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aakash Manghani"}},
	"MANAGER_ABHINAV_KHANDELWAL":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhinav Khandelwal"}},
	"MANAGER_ABHINAV_SHARMA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhinav Sharma"}},
	"MANAGER_ABHIROOP_MUKHERJEE":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhiroop Mukherjee"}},
	"MANAGER_ABHISHEK_BISEN":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhishek Bisen"}},
	"MANAGER_ABHISHEK_GHOSH":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhishek Ghosh"}},
	"MANAGER_ABHISHEK_GUPTA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhishek Gupta"}},
	"MANAGER_ABHISHEK_IYER":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhishek Iyer"}},
	"MANAGER_ABHISHEK_SINGH":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhishek Singh"}},
	"MANAGER_ABHISHEK_SONTHALIA":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abhishek Sonthalia"}},
	"MANAGER_ABUL_FATEH":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Abul Fateh"}},
	"MANAGER_ADITYA_KHEMANI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aditya Khemani"}},
	"MANAGER_ADITYA_MULKI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aditya Mulki"}},
	"MANAGER_ADITYA_PAGARIA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aditya Pagaria"}},
	"MANAGER_AISHWARYA_AGARWAL":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aishwarya Agarwal"}},
	"MANAGER_AJAY_ARGAL":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ajay Argal"}},
	"MANAGER_AJAY_GARG":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ajay Garg"}},
	"MANAGER_AJAY_KHANDELWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ajay Khandelwal"}},
	"MANAGER_AJAY_TYAGI":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ajay Tyagi"}},
	"MANAGER_AKHIL_KAKKAR":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Akhil Kakkar"}},
	"MANAGER_AKHIL_KALLURI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Akhil Kalluri"}},
	"MANAGER_AKHIL_MITTAL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Akhil Mittal"}},
	"MANAGER_AKHIL_THAKKER":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Akhil Thakker"}},
	"MANAGER_ALOK_RANJAN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Alok Ranjan"}},
	"MANAGER_ALOK_SAHOO":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Alok Sahoo"}},
	"MANAGER_ALOK_SINGH":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Alok Singh"}},
	"MANAGER_AMANDEEP_CHOPRA":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amandeep Chopra"}},
	"MANAGER_AMAR_KALKUNDRIKAR":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amar Kalkundrikar"}},
	"MANAGER_AMEY_SATHE":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amey Sathe"}},
	"MANAGER_AMIT_GANATRA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Ganatra"}},
	"MANAGER_AMIT_GARG":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Garg"}},
	"MANAGER_AMIT_HIREMATH":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Hiremath"}},
	"MANAGER_AMIT_KADAM":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Kadam"}},
	"MANAGER_AMIT_MODANI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Modani"}},
	"MANAGER_AMIT_NADEKAR":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Nadekar"}},
	"MANAGER_AMIT_NIGAM":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Nigam"}},
	"MANAGER_AMIT_PREMCHANDANI":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Premchandani"}},
	"MANAGER_AMIT_SHARMA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Sharma"}},
	"MANAGER_AMIT_SOMANI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Somani"}},
	"MANAGER_AMIT_VORA":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Amit Vora"}},
	"MANAGER_ANAND_GUPTA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anand Gupta"}},
	"MANAGER_ANAND_LADDHA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anand Laddha"}},
	"MANAGER_ANAND_NEVATIA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anand Nevatia"}},
	"MANAGER_ANAND_RADHAKRISHNAN":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anand Radhakrishnan"}},
	"MANAGER_ANAND_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anand Sharma"}},
	"MANAGER_ANANDHA_PADMANABHAN":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"A. Anandha Padmanabhan"}},
	"MANAGER_ANIL_BAMBOLI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anil Bamboli"}},
	"MANAGER_ANIL_GHELANI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anil Ghelani"}},
	"MANAGER_ANIL_SHAH":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anil Shah"}},
	"MANAGER_ANINDYA_SARKAR":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anindya Sarkar"}},
	"MANAGER_ANIRUDDHA_NAHA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aniruddha Naha"}},
	"MANAGER_ANISH_TAWAKLEY":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anish Tawakley"}},
	"MANAGER_ANJU_CHHAJER":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anju Chhajer"}},
	"MANAGER_ANKIT_AGARWAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ankit Agarwal"}},
	"MANAGER_ANKIT_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ankit Jain"}},
	"MANAGER_ANKIT_PANDE":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ankit A. Pande"}},
	"MANAGER_ANKIT_TIKMANY":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ankit Tikmany"}},
	"MANAGER_ANKUR_ARORA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ankur Arora"}},
	"MANAGER_ANKUSH_SOOD":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ankush Sood"}},
	"MANAGER_ANOOP_BHASKAR":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anoop Bhaskar"}},
	"MANAGER_ANUJ_JAIN":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anuj Jain"}},
	"MANAGER_ANUJ_TAGRA":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anuj Tagra"}},
	"MANAGER_ANUPAM_JOSHI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anupam Joshi"}},
	"MANAGER_ANUPAM_TIWARI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anupam Tiwari"}},
	"MANAGER_ANURAG_MITTAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Anurag Mittal"}},
	"MANAGER_APARNA_KARNIK":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aparna Karnik"}},
	"MANAGER_ARDHENDU_BHATTACHARYA":  {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ardhendu Bhattacharya"}},
	"MANAGER_ARJUN_KHANNA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Arjun Khanna"}},
	"MANAGER_ARUN_AGARWAL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Arun Agarwal"}},
	"MANAGER_ARUN_R.":                {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Arun R."}},
	"MANAGER_ARUN_SUNDARESAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Arun Sundaresan"}},
	"MANAGER_ARVIND_SUBRAMANIAN":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Arvind Subramanian"}},
	"MANAGER_ASHISH_AGARWAL":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ashish Agarwal"}},
	"MANAGER_ASHISH_AGGARWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ashish Aggarwal"}},
	"MANAGER_ASHISH_NAIK":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ashish Naik"}},
	"MANAGER_ASHUTOSH_BHARGAVA":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ashutosh Bhargava"}},
	"MANAGER_ASHUTOSH_DESAI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ashutosh Desai"}},
	"MANAGER_ASHWANI_AGARWALLA":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ashwani Agarwalla"}},
	"MANAGER_ASIT_BHANDARKAR":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Asit Bhandarkar"}},
	"MANAGER_ATUL_BHOLE":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Atul Bhole"}},
	"MANAGER_ATUL_PENKAR":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Atul Penkar"}},
	"MANAGER_AUROBINDO_GAYAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Aurobinda Prasad Gayan"}},
	"MANAGER_AVNISH_JAIN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Avnish Jain"}},
	"MANAGER_AYUSH_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ayush Jain"}},
	"MANAGER_B_KUMAR":                {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"B. Kumar"}},
	"MANAGER_BHAGYESH_KAGALKAR":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bhagyesh Kagalkar"}},
	"MANAGER_BHARAT_LAHOTI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bharat Lahoti"}},
	"MANAGER_BHARTI_SAWANT":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bharti Sawant"}},
	"MANAGER_BHAVESH_JAIN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bhavesh Jain"}},
	"MANAGER_BHAVIK_DAVE":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bhavik Dave"}},
	"MANAGER_BHAVIN_VITHLANI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bhavin Vithlani"}},
	"MANAGER_BHUPESH_BAMETA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bhupesh Bameta"}},
	"MANAGER_BHUPESH_KALYANI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Bhupesh Kalyani"}},
	"MANAGER_BRIJESH_SHAH":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Brijesh Shah"}},
	"MANAGER_CHAITANYA_CHOKSI":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chaitanya Choksi"}},
	"MANAGER_CHANCHAL_KHANDELWAL":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chanchal Khandelwal"}},
	"MANAGER_CHANDNI_GUPTA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chandni Gupta"}},
	"MANAGER_CHANDRAPRAKASH_PADIYAR": {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chandraprakash Padiyar"}},
	"MANAGER_CHARANJIT_SINGH":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Charanjit Singh"}},
	"MANAGER_CHEENU_GUPTA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Cheenu Gupta"}},
	"MANAGER_CHIRAG_DAGLI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chirag Dagli"}},
	"MANAGER_CHIRAG_MEHTA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chirag Mehta"}},
	"MANAGER_CHIRAG_SETALVAD":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chirag Setalvad"}},
	"MANAGER_CHOCKALINGAM_NARAYANAN": {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Chockalingam Narayanan"}},
	"MANAGER_DAYLYNN_PINTO":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Daylynn Pinto"}},
	"MANAGER_DEEPAK_AGRAWAL":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Deepak Agrawal"}},
	"MANAGER_DEEPAK_GUPTA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Deepak Gupta"}},
	"MANAGER_DEVANG_SHAH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Devang Shah"}},
	"MANAGER_DEVENDER_SINGHAL":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Devender Singhal"}},
	"MANAGER_DEVESH_THACKER":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Devesh Thacker"}},
	"MANAGER_DHARMESH_KAKKAD":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dharmesh Kakkad"}},
	"MANAGER_DHAVAL_GALA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhaval Gala"}},
	"MANAGER_DHAVAL_SHAH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhaval Shah"}},
	"MANAGER_DHAWAL_DALAL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhawal Dalal"}},
	"MANAGER_DHAWAL_DHANANI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhawal Dhanani"}},
	"MANAGER_DHIMANT_KOTHARI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhimant Kothari"}},
	"MANAGER_DHRUMIL_SHAH":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhrumil Shah"}},
	"MANAGER_DHRUV_BHATIA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dhruv Bhatia"}},
	"MANAGER_DIIPESH_SHAH":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Diipesh Shah"}},
	"MANAGER_DINESH_AHUJA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dinesh Ahuja"}},
	"MANAGER_DINESH_BALACHANDRAN":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dinesh Balachandran"}},
	"MANAGER_DWIJENDRA_SRIVASTAVA":   {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Dwijendra Srivastava"}},
	"MANAGER_EKTA_GALA":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ekta Gala"}},
	"MANAGER_ENNETTE_FERNANDES":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ennette Fernandes"}},
	"MANAGER_FATEMA_PACHA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Fatema Pacha"}},
	"MANAGER_GARGI_BANERJEE":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gargi Banerjee"}},
	"MANAGER_GAURAV_KHANDELWAL":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gaurav Khandelwal"}},
	"MANAGER_GAURAV_KOCHAR":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gaurav Kochar"}},
	"MANAGER_GAURAV_MISRA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gaurav Misra"}},
	"MANAGER_GAUTAM_BHUPAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gautam Bhupal"}},
	"MANAGER_GAUTAM_KAUL":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gautam Kaul"}},
	"MANAGER_GHAZAL_JAIN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ghazal Jain"}},
	"MANAGER_GOPAL_AGRAWAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gopal Agrawal"}},
	"MANAGER_GURVINDER_WASAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Gurvinder Wasan"}},
	"MANAGER_HARDICK_BORA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hardick Bora"}},
	"MANAGER_HARDIK_SHAH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hardik Shah"}},
	"MANAGER_HARISH_BIHANI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harish Bihani"}},
	"MANAGER_HARISH_KRISHNAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harish Krishnan"}},
	"MANAGER_HARSHA_UPADHYAYA":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harsha Upadhyaya"}},
	"MANAGER_HARSHAD_BORAWAKE":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harshad Borawake"}},
	"MANAGER_HARSHAL_JOSHI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harshal Joshi"}},
	"MANAGER_HARSHIL_JOSHI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harshil Joshi"}},
	"MANAGER_HARSHIL_SUVARNKAR":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Harshil Suvarnkar"}},
	"MANAGER_HETAL_GADA":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hetal Gada"}},
	"MANAGER_HITASH_DANG":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hitash Dang"}},
	"MANAGER_HITEN_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hiten Jain"}},
	"MANAGER_HITEN_SHAH":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hiten Shah"}},
	"MANAGER_HITESH_DAS":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Hitesh Das"}},
	"MANAGER_IHAB_DALWAI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ihab Dalwai"}},
	"MANAGER_JAIPRAKASH_TOSHNIWAL":   {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jaiprakash Toshniwal"}},
	"MANAGER_JALPAN_SHAH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jalpan Shah"}},
	"MANAGER_JAY_KOTHARI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jay Kothari"}},
	"MANAGER_JIGAR_SHETHIA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jigar Shethia"}},
	"MANAGER_JIGNESH_RAO":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jignesh Rao"}},
	"MANAGER_JINESH_GOPANI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jinesh Gopani"}},
	"MANAGER_JITENDRA_SRIRAM":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Jitendra Sriram"}},
	"MANAGER_KAMAL_GADA":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kamal Gada"}},
	"MANAGER_KAPIL_PUNJABI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kapil Punjabi"}},
	"MANAGER_KARAN_DOSHI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Karan Doshi"}},
	"MANAGER_KARAN_MUNDHRA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Karan Mundhra"}},
	"MANAGER_KARAN_MUNDRA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Karan Mundra"}},
	"MANAGER_KARAN_SINGH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Karan Singh"}},
	"MANAGER_KARN_KUMAR":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Karn Kumar"}},
	"MANAGER_KARTIK_SORAL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kartik Soral"}},
	"MANAGER_KAUSTUBH_GUPTA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kaustubh Gupta"}},
	"MANAGER_KAUSTUBH_SULE":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kaustubh Sule"}},
	"MANAGER_KAYZAD_EGHLIM":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kayzad Eghlim"}},
	"MANAGER_KEDAR_KARNIK":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kedar Karnik"}},
	"MANAGER_KHOZEM_JABALPURWALA":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Khozem Jabalpurwala"}},
	"MANAGER_KINJAL_DESAI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kinjal Desai"}},
	"MANAGER_KIRAN_SEBASTIAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kiran Sebastian"}},
	"MANAGER_KRISHAN_DAGA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Krishan Daga"}},
	"MANAGER_KRISHNA_CHEEMALAPATI":   {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Krishna Cheemalapati"}},
	"MANAGER_KRISHNA_SANGHAVI":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Krishna Sanghavi"}},
	"MANAGER_KUNAL_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kunal Jain"}},
	"MANAGER_KUNAL_SANGOI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kunal Sangoi"}},
	"MANAGER_KUSH_SONIGARA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Kush Sonigara"}},
	"MANAGER_LAKSHMIKANTH_REDDY":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Lakshmikanth Reddy"}},
	"MANAGER_LALIT_KUMAR":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Lalit Kumar"}},
	"MANAGER_LAUKIK_BAGWE":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Laukik Bagwe"}},
	"MANAGER_LOKESH_MALLYA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Lokesh Mallya"}},
	"MANAGER_LOVELISH_SOLANKI":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Lovelish Solanki"}},
	"MANAGER_MAHENDRA_JAJOO":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mahendra Jajoo"}},
	"MANAGER_MAHESH_CHHABRIA":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mahesh Chhabria"}},
	"MANAGER_MAHESH_PATIL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mahesh Patil"}},
	"MANAGER_MANISH_BANTHIA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Manish Banthia"}},
	"MANAGER_MANISH_GUNWANI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Manish Gunwani"}},
	"MANAGER_MANISH_LODHA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Manish Lodha"}},
	"MANAGER_MANSI_SAJEJA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mansi Sajeja"}},
	"MANAGER_MARZBAN_IRANI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Marzban Irani"}},
	"MANAGER_MAYANK_BUKREDIWALA":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mayank Bukrediwala"}},
	"MANAGER_MAYANK_PRAKASH":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mayank Prakash"}},
	"MANAGER_MAYUR_PATEL":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mayur Patel"}},
	"MANAGER_MEENAKSHI_DAWAR":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Meenakshi Dawar"}},
	"MANAGER_MEETA_SHETTY":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Meeta Shetty"}},
	"MANAGER_MEHUL_DAMA":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mehul Dama"}},
	"MANAGER_MEHUL_SONI":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mehul Soni"}},
	"MANAGER_MILAN_MODY":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Milan Mody"}},
	"MANAGER_MILIND_AGRAWAL":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Milind Agrawal"}},
	"MANAGER_MILIND_BAFNA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Milind Bafna"}},
	"MANAGER_MITHRAEM_BHARUCHA":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mithraem Bharucha"}},
	"MANAGER_MITTUL_KALAWADIA":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mittul Kalawadia"}},
	"MANAGER_MOHIT_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mohit Jain"}},
	"MANAGER_MOHIT_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Mohit Sharma"}},
	"MANAGER_MONIKA_GANDHI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Monika Gandhi"}},
	"MANAGER_MURTHY_NAGARAJAN":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Murthy Nagarajan"}},
	"MANAGER_NAGHMA_KHOJA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Naghma Khoja"}},
	"MANAGER_NEELESH_DHAMNASKAR":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Neelesh Dhamnaskar"}},
	"MANAGER_NEELESH_SURANA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Neelesh Surana"}},
	"MANAGER_NEELOTPAL_SAHAI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Neelotpal Sahai"}},
	"MANAGER_NEERAJ_KUMAR":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Neeraj Kumar"}},
	"MANAGER_NEERAJ_SAXENA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Neeraj Saxena"}},
	"MANAGER_NEMISH_SHETH":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nemish Sheth"}},
	"MANAGER_NIDHI_CHAWLA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nidhi Chawla"}},
	"MANAGER_NIKET_SHAH":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Niket Shah"}},
	"MANAGER_NIKHIL_KABRA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nikhil Kabra"}},
	"MANAGER_NIKHIL_RUNGTA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nikhil Rungta"}},
	"MANAGER_NILESH_SHETTY":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nilesh Shetty"}},
	"MANAGER_NIRALI_BHANSALI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nirali Bhansali"}},
	"MANAGER_NISHIT_PATEL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nishit Patel"}},
	"MANAGER_NISHITA_DOSHI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nishita Doshi"}},
	"MANAGER_NITESH_JAIN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nitesh Jain"}},
	"MANAGER_NITIN_GOSAR":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Nitin Gosar"}},
	"MANAGER_PALLAB_ROY":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pallab Roy"}},
	"MANAGER_PANKAJ_PATHAK":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pankaj Pathak"}},
	"MANAGER_PANKAJ_TIBREWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pankaj Tibrewal"}},
	"MANAGER_PARIJAT_AGRAWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Parijat Agrawal"}},
	"MANAGER_PARIJAT_GARG":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Parijat Garg"}},
	"MANAGER_PAUL_PARAMPREET":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Paul Parampreet"}},
	"MANAGER_PIYUSH_BARANWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Piyush Baranwal"}},
	"MANAGER_PRADEEP_GOKHALE":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pradeep Gokhale"}},
	"MANAGER_PRAKASH_GOEL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prakash Goel"}},
	"MANAGER_PRANAV_GOKHALE":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pranav Gokhale"}},
	"MANAGER_PRANAV_GUPTA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pranav Gupta"}},
	"MANAGER_PRANAVI_KULKARNI":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pranavi Kulkarni"}},
	"MANAGER_PRANAY_SINHA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pranay Sinha"}},
	"MANAGER_PRASANNA_PATHAK":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prasanna Pathak"}},
	"MANAGER_PRASHANT_JAIN":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prashant Jain"}},
	"MANAGER_PRASHANT_PIMPLE":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prashant Pimple"}},
	"MANAGER_PRATEEK_JAIN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prateek Jain"}},
	"MANAGER_PRATEEK_NIGUDKAR":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prateek Nigudkar"}},
	"MANAGER_PRATEEK_PODDAR":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Prateek Poddar"}},
	"MANAGER_PRATIBH_AGARWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pratibh Agarwal"}},
	"MANAGER_PRATISH_KRISHNAN":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Pratish Krishnan"}},
	"MANAGER_PRAVEEN_AYATHAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Praveen Ayathan"}},
	"MANAGER_PRAVEEN_JAIN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Praveen Jain"}},
	"MANAGER_PREETHI_S":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Preethi S"}},
	"MANAGER_PRIYA_RANJAN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Priya Ranjan"}},
	"MANAGER_PRIYANKA_KHANDELWAL":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Priyanka Khandelwal"}},
	"MANAGER_PUNEET_PAL":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Puneet Pal"}},
	"MANAGER_R_ARUN":                 {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"R. Arun"}},
	"MANAGER_R_JANAKIRAMAN":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"R. Janakiraman"}},
	"MANAGER_R_SIVAKUMAR":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"R Sivakumar"}},
	"MANAGER_R_SRINIVASAN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"R. Srinivasan"}},
	"MANAGER_RAHUL_DEDHIA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rahul Dedhia"}},
	"MANAGER_RAHUL_GOSWAMI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rahul Goswami"}},
	"MANAGER_RAHUL_JAGWANI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rahul Jagwani"}},
	"MANAGER_RAHUL_PAL":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rahul Pal"}},
	"MANAGER_RAHUL_SINGH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rahul Singh"}},
	"MANAGER_RAJ_GANDHI":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Raj gandhi"}},
	"MANAGER_RAJ_MEHTA":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Raj Mehta"}},
	"MANAGER_RAJASA_KAKULAVARAPU":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rajasa Kakulavarapu"}},
	"MANAGER_RAJAT_CHANDAK":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rajat Chandak"}},
	"MANAGER_RAJEEV_RADHAKRISHNAN":   {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rajeev Radhakrishnan"}},
	"MANAGER_RAJEEV_THAKKAR":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rajeev Thakkar"}},
	"MANAGER_RAJU_SHARMA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Raju Sharma"}},
	"MANAGER_RAKESH_VYAS":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rakesh Vyas"}},
	"MANAGER_RAMA_SRINIVASAN":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rama Srinivasan"}},
	"MANAGER_RAMNEEK_KUNDRA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ramneek Kundra"}},
	"MANAGER_RANJITHGOPAL_A.":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ranjithgopal A."}},
	"MANAGER_RATISH_VARIER":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ratish Varier"}},
	"MANAGER_RAUNAK_ONKAR":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Raunak Onkar"}},
	"MANAGER_RAVI_ADUKIA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ravi Adukia"}},
	"MANAGER_RAVI_GOPALAKRISHNAN":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ravi Gopalakrishnan"}},
	"MANAGER_RAVIPRAKASH_SHARMA":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Raviprakash Sharma"}},
	"MANAGER_RESHAM_JAIN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Resham Jain"}},
	"MANAGER_RICHARD_DSOUZA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Richard D'souza"}},
	"MANAGER_RISHI_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rishi Sharma"}},
	"MANAGER_RITESH_JAIN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ritesh Jain"}},
	"MANAGER_RITESH_LUNAWAT":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ritesh Lunawat"}},
	"MANAGER_RITESH_NAMBIAR":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Ritesh Nambiar"}},
	"MANAGER_ROHAN_KORDE":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rohan Korde"}},
	"MANAGER_ROHAN_MARU":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rohan Maru"}},
	"MANAGER_ROHIT_SEKSARIA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rohit Seksaria"}},
	"MANAGER_ROHIT_SHIMPI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rohit Shimpi"}},
	"MANAGER_ROHIT_SINGHANIA":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rohit Singhania"}},
	"MANAGER_ROSHAN_CHUTKEY":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Roshan Chutkey"}},
	"MANAGER_ROSHI_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Roshi Jain"}},
	"MANAGER_RUKUN_TARACHANDANI":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rukun Tarachandani"}},
	"MANAGER_RUPESH_PATEL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Rupesh Patel"}},
	"MANAGER_S_BHARATH":              {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"S. Bharath"}},
	"MANAGER_SACHIN_JAIN":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sachin Jain"}},
	"MANAGER_SACHIN_PADWAL-DESAI":    {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sachin Padwal-Desai"}},
	"MANAGER_SACHIN_RELEKAR":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sachin Relekar"}},
	"MANAGER_SACHIN_TRIVEDI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sachin Trivedi"}},
	"MANAGER_SAHIL_SHAH":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sahil Shah"}},
	"MANAGER_SAILESH_JAIN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sailesh Jain"}},
	"MANAGER_SAILESH_RAJ BHAN":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sailesh Raj Bhan"}},
	"MANAGER_SAMIR_RACHH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Samir Rachh"}},
	"MANAGER_SANDEEP_AGARWAL":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sandeep Agarwal"}},
	"MANAGER_SANDEEP_JAIN":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sandeep Jain"}},
	"MANAGER_SANDEEP_MANAM":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sandeep Manam"}},
	"MANAGER_SANDEEP_TANDON":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sandeep Tandon"}},
	"MANAGER_SANDEEP_YADAV":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sandeep Yadav"}},
	"MANAGER_SANJAY_BEMBALKAR":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sanjay Bembalkar"}},
	"MANAGER_SANJAY_CHAWLA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sanjay Chawla"}},
	"MANAGER_SANJAY_DOSHI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sanjay Doshi"}},
	"MANAGER_SANJAY_GODAMBE":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sanjay Godambe"}},
	"MANAGER_SANJAY_PAWAR":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sanjay Pawar"}},
	"MANAGER_SANJEEV_SHARMA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sanjeev Sharma"}},
	"MANAGER_SANKALP_BAID":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sankalp Baid"}},
	"MANAGER_SANKARAN_NAREN":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sankaran Naren"}},
	"MANAGER_SANTOSH_KAMATH":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Santosh Kamath"}},
	"MANAGER_SANTOSH_SINGH":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Santosh Singh"}},
	"MANAGER_SATISH_DONDAPATI":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Satish Dondapati"}},
	"MANAGER_SATISH_MISHRA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Satish Mishra"}},
	"MANAGER_SATISH_RAMANATHAN":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Satish Ramanathan"}},
	"MANAGER_SATYABRATA_MOHANTY":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Satyabrata Mohanty"}},
	"MANAGER_SAURABH_PANT":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Saurabh Pant"}},
	"MANAGER_SHADAB_RIZVI":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shadab Rizvi"}},
	"MANAGER_SHALINI_TIBREWALA":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shalini Tibrewala"}},
	"MANAGER_SHARMILA_DMELLO":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sharmila D'mello"}},
	"MANAGER_SHARWAN_GOYAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sharwan Kumar Goyal"}},
	"MANAGER_SHASHANK_VERMA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shashank Verma"}},
	"MANAGER_SHIBANI_KURIAN":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shibani Kurian"}},
	"MANAGER_SHOBHIT_MEHROTRA":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shobhit Mehrotra"}},
	"MANAGER_SHREYASH_DEVALKAR":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shreyash Devalkar"}},
	"MANAGER_SHRIDATTA_BHANDWALDAR":  {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shridatta Bhandwaldar"}},
	"MANAGER_SHRIRAM_RAMANATHAN":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Shriram Ramanathan"}},
	"MANAGER_SIDDHANT_CHHABRIA":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Siddhant Chhabria"}},
	"MANAGER_SIDDHARTH_BOTHRA":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Siddharth Bothra"}},
	"MANAGER_SIDDHARTH_DEB":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Siddharth Deb"}},
	"MANAGER_SILKY_JAIN":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Silky Jain"}},
	"MANAGER_SNEHA_JOSHI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sneha Joshi"}},
	"MANAGER_SOHINI_ANDANI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sohini Andani"}},
	"MANAGER_SONAL_GUPTA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sonal Gupta"}},
	"MANAGER_SONAM_UDASI":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sonam Udasi"}},
	"MANAGER_SORBH_GUPTA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sorbh Gupta"}},
	"MANAGER_SRI_SHARMA":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sri Sharma"}},
	"MANAGER_SRINIVAS_RAVURI":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Srinivas Ravuri"}},
	"MANAGER_SRINIVASAN_RAMAMURTHY":  {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Srinivasan Ramamurthy"}},
	"MANAGER_SUDHIR_AGARWAL":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sudhir Agarwal"}},
	"MANAGER_SUDHIR_KEDIA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sudhir Kedia"}},
	"MANAGER_SUMAN_PRASAD":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Suman Prasad"}},
	"MANAGER_SUMIT_AGRAWAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sumit Agrawal"}},
	"MANAGER_SUMIT_BHATNAGAR":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sumit Bhatnagar"}},
	"MANAGER_SUNAINA_CUNHA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sunaina Cunha"}},
	"MANAGER_SUNIL_PATIL":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sunil Patil"}},
	"MANAGER_SURBHI_SHARMA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Surbhi Sharma"}},
	"MANAGER_SUSHIL_BUDHIA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Sushil Budhia"}},
	"MANAGER_SUYASH_CHOUDHARY":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Suyash Choudhary"}},
	"MANAGER_SWAPNIL_MAYEKAR":        {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Swapnil Mayekar"}},
	"MANAGER_SWATI_KULKARNI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Swati Kulkarni"}},
	"MANAGER_TAHER_BADSHAH":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Taher Badshah"}},
	"MANAGER_TANMAYA_DESAI":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Tanmaya Desai"}},
	"MANAGER_TARUN_SINGH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Tarun Singh"}},
	"MANAGER_TEJAS_GUTKA":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Tejas Gutka"}},
	"MANAGER_TEJAS_SHETH":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Tejas Sheth"}},
	"MANAGER_TRIDEEP_BHATTACHARYA":   {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Trideep Bhattacharya"}},
	"MANAGER_UMESH_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Umesh Sharma"}},
	"MANAGER_UTKARSH_KATKORIA":       {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Utkarsh Katkoria"}},
	"MANAGER_V_BALASUBRAMANIAN":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"V Balasubramanian"}},
	"MANAGER_V_SRIVATSA":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"V Srivatsa"}},
	"MANAGER_VAIBHAV_DUSAD":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vaibhav Dusad"}},
	"MANAGER_VARUN_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Varun Sharma"}},
	"MANAGER_VASAV_SAHGAL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vasav Sahgal"}},
	"MANAGER_VENKAT_SAMALA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Venkat Samala"}},
	"MANAGER_VENKATESH_SANJEEVI":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Venkatesh Sanjeevi"}},
	"MANAGER_VENUGOPAL_MANGHAT":      {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Venugopal Manghat"}},
	"MANAGER_VIHAG_MISHRA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vihag Mishra"}},
	"MANAGER_VIHANG_NAIK":            {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vihang Naik"}},
	"MANAGER_VIKAS_AGRAWAL":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vikas Agrawal"}},
	"MANAGER_VIKAS_GARG":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vikas Garg"}},
	"MANAGER_VIKASH_AGARWAL":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vikash Agarwal"}},
	"MANAGER_VIKRAM_CHOPRA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vikram Chopra"}},
	"MANAGER_VIKRAM_PAMNANI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vikram Pamnani"}},
	"MANAGER_VIKRANT_MEHTA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vikrant Mehta"}},
	"MANAGER_VINAY_PAHARIA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vinay Paharia"}},
	"MANAGER_VINAY_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vinay Sharma"}},
	"MANAGER_VINEET_MALOO":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vineet Maloo"}},
	"MANAGER_VINIT_SAMBRE":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vinit Sambre"}},
	"MANAGER_VINOD_BHAT":             {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vinod Bhat"}},
	"MANAGER_VIRAJ_KULKARNI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Viraj Kulkarni"}},
	"MANAGER_VISHAL_CHOPDA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vishal Chopda"}},
	"MANAGER_VISHAL_GAJWANI":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vishal Gajwani"}},
	"MANAGER_VISHAL_MISHRA":          {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vishal Mishra"}},
	"MANAGER_VISHAL_THAKKER":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vishal Thakker"}},
	"MANAGER_VIVEK_RAMAKRISHNAN":     {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vivek Ramakrishnan"}},
	"MANAGER_VIVEK_SHARMA":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vivek Sharma"}},
	"MANAGER_VRIJESH_KASERA":         {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Vrijesh Kasera"}},
	"MANAGER_YOGESH_PATIL":           {QueryType: FilterType_MULTI_TERMED_OR, QueryField: "fundFundamentalDetails.fundManagers.managers.name", QueryTerms: []string{"Yogesh Patil"}},

	"LOW_DURATION_DEBT": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_LOW_DURATION.String(),
	},
	"QUANTUM_AMC": {
		QueryType:  FilterType_TERMED,
		QueryField: "amc",
		QueryTerm:  mfPb.Amc_QUANTUM.String(),
	},
	"SMALL_CAP_INDEX": {
		QueryType:  FilterType_TERMED,
		QueryField: "fiDefinedCategory",
		QueryTerm:  mfPb.MutualFundCategoryName_INDEX_FUNDS_SMALL_CAP.String(),
	},
	"US_FOCUSED_INDEX": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fiDefinedCategory",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_INDEX_FUNDS_S_AND_P_500.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_NASDAQ.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_US_TOTAL_MARKET.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_NYSE_FAANG.String(),
		},
	},
	"OTHERS_INDEX": {
		QueryType:  FilterType_MULTI_TERMED_OR,
		QueryField: "fiDefinedCategory",
		QueryTerms: []string{
			mfPb.MutualFundCategoryName_INDEX_FUNDS_BANKING.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_EQUAL_WEIGHT.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_SMART_BETA.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_TOTAL_MARKET.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_HEALTH_CARE.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_LARGE_AND_MID.String(),
			mfPb.MutualFundCategoryName_INDEX_FUNDS_ASIA_TECH.String(),
		},
	},
	"FOF_HYBRID_ALLOC": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_FUND_OF_FUNDS.String(),
	},
	"FLEXI_CAP_EQUITY": {
		QueryType:  FilterType_TERMED,
		QueryField: "categoryName",
		QueryTerm:  mfPb.MutualFundCategoryName_FLEXI_CAP.String(),
	},
	"GREATER_THAN_1_PER_YIELD_TO_MATURITY": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.yieldToMaturity",
		QueryUpperBound: 100,
		QueryLowerBound: 1,
	},
	"GREATER_THAN_3_PER_YIELD_TO_MATURITY": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.yieldToMaturity",
		QueryUpperBound: 100,
		QueryLowerBound: 3,
	},
	"GREATER_THAN_5_PER_YIELD_TO_MATURITY": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.yieldToMaturity",
		QueryUpperBound: 100,
		QueryLowerBound: 5,
	},
	"GREATER_THAN_7_PER_YIELD_TO_MATURITY": {
		QueryType:       FilterType_RANGED,
		QueryField:      "returns.yieldToMaturity",
		QueryUpperBound: 100,
		QueryLowerBound: 7,
	},
}
