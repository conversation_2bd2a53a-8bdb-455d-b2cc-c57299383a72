package decision_engine

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	"github.com/Knetic/govaluate"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/timeunit"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	afuPb "github.com/epifi/gamma/api/auth/afu"
	heatlthEnginePb "github.com/epifi/gamma/api/health_engine"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	beneficiaryManagementPb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/savings"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendors"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/pkg/banking"
	"github.com/epifi/gamma/pkg/feature"
	featureCfg "github.com/epifi/gamma/pkg/feature"
	"github.com/epifi/gamma/pkg/feature/release"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/upi"
	upiPkg "github.com/epifi/gamma/pkg/upi"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/errgroup"
)

// Methods to generate rules for various payment protocols
// We dont use expression parameter map anywhere
// Instead we use expression function map and the functions get the variables we need from the environment of the builder function
// This approach allows us to use editor features to write our conditions. Otherwise conditions have to be written between quotes
type ruleFactory struct{}

// The conditions are evaluated in the order they appear in the list.
// The error corresponding to first failed condition is propagated to the rule engine, so the conditions
// should be in a priority of user facing errors
// for eg. If two conditions A and B are failing and we want to show the user error corresponding to
// condition A the condition A should be before B in the list
// nolint: funlen
func (d *ruleFactory) buildINTRARule(ctx context.Context, salience int, currentActorId string, currentActorAccountPiIds []string, amount *moneyPb.Money, activeAccountPiTo, activeAccountPiFrom []*piPb.PaymentInstrument, isInternal, isInternalUser, isCollectRequest bool,
	hardPreferredPaymentProtocol paymentPb.PaymentProtocol, intraParams *ProtocolParams, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient, heClient heatlthEnginePb.HealthEngineServiceClient,
	evaluator release.IEvaluator, beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient) *rule {
	return &rule{
		name:     "Is INTRA Feasible",
		salience: salience,
		conditions: []*condition{
			{
				expr:        "isInternal()",
				onFailError: intraIsInternalError,
			},
			{
				expr:        "isPreferredINTRA()",
				onFailError: intraIsPreferredINTRAError,
			},
			{
				expr:        "doPIsExist()",
				onFailError: intraDoPIsExistError,
			},
			{
				expr:        "checkTotalTransactedAmountInCoolDown()",
				onFailError: totalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkTotalIntraTransactedAmountInCoolDown()",
				onFailError: intraCheckTotalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkAmountWithinRange()",
				onFailError: intraCheckAmountWithinRangeError,
			},
			{
				expr:        "isNotCollectReqForExternal()",
				onFailError: intraIsNotCollectReqForExternalError,
			},
			{
				expr:        "checkIfTimeNotInINTRARestrictedWindow()",
				onFailError: intraCheckIfTimeNotInRestrictedWindowError,
			},
			{
				expr:        "checkHealthOfINTRAForRemitterBank()",
				onFailError: intraCheckHealthForRemitterBank,
			},
			{
				expr:        "checkBeneficiaryIsEligibleForTxn()",
				onFailError: checkBeneficiaryCooldownError,
			},
		},
		expressionFunctionMap: map[string]govaluate.ExpressionFunction{
			"isInternal": func(args ...interface{}) (interface{}, error) {
				return isInternal == true, nil
			},
			"isPreferredINTRA": func(args ...interface{}) (interface{}, error) {
				res := hardPreferredPaymentProtocol == paymentPb.PaymentProtocol_INTRA_BANK ||
					hardPreferredPaymentProtocol == paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
				return res, nil
			},
			"checkAmountWithinRange": func(args ...interface{}) (interface{}, error) {
				if money.Compare(amount, &moneyPb.Money{Units: intraParams.MinAmount}) < 0 ||
					money.Compare(amount, &moneyPb.Money{Units: intraParams.MaxAmount}) > 0 {
					return false, nil
				}
				return true, nil
			},
			"doPIsExist": func(args ...interface{}) (interface{}, error) {
				return len(activeAccountPiTo) != 0 && len(activeAccountPiFrom) != 0, nil
			},
			"isNotCollectReqForExternal": func(args ...interface{}) (interface{}, error) {
				return !isCollectRequest || isInternalUser, nil

			},
			"checkIfTimeNotInINTRARestrictedWindow": func(args ...interface{}) (interface{}, error) {
				return checkIfPaymentProtocolNotRestricted(ctx, intraParams.ProtocolRestrictedWindow, paymentPb.PaymentProtocol_INTRA_BANK)
			},
			"checkTotalIntraTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDownForProtocol(ctx, currentActorId, currentActorAccountPiIds, amount, intraParams, orderClient, paymentPb.PaymentProtocol_INTRA_BANK, userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient)
			},
			"checkTotalTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDown(amount, intraParams)
			},
			// as of now user has only 1 account pi, so we directly send the first pi from list
			// todo(Harleen Singh): move this to a common method that should be called in fetchPaymentInstruments also
			"checkHealthOfINTRAForRemitterBank": func(arguments ...interface{}) (interface{}, error) {
				return checkIfPaymentsAreHealthyForRemitterBank(ctx, currentActorId, evaluator, heClient, paymentPb.PaymentProtocol_INTRA_BANK, activeAccountPiFrom[0].GetAccount().GetIfscCode())
			},
			"checkBeneficiaryIsEligibleForTxn": func(arguments ...interface{}) (interface{}, error) {
				return checkBeneficiaryIsEligibleForTxn(ctx, currentActorId, activeAccountPiFrom[0].GetId(), activeAccountPiTo[0].GetId(), paymentProtocolsForBeneficiaryCooldown, beneficiaryManagementClient, payClient, evaluator, amount,
					orderConfig.PaymentProtocolDecisionParams().BeneficiaryCoolDownAmountLimit())
			},
		},
		paymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		fetchPaymentInstruments: func() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {
			if len(activeAccountPiTo) != 0 && len(activeAccountPiFrom) != 0 {
				return activeAccountPiFrom[0], activeAccountPiTo[0], nil
			}
			return nil, nil, errors.New("(INTRA): Payment Instruments dont exist on both sides")
		},
	}
}

// nolint: funlen
func (*ruleFactory) buildUPIRule(ctx context.Context, salience int, upiMinAmount, upiMaxAmount int64,
	currentActorId,
	currentActorAccountId string,
	currentActorAccountPiIds []string,
	amount *moneyPb.Money,
	upiPiTo, upiLitePiTo,
	accountPiTo,
	upiPiFrom, upiLitePiFrom []*piPb.PaymentInstrument,
	isCollectRequest, isVerifiedMerchant bool,
	preferredProtocol paymentPb.PaymentProtocol,
	orderClient orderPb.OrderServiceClient, upiClient upiPb.UPIClient,
	upiParams *ProtocolParams, paymentClient paymentPb.PaymentClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient, heClient heatlthEnginePb.HealthEngineServiceClient,
	evaluator release.IEvaluator, purposeCode string, piClient piPb.PiClient,
	platform commontypes.Platform) *rule {
	return &rule{
		name:     "Is UPI Feasible",
		salience: salience,
		conditions: []*condition{
			{
				expr:        "isPreferredUPI()",
				onFailError: upiIsPreferredUPIError,
			},
			{
				expr:        "arePIsFromUPI()",
				onFailError: upiArePIsFromUPIError,
			},
			{
				expr:        "isValidCollectRequest()",
				onFailError: upiIsValidCollectRequestError,
			},
			{
				// checks if total transacted amount using all protocol is higher than what is allowed if payer is in cooldown period
				expr:        "checkTotalTransactedAmountInCoolDown()",
				onFailError: totalTransactedAmountLimitInCoolDownError,
			},
			{
				// checks if total transacted amount is higher than what is allowed if payer is in cooldown period
				expr:        "checkTotalUpiTransactedAmountInCoolDown()",
				onFailError: upiCheckTotalTransactedAmountLimitInCoolDownPeriodError,
			},
			{
				expr:        "checkAmountWithinRange()",
				onFailError: upiCheckAmountWithinRangeError,
			},
			{
				expr:        "declinePaymentsToAddFundsVpa()",
				onFailError: declinePaymentsToAddFundsVpaError,
			},
			{
				// checks if total transacted amount in the last x hours specified by UpiLimitWindow exceeds UpiTotalTransactedAmountLimit
				expr:        "checkTotalTransactedAmountInLimitWindow()",
				onFailError: upiCheckTotalTransactedAmountInLimitWindowError,
			},
			{
				// checks if number of transactions in the last x hours specified by UpiLimitWindow exceeds UpiTransactionCountLimit
				expr:        "checkTransactionCountInLimitWindow()",
				onFailError: upiCheckTransactionCountInLimitWindowError,
			},
			{
				// checks if total transacted amount is higher than what is allowed if payer has reset their UPI pin in pinResetLimitWindow
				expr:        "checkTransactionAmountLimitIfPinResetInPinSetWindow()",
				onFailError: upiCheckTransactionAmountLimitIfPinResetInPinSetWindow,
			},
			{
				expr:        "checkIfTimeNotInUPIRestrictedWindow()",
				onFailError: upiCheckIfTimeNotInRestrictedWindowError,
			},
			{
				expr:        "checkHealthOfUPIForRemitterBank()",
				onFailError: upiCheckHealthForRemitterBank,
			},
			{
				expr:        "isValidUpiLiteTopUp()",
				onFailError: upiIsValidUpiLiteTopUpErr,
			},
			{
				expr:        "isTransactionAllowedForAccountType()",
				onFailError: upiIsTransactionAllowedForAccountTypeError,
			},
		},
		expressionFunctionMap: map[string]govaluate.ExpressionFunction{
			"arePIsFromUPI": func(args ...interface{}) (interface{}, error) {
				isFeasible := (len(upiPiFrom) != 0 || len(upiLitePiFrom) != 0) && (len(upiPiTo) != 0 || len(accountPiTo) != 0 || len(upiLitePiTo) != 0)
				if !isFeasible {
					upiPiToIds, upiPiFromIds := getUpiToAndFromPi(upiPiTo, upiPiFrom)
					logger.Error(ctx, "upi payment is not feasible", zap.Strings(logger.PI_TO, upiPiToIds), zap.Strings(logger.PI_FROM, upiPiFromIds))
				}
				return isFeasible, nil
			},
			"isValidCollectRequest": func(args ...interface{}) (interface{}, error) {
				return !isCollectRequest || (len(upiPiTo) != 0 && len(upiLitePiTo) == 0 && (len(upiPiFrom) != 0 || len(upiLitePiFrom) != 0)), nil
			},
			"isPreferredUPI": func(args ...interface{}) (interface{}, error) {
				res := preferredProtocol == paymentPb.PaymentProtocol_UPI ||
					preferredProtocol == paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
				return res, nil
			},
			"checkAmountWithinRange": func(args ...interface{}) (interface{}, error) {
				if money.Compare(amount, &moneyPb.Money{Units: upiMinAmount}) < 0 ||
					money.Compare(amount, &moneyPb.Money{Units: upiMaxAmount}) > 0 {
					return false, nil
				}
				return true, nil
			},
			"declinePaymentsToAddFundsVpa": func(args ...interface{}) (interface{}, error) {
				var filteredUpiPiTo []*piPb.PaymentInstrument
				for _, pi := range upiPiTo {
					vpa, err := pi.GetVPA()
					if err != nil {
						return nil, fmt.Errorf("error fetching vpa for pi: %s, %w", pi.GetId(), err)
					}
					if !upi.IsAddFundsVpa(vpa) {
						filteredUpiPiTo = append(filteredUpiPiTo, pi)
					}
				}
				return len(filteredUpiPiTo) != 0 || len(accountPiTo) != 0 || len(upiLitePiTo) != 0, nil
			},
			"checkTotalTransactedAmountInLimitWindow": func(args ...interface{}) (interface{}, error) {
				if isCollectRequest || len(upiLitePiFrom) != 0 {
					return true, nil
				}
				var (
					err                                   error
					upiLimitWindow                        time.Duration
					upiLimitWindowStartTime               *timestamppb.Timestamp
					totalUpiTransactedAmountInLimitWindow *moneyPb.Money
					finalAmount                           *moneyPb.Money
				)
				upiLimitWindowConf := upiParams.ProtocolLimitWindow
				upiLimitWindow, err = timeunit.ToDuration(upiLimitWindowConf.Value, upiLimitWindowConf.TimeUnit)
				if err != nil {
					return false, fmt.Errorf("error parsing upiLimitWindow: %w", err)
				}

				upiLimitWindowStartTime = timestamppb.New(time.Now().Add(-1 * upiLimitWindow))
				totalUpiTransactedAmountInLimitWindow, err = getUpiTotalTxnAmount(ctx, currentActorId, currentActorAccountPiIds, upiLimitWindowStartTime, orderClient, userGroupClient, usersClient, orderConfig,
					actorClient, payClient)
				if err != nil {
					return false, fmt.Errorf("error getting upi total txn amount: %w", err)
				}
				finalAmount, err = money.Sum(totalUpiTransactedAmountInLimitWindow, amount)
				if err != nil {
					logger.ErrorNoCtx("error adding money", zap.Error(err))
					return false, err
				}
				if money.Compare(finalAmount, &moneyPb.Money{Units: upiParams.TotalTransactedAmountLimit}) > 0 {
					return false, nil
				}
				return true, nil
			},
			"checkTransactionCountInLimitWindow": func(args ...interface{}) (interface{}, error) {
				if isCollectRequest || len(upiLitePiFrom) != 0 {
					return true, nil
				}

				// if the payee PI is merchant then by pass the velocity check for transaction
				// count as it is limited for P2P payments.
				if len(upiPiTo) > 0 && upiPiTo[0].IsMerchantPI() {
					return true, nil
				}

				var (
					err                           error
					upiLimitWindow                time.Duration
					upiLimitWindowStartTime       *timestamppb.Timestamp
					totalNoOfUpiTxnsInLimitWindow int64
				)
				upiLimitWindowConf := upiParams.ProtocolLimitWindow
				upiLimitWindow, err = timeunit.ToDuration(upiLimitWindowConf.Value, upiLimitWindowConf.TimeUnit)
				if err != nil {
					return false, fmt.Errorf("error parsing upiLimitWindow: %w", err)
				}

				upiLimitWindowStartTime = timestamppb.New(time.Now().Add(-1 * upiLimitWindow))
				totalNoOfUpiTxnsInLimitWindow, err = getUpiTotalTxnCount(ctx, currentActorId, currentActorAccountPiIds, upiLimitWindowStartTime, payClient, paymentClient, userGroupClient, usersClient,
					orderConfig,
					actorClient,
				)
				if err != nil {
					return false, fmt.Errorf("error getting upi total txn amount: %w", err)
				}
				if totalNoOfUpiTxnsInLimitWindow+1 > upiParams.TransactionCountLimit {
					return false, nil
				}
				return true, nil
			},
			"checkTotalUpiTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				if isCollectRequest || len(upiLitePiFrom) != 0 {
					return true, nil
				}
				// cooldown restriction does not apply for payment to verified merchants
				if isVerifiedMerchant {
					return true, nil
				}
				return checkTotalTransactedAmountInCoolDownForProtocol(ctx, currentActorId, currentActorAccountPiIds, amount, upiParams, orderClient, paymentPb.PaymentProtocol_UPI, userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient)
			},
			"checkTotalTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				if isCollectRequest || len(upiLitePiFrom) != 0 {
					return true, nil
				}
				// cooldown restriction does not apply for payment to verified merchants
				if isVerifiedMerchant {
					return true, nil
				}
				return checkTotalTransactedAmountInCoolDown(amount, upiParams)
			},
			"checkTransactionAmountLimitIfPinResetInPinSetWindow": func(args ...interface{}) (interface{}, error) {
				if isCollectRequest || len(upiLitePiFrom) != 0 {
					return true, nil
				}

				return checkTransactionAmountLimitIfPinResetInPinSetWindow(ctx, currentActorId, currentActorAccountId, currentActorAccountPiIds, amount, upiParams, orderClient,
					upiClient,
					upiOnboardingClient,
					userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient)

			},
			"checkIfTimeNotInUPIRestrictedWindow": func(args ...interface{}) (interface{}, error) {
				return checkIfPaymentProtocolNotRestricted(ctx, upiParams.ProtocolRestrictedWindow, paymentPb.PaymentProtocol_UPI)
			},
			"isValidUpiLiteTopUp": func(args ...interface{}) (interface{}, error) {
				if purposeCode != vendors.DefaultPurposeUpiLiteTopUp {
					return true, nil
				}
				// top up can only be done from the source upi account of upi lite
				return len(upiLitePiTo) != 0 && len(upiPiFrom) != 0 && upiLitePiTo[0].GetUpiLite().GetPiRefId() == upiPiFrom[0].GetId(), nil
			},
			"checkHealthOfUPIForRemitterBank": func(args ...interface{}) (interface{}, error) {
				if len(upiLitePiFrom) != 0 {
					sourcePi, err := getSourcePiForUpiLite(ctx, upiLitePiFrom[0], piClient)
					if err != nil {
						return nil, fmt.Errorf("error fetching source pi for upi lite pi: %s, %w", upiLitePiFrom[0].GetId(), err)
					}
					return checkIfPaymentsAreHealthyForRemitterBank(ctx, currentActorId, evaluator, heClient, paymentPb.PaymentProtocol_UPI, sourcePi.GetUpi().GetIfscCode())
				}
				return checkIfPaymentsAreHealthyForRemitterBank(ctx, currentActorId, evaluator, heClient, paymentPb.PaymentProtocol_UPI, upiPiFrom[0].GetUpi().GetIfscCode())
			},
			"isTransactionAllowedForAccountType": func(arguments ...interface{}) (interface{}, error) {
				// allow only P2M transactions for UOD accounts
				if len(upiPiFrom) > 0 && lo.Contains([]accounts.Type{accounts.Type_OVER_DRAFT, accounts.Type_UNSECURED_OVERDRAFT}, upiPiFrom[0].GetUpi().GetAccountType()) {
					if len(upiPiTo) > 0 && upiPiTo[0].IsMerchantPI() {
						return true, nil
					}
					return false, nil
				}
				// allow only P2M transactions for RuPay CC accounts
				if len(upiPiFrom) > 0 && lo.Contains([]accounts.Type{accounts.Type_CREDIT}, upiPiFrom[0].GetUpi().GetAccountType()) {
					if len(upiPiTo) > 0 && upiPiTo[0].IsMerchantPI() {
						// Check RuPay CC restrictions if applicable for payment to merchant for a particular amount.
						accepted, err := payPkg.IsRuPayCCAcceptedForMerchant(ctx, upiClient, upiPiTo[0].GetUpi().GetVpa(), amount)
						switch {
						case err != nil:
							logger.Error(ctx, "error checking ruPay cc acceptance", zap.String(logger.PI_TO, upiPiTo[0].GetId()), zap.Error(err))
							return false, ruPayCreditCardTransactionNotAllowedError
						case !accepted:
							return false, ruPayCreditCardTransactionAmountLimitError
						default:
							return true, nil
						}
					}
					// if upiPiTo is not a merchant, then Credit PIs would have already been filtered out earlier in the flow, so this point should never be reached.
					// ref - https://github.com/epiFi/gamma/blob/f00a005bb1d26d8f002aab500dd6353126577ca7/order/payment/decision_engine/federal_decision_taker.go#L606-L607
					return false, nil
				}
				return true, nil
			},
		},
		paymentProtocol: paymentPb.PaymentProtocol_UPI,
		fetchPaymentInstruments: func() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {
			// currently while making fund transfer order, we always get the account details of the actor from, so we will always get either
			// upi lite account or upi account, we currently merge both upi and upi lite pi list, and send the first account, we are doing the same
			// while checking the health of remitter bank as well, there we just use the source account pi bank ifsc to check the health of remitter
			// For any further change here in logic of fetching pi from, we must make changes in health check rule accordingly
			if (len(upiPiFrom) != 0 || len(upiLitePiFrom) != 0) && (len(upiPiTo) != 0 || len(accountPiTo) != 0 || len(upiLitePiTo) != 0) {
				if len(upiLitePiTo) != 0 {
					return upiPiFrom[0], upiLitePiTo[0], nil
				}

				var finalPiFrom []*piPb.PaymentInstrument
				finalPiFrom = append(finalPiFrom, upiLitePiFrom...)
				finalPiFrom = append(finalPiFrom, upiPiFrom...)
				if len(upiPiTo) == 0 {
					return finalPiFrom[0], accountPiTo[0], nil
				}
				// filtering out add funds vpa, payment to add funds vpa should not be allowed
				for _, pi := range upiPiTo {
					vpa, err := pi.GetVPA()
					if err != nil {
						return nil, nil, fmt.Errorf("error fetching vpa: %w", err)
					}
					if !upi.IsAddFundsVpa(vpa) {
						return finalPiFrom[0], pi, nil
					}
				}
			}
			return nil, nil, errors.New("(UPI): PIs not available")
		},
	}
}

// checkBeneficiaryIsEligibleForTxn will check if the beneficiary is in cooldown or not
// Current logic for imposing the cooldown (subjected to change with time and product calls):
// 1. new beneficiary added in last 24 hours
// 2. transacted_amount + current_transaction_amount > allowed_transaction_amount
func checkBeneficiaryIsEligibleForTxn(ctx context.Context, actorFrom, piFrom, piTo string, paymentProtocols []paymentPb.PaymentProtocol, beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient, payclient pay.PayClient, evaluator release.IEvaluator, amount *moneyPb.Money,
	beneficiaryCoolDownAmountLimit int64) (bool, error) {
	isBeneficiaryCoolDownAllowed, isBeneficiaryCoolDownAllowedErr := evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_BENEFICIARY_COOL_DOWN_RULE).WithActorId(actorFrom))

	if isBeneficiaryCoolDownAllowedErr != nil {
		logger.Info(ctx, "Error from constraint check", zap.Error(isBeneficiaryCoolDownAllowedErr))
		// todo(Rahul Kumar): return false post the UTs are fixed
		return true, isBeneficiaryCoolDownAllowedErr
	}
	if !isBeneficiaryCoolDownAllowed {
		return true, nil
	}
	// fetch the beneficiary
	beneficiaryResponse, beneficiaryResponseErr := beneficiaryManagementClient.GetBeneficiary(ctx, &beneficiaryManagementPb.GetBeneficiaryRequest{
		ActorFrom: actorFrom,
		PiTo:      piTo,
	})

	// return true if no beneficary has been created for the given actorFrom<->piTo OR beneficiary is not in the cooldown.
	// NOTE: As of now we are creating beneficiary only when fromActor resolves the piTo i.e. fromActor initiates a bank_transfer for the first time
	if beneficiaryResponse.GetStatus().IsRecordNotFound() || !datetime.IsBetweenTimestamp(time.Now(), beneficiaryResponse.GetBeneficiary().GetCooldownStartTime().AsTime(), beneficiaryResponse.GetBeneficiary().GetCooldownEndTime().AsTime()) {
		return true, nil
	}

	if teErr := epifigrpc.RPCError(beneficiaryResponse, beneficiaryResponseErr); teErr != nil {
		return false, fmt.Errorf("error in getting beneficiary data: %v", teErr)
	}

	aggregatesResp, aggregatesRespErr := payclient.GetTransactionAggregates(ctx, &pay.GetTransactionAggregatesRequest{
		ActorId:             actorFrom,
		PiIds:               []string{piFrom},
		OtherPiIds:          []string{piTo},
		AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
		FromTime:            beneficiaryResponse.GetBeneficiary().GetCooldownStartTime(),
		ToTime:              timestamppb.New(time.Now()),
		PaymentProtocol:     paymentProtocols,
		TransactionsStatus:  []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
	})

	if aggregatesErr := epifigrpc.RPCError(aggregatesResp, aggregatesRespErr); aggregatesErr != nil {
		logger.Error(ctx, "failed to fetch the amount aggregates between 2 PIs in the beneficiary flow", zap.String(logger.PI_FROM, piFrom), zap.String(logger.PI_TO, piTo), zap.Error(aggregatesErr))
		// *************** NOTE *******************
		// conscious call to supress the error from aggregates since we have seen pinot going down multiple times, it should not block the payments
		// todo(Harleen Singh): remove this once the pinot is stable
		return true, nil
	}

	aggregatedAmount, aggregatedAmountErr := money.Sum(amount, aggregatesResp.GetTransactionAggregates().GetSumAmount())
	if aggregatedAmountErr != nil {
		logger.Error(ctx, "error while adding transaction amount and transacted amount", zap.Error(aggregatedAmountErr))
		// todo(Rahul Kumar): return false post the UTs are fixed
		return true, aggregatedAmountErr
	}

	// check if the beneficiary is in the cooldown and the transacted_amount + current_transaction_amount < allowed_transaction_amount
	if money.Compare(aggregatedAmount, &moneyPb.Money{Units: beneficiaryCoolDownAmountLimit}) == 1 {
		return false, nil
	}

	return true, nil
}

// checkIfPaymentsAreHealthyForRemitterBank will check if the payment for the given protocol
// is healthy or not using health engine data
// suppressing the grpc status error from health engine since it should not block the payments flow
func checkIfPaymentsAreHealthyForRemitterBank(ctx context.Context, actorId string, evaluator release.IEvaluator, heClient heatlthEnginePb.HealthEngineServiceClient, paymentProtocol paymentPb.PaymentProtocol, remitterIfsc string) (bool, error) {
	featureFlag := getHealthEngineFeature(paymentProtocol)
	if featureFlag == types.Feature_FEATURE_UNSPECIFIED {
		return false, fmt.Errorf("feature is not defined for this protocol: %s", paymentProtocol.String())
	}

	isHealthEngineAllowed, isHealthEngineAllowedErr := evaluator.Evaluate(ctx, release.NewCommonConstraintData(featureFlag).WithActorId(actorId))
	if isHealthEngineAllowedErr != nil {
		return false, isHealthEngineAllowedErr
	}
	if !isHealthEngineAllowed {
		return true, nil
	}

	var req = &heatlthEnginePb.GetHealthStatusRequest{}
	var transactionMetricsData = &heatlthEnginePb.MatrixDataRequest_TransactionMetric{
		TransactionMetric: &heatlthEnginePb.TransactionMetric{},
	}

	remitterBank, err := banking.GetBankFromIfsc(remitterIfsc)
	if err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Info(ctx, "bank not found for given ifsc", zap.String(logger.IFSC_CODE, remitterIfsc))
		default:
			logger.Error(ctx, "error fetching bank for given ifsc", zap.String(logger.IFSC_CODE, remitterIfsc), zap.Error(err))
		}
		// considering payments as healthy as we don't want to block the user from transacting if are not able to fetch bank from ifsc
		return true, nil
	}

	transactionMetricsData.TransactionMetric.BankName = remitterBank
	transactionMetricsData.TransactionMetric.TxnType = paymentPb.AccountingEntryType_DEBIT
	transactionMetricsData.TransactionMetric.PaymentProtocol = paymentProtocol

	req.MatrixData = &heatlthEnginePb.MatrixDataRequest{
		MatrixData: transactionMetricsData,
	}
	getHealthStatusRes, getHealthStatusErr := heClient.GetHealthStatus(ctx, req)
	if getHealthStatusErr != nil {
		return false, getHealthStatusErr
	}
	if getHealthStatusRes.GetStatus().GetCode() == uint32(heatlthEnginePb.GetHealthStatusResponse_UNHEALTHY_STATE) {
		return false, nil
	}
	return true, nil
}

func getHealthEngineFeature(paymentProtocol paymentPb.PaymentProtocol) types.Feature {
	switch paymentProtocol {
	case paymentPb.PaymentProtocol_IMPS:
		return types.Feature_HEALTH_ENGINE_FOR_IMPS_PAYMENTS
	case paymentPb.PaymentProtocol_NEFT:
		return types.Feature_HEALTH_ENGINE_FOR_NEFT_PAYMENTS
	case paymentPb.PaymentProtocol_RTGS:
		return types.Feature_HEALTH_ENGINE_FOR_RTGS_PAYMENTS
	case paymentPb.PaymentProtocol_INTRA_BANK:
		return types.Feature_HEALTH_ENGINE_FOR_INTRA_PAYMENTS
	case paymentPb.PaymentProtocol_UPI:
		return types.Feature_HEALTH_ENGINE_FOR_PAYMENTS
	default:
		return types.Feature_FEATURE_UNSPECIFIED
	}
}

// nolint: funlen
func (*ruleFactory) buildIMPSRule(ctx context.Context, salience int, currentActorId string, currentActorAccountPiIds []string, amount *moneyPb.Money, accountPiTo, accountPiFrom []*piPb.PaymentInstrument, preferredProtocol paymentPb.PaymentProtocol, isInternalUser, isCollectRequest bool,
	impsParams *ProtocolParams, orderClient orderPb.OrderServiceClient, paymentClient paymentPb.PaymentClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient, heClient heatlthEnginePb.HealthEngineServiceClient,
	evaluator release.IEvaluator, beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient) *rule {
	return &rule{
		name:     "Is IMPS Feasible",
		salience: salience,
		conditions: []*condition{
			{
				expr:        "isPreferredIMPS()",
				onFailError: impsIsPreferredIMPSError,
			},
			{
				expr:        "checkTotalTransactedAmountInCoolDown()",
				onFailError: totalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkTotalImpsTransactedAmountInCoolDown()",
				onFailError: impsCheckTotalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkAmountWithinRange()",
				onFailError: impsCheckAmountWithinRangeError,
			},
			{
				expr:        "doPIssExist()",
				onFailError: impsDoPIssExistError,
			},
			{
				expr:        "isNotCollectReqForExternal()",
				onFailError: impsIsNotCollectReqForExternalError,
			},
			{
				expr:        "checkIfTimeNotInIMPSRestrictedWindow()",
				onFailError: impsCheckIfTimeNotInRestrictedWindowError,
			},
			{
				expr:        "checkTransactionCountInLimitWindow()",
				onFailError: impsCheckTransactionCountInLimitWindowError,
			},
			{
				expr:        "checkHealthOfIMPSForRemitterBank()",
				onFailError: impsCheckHealthForRemitterBank,
			},
			{
				expr:        "doBanksSupportIMPS()",
				onFailError: impsBankNotSupportedError,
			},
			{
				expr:        "checkBeneficiaryIsEligibleForTxn()",
				onFailError: checkBeneficiaryCooldownError,
			},
		},
		expressionFunctionMap: map[string]govaluate.ExpressionFunction{
			"doPIssExist": func(args ...interface{}) (interface{}, error) {
				if len(accountPiTo) == 0 || len(accountPiFrom) == 0 {
					logger.Debug(ctx, "no pi found for imps transaction")
				}
				return len(accountPiTo) != 0 && len(accountPiFrom) != 0, nil
			},
			"isPreferredIMPS": func(args ...interface{}) (interface{}, error) {
				res := preferredProtocol == paymentPb.PaymentProtocol_IMPS ||
					preferredProtocol == paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
				if !res {
					logger.Debug(ctx, "imps not preferred for payment")
				}
				return res, nil
			},
			"checkAmountWithinRange": func(args ...interface{}) (interface{}, error) {
				if money.Compare(amount, &moneyPb.Money{Units: impsParams.MinAmount}) < 0 ||
					money.Compare(amount, &moneyPb.Money{Units: impsParams.MaxAmount}) > 0 {
					logger.Debug(ctx, "amount not in range for imps")
					return false, nil
				}
				return true, nil
			},
			"isNotCollectReqForExternal": func(args ...interface{}) (interface{}, error) {
				return !isCollectRequest || isInternalUser, nil
			},
			"doBanksSupportIMPS": func(args ...interface{}) (interface{}, error) {
				toPiFound := false
				for _, pi := range accountPiTo {
					ifsc := pi.GetAccount().GetIfscCode()
					if isImpsSupported(ifsc) {
						toPiFound = true
						break
					}
				}
				if !toPiFound {
					logger.Info(ctx, "imps not supported by beneficiary/remitter bank")
				}
				return toPiFound, nil
			},
			"checkIfTimeNotInIMPSRestrictedWindow": func(args ...interface{}) (interface{}, error) {
				return checkIfPaymentProtocolNotRestricted(ctx, impsParams.ProtocolRestrictedWindow, paymentPb.PaymentProtocol_IMPS)
			},
			"checkTotalImpsTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDownForProtocol(ctx, currentActorId, currentActorAccountPiIds, amount, impsParams, orderClient, paymentPb.PaymentProtocol_IMPS, userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient)
			},
			"checkTotalTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDown(amount, impsParams)
			},
			"checkTransactionCountInLimitWindow": func(args ...interface{}) (interface{}, error) {
				// merchant Account PIs do not have identifier for merchant PI and we do not want to
				// restrict IMPS transaction for merchant PI. We will be relying on preferred payment protocol for Account Merchant PI's
				// as internal service will always specify payment protocol to use for IMPS payment.
				if preferredProtocol == paymentPb.PaymentProtocol_IMPS {
					return true, nil
				}
				return checkImpsTxnCount(ctx, impsParams, currentActorId, currentActorAccountPiIds, payClient, paymentClient, userGroupClient,
					usersClient,
					orderConfig,
					actorClient)

			},
			// as of now user has only 1 account pi, so we directly send the first pi from list
			// todo(Harleen Singh): move this to a common method that should be called in fetchPaymentInstruments also
			"checkHealthOfIMPSForRemitterBank": func(arguments ...interface{}) (interface{}, error) {
				// as of now user has only 1 account pi, so we directly send the first pi from list
				// todo(Harleen Singh): move this to a common method that should be called in fetchPaymentInstruments also
				return checkIfPaymentsAreHealthyForRemitterBank(ctx, currentActorId, evaluator, heClient, paymentPb.PaymentProtocol_IMPS, accountPiFrom[0].GetAccount().GetIfscCode())
			},
			"checkBeneficiaryIsEligibleForTxn": func(arguments ...interface{}) (interface{}, error) {
				return checkBeneficiaryIsEligibleForTxn(ctx, currentActorId, accountPiFrom[0].GetId(), accountPiTo[0].GetId(), paymentProtocolsForBeneficiaryCooldown, beneficiaryManagementClient, payClient, evaluator, amount,
					orderConfig.PaymentProtocolDecisionParams().BeneficiaryCoolDownAmountLimit())
			},
		},
		paymentProtocol: paymentPb.PaymentProtocol_IMPS,
		fetchPaymentInstruments: func() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {
			if len(accountPiTo) != 0 && len(accountPiFrom) != 0 {
				return accountPiFrom[0], accountPiTo[0], nil
			}
			return nil, nil, errors.New("(IMPS): No Account PIs present")
		},
	}
}

// nolint
func (*ruleFactory) buildRTGSRule(ctx context.Context, salience int, currentActorId string, currentActorAccountPiIds []string,
	amount *moneyPb.Money,
	accountPiTo,
	accountPiFrom []*piPb.PaymentInstrument,
	preferredProtocol paymentPb.PaymentProtocol,
	isInternalUser,
	isCollectRequest bool, rtgsParams *ProtocolParams, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient, heClient heatlthEnginePb.HealthEngineServiceClient,
	evaluator release.IEvaluator, beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient) *rule {
	return &rule{
		name:     "Is RTGS Feasible",
		salience: salience,
		conditions: []*condition{
			{
				expr:        "isPreferredRTGS()",
				onFailError: rtgsIsPreferredRTGSError,
			},
			{
				expr:        "checkIfscInBlockListForRtgs()",
				onFailError: rtgsIfscNotSupportedError,
			},
			{
				expr:        "checkTotalTransactedAmountInCoolDown()",
				onFailError: totalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkTotalRtgsTransactedAmountInCoolDown()",
				onFailError: rtgsCheckTotalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkAmountWithinRange()",
				onFailError: rtgsCheckAmountWithinRangeError,
			},
			{
				expr:        "doPIssExist()",
				onFailError: rtgsDoPIsExistError,
			},
			{
				expr:        "isNotCollectReqForExternal()",
				onFailError: rtgsIsNotCollectReqForExternalError,
			},
			{
				expr:        "checkIfTimeNotInRTGSRestrictedWindow()",
				onFailError: rtgsCheckIfTimeNotInRestrictedWindowError,
			},
			{

				expr:        "checkHealthOfRTGSForRemitterBank()",
				onFailError: rtgsCheckHealthForRemitterBank,
			},
			{
				expr:        "checkBeneficiaryIsEligibleForTxn()",
				onFailError: checkBeneficiaryCooldownError,
			},
		},
		expressionFunctionMap: map[string]govaluate.ExpressionFunction{
			"doPIssExist": func(args ...interface{}) (interface{}, error) {
				return len(accountPiTo) != 0 && len(accountPiFrom) != 0, nil
			},
			"isPreferredRTGS": func(args ...interface{}) (interface{}, error) {
				res := preferredProtocol == paymentPb.PaymentProtocol_RTGS ||
					preferredProtocol == paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
				return res, nil
			},
			"checkIfscInBlockListForRtgs": func(args ...interface{}) (interface{}, error) {
				for _, pi := range accountPiTo {
					if isIfscInBlockListForRtgs(pi.GetAccount().GetIfscCode()) {
						return false, nil
					}
				}
				return true, nil
			},
			"checkAmountWithinRange": func(args ...interface{}) (interface{}, error) {
				if money.Compare(amount, &moneyPb.Money{Units: rtgsParams.MinAmount}) < 0 ||
					money.Compare(amount, &moneyPb.Money{Units: rtgsParams.MaxAmount}) > 0 {
					return false, nil
				}
				return true, nil
			},
			"isNotCollectReqForExternal": func(args ...interface{}) (interface{}, error) {
				return !isCollectRequest || isInternalUser, nil
			},
			"checkIfTimeNotInRTGSRestrictedWindow": func(args ...interface{}) (interface{}, error) {
				return checkIfPaymentProtocolNotRestricted(ctx, rtgsParams.ProtocolRestrictedWindow, paymentPb.PaymentProtocol_RTGS)
			},
			"checkTotalRtgsTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDownForProtocol(ctx, currentActorId, currentActorAccountPiIds, amount, rtgsParams, orderClient, paymentPb.PaymentProtocol_RTGS, userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient)
			},
			"checkTotalTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDown(amount, rtgsParams)
			},
			// as of now user has only 1 account pi, so we directly send the first pi from list
			// todo(Harleen Singh): move this to a common method that should be called in fetchPaymentInstruments also
			"checkHealthOfRTGSForRemitterBank": func(arguments ...interface{}) (interface{}, error) {
				return checkIfPaymentsAreHealthyForRemitterBank(ctx, currentActorId, evaluator, heClient, paymentPb.PaymentProtocol_RTGS, accountPiFrom[0].GetAccount().GetIfscCode())
			},
			"checkBeneficiaryIsEligibleForTxn": func(arguments ...interface{}) (interface{}, error) {
				return checkBeneficiaryIsEligibleForTxn(ctx, currentActorId, accountPiFrom[0].GetId(), accountPiTo[0].GetId(), paymentProtocolsForBeneficiaryCooldown, beneficiaryManagementClient, payClient, evaluator, amount,
					orderConfig.PaymentProtocolDecisionParams().BeneficiaryCoolDownAmountLimit())
			},
		},
		paymentProtocol: paymentPb.PaymentProtocol_RTGS,
		fetchPaymentInstruments: func() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {
			if len(accountPiTo) != 0 && len(accountPiFrom) != 0 {
				return accountPiFrom[0], accountPiTo[0], nil
			}
			return nil, nil, errors.New("(RTGS): No Account PIs present")
		},
	}
}

// nolint
func (*ruleFactory) buildNEFTRule(ctx context.Context, salience int, currentActorId string, currentActorAccountPiIds []string,
	amount *moneyPb.Money,
	accountPiTo,
	accountPiFrom []*piPb.PaymentInstrument,
	preferredProtocol paymentPb.PaymentProtocol,
	isInternalUser,
	isCollectRequest bool, neftParams *ProtocolParams, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient, heClient heatlthEnginePb.HealthEngineServiceClient,
	evaluator release.IEvaluator, beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient) *rule {
	return &rule{
		name:     "Is NEFT Feasible",
		salience: salience,
		conditions: []*condition{
			{
				expr:        "isPreferredNEFT()",
				onFailError: neftIsPreferredNEFTError,
			},
			{
				expr:        "checkTotalTransactedAmountInCoolDown()",
				onFailError: totalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkTotalNeftTransactedAmountInCoolDown()",
				onFailError: neftCheckTotalTransactedAmountLimitInCoolDownError,
			},
			{
				expr:        "checkAmountWithinRange()",
				onFailError: neftCheckAmountWithinRangeError,
			},
			{
				expr:        "doPIssExist()",
				onFailError: neftDoPIssExistError,
			},
			{
				expr:        "isNotCollectReqForExternal()",
				onFailError: neftIsNotCollectReqForExternalError,
			},
			{
				expr:        "checkIfTimeNotInNEFTRestrictedWindow()",
				onFailError: neftCheckIfTimeNotInRestrictedWindowError,
			},
			{
				expr:        "checkHealthOfNEFTForRemitterBank()",
				onFailError: neftCheckHealthForRemitterBank,
			},
			{
				expr:        "checkIfTimeNotInDailyNEFTDownTime()",
				onFailError: neftCheckIfTimeNotInRestrictedWindowError,
			},
			{
				expr:        "checkBeneficiaryIsEligibleForTxn()",
				onFailError: checkBeneficiaryCooldownError,
			},
		},
		expressionFunctionMap: map[string]govaluate.ExpressionFunction{
			"doPIssExist": func(args ...interface{}) (interface{}, error) {
				return len(accountPiTo) != 0 && len(accountPiFrom) != 0, nil
			},
			"isPreferredNEFT": func(args ...interface{}) (interface{}, error) {
				res := preferredProtocol == paymentPb.PaymentProtocol_NEFT ||
					preferredProtocol == paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
				return res, nil
			},
			"checkAmountWithinRange": func(args ...interface{}) (interface{}, error) {
				if money.Compare(amount, &moneyPb.Money{Units: neftParams.MinAmount}) < 0 ||
					money.Compare(amount, &moneyPb.Money{Units: neftParams.MaxAmount}) > 0 {
					return false, nil
				}
				return true, nil
			},
			"isNotCollectReqForExternal": func(args ...interface{}) (interface{}, error) {
				return !isCollectRequest || isInternalUser, nil
			},
			"checkIfTimeNotInNEFTRestrictedWindow": func(args ...interface{}) (interface{}, error) {
				return checkIfPaymentProtocolNotRestricted(ctx, neftParams.ProtocolRestrictedWindow, paymentPb.PaymentProtocol_NEFT)
			},
			"checkTotalNeftTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDownForProtocol(ctx, currentActorId, currentActorAccountPiIds, amount, neftParams, orderClient, paymentPb.PaymentProtocol_NEFT, userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient)
			},
			"checkTotalTransactedAmountInCoolDown": func(args ...interface{}) (interface{}, error) {
				return checkTotalTransactedAmountInCoolDown(amount, neftParams)
			},
			// as of now user has only 1 account pi, so we directly send the first pi from list
			// todo(Harleen Singh): move this to a common method that should be called in fetchPaymentInstruments also
			"checkHealthOfNEFTForRemitterBank": func(arguments ...interface{}) (interface{}, error) {
				return checkIfPaymentsAreHealthyForRemitterBank(ctx, currentActorId, evaluator, heClient, paymentPb.PaymentProtocol_NEFT, accountPiFrom[0].GetAccount().GetIfscCode())
			},
			"checkIfTimeNotInDailyNEFTDownTime": func(arguments ...interface{}) (interface{}, error) {
				return checkIfPaymentProtocolNotInDailyDownTime(neftParams.ProtocolDailyDowntime, paymentPb.PaymentProtocol_NEFT)
			},
			"checkBeneficiaryIsEligibleForTxn": func(arguments ...interface{}) (interface{}, error) {
				return checkBeneficiaryIsEligibleForTxn(ctx, currentActorId, accountPiFrom[0].GetId(), accountPiTo[0].GetId(), paymentProtocolsForBeneficiaryCooldown, beneficiaryManagementClient, payClient, evaluator, amount,
					orderConfig.PaymentProtocolDecisionParams().BeneficiaryCoolDownAmountLimit())
			},
		},
		paymentProtocol: paymentPb.PaymentProtocol_NEFT,
		fetchPaymentInstruments: func() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {
			if len(accountPiTo) != 0 && len(accountPiFrom) != 0 {
				return accountPiFrom[0], accountPiTo[0], nil
			}
			return nil, nil, errors.New("(NEFT): No Account PIs present")
		},
	}
}

// helper method to fetch number of upi transactions in PAID, IN_PAYMENT status by actorId, since fromTime
// nolint:funlen
func getUpiTotalTxnCount(ctx context.Context, actorId string, accountPiIds []string, fromTime *timestamppb.Timestamp, payClient pay.PayClient, paymentClient paymentPb.PaymentClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
) (int64, error) {
	return getProtocolTotalTxnCount(ctx, actorId, accountPiIds, fromTime, payClient, []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_UPI}, paymentClient, userGroupClient,
		usersClient,
		orderConfig,
		actorClient)
}

// helper method to fetch upi total transacted amount in PAID, IN_PAYMENT status by actorId, since fromTime
// nolint:funlen
func getUpiTotalTxnAmount(ctx context.Context, actorId string, accountPiIds []string, fromTime *timestamppb.Timestamp, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient,
) (*moneyPb.Money, error) {
	return getProtocolTotalTxnAmount(ctx, actorId, accountPiIds, fromTime, orderClient, []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_UPI}, userGroupClient, usersClient, orderConfig, actorClient, payClient, false, nil, nil, orderConfig.PinotQueryTimeout())
}

// isCoolOffApplicable: checks if cool off is applicable for the account / device.
//  1. checks for the device registration cool off.
//  2. If device registration cool off is not applicable
//     then cool off on the basis of last account linking
//     timestamp is checked.
func isCoolOffApplicable(ctx context.Context, accountId, actorId string, fromTime *timestamppb.Timestamp,
	authClient authPb.AuthClient, clients *featureEvalHelperClients, orderConfig *config.Config) (bool, *timestamppb.Timestamp, error) {
	// check if device is registered after 'fromTime'
	isDeviceInCoolOffPeriod, coolDownTriggerTime, err := isDeviceInCoolOff(ctx, actorId, fromTime, authClient, orderConfig)
	if isDeviceInCoolOffPeriod {
		return isDeviceInCoolOffPeriod, coolDownTriggerTime, err
	}

	// cool-off inapplicable to internal user as an exception
	if featureCfg.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().BypassTPAPCoolDown(), clients.userGroupClient, clients.usersClient, clients.actorClient) {
		return false, nil, nil
	}

	return upiPkg.IsAccountInCoolOff(ctx, accountId, fromTime, clients.upiOnboardingClient)
}

// checks if the device is in cool off period. If true, also returns the registration timestamp.
// NOTE - This will help us apply cool offs in case of both registrations and re-registrations.
// In case of re-registrations we will apply cool offs based on info fetched from the GetAfuSummaries
// as well, so this will be overridden in those cases.
func isDeviceInCoolOff(ctx context.Context, actorId string, fromTime *timestamppb.Timestamp, authClient authPb.AuthClient, orderConfig *config.Config) (bool, *timestamppb.Timestamp, error) {
	authRes, err := authClient.GetFirstDeviceRegAfterTime(ctx, &authPb.GetFirstDeviceRegAfterTimeRequest{
		ActorId:  actorId,
		FromTime: fromTime,
	})
	switch {
	case err != nil:
		return false, nil, err
	case authRes.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode():
		return false, nil, nil
	case !authRes.GetStatus().IsSuccess():
		return false, nil, fmt.Errorf("error fetching device details for actor: %s", actorId)
	}

	// ref: https://drive.google.com/file/d/1UMefJQuPzirb-GOPT9bHKJZf0J8BOj3G/view
	maxCoolDownTriggerDurationLimit := orderConfig.PaymentProtocolDecisionParams().UPIParams().UpiProfileUpdateCoolDownTriggerDurationLimit()
	tm, err := timeunit.ToDuration(maxCoolDownTriggerDurationLimit.Value, maxCoolDownTriggerDurationLimit.TimeUnit)
	if err != nil {
		return false, nil, fmt.Errorf("error parsing protocol cool down trigger duration limit: %w", err)
	}
	coolDownTriggerTime := timestamppb.Now().AsTime().Add(-tm)
	if authRes.GetDeviceRegistration().GetCreatedAt().AsTime().After(coolDownTriggerTime) {
		coolDownTriggerTime = authRes.GetDeviceRegistration().GetCreatedAt().AsTime()
	}
	return true, timestamppb.New(coolDownTriggerTime), nil
}

func getLatestPinSetTime(ctx context.Context, accountId string, pinResetLimitWindow time.Duration, upiClient upiPb.UPIClient) (*timestamppb.Timestamp, error) {
	upiPinResetLimitWindowStartTime := timestamppb.New(time.Now().Add(-1 * pinResetLimitWindow))
	res, err := upiClient.GetPinInfosByAccountId(ctx, &upiPb.GetPinInfosByAccountIdRequest{
		FromTime:  upiPinResetLimitWindowStartTime,
		AccountId: accountId,
		Limit:     1,
		SelectMask: []upiPb.AccountUpiPinInfoFieldMask{
			upiPb.AccountUpiPinInfoFieldMask_CREATED_AT,
		},
		UserActions: []upiPb.GetPinInfosByAccountIdRequest_Action{
			upiPb.GetPinInfosByAccountIdRequest_PIN_SET,
			upiPb.GetPinInfosByAccountIdRequest_PIN_RESET,
		},
		Status: upiPb.GetPinInfosByAccountIdRequest_SUCCESS,
		SortBy: upiPb.AccountUpiPinInfoFieldMask_CREATED_AT,
	})
	switch {
	case err != nil:
		return nil, err
	case res.GetStatus().IsInvalidArgument():
		return nil, fmt.Errorf("GetPinInfosByAccountId() rpc returned invalid argument, account id : %s", accountId)
	case res.GetStatus().IsRecordNotFound():
		return nil, nil
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetPinInfosByAccountId() rpc call failed, account id : %s", accountId)
	}
	var accountPinInfo *upiPb.AccountUpiPinInfo
	if len(res.GetAccountUpiPinInfos()) != 0 {
		accountPinInfo = res.GetAccountUpiPinInfos()[0]
	}
	return accountPinInfo.GetCreatedAt(), nil
}

func getLatestPinSetForTpapAccounts(ctx context.Context, accountId string, pinResetLimitWindow time.Duration, upiClient upiPb.UPIClient, upiOnboardingClient upiOnboardingPb.UpiOnboardingClient) (*timestamppb.Timestamp, error) {
	res, err := upiOnboardingClient.GetLatestUpiRequestLogForAccount(ctx, &upiOnboardingPb.GetLatestUpiRequestLogForAccountRequest{
		AccountId: accountId,
		ApiTypes: []upiOnboardingEnumsPb.UpiRequestLogApiType{
			upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_CHANGE_PIN,
			upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE,
		},
		ApiStatus: upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
	})

	switch {
	case err != nil:
		return nil, err
	case res.GetStatus().IsInvalidArgument():
		return nil, fmt.Errorf("GetLatestUpiRequestLogByAccountId() rpc returned invalid argument, account id : %s", accountId)
	case res.GetStatus().IsRecordNotFound():
		// fall back to savings account in case tpap account is not found
		return getLatestPinSetTime(ctx, accountId, pinResetLimitWindow, upiClient)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetLatestUpiRequestLogByAccountId() rpc call failed, account id : %s", accountId)
	}

	// if request was made before pin reset limit window
	if time.Since(res.GetUpiRequestLog().GetCreatedAt().AsTime()) > pinResetLimitWindow {
		return nil, nil
	}

	return res.GetUpiRequestLog().GetCreatedAt(), nil
}

// this function will check if the given paymentProtocol is restricted(down) or not
// return true,nil if it is not restricted
// return false,err if there is error in any step
// return false,nil if the give paymentProtocol is restricted(down)
func checkIfPaymentProtocolNotRestricted(ctx context.Context, r *config.RestrictedWindow, protocolType paymentPb.PaymentProtocol) (bool, error) {
	if r == nil {
		return true, nil
	}
	restrictedWindowString := " restricted window"
	protocolName := protocolType.String()
	if !r.IsEnabled() {
		return true, nil
	}
	timeZoneLocation, err := time.LoadLocation(r.Timezone())
	if err != nil {
		errorString := "error loading location for " + protocolName + restrictedWindowString
		logger.Error(ctx, errorString)
		return false, err
	}
	dateTimeFormat := r.DateTimeFormat()
	restrictedStartDateTime, err := time.ParseInLocation(dateTimeFormat, r.FromDateTime(), timeZoneLocation)
	if err != nil {
		errorString := "error parsing fromDateTime for " + protocolName + restrictedWindowString
		logger.Error(ctx, errorString)
		return false, err
	}
	restrictedEndDateTime, err := time.ParseInLocation(dateTimeFormat, r.ToDateTime(), timeZoneLocation)
	if err != nil {
		errorString := "error parsing toDateTime for " + protocolName + restrictedWindowString
		logger.Error(ctx, errorString)
		return false, err
	}
	currentTime := time.Now().In(timeZoneLocation)
	if inTimeSpan(restrictedStartDateTime, restrictedEndDateTime, currentTime) {
		return false, nil
	}
	return true, nil
}

// this function will check if the given paymentProtocol's total transacted amount in cooldown exceeded or not
// return true,nil if it is not restricted
// return false,err if there is error in any step
// return false,nil if the give paymentProtocol is restricted(down)
func checkTotalTransactedAmountInCoolDownForProtocol(ctx context.Context, currentActorId string, currentActorAccountPiIds []string, amount *moneyPb.Money, protocolParams *ProtocolParams, orderClient orderPb.OrderServiceClient, paymentProtocol paymentPb.PaymentProtocol,
	userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient) (bool, error) {
	if !protocolParams.IsRestrictedInCoolDown {
		return true, nil
	}

	totalTransactedAmountInCoolDown, err := getProtocolTotalTxnAmount(ctx, currentActorId, currentActorAccountPiIds, protocolParams.CoolDownTriggerTime, orderClient, []paymentPb.PaymentProtocol{paymentProtocol}, userGroupClient, usersClient, orderConfig,
		actorClient, payClient, false, nil, nil, orderConfig.PinotQueryTimeout())
	if err != nil {
		return false, fmt.Errorf("error getting given payment protocol total txn amount:%s: %w", paymentProtocol.String(), err)
	}
	finalAmount, err := money.Sum(totalTransactedAmountInCoolDown, amount)
	if err != nil {
		return false, fmt.Errorf("error while adding money: %w", err)
	}
	if money.Compare(finalAmount, &moneyPb.Money{Units: protocolParams.TotalTransactedAmountUsingProtocolLimitInCoolDown}) > 0 {
		return false, getProtocolLevelCoolDownErrorDueToLimitExceeded(paymentProtocol, protocolParams.ProfileUpdateTypeInCoolDownForProtocol)
	}
	return true, nil
}

// returns cooldown error if total transacted amount through given protocol exceeded than cooldown limit
// nolint: funlen
func getProtocolLevelCoolDownErrorDueToLimitExceeded(paymentProtocol paymentPb.PaymentProtocol, profileUpdateType paymentPb.CoolDownDetails_ProfileUpdateType) error {
	switch {
	case paymentProtocol == paymentPb.PaymentProtocol_UPI && profileUpdateType == paymentPb.CoolDownDetails_NEW_USER_COOLDOWN:
		return upiCheckTotalTransactedAmountLimitInNewUserCoolDownPeriodError
	case paymentProtocol == paymentPb.PaymentProtocol_UPI && profileUpdateType == paymentPb.CoolDownDetails_DEVICE_COOLDOWN:
		return upiCheckTotalTransactedAmountLimitInDeviceCoolDownPeriodError
	case paymentProtocol == paymentPb.PaymentProtocol_UPI && profileUpdateType == paymentPb.CoolDownDetails_EMAIL_COOLDOWN:
		return upiCheckTotalTransactedAmountLimitInEmailCoolDownPeriodError
	case paymentProtocol == paymentPb.PaymentProtocol_UPI && profileUpdateType == paymentPb.CoolDownDetails_MOBILE_COOLDOWN:
		return upiCheckTotalTransactedAmountLimitInMobileCoolDownPeriodError

	case paymentProtocol == paymentPb.PaymentProtocol_INTRA_BANK && profileUpdateType == paymentPb.CoolDownDetails_NEW_USER_COOLDOWN:
		return intraCheckTotalTransactedAmountLimitInNewUserCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_INTRA_BANK && profileUpdateType == paymentPb.CoolDownDetails_DEVICE_COOLDOWN:
		return intraCheckTotalTransactedAmountLimitInDeviceCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_INTRA_BANK && profileUpdateType == paymentPb.CoolDownDetails_EMAIL_COOLDOWN:
		return intraCheckTotalTransactedAmountLimitInEmailCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_INTRA_BANK && profileUpdateType == paymentPb.CoolDownDetails_MOBILE_COOLDOWN:
		return intraCheckTotalTransactedAmountLimitInMobileCoolDownError

	case paymentProtocol == paymentPb.PaymentProtocol_IMPS && profileUpdateType == paymentPb.CoolDownDetails_NEW_USER_COOLDOWN:
		return impsCheckTotalTransactedAmountLimitInNewUserCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_IMPS && profileUpdateType == paymentPb.CoolDownDetails_DEVICE_COOLDOWN:
		return impsCheckTotalTransactedAmountLimitInDeviceCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_IMPS && profileUpdateType == paymentPb.CoolDownDetails_EMAIL_COOLDOWN:
		return impsCheckTotalTransactedAmountLimitInEmailCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_IMPS && profileUpdateType == paymentPb.CoolDownDetails_MOBILE_COOLDOWN:
		return impsCheckTotalTransactedAmountLimitInMobileCoolDownError

	case paymentProtocol == paymentPb.PaymentProtocol_NEFT && profileUpdateType == paymentPb.CoolDownDetails_NEW_USER_COOLDOWN:
		return neftCheckTotalTransactedAmountLimitInNewUserCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_NEFT && profileUpdateType == paymentPb.CoolDownDetails_DEVICE_COOLDOWN:
		return neftCheckTotalTransactedAmountLimitInDeviceCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_NEFT && profileUpdateType == paymentPb.CoolDownDetails_EMAIL_COOLDOWN:
		return neftCheckTotalTransactedAmountLimitInEmailCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_NEFT && profileUpdateType == paymentPb.CoolDownDetails_MOBILE_COOLDOWN:
		return neftCheckTotalTransactedAmountLimitInMobileCoolDownError

	case paymentProtocol == paymentPb.PaymentProtocol_RTGS && profileUpdateType == paymentPb.CoolDownDetails_NEW_USER_COOLDOWN:
		return rtgsCheckTotalTransactedAmountLimitInNewUserCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_RTGS && profileUpdateType == paymentPb.CoolDownDetails_DEVICE_COOLDOWN:
		return rtgsCheckTotalTransactedAmountLimitInDeviceCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_RTGS && profileUpdateType == paymentPb.CoolDownDetails_EMAIL_COOLDOWN:
		return rtgsCheckTotalTransactedAmountLimitInEmailCoolDownError
	case paymentProtocol == paymentPb.PaymentProtocol_RTGS && profileUpdateType == paymentPb.CoolDownDetails_MOBILE_COOLDOWN:
		return rtgsCheckTotalTransactedAmountLimitInMobileCoolDownError

	default:
		return nil
	}
}

// this function will check if total transacted amount through protocols (neft, rtgs, imps, intra and upi) in cooldown exceeded or not
// return true,nil if it is not restricted
// return false,err if there is error in any step
// return false,nil if the give paymentProtocol is restricted(down)
func checkTotalTransactedAmountInCoolDown(amount *moneyPb.Money, protocolParams *ProtocolParams) (bool, error) {
	if !protocolParams.IsAllRestrictedInCoolDown {
		return true, nil
	}

	finalAmount, err := money.Sum(protocolParams.TotalTransactedAmountInCoolDown, amount)
	if err != nil {
		return false, fmt.Errorf("error while adding money: %w", err)
	}
	if money.Compare(finalAmount, &moneyPb.Money{Units: protocolParams.TotalTransactedAmountLimitInCoolDown}) > 0 {
		return false, getCoolDownErrorDueToTotalTransactedAmountLimitExceeded(protocolParams.ProfileUpdateTypeInCoolDown)
	}
	return true, nil
}

// checkTransactionAmountLimitIfPinResetInPinSetWindow checks if the transaction is valid when pin reset has occurred
// return true,nil if it is not restricted
// return false,err if there is error in any step
// return false,nil if the give paymentProtocol is restricted(down)
func checkTransactionAmountLimitIfPinResetInPinSetWindow(ctx context.Context, currentActorId string, currentActorAccountId string, currentActorAccountPiIds []string, amount *moneyPb.Money, upiParams *ProtocolParams, orderClient orderPb.OrderServiceClient,
	upiClient upiPb.UPIClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient) (bool, error) {

	if !upiParams.IsRestrictedAfterPinReset {
		return true, nil
	}

	var (
		latestAccountUpiPinInfoCreatedTime       *timestamppb.Timestamp
		totalUpiTransactedAmountSinceUpiPinReset *moneyPb.Money
	)

	pinResetLimitWindow := upiParams.PinResetLimitWindow
	window, err := timeunit.ToDuration(pinResetLimitWindow.Value, pinResetLimitWindow.TimeUnit)
	if err != nil {
		return false, fmt.Errorf("error parsing UpiPinResetLimitWindow: %w", err)
	}
	latestAccountUpiPinInfoCreatedTime, err = getLatestPinSetForTpapAccounts(ctx, currentActorAccountId, window, upiClient, upiOnboardingClient)
	if err != nil {
		return false, fmt.Errorf("error getting latest pin set time: %w", err)
	}
	if latestAccountUpiPinInfoCreatedTime == nil {
		return true, nil
	}
	totalUpiTransactedAmountSinceUpiPinReset, err = getUpiTotalTxnAmount(ctx, currentActorId, currentActorAccountPiIds, latestAccountUpiPinInfoCreatedTime, orderClient, userGroupClient, usersClient, orderConfig,
		actorClient, payClient)
	if err != nil {
		return false, fmt.Errorf("error getting upi total txn amount: %w", err)
	}
	finalAmount, err := money.Sum(totalUpiTransactedAmountSinceUpiPinReset, amount)
	if err != nil {
		return false, fmt.Errorf("error getting sum of amounts: %w", err)
	}
	if money.Compare(finalAmount, &moneyPb.Money{Units: upiParams.PinResetTotalTransactedAmountLimit}) > 0 {
		return false, nil
	}
	return true, nil
}

// return cooldown error if total transacted amount through all protocol exceeded cooldown amount limit
func getCoolDownErrorDueToTotalTransactedAmountLimitExceeded(profileUpdateType paymentPb.CoolDownDetails_ProfileUpdateType) error {
	switch {
	case profileUpdateType == paymentPb.CoolDownDetails_NEW_USER_COOLDOWN:
		return totalTransactedAmountLimitInNewUserCoolDownError
	case profileUpdateType == paymentPb.CoolDownDetails_DEVICE_COOLDOWN:
		return totalTransactedAmountLimitInDeviceCoolDownError
	case profileUpdateType == paymentPb.CoolDownDetails_EMAIL_COOLDOWN:
		return totalTransactedAmountLimitInEmailCoolDownError
	case profileUpdateType == paymentPb.CoolDownDetails_MOBILE_COOLDOWN:
		return totalTransactedAmountLimitInMobileCoolDownError

	default:
		return nil
	}
}

type featureEvalHelperClients struct {
	userGroupClient     userGroupPb.GroupClient
	usersClient         user.UsersClient
	actorClient         actorPb.ActorClient
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient
}

// fetches protocol level params based factors like last profile update time
// nolint: funlen
func getPaymentProtocolParamsMap(ctx context.Context,
	params *config.PaymentProtocolDecisionParams,
	currentActorAccountId,
	actorId string,
	accountPiIds []string,
	authClient authPb.AuthClient,
	orderClient orderPb.OrderServiceClient,
	userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	activeAccountPiTos []*piPb.PaymentInstrument,
	platform commontypes.Platform) (map[paymentPb.PaymentProtocol]*ProtocolParams, error) {

	var (
		restrictedTime, tm   *timestamppb.Timestamp
		isRestricted         bool
		coolDownType         paymentPb.CoolDownDetails_ProfileUpdateType
		protocolfromTimelist []*timestamppb.Timestamp
		protocolMap          = map[paymentPb.PaymentProtocol]*ProtocolParams{
			paymentPb.PaymentProtocol_UPI: {
				MinAmount:                          params.UPIParams().UpiMinAmount(),
				MaxAmount:                          params.UPIParams().UpiMaxAmount(),
				ProtocolRestrictedWindow:           params.UPIParams().UPIRestrictedWindow(),
				TotalTransactedAmountLimit:         params.UPIParams().UpiTotalTransactedAmountLimit(),
				TransactionCountLimit:              params.UPIParams().UpiTransactionCountLimit(),
				ProtocolLimitWindow:                params.UPIParams().UpiLimitWindow(),
				PinResetTotalTransactedAmountLimit: params.UPIParams().UpiPinResetTotalTransactedAmountLimit(),
				PinResetLimitWindow:                params.UPIParams().UpiPinResetLimitWindow(),
				ProfileUpdateCoolDownWindow:        params.UPIParams().UpiProfileUpdateCoolDownWindow()[platform.String()],
				TotalTransactedAmountUsingProtocolLimitInCoolDown: params.UPIParams().UpiCoolDownPeriodTotalTransactedAmountLimit(),
				TotalTransactedAmountLimitInCoolDown:              params.TotalTransactedAmountLimitInCoolDown(),
				IsRestrictedInCoolDown:                            false,
				IsAllRestrictedInCoolDown:                         false,
				IsRestrictedAfterPinReset:                         true,
			},
			paymentPb.PaymentProtocol_NEFT: {
				MinAmount:                params.NEFTParams().MinAmount(),
				MaxAmount:                params.NEFTParams().MaxAmount(),
				ProtocolRestrictedWindow: params.NEFTParams().ProtocolRestrictedWindow(),
				TotalTransactedAmountUsingProtocolLimitInCoolDown: params.NEFTParams().TotalTransactedAmountLimitInCoolDown(),
				ProfileUpdateCoolDownWindow:                       params.NEFTParams().ProfileUpdateCoolDownWindow(),
				TotalTransactedAmountLimitInCoolDown:              params.TotalTransactedAmountLimitInCoolDown(),
				IsRestrictedInCoolDown:                            false,
				IsAllRestrictedInCoolDown:                         false,
				ProtocolDailyDowntime:                             params.NEFTParams().ProtocolDailyDowntime(),
			},
			paymentPb.PaymentProtocol_IMPS: {
				MinAmount:                params.IMPSParams().MinAmount(),
				MaxAmount:                params.IMPSParams().MaxAmount(),
				ProtocolRestrictedWindow: params.IMPSParams().ProtocolRestrictedWindow(),
				TotalTransactedAmountUsingProtocolLimitInCoolDown: params.IMPSParams().TotalTransactedAmountLimitInCoolDown(),
				ProfileUpdateCoolDownWindow:                       params.IMPSParams().ProfileUpdateCoolDownWindow(),
				TotalTransactedAmountLimitInCoolDown:              params.TotalTransactedAmountLimitInCoolDown(),
				IsRestrictedInCoolDown:                            false,
				IsAllRestrictedInCoolDown:                         false,
				TransactionCountLimit:                             params.IMPSParams().TransactionCountLimit(),
				ProtocolLimitWindow:                               params.IMPSParams().LimitWindow(),
			},
			paymentPb.PaymentProtocol_RTGS: {
				MinAmount:                params.RTGSParams().MinAmount(),
				MaxAmount:                params.RTGSParams().MaxAmount(),
				ProtocolRestrictedWindow: params.RTGSParams().ProtocolRestrictedWindow(),
				TotalTransactedAmountUsingProtocolLimitInCoolDown: params.RTGSParams().TotalTransactedAmountLimitInCoolDown(),
				ProfileUpdateCoolDownWindow:                       params.RTGSParams().ProfileUpdateCoolDownWindow(),
				TotalTransactedAmountLimitInCoolDown:              params.TotalTransactedAmountLimitInCoolDown(),
				IsRestrictedInCoolDown:                            false,
				IsAllRestrictedInCoolDown:                         false,
			},
			paymentPb.PaymentProtocol_INTRA_BANK: {
				MinAmount:                params.INTRAParams().MinAmount(),
				MaxAmount:                params.INTRAParams().MaxAmount(),
				ProtocolRestrictedWindow: params.INTRAParams().ProtocolRestrictedWindow(),
				TotalTransactedAmountUsingProtocolLimitInCoolDown: params.INTRAParams().TotalTransactedAmountLimitInCoolDown(),
				ProfileUpdateCoolDownWindow:                       params.INTRAParams().ProfileUpdateCoolDownWindow(),
				TotalTransactedAmountLimitInCoolDown:              params.TotalTransactedAmountLimitInCoolDown(),
				IsRestrictedInCoolDown:                            false,
				IsAllRestrictedInCoolDown:                         false,
			},
		}
		featureEvalClients = &featureEvalHelperClients{
			upiOnboardingClient: upiOnboardingClient,
			usersClient:         usersClient,
			actorClient:         actorClient,
			userGroupClient:     userGroupClient,
		}
	)

	if isUserWhitelisted := feature.IsFeatureEnabledForUser(ctx, actorId, orderConfig.FeatureFlags().EnableBypassCooldownForWhitelistedUser(), userGroupClient, usersClient, actorClient); isUserWhitelisted {
		protocolMap[paymentPb.PaymentProtocol_UPI].IsRestrictedAfterPinReset = false
		return protocolMap, nil
	}

	// for getting earliest starting cooldown Time among all protocol and overall cooldown
	fromTime, err := getProtocolCoolDownWindowTime(params.ProfileUpdateCoolDownWindow())
	if err != nil {
		return protocolMap, fmt.Errorf("Overall: %w", err)
	}
	rtgsFromTime, err := getProtocolCoolDownWindowTime(params.RTGSParams().ProfileUpdateCoolDownWindow())
	if err != nil {
		return protocolMap, fmt.Errorf("RTGS: %w", err)
	}
	neftFromTime, err := getProtocolCoolDownWindowTime(params.NEFTParams().ProfileUpdateCoolDownWindow())
	if err != nil {
		return protocolMap, fmt.Errorf("NEFT: %w", err)
	}
	intraFromTime, err := getProtocolCoolDownWindowTime(params.INTRAParams().ProfileUpdateCoolDownWindow())
	if err != nil {
		return protocolMap, fmt.Errorf("INTRA: %w", err)
	}
	impsFromTime, err := getProtocolCoolDownWindowTime(params.IMPSParams().ProfileUpdateCoolDownWindow())
	if err != nil {
		return protocolMap, fmt.Errorf("IMPS: %w", err)
	}
	upiFromTime, err := getProtocolCoolDownWindowTime(params.UPIParams().UpiProfileUpdateCoolDownWindow()[platform.String()])
	if err != nil {
		return protocolMap, fmt.Errorf("UPI: %w", err)
	}

	// Need to get oldest timestamp for every Protocol (To Prevent fraud)
	restrictedTime = timestamppb.Now()
	protocolMap[paymentPb.PaymentProtocol_NEFT].CoolDownTriggerTime = timestamppb.Now()
	protocolMap[paymentPb.PaymentProtocol_RTGS].CoolDownTriggerTime = timestamppb.Now()
	protocolMap[paymentPb.PaymentProtocol_INTRA_BANK].CoolDownTriggerTime = timestamppb.Now()
	protocolMap[paymentPb.PaymentProtocol_IMPS].CoolDownTriggerTime = timestamppb.Now()
	protocolMap[paymentPb.PaymentProtocol_UPI].CoolDownTriggerTime = timestamppb.Now()

	isCoolOffApplicablePeriod, coolDownTriggerTime, _ := isCoolOffApplicable(ctx, currentActorAccountId, actorId,
		upiFromTime, authClient, featureEvalClients, orderConfig)
	if isCoolOffApplicablePeriod {
		protocolMap[paymentPb.PaymentProtocol_UPI].IsRestrictedInCoolDown = true
		protocolMap[paymentPb.PaymentProtocol_UPI].CoolDownTriggerTime = coolDownTriggerTime
		protocolMap[paymentPb.PaymentProtocol_UPI].ProfileUpdateTypeInCoolDownForProtocol = paymentPb.CoolDownDetails_NEW_USER_COOLDOWN
		protocolMap[paymentPb.PaymentProtocol_UPI].TotalTransactedAmountUsingProtocolLimitInCoolDown = params.UPIParams().UpiNewUserTotalTransactedAmountLimit()
	}

	if upiFromTime, err = getProtocolCoolDownWindowTime(params.UPIParams().UpiProfileUpdateAfuSummariesFetchDuration()); err != nil {
		return protocolMap, fmt.Errorf("UPI: %w", err)
	}
	protocolfromTimelist = append(protocolfromTimelist, fromTime, rtgsFromTime, neftFromTime, intraFromTime, impsFromTime, upiFromTime)
	earliestTime := getEarliestTime(protocolfromTimelist)

	// fetching summaries for all the AFUs done by a user in CoolDownPeriod
	authRes, err := authClient.GetAFUSummaries(ctx, &authPb.GetAFUSummariesRequest{
		ActorId:  actorId,
		Statuses: []afuPb.OverallStatus{afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
		FromTime: earliestTime,
	})

	switch {
	case err != nil:
		return protocolMap, fmt.Errorf("error fetching afu summaries details for actor: %s, Error: %w", actorId, err)
	case authRes.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode():
		return protocolMap, nil
	case !authRes.GetStatus().IsSuccess():
		return protocolMap, fmt.Errorf("GetAFUSummaries() rpc call failed with status: %s", authRes.GetStatus().String())
	}

	for _, afuSummary := range authRes.GetAfuSummaries() {
		// Ignore AFU whose overall status is not completed
		if afuSummary.AfuStatus != afuPb.OverallStatus_OVERALL_STATUS_COMPLETED {
			continue
		}

		tm = afuSummary.UpdatedAt
		authfactor := afuSummary.GetAuthFactors()[0]
		// overall
		if datetime.IsBetweenTimestamp(tm.AsTime(), fromTime.AsTime(), restrictedTime.AsTime()) {
			isRestricted = true
			restrictedTime = tm
			coolDownType = getCoolDownTypeFromAuthFactor(authfactor)
		}

		// upi
		getProtocolEarliestStartCoolDown(tm, upiFromTime, paymentPb.PaymentProtocol_UPI, protocolMap, authfactor)
		// imps
		getProtocolEarliestStartCoolDown(tm, impsFromTime, paymentPb.PaymentProtocol_IMPS, protocolMap, authfactor)
		// intra
		getProtocolEarliestStartCoolDown(tm, intraFromTime, paymentPb.PaymentProtocol_INTRA_BANK, protocolMap, authfactor)
		// neft
		getProtocolEarliestStartCoolDown(tm, neftFromTime, paymentPb.PaymentProtocol_NEFT, protocolMap, authfactor)
		// rtgs
		getProtocolEarliestStartCoolDown(tm, rtgsFromTime, paymentPb.PaymentProtocol_RTGS, protocolMap, authfactor)
	}

	// override cool off for intra bank for self account transfer via intra bank protocol.
	if isCoolOffNotApplicablePisToForIntraBank(activeAccountPiTos, orderConfig.PaymentProtocolDecisionParams().INTRAParams().IntraCoolOffNotApplicablePis()) {
		protocolMap[paymentPb.PaymentProtocol_INTRA_BANK].IsRestrictedInCoolDown = false
	}

	if isRestricted {
		totalAmount, err := getTotalTxnAmount(ctx, actorId, accountPiIds, restrictedTime, orderClient, userGroupClient, usersClient, orderConfig,
			actorClient, payClient)
		if err != nil {
			return protocolMap, fmt.Errorf("error fetching total amount if in restriction: %w", err)
		}
		protocolMap[paymentPb.PaymentProtocol_INTRA_BANK].TotalTransactedAmountInCoolDown = totalAmount
		protocolMap[paymentPb.PaymentProtocol_INTRA_BANK].IsAllRestrictedInCoolDown = true
		protocolMap[paymentPb.PaymentProtocol_INTRA_BANK].ProfileUpdateTypeInCoolDown = coolDownType

		// override cool off for intra bank for self account transfer via intra bank protocol.
		if isCoolOffNotApplicablePisToForIntraBank(activeAccountPiTos, orderConfig.PaymentProtocolDecisionParams().INTRAParams().IntraCoolOffNotApplicablePis()) {
			protocolMap[paymentPb.PaymentProtocol_INTRA_BANK].IsAllRestrictedInCoolDown = false
		}

		protocolMap[paymentPb.PaymentProtocol_UPI].TotalTransactedAmountInCoolDown = totalAmount
		protocolMap[paymentPb.PaymentProtocol_UPI].IsAllRestrictedInCoolDown = true
		protocolMap[paymentPb.PaymentProtocol_UPI].ProfileUpdateTypeInCoolDown = coolDownType
		protocolMap[paymentPb.PaymentProtocol_NEFT].TotalTransactedAmountInCoolDown = totalAmount
		protocolMap[paymentPb.PaymentProtocol_NEFT].IsAllRestrictedInCoolDown = true
		protocolMap[paymentPb.PaymentProtocol_NEFT].ProfileUpdateTypeInCoolDown = coolDownType
		protocolMap[paymentPb.PaymentProtocol_RTGS].TotalTransactedAmountInCoolDown = totalAmount
		protocolMap[paymentPb.PaymentProtocol_RTGS].IsAllRestrictedInCoolDown = true
		protocolMap[paymentPb.PaymentProtocol_RTGS].ProfileUpdateTypeInCoolDown = coolDownType
		protocolMap[paymentPb.PaymentProtocol_IMPS].TotalTransactedAmountInCoolDown = totalAmount
		protocolMap[paymentPb.PaymentProtocol_IMPS].IsAllRestrictedInCoolDown = true
		protocolMap[paymentPb.PaymentProtocol_IMPS].ProfileUpdateTypeInCoolDown = coolDownType
		return protocolMap, nil
	}

	return protocolMap, nil
}

func getCoolDownTypeFromAuthFactor(authfactor afuPb.AuthFactor) paymentPb.CoolDownDetails_ProfileUpdateType {
	switch {
	case authfactor == afuPb.AuthFactor_DEVICE:
		return paymentPb.CoolDownDetails_DEVICE_COOLDOWN
	case authfactor == afuPb.AuthFactor_EMAIL:
		return paymentPb.CoolDownDetails_EMAIL_COOLDOWN
	case authfactor == afuPb.AuthFactor_PHONE_NUM:
		return paymentPb.CoolDownDetails_MOBILE_COOLDOWN
	default:
		return paymentPb.CoolDownDetails_COOLDOWN_DETAILS_TYPE_UNSPECIFIED
	}
}

// helper method to convert timeDuration type to *timestamp.Timestamp
func getProtocolCoolDownWindowTime(protocolCoolDownWindow *cfg.TimeDuration) (*timestamppb.Timestamp, error) {
	coolOffWindow, err := timeunit.ToDuration(protocolCoolDownWindow.Value, protocolCoolDownWindow.TimeUnit)
	if err != nil {
		return nil, fmt.Errorf("error parsing protocol coolOffWindow: %w", err)
	}
	fromTime := timestamppb.New(time.Now().Add(-1 * coolOffWindow))
	return fromTime, nil
}

// helper method to fetch earliest time among list of time
func getEarliestTime(timeList []*timestamppb.Timestamp) *timestamppb.Timestamp {
	earliestTime := timestamppb.Now()
	for _, tm := range timeList {
		if earliestTime.AsTime().After(tm.AsTime()) {
			earliestTime = tm
		}
	}
	return earliestTime
}

// helper method to fetch earliest triggered cooldown Time for given payment protocol
func getProtocolEarliestStartCoolDown(tm *timestamppb.Timestamp, protocolFromTime *timestamppb.Timestamp, protocol paymentPb.PaymentProtocol, protcolParams map[paymentPb.PaymentProtocol]*ProtocolParams, authFactor afuPb.AuthFactor) {
	if datetime.IsBetweenTimestamp(tm.AsTime(), protocolFromTime.AsTime(), protcolParams[protocol].CoolDownTriggerTime.AsTime()) {
		protcolParams[protocol].IsRestrictedInCoolDown = true
		protcolParams[protocol].CoolDownTriggerTime = tm
		protcolParams[protocol].ProfileUpdateTypeInCoolDownForProtocol = getCoolDownTypeFromAuthFactor(authFactor)
	}
}

// helper method to fetch total transacted amount in PAID, IN_PAYMENT status by actorId, since fromTime
// nolint:funlen
func getTotalTxnAmount(ctx context.Context, actorId string, accountPiIds []string, fromTime *timestamppb.Timestamp, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient) (*moneyPb.Money, error) {

	var totalTransactionAmount *moneyPb.Money

	// Check if optimization flag is enabled
	if feature.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().EnableOptimisationForGetTotalTransactionAmountParams(),
		userGroupClient, usersClient, actorClient) {

		// Try using Pinot RPC for optimization
		getAggregatesTotalTxnAmountResponse, err := payClient.GetTransactionAggregates(ctx, &pay.GetTransactionAggregatesRequest{
			ActorId:  actorId,
			PiIds:    accountPiIds,
			FromTime: fromTime,
			TransactionsStatus: []paymentPb.TransactionStatus{
				paymentPb.TransactionStatus_SUCCESS,
				paymentPb.TransactionStatus_UNKNOWN,
				paymentPb.TransactionStatus_MANUAL_INTERVENTION,
				paymentPb.TransactionStatus_IN_PROGRESS,
				paymentPb.TransactionStatus_INITIATED,
			},
			PaymentProtocol: []paymentPb.PaymentProtocol{
				paymentPb.PaymentProtocol_INTRA_BANK,
				paymentPb.PaymentProtocol_NEFT,
				paymentPb.PaymentProtocol_IMPS,
				paymentPb.PaymentProtocol_RTGS,
				paymentPb.PaymentProtocol_UPI,
			},
			AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
			AccountTypes:        []accounts.Type{accounts.Type_SAVINGS},
		})

		switch {
		case err != nil:
			// Log error and fall back to standard CRDB flow
			logger.Error(ctx, "Error in getting transaction aggregates from pinot", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		case !getAggregatesTotalTxnAmountResponse.GetStatus().IsSuccess():
			// Log failure status and fall back to standard CRDB flow
			logger.Error(ctx, "Failed status in getting transaction aggregates from pinot",
				zap.String("status", getAggregatesTotalTxnAmountResponse.GetStatus().String()),
				zap.String(logger.ACTOR_ID_V2, actorId))
		default:
			// Success case - return the optimized result
			return getAggregatesTotalTxnAmountResponse.GetTransactionAggregates().GetSumAmount(), nil
		}
		if !feature.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().EnableFallbackForGetTotalTransactionAmountToCrdb(),
			userGroupClient, usersClient, actorClient) {
			return nil, fmt.Errorf("error while fetching total transaction amount from pinot: %w", epifigrpc.RPCError(getAggregatesTotalTxnAmountResponse, err))
		}
	}

	// Fall back to standard CRDB flow - using GetTotalTransactionAmount
	getTotalTxnAmountResponse, err := payClient.GetTotalTransactionAmount(ctx, &pay.GetTotalTransactionAmountRequest{
		ActorId:   actorId,
		PiIds:     accountPiIds,
		StartTime: fromTime,
		EndTime:   timestamppb.Now(),
		TransactionStatus: []paymentPb.TransactionStatus{
			paymentPb.TransactionStatus_SUCCESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_INITIATED,
		},
		PaymentProtocols: []paymentPb.PaymentProtocol{
			paymentPb.PaymentProtocol_INTRA_BANK,
			paymentPb.PaymentProtocol_NEFT,
			paymentPb.PaymentProtocol_IMPS,
			paymentPb.PaymentProtocol_RTGS,
			paymentPb.PaymentProtocol_UPI,
		},
		TransactionType: paymentPb.AccountingEntryType_DEBIT,
		AccountTypes:    []accounts.Type{accounts.Type_SAVINGS},
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("error while fetching total transaction amount: %w", err)
	case !getTotalTxnAmountResponse.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetTotalTransactionAmount() rpc call failed with status: %s", getTotalTxnAmountResponse.GetStatus().String())
	default:
		totalTransactionAmount = getTotalTxnAmountResponse.GetTotalAmount()
	}

	return totalTransactionAmount, nil
}

// getProtocolTotalTxnAmount retrieves the total transaction amount in PAID, IN_PAYMENT status by actorId for specified payment protocols within a time window.
// By default, fetches all transactions (on-app and off-app). For filtering:
// - Provide workflow/provenance to get only specific transactions
// - Example: For on-app txns only (incl. mutual funds), use workflows=[P2P_COLLECT, P2P_FUND_TRANSFER] and provenance=[USER_APP, THIRD_PARTY]
// nolint:funlen
func getProtocolTotalTxnAmount(ctx context.Context, actorId string, accountPiIds []string, fromTime *timestamppb.Timestamp, orderClient orderPb.OrderServiceClient, protocolTypes []paymentPb.PaymentProtocol, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient,
	shouldFetchOnlyOnAppTxns bool,
	workflow []orderPb.OrderWorkflow,
	provenance []orderPb.OrderProvenance, pinotQueryTimeout time.Duration) (*moneyPb.Money, error) {
	var totalTransactionAmount *moneyPb.Money

	timeoutCtx, cancel := context.WithTimeout(ctx, pinotQueryTimeout)
	defer cancel()

	// Check if optimization flag is enabled
	if feature.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().EnableOptimisationForGetTotalTransactionAmountParams(),
		userGroupClient, usersClient, actorClient) {
		getTransactionAggregatesRequest := &pay.GetTransactionAggregatesRequest{
			ActorId:  actorId,
			PiIds:    accountPiIds,
			FromTime: fromTime,
			TransactionsStatus: []paymentPb.TransactionStatus{
				paymentPb.TransactionStatus_SUCCESS,
				paymentPb.TransactionStatus_UNKNOWN,
				paymentPb.TransactionStatus_MANUAL_INTERVENTION,
				paymentPb.TransactionStatus_IN_PROGRESS,
				paymentPb.TransactionStatus_INITIATED,
			},
			PaymentProtocol:     protocolTypes,
			AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
			AccountTypes:        []accounts.Type{accounts.Type_SAVINGS},
		}
		// the setting of workflow and provenance for on-app is kept under feature flag because it may cause db overload
		// it includes the on-app transactions (includes mutual funds but not other investments because mutual funds workflow and provenance matches the on-app txn) in PAID, IN_PAYMENT status
		if shouldFetchOnlyOnAppTxns {
			// Add workflow and provenance if provided
			if len(workflow) > 0 {
				getTransactionAggregatesRequest.Workflow = workflow
			}
			if len(provenance) > 0 {
				getTransactionAggregatesRequest.Provenance = provenance
			}
		}
		// Try using Pinot RPC for optimization
		getAggregatesTotalTxnAmountResponse, err := payClient.GetTransactionAggregates(timeoutCtx, getTransactionAggregatesRequest)

		switch {
		case err != nil:
			// Log error and fall back to standard CRDB flow
			logger.Error(timeoutCtx, "Error in getting transaction aggregates from pinot",
				zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, actorId))
		case !getAggregatesTotalTxnAmountResponse.GetStatus().IsSuccess():
			// Log failure status and fall back to standard CRDB flow
			logger.Error(ctx, "Failed status in getting transaction aggregates from pinot",
				zap.String("status", getAggregatesTotalTxnAmountResponse.GetStatus().String()),
				zap.String(logger.ACTOR_ID_V2, actorId))
		default:
			// Success case - return the optimized result
			return getAggregatesTotalTxnAmountResponse.GetTransactionAggregates().GetSumAmount(), nil
		}
		if !feature.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().EnableFallbackForGetTotalTransactionAmountToCrdb(),
			userGroupClient, usersClient, actorClient) {
			return nil, fmt.Errorf("error while fetching total transaction amount from pinot: %w", epifigrpc.RPCError(getAggregatesTotalTxnAmountResponse, err))
		}
	}

	// Fall back to standard CRDB flow - using GetTotalTransactionAmount
	getTotalTxnAmountResponse, err := payClient.GetTotalTransactionAmount(ctx, &pay.GetTotalTransactionAmountRequest{
		ActorId:   actorId,
		PiIds:     accountPiIds,
		StartTime: fromTime,
		EndTime:   timestamppb.Now(),
		TransactionStatus: []paymentPb.TransactionStatus{
			paymentPb.TransactionStatus_SUCCESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_INITIATED,
		},
		PaymentProtocols: protocolTypes,
		TransactionType:  paymentPb.AccountingEntryType_DEBIT,
		AccountTypes:     []accounts.Type{accounts.Type_SAVINGS},
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("error while fetching total transaction amount: %w", err)
	case !getTotalTxnAmountResponse.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetTotalTransactionAmount() rpc call failed with status: %s", getTotalTxnAmountResponse.GetStatus().String())
	default:
		totalTransactionAmount = getTotalTxnAmountResponse.GetTotalAmount()
	}

	return totalTransactionAmount, nil
}

// getProtocolTotalTxnCount fetch the total txn count for given protocol list from startTime to current time
func getProtocolTotalTxnCount(ctx context.Context, actorId string, accountPiIds []string, startTime *timestamppb.Timestamp, payClient pay.PayClient, protocols []paymentPb.PaymentProtocol, paymentClient paymentPb.PaymentClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient) (int64, error) {

	var totalTransactionCount int64

	// Check if optimization flag is enabled
	if feature.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().EnableOptimisationForGetTotalTransactionCountParams(), userGroupClient,
		usersClient, actorClient) {

		// Try using Pinot RPC for optimization
		getTxnAggregate, err := payClient.GetTransactionAggregates(ctx, &pay.GetTransactionAggregatesRequest{
			ActorId:             actorId,
			PiIds:               accountPiIds,
			AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
			FromTime:            startTime,
			PaymentProtocol:     protocols,
			TransactionsStatus: []paymentPb.TransactionStatus{
				paymentPb.TransactionStatus_SUCCESS,
				paymentPb.TransactionStatus_UNKNOWN,
				paymentPb.TransactionStatus_MANUAL_INTERVENTION,
				paymentPb.TransactionStatus_IN_PROGRESS,
				paymentPb.TransactionStatus_INITIATED,
			},
			AccountTypes: []accounts.Type{accounts.Type_SAVINGS},
		})

		switch {
		case err != nil:
			// Log error and fall back to standard CRDB flow
			logger.Error(ctx, "Error in getting transaction aggregates from pinot", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		case !getTxnAggregate.GetStatus().IsSuccess():
			// Log failure status and fall back to standard CRDB flow
			logger.Error(ctx, "Failed status in getting transaction aggregates from pinot",
				zap.String("status", getTxnAggregate.GetStatus().String()),
				zap.String(logger.ACTOR_ID_V2, actorId))
		default:
			// Success case - return the optimized result
			return getTxnAggregate.GetTransactionAggregates().GetCount(), nil
		}
		if !feature.IsFeatureEnabledForUserV2(ctx, actorId, orderConfig.FeatureFlags().EnableFallbackForGetTotalTransactionCountToCrdb(),
			userGroupClient, usersClient, actorClient) {
			return 0, fmt.Errorf("error while fetching total transaction amount from pinot: %w", epifigrpc.RPCError(getTxnAggregate, err))
		}
	}

	// Fall back to standard CRDB flow - using GetTransactionsCount
	getTotalTxnCountResponse, err := paymentClient.GetTransactionsCount(ctx, &paymentPb.GetTransactionsCountRequest{
		ActorId:   actorId,
		PiIds:     accountPiIds,
		StartTime: startTime,
		EndTime:   timestamppb.Now(),
		TransactionStatus: []paymentPb.TransactionStatus{
			paymentPb.TransactionStatus_SUCCESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_INITIATED,
		},
		PaymentProtocols: protocols,
		TransactionType:  paymentPb.AccountingEntryType_DEBIT,
		AccountTypes:     []accounts.Type{accounts.Type_SAVINGS},
	})

	switch {
	case err != nil:
		return 0, fmt.Errorf("error while fetching total transaction count: %w", err)
	case !getTotalTxnCountResponse.GetStatus().IsSuccess():
		return 0, fmt.Errorf("GetTransactionsCount() rpc call failed with status: %s", getTotalTxnCountResponse.GetStatus().String())
	default:
		totalTransactionCount = getTotalTxnCountResponse.GetCount()
	}

	return totalTransactionCount, nil
}

// checkImpsTxnCount check IMPS txn count limit for IMPS transaction for an actor.
func checkImpsTxnCount(ctx context.Context, impsParams *ProtocolParams, currentActorId string, currentAccountPiIds []string, payClient pay.PayClient, paymentClient paymentPb.PaymentClient, userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient) (bool, error) {
	var (
		err                            error
		impsLimitWindow                time.Duration
		impsLimitWindowStartTime       *timestamppb.Timestamp
		totalNoOfImpsTxnsInLimitWindow int64
	)
	impsLimitWindowConf := impsParams.ProtocolLimitWindow
	impsLimitWindow, err = timeunit.ToDuration(impsLimitWindowConf.Value, impsLimitWindowConf.TimeUnit)
	if err != nil {
		return false, fmt.Errorf("error parsing impsLimitWindow: %w", err)
	}

	impsLimitWindowStartTime = timestamppb.New(time.Now().Add(-1 * impsLimitWindow))
	totalNoOfImpsTxnsInLimitWindow, err = getProtocolTotalTxnCount(ctx, currentActorId, currentAccountPiIds, impsLimitWindowStartTime, payClient, []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_IMPS}, paymentClient, userGroupClient,
		usersClient,
		orderConfig,
		actorClient)
	if err != nil {
		return false, fmt.Errorf("error getting imps total txn amount: %w", err)
	}
	if totalNoOfImpsTxnsInLimitWindow+1 > impsParams.TransactionCountLimit {
		return false, nil
	}
	return true, nil
}

// The conditions are evaluated in the order they appear in the list.
// The error corresponding to first failed condition is propagated to the rule engine, so the conditions
// should be in a priority of user facing errors
// for eg. If two conditions A and B are failing and we want to show the user error corresponding to
// condition A the condition A should be before B in the list.
// nolint: funlen
func (d *ruleFactory) buildUserPaymentRule(ctx context.Context, currentActorId string, currentActorAccountPiIds []string, amount *moneyPb.Money, currentActorAccountId string,
	currentActorAccountType accounts.Type, isCollectRequest bool, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient, usersClient user.UsersClient, params *config.PaymentProtocolDecisionParams,
	orderConfig *config.Config, actorClient actorPb.ActorClient, payClient pay.PayClient, operationalStatusClient operstatus.OperationalStatusServiceClient, evaluator release.IEvaluator, savingsClient savings.SavingsClient) *rule {

	// The expression functions return their own set of errors if applicable.
	// The onFailError will be used as a fallback error only if the expression function does not return an error.
	return &rule{
		name: "Is Payment Feasible",
		conditions: []*condition{
			{
				// The expression function isAccountNotFrozenForFundTransfer returns an error if the account is frozen.
				expr:        "isAccountNotFrozenForFundTransfer()",
				onFailError: accountFrozenError,
			},
			{
				expr:        "isAccountActiveForFundTransfer()",
				onFailError: accountNotActiveError,
			},
			{
				expr:        "isTotalTransactedAmountInLimit()",
				onFailError: totalTransactedAmountLimit,
			},
		},
		expressionFunctionMap: map[string]govaluate.ExpressionFunction{
			"isAccountNotFrozenForFundTransfer": func(args ...interface{}) (interface{}, error) {
				// Currently we are not putting any restriction on collect request for frozen Accounts.
				if isCollectRequest || !isOperationalStatusCheckEnabled(ctx, evaluator, currentActorId) {
					return true, nil
				}
				// use custom timeout for this check to prevent long running requests
				ctxWithTimeout, ctxCancel := context.WithTimeout(ctx, orderConfig.TimeoutForGetOperationalStatus())
				defer ctxCancel()
				res, err := isAccountFrozenForFundTransfer(ctxWithTimeout, currentActorAccountId, currentActorAccountType, operationalStatusClient)
				return !res, err
			},
			"isAccountActiveForFundTransfer": func(args ...interface{}) (interface{}, error) {
				// Currently we are not putting any restriction on collect request for not active Accounts.
				if isCollectRequest || !isOperationalStatusCheckEnabled(ctx, evaluator, currentActorId) {
					return true, nil
				}
				// use custom timeout for this check to prevent long running requests
				ctxWithTimeout, ctxCancel := context.WithTimeout(ctx, orderConfig.TimeoutForGetOperationalStatus())
				defer ctxCancel()
				return isAccountActiveForFundTransfer(ctxWithTimeout, currentActorAccountId, currentActorAccountType, operationalStatusClient)
			},
			"isTotalTransactedAmountInLimit": func(args ...interface{}) (interface{}, error) {
				return isTotalTransactedAmountInLimit(ctx, evaluator, currentActorId, currentActorAccountPiIds, amount, params.UserPaymentParams(), orderClient,
					nil, userGroupClient,
					usersClient,
					orderConfig,
					actorClient,
					payClient,
					savingsClient,
					currentActorAccountId)
			},
		},
	}
}

// isTotalTransactedAmountInLimit check the total transacted amount limit for userPaymentParams window for a actor not exceeding the amount limit for userPaymentParams
// If amount exceeded/error occurred while computing it will return false.
func isTotalTransactedAmountInLimit(ctx context.Context, evaluator release.IEvaluator, currentActorId string, currentActorAccountPiIds []string, amount *moneyPb.Money, userPaymentParams *config.UserPaymentParams, orderClient orderPb.OrderServiceClient, paymentProtocols []paymentPb.PaymentProtocol,
	userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	orderConfig *config.Config,
	actorClient actorPb.ActorClient,
	payClient pay.PayClient,
	savingsClient savings.SavingsClient,
	currentActorAccountId string) (bool, error) {
	var (
		err                   error
		limitWindow           time.Duration
		limitWindowStartTime  *timestamppb.Timestamp
		isNewAccount          bool
		totalTransactedAmount *moneyPb.Money
	)

	// Parse the standard limit window for transaction aggregation
	limitWindowConf := userPaymentParams.TotalTransactionAmountLimit().TimeWindow()
	limitWindow, err = timeunit.ToDuration(limitWindowConf.Value, limitWindowConf.TimeUnit)
	if err != nil {
		return false, fmt.Errorf("error parsing TotalTransactionAmountLimit.TimeWindowConf: %w", err)
	}

	limitWindowStartTime = timestamppb.New(time.Now().Add(-1 * limitWindow))

	// Create errgroup for parallel execution
	grp, grpCtx := errgroup.WithContext(ctx)

	// Check account age in parallel if new account limit is configured
	if userPaymentParams.NewAccountTransactionAmountLimit() != nil {
		grp.Go(func() error {
			var err1 error
			isNewAccount, err1 = isAccountCreatedWithinAgeThreshold(grpCtx, currentActorAccountId, savingsClient, userPaymentParams.NewAccountTransactionAmountLimit().AccountAgeLimitThreshold())
			if err1 != nil {
				// We are not returning error here as we don't want to block the payment if we are not sure about the account age.
				// We will apply standard limit (for now it is configured as INR 10 Lacs)
				logger.Error(grpCtx, "error checking account age", zap.Error(err1), zap.String(logger.ACTOR_ID_V2, currentActorId))
				return nil
			}
			return nil
		})
	}

	// Get transaction amounts in parallel
	grp.Go(func() error {
		var err2 error
		if len(paymentProtocols) == 0 {
			paymentProtocols = payPkg.GetAllPaymentProtocol()
		}
		shouldFetchOnlyOnAppTxns, err := evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP).WithActorId(currentActorId))
		if err != nil {
			logger.Error(ctx, "error evaluating on app transaction limit", zap.Error(err), zap.String(logger.ACTOR_ID_V2, currentActorId))
		}
		totalTransactedAmount, err2 = getProtocolTotalTxnAmount(grpCtx, currentActorId, currentActorAccountPiIds, limitWindowStartTime, orderClient,
			paymentProtocols, userGroupClient, usersClient, orderConfig, actorClient, payClient, shouldFetchOnlyOnAppTxns,
			[]orderPb.OrderWorkflow{
				orderPb.OrderWorkflow_P2P_COLLECT,
				orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
			},
			[]orderPb.OrderProvenance{
				orderPb.OrderProvenance_USER_APP,
				orderPb.OrderProvenance_THIRD_PARTY,
			}, orderConfig.PinotQueryTimeout())
		if err2 != nil {
			return fmt.Errorf("error getting given payment protocols total txn amount:%v: %w", paymentProtocols, err2)
		}
		return nil
	})

	// Wait for both goroutines to complete
	if grpErr := grp.Wait(); grpErr != nil {
		return false, grpErr
	}

	// Calculate final amount
	finalAmount, err := money.Sum(totalTransactedAmount, amount)
	if err != nil {
		return false, fmt.Errorf("error while adding money: %w", err)
	}

	switch {
	case isNewAccount:
		newAccountLimit := userPaymentParams.NewAccountTransactionAmountLimit().AmountLimit()
		if money.Compare(finalAmount, &moneyPb.Money{Units: newAccountLimit}) > 0 {
			return false, errTotalTransactedAmountLimitForNewUser
		}
	default:
		// If new account limit is not applicable then check standard limit
		standardLimit := userPaymentParams.TotalTransactionAmountLimit().AmountLimit()
		if money.Compare(finalAmount, &moneyPb.Money{Units: standardLimit}) > 0 {
			return false, totalTransactedAmountLimit
		}
	}
	return true, nil
}

// checkIfPaymentProtocolNotInDailyDownTime will check if the given paymentProtocol is fall in Daily downtime or not
// return true,nil if it is not fall in Daily Downtime
// return false,err if there is error in any step
// return false,nil if the give paymentProtocol fall in Daily Downtime
func checkIfPaymentProtocolNotInDailyDownTime(dailyProtocolDowntime *config.ProtocolDailyDowntime, protocolType paymentPb.PaymentProtocol) (bool, error) {
	if dailyProtocolDowntime == nil {
		return true, nil
	}

	if !dailyProtocolDowntime.IsEnabled() {
		return true, nil
	}

	currentTime := time.Now().In(datetime.IST)
	isDownTime, err := datetime.TimeLieBetweenStartAndEndInHHMM(currentTime, dailyProtocolDowntime.StartTime(), dailyProtocolDowntime.EndTime())
	if err != nil {
		return false, fmt.Errorf("error in psring if time in b/w fromTime and ToTime for payment protocol: %s, %w", protocolType, err)
	}

	if isDownTime {
		return false, nil
	}
	return true, nil
}

// isCoolOffNotApplicablePisToForIntraBank checks if all available PI's in activeAccountPiTos is present in coolOffNotApplicablePisTo
// If any of PIs from activeAccountPiTos is not present in list of coolOffNotApplicablePisTo then it will return false
// If all PI's in activePisTos present in coolOffNotApplicablePisTo it will return true
func isCoolOffNotApplicablePisToForIntraBank(activeAccountPiTos []*piPb.PaymentInstrument, coolOffNotApplicablePisTo []string) bool {
	atLeastOnePiNotPresentInCoolOffNotApplicableList := false
	for _, activePiTo := range activeAccountPiTos {
		if !lo.Contains(coolOffNotApplicablePisTo, activePiTo.GetId()) {
			atLeastOnePiNotPresentInCoolOffNotApplicableList = true
		}
	}
	return !atLeastOnePiNotPresentInCoolOffNotApplicableList
}

func getUpiToAndFromPi(upiPiTo, upiPiFrom []*piPb.PaymentInstrument) ([]string, []string) {
	var (
		upiPiToIds   []string
		upiPiFromIds []string
	)

	for _, upiPi := range upiPiTo {
		upiPiToIds = append(upiPiToIds, upiPi.GetId())
	}

	for _, upiPi := range upiPiFrom {
		upiPiFromIds = append(upiPiFromIds, upiPi.GetId())
	}

	return upiPiToIds, upiPiFromIds
}

// getSourcePiForUpiLite - fetches and returns the source account pi for given upi lite pi
func getSourcePiForUpiLite(ctx context.Context, upiLitePi *piPb.PaymentInstrument, piClient piPb.PiClient) (*piPb.PaymentInstrument, error) {
	piResp, err := piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
		Id: upiLitePi.GetUpiLite().GetPiRefId(),
	})
	if err = epifigrpc.RPCError(piResp, err); err != nil {
		return nil, fmt.Errorf("failed to get source pi from upi lite pi: %s, srcPi: %s %w", upiLitePi.GetId(), upiLitePi.GetUpiLite().GetPiRefId(), err)
	}
	return piResp.GetPaymentInstrument(), nil
}

func isAccountFrozenForFundTransfer(ctx context.Context, currentActorAccountId string, currentActorAccountType accounts.Type, operationalStatusClient operstatus.OperationalStatusServiceClient) (bool, error) {
	// Check If Account type is Savings , If not Then we can not know the Operational Status.
	if currentActorAccountType != accounts.Type_SAVINGS {
		return false, nil
	}
	req := &operstatus.GetOperationalStatusRequest{
		DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: currentActorAccountId,
		},
	}
	operStatusResp, err := operationalStatusClient.GetOperationalStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, "error getting operational status", zap.Error(err))
		return false, nil // On error, consider the account as Not Frozen as we are not sure.
	}
	operationalStatus := operStatusResp.GetOperationalStatusInfo().GetOperationalStatus()
	freezeStatus := operStatusResp.GetOperationalStatusInfo().GetFreezeStatus()
	freezeReasonCodes := operStatusResp.GetOperationalStatusInfo().GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetFreezeReasonCodes()
	if freezeStatus == enums.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE {
		switch operationalStatus {
		case enums.OperationalStatus_OPERATIONAL_STATUS_CLOSED:
			return true, accountClosedTotalFreezeError
		case enums.OperationalStatus_OPERATIONAL_STATUS_DORMANT:
			return true, accountDormantTotalFreezeError
		case enums.OperationalStatus_OPERATIONAL_STATUS_INACTIVE:
			return true, accountInactiveTotalFreezeError
		default:
			return true, accountActiveTotalFreezeError
		}
	}
	if freezeStatus == enums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE {
		// Check if the account is frozen due to KYC
		// TODO: Maintain enum for freeze reason codes, could not find any existing enum for freeze reason codes
		// 16 is the code for KYC Compliance
		if lo.Contains(freezeReasonCodes, "16") {
			return true, accountFreezeKycExpiredError
		}
		switch operationalStatus {
		case enums.OperationalStatus_OPERATIONAL_STATUS_CLOSED:
			return true, accountClosedDebitFreezeError
		case enums.OperationalStatus_OPERATIONAL_STATUS_DORMANT:
			return true, accountDormantDebitFreezeError
		case enums.OperationalStatus_OPERATIONAL_STATUS_INACTIVE:
			return true, accountInactiveDebitFreezeError
		default:
			return true, accountActiveDebitFreezeError
		}
	}
	return false, nil
}

func isAccountActiveForFundTransfer(ctx context.Context, currentActorAccountId string, currentActorAccountType accounts.Type, operationalStatusClient operstatus.OperationalStatusServiceClient) (bool, error) {
	// Check If Account type is Savings , If not then we can not know the Operational Status.
	if currentActorAccountType != accounts.Type_SAVINGS {
		return true, nil
	}
	req := &operstatus.GetOperationalStatusRequest{
		DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: currentActorAccountId,
		},
	}
	operStatusResp, err := operationalStatusClient.GetOperationalStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, "error getting operational status", zap.Error(err))
		return true, nil // On error, consider the account as Active as we are not sure.
	}
	operationalStatus := operStatusResp.GetOperationalStatusInfo().GetOperationalStatus()
	freezeStatus := operStatusResp.GetOperationalStatusInfo().GetFreezeStatus()
	if operationalStatus == enums.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
		if freezeStatus == enums.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE {
			return false, accountClosedCreditFreezeError
		} else {
			return false, accountClosedNoFreezeError
		}
	}
	if operationalStatus == enums.OperationalStatus_OPERATIONAL_STATUS_DORMANT {
		if freezeStatus == enums.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE {
			return false, accountDormantCreditFreezeError
		} else {
			return false, accountDormantNoFreezeError
		}
	}
	if operationalStatus == enums.OperationalStatus_OPERATIONAL_STATUS_INACTIVE {
		if freezeStatus == enums.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE {
			return false, accountInactiveCreditFreezeError
		} else {
			return false, accountInactiveNoFreezeError
		}
	}
	return true, nil
}

// isOperationalStatusCheckEnabled checks if the feature is enabled for the actor to put the operational status check on the account before initiating a payment.
func isOperationalStatusCheckEnabled(ctx context.Context, evaluator release.IEvaluator, actorId string) bool {
	releaseConstraint := release.NewCommonConstraintData(types.Feature_FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK).WithActorId(actorId)
	isFeatureEnabled, err := evaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		logger.Error(ctx, "failed to evaluate actor for operation status check feature for initiating payment", zap.Error(err))
		return false
	}
	return isFeatureEnabled
}

// isAccountCreatedWithinAgeThreshold checks if the account was created within the specified number of days
func isAccountCreatedWithinAgeThreshold(ctx context.Context, accountId string, savingsClient savings.SavingsClient, accountAgeLimitThreshold time.Duration) (bool, error) {
	// Fetch account details using the account ID
	accountResponse, err := savingsClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_Id{
			Id: accountId,
		},
	})
	if err != nil {
		logger.Debug(ctx, "Error getting account details. Falling back to 10L limit", zap.Error(err))
		return false, nil
	}
	if accountResponse == nil || accountResponse.GetAccount() == nil {
		return false, fmt.Errorf("invalid account response")
	}

	accountCreationTime := accountResponse.GetAccount().GetCreatedAt().AsTime()
	return time.Since(accountCreationTime) <= accountAgeLimitThreshold, nil
}
