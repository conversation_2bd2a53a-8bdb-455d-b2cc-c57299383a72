Application:
  Environment: "uat"
  Name: "order"
  IsSeparateEnquiryPerProtocolEnabled: true

Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 5s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "uat/rds/postgres14"

EpifiWealthDB:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 5s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "uat/rds/postgres14"

PaymentOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-orchestration-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 1 hour is followed for next 12 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10

IntraBankEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-intrabank-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10

UPIEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-upi-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10

IMPSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-imps-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10

NEFTEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-neft-enquiry-queue"
  RetryStrategy:
    Hybrid: # Regular interval of 15 minutes is followed is followed for first 2 hours post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        RegularInterval:
          Interval: 15
          MaxAttempts: 8
          TimeUnit: "Minute"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10

RTGSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-rtgs-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10

OrderOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-orchestration-queue"
  # Regular interval for 1 min, as the business logic is limited to DB update
  RetryStrategy:
    RegularInterval:
      Interval:    5
      MaxAttempts: 13
      TimeUnit: "Second"

PaymentCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-payment-callback-update-queue"
  # Regular interval for 1 min, as the business logic is limited to DB update
  RetryStrategy:
    RegularInterval:
      Interval:    5
      MaxAttempts: 13
      TimeUnit: "Second"

InboundTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-inbound-txn-queue"
  # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 6 hours.
  # The deposit creation notifications event are expected to be processed post deposit creation
  # Hence, retry strategies are aligned with deposit creation/closure retry strategies and status checks
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 12
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 20
          MaxAttempts: 18
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 12

InboundUpiTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-inbound-upi-txn-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 12
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "order-consumer"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-update-order-notification-consumer-queue"
  # Regular interval for 1 min
  RetryStrategy:
    RegularInterval:
      Interval:    5
      MaxAttempts: 13
      TimeUnit: "Second"

OrderCollectNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-collect-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderNotificationFallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-notification-fallback-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderWorkflowProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-workflow-processing-queue"
  # The retry strategy for OrderWorkflowProcessing is defined by consumer business logic, hence the strategy
  # defined here is only used to for max attempts by the subscriber library code.
  # Please check the workflow specific retry strategies in order-params.yml
  RetryStrategy:
    RegularInterval:
      Interval: 20
      MaxAttempts: 100
      TimeUnit: "Minute"

DisputeEventProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-dispute-events-external-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

SavingsLedgerReconSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-savings-ledger-recon-queue"
  # Exponential backoff till 4 min, to add tolerance against transient failure due to network failure while fetching statement
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

AATxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-aa-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PurgeAaDataSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-aa-data-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaTxnPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-aa-txn-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaDataPurgeOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-aa-data-purging-orchestration-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

AAFirstDataPullTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-aa-first-data-pull-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1s
    Namespace: "order-aa"

OrderOrchestrationPublisher:
  QueueName: "uat-order-orchestration-queue"

PaymentOrchestrationPublisher:
  QueueName: "uat-payment-orchestration-queue"

OrderUpdateEventPublisher:
  TopicName: "uat-order-update-topic"

OrderMerchantMergeEventPublisher:
  TopicName: "uat-batch-order-update-topic"

AATxnPublisher:
  TopicName: "uat-order-aa-txn-topic"

TxnDetailedStatusUpdateSnsPublisher:
  TopicName: "uat-txn-detailed-status-update-topic"

WorkflowProcessingPublisher:
  QueueName: "uat-order-workflow-processing-queue"

SavingsLedgerReconPublisher:
  QueueName: "uat-order-savings-ledger-recon-queue"

OrderNotificationPublisher:
  QueueName: "uat-order-update-order-notification-consumer-queue"

OrderCollectNotificationPublisher:
  QueueName: "uat-order-collect-notification-consumer-queue"

OrderNotificationFallbackPublisher:
  QueueName: "uat-order-notification-fallback-consumer-queue"

OrderSearchPublisher:
  QueueName: "uat-order-update-indexing-consumer-queue"

IntraBankEnquiryPublisher:
  QueueName: "uat-payment-intrabank-enquiry-queue"

UPIEnquiryPublisher:
  QueueName: "uat-payment-upi-enquiry-queue"

IMPSEnquiryPublisher:
  QueueName: "uat-payment-imps-enquiry-queue"

NEFTEnquiryPublisher:
  QueueName: "uat-payment-neft-enquiry-queue"

RTGSEnquiryPublisher:
  QueueName: "uat-payment-rtgs-enquiry-queue"

AaAccountPiPurgePublisher:
  QueueName: "uat-aa-account-pi-purge-queue"

ActorPiRelationPurgePublisher:
  QueueName: "uat-actor-pi-purge-queue"

AaDataPurgeOrchestrationPublisher:
  QueueName: "uat-order-aa-data-purging-orchestration-queue"

EventsCompletedTnCPublisher:
  QueueName: "uat-event-completed-tnc-queue"

SignalWorkflowPublisher:
  QueueName: "uat-celestial-signal-workflow-queue"

TxnNotificationPublisher:
  QueueName: "uat-transaction-notification-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "uat-order-in-payment-order-update-queue"
ProcrastinatorWorkflowPublisher:
  QueueName: "uat-celestial-initiate-procrastinator-workflow-queue"

AWS:
  Region: "ap-south-1"

FundTransferWorkflow:
  LowValueTransactionUpperLimit:
    CurrencyCode: "INR"
    Units: 0
    Nanos: 0

CollectShortCircuitWorkflow:
  LowValueTransactionUpperLimit:
    CurrencyCode: "INR"
    Units: 0
    Nanos: 0
  CollectAmountLimit:
    CurrencyCode: "INR"
    Units: 2000
    Nanos: 0
  CollectExpirationDuration: "48h"

Flags:
  TrimDebugMessageFromStatus: false

CustomRuleDEParams:
  IsCustomRuleDERestricted: false

RuleEngineParams:
  SalienceINTRA: 10
  SalienceIMPS: 9
  SalienceUPI: 11
  SalienceRTGS: 7
  SalienceNEFT: 6
  BumpedSalience: 20

PaymentProtocolDecisionParams:
  INTRAParams:
    MinAmount: 500
    MaxAmount: 1000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  IMPSParams:
    MinAmount: 500
    MaxAmount: 3000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  NEFTParams:
    MinAmount: 3000
    MaxAmount: 200000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  RTGSParams:
    MinAmount: 200000
    # TODO(raunak): To handle infinity amounts gracefully - maybe -1 ??
    MaxAmount: 1000000000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UPIParams:
    UpiMinAmount: 1
    UpiMaxAmount: 100000
    UpiTotalTransactedAmountLimit: 200000
    UpiTransactionCountLimit: 10
    UpiNewUserTotalTransactedAmountLimit: 5000
    UpiCoolDownPeriodTotalTransactedAmountLimit: 10000
    UpiPinResetTotalTransactedAmountLimit: 5000
    UpiLimitWindow:
      Value: 24
      TimeUnit: "Hour"
    UpiPinResetLimitWindow:
      Value: 12
      TimeUnit: "Hour"
    UpiProfileUpdateCoolDownWindow:
      ANDROID:
        Value: 24
        TimeUnit: "Hour"
      IOS:
        Value: 24
        TimeUnit: "Hour"
    UpiProfileUpdateCoolDownTriggerDurationLimit:
      Value: 24
      TimeUnit: "Hour"
    UpiProfileUpdateAfuSummariesFetchDuration:
      Value: 24
      TimeUnit: "Hour"
    UPIRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  # Specific MCC or payment initiation mode or purpose code for which UPI txn limit is different than normal
  # Circular: https://www.npci.org.in/PDF/npci/upi/circular/2020/UPI%20OC%2082%20-%20Implementation%20of%20Rs%20%202%20Lakh%20limit%20per%20transaction%20for%20specific%20categories%20in%20UPI.pdf
  UPILimitExceptions:
    MCC:
      "6211":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "7322":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "5960":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "6300":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "6529":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "6540":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
      "4812":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
      "4814":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
    P2PInitiationMode:
      "04":
        Limits:
          MaxAmount: 0
          MinAmount: 0
      "05":
        Limits:
          MaxAmount: 0
          MinAmount: 0
    P2MInitiationMode:
      "12":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
    PurposeCode:
      "15":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      # defaultPurposeUpiLiteEnablement
      "41":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteTopUp
      "42":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteDisablement
      "43":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # upi lite payment
      "44":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 500
          MinAmount: 1

  TotalTransactedAmountLimitInCoolDown: 10000
  ProfileUpdateCoolDownWindow:
    Value: 24
    TimeUnit: "Hour"

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "uat/gcloud/profiling-service-account-key"
    EventSalt: "uat/rudder/salt"
    EpifiFederalPgpPrivateKey: "uat/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "uat/pgp/federal-pgp-pub-key"
    EpifiFederalPgpPassphrase: "uat/pgp/pgp-epifi-key-passphrase-fed-api"
    EncryptorData: "uat/order/aes/txn-monitoring-aes-encryption-data"

RedisOptions:
  IsSecureRedis: true
  Options:
    # for non-prod we are using cx redis cluster with a different db
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on uat we are using common redis cluster with a different db
    DB: 3

DynamicVPAParamsMap:
  FEDERAL_BANK:
    AccountNumber: "**************"
    Name: "Fi - Federal Bank"
    Ifsc: "FDRL0005555"
    AccountReferenceNumber: "DPyyFKvsMTvhOXvq5sL+5JZbm38ofcW692RcDTwor2w="
    DynamicVPAString: "addfi.%s@fedepsp"
    MerchantDetails:
      MerchantID: "MerchantID"
      MerchantStoreID: "MerchantStoreID"
      MerchantTerminalId: "MerchantTerminalId"
      Genre: 1
      BrandName: "Fi - Federal Bank"
      LegalName: "Fi - Federal Bank"
      FranchiseName: "Fi - Federal Bank"
      OwnerShipType: 4
      OnboardingType: 1
      MerchantType: 2
      SubCode: "5399"
      Mcc: "5399"

DynamicVPAV1ParamsMap:
  FEDERAL_BANK:
    AccountNumber: "**************"
    Name: "Fi - Federal Bank"
    Ifsc: "FDRL0005555"
    AccountReferenceNumber: "DPyyFKvsMTvhOXvq5sL+5JZbm38ofcW692RcDTwor2w="
    DynamicVPAString: "addfi.5555@fifederal"
    MerchantDetails:
      MerchantID: "MerchantID"
      MerchantStoreID: "MerchantStoreID"
      MerchantTerminalId: "MerchantTerminalId"
      Genre: 1
      BrandName: "Fi - Federal Bank"
      LegalName: "Fi - Federal Bank"
      FranchiseName: "Fi - Federal Bank"
      OwnerShipType: 4
      OnboardingType: 1
      MerchantType: 2
      SubCode: "7409"
      Mcc: "7409"

ConnectedAccountUserGroupParams:
  IsAARestricted: true
  AllowedUserGroupForAA:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # Connected Account

TxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-transaction-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineCardTransactionsProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-card-txns-decline-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineDataAwsSftpBucket:
  AwsBucket: "epifi-uat-federal-debit-card-data"
  AclString: "private"
  SrcFolder: "in_data"
  DstFolder: "processed"

SavingsLedgerReconCacheConfig:
  IsCachingEnabled: false
  SavingsLedgerReconPrefix: "savings_ledger_recon_account_Id"
  CacheTTl: "2m"
  SavingsLedgerRedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById for storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: false
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
      ClientName: order

TransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"

Tracing:
  Enable: false

FeatureFlags:
  EnableParameterizedTimelineEvents:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 #internal
  EnablePGDBDaoForAA: true
  EnableB2CCallbackSignalToCelestial: true
  EnableOnAppEnachExecutionInboundNotifProcessor: true

Events:
  IncomingCreditMaxPublishDelay: "2m"

OrderVpaVerificationPublisher:
  QueueName: "uat-unverified-vpa-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

FeatureReleaseConfig:
  FeatureConstraints:
    HEALTH_ENGINE_FOR_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 250
        MinIOSVersion: 345
      StickyPercentageConstraintConfig:
        RolloutPercentage: 15
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_INTRA_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_NEFT_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_IMPS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_RTGS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    BENEFICIARY_COOL_DOWN_RULE:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    REPORT_FRAUD_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

DeemedTransactionUPIEnquiryPublisher:
  QueueName: "uat-deemed-payment-upi-enquiry-queue"

DeemedTransactionUpiEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-deemed-payment-upi-enquiry-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 3
      MaxAttempts: 17
      TimeUnit: "Hour"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "order:upi"

NonTpapPspHandles: ["fedepsp"]

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: order-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ReportFraudConfig:
  TransactionProtocolToMaxDaysAvailableForReportFraud:
    CARD: 30.0
    IMPS: 60.0
    UPI: 60.0

DBConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    AppName: "order"
    StatementTimeout: 15s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      EnableMultiDBSupport: true
      DBResolverList:
        - Alias: "connected_account_pgdb"
          DbDsn:
            DbType: "PGDB"
            AppName: "order"
            StatementTimeout: 1m
            Name: "connected_account"
            SSLMode: "verify-full"
            SSLRootCert: "uat/rds/rds-ca-root-2061"
            SecretName: "uat/rds/postgres14"
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "order"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
