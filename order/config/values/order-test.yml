Application:
  Environment: "test"
  Name: "order"
  IsSeparateEnquiryPerProtocolEnabled: true

Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9853

EpifiDb:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          Host: "localhost"
          Port: 5432
          Username: "root"
          Name: "connected_account_test"
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

EpifiWealthDB:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_wealth_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          Host: "localhost"
          Port: 5432
          Username: "root"
          Name: "connected_account_test"
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

PaymentOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-orchestration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

IntraBankEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-intrabank-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

UPIEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-upi-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

IMPSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-imps-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

NEFTEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-neft-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

RTGSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-rtgs-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-orchestration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

PaymentCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "payment-callback-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

InboundTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "inbound-txn-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderUpdateSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-update-order-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderCollectNotificationSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-collect-notification-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderNotificationFallbackSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-notification-fallback-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

OrderWorkflowProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-workflow-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 15
      TimeUnit: "Second"

DisputeEventProcessingSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-dispute-events-external-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

SavingsLedgerReconSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-savings-ledger-recon-queue"
  # Exponential backoff till 2 min, to add tolerance against transient failure due to network failure while fetching statement
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AATxnSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-aa-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PurgeAaDataSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-aa-data-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaTxnPurgeSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "aa-txn-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

AaDataPurgeOrchestrationSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-aa-data-purging-orchestration-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

InboundUpiTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "inbound-upi-txn-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 12
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "order-consumer"

AAFirstDataPullTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "order-aa-first-data-pull-txn-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1s
    Namespace: "order-aa"

OrderOrchestrationPublisher:
  QueueName: "order-orchestration-queue"

PaymentOrchestrationPublisher:
  QueueName: "payment-orchestration-queue"

OrderUpdateEventPublisher:
  TopicName: "order-update-topic"

OrderMerchantMergeEventPublisher:
  TopicName: "batch-order-update-topic"

AATxnPublisher:
  TopicName: "order-aa-txn-topic"

TxnDetailedStatusUpdateSnsPublisher:
  TopicName: "txn-detailed-status-update-topic"

WorkflowProcessingPublisher:
  QueueName: "order-workflow-processing-queue"

SavingsLedgerReconPublisher:
  QueueName: "order-savings-ledger-recon-queue"

OrderNotificationPublisher:
  QueueName: "order-update-order-notification-consumer-queue"

OrderCollectNotificationPublisher:
  QueueName: "order-collect-notification-consumer-queue"

OrderNotificationFallbackPublisher:
  QueueName: "order-notification-fallback-consumer-queue"

OrderSearchPublisher:
  QueueName: "order-update-indexing-consumer-queue"

IntraBankEnquiryPublisher:
  QueueName: "payment-intrabank-enquiry-queue"

UPIEnquiryPublisher:
  QueueName: "payment-upi-enquiry-queue"

IMPSEnquiryPublisher:
  QueueName: "payment-imps-enquiry-queue"

NEFTEnquiryPublisher:
  QueueName: "payment-neft-enquiry-queue"

RTGSEnquiryPublisher:
  QueueName: "payment-rtgs-enquiry-queue"

AaAccountPiPurgePublisher:
  QueueName: "aa-account-pi-purge-queue"

ActorPiRelationPurgePublisher:
  QueueName: "actor-pi-purge-queue"

AaDataPurgeOrchestrationPublisher:
  QueueName: "order-aa-data-purging-orchestration-queue"

EventsCompletedTnCPublisher:
  QueueName: "event-completed-tnc-queue"

SignalWorkflowPublisher:
  QueueName: "celestial-signal-workflow-queue"

TxnNotificationPublisher:
  QueueName: "transaction-notification-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "order-in-payment-order-update-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "celestial-initiate-procrastinator-workflow-queue"

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false
  SkipSmsForUpiDebitTransactions: false

CustomRuleDEParams:
  IsCustomRuleDERestricted: false

PaymentProtocolDecisionParams:
  INTRAParams:
    MinAmount: 1
    MaxAmount: 1000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  IMPSParams:
    MinAmount: 1
    # Max amount is kept high to allow users to invest in long-term Jump schemes
    MaxAmount: 50000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"
    LimitWindow:
      Value: 24
      TimeUnit: "Hour"
    TransactionCountLimit: 50

  NEFTParams:
    MinAmount: 1
    MaxAmount: 5000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"
    ProtocolDailyDowntime:
      IsEnabled: false
      StartTime: "23:15"
      EndTime: "01:10"

  RTGSParams:
    MinAmount: 100
    # TODO(raunak): To handle infinity amounts gracefully - maybe -1 ??
    MaxAmount: 3000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  UPIParams:
    UpiMinAmount: 1
    UpiMaxAmount: 2000
    UpiTotalTransactedAmountLimit: 200000
    UpiTransactionCountLimit: 100
    UpiNewUserTotalTransactedAmountLimit: 5000
    UpiCoolDownPeriodTotalTransactedAmountLimit: 10000
    UpiPinResetTotalTransactedAmountLimit: 5000
    UpiLimitWindow:
      Value: 24
      TimeUnit: "Hour"
    UpiPinResetLimitWindow:
      Value: 12
      TimeUnit: "Hour"
    UpiProfileUpdateCoolDownWindow:
      ANDROID:
        Value: 24
        TimeUnit: "Hour"
      IOS:
        Value: 24
        TimeUnit: "Hour"
    UpiProfileUpdateCoolDownTriggerDurationLimit:
      Value: 24
      TimeUnit: "Hour"
    UpiProfileUpdateAfuSummariesFetchDuration:
      Value: 24
      TimeUnit: "Hour"
    UPIRestrictedWindow:
      IsEnabled: false
      Timezone: "Asia/Kolkata"
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2021-04-18:00:00:00"
      ToDateTime: "2021-04-18:14:00:00"

  # Specific MCC or payment initiation mode or purpose code for which UPI txn limit is different than normal
  # Circular: https://www.npci.org.in/PDF/npci/upi/circular/2020/UPI%20OC%2082%20-%20Implementation%20of%20Rs%20%202%20Lakh%20limit%20per%20transaction%20for%20specific%20categories%20in%20UPI.pdf
  UPILimitExceptions:
    MCC:
      "6211":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
      "7322":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
      "5960":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
      "6300":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
      "6529":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
      "6540":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
      "4812":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
      "4814":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
    P2PInitiationMode:
      "04":
        Limits:
          MaxAmount: 0
          MinAmount: 0
      "05":
        Limits:
          MaxAmount: 0
          MinAmount: 0
    P2MInitiationMode:
      "12":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
    PurposeCode:
      "15":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 10000
          MinAmount: 1
      # defaultPurposeUpiLiteEnablement
      "41":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteTopUp
      "42":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteDisablement
      "43":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # upi lite payment
      "44":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 500
          MinAmount: 1

  TotalTransactedAmountLimitInCoolDown: 10000
  ProfileUpdateCoolDownWindow:
    Value: 24
    TimeUnit: "Hour"

  UserPaymentParams:
    TotalTransactionAmountLimit:
      AmountLimit: 1000000
      TimeWindow:
        Value: 24
        TimeUnit: "Hour"
    NewAccountTransactionAmountLimit:
      AmountLimit: 50000
      AccountAgeLimitThreshold: "2160h" # 90 days




Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    EventSalt: "ANIqDq8bJOfpuZTOCyyDxsdwEx0"
    EncryptorData: "test/order/aes/txn-monitoring-aes-encryption-data"
    #Federal
    EpifiFederalPgpPassphrase: "qwerty1234"
    EpifiFederalPgpPrivateKey: |
      -----BEGIN PGP PRIVATE KEY BLOCK-----

      lQdGBF5qBvgBEADGijefHuDTXqiReXdZcxbknDmueW5XGWzR0EHec9kRYkxcQy+H
      gY/q7RJ+5VNNM8KfWtdM+K2zoTdkv6uIp6eYEcC6YcRV1MeX6KbxGGpMzRmb5f0c
      d+gePS1WOeOWjjS+3AXmH0DO+v8bXDxAullL8L0jDYK1sRxWlu9tdwglyJx7IXQi
      VibCaSfrNDrTI7HmrLw8PZ/rfHq5prhEQ9cCeI+kHKz2Amiuts8PGl5M1MEx2oC8
      nSMtb3YXg32FiYkyOi/vXZ5jmltcR9PyhJnMkNFrZRcBxU5dWhB/CpD/IebaKbir
      KuH2Ig/0d8PvvLlyILMyto5C2hfho2QWbFisMuSF23x798CYrFqLBDjFersDDfIW
      4yKsMD+2J+9T/7nBUYdIUmCEmg+khtIVanpJnQQP+iqb8NB7oTevBXKaIST95NhK
      b7B6J9/IDy0xFyPFPtXQyHSoufAjJPfNIgFb8cMTIuQEfN402Fp6eH5NwlvECFWy
      y8S00VFxr8mHXZ1zmvYK6Kp3OLR+q0tpUKmWu6KR4UdKY2Lce7ciuFVGlw1ufBgE
      nxlJNguLDCD23aod78QTWI6ubnCRKTdDLoR7H3CRkIQYzQme3xKJRHdEBfEOioTd
      6+rc1VHp1iLlo7uAlk01wGVdwRmO3deSi1y367TWXy1tyFscOvlGuF6r9QARAQAB
      /gcDAo8iYHB6tWca6/vYjx8IhXmfges2Lti8I805xB7+Ooilxat4ZtBUQHjMGm36
      CPTHwxpca4HfrGIYkCPQ7RmIQTYJBir1NWciD/Dvk0F3TeBicXz9Qzg8nsIsmNug
      SUgHFmbUshT9hMYXAp6no4a+0vJhOH+Wg5CbXV++fl6VhxOoesftfoenJ1JJ2G4Y
      HWElJWuaY4ozf5APC9JrC4KKyjJpZFWg7GoHjyPi6DYNOP1UhXghB0r2biokoJJt
      HErCBXpH+yA2/8L/j0tfDT31IiP/Ge0m2mFIKt6SpBzkbyeY5Pr/ZfMIt4rPwsf8
      2O2B1aL5JF358cKsv4DGGjIv1G3joqyYbrub8j8zL0shYmiBfPeLXdW39NQxplxy
      BDiSCSMRa3IV3J7YztR87GEeKbcqS6gY0sVQB9HrkNTxqh9DG0kNwovZRhGAbK1+
      +1ctahA+XEWghkXF2U2VBJjT42OgzYtgAkyD1+vPeThEKfzOxMARGcavuURX+kiI
      e19MLPuFdJhcZ8pOUv3Y3Otpa5BgsjBUWObGVwUFe1XJ9Zhe+JOIg2QUj35kVReR
      NiyvYrh6QHxnlG15HAgpica3a1gEnxf6M5nXP2XOsmGX+SKzlnBtvkxCy9+3zopJ
      S3NReIK52YWX/NPTK4rKahbv+17xFyRk5msmxsxVO1X3nZbGRgy+49Uv3D8fc7JD
      u3hnpj0DgIaQXKGv/vlEqFtHUsonABXG9ylPvsTJ4I7gbUbmpLV1t8LmF5x4xFfw
      n0O++iBRUqzkLi2+6e5ZPTna24F8hWkAq6k++nVKFCSz2NoDcTwrjLRM/fkkqJe3
      g2X1tug7x0B+/MwbY0IJwhEaHbZpeGVuXr+XIEs2uleWZ3kiJJYHSyRHHpcc/IxQ
      6N4gflA/tZKwFuBBBnIRXt97m2WseTGcxF+lAuVbCS7mB2PFscYfkQVEaq7enHrN
      Cx62RlIf8ZnmeeIVmU9VylFOy4BlFcg4lwjuY2cgoX+q/zNwkhaoGRXlp039CjFW
      0R6OMV2iY3tUDpQyTki9lc4seSIKyN+LOEd74R6mHZEKNpuDJFmGDla5GDPW9ZP3
      s57zvRXDOjgrxsQNcZUcF/cmaIc+pdVzgs32P/ojaCW5h7ncr66hxms4VBvi1JVD
      N3Q74BAcHbyJ/FYVW1lgHSzDVXJL8EqctV6/HGg9e+IRRgYBIf0q4fUWURrIes6d
      /TQ2S1O/u536ncDTzhuh7MoC5UUv97Cmjovk5iLbdsYXUapJKMZX+duDw2LhOUzy
      kukrLShRR8BQ4aQndStLBGzorVjMOrPMFZQivwmwFEB3n4rXBeG4YtMAUvOZeZvM
      tMvD3gKh+0hkRgYRuQi0ZKBb3OSaw7O/Dvb+68wUMlPfoK+i1x5TIx4wY5ws3A37
      XM3i10aFbWouk84RZ5ntF4aDcFZ+r/T1BkzHc7e4HrcuYz3nAudxx+zqkjCM/MZv
      4dmTnw5ODcN97tXhnxaLcRVT9M+2inMOtuSVYa48y7AbyR/cSk9N46CVpTwwioiI
      5fpvHCTGy52m5HxXVwwNXqliWESGc5PefoN5IoDUzX5J6RVFcDw7n0bTNBbvxFHT
      mbsu2w+biEmQXqovAIW0YZK0Xnnu4J0DwKCb+8loxnNOTzANyfkUa2eskjZ7H2Q6
      J4aq7sqH/lAQU439uHeuCPmzwK2jlDmrUhO1SgJyHglP/tnSPkk+rCEivqIawqgr
      ATFhWgNUXP9zPBHejhiNFOO65AXVTivAmlFkZm6+di6XBD9lCF5dtP+0I0F1dG9n
      ZW5lcmF0ZWQgS2V5IDxhbmFuZEBlcGlmaS5jb20+iQJOBBMBCAA4FiEEhUtW2MRo
      EDU7awr4z4B1fb7ViawFAl5qBvgCGy8FCwkIBwIGFQoJCAsCBBYCAwECHgECF4AA
      CgkQz4B1fb7Viaw0RBAAtLprII+/f3u53627dzrbyIE/z19ih34v/WVsDiMpyUYK
      qgXvL9a0P8dP+EO/rERxH9jbt5KtJ1zE4bGoT7zEQr/a36gwAu5DwaFInaPTrqPZ
      5FL/xytReUcjuPb5W6qWF2mO6iSd3ZwQbo/ZNjqgGIem+ouPfPtyyz7mdsM42FRF
      ANZ6JWSpqpuEl1l1GNFfVEmJMM0SgfsSm2XClGgTHQ2hyRyH0zcBoojjOigqkEHd
      ytM3MVovn2z2+HjqSU1GMCT21dRKZnV/FkN8GHW4ITOUd2X1Z4eBt7y8/n/WIFbx
      uwbSRtYKULe5LjgMA+Cv6bUBRqruUOdW52iFpxU0Eyr6+T2kEJeXtyWkGA297B5W
      LtCWRPvQAdoj8Qxky2ch5zyrKfSPwpPF3o/HcPvodk5hYXkhE/AHNqqu0ORmtevj
      20pbH84yUfjJEKJbLKu8MEuKysxSgNN00gHmjVEJa98Cl+qMAdzNBpDiJTlriM6y
      uwychPPrgZoe98YKy2/xRsZ2s/BPRazqllwZ8i+x/Ks8iYC+lSrmGlAtZulwOS8g
      +Flz7yPuYZB9IXNRyd7dZNRlxrGlcD1VHUkgyoZvL3EgpKtUofaNJ1WYtXigEqe8
      aLci0kOkZjjhA9FhikU3VUf+jlLKR5sGx2xWPZAnASrZU8/pIGI6sUsxWmSBHvg=
      =c5Qs
      -----END PGP PRIVATE KEY BLOCK-----

    FederalPgpPublicKey: |
      -----BEGIN PGP PUBLIC KEY BLOCK-----

      mQINBF5or0QBEADLY8Gg8s3RoRjxtX3fyHvgpAl/jdE1v3QETt6mBBWK2jrxgDYv
      uGrE2dqHr6/XETpFzgsmGpXILijryvqu+6QL5/RQTXfSUyK+l14unUEWwAtIEPt9
      kbxxXs8QvTw4+PnskVCOOcmwPnamU7jHYmx3bR82KBW0lroSroAq+U0bQGWuDeei
      Ry5uhN6xzQHjEXJEEwLMn0UBJI/vf4B6jJ3Z3Se9yCcuUqEPCAId3Vz2nNFwEAQA
      kC/WeuaVnxqL/evRlNeEzPVKmbwzLhc9e2p4acPRdMSeu9lvmHuS7VTR0iAomjL2
      KCXCJWQ+LhkusKDTjh5U+/cZtRF3JWRzdjjIGhxjz87kzjQXup+0KETF06Uzm4BB
      dVodXV2YJKn3WsC9xolAOjZ3sFPHReOxzS2MVtzAp7hFUBagZ/PmokbG/lEkPqWT
      1YBGCrIZ1CRFWpQCZeA4gbVny/RAeMuaDJp+Sr6QVi/NPAGtMXregGxxuHYPRZmb
      6xNPxsTW0Ak31YzFYCj/1hMMtGLp81cc3FVGGZIZWHajm0LBBOHE6sjCaKdyl6Bo
      BHxhaAMRHg+H123JOCz1WdkyhE+5R+0sQiEZLNK7/hciVaLESrfblNCc5RhgQ3mm
      qLYydkZMqhi1zwDIKg7YueJoQM+gd6bTzNfqLGW2UPOQojJZ9SosxcHthwARAQAB
      tCNBdXRvZ2VuZXJhdGVkIEtleSA8YW5hbmRAZXBpZmkuY29tPokCTgQTAQgAOBYh
      BL2/CLI39EtWfkngfSioaI1AylhdBQJeaK9EAhsvBQsJCAcCBhUKCQgLAgQWAgMB
      Ah4BAheAAAoJECioaI1Aylhd1vYQAKRmtMDPSgmiAN5+eVDA75PPdmjemzkHyM2S
      lEOljzUeCZKOIY3JZCWwQEFdoru9HHmMqFPrHUSJMGqN19xc/JYeEMxv/hkZoeEv
      DPG70cvnJotDYqm++EFRH8u0VbvEWDJKMCBAKQ2AYcWsrq5EsA3FP/a+FvxriBgj
      tfA5pMSrcxCsLfqkfgrA9k2JQekJ5hTIi4p9Dcm3eaKr6fqmNW3FRTQ/Pg8M04vA
      WuwRwOqccUKauNAT+UrqOdJZWOJswlC5OumjHRXILoyBps8ALLuVMEkFyLEBDPLR
      D79bgxjIzHmOxjIJPv8NypFqr7Z/KNIknsGQb0+d0Nb9RgOIWDpqeiKHLI3ejsqJ
      S4+q4G0oSta5CKfg5lMN80pmFqeuSQ9ZU2PfQL9JadTerzWs+6ob+HLAAnXelcOJ
      aRO2y7jUa86cgkxxy64wiiZUup0d9rDN8o/ek0EUnzROXteiSvX2Rx8a8yd9k/FI
      j3iXEY5M0Mqo6/HapgNttRrCK9ku1mrJ5YnnkSS+DlBVYtXM1kL2pKSHVtdcxVxi
      yKNBHzAyG0cjHMatW3RBGSReYZbe5+g+QaxdqzVLK6DEFqp1n2kAM+h9aCdBrqwC
      TtIUbUa7OIiSXWCau5c5NgxESfwVDS6tBNA8BN/MxjcvpNsJ0xG/YmfCAy3eYFXF
      xiTYY1ba
      =bnCx
      -----END PGP PUBLIC KEY BLOCK-----

RuleEngineParams:
  SalienceINTRA: 14
  SalienceUPI: 9
  SalienceIMPS: 8
  SalienceRTGS: 7
  SalienceNEFT: 6
  BumpedSalience: 20
  SecondHighestBumpedSalience: 13

AddFundsWorkflow:
  InitialDelay:
    Value: 5
    TimeUnit: "Second"

AddFundsCollectWorkflow:
  InitialDelay:
    Value: 5
    TimeUnit: "Second"

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "01:30"
  EndTime: "03:30"

AddFundsAlertMailingParams:
  IsEnabled: false

RedisOptions:
  Options:
    IsSecureRedis: false
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 2


CampaignParams:
  Add_Funds:
    Disable: false
    CampaignNotifications:
      - TriggerAfter: "1h"
        Title: "Congrats! Your Federal Bank Savings Account on Fi is ready ✅"
        Body: "Add ₹5000 or more into your account & earn up to ₹250 in a Smart deposit."
        NotificationType: "SystemTray"
        NotificationExpiry: "1h"
        ValidFrom: 0
        ValidTill: 23
        Disable: false

      - TriggerAfter: "20h"
        Title: "Add funds into your Fi account & win big 🎁"
        Body: "Earn up to ₹250 in a Smart Deposit by transferring ₹5000 or more into your account now!"
        NotificationType: "SystemTray"
        NotificationExpiry: "1h"
        ValidFrom: 0
        ValidTill: 23
        Disable: false

      - TriggerAfter: "48h"
        Title: "It pays to save  (No, really!)"
        Body: "Start saving with Fi by adding ₹5,000 into your account & earn up to ₹250. As you maximise your savings, we amplify your rewards."
        NotificationType: "SystemTray"
        NotificationExpiry: "1h"
        ValidFrom: 0
        ValidTill: 23
        Disable: false

TxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "transaction-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineCardTransactionsProcessingSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "card-txns-decline-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ReconParams:
  TimeLimitForExhaustedAttempt: 1 # Time limit before attempting reconciliation again (hours)
  IsReconRestricted: true
  AllowedUserGroupForRecon:
    - 1 # INTERNAL = user belongs to epiFi

DeclineDataAwsSftpBucket:
  AwsBucket: "epifi-federal-debit-card-data"
  AclString: "private"
  SrcFolder: "in_data"
  DstFolder: "processed"

SavingsLedgerReconCacheConfig:
  IsCachingEnabled: false
  SavingsLedgerReconPrefix: "savings_ledger_recon_account_Id"
  CacheTTl: "2m"
  SavingsLedgerRedisOptions:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 5

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById for storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 5
      ClientName: order

TransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: true
        CacheTTL: "5m"

FeatureFlags:
  EnableB2CCallbackSignalToCelestial: true
  EnableAllTransactionForSelectedOrderStatesParams:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - 1 #internal
  DisableCommsForP2PFundTransfer: false
  EnablePGDBDaoForAA: true
  EnableOnAppEnachExecutionInboundNotifProcessor: true
  EnableParameterizedTimelineEvents:
    IsFeatureRestricted: false

Events:
  IncomingCreditMaxPublishDelay: "2m"

RemitterInfoSyncDelayThreshold: "60s"

OrderVpaVerificationPublisher:
  QueueName: "unverified-vpa-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

VerifyVpaDeadlineForInboundNotification: "15s"

FeatureReleaseConfig:
  FeatureConstraints:
  HEALTH_ENGINE_FOR_INTRA_PAYMENTS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 250
      MinIOSVersion: 345
    StickyPercentageConstraintConfig:
      RolloutPercentage: 0
    UserGroupConstraintConfig:
      AllowedGroups:
        - 1 # INTERNAL
  HEALTH_ENGINE_FOR_NEFT_PAYMENTS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 250
      MinIOSVersion: 345
    StickyPercentageConstraintConfig:
      RolloutPercentage: 0
    UserGroupConstraintConfig:
      AllowedGroups:
        - 1 # INTERNAL
  HEALTH_ENGINE_FOR_IMPS_PAYMENTS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 250
      MinIOSVersion: 345
    StickyPercentageConstraintConfig:
      RolloutPercentage: 0
    UserGroupConstraintConfig:
      AllowedGroups:
        - 1 # INTERNAL
  HEALTH_ENGINE_FOR_RTGS_PAYMENTS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 250
      MinIOSVersion: 345
    StickyPercentageConstraintConfig:
      RolloutPercentage: 0
    UserGroupConstraintConfig:
      AllowedGroups:
        - 1 # INTERNAL
  REPORT_FRAUD_ORDER_RECEIPT:
    AppVersionConstraintConfig:
      MinAndroidVersion: 0
      MinIOSVersion: 0
    StickyPercentageConstraintConfig:
      RolloutPercentage: 100
    UserGroupConstraintConfig:
      AllowedGroups:
        - 1 # INTERNAL

PaymentEnquiryParams:
  PaymentProcessingSLAVendorMap:
    "FEDERAL_BANK":
      IntraProcessingTime: "4s"
      RtgsProcessingTime: "4s"
      ImpsProcessingTime: "6s"
      UpiProcessingTime: "6s"
      NeftProcessingTime: "5s"

DeemedTransactionUPIEnquiryPublisher:
  QueueName: "deemed-payment-upi-enquiry-queue"

DeemedTransactionUpiEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "deemed-payment-upi-enquiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 15
      TimeUnit: "Second"

NonTpapPspHandles: [ "fede" ]

IsPushingPaymentsMetricsToHealthEngineEnabled: false

QuestRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0
  ClientName: order-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ReportFraudConfig:
  TransactionProtocolToMaxDaysAvailableForReportFraud:
    CARD: 1.0
    IMPS: 1.0
    UPI: 1.0
