package forms

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/slack-go/slack"
	"go.uber.org/zap"

	slackPb "github.com/epifi/gamma/api/vendorgateway/slack_bot"
	"github.com/epifi/gamma/api/vendorgateway/slack_bot/types"
	genconf "github.com/epifi/gamma/slack_bot/config/genconf"
)

type TagGroups struct {
	slackVgClient slackPb.SlackBotClient
	gconf         *genconf.Config
	UserGroups    []*types.UserGroup
}

func NewTagGroupsForm(slackVgClient slackPb.SlackBotClient, gconf *genconf.Config) *TagGroups {
	return &TagGroups{
		slackVgClient: slackVgClient,
		gconf:         gconf,
	}
}

func (tg *TagGroups) HandleEvent(ctx context.Context, event *slack.InteractionCallback, callbackId string) error {
	ctx = epificontext.WithTraceId(ctx, make(map[string][]string))
	parts := strings.Split(callbackId, ":")
	action := parts[len(parts)-1]

	switch action {
	case "request_modal":
		err := tg.handleEventViewSubmission(ctx, event)
		if err != nil {
			logger.Error(ctx, "error handling tag groups event", zap.Error(err))
			return err
		}
	case "tag_groups":
		err := tg.handleEventFromThreads(ctx, event)
		if err != nil {
			logger.Error(ctx, "error handling tag groups event", zap.Error(err))
			return err
		}
	default:
		return fmt.Errorf("no handler found for callbackId %w", fmt.Errorf("callbackid: %s", callbackId))
	}
	return nil
}

func (tg *TagGroups) handleEventFromThreads(ctx context.Context, event *slack.InteractionCallback) error {
	// Get thread timestamp
	threadTs := findThreadMsgTs(event)

	form, err := tg.GenerateViewModal(event.Channel.ID, threadTs)
	if err != nil {
		logger.Error(ctx, "error generating view modal for tag groups", zap.Error(err))
		return fmt.Errorf("error generating view modal for tag groups %w", err)
	}

	// Open the modal
	resp, respErr := tg.slackVgClient.OpenViewModal(ctx, &slackPb.OpenViewModalRequest{ViewModal: form, TriggerId: event.TriggerID})
	if err := epifigrpc.RPCError(resp, respErr); err != nil {
		return fmt.Errorf("error opening view modal %w", err)
	}

	return nil
}

func (tg *TagGroups) handleEventViewSubmission(ctx context.Context, event *slack.InteractionCallback) error {
	privateMetadata, err := unmarshalPrivateMetadata(event)
	if err != nil {
		return fmt.Errorf("error unmarshalling private metadata: %w", err)
	}

	channelID := privateMetadata["channel_id"]
	threadTs := privateMetadata["thread_ts"]

	// Process form values
	var selectedGroups []string
	var message string

	for _, blockValues := range event.View.State.Values {
		for actionId, fieldValue := range blockValues {
			switch actionId {
			case "groupSelect":
				if len(fieldValue.SelectedOptions) > 0 {
					for _, option := range fieldValue.SelectedOptions {
						selectedGroups = append(selectedGroups, option.Value)
					}
				}
			case "messageText":
				message = fieldValue.Value
			}
		}
	}

	if len(selectedGroups) == 0 {
		logger.Info(ctx, "no groups selected for tagging")
		return nil
	}
	var groupTags []string
	for _, group := range selectedGroups {
		groupTags = append(groupTags, fmt.Sprintf("<!subteam^%s>", group))
	}

	finalMessage := strings.Join(groupTags, " ")
	if message != "" {
		finalMessage += " " + message
	}

	// Post the message - if threadTs is empty, post to channel, otherwise post to thread
	if threadTs == "" {
		err = tg.postSlackMsgInChannel(ctx, finalMessage, channelID)
	} else {
		err = tg.postSlackMsgInThread(ctx, finalMessage, threadTs, channelID)
	}

	if err != nil {
		return err
	}

	return nil
}

func (tg *TagGroups) postSlackMsgInChannel(ctx context.Context, msg, channelId string) error {
	resp, err := tg.slackVgClient.PostSlackMsg(ctx, &slackPb.PostSlackMsgRequest{
		Msg: &slackPb.PostSlackMsgRequest_TextMsg{
			TextMsg: msg,
		},
		Channel: channelId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "failed to post message on slack channel", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (tg *TagGroups) postSlackMsgInThread(ctx context.Context, msg, threadTs, channelId string) error {
	resp, err := tg.slackVgClient.PostSlackMsgInThread(ctx, &slackPb.PostSlackMsgInThreadRequest{
		Msg: &slackPb.PostSlackMsgInThreadRequest_TextMsg{
			TextMsg: msg,
		},
		Ts:      threadTs,
		Channel: channelId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "failed to post message on slack", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (tg *TagGroups) GenerateViewModal(channelID, threadTs string) (*types.ViewModal, error) {
	titleText := types.TextBlockObject{Type: "plain_text", Text: "Tag User Groups", Emoji: false}
	closeText := types.TextBlockObject{Type: "plain_text", Text: "Close", Emoji: false}
	submitText := types.TextBlockObject{Type: "plain_text", Text: "Tag Groups", Emoji: false}

	headerText := types.TextBlockObject{Type: "mrkdwn", Text: "Select the user groups you want to tag and optionally add a message", Emoji: false}
	headerSection := types.SectionBlock{Text: &headerText}

	groupOptions := tg.createGroupOptions()
	groupBlock := createMultiSelectMenuBlock("Select Groups", groupOptions, "Group Selection", "groupSelect", nil)
	groupBlock.Optional = false

	messageBlock := createInputBlock("Message (Optional)", "messageText", "Message Input", true, "Add any additional context or message", "Add any additional context or message", "")
	messageBlock.Optional = true

	var blockList []*types.Block
	blockList = append(blockList, &types.Block{Block: &types.Block_SectionBlock{SectionBlock: &headerSection}},
		&types.Block{Block: &types.Block_InputBlock{InputBlock: groupBlock}},
		&types.Block{Block: &types.Block_InputBlock{InputBlock: messageBlock}})

	blocks := types.Blocks{Block: blockList}

	metaData := map[string]string{
		"channel_id": channelID,
		"thread_ts":  threadTs,
	}

	res, err := json.Marshal(metaData)
	if err != nil {
		return nil, fmt.Errorf("error marshaling metadata %w", err)
	}

	return &types.ViewModal{
		Type:            "modal",
		Title:           &titleText,
		Close:           &closeText,
		Submit:          &submitText,
		PrivateMetadata: string(res),
		CallBackId:      "tag_groups:request_modal",
		Blocks:          &blocks,
	}, nil
}

func (tg *TagGroups) createGroupOptions() []*types.OptionBlockObject {
	var groupNames []string

	for _, group := range tg.UserGroups {
		displayName := group.GroupName
		if group.Handle != "" {
			displayName = fmt.Sprintf("@%s", group.Handle)
		}
		groupNames = append(groupNames, displayName)
	}

	options := createOptionBlockObjects(groupNames)

	// Map the display names back to group IDs for the values
	for i, group := range tg.UserGroups {
		if i < len(options) {
			options[i].Value = group.Id
		}
	}

	if cfg.IsLocalEnvOrRemoteDebug() {
		options = append(options, &types.OptionBlockObject{
			Text: &types.TextBlockObject{
				Type: "plain_text",
				Text: "@platform-oncall",
			},
			Value: "DUMMY",
		})
	}

	return options
}
