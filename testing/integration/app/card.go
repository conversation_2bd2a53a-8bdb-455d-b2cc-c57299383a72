// nolint
package app

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	moneyPb "github.com/epifi/be-common/pkg/money"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/logger"

	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/card"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	fePayTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/frontend/pay/transaction/payload"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
)

const (
	pollingCount = 50
)

func CreateCard(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, blockedCardId string) *card.CreateCardResponse {
	resp, err := client.CreateCard(ctx, &card.CreateCardRequest{
		Req:           reqH,
		BlockedCardId: blockedCardId,
	})
	a.NoError(err)
	a.Equal(uint32(card.CreateCardResponse_PENDING), resp.Status.Code)
	return resp
}

func CheckCreateCardStatus(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) *card.CheckCreateCardStatusResponse {
	var (
		statusResp *card.CheckCreateCardStatusResponse
		err        error
	)

	createCardStatus := card.CheckCreateCardStatusResponse_PENDING
	i := 0
	for ; i < 10 && createCardStatus != card.CheckCreateCardStatusResponse_OK; i++ {
		statusResp, err = client.CheckCreateCardStatus(ctx, &card.CheckCreateCardStatusRequest{
			Req:    reqH,
			CardId: cardId,
		})
		a.NoError(err)
		if statusResp.GetStatus().GetCode() == uint32(card.SetCardPinResponse_OK) {
			createCardStatus = card.CheckCreateCardStatusResponse_OK
		}
		if createCardStatus != card.CheckCreateCardStatusResponse_OK {
			logger.Info(ctx, "card creation still in progress", zap.String("statusresp", statusResp.String()))
		} else {
			logger.Info(ctx, "card created successfully", zap.String("statusresp", statusResp.String()))
			return statusResp
		}
		time.Sleep(time.Second * 1)
	}
	a.True(statusResp.GetStatus().GetCode() == uint32(card.CheckCreateCardStatusResponse_OK))

	return statusResp
}

func SetCardPin(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string, authAttemptId string, uiEntryPoint card.UIEntryPoint) *card.SetCardPinResponse {
	resp, err := client.SetCardPin(ctx, &card.SetCardPinRequest{
		Req:           reqH,
		CardId:        cardId,
		CredBlock:     "random-string",
		PinSetFlow:    1,
		RequestId:     "random-reqeustId",
		UiEntryPoint:  uiEntryPoint,
		AuthAttemptId: authAttemptId,
	})
	a.NoError(err)
	a.Equal(uint32(card.SetCardPinResponse_PENDING), resp.Status.Code)
	return resp
}

func CardPinSetUpStatus(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) *card.CheckCardPinSetupStatusResponse {
	var (
		statusResp *card.CheckCardPinSetupStatusResponse
		err        error
	)

	// pin set is now async call. Check status of pin set requests.
	pinSetStatus := card.SetCardPinResponse_PENDING
	i := 0
	for ; i < 10 && pinSetStatus != card.SetCardPinResponse_OK; i++ {
		statusResp, err = client.CheckCardPinSetupStatus(ctx, &card.CheckCardPinSetupStatusRequest{
			Req:    reqH,
			CardId: cardId,
		})
		a.NoError(err)
		if statusResp.GetStatus().GetCode() == uint32(card.SetCardPinResponse_OK) {
			pinSetStatus = card.SetCardPinResponse_OK
		}
		if pinSetStatus != card.SetCardPinResponse_OK {
			logger.Info(ctx, "card pin setup still in progress", zap.String("statusresp", statusResp.String()))
		} else {
			logger.Info(ctx, "card pin setup completed successfully", zap.String("statusresp", statusResp.String()))
			return statusResp
		}
		time.Sleep(time.Second * 1)
	}
	a.True(statusResp.GetStatus().GetCode() == uint32(card.SetCardPinResponse_OK))

	return statusResp
}

func CardHomeScreen(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) []*card.CardDetail {
	resp, err := client.CardHomeScreen(ctx, &card.CardHomeScreenRequest{
		Req:    reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.NotNil(resp)
	a.True(resp.GetStatus().IsSuccess())
	a.NotNil(resp.Cards)
	// at least 1 card is created
	a.NotNil(resp.Cards[0].BasicInfo)
	logger.Debug(ctx, fmt.Sprintf("16/4/3 %v", resp.Cards[0].BasicInfo.String()))
	// validateDynamicTiles(a, resp.Tiles, resp.Cards)
	// validateCardOffersTile(a, resp.OffersTile, resp.Cards)
	validateCardHomeActions(a, resp.Actions, resp.Cards)
	return resp.GetCards()
}

// TODO(priyansh) : Uncomment this once card offers is enabled in QA
/*
func validateCardOffersTile(a *require.Assertions, offersTile *card.CardOffersTile, cards []*card.CardDetail) {
	a.True(len(cards) == 1)
	switch cards[0].GetState() {
	case card.CardState_ACTIVATED:
		a.True(offersTile.Action.Type == card.CardActionType_FETCH_OFFERS)
		a.True(len(offersTile.GetAction().GetCardActionAuthOptions()) > 0 &&
			offersTile.GetAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
			offersTile.GetAction().GetCardActionAuthOptions()[0].CanBeUsed)
		a.True(offersTile.GetAction().GetActive())
	default:
		a.True(offersTile == nil)
	}
}
*/

/*
	func validateDynamicTiles(a *require.Assertions, dynamicTiles []*card.CardDynamicTile, cards []*card.CardDetail) {
		a.True(len(cards) == 1)
		switch cards[0].GetState() {
		case card.CardState_BLOCKED:
			a.True(len(dynamicTiles) == 1)
			a.True(dynamicTiles[0].Action.Type == card.CardActionType_CREATE_CARD)
			a.True(len(dynamicTiles[0].GetAction().GetCardActionAuthOptions()) > 0 &&
				dynamicTiles[0].GetAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
				dynamicTiles[0].GetAction().GetCardActionAuthOptions()[0].CanBeUsed == true)
		case card.CardState_SUSPENDED:
			a.True(len(dynamicTiles) == 1)
			a.True(dynamicTiles[0].Action.Type == card.CardActionType_RENEW_CARD)
			a.True(len(dynamicTiles[0].GetAction().GetCardActionAuthOptions()) > 0 &&
				dynamicTiles[0].GetAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
				dynamicTiles[0].GetAction().GetCardActionAuthOptions()[0].CanBeUsed == true)
		case card.CardState_CREATED:
			// We will not get delivery tracking tile until 24 hours of card creation
			a.True(len(dynamicTiles) == 1)
			a.True(dynamicTiles[0].Action.Type == card.CardActionType_ACTIVATE)
			a.True(len(dynamicTiles[0].GetAction().GetCardActionAuthOptions()) > 0 &&
				dynamicTiles[0].GetAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
				dynamicTiles[0].GetAction().GetCardActionAuthOptions()[0].CanBeUsed == true)
		}
	}
*/
func validateCardHomeActions(a *require.Assertions, cardActions []*card.CardAction, cards []*card.CardDetail) {
	a.True(len(cards) == 1)
	a.True(len(cardActions) == 4)
	for _, action := range cardActions {
		switch action.GetType() {
		case card.CardActionType_CVV_ENQUIRY:
			validateCvvEnquiryAction(a, action, cards[0].GetState())
		case card.CardActionType_LOCK, card.CardActionType_UNLOCK:
			validateLockUnlockAction(a, action, cards[0].GetState())
		case card.CardActionType_ENABLE_NFC, card.CardActionType_DISABLE_NFC:
			validateNfcEnableDisableAction(a, action, cards[0].GetState())
		case card.CardActionType_ENABLE_ECOMM, card.CardActionType_DISABLE_ECOMM:
			validateEcommEnableDisableAction(a, action, cards[0].GetState())
		case card.CardActionType_CARD_SETTINGS:
			validateCardSettingAction(a, action)
		default:
			a.Error(fmt.Errorf("no such card action expected"))
		}
	}
}

func validateLockUnlockAction(a *require.Assertions, lockUnlockAction *card.CardAction, cardState card.CardState) {
	switch cardState {
	case card.CardState_ACTIVATED:
		a.True(lockUnlockAction.GetType() == card.CardActionType_LOCK)
		a.True(lockUnlockAction.GetActive())
		a.True(len(lockUnlockAction.GetCardActionAuthOptions()) > 0 &&
			lockUnlockAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
			lockUnlockAction.GetCardActionAuthOptions()[0].CanBeUsed)
	case card.CardState_SUSPENDED:
		a.True(lockUnlockAction.GetType() == card.CardActionType_UNLOCK)
		a.True(lockUnlockAction.GetActive())
	default:
		a.False(lockUnlockAction.GetActive())
	}
}

func validateCvvEnquiryAction(a *require.Assertions, cvvEnquiryAction *card.CardAction, cardState card.CardState) {
	switch cardState {
	case card.CardState_ACTIVATED:
		a.True(cvvEnquiryAction.GetActive())
		a.True(len(cvvEnquiryAction.GetCardActionAuthOptions()) > 0 &&
			cvvEnquiryAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_VALIDATE_UPI_PIN &&
			cvvEnquiryAction.GetCardActionAuthOptions()[0].CanBeUsed)
	default:
		a.False(cvvEnquiryAction.GetActive())
	}
}

func validateNfcEnableDisableAction(a *require.Assertions, enableDisableNfcAction *card.CardAction, cardState card.CardState) {
	switch cardState {
	case card.CardState_ACTIVATED:
		if enableDisableNfcAction.GetType() == card.CardActionType_ENABLE_NFC {
			a.True(len(enableDisableNfcAction.GetCardActionAuthOptions()) > 0 &&
				enableDisableNfcAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_VALIDATE_UPI_PIN &&
				enableDisableNfcAction.GetCardActionAuthOptions()[0].CanBeUsed)
			// Action will be inactive as card is not delivered
			a.False(enableDisableNfcAction.GetActive())
		} else {
			a.True(len(enableDisableNfcAction.GetCardActionAuthOptions()) > 0 &&
				enableDisableNfcAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
				enableDisableNfcAction.GetCardActionAuthOptions()[0].CanBeUsed)
			a.False(enableDisableNfcAction.GetActive())
		}
	default:
		a.False(enableDisableNfcAction.GetActive())
	}
}

func validateEcommEnableDisableAction(a *require.Assertions, enableDisableEcommAction *card.CardAction, cardState card.CardState) {
	switch cardState {
	case card.CardState_ACTIVATED:
		if enableDisableEcommAction.GetType() == card.CardActionType_ENABLE_ECOMM {
			a.True(len(enableDisableEcommAction.GetCardActionAuthOptions()) > 0 &&
				enableDisableEcommAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_VALIDATE_UPI_PIN &&
				enableDisableEcommAction.GetCardActionAuthOptions()[0].CanBeUsed)
			a.True(enableDisableEcommAction.GetActive())
		} else {
			a.True(len(enableDisableEcommAction.GetCardActionAuthOptions()) > 0 &&
				enableDisableEcommAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
				enableDisableEcommAction.GetCardActionAuthOptions()[0].CanBeUsed)
			a.True(enableDisableEcommAction.GetActive())
		}
	default:
		a.False(enableDisableEcommAction.GetActive())
	}
}

func validateCardSettingAction(a *require.Assertions, cardSettingsAction *card.CardAction) {
	a.True(cardSettingsAction.GetActive())
	a.True(len(cardSettingsAction.GetCardActionAuthOptions()) > 0 &&
		cardSettingsAction.GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
		cardSettingsAction.GetCardActionAuthOptions()[0].CanBeUsed)
}

func GetCardDetailsWithCvv(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardDetails []*card.CardDetail) {
	cardDetailsWithCvv, err := client.GetCardDetailsWithCvv(ctx, &card.GetCardDetailsWithCvvRequest{
		Req:       reqH,
		CardId:    cardDetails[0].GetId(),
		CredBlock: "cred-block",
	})

	a.NoError(err)
	a.NotNil(cardDetailsWithCvv)

	a.True(cardDetailsWithCvv.GetStatus().IsSuccess(), "api response should be success")
	// card number should be a number as string
	_, err = strconv.Atoi(cardDetailsWithCvv.GetCardInfo().GetCardNumber())
	a.NoError(err)
	// expiry should be of the form MM/YY as string
	_, err = strconv.Atoi(cardDetailsWithCvv.GetCardInfo().GetExpiry()[0:2])
	a.NoError(err)
	_, err = strconv.Atoi(cardDetailsWithCvv.GetCardInfo().GetExpiry()[len(cardDetailsWithCvv.GetCardInfo().GetExpiry())-2:])
	a.NoError(err)
	index := strings.IndexByte(cardDetailsWithCvv.GetCardInfo().GetExpiry(), '/')
	a.Equal(index, 2)
	// cvv should be a number as string
	_, err = strconv.Atoi(cardDetailsWithCvv.GetCardInfo().GetCvv())
	a.NoError(err)
	a.True(len(cardDetailsWithCvv.GetSecureCardInfo().GetCardNumber()) != 0)
	a.True(len(cardDetailsWithCvv.GetSecureCardInfo().GetCvv()) != 0)
}

func GenerateTxnIdForCard(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardDetails []*card.CardDetail) {
	cardGenTxnIdRes, err := client.GenerateTxnId(ctx, &card.GenerateTxnIdRequest{
		ActionType: card.CardActionType_ENABLE_ECOMM,
		Vendor:     cardDetails[0].GetIssuerBank(),
		CardId:     cardDetails[0].GetId(),
		Req:        reqH,
	})
	a.NoError(err)
	a.NotNil(cardGenTxnIdRes)
	a.True(cardGenTxnIdRes.GetStatus().IsSuccess(), "api response should be success")
	a.NotEmpty(cardGenTxnIdRes.GetTxnId(), "txn id must be present in success response")
}

func CardSettingTiles(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardDetails []*card.CardDetail) {
	cardSettingRes, err := client.GetCardSettings(ctx, &card.GetCardSettingsRequest{
		Req:    reqH,
		CardId: cardDetails[0].GetId(),
	})
	a.NoError(err)
	a.NotNil(cardSettingRes)
	a.True(cardSettingRes.GetStatus().IsSuccess(), "rpc response should be success")
	// Currently we have configured 4 card setting tiles
	a.True(len(cardSettingRes.GetCardSettingTiles()) >= 4 && len(cardSettingRes.GetCardSettingTiles()) <= 6, fmt.Sprintf("card settings tiles should be >=4 or <=6, found %v, values: %v",
		len(cardSettingRes.GetCardSettingTiles()), cardSettingRes.GetCardSettingTiles()))

	fmt.Printf("cardSettingRes.GetCardSettingTiles() %v\n", cardSettingRes.GetCardSettingTiles())

	for idx := range cardSettingRes.GetCardSettingTiles() {
		cardSettingTile := cardSettingRes.GetCardSettingTiles()[idx]

		switch cardSettingTile.GetCardAction().GetType() {
		case card.CardActionType_RESET_PIN:
			validateResetPinTile(cardSettingTile, a)
		case card.CardActionType_RENEW_CARD:
			validateRenewCardTile(cardSettingTile, a)
		case card.CardActionType_CARD_LIMIT:
			validateCardLimitTile(cardSettingTile, a)
		case card.CardActionType_CARD_USAGE:
			validateCardUsageTile(cardSettingTile, a)
		default:
			a.Error(fmt.Errorf("no such card setting tile expected"))
		}

	}
}

func GetCardLimits(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardDetails []*card.CardDetail) *card.GetCardLimitsResponse {
	cardLimitRes, err := client.GetCardLimits(ctx, &card.GetCardLimitsRequest{
		Req:    reqH,
		CardId: cardDetails[0].GetId(),
	})
	a.NoError(err)
	a.NotNil(cardLimitRes)
	a.True(cardLimitRes.GetStatus().IsSuccess(), "GetCardLimits api response should be success")
	a.NotEmpty(cardLimitRes.GetRequestId(), "RequestId is required")
	a.NotNil(cardLimitRes.GetCardLimitTile())
	validateCardLimitSettingTile(cardLimitRes.GetCardLimitTile(), a)
	return cardLimitRes
}

func validateCardLimitSettingTile(cardLimitTile *card.CardLimitTile, a *require.Assertions) {
	a.True(len(cardLimitTile.GetCategorisedGrossLimit()) == 2, "Categorised gross limit should be two ATM and Purchase")
	for idx := range cardLimitTile.GetCategorisedGrossLimit() {
		grossCategorisedLimit := cardLimitTile.GetCategorisedGrossLimit()[idx]
		a.True(len(grossCategorisedLimit.GetCategorisedLimits()) == 2, "Categorised limit size should be 2 (DOM and INT)")
		for idy := range grossCategorisedLimit.GetCategorisedLimits() {
			categorisedLimit := grossCategorisedLimit.GetCategorisedLimits()[idy]
			switch grossCategorisedLimit.GetLimitCategory() {
			case card.LimitCategory_ATM_USAGE:
				a.True(len(categorisedLimit.GetCardLimitDetails()) == 1, "card limit details should be 1 for ATM usage")
			case card.LimitCategory_PURCHASE_LIMIT:
				a.True(len(categorisedLimit.GetCardLimitDetails()) == 3, "card limit details should be 4 for purchase usage")
			default:
				a.NoError(fmt.Errorf("no such limit category defined %s", categorisedLimit.GetLimitCategory().String()))
			}

		}
	}
}

func UpdateCardLimits(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	// Enabling card ecommerce and domestic txns so that we can allow modifying its limits
	enableCardEcomm(ctx, a, client, reqH, cardId)
	enableCardDomesticUsage(ctx, a, client, reqH, cardId)
	// enableCardInternationalUsage(ctx, a, client, reqH, cardId)

	updateCardLimitRes, err := client.UpdateCardLimits(ctx, &card.UpdateCardLimitsRequest{
		Req:       reqH,
		CardId:    cardId,
		RequestId: "random-reqeust-id",
		CredBlock: "dummy-cred",
		UpdateCardLimitDetails: []*card.UpdateCardLimitDetail{
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
				LocType: card.CardUsageLocationType_DOMESTIC,
				TxnType: card.CardTransactionType_ATM,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
				LocType: card.CardUsageLocationType_DOMESTIC,
				TxnType: card.CardTransactionType_ECOMMERCE,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
				LocType: card.CardUsageLocationType_DOMESTIC,
				TxnType: card.CardTransactionType_POS,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        50000,
				},
				LocType: card.CardUsageLocationType_DOMESTIC,
				TxnType: card.CardTransactionType_NFC,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
				LocType: card.CardUsageLocationType_INTERNATIONAL,
				TxnType: card.CardTransactionType_ATM,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
				LocType: card.CardUsageLocationType_INTERNATIONAL,
				TxnType: card.CardTransactionType_NFC,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        100000,
				},
				LocType: card.CardUsageLocationType_INTERNATIONAL,
				TxnType: card.CardTransactionType_POS,
			},
			{
				UpdatedAllowedAmount: &types.Money{
					CurrencyCode: "INR",
					Units:        500000,
				},
				LocType: card.CardUsageLocationType_INTERNATIONAL,
				TxnType: card.CardTransactionType_ECOMMERCE,
			},
		},
		IsLimitIncreased: true,
	})
	a.NoError(err)
	a.NotNil(updateCardLimitRes)
	a.True(updateCardLimitRes.GetStatus().IsSuccess(), "UpdateCardLimits api response should be success")
	a.NotEmpty(updateCardLimitRes.GetRequestId(), "RequestId is required")
	a.NotNil(updateCardLimitRes.GetCardLimitTile())
	validateCardLimitSettingTile(updateCardLimitRes.GetCardLimitTile(), a)
}

func validateCardUsageTile(cardUsageSettingTile *card.CardSettingTile, a *require.Assertions) {
	a.True(len(cardUsageSettingTile.GetCardUsageTiles()) == 5, "card usage tiles must be of size 5")
	a.True(cardUsageSettingTile.GetCardAction().GetActive())
	a.True(len(cardUsageSettingTile.GetCardAction().GetCardActionAuthOptions()) > 0 &&
		cardUsageSettingTile.GetCardAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
		cardUsageSettingTile.GetCardAction().GetCardActionAuthOptions()[0].CanBeUsed,
		"must to have no auth action")

	for idx := range cardUsageSettingTile.GetCardUsageTiles() {
		cardUsageTile := cardUsageSettingTile.GetCardUsageTiles()[idx]

		switch cardUsageTile.GetCardAction().GetType() {
		case card.CardActionType_DISABLE_POS, card.CardActionType_ENABLE_POS:
			a.True(cardUsageTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TOGGLE,
				"pos card usage tile display should be toggle")
			if card.CardActionType_DISABLE_POS == cardUsageTile.GetCardAction().GetType() {
				validateDisableCardUsageAction(cardUsageTile, a)
			} else {
				validateEnableCardUsageAction(cardUsageTile, a)
			}
		case card.CardActionType_DISABLE_ATM_WITHDRAWALS, card.CardActionType_ENABLE_ATM_WITHDRAWALS:
			a.True(cardUsageTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TOGGLE,
				"atm withdrawals card usage tile display should be toggle")
			if card.CardActionType_DISABLE_ATM_WITHDRAWALS == cardUsageTile.GetCardAction().GetType() {
				validateDisableCardUsageAction(cardUsageTile, a)
			} else {
				validateEnableCardUsageAction(cardUsageTile, a)
			}
		case card.CardActionType_DISABLE_NFC, card.CardActionType_ENABLE_NFC:
			a.True(cardUsageTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TOGGLE,
				"nfc card usage tile display should be toggle")
			if card.CardActionType_ENABLE_NFC == cardUsageTile.GetCardAction().GetType() {
				validateEnableCardUsageAction(cardUsageTile, a)
			} else {
				validateDisableCardUsageAction(cardUsageTile, a)
			}
		case card.CardActionType_ENABLE_ECOMM, card.CardActionType_DISABLE_ECOMM:
			a.True(cardUsageTile.GetCardAction().GetActive())
			a.True(cardUsageTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TOGGLE,
				"ecomm card usage tile display should be toggle")
			if card.CardActionType_ENABLE_ECOMM == cardUsageTile.GetCardAction().GetType() {
				validateEnableCardUsageAction(cardUsageTile, a)
			} else {
				validateDisableCardUsageAction(cardUsageTile, a)
			}
		case card.CardActionType_ENABLE_INTERNATIONAL_USAGE, card.CardActionType_DISABLE_INTERNATIONAL_USAGE:
			a.True(cardUsageTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_CHECKBOX,
				"international card usage tile display should be checkbox")
			if card.CardActionType_DISABLE_INTERNATIONAL_USAGE == cardUsageTile.GetCardAction().GetType() {
				validateDisableCardUsageAction(cardUsageTile, a)
			} else {
				validateEnableCardUsageAction(cardUsageTile, a)
			}
		default:
			a.Error(fmt.Errorf("no such usage tile expected in card setting response"))
		}
	}
}

func validateDisableCardUsageAction(cardUsageTile *card.CardUsageTile, a *require.Assertions) {
	a.True(cardUsageTile.GetCurrentState() == card.CardControlState_ENABLED)
	a.True(len(cardUsageTile.GetCardAction().GetCardActionAuthOptions()) > 0 &&
		cardUsageTile.GetCardAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
		cardUsageTile.GetCardAction().GetCardActionAuthOptions()[0].CanBeUsed,
		fmt.Sprintf("auth option none should be present for card usage tile %v", cardUsageTile.GetCardAction().GetType().String()))
}

func validateEnableCardUsageAction(cardUsageTile *card.CardUsageTile, a *require.Assertions) {
	a.True(cardUsageTile.GetCurrentState() == card.CardControlState_DISABLED)
	a.True(len(cardUsageTile.GetCardAction().GetCardActionAuthOptions()) > 0 &&
		cardUsageTile.GetCardAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_VALIDATE_UPI_PIN &&
		cardUsageTile.GetCardAction().GetCardActionAuthOptions()[0].CanBeUsed)
}

func validateCardLimitTile(cardLimitTile *card.CardSettingTile, a *require.Assertions) {
	a.True(len(cardLimitTile.GetCardUsageTiles()) == 0)
	a.True(len(cardLimitTile.GetCardAction().GetCardActionAuthOptions()) > 0 &&
		cardLimitTile.GetCardAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
		cardLimitTile.GetCardAction().GetCardActionAuthOptions()[0].CanBeUsed)
	a.True(cardLimitTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TEXT)
	a.True(cardLimitTile.GetCardAction().GetActive())
}

func validateRenewCardTile(renewNewCardTile *card.CardSettingTile, a *require.Assertions) {
	a.True(len(renewNewCardTile.GetCardUsageTiles()) == 0)
	a.True(len(renewNewCardTile.GetCardAction().GetCardActionAuthOptions()) > 0 &&
		renewNewCardTile.GetCardAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_CARD_ACTION_AUTH_NONE &&
		renewNewCardTile.GetCardAction().GetCardActionAuthOptions()[0].CanBeUsed)
	a.True(renewNewCardTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TEXT)
}

func validateResetPinTile(resetPinTile *card.CardSettingTile, a *require.Assertions) {
	a.True(len(resetPinTile.GetCardUsageTiles()) == 0, "card usage tile shouldn't be in reset pin type")
	a.True(resetPinTile.GetCardAction().GetActive())
	a.True(resetPinTile.GetCardAction().GetDisplayFormat() == card.DisplayFormat_TEXT)
	a.True(len(resetPinTile.GetCardAction().GetCardActionAuthOptions()) > 0 &&
		resetPinTile.GetCardAction().GetCardActionAuthOptions()[0].Auth == card.CardActionAuth_VALIDATE_LIVENESS_PLUS_FACEMATCH &&
		resetPinTile.GetCardAction().GetCardActionAuthOptions()[0].CanBeUsed)
}

func CardControls(ctx context.Context, a *require.Assertions, cardsClient card.CardClient, reqH *header.RequestHeader, cardDetails []*card.CardDetail) {
	LockCard(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	unLockCard(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	// enableCardNfc(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	disableCardNfc(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	enableCardEcomm(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	disableCardEcomm(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	enableCardDomesticUsage(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	disableCardDomesticUsage(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	// enableCardInternationalUsage(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	disableCardInternationalUsage(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	// enableCardAtm(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	// disableCardAtm(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	// enableCardPos(ctx, a, cardsClient, reqH, cardDetails[0].Id)

	// disableCardPos(ctx, a, cardsClient, reqH, cardDetails[0].Id)
}

func LockCard(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.LockCards(ctx, &card.LockCardsRequest{
		Req:     reqH,
		CardIds: []string{cardId},
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.GetLockStateInfo())
	a.NotNil(resp.GetLockStateInfo()[cardId])
	a.True(resp.GetLockStateInfo()[cardId].Card.State.String() == card.CardState_SUSPENDED.String())
}

func unLockCard(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.UnlockCards(ctx, &card.UnlockCardsRequest{
		Req:       reqH,
		RequestId: uuid.New().String(),
		CredBlock: "cred-block",
		CardIds:   []string{cardId},
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.GetUnlockStateInfo())
	a.True(resp.GetUnlockStateInfo()[cardId].Card.State.String() == card.CardState_ACTIVATED.String())
}

/*
func enableCardNfc(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.EnableNfc(ctx, &card.EnableNfcRequest{
		Req:   reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_DISABLE_NFC)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_ENABLED)
}
*/

func disableCardNfc(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.DisableNfc(ctx, &card.DisableNfcRequest{
		Req:    reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_ENABLE_NFC)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_DISABLED)
}

func enableCardEcomm(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.EnableEcomm(ctx, &card.EnableEcommRequest{
		Req:       reqH,
		CardId:    cardId,
		CredBlock: "dummy-cred-block",
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_DISABLE_ECOMM)
	a.True(resp.UsageTile.CardAction.Active)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_ENABLED)
}

func disableCardEcomm(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.DisableEcomm(ctx, &card.DisableEcommRequest{
		Req:    reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_ENABLE_ECOMM)
	a.True(resp.UsageTile.CardAction.Active)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_DISABLED)
}

func enableCardDomesticUsage(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.EnableDomesticCardUsage(ctx, &card.EnableDomesticCardUsageRequest{
		Req:       reqH,
		CardId:    cardId,
		CredBlock: "dummy-cred-block",
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Nil(resp.UsageTile)
}

func disableCardDomesticUsage(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.DisableDomesticCardUsage(ctx, &card.DisableDomesticCardUsageRequest{
		Req:    reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Nil(resp.UsageTile)
}

/*
func enableCardInternationalUsage(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.EnableInternationalCardUsage(ctx, &card.EnableInternationalCardUsageRequest{
		Req:   reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_DISABLE_INTERNATIONAL_USAGE)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_ENABLED)
}
*/

func disableCardInternationalUsage(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.DisableInternationalCardUsage(ctx, &card.DisableInternationalCardUsageRequest{
		Req:    reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_ENABLE_INTERNATIONAL_USAGE)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_DISABLED)
}

// TODO(priyansh) : Uncomment this when we have QR code flow or when we can mark card as delivered.
//  As offline txns can only be enabled/disabled when card is delivered.
/*
func enableCardAtm(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.EnableATM(ctx, &card.EnableATMRequest{
		Req:      reqH,
		CardId:    cardId,
		RequestId: "request-id",
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_DISABLE_ATM_WITHDRAWALS)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_ENABLED)
}

func disableCardAtm(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.DisableATM(ctx, &card.DisableATMRequest{
		Req:   reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_ENABLE_ATM_WITHDRAWALS)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_DISABLED)
}

func enableCardPos(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.EnablePOS(ctx, &card.EnablePOSRequest{
		Req:      reqH,
		CardId:    cardId,
		RequestId: "request-id",
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_DISABLE_POS)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_ENABLED)
}

func disableCardPos(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.DisablePOS(ctx, &card.DisablePOSRequest{
		Req:   reqH,
		CardId: cardId,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.Actions)
	a.NotNil(resp.Card)
	a.Equal(resp.State, card.CardActionState_SUCCESS)
	a.Equal(resp.UsageTile.CardAction.Type, card.CardActionType_ENABLE_POS)
	a.Equal(resp.UsageTile.CurrentState, card.CardControlState_DISABLED)
}
*/

func RenewCard(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) {
	resp, err := client.RenewCard(ctx, &card.RenewCardRequest{
		Req:         reqH,
		CardId:      cardId,
		Reason:      "card stolen",
		Provenance:  card.Provenance_USER_APP,
		AddressType: types.AddressType_SHIPPING,
	})
	a.NoError(err)
	a.Equal(resp.Status.Code, uint32(card.RenewCardResponse_SHIPPING_ADDRESS_UPDATE_PENDING))
	a.NotNil(resp.DisplayMessage)
}

func RenewCardStatus(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) *card.CheckRenewCardStatusResponse {
	var (
		statusResp *card.CheckRenewCardStatusResponse
		err        error
	)

	// renew card is now async call. Check status of renew card requests.
	renewStatus := card.CheckRenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING
	i := 0
	for ; i < pollingCount && renewStatus != card.CheckRenewCardStatusResponse_OK; i++ {
		statusResp, err = client.CheckRenewCardStatus(ctx, &card.CheckRenewCardStatusRequest{
			Req:    reqH,
			CardId: cardId,
		})
		a.NoError(err)
		if statusResp.GetStatus().GetCode() == uint32(card.CheckRenewCardStatusResponse_OK) {
			renewStatus = card.CheckRenewCardStatusResponse_OK
		}
		if renewStatus != card.CheckRenewCardStatusResponse_OK {
			logger.Info(ctx, "card renew still in progress", zap.String("statusresp", statusResp.String()))
		} else {
			logger.Info(ctx, "card renew completed successfully", zap.String("statusresp", statusResp.String()))
			return statusResp
		}
		time.Sleep(time.Second * 2)
	}
	a.True(statusResp.GetStatus().GetCode() == uint32(card.CheckRenewCardStatusResponse_OK))
	a.NotNil(statusResp.GetNewCardId())

	return statusResp
}

func CardAuth(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) *card.InitiateCardAuthActionResponse {
	resp, err := client.InitiateCardAuthAction(ctx, &card.InitiateCardAuthActionRequest{
		Req:    reqH,
		CardId: cardId,
		Action: card.CardActionType_RESET_PIN,
		Auth:   card.CardActionAuth_VALIDATE_LIVENESS_PLUS_FACEMATCH,
	})
	logger.Info(ctx, "init card auth action", zap.Any("InitiateCardAuthActionRespp", resp))
	a.NoError(err)
	a.True(resp.NextAction.Screen == deeplink.Screen_CHECK_LIVENESS)
	return resp
}

func CardAuthNextAction(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, attemptId string) *deeplink.Deeplink {
	nextActionRes, err := client.GetCardAuthNextAction(ctx, &card.GetCardAuthNextActionRequest{
		Req:       reqH,
		AttemptId: attemptId,
	})
	logger.Info(ctx, "fetch card auth next action", zap.Any("GetCardAuthNextActionRespp", nextActionRes))
	a.NoError(err)
	return nextActionRes.GetNextAction()
}

// nolint:funlen
func CheckLivenessWithCardNextAction(
	ctx context.Context,
	a *require.Assertions,
	sc signup.SignupClient,
	reqH *header.RequestHeader,
	video []byte,
	actionAfterLiveness deeplink.Screen,
	attemptId string,
	client card.CardClient,
	allowErrorInLiveness ...bool,
) {
	CheckLivenessVideo(ctx, a, sc, reqH, video, attemptId, allowErrorInLiveness...)
	var nextAction *deeplink.Deeplink
	for i := 0; i < PollMaxRetries; i++ {
		time.Sleep(PollInterval)
		nextAction = CardAuthNextAction(ctx, a, client, reqH, attemptId)
		// for following screens, polling is done.
		if nextAction.GetScreen() != deeplink.Screen_CHECK_LIVENESS &&
			nextAction.GetScreen() != deeplink.Screen_GET_CARD_AUTH_NEXT_ACTION_API {
			// break if not polling screen.
			break
		}
	}
	a.Equal(actionAfterLiveness.String(), nextAction.GetScreen().String(),
		fmt.Sprintf("next action after liveness polling: %v", nextAction),
	)
}

func InitiatePhysicalCardDispatch(ctx context.Context, a *require.Assertions, client card.CardClient, fePayTxnClient fePayTxnPb.TransactionClient, reqH *header.RequestHeader, cardId string) *card.InitiatePhysicalCardDispatchResponse {
	savedCard, err := client.GetCardDetails(ctx, &card.GetCardDetailsRequest{
		Req:    reqH,
		CardId: cardId,
	})
	a.NoError(err)
	if savedCard.GetCard().GetForm() == card.CardForm_PHYSICAL {
		return &card.InitiatePhysicalCardDispatchResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusAlreadyExists(),
			},
		}
	}

	chargesRes, err := client.FetchPhysicalCardChargesForUser(ctx, &card.FetchPhysicalCardChargesForUserRequest{
		Req: reqH,
	})

	a.NoError(err)
	a.Equal(rpc.StatusOk().GetCode(), chargesRes.GetRespHeader().GetStatus().GetCode())
	a.Equal(chargesRes.GetNextAction().GetScreen(), deeplink.Screen_DEBIT_CARD_ORDER_PHYSICAL_CARD_SCREEN)

	orderCardPageScreenOptions := &dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions{}
	unmarshallErr := chargesRes.GetNextAction().GetScreenOptionsV2().UnmarshalTo(orderCardPageScreenOptions)
	a.NoError(unmarshallErr)

	res, err := client.InitiatePhysicalCardDispatch(ctx, &card.InitiatePhysicalCardDispatchRequest{
		Req:         reqH,
		Provenance:  card.Provenance_USER_APP,
		CardId:      cardId,
		AddressType: types.AddressType_SHIPPING,
		Amount:      orderCardPageScreenOptions.GetAmount(),
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().GetCode(), res.GetStatus().GetCode())

	if moneyPb.IsZero(orderCardPageScreenOptions.GetAmount().GetBeMoney()) {
		logger.Info(ctx, "card dispatch charges are zero, skipping payment")
		return res
	}

	fundTransferRes, err := fePayTxnClient.AuthoriseFundTransfer(ctx, &fePayTxnPb.AuthoriseFundTransferRequest{
		Req:             reqH,
		ClientRequestId: res.GetNextAction().GetFederalSecurePinScreenOptions().GetTxnAttributes().GetClientRequestId(),
		Credential:      &fePayTxnPb.AuthoriseFundTransferRequest_PartnerSdkCredBlock{PartnerSdkCredBlock: "encrypted-shit"},
		PaymentDetails: &payload.PaymentDetails{
			PiFrom:          res.GetNextAction().GetFederalSecurePinScreenOptions().GetTxnAttributes().GetPayerPaymentInstrumentId(),
			PiTo:            res.GetNextAction().GetFederalSecurePinScreenOptions().GetTxnAttributes().GetPayeePaymentInstrumentId(),
			Amount:          res.GetNextAction().GetFederalSecurePinScreenOptions().GetTxnAttributes().GetAmount(),
			Remarks:         res.GetNextAction().GetFederalSecurePinScreenOptions().GetTxnAttributes().GetRemarks(),
			ReqId:           res.GetNextAction().GetFederalSecurePinScreenOptions().GetTxnAttributes().GetRequestId(),
			PaymentProtocol: fePayPb.PaymentProtocol_INTRA_BANK,
		},
	})
	logger.Info(ctx, "fund transfer res", zap.Any("res", fundTransferRes))
	a.Nil(err)
	a.NotNil(fundTransferRes)
	a.NotNil(fundTransferRes.GetRespHeader().GetStatus())
	a.Equal(fundTransferRes.GetRespHeader().GetStatus().GetCode(), rpc.StatusOk().GetCode())

	return res
}

func PhysicalCardDispatchStatusCheck(ctx context.Context, a *require.Assertions, cardClient cardProvPb.CardProvisioningClient, cardId string) *cardProvPb.GetPhysicalCardDispatchStatusResponse {
	var (
		statusResp *cardProvPb.GetPhysicalCardDispatchStatusResponse
		err        error
	)
	dispatchStatus := cardProvPb.GetPhysicalCardDispatchStatusResponse_IN_PROGRESS
	i := 0

	for ; i < pollingCount && dispatchStatus != cardProvPb.GetPhysicalCardDispatchStatusResponse_OK; i++ {
		statusResp, err = cardClient.GetPhysicalCardDispatchStatus(ctx, &cardProvPb.GetPhysicalCardDispatchStatusRequest{
			CardId: cardId,
		})
		a.NoError(err)
		switch {
		case statusResp.GetStatus().GetCode() == uint32(cardProvPb.GetPhysicalCardDispatchStatusResponse_OK):
			statusResp.Status = rpc.StatusOk()
			dispatchStatus = cardProvPb.GetPhysicalCardDispatchStatusResponse_OK
			logger.Info(ctx, "card dispatched successfully", zap.String("statusresp", statusResp.String()))
		default:
			logger.Info(ctx, "card dispatch still in progress", zap.String("statusresp", statusResp.String()))
		}
		time.Sleep(time.Second * 2)
	}

	a.True(statusResp.GetStatus().GetCode() == uint32(cardProvPb.GetPhysicalCardDispatchStatusResponse_OK))
	return statusResp
}

func InitiateRenewCardFlow(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) *card.InitiateCardFlowResponse {
	res, err := client.InitiateCardFlow(ctx, &card.InitiateCardFlowRequest{
		Req:          reqH,
		CardFlowType: card.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD,
		Data: &card.InitiateCardFlowRequest_RenewCardRequest{
			RenewCardRequest: &card.RenewCardRequest{
				Req:         reqH,
				CardId:      cardId,
				Reason:      "card stolen",
				Provenance:  card.Provenance_USER_APP,
				AddressType: types.AddressType_SHIPPING,
				CardForm:    card.CardForm_DIGITAL,
			},
		},
	})

	a.NoError(err)
	a.Equal(res.GetRespHeader().GetStatus().GetCode(), rpc.StatusOk().GetCode())
	a.NotNil(res.GetNextAction())
	a.Equal(res.GetNextAction().GetScreen(), deeplink.Screen_DEBIT_CARD_CHECK_FLOW_STATUS_SCREEN)
	return res
}

func CheckRenewCardFlowStatus(ctx context.Context, a *require.Assertions, client card.CardClient, reqH *header.RequestHeader, cardId string) string {
	var (
		statusResp                         *card.CheckFlowStatusResponse
		res                                dcScreenOptionsPb.DebitCardRequestNewCardSuccessScreenOptions
		initiateCardAuthActionScreenOption dcScreenOptionsPb.InitiateCardAuthActionApiScreenOptions
		err                                error
	)

	for i := 0; i < pollingCount; i++ {
		statusResp, err = client.CheckFlowStatus(ctx, &card.CheckFlowStatusRequest{
			Req:          reqH,
			CardFlowType: card.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD,
			Data: &card.CheckFlowStatusRequest_CheckRenewCardStatusRequest{
				CheckRenewCardStatusRequest: &card.CheckRenewCardStatusRequest{
					CardId: cardId,
				},
			},
			RetryAttemptNumber: 1,
		})
		a.NoError(err)
		a.NotNil(statusResp.GetNextAction())
		a.True(statusResp.GetRespHeader().GetStatus().IsSuccess())
		if statusResp.GetNextAction().GetScreen() == deeplink.Screen_DEBIT_CARD_REQUEST_NEW_CARD_SUCCESS_SCREEN {
			logger.Info(ctx, "card renewal moved to success", zap.String("statusresp", statusResp.String()))
			break
		}
		logger.Info(ctx, "card renewal in progress", zap.String("statusresp", statusResp.String()))
		a.Equal(statusResp.GetNextAction().GetScreen(), deeplink.Screen_DEBIT_CARD_CHECK_FLOW_STATUS_SCREEN)
		time.Sleep(1 * time.Second)
	}
	a.True(statusResp.GetRespHeader().GetStatus().IsSuccess())
	err = statusResp.GetNextAction().GetScreenOptionsV2().UnmarshalTo(&res)
	a.NoError(err)

	if res.GetCta().GetDeeplink().GetScreenOptionsV2() != nil {
		err = res.GetCta().GetDeeplink().GetScreenOptionsV2().UnmarshalTo(&initiateCardAuthActionScreenOption)
	} else {
		return res.GetCta().GetDeeplink().GetCardHomeScreenOptions().GetCardId()
	}
	a.NoError(err)

	return initiateCardAuthActionScreenOption.GetCardId()
}
