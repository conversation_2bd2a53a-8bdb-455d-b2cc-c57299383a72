// nolint:dupl,depguard
package app

import (
	"context"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	authPb "github.com/epifi/gamma/api/auth"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	feRewards "github.com/epifi/gamma/api/frontend/rewards"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	types "github.com/epifi/gamma/api/typesv2"
)

type RewardAction string

const (
	TxnOfAmount915 RewardAction = "Txn of amount 915"
	TxnOfAmount916 RewardAction = "Txn of amount 916"
	TxnOfAmount917 RewardAction = "Txn of amount 917"
	TxnOfAmount918 RewardAction = "Txn of amount 918"
	TxnOfAmount919 RewardAction = "Txn of amount 919"
	TxnOfAmount920 RewardAction = "Txn of amount 920"
	TxnOfAmount921 RewardAction = "txn of amount 921"
	TxnOfAmount922 RewardAction = "txn of amount 922"
	TxnOfAmount923 RewardAction = "txn of amount 923"
	TxnOfAmount924 RewardAction = "txn of amount 924"
	TxnOfAmount925 RewardAction = "txn of amount 925"
	TxnOfAmount926 RewardAction = "txn of amount 926"
)

var (
	timeAfter1Hour, _    = ptypes.TimestampProto(time.Now().Add(60 * time.Minute))
	timeAfter2Month, _   = ptypes.TimestampProto(time.Now().AddDate(0, 2, 0))
	timeNowString        = time.Now().Format(time.RFC3339)
	timeAfter30MinString = time.Now().Add(30 * time.Minute).Format(time.RFC3339)

	// map gets populated once all the reward offers in actionToRewardOfferReqMap are created
	actionToRewardOfferIdMap = make(map[RewardAction]string, 30)

	// actions to reward offer map
	actionToRewardOfferReqMap = map[RewardAction]*rewardOfferPb.CreateRewardOfferRequest{
		TxnOfAmount915: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 915",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "TXN_AMOUNT*0.1",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig:  &rewardOfferPb.RewardNotificationConfig{},
				IsImplicitLockingDisabled: true,
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				BgColor:      "bg_color",
				ActionDesc:   "acceptance testing",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		TxnOfAmount916: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 916",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "TXN_AMOUNT*0.1",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				AutoProcessReward:        true,
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		TxnOfAmount917: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 917",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
					RewardConfig: &rewardOfferPb.RewardConfigOption_SmartDepositConfig{
						SmartDepositConfig: &rewardOfferPb.SmartDepositConfig{
							MaturityDateConfig: &rewardsPb.RewardTimeConfig{
								Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: timeAfter2Month},
							},
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "200",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS in SD",
						AfterClaimTextExpression:  "REWARD_UNITS in SD",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// nolint: dupl
		TxnOfAmount918: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 918",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_CASH,
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "200",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Cash",
						AfterClaimTextExpression:  "REWARD_UNITS Cash",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// nolint: dupl
		TxnOfAmount919: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 919",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_CASH,
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "200",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Cash",
						AfterClaimTextExpression:  "REWARD_UNITS Cash",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				AutoProcessReward:        true,
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// nolint: dupl
		TxnOfAmount920: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 920",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_GIFT_HAMPER,
					RewardConfig: &rewardOfferPb.RewardConfigOption_GiftHamperConfig{
						GiftHamperConfig: &rewardOfferPb.GiftHamperConfig{
							VendorName:      "Monsoon Harvest Vendor",
							VendorProductId: "Monsoon Harvest Product Id",
							ProductName:     "Monsoon Harvest Box",
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "Gift Hamper",
						AfterClaimTextExpression:  "Gift Hamper",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// to depict the capping of reward units
		// nolint:dupl
		TxnOfAmount921: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 921",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
					RewardUnitsCapUserAggregate: &rewardOfferPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOfferPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      23,
							},
						},
						IsHardCheck: true,
					},
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "90",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// to depict the capping of reward units
		// nolint:dupl
		TxnOfAmount922: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 922",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
					RewardUnitsCapUserAggregate: &rewardOfferPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOfferPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      50,
							},
						},
						IsHardCheck: true,
					},
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "90",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// to generate reward in LOCKED status (with EXPLICITLY_LOCKED subStatus)
		// nolint:dupl
		TxnOfAmount923: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 923",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
					RewardUnitsCapUserAggregate: &rewardOfferPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOfferPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      50,
							},
						},
						IsHardCheck: true,
					},
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 10},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "90",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// to generate a reward projection for the event
		// nolint:dupl
		TxnOfAmount924: {
			OfferType:   rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE,
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 924",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
					RewardUnitsCapUserAggregate: &rewardOfferPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOfferPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      100,
							},
						},
						IsHardCheck: true,
					},
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 10},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "100",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:         false,
			GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_PROJECTION,
			UnlockMeta:        &rewardOfferPb.UnlockMeta{},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// to generate reward in LOCKED status (with EXPLICITLY_LOCKED subStatus), that gets unlocked upon receiving another event
		// nolint:dupl
		TxnOfAmount925: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 925",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
					RewardUnitsCapUserAggregate: &rewardOfferPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOfferPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      50,
							},
						},
						IsHardCheck: true,
					},
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "90",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
				RewardExpiryTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{
						RelativeTimeInMinutes: 120,
					},
				},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:      false,
			GenerationType: rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockEvent:    rewardsPb.CollectedDataType_ORDER,
			UnlockMeta: &rewardOfferPb.UnlockMeta{
				Cta: &rewardOfferPb.CTA{
					Name: "Unlock CTA",
				},
				Constraint: &rewardOfferPb.ConstraintsMeta{Expression: "TXN_AMOUNT == 15"},
			},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
		// to generate reward in LOCKED status (with EXPLICITLY_LOCKED subStatus), that gets unlocked upon receiving another event
		// nolint:dupl
		TxnOfAmount926: {
			ActiveSince: timeNowString,
			ActiveTill:  timeAfter30MinString,
			CreatedBy:   "acceptance-test",
			ActionType:  "ORDER",
			ConstraintMeta: &rewardOfferPb.ConstraintsMeta{
				Expression: "TXN_AMOUNT == 926",
			},
			RewardMeta: &rewardOfferPb.RewardMeta{
				AutoProcessReward:         true,
				IsImplicitLockingDisabled: true,
				RewardAggregates: &rewardOfferPb.RewardAggregates{
					UserAggregate:   10,
					ActionAggregate: 10,
					RewardUnitsCapUserAggregate: &rewardOfferPb.RewardUnitsCapAggregate{
						UnitsCaps: []*rewardOfferPb.RewardUnitsCapAggregate_RewardUnitsCap{
							{
								RewardType: rewardsPb.RewardType_FI_COINS,
								Units:      50,
							},
						},
						IsHardCheck: true,
					},
				},
				RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
				},
				DefaultDecideTimeInSecs: 10,
				Probability:             1,
				RewardConfigOptions: []*rewardOfferPb.RewardConfigOption{{
					RewardType: rewardsPb.RewardType_FI_COINS,
					RewardConfig: &rewardOfferPb.RewardConfigOption_FiCoinsRewardConfig{
						FiCoinsRewardConfig: &rewardOfferPb.FiCoinsRewardConfig{
							IsFiPoints: commontypes.BooleanEnum_TRUE,
						},
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					UnitsConfig: &rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig{
						ExpressionProbabilityConfig: &rewardOfferPb.ExpressionProbabilityConfig{
							Expression:  "90",
							Probability: 1,
						},
					},
					DisplayConfig: &rewardOfferPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "REWARD_UNITS Fi-Coins",
						AfterClaimTextExpression:  "REWARD_UNITS Fi-Coins",
						Icon:                      "icon_url",
						BgColor:                   "bg_color",
					},
				}},
				RewardNotificationConfig: &rewardOfferPb.RewardNotificationConfig{},
				RewardExpiryTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{
						RelativeTimeInMinutes: 120,
					},
				},
			},
			DisplayMeta: &rewardOfferPb.DisplayMeta{
				DisplaySince: timeNowString,
				DisplayTill:  timeAfter30MinString,
				DisplayType:  rewardOfferPb.DisplayType_FRINGE,
				Title:        "title",
				Steps:        []string{"steps"},
				Tncs:         []string{"tnc"},
				Icon:         "icon_url",
				ActionDesc:   "acceptance testing",
				BgColor:      "bg_color",
			},
			IsVisible:      false,
			GenerationType: rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
			UnlockEvent:    rewardsPb.CollectedDataType_ORDER,
			UnlockMeta: &rewardOfferPb.UnlockMeta{
				Cta: &rewardOfferPb.CTA{
					Name: "Unlock CTA",
				},
				Constraint: &rewardOfferPb.ConstraintsMeta{Expression: "TXN_AMOUNT == 16"},
			},
			AdditionalDetails: &rewardOfferPb.AdditionalDetails{},
		},
	}
)

type RewardsTestSuite struct {
	rewardOffersClient rewardOfferPb.RewardOffersClient
	beRewardsClient    rewardsPb.RewardsGeneratorClient
	feRewardsClient    feRewards.RewardsClient
	beAuthClient       authPb.AuthClient
	beProjectorClient  projectorPb.ProjectorServiceClient
}

func NewRewardsTestSuite(rewardOffersClient rewardOfferPb.RewardOffersClient, beRewardsClient rewardsPb.RewardsGeneratorClient, feRewardsClient feRewards.RewardsClient, beAuthClient authPb.AuthClient, beProjectorClient projectorPb.ProjectorServiceClient) *RewardsTestSuite {
	return &RewardsTestSuite{rewardOffersClient: rewardOffersClient, beRewardsClient: beRewardsClient, feRewardsClient: feRewardsClient, beAuthClient: beAuthClient, beProjectorClient: beProjectorClient}
}

// createRewardOffers create reward offers in rewards system.
func (rts *RewardsTestSuite) CreateAllRewardOffers(t *testing.T) {
	for action, rewardOfferReq := range actionToRewardOfferReqMap {
		// create reward offer
		res, err := rts.rewardOffersClient.CreateRewardOffer(context.Background(), rewardOfferReq)
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetStatus().GetCode())

		// mark reward offer as approved
		res, err = rts.rewardOffersClient.UpdateRewardOfferStatus(context.Background(), &rewardOfferPb.UpdateRewardOfferStatusRequest{
			Id:     res.RewardOffer.Id,
			Status: rewardOfferPb.RewardOfferStatus_APPROVED,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetStatus().GetCode())

		// mark reward offer as active
		res, err = rts.rewardOffersClient.UpdateRewardOfferStatus(context.Background(), &rewardOfferPb.UpdateRewardOfferStatusRequest{
			Id:     res.RewardOffer.Id,
			Status: rewardOfferPb.RewardOfferStatus_ACTIVE,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetStatus().GetCode())

		// populate id of just created reward offer in actionToRewardOfferIdMap
		actionToRewardOfferIdMap[action] = res.RewardOffer.Id
	}
}

// TerminateAllRewardOffers move all the previously created test reward offers to TERMINATED status.
func (rts *RewardsTestSuite) TerminateAllRewardOffers(t *testing.T) {
	for _, rewardOfferId := range actionToRewardOfferIdMap {
		// create reward offer
		res, err := rts.rewardOffersClient.UpdateRewardOfferStatus(context.Background(), &rewardOfferPb.UpdateRewardOfferStatusRequest{
			Id:     rewardOfferId,
			Status: rewardOfferPb.RewardOfferStatus_TERMINATED,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetStatus().GetCode())
	}
}

func (rts *RewardsTestSuite) TestFiCoinsReward_GenerateAndClaimFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 915, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount915])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")

	// claim the earned reward
	claimRewardInputScreenRes, err := rts.feRewardsClient.GetClaimRewardInputScreen(context.Background(), &feRewards.ClaimRewardInputScreenRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), claimRewardInputScreenRes.GetStatus().GetCode())
	assert.Nil(t, claimRewardInputScreenRes.GetInputScreen())

	rts.claimReward(t, &feRewards.ClaimRewardRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})

	// poll reward status until terminal state is reached or attempts are exhausted
	var rewardStatus rewardsPb.RewardStatus
	for attempt := 0; attempt <= 20; attempt++ {
		rewardStatus, _ = rts.getRewardStatusDetails(t, earnedReward.GetId())
		if rewardStatus == rewardsPb.RewardStatus_PROCESSING_FAILED || rewardStatus == rewardsPb.RewardStatus_PROCESSED {
			break
		}
		// wait for sometime before next poll
		time.Sleep(1 * time.Second)
	}
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, rewardStatus)
}

// TestFiCoinsReward_GenerateTimeLockedRewardFlow is used to test generation of explicitly locked rewards until a given time
func (rts *RewardsTestSuite) TestFiCoinsReward_GenerateTimeLockedRewardFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 923, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount923])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")
	assert.Equal(t, rewardsPb.RewardStatus_LOCKED, earnedReward.GetStatus())
	assert.Equal(t, rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED, earnedReward.GetSubStatusV2())
}

// TestFiCoinsReward_GenerateLockedRewardFlow is used to test generation of explicitly locked rewards that get unlocked upon receiving an event
func (rts *RewardsTestSuite) TestFiCoinsReward_GenerateLockedRewardFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 925, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount925])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")
	assert.Equal(t, rewardsPb.RewardStatus_LOCKED, earnedReward.GetStatus())
	assert.Equal(t, rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED, earnedReward.GetSubStatusV2())

	// do action for triggering unlock of generated reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 15, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be unlocked
	time.Sleep(6 * time.Second)

	// fetch the updated reward
	updatedRewardStatus, updatedSubStatus := rts.getRewardStatusDetails(t, earnedReward.GetId())
	assert.Equal(t, rewardsPb.RewardStatus_CREATED, updatedRewardStatus)
	assert.Equal(t, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED, updatedSubStatus)
}

// TestFiCoinsReward_GenerateLockedRewardWithAutoClaimFlow is used to test generation of explicitly locked rewards that get auto-claimed upon receiving an event
func (rts *RewardsTestSuite) TestFiCoinsReward_GenerateLockedRewardWithAutoClaimFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 926, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount926])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")
	assert.Equal(t, rewardsPb.RewardStatus_LOCKED, earnedReward.GetStatus())
	assert.Equal(t, rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED, earnedReward.GetSubStatusV2())

	// do action for triggering unlock of generated reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 16, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be unlocked and processed
	time.Sleep(8 * time.Second)

	// fetch the updated reward
	updatedRewardStatus, updatedSubStatus := rts.getRewardStatusDetails(t, earnedReward.GetId())
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, updatedRewardStatus)
	assert.Equal(t, rewardsPb.SubStatus_SUB_STATUS_CREDITED_FI_COINS, updatedSubStatus)
}

// TestFiCoinsReward_GenerateRewardProjectionFlow is used to test generation of reward projection
func (rts *RewardsTestSuite) TestFiCoinsReward_GenerateRewardProjectionFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.UPITransfer(context.Background(), t, 924)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	projectedReward := rts.getProjectionByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount924])[0]
	firstRewardOption := projectedReward.GetProjectedOptions().GetRewardUnitsWithTypes()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")
	assert.InEpsilon(t, float32(100), firstRewardOption.GetRewardUnits(), 0.001)
}

// TestFiCoinsReward_GenerationWithCappedUnits checks whether the generated fi-coins reward was capped based on the max-cap config or not.
// A reward is expected with the capped value.
func (rts *RewardsTestSuite) TestFiCoinsReward_GenerationWithCappedUnits(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 921, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount921])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")
	// 23 here refers to the max-cap on the fi-coins reward units set in the reward-offer
	assert.Equal(t, int32(23), int32(earnedReward.RewardOptions.GetOptions()[0].GetFiCoins().Units), "reward units not capped as expected")
}

// TestRewardUnitsCapAlreadyExhaustedFlow validates the scenario where the reward shouldn't be generated if the units cap is already reached.
// Following logic is used to test this scenario
// 1. Trigger an action to generate a reward so that max cap is reached after generating this reward -> reward should be generated.
// 2. Re perform the action again -> no reward should be generated this time.
func (rts *RewardsTestSuite) TestRewardUnitsCapAlreadyExhaustedFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 922, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount922])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")
	// 50 here refers to the max-cap on the fi-coins reward units set in the reward-offer
	assert.Equal(t, int32(50), int32(earnedReward.RewardOptions.GetOptions()[0].GetFiCoins().Units), "reward units not capped as expected")

	// do action for triggering FiCoins reward again
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 922, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated (ideally it shouldn't get generated)
	time.Sleep(5 * time.Second)

	earnedRewards := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount922])
	// total rewards should be one only, i.e. the earlier one
	assert.Equal(t, 1, len(earnedRewards), "reward was not supposed to be given since the actor had already touched the max-cap config")
}

func (rts *RewardsTestSuite) TestFiCoinsReward_AutoProcessFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering FiCoins reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 916, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount916])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_FI_COINS, firstRewardOption.GetRewardType(), "reward is not of type fi coins")

	// no need to claim the reward as it should be auto processed

	// poll reward status until terminal state is reached or attempts are exhausted
	var rewardStatus rewardsPb.RewardStatus
	for attempt := 0; attempt <= 20; attempt++ {
		rewardStatus, _ = rts.getRewardStatusDetails(t, earnedReward.GetId())
		if rewardStatus == rewardsPb.RewardStatus_PROCESSING_FAILED || rewardStatus == rewardsPb.RewardStatus_PROCESSED {
			break
		}
		// wait for sometime before next poll
		time.Sleep(1 * time.Second)
	}
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, rewardStatus)
}

func (rts *RewardsTestSuite) TestSDReward_NewRewardSDFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering SD reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 917, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount917])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_SMART_DEPOSIT, firstRewardOption.GetRewardType(), "reward is not of type sd")

	// claim the earned reward
	claimRewardInputScreenRes, err := rts.feRewardsClient.GetClaimRewardInputScreen(context.Background(), &feRewards.ClaimRewardInputScreenRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), claimRewardInputScreenRes.GetStatus().GetCode())
	// no existing sd exists for user so CREATE_NEW_REWARD_SD deeplink should come for claiming SD reward
	assert.Equal(t, deeplinkPb.Screen_CREATE_NEW_REWARD_SD_SCREEN, claimRewardInputScreenRes.GetInputScreen().GetScreen())

	// uncomment the claim flow once B2C order starts working in acceptance test setup

	rts.claimReward(t, &feRewards.ClaimRewardRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})

	// poll reward status until terminal state is reached or attempts are exhausted
	var rewardStatus rewardsPb.RewardStatus
	for attempt := 0; attempt <= 30; attempt++ {
		rewardStatus, _ = rts.getRewardStatusDetails(t, earnedReward.GetId())
		if rewardStatus == rewardsPb.RewardStatus_PROCESSING_FAILED || rewardStatus == rewardsPb.RewardStatus_PROCESSED {
			break
		}
		// wait for sometime before next poll
		time.Sleep(5 * time.Second)
	}
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, rewardStatus)
}

// nolint: dupl
func (rts *RewardsTestSuite) TestCashReward_GenerateAndClaimFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering cash reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 918, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount918])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_CASH, firstRewardOption.GetRewardType(), "reward is not of type cash")

	// claim the earned reward
	claimRewardInputScreenRes, err := rts.feRewardsClient.GetClaimRewardInputScreen(context.Background(), &feRewards.ClaimRewardInputScreenRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), claimRewardInputScreenRes.GetStatus().GetCode())
	// no deeplink should be sent for claiming cash reward
	assert.Nil(t, claimRewardInputScreenRes.GetInputScreen())

	rts.claimReward(t, &feRewards.ClaimRewardRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})

	// poll reward status until terminal state is reached or attempts are exhausted
	var rewardStatus rewardsPb.RewardStatus
	for attempt := 0; attempt <= 30; attempt++ {
		rewardStatus, _ = rts.getRewardStatusDetails(t, earnedReward.GetId())
		if rewardStatus == rewardsPb.RewardStatus_PROCESSING_FAILED || rewardStatus == rewardsPb.RewardStatus_PROCESSED {
			break
		}
		// wait for sometime before next poll
		time.Sleep(5 * time.Second)
	}
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, rewardStatus)
}

// nolint: dupl
func (rts *RewardsTestSuite) TestCashReward_AutoProcessFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering cash reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 919, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount919])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_CASH, firstRewardOption.GetRewardType(), "reward is not of type cash")

	// no need to claim the reward as it should be auto processed

	// poll reward status until terminal state is reached or attempts are exhausted
	var rewardStatus rewardsPb.RewardStatus
	for attempt := 0; attempt <= 30; attempt++ {
		rewardStatus, _ = rts.getRewardStatusDetails(t, earnedReward.GetId())
		if rewardStatus == rewardsPb.RewardStatus_PROCESSING_FAILED || rewardStatus == rewardsPb.RewardStatus_PROCESSED {
			break
		}
		// wait for sometime before next poll
		time.Sleep(5 * time.Second)
	}
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, rewardStatus)
}

// nolint: dupl
func (rts *RewardsTestSuite) TestGiftHamper_GenerateAndClaimFlow(t *testing.T, pts *PayTestSuite, userAuth *header.RequestHeader) {
	t.Parallel()
	// do action for triggering Gift hamper reward
	pts.AddFundForSingleUser(context.Background(), t, userAuth, 920, fePayPb.OrderStatus_PAYMENT_SUCCESS)
	// wait for reward to be generated
	time.Sleep(6 * time.Second)

	// fetch the earned reward
	actorId := rts.getActorIdUsingAuthHeader(t, userAuth)
	earnedReward := rts.getRewardsByActorAndRewardOfferId(t, actorId, actionToRewardOfferIdMap[TxnOfAmount920])[0]
	firstRewardOption := earnedReward.GetRewardOptions().GetOptions()[0]
	assert.Equal(t, rewardsPb.RewardType_GIFT_HAMPER, firstRewardOption.GetRewardType(), "reward is not of type gift hamper")

	// claim the earned reward
	claimRewardInputScreenRes, err := rts.feRewardsClient.GetClaimRewardInputScreen(context.Background(), &feRewards.ClaimRewardInputScreenRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), claimRewardInputScreenRes.GetStatus().GetCode())
	// REWARD_SHIPPING_ADDRESS_INPUT_SCREEN should be sent for entering delivery address for claiming gift hamper reward
	assert.Equal(t, deeplinkPb.Screen_REWARD_SHIPPING_ADDRESS_INPUT_SCREEN, claimRewardInputScreenRes.GetInputScreen().GetScreen())

	rts.claimReward(t, &feRewards.ClaimRewardRequest{
		Req:            userAuth,
		RewardId:       earnedReward.GetId(),
		RewardOptionId: firstRewardOption.GetId(),
		// shipping address needs to be passed for claiming a gift hamper reward
		ClaimMetadata: &feRewards.ClaimRewardRequest_ClaimMetadata{
			ShippingAddress: &types.PostalAddress{
				AddressLines: []string{"address line 1", "address line 2"},
			},
		},
	})

	// poll reward status until terminal state is reached or attempts are exhausted
	var rewardStatus rewardsPb.RewardStatus
	for attempt := 0; attempt <= 20; attempt++ {
		rewardStatus, _ = rts.getRewardStatusDetails(t, earnedReward.GetId())
		if rewardStatus == rewardsPb.RewardStatus_PROCESSING_FAILED || rewardStatus == rewardsPb.RewardStatus_PROCESSED {
			break
		}
		// wait for sometime before next poll
		time.Sleep(1 * time.Second)
	}
	assert.Equal(t, rewardsPb.RewardStatus_PROCESSED, rewardStatus)
}

func (rts *RewardsTestSuite) getRewardStatusDetails(t *testing.T, rewardId string) (rewardsPb.RewardStatus, rewardsPb.SubStatus) {
	rewardByIdRes, err := rts.beRewardsClient.GetRewardsByRewardId(context.Background(), &rewardsPb.RewardsByRewardIdRequest{RewardId: rewardId})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), rewardByIdRes.GetStatus().GetCode())
	assert.NotNil(t, rewardByIdRes.GetReward())
	return rewardByIdRes.GetReward().GetStatus(), rewardByIdRes.GetReward().GetSubStatusV2()
}

func (rts *RewardsTestSuite) claimReward(t *testing.T, req *feRewards.ClaimRewardRequest) {
	claimRewardRes, err := rts.feRewardsClient.ClaimReward(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), claimRewardRes.GetStatus().GetCode())
}

// getRewardsByActorAndRewardOfferId fetches rewards by actor and reward offer id.
func (rts *RewardsTestSuite) getRewardsByActorAndRewardOfferId(t *testing.T, actorId string, rewardOfferId string) []*rewardsPb.Reward {
	earnedRewardsRes, err := rts.beRewardsClient.GetRewardsByActorId(context.Background(), &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			RewardOfferId: rewardOfferId,
		},
		PageContext: &rpc.PageContextRequest{PageSize: 10},
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), earnedRewardsRes.GetStatus().GetCode())
	return earnedRewardsRes.GetRewards()
}

// getProjectionByActorAndRewardOfferId fetches projections by actor and reward offer id.
func (rts *RewardsTestSuite) getProjectionByActorAndRewardOfferId(t *testing.T, actorId string, rewardOfferId string) []*projectorPb.Projection {
	projectionsRes, err := rts.beProjectorClient.GetRewardsProjections(context.Background(), &projectorPb.GetRewardsProjectionsRequest{
		ActorId: actorId,
		Filters: &projectorPb.GetRewardsProjectionsRequest_Filters{
			OfferIds: []string{rewardOfferId},
		},
		PageCtxRequest: &rpc.PageContextRequest{PageSize: 10},
	})
	require.NoError(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), projectionsRes.GetStatus().GetCode())
	return projectionsRes.GetIndividualProjections().GetProjections()
}

// nolint:dupl
func (rts *RewardsTestSuite) getActorIdUsingAuthHeader(t *testing.T, auth *header.RequestHeader) string {
	validateAuthRes, err := rts.beAuthClient.ValidateToken(context.Background(), &authPb.ValidateTokenRequest{
		Token:     auth.Auth.GetAccessToken(),
		TokenType: authPb.TokenType_ACCESS_TOKEN,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), validateAuthRes.GetStatus().GetCode())
	assert.NotEmpty(t, validateAuthRes.GetActorId())
	return validateAuthRes.GetActorId()
}
